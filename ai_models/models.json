[{"model_id": "code_analyzer_v1", "name": "Code Analysis Model v1", "model_type": "code_analysis", "model_path": null, "model_url": null, "version": "1.0.0", "description": "Local model for code analysis and quality assessment", "parameters": {"temperature": 0.1, "max_tokens": 2048, "top_p": 0.9}, "requirements": {"min_memory_gb": 4, "gpu_required": false}, "privacy_level": "high", "max_context_length": 8192, "supports_streaming": true}, {"model_id": "code_explainer_v1", "name": "Code Explanation Model v1", "model_type": "code_explanation", "model_path": null, "model_url": null, "version": "1.0.0", "description": "Local model for generating code explanations and documentation", "parameters": {"temperature": 0.3, "max_tokens": 1024, "top_p": 0.95}, "requirements": {"min_memory_gb": 2, "gpu_required": false}, "privacy_level": "high", "max_context_length": 4096, "supports_streaming": true}, {"model_id": "refactoring_assistant_v1", "name": "Refactoring Assistant v1", "model_type": "refactoring", "model_path": null, "model_url": null, "version": "1.0.0", "description": "Local model for suggesting code refactoring improvements", "parameters": {"temperature": 0.2, "max_tokens": 1536, "top_p": 0.9}, "requirements": {"min_memory_gb": 3, "gpu_required": false}, "privacy_level": "high", "max_context_length": 6144, "supports_streaming": true}]