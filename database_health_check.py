#!/usr/bin/env python3
"""
Database Connectivity and Health Verification Script
==================================================

This script performs comprehensive database connectivity and health verification
for all identified database systems in the Vibe Check project.

Database Systems Tested:
1. SQLite Analysis Results Database (~/.vibe_check/analysis_results.db)
2. Time-Series Database (TSDB) (.vibe_check_tsdb/ directory)
3. Historical Trend Storage (~/.vibe_check/history/)
4. Legacy Time-Series Storage (vibe_check_metrics.db)

Author: Database Health Verification System
Date: 2025-06-29
"""

import asyncio
import json
import logging
import os
import sqlite3
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DatabaseHealthChecker:
    """Comprehensive database health verification system."""
    
    def __init__(self):
        """Initialize the health checker."""
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'databases': {},
            'summary': {
                'total_databases': 0,
                'healthy_databases': 0,
                'failed_databases': 0,
                'warnings': []
            }
        }
        
    async def run_all_checks(self) -> Dict[str, Any]:
        """Run all database health checks."""
        logger.info("Starting comprehensive database health verification...")
        
        # Test each database system
        await self._test_sqlite_analysis_db()
        await self._test_tsdb_system()
        await self._test_historical_trend_storage()
        await self._test_legacy_metrics_db()
        
        # Generate summary
        self._generate_summary()
        
        logger.info("Database health verification completed")
        return self.results
    
    async def _test_sqlite_analysis_db(self):
        """Test SQLite Analysis Results Database."""
        db_name = "sqlite_analysis_results"
        logger.info(f"Testing {db_name}...")
        
        try:
            # Import required modules
            from vibe_check.core.persistence.database import DatabaseManager
            from vibe_check.core.persistence.storage import ResultStorage
            from vibe_check.core.persistence.models import AnalysisRun, AnalysisMode
            
            # Test database connectivity
            db_manager = DatabaseManager()
            db_path = db_manager.db_path
            
            result = {
                'status': 'unknown',
                'path': str(db_path),
                'exists': db_path.exists(),
                'size_bytes': 0,
                'connectivity': False,
                'schema_valid': False,
                'crud_operations': False,
                'integrity_check': False,
                'performance_baseline': {},
                'errors': [],
                'warnings': []
            }
            
            # Check file existence and size
            if db_path.exists():
                result['size_bytes'] = db_path.stat().st_size
                logger.info(f"Database file exists: {db_path} ({result['size_bytes']} bytes)")
            else:
                logger.info(f"Database file will be created: {db_path}")
            
            # Test connectivity
            start_time = time.time()
            try:
                with db_manager.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1")
                    result['connectivity'] = True
                    logger.info("✓ Database connectivity successful")
            except Exception as e:
                result['errors'].append(f"Connectivity failed: {str(e)}")
                logger.error(f"✗ Database connectivity failed: {e}")
            
            connectivity_time = time.time() - start_time
            result['performance_baseline']['connectivity_time'] = connectivity_time
            
            # Test schema integrity
            if result['connectivity']:
                try:
                    start_time = time.time()
                    stats = db_manager.get_database_stats()
                    result['schema_valid'] = True
                    result['performance_baseline']['schema_check_time'] = time.time() - start_time
                    logger.info("✓ Schema integrity verified")
                    logger.info(f"  - Analysis runs: {stats.get('total_runs', 0)}")
                    logger.info(f"  - File results: {stats.get('total_file_results', 0)}")
                    logger.info(f"  - Issues: {stats.get('total_issues', 0)}")
                except Exception as e:
                    result['errors'].append(f"Schema validation failed: {str(e)}")
                    logger.error(f"✗ Schema validation failed: {e}")
            
            # Test CRUD operations
            if result['schema_valid']:
                try:
                    start_time = time.time()
                    storage = ResultStorage(db_manager)
                    
                    # Create test analysis run
                    test_run = AnalysisRun(
                        project_path="/test/project",
                        analysis_mode=AnalysisMode.VCS,
                        profile="test",
                        start_time=datetime.now()
                    )
                    
                    # Test insert
                    run_id = storage.save_analysis_run(test_run)
                    logger.info(f"✓ Test insert successful (run_id: {run_id})")
                    
                    # Test read
                    retrieved_run = storage.get_analysis_run(run_id)
                    if retrieved_run and retrieved_run.project_path == "/test/project":
                        logger.info("✓ Test read successful")
                    else:
                        raise Exception("Retrieved data doesn't match inserted data")
                    
                    # Test cleanup (delete test data)
                    db_manager.execute_update("DELETE FROM analysis_runs WHERE run_id = ?", (run_id,))
                    logger.info("✓ Test cleanup successful")
                    
                    result['crud_operations'] = True
                    result['performance_baseline']['crud_time'] = time.time() - start_time
                    
                except Exception as e:
                    result['errors'].append(f"CRUD operations failed: {str(e)}")
                    logger.error(f"✗ CRUD operations failed: {e}")
            
            # Test SQLite integrity
            if result['connectivity']:
                try:
                    start_time = time.time()
                    with db_manager.get_connection() as conn:
                        cursor = conn.cursor()
                        cursor.execute("PRAGMA integrity_check")
                        integrity_result = cursor.fetchone()[0]
                        
                        if integrity_result == "ok":
                            result['integrity_check'] = True
                            logger.info("✓ SQLite integrity check passed")
                        else:
                            result['errors'].append(f"Integrity check failed: {integrity_result}")
                            logger.error(f"✗ Integrity check failed: {integrity_result}")
                    
                    result['performance_baseline']['integrity_check_time'] = time.time() - start_time
                    
                except Exception as e:
                    result['errors'].append(f"Integrity check failed: {str(e)}")
                    logger.error(f"✗ Integrity check failed: {e}")
            
            # Determine overall status
            if result['connectivity'] and result['schema_valid'] and result['crud_operations'] and result['integrity_check']:
                result['status'] = 'healthy'
            elif result['connectivity']:
                result['status'] = 'degraded'
            else:
                result['status'] = 'failed'
            
        except ImportError as e:
            result = {
                'status': 'failed',
                'errors': [f"Import error: {str(e)}"],
                'warnings': ['Required modules not available']
            }
            logger.error(f"✗ Import error for {db_name}: {e}")
        except Exception as e:
            result = {
                'status': 'failed',
                'errors': [f"Unexpected error: {str(e)}"]
            }
            logger.error(f"✗ Unexpected error testing {db_name}: {e}")
        
        self.results['databases'][db_name] = result
        self.results['summary']['total_databases'] += 1
        
        if result['status'] == 'healthy':
            self.results['summary']['healthy_databases'] += 1
        else:
            self.results['summary']['failed_databases'] += 1
    
    async def _test_tsdb_system(self):
        """Test Time-Series Database (TSDB) system."""
        db_name = "tsdb_time_series"
        logger.info(f"Testing {db_name}...")
        
        try:
            # Import required modules
            from vibe_check.monitoring.storage.time_series_engine import (
                TimeSeriesStorageEngine, TSDBConfig, create_tsdb, MetricSample
            )
            
            # Configure TSDB
            tsdb_config = TSDBConfig(
                data_dir=Path(".vibe_check_tsdb_test"),
                retention_days=1,  # Short retention for testing
                max_ingestion_rate=100
            )
            
            result = {
                'status': 'unknown',
                'data_dir': str(tsdb_config.data_dir),
                'exists': tsdb_config.data_dir.exists(),
                'connectivity': False,
                'ingestion_test': False,
                'query_test': False,
                'compression_test': False,
                'performance_baseline': {},
                'errors': [],
                'warnings': []
            }
            
            # Test TSDB initialization
            start_time = time.time()
            try:
                tsdb = await create_tsdb(tsdb_config)
                result['connectivity'] = True
                result['performance_baseline']['initialization_time'] = time.time() - start_time
                logger.info("✓ TSDB initialization successful")
                
                # Check data directory
                if tsdb_config.data_dir.exists():
                    result['exists'] = True
                    logger.info(f"✓ TSDB data directory exists: {tsdb_config.data_dir}")
                
            except Exception as e:
                result['errors'].append(f"TSDB initialization failed: {str(e)}")
                logger.error(f"✗ TSDB initialization failed: {e}")
                self.results['databases'][db_name] = result
                self.results['summary']['total_databases'] += 1
                self.results['summary']['failed_databases'] += 1
                return
            
            # Test metric ingestion
            if result['connectivity']:
                try:
                    start_time = time.time()
                    
                    # Ingest test metrics
                    test_metrics = [
                        {
                            'name': 'test_metric_1',
                            'value': 42.0,
                            'timestamp': time.time(),
                            'labels': {'test': 'true', 'component': 'health_check'}
                        },
                        {
                            'name': 'test_metric_2', 
                            'value': 84.0,
                            'timestamp': time.time(),
                            'labels': {'test': 'true', 'component': 'health_check'}
                        }
                    ]
                    
                    for metric in test_metrics:
                        await tsdb.ingest_sample(
                            metric['name'],
                            metric['value'],
                            metric['timestamp'],
                            metric['labels']
                        )
                    
                    # Force flush
                    await tsdb._flush_write_buffer()
                    
                    result['ingestion_test'] = True
                    result['performance_baseline']['ingestion_time'] = time.time() - start_time
                    logger.info("✓ Metric ingestion successful")
                    
                except Exception as e:
                    result['errors'].append(f"Metric ingestion failed: {str(e)}")
                    logger.error(f"✗ Metric ingestion failed: {e}")
            
            # Test querying
            if result['ingestion_test']:
                try:
                    start_time = time.time()
                    
                    # Query test metrics
                    end_time = time.time()
                    start_time_query = end_time - 3600  # Last hour
                    
                    series_list = await tsdb.query_range(
                        'test_metric_1',
                        start_time_query,
                        end_time,
                        {'test': 'true'}
                    )
                    
                    if series_list and len(series_list) > 0:
                        result['query_test'] = True
                        logger.info(f"✓ Query test successful (found {len(series_list)} series)")
                    else:
                        result['warnings'].append("Query returned no results")
                        logger.warning("⚠ Query returned no results")
                    
                    result['performance_baseline']['query_time'] = time.time() - start_time
                    
                except Exception as e:
                    result['errors'].append(f"Query test failed: {str(e)}")
                    logger.error(f"✗ Query test failed: {e}")
            
            # Test statistics and health
            if result['connectivity']:
                try:
                    stats = tsdb.get_stats()
                    result['performance_baseline']['stats'] = stats
                    logger.info(f"✓ TSDB statistics: {stats}")
                    
                    if stats.get('total_series', 0) > 0:
                        result['compression_test'] = True
                        logger.info("✓ TSDB contains data series")
                    
                except Exception as e:
                    result['warnings'].append(f"Statistics retrieval failed: {str(e)}")
                    logger.warning(f"⚠ Statistics retrieval failed: {e}")
            
            # Cleanup test data
            try:
                await tsdb.shutdown()
                # Remove test directory
                import shutil
                if tsdb_config.data_dir.exists():
                    shutil.rmtree(tsdb_config.data_dir)
                logger.info("✓ Test cleanup completed")
            except Exception as e:
                result['warnings'].append(f"Cleanup failed: {str(e)}")
                logger.warning(f"⚠ Cleanup failed: {e}")
            
            # Determine overall status
            if result['connectivity'] and result['ingestion_test'] and result['query_test']:
                result['status'] = 'healthy'
            elif result['connectivity']:
                result['status'] = 'degraded'
            else:
                result['status'] = 'failed'
                
        except ImportError as e:
            result = {
                'status': 'failed',
                'errors': [f"Import error: {str(e)}"],
                'warnings': ['TSDB modules not available']
            }
            logger.error(f"✗ Import error for {db_name}: {e}")
        except Exception as e:
            result = {
                'status': 'failed',
                'errors': [f"Unexpected error: {str(e)}"]
            }
            logger.error(f"✗ Unexpected error testing {db_name}: {e}")
        
        self.results['databases'][db_name] = result
        self.results['summary']['total_databases'] += 1
        
        if result['status'] == 'healthy':
            self.results['summary']['healthy_databases'] += 1
        else:
            self.results['summary']['failed_databases'] += 1
    
    async def _test_historical_trend_storage(self):
        """Test Historical Trend Storage (JSON files)."""
        db_name = "historical_trend_storage"
        logger.info(f"Testing {db_name}...")
        
        try:
            # Import required modules
            from vibe_check.core.trend_analysis.trend_storage import TrendStorage
            from vibe_check.core.models.project_metrics import ProjectMetrics
            
            # Test storage directory
            storage_dir = Path.home() / ".vibe_check" / "history"
            
            result = {
                'status': 'unknown',
                'storage_dir': str(storage_dir),
                'exists': storage_dir.exists(),
                'writable': False,
                'readable': False,
                'json_format_valid': False,
                'performance_baseline': {},
                'errors': [],
                'warnings': []
            }
            
            # Test directory access
            try:
                start_time = time.time()
                trend_storage = TrendStorage(storage_dir)
                result['exists'] = storage_dir.exists()
                result['performance_baseline']['initialization_time'] = time.time() - start_time
                logger.info(f"✓ Storage directory initialized: {storage_dir}")
            except Exception as e:
                result['errors'].append(f"Directory initialization failed: {str(e)}")
                logger.error(f"✗ Directory initialization failed: {e}")
                self.results['databases'][db_name] = result
                self.results['summary']['total_databases'] += 1
                self.results['summary']['failed_databases'] += 1
                return
            
            # Test write operations
            try:
                start_time = time.time()
                
                # Create test metrics
                test_metrics = ProjectMetrics("/test/project")
                test_metrics.total_file_count = 10
                test_metrics.total_line_count = 1000
                test_metrics.avg_complexity = 2.5
                
                # Store test metrics
                stored_path = trend_storage.store_metrics(test_metrics)
                result['writable'] = True
                result['performance_baseline']['write_time'] = time.time() - start_time
                logger.info(f"✓ Write test successful: {stored_path}")
                
            except Exception as e:
                result['errors'].append(f"Write test failed: {str(e)}")
                logger.error(f"✗ Write test failed: {e}")
            
            # Test read operations
            if result['writable']:
                try:
                    start_time = time.time()
                    
                    # Read historical metrics
                    historical_metrics = trend_storage.get_historical_metrics("/test/project", limit=5)
                    
                    if historical_metrics:
                        result['readable'] = True
                        logger.info(f"✓ Read test successful (found {len(historical_metrics)} records)")
                    else:
                        result['warnings'].append("No historical data found")
                        logger.warning("⚠ No historical data found")
                    
                    result['performance_baseline']['read_time'] = time.time() - start_time
                    
                except Exception as e:
                    result['errors'].append(f"Read test failed: {str(e)}")
                    logger.error(f"✗ Read test failed: {e}")
            
            # Test JSON format validation
            if result['readable']:
                try:
                    start_time = time.time()
                    
                    # Check JSON files in storage directory
                    json_files = list(storage_dir.rglob("*.json"))
                    valid_files = 0
                    
                    for json_file in json_files[:5]:  # Check first 5 files
                        try:
                            with open(json_file, 'r') as f:
                                data = json.load(f)
                                # Validate required fields
                                if 'timestamp' in data and 'project_path' in data:
                                    valid_files += 1
                        except json.JSONDecodeError:
                            continue
                    
                    if valid_files > 0:
                        result['json_format_valid'] = True
                        logger.info(f"✓ JSON format validation successful ({valid_files} valid files)")
                    else:
                        result['warnings'].append("No valid JSON files found")
                        logger.warning("⚠ No valid JSON files found")
                    
                    result['performance_baseline']['validation_time'] = time.time() - start_time
                    
                except Exception as e:
                    result['errors'].append(f"JSON validation failed: {str(e)}")
                    logger.error(f"✗ JSON validation failed: {e}")
            
            # Cleanup test data
            try:
                test_project_dir = storage_dir / "project"
                if test_project_dir.exists():
                    import shutil
                    shutil.rmtree(test_project_dir)
                logger.info("✓ Test cleanup completed")
            except Exception as e:
                result['warnings'].append(f"Cleanup failed: {str(e)}")
                logger.warning(f"⚠ Cleanup failed: {e}")
            
            # Determine overall status
            if result['writable'] and result['readable']:
                result['status'] = 'healthy'
            elif result['exists']:
                result['status'] = 'degraded'
            else:
                result['status'] = 'failed'
                
        except ImportError as e:
            result = {
                'status': 'failed',
                'errors': [f"Import error: {str(e)}"],
                'warnings': ['Trend storage modules not available']
            }
            logger.error(f"✗ Import error for {db_name}: {e}")
        except Exception as e:
            result = {
                'status': 'failed',
                'errors': [f"Unexpected error: {str(e)}"]
            }
            logger.error(f"✗ Unexpected error testing {db_name}: {e}")
        
        self.results['databases'][db_name] = result
        self.results['summary']['total_databases'] += 1
        
        if result['status'] == 'healthy':
            self.results['summary']['healthy_databases'] += 1
        else:
            self.results['summary']['failed_databases'] += 1
    
    async def _test_legacy_metrics_db(self):
        """Test Legacy Time-Series Storage (vibe_check_metrics.db)."""
        db_name = "legacy_metrics_db"
        logger.info(f"Testing {db_name}...")
        
        # Check for legacy database file
        legacy_db_paths = [
            Path("vibe_check_metrics.db"),
