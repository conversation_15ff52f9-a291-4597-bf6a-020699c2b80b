{"timestamp": 1751142227.415881, "overall_status": "FAIL", "validation_time": 67.54736161231995, "results": {"test_infrastructure": {"total_tests": 789, "collection_errors": 7, "success_rate": 99.12060301507537, "status": "PASS", "errors": ["__________________________ ERROR collecting tests/ai ___________________________", "________ ERROR collecting tests/integration/test_monitoring_isolated.py ________", "_______ ERROR collecting tests/integration/test_unified_visualization.py _______", "_______________ ERROR collecting tests/test_vcs_comprehensive.py _______________", "___ ERROR collecting tests/unit/core/trend_analysis/test_trend_visualizer.py ___"]}, "constants_usage": {"violations": ["vibe_check/core/config.py: Use ToolNames.RUFF", "vibe_check/core/config.py: Use ToolNames.MYPY", "vibe_check/core/config.py: Use ToolNames.BANDIT", "vibe_check/core/config.py: Use AnalysisThresholds.CYCLOMATIC_COMPLEXITY", "vibe_check/core/models.py: Use AnalysisThresholds.CYCLOMATIC_COMPLEXITY", "vibe_check/core/dependency_manager.py: Use AnalysisThresholds.CYCLOMATIC_COMPLEXITY", "vibe_check/core/unified_analyzer.py: Use AnalysisThresholds.CYCLOMATIC_COMPLEXITY", "vibe_check/core/progress.py: Use AnalysisThresholds.CYCLOMATIC_COMPLEXITY", "vibe_check/core/async_unified_analyzer.py: Use AnalysisThresholds.CYCLOMATIC_COMPLEXITY", "vibe_check/core/common_utils.py: Use AnalysisThresholds.CYCLOMATIC_COMPLEXITY"], "total_violations": 170, "status": "FAIL"}, "terminology": {"violations": ["vibe_check/compat.py: Use 'config' instead of 'configuration'", "vibe_check/__init__.py: Use 'config' instead of 'configuration'", "vibe_check/core/logging.py: Use 'config' instead of 'configuration'", "vibe_check/core/compatibility.py: Use 'config' instead of 'configuration'", "vibe_check/core/config.py: Use 'config' instead of 'configuration'", "vibe_check/core/models.py: Use 'config' instead of 'configuration'", "vibe_check/core/unified_analyzer.py: Use 'config' instead of 'configuration'", "vibe_check/core/simple_analyzer.py: Use 'config' instead of 'configuration'", "vibe_check/core/__init__.py: Use 'config' instead of 'configuration'", "vibe_check/core/error_handling.py: Use 'config' instead of 'configuration'"], "total_violations": 174, "status": "WARN"}, "imports": {"violations": [], "total_violations": 0, "status": "PASS"}, "code_quality": {"status": "ERROR", "error": "Command '['/Users/<USER>/Lokalne_Kody/PAT_project_analysis/.venv/bin/python3', '-m', 'vibe_check', 'analyze', 'vibe_check', '--profile', 'minimal']' timed out after 60 seconds"}, "security": {"high_severity_issues": 18, "medium_severity_issues": 7, "status": "FAIL"}}}