- id: vibe-check-minimal
  name: Vibe Check (Minimal)
  description: Fast code quality analysis for pre-commit (minimal rules)
  entry: vibe-check analyze --mode=precommit --level=minimal
  language: python
  types_or:
  - python
  - javascript
  - typescript
  - yaml
  - json
  args: []
  require_serial: false
  minimum_pre_commit_version: 2.9.2
- id: vibe-check-standard
  name: Vibe Check (Standard)
  description: Balanced code quality analysis for pre-commit
  entry: vibe-check analyze --mode=precommit --level=standard
  language: python
  types_or:
  - python
  - javascript
  - typescript
  - yaml
  - json
  args: []
  require_serial: false
  minimum_pre_commit_version: 2.9.2
- id: vibe-check-strict
  name: Vibe Check (Strict)
  description: Comprehensive code quality analysis for pre-commit
  entry: vibe-check analyze --mode=precommit --level=strict
  language: python
  types_or:
  - python
  - javascript
  - typescript
  - yaml
  - json
  args: []
  require_serial: true
  minimum_pre_commit_version: 2.9.2
