#!/usr/bin/env python3
"""
Terminology Consistency Audit Tool
==================================

This script analyzes the Vibe Check codebase for terminology consistency:
1. Class naming conventions and conflicts
2. Function and method naming patterns
3. Variable naming consistency
4. Documentation terminology alignment
5. User-facing text standardization

Strategic Focus:
- Identify naming conflicts and ambiguous terms
- Detect inconsistent terminology across modules
- Find opportunities for standardization
- Ensure alignment with project glossary
"""

import ast
import re
from pathlib import Path
from collections import defaultdict, Counter
from typing import Dict, List, Set, Tuple
from dataclasses import dataclass


@dataclass
class TerminologyIssue:
    """Represents a terminology consistency issue."""
    term: str
    issue_type: str  # 'naming_conflict', 'inconsistent_usage', 'ambiguous_term'
    files: List[str]
    contexts: List[str]
    suggestions: List[str]
    severity: str  # 'high', 'medium', 'low'


class TerminologyAnalyzer(ast.NodeVisitor):
    """AST visitor to analyze terminology usage."""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.classes = []
        self.functions = []
        self.variables = []
        self.imports = []
        self.docstrings = []
        self.current_context = []
    
    def visit_ClassDef(self, node: ast.ClassDef) -> None:
        """Analyze class names."""
        self.classes.append({
            'name': node.name,
            'line': node.lineno,
            'context': '.'.join(self.current_context),
            'file': self.file_path
        })
        
        # Extract docstring
        if (node.body and isinstance(node.body[0], ast.Expr) and 
            isinstance(node.body[0].value, ast.Constant) and 
            isinstance(node.body[0].value.value, str)):
            self.docstrings.append({
                'text': node.body[0].value.value,
                'type': 'class',
                'name': node.name,
                'file': self.file_path
            })
        
        self.current_context.append(f"class:{node.name}")
        self.generic_visit(node)
        self.current_context.pop()
    
    def visit_FunctionDef(self, node: ast.FunctionDef) -> None:
        """Analyze function names."""
        self.functions.append({
            'name': node.name,
            'line': node.lineno,
            'context': '.'.join(self.current_context),
            'file': self.file_path,
            'is_method': bool(self.current_context and 'class:' in self.current_context[-1])
        })
        
        # Extract docstring
        if (node.body and isinstance(node.body[0], ast.Expr) and 
            isinstance(node.body[0].value, ast.Constant) and 
            isinstance(node.body[0].value.value, str)):
            self.docstrings.append({
                'text': node.body[0].value.value,
                'type': 'function',
                'name': node.name,
                'file': self.file_path
            })
        
        self.current_context.append(f"function:{node.name}")
        self.generic_visit(node)
        self.current_context.pop()
    
    def visit_Name(self, node: ast.Name) -> None:
        """Analyze variable names."""
        if isinstance(node.ctx, ast.Store):  # Variable assignment
            self.variables.append({
                'name': node.id,
                'line': node.lineno,
                'context': '.'.join(self.current_context),
                'file': self.file_path
            })
        self.generic_visit(node)
    
    def visit_Import(self, node: ast.Import) -> None:
        """Analyze import statements."""
        for alias in node.names:
            self.imports.append({
                'name': alias.name,
                'alias': alias.asname,
                'line': node.lineno,
                'file': self.file_path
            })
        self.generic_visit(node)
    
    def visit_ImportFrom(self, node: ast.ImportFrom) -> None:
        """Analyze from-import statements."""
        module = node.module or ''
        for alias in node.names:
            self.imports.append({
                'name': f"{module}.{alias.name}" if module else alias.name,
                'alias': alias.asname,
                'line': node.lineno,
                'file': self.file_path
            })
        self.generic_visit(node)


def analyze_naming_conventions(all_data: Dict) -> List[TerminologyIssue]:
    """Analyze naming convention consistency."""
    issues = []
    
    # Check class naming (should be PascalCase)
    for class_info in all_data['classes']:
        name = class_info['name']
        if not re.match(r'^[A-Z][a-zA-Z0-9]*$', name):
            issues.append(TerminologyIssue(
                term=name,
                issue_type='naming_convention',
                files=[class_info['file']],
                contexts=[f"Class at line {class_info['line']}"],
                suggestions=[f"Use PascalCase: {to_pascal_case(name)}"],
                severity='medium'
            ))
    
    # Check function naming (should be snake_case)
    for func_info in all_data['functions']:
        name = func_info['name']
        if not name.startswith('_') and not re.match(r'^[a-z][a-z0-9_]*$', name):
            issues.append(TerminologyIssue(
                term=name,
                issue_type='naming_convention',
                files=[func_info['file']],
                contexts=[f"Function at line {func_info['line']}"],
                suggestions=[f"Use snake_case: {to_snake_case(name)}"],
                severity='medium'
            ))
    
    return issues


def find_terminology_conflicts(all_data: Dict) -> List[TerminologyIssue]:
    """Find conflicting terminology usage."""
    issues = []
    
    # Group similar terms
    term_groups = defaultdict(list)
    
    # Analyze class names for conflicts
    for class_info in all_data['classes']:
        base_term = extract_base_term(class_info['name'])
        term_groups[base_term].append(('class', class_info))
    
    # Analyze function names for conflicts
    for func_info in all_data['functions']:
        base_term = extract_base_term(func_info['name'])
        term_groups[base_term].append(('function', func_info))
    
    # Find conflicts
    for base_term, usages in term_groups.items():
        if len(usages) > 1:
            # Check for inconsistent naming patterns
            names = [usage[1]['name'] for usage in usages]
            if len(set(names)) > 1:  # Different variations of the same term
                files = [usage[1]['file'] for usage in usages]
                contexts = [f"{usage[0]}: {usage[1]['name']}" for usage in usages]
                
                issues.append(TerminologyIssue(
                    term=base_term,
                    issue_type='inconsistent_usage',
                    files=files,
                    contexts=contexts,
                    suggestions=[f"Standardize to one form: {suggest_standard_form(names)}"],
                    severity='high' if len(files) > 3 else 'medium'
                ))
    
    return issues


def analyze_domain_terminology(all_data: Dict) -> List[TerminologyIssue]:
    """Analyze domain-specific terminology consistency."""
    issues = []
    
    # Define domain terminology mappings
    domain_terms = {
        'analysis': ['analyze', 'analyzer', 'analysis', 'analyse'],
        'configuration': ['config', 'configuration', 'settings', 'options'],
        'metrics': ['metric', 'metrics', 'measurement', 'measure'],
        'error': ['error', 'exception', 'failure', 'issue'],
        'result': ['result', 'output', 'outcome', 'response'],
        'manager': ['manager', 'handler', 'controller', 'coordinator'],
        'processor': ['processor', 'handler', 'worker', 'executor'],
        'generator': ['generator', 'creator', 'builder', 'factory']
    }
    
    # Check for inconsistent domain term usage
    for domain, variants in domain_terms.items():
        usage_count = Counter()
        usage_files = defaultdict(list)
        
        # Count usage across all names
        for class_info in all_data['classes']:
            for variant in variants:
                if variant.lower() in class_info['name'].lower():
                    usage_count[variant] += 1
                    usage_files[variant].append(class_info['file'])
        
        for func_info in all_data['functions']:
            for variant in variants:
                if variant.lower() in func_info['name'].lower():
                    usage_count[variant] += 1
                    usage_files[variant].append(func_info['file'])
        
        # If multiple variants are used significantly, suggest standardization
        used_variants = [v for v, count in usage_count.items() if count > 2]
        if len(used_variants) > 1:
            most_common = usage_count.most_common(1)[0][0]
            all_files = []
            for variant in used_variants:
                all_files.extend(usage_files[variant])
            
            issues.append(TerminologyIssue(
                term=domain,
                issue_type='domain_inconsistency',
                files=list(set(all_files)),
                contexts=[f"Uses variants: {', '.join(used_variants)}"],
                suggestions=[f"Standardize to '{most_common}' for {domain} concepts"],
                severity='high'
            ))
    
    return issues


def extract_base_term(name: str) -> str:
    """Extract the base term from a name."""
    # Remove common suffixes and prefixes
    suffixes = ['Manager', 'Handler', 'Processor', 'Generator', 'Analyzer', 'Engine', 'Service']
    prefixes = ['Base', 'Abstract', 'Default', 'Simple']
    
    base = name
    for suffix in suffixes:
        if base.endswith(suffix):
            base = base[:-len(suffix)]
            break
    
    for prefix in prefixes:
        if base.startswith(prefix):
            base = base[len(prefix):]
            break
    
    return base.lower()


def to_pascal_case(name: str) -> str:
    """Convert name to PascalCase."""
    return ''.join(word.capitalize() for word in re.split(r'[_\s]+', name))


def to_snake_case(name: str) -> str:
    """Convert name to snake_case."""
    # Insert underscores before uppercase letters
    s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
    return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()


def suggest_standard_form(names: List[str]) -> str:
    """Suggest a standard form from a list of name variations."""
    # Return the most common or longest form
    return max(names, key=len)


def analyze_file(file_path: Path) -> Dict:
    """Analyze a single Python file for terminology."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content, filename=str(file_path))
        analyzer = TerminologyAnalyzer(str(file_path))
        analyzer.visit(tree)
        
        return {
            'classes': analyzer.classes,
            'functions': analyzer.functions,
            'variables': analyzer.variables,
            'imports': analyzer.imports,
            'docstrings': analyzer.docstrings
        }
        
    except (SyntaxError, UnicodeDecodeError) as e:
        print(f"Warning: Could not analyze {file_path}: {e}")
        return {'classes': [], 'functions': [], 'variables': [], 'imports': [], 'docstrings': []}


def main():
    """Main terminology analysis function."""
    print("📝 Starting Terminology Consistency Audit")
    print("=" * 50)
    
    # Find all Python files
    vibe_check_dir = Path("vibe_check")
    if not vibe_check_dir.exists():
        print("Error: vibe_check directory not found")
        return
    
    python_files = list(vibe_check_dir.rglob("*.py"))
    print(f"Found {len(python_files)} Python files to analyze")
    
    # Analyze all files
    all_data = {
        'classes': [],
        'functions': [],
        'variables': [],
        'imports': [],
        'docstrings': []
    }
    
    for file_path in python_files:
        file_data = analyze_file(file_path)
        for key in all_data:
            all_data[key].extend(file_data[key])
    
    print(f"Found {len(all_data['classes'])} classes, {len(all_data['functions'])} functions")
    
    # Analyze terminology issues
    all_issues = []
    all_issues.extend(analyze_naming_conventions(all_data))
    all_issues.extend(find_terminology_conflicts(all_data))
    all_issues.extend(analyze_domain_terminology(all_data))
    
    # Generate report
    print(f"\n📊 Terminology Analysis Report")
    print("=" * 50)
    
    high_issues = [i for i in all_issues if i.severity == 'high']
    medium_issues = [i for i in all_issues if i.severity == 'medium']
    
    print(f"\n🚨 HIGH PRIORITY ISSUES ({len(high_issues)}):")
    for issue in high_issues[:10]:
        print(f"  • {issue.issue_type}: {issue.term}")
        print(f"    Files affected: {len(issue.files)}")
        print(f"    Suggestion: {issue.suggestions[0] if issue.suggestions else 'Review manually'}")
        print()
    
    print(f"\n⚠️  MEDIUM PRIORITY ISSUES ({len(medium_issues)}):")
    for issue in medium_issues[:5]:
        print(f"  • {issue.issue_type}: {issue.term}")
        print(f"    Files affected: {len(issue.files)}")
        print()
    
    return all_issues


if __name__ == "__main__":
    issues = main()
