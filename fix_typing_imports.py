#!/usr/bin/env python3
"""
Strategic Fix: Missing Type Imports
==================================

This script systematically fixes missing typing imports across the codebase
to resolve the 18 test collection errors.

Root Cause: Missing `from typing import` statements
Strategic Solution: Automated import fixing with validation
"""

import re
import os
from pathlib import Path
from typing import List, Set, Dict, Tu<PERSON>

def find_missing_typing_imports(file_path: Path) -> Tuple[Set[str], List[str]]:
    """Find missing typing imports in a Python file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return set(), []
    
    # Find all typing usage patterns
    typing_patterns = {
        'Optional': r'\bOptional\[',
        'Union': r'\bUnion\[',
        'List': r'\bList\[',
        'Dict': r'\bDict\[',
        'Tuple': r'\bTuple\[',
        'Set': r'\bSet\[',
        'Any': r'\bAny\b',
        'Callable': r'\bCallable\[',
        'TypeVar': r'\bTypeVar\(',
        'Generic': r'\bGeneric\[',
        'Protocol': r'\bProtocol\b',
        'Literal': r'\bLiteral\[',
        'Final': r'\bFinal\[',
        'ClassVar': r'\bClassVar\[',
    }
    
    used_types = set()
    for type_name, pattern in typing_patterns.items():
        if re.search(pattern, content):
            used_types.add(type_name)
    
    # Check existing imports
    existing_imports = set()
    
    # Check for existing typing imports
    typing_import_patterns = [
        r'from typing import ([^#\n]+)',
        r'import typing',
    ]
    
    for pattern in typing_import_patterns:
        matches = re.findall(pattern, content)
        for match in matches:
            if isinstance(match, str):
                # Parse comma-separated imports
                imports = [imp.strip() for imp in match.split(',')]
                existing_imports.update(imports)
    
    missing_types = used_types - existing_imports
    
    # Get content lines for context
    lines = content.split('\n')
    
    return missing_types, lines

def fix_typing_imports(file_path: Path) -> bool:
    """Fix missing typing imports in a file."""
    missing_types, lines = find_missing_typing_imports(file_path)
    
    if not missing_types:
        return False  # No changes needed
    
    print(f"Fixing {file_path}: Adding {missing_types}")
    
    # Find the best place to add the import
    import_line_idx = -1
    has_typing_import = False
    
    for i, line in enumerate(lines):
        if line.startswith('from typing import'):
            # Extend existing typing import
            existing_imports = line.replace('from typing import ', '').strip()
            all_imports = set(existing_imports.split(', ')) | missing_types
            new_import_line = f"from typing import {', '.join(sorted(all_imports))}"
            lines[i] = new_import_line
            has_typing_import = True
            break
        elif line.startswith('import ') or line.startswith('from '):
            import_line_idx = i
    
    if not has_typing_import:
        # Add new typing import after other imports
        if import_line_idx >= 0:
            new_import = f"from typing import {', '.join(sorted(missing_types))}"
            lines.insert(import_line_idx + 1, new_import)
        else:
            # Add at the beginning after docstring/comments
            insert_idx = 0
            for i, line in enumerate(lines):
                if line.strip() and not line.startswith('#') and not line.startswith('"""') and not line.startswith("'''"):
                    insert_idx = i
                    break
            new_import = f"from typing import {', '.join(sorted(missing_types))}"
            lines.insert(insert_idx, new_import)
    
    # Write back the fixed content
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        return True
    except Exception as e:
        print(f"Error writing {file_path}: {e}")
        return False

def main():
    """Main function to fix typing imports across the codebase."""
    print("🔧 STRATEGIC FIX: Missing Type Imports")
    print("=" * 45)
    
    # Find all Python files in the project
    project_root = Path('.')
    python_files = []
    
    for pattern in ['vibe_check/**/*.py', 'tests/**/*.py']:
        python_files.extend(project_root.glob(pattern))
    
    print(f"\nFound {len(python_files)} Python files to check")
    
    fixed_files = []
    error_files = []
    
    for file_path in python_files:
        try:
            if fix_typing_imports(file_path):
                fixed_files.append(file_path)
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
            error_files.append(file_path)
    
    print(f"\n📊 RESULTS:")
    print(f"✅ Fixed {len(fixed_files)} files")
    print(f"❌ Errors in {len(error_files)} files")
    
    if fixed_files:
        print(f"\n🔧 Fixed files:")
        for file_path in fixed_files[:10]:  # Show first 10
            print(f"  - {file_path}")
        if len(fixed_files) > 10:
            print(f"  ... and {len(fixed_files) - 10} more")
    
    if error_files:
        print(f"\n❌ Error files:")
        for file_path in error_files:
            print(f"  - {file_path}")
    
    print(f"\n✅ Strategic typing imports fix completed!")

if __name__ == "__main__":
    main()
