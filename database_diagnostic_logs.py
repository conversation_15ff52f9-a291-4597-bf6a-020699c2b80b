#!/usr/bin/env python3
"""
Database Diagnostic Logs
========================

Creates detailed diagnostic logs to validate the root cause assumptions
for the database connectivity issues found in the health check.
"""

import asyncio
import logging
import sys
import time
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from vibe_check.monitoring.storage.time_series_engine import TimeSeriesStorageEngine, TSDBConfig
from vibe_check.core.models.project_metrics import ProjectMetrics
from vibe_check.core.trend_analysis.trend_storage import TrendStorage

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def diagnose_tsdb_issue() -> None:
    """Diagnose the TSDB type comparison issue"""
    logger.info("=== DIAGNOSING TSDB TYPE COMPARISON ISSUE ===")
    
    try:
        # Create TSDB with test configuration
        config = TSDBConfig(data_dir=Path(".vibe_check_tsdb_diagnostic"))
        tsdb = TimeSeriesStorageEngine(config)
        
        logger.info("✓ TSDB initialization successful")
        
        # Get stats to see the structure
        stats = tsdb.get_stats()
        logger.info(f"Stats structure: {type(stats)}")
        logger.info(f"Stats content: {stats}")
        
        # Check cache manager stats specifically
        if hasattr(tsdb.cache_manager, 'get_stats'):
            cache_stats = tsdb.cache_manager.get_stats()
            logger.info(f"Cache stats type: {type(cache_stats)}")
            logger.info(f"Cache stats content: {cache_stats}")
        
        # Try to ingest a sample to trigger the error
        logger.info("Attempting metric ingestion...")
        try:
            result = await tsdb.ingest_sample("test_metric", 42.0, {"test": "label"})
            logger.info(f"✓ Ingestion successful: {result}")
        except Exception as e:
            logger.error(f"✗ Ingestion failed: {e}")
            logger.error(f"Error type: {type(e)}")
            
            # Try to trace where the comparison happens
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
        
        await tsdb.shutdown()
        
    except Exception as e:
        logger.error(f"TSDB diagnostic failed: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")

def diagnose_project_metrics_issue() -> None:
    """Diagnose the ProjectMetrics property setter issue"""
    logger.info("=== DIAGNOSING PROJECT METRICS PROPERTY ISSUE ===")
    
    try:
        # Create a ProjectMetrics instance
        metrics = ProjectMetrics(project_path="/test/path")
        logger.info("✓ ProjectMetrics creation successful")
        
        # Check the total_file_count property
        logger.info(f"total_file_count type: {type(metrics.total_file_count)}")
        logger.info(f"total_file_count value: {metrics.total_file_count}")
        
        # Check if it has a setter
        prop = getattr(ProjectMetrics, 'total_file_count', None)
        logger.info(f"total_file_count property object: {prop}")
        logger.info(f"Property type: {type(prop)}")
        
        if isinstance(prop, property):
            logger.info(f"Property fget: {prop.fget}")
            logger.info(f"Property fset: {prop.fset}")
            logger.info(f"Property fdel: {prop.fdel}")
            
            if prop.fset is None:
                logger.error("✗ CONFIRMED: total_file_count has no setter!")
            else:
                logger.info("✓ total_file_count has a setter")
        
        # Try to set the property to trigger the error
        logger.info("Attempting to set total_file_count...")
        try:
            # This should fail since total_file_count is read-only
            setattr(metrics, 'total_file_count', 10)
            logger.info("✓ Property setting successful")
        except Exception as e:
            logger.error(f"✗ Property setting failed: {e}")
            logger.error(f"Error type: {type(e)}")
        
        # Test TrendStorage with this metrics object
        logger.info("Testing TrendStorage with ProjectMetrics...")
        try:
            storage = TrendStorage()
            logger.info("✓ TrendStorage creation successful")
            
            # Try the _metrics_to_dict method that's likely causing the issue
            metrics_dict = storage._metrics_to_dict(metrics)
            logger.info(f"✓ _metrics_to_dict successful: {type(metrics_dict)}")
            logger.info(f"Dict keys: {list(metrics_dict.keys())}")
            
        except Exception as e:
            logger.error(f"✗ TrendStorage operation failed: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
        
    except Exception as e:
        logger.error(f"ProjectMetrics diagnostic failed: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")

def diagnose_property_access_patterns() -> None:
    """Diagnose how properties are accessed in ProjectMetrics"""
    logger.info("=== DIAGNOSING PROPERTY ACCESS PATTERNS ===")
    
    try:
        metrics = ProjectMetrics(project_path="/test/path")
        
        # Check all properties that might be read-only
        properties_to_check = [
            'total_file_count', 'total_line_count', 'max_complexity', 
            'issue_count', 'issues_by_severity', 'avg_complexity',
            'avg_doc_coverage', 'avg_type_coverage'
        ]
        
        for prop_name in properties_to_check:
            try:
                prop = getattr(ProjectMetrics, prop_name, None)
            if isinstance(prop, property):
                has_setter = prop.fset is not None
                logger.info(f"{prop_name}: property with setter={has_setter}")
                
                # Try to get the value
                value = getattr(metrics, prop_name)
                logger.info(f"  Current value: {value} (type: {type(value)})")
                
                # Try to set if it has a setter
                if has_setter:
                    try:
                        setattr(metrics, prop_name, value)
                        logger.info(f"  ✓ Setting successful")
                    except Exception as e:
                        logger.error(f"  ✗ Setting failed: {e}")
                else:
                    logger.warning(f"  ⚠ No setter available")
            else:
                logger.info(f"{prop_name}: regular attribute")
                
        except Exception as e:
            logger.error(f"Error checking {prop_name}: {e}")
        
    except Exception as e:
        logger.error(f"Property access diagnostic failed: {e}")

async def main() -> None:
    """Run all diagnostic tests"""
    logger.info("Starting comprehensive database diagnostic analysis...")
    
    # Diagnose TSDB issue
    await diagnose_tsdb_issue()
    
    # Diagnose ProjectMetrics issue
    diagnose_project_metrics_issue()
    
    # Diagnose property access patterns
    diagnose_property_access_patterns()
    
    logger.info("Diagnostic analysis completed")

if __name__ == "__main__":
    asyncio.run(main())