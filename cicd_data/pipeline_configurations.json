[{"config_id": "d0d3a08d-b742-4e14-a79d-a7c1d8b8d526", "name": "GitHub Actions Analysis Pipeline", "platform": "github_actions", "trigger_events": ["push", "pull_request"], "analysis_config": {"enabled_categories": ["style", "security", "complexity"], "severity_threshold": "warning", "fail_on_error": true, "generate_report": true}, "quality_gates": [], "notification_settings": {}, "environment_variables": {}, "steps": []}, {"config_id": "36609d69-8816-4403-9296-264e443a8a04", "name": "GitLab CI Analysis Pipeline", "platform": "gitlab_ci", "trigger_events": ["push", "merge_request"], "analysis_config": {"enabled_categories": ["security", "complexity", "docs"], "severity_threshold": "error", "fail_on_error": true}, "quality_gates": [], "notification_settings": {}, "environment_variables": {}, "steps": []}, {"config_id": "ff0598e3-79df-46a7-890d-75e228eb0d74", "name": "Jenkins Analysis Pipeline", "platform": "jenkins", "trigger_events": ["push", "schedule"], "analysis_config": {"enabled_categories": ["style", "security", "complexity", "types"], "severity_threshold": "warning"}, "quality_gates": [], "notification_settings": {}, "environment_variables": {}, "steps": []}, {"config_id": "10b904d6-8e72-4169-8df0-28347b9103e0", "name": "Azure DevOps Analysis Pipeline", "platform": "azure_devops", "trigger_events": ["push", "pull_request", "schedule"], "analysis_config": {"enabled_categories": ["style", "security", "complexity", "docs", "imports", "types"], "severity_threshold": "info", "generate_report": true}, "quality_gates": [], "notification_settings": {}, "environment_variables": {}, "steps": []}]