#!/usr/bin/env python3
"""
Strategic Import Syntax Fixer
=============================

This script fixes the systematic import syntax errors across the Vibe Check codebase
where 'from .module import (' statements are missing, causing IndentationError.

<PERSON><PERSON> identified:
- Files have import lists starting with indented class names
- Missing the 'from .module import (' prefix
- Affects 21+ files across enterprise and UI modules

This represents an architectural issue where files were likely generated
from templates with incomplete import statements.
"""

import re
import os
from pathlib import Path
from typing import List, Tuple, Dict

def detect_missing_import_pattern(file_path: Path) -> Tuple[bool, List[str], int]:
    """
    Detect if a file has the missing import pattern.
    
    Returns:
        (has_pattern, import_lines, start_line_number)
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Look for pattern: indented class names without preceding 'from' statement
        for i, line in enumerate(lines):
            # Check if line starts with 4+ spaces and contains a class name followed by comma
            if re.match(r'^    [A-Z][a-zA-Z0-9_]*,?\s*$', line.strip()):
                # Check if previous non-empty line is not a 'from' statement
                prev_line_idx = i - 1
                while prev_line_idx >= 0 and lines[prev_line_idx].strip() == '':
                    prev_line_idx -= 1
                
                if prev_line_idx >= 0:
                    prev_line = lines[prev_line_idx].strip()
                    if not prev_line.startswith('from ') and not prev_line.endswith('('):
                        # Found the pattern - collect all import lines
                        import_lines = []
                        j = i
                        while j < len(lines):
                            current_line = lines[j].strip()
                            if re.match(r'^[A-Z][a-zA-Z0-9_]*,?\s*$', current_line) or current_line == ')':
                                import_lines.append(current_line)
                                if current_line == ')':
                                    break
                                j += 1
                            else:
                                break
                        return True, import_lines, i + 1  # 1-based line number
        
        return False, [], 0
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return False, [], 0

def infer_import_module(file_path: Path, import_classes: List[str]) -> str:
    """
    Infer the module name to import from based on file location and class names.
    """
    # Common patterns for module names based on file location
    parent_dir = file_path.parent.name
    file_name = file_path.stem
    
    # For __init__.py files, try to infer from class names or use common patterns
    if file_name == '__init__':
        # Common module name patterns
        if 'Manager' in ' '.join(import_classes):
            return 'manager'
        elif 'Server' in ' '.join(import_classes):
            return 'server'
        elif 'API' in ' '.join(import_classes):
            return 'models'
        elif 'Pipeline' in ' '.join(import_classes):
            return 'models'
        elif any('Analysis' in cls for cls in import_classes):
            return 'models'
        else:
            return 'models'  # Default fallback
    else:
        # For specific files, usually import from models
        return 'models'

def fix_import_syntax(file_path: Path) -> bool:
    """
    Fix the import syntax in a single file.
    
    Returns:
        True if file was modified, False otherwise
    """
    has_pattern, import_lines, start_line = detect_missing_import_pattern(file_path)
    
    if not has_pattern:
        return False
    
    print(f"Fixing {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Extract class names (remove commas and closing paren)
        class_names = []
        for line in import_lines:
            clean_line = line.replace(',', '').replace(')', '').strip()
            if clean_line and clean_line != ')':
                class_names.append(clean_line)
        
        # Infer module name
        module_name = infer_import_module(file_path, class_names)
        
        # Create the proper import statement
        if len(class_names) == 1:
            import_statement = f"from .{module_name} import {class_names[0]}\n"
        else:
            import_statement = f"from .{module_name} import (\n"
            for i, class_name in enumerate(class_names):
                if i == len(class_names) - 1:
                    import_statement += f"    {class_name}\n"
                else:
                    import_statement += f"    {class_name},\n"
            import_statement += ")\n"
        
        # Replace the problematic lines
        # Find the range of lines to replace
        start_idx = start_line - 1  # Convert to 0-based
        end_idx = start_idx
        
        # Find the end of the import block
        while end_idx < len(lines):
            line = lines[end_idx].strip()
            if line == ')' or (line and not re.match(r'^[A-Z][a-zA-Z0-9_]*,?\s*$', line)):
                if line == ')':
                    end_idx += 1
                break
            end_idx += 1
        
        # Replace the lines
        new_lines = lines[:start_idx] + [import_statement] + lines[end_idx:]
        
        # Write back to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(new_lines)
        
        print(f"  ✅ Fixed import in {file_path}")
        return True
        
    except Exception as e:
        print(f"  ❌ Error fixing {file_path}: {e}")
        return False

def main():
    """Main function to fix import syntax across the codebase."""
    print("🔧 Strategic Import Syntax Fixer")
    print("=" * 50)
    
    # Define the directories to scan
    base_dir = Path("vibe_check")
    target_dirs = [
        base_dir / "enterprise",
        base_dir / "ui"
    ]
    
    fixed_files = []
    total_files = 0
    
    for target_dir in target_dirs:
        if not target_dir.exists():
            print(f"⚠️  Directory not found: {target_dir}")
            continue
            
        print(f"\n📁 Scanning {target_dir}")
        
        # Find all Python files
        for py_file in target_dir.rglob("*.py"):
            total_files += 1
            if fix_import_syntax(py_file):
                fixed_files.append(py_file)
    
    print(f"\n📊 Summary:")
    print(f"   Total files scanned: {total_files}")
    print(f"   Files fixed: {len(fixed_files)}")
    
    if fixed_files:
        print(f"\n✅ Fixed files:")
        for file_path in fixed_files:
            print(f"   - {file_path}")
    
    print(f"\n🎯 Strategic Impact:")
    print(f"   - Resolved systematic import syntax errors")
    print(f"   - Improved architectural consistency")
    print(f"   - Enhanced future maintainability")

if __name__ == "__main__":
    main()
