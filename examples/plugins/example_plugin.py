"""
Example VCS Plugin
==================

This is an example plugin that demonstrates how to create custom rules
and analyzers for the VCS system.
"""

import ast
from typing import List, Dict, Any

from vibe_check.core.vcs.plugins import RulePlugin, AnalyzerPlugin, FormatterPlugin
from vibe_check.core.vcs.plugins.plugin_interface import PluginMetadata
from vibe_check.core.vcs.models import (
    AnalysisTarget, AnalysisResult, AnalysisContext, AnalysisIssue,
    RuleCategory, IssueSeverity
)
from vibe_check.core.vcs.registry import AnalysisRule


class ExampleRulePlugin(RulePlugin):
    """Example plugin that provides custom analysis rules."""
    
    @property
    def metadata(self) -> PluginMetadata:
        return PluginMetadata(
            name="example-rules",
            version="1.0.0",
            description="Example custom rules for demonstration",
            author="VCS Team",
            license="MIT",
            homepage="https://github.com/example/vcs-example-plugin"
        )
    
    async def initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the plugin."""
        self.config = config
        print(f"Initialized {self.metadata.name} plugin")
    
    async def cleanup(self) -> None:
        """Cleanup plugin resources."""
        print(f"Cleaned up {self.metadata.name} plugin")
    
    def get_rules(self) -> List[type]:
        """Get custom rules provided by this plugin."""
        return [
            TodoCommentRule,
            LongLineRule,
            MagicNumberRule
        ]
    
    def get_rule_categories(self) -> List[RuleCategory]:
        """Get supported rule categories."""
        return [RuleCategory.STYLE, RuleCategory.COMPLEXITY]


class TodoCommentRule(AnalysisRule):
    """Rule to detect TODO comments."""
    
    def __init__(self):
        super().__init__(
            rule_id="TODO001",
            category=RuleCategory.STYLE,
            name="TODO Comment Detection",
            description="Detects TODO comments that should be converted to issues",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Analyze for TODO comments."""
        issues = []
        lines = content.split('\n')
        
        for line_num, line in enumerate(lines, 1):
            if 'TODO' in line.upper():
                column = line.upper().find('TODO')
                issues.append(self.create_issue(
                    line=line_num,
                    column=column,
                    message="TODO comment found - consider creating a proper issue",
                    fix_suggestion="Create an issue in your issue tracker"
                ))
        
        return issues


class LongLineRule(AnalysisRule):
    """Rule to detect overly long lines."""
    
    def __init__(self):
        super().__init__(
            rule_id="LINE001",
            category=RuleCategory.STYLE,
            name="Long Line Detection",
            description="Detects lines that exceed the maximum length",
            severity=IssueSeverity.WARNING
        )
        self.max_length = 120  # Configurable
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Analyze for long lines."""
        issues = []
        lines = content.split('\n')
        
        for line_num, line in enumerate(lines, 1):
            if len(line) > self.max_length:
                issues.append(self.create_issue(
                    line=line_num,
                    column=self.max_length,
                    message=f"Line too long ({len(line)} > {self.max_length} characters)",
                    fix_suggestion="Break line into multiple lines"
                ))
        
        return issues


class MagicNumberRule(AnalysisRule):
    """Rule to detect magic numbers."""
    
    def __init__(self):
        super().__init__(
            rule_id="MAGIC001",
            category=RuleCategory.COMPLEXITY,
            name="Magic Number Detection",
            description="Detects magic numbers that should be constants",
            severity=IssueSeverity.INFO
        )
        self.allowed_numbers = {0, 1, -1, 2}  # Common acceptable numbers
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Analyze for magic numbers."""
        issues = []
        
        for node in ast.walk(ast_tree):
            if isinstance(node, ast.Num) and isinstance(node.n, (int, float)):
                if node.n not in self.allowed_numbers:
                    issues.append(self.create_issue(
                        line=node.lineno,
                        column=node.col_offset,
                        message=f"Magic number {node.n} should be a named constant",
                        fix_suggestion="Define this number as a named constant"
                    ))
        
        return issues


class ExampleAnalyzerPlugin(AnalyzerPlugin):
    """Example analyzer plugin for custom analysis."""
    
    @property
    def metadata(self) -> PluginMetadata:
        return PluginMetadata(
            name="example-analyzer",
            version="1.0.0",
            description="Example custom analyzer for file statistics",
            author="VCS Team",
            license="MIT"
        )
    
    async def initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the analyzer."""
        self.config = config
        print(f"Initialized {self.metadata.name} analyzer")
    
    async def cleanup(self) -> None:
        """Cleanup analyzer resources."""
        print(f"Cleaned up {self.metadata.name} analyzer")
    
    async def analyze(self, target: AnalysisTarget, context: AnalysisContext) -> AnalysisResult:
        """Perform custom file analysis."""
        content = target.get_content()
        lines = content.split('\n')
        
        issues = []
        
        # Check file size
        if len(lines) > 500:
            issues.append(AnalysisIssue(
                line=1,
                column=1,
                message=f"File is very large ({len(lines)} lines) - consider refactoring",
                severity=IssueSeverity.WARNING,
                rule_id="SIZE001",
                category=RuleCategory.COMPLEXITY,
                source="example-analyzer"
            ))
        
        # Check for empty lines at end
        if lines and not lines[-1].strip():
            issues.append(AnalysisIssue(
                line=len(lines),
                column=1,
                message="File ends with empty line",
                severity=IssueSeverity.INFO,
                rule_id="EMPTY001",
                category=RuleCategory.STYLE,
                source="example-analyzer"
            ))
        
        return AnalysisResult(
            target=target,
            issues=issues,
            success=True,
            execution_time=0.001,  # Mock timing
            rules_executed=["SIZE001", "EMPTY001"]
        )
    
    def get_supported_file_types(self) -> List[str]:
        """Get supported file types."""
        return ['.py', '.pyx', '.pyi']


class ExampleFormatterPlugin(FormatterPlugin):
    """Example formatter plugin for custom output format."""
    
    @property
    def metadata(self) -> PluginMetadata:
        return PluginMetadata(
            name="example-formatter",
            version="1.0.0",
            description="Example custom formatter for CSV output",
            author="VCS Team",
            license="MIT"
        )
    
    async def initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the formatter."""
        self.config = config
        print(f"Initialized {self.metadata.name} formatter")
    
    async def cleanup(self) -> None:
        """Cleanup formatter resources."""
        print(f"Cleaned up {self.metadata.name} formatter")
    
    def get_format_name(self) -> str:
        """Get format name."""
        return "csv"
    
    def format_results(self, results: List[AnalysisResult]) -> str:
        """Format results as CSV."""
        lines = ["File,Line,Column,Severity,Rule,Message"]
        
        for result in results:
            file_path = str(result.target.path) if result.target.path else "unknown"
            
            for issue in result.issues:
                line = f'"{file_path}",{issue.line},{issue.column},{issue.severity.value},"{issue.rule_id}","{issue.message}"'
                lines.append(line)
        
        return '\n'.join(lines)
    
    def get_file_extension(self) -> str:
        """Get file extension for this format."""
        return ".csv"


# Plugin discovery - these will be automatically discovered by the plugin loader
__all__ = ['ExampleRulePlugin', 'ExampleAnalyzerPlugin', 'ExampleFormatterPlugin']
