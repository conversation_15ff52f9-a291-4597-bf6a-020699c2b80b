# Vibe Check Pre-commit Configuration
# FastAPI Project Example

validation_level: strict
timeout: 60
fail_fast: false

# Rule configuration
rules:
  enabled_categories:
    - style
    - security
    - complexity
    - imports
    - types
    - documentation
  
  disabled_rules: []
  
  fastapi_specific:
    validate_routes: true
    check_dependencies: true
    security_headers: true
    validate_schemas: true

# Performance settings
performance:
  parallel_processing: true
  max_workers: 8
  cache_enabled: true

# Integration settings
integration:
  external_tools:
    - ruff
    - mypy
    - bandit
  
  exclude_patterns:
    - "tests/"
    - "__pycache__/"
    - "*.pyc"
    - ".git/"
    - "venv/"
    - ".pytest_cache/"
    - "htmlcov/"

# FastAPI-specific settings
fastapi:
  validate_openapi: true
  check_cors: true
  validate_middleware: true
  security_checks: true

# Type checking
type_checking:
  strict_mode: true
  check_untyped_defs: true
  warn_return_any: true

# Output settings
output:
  format: "detailed"
  show_progress: true
  quiet_mode: false
  include_metrics: true
