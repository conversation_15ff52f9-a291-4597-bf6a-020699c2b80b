# Vibe Check Pre-commit Configuration
# Basic Python Project Example

validation_level: standard
timeout: 30
fail_fast: true

# Rule configuration
rules:
  enabled_categories:
    - style
    - security
    - complexity
    - imports
  
  disabled_rules: []

# Performance settings
performance:
  parallel_processing: true
  max_workers: 4
  cache_enabled: true

# Integration settings
integration:
  external_tools: []
  
  exclude_patterns:
    - "tests/"
    - "__pycache__/"
    - "*.pyc"
    - ".git/"
    - ".venv/"
    - "venv/"

# Output settings
output:
  format: "summary"
  show_progress: false
  quiet_mode: true
