# Vibe Check Pre-commit Configuration
# Django Project Example

validation_level: standard
timeout: 45
fail_fast: false

# Rule configuration
rules:
  enabled_categories:
    - style
    - security
    - complexity
    - imports
    - documentation
  
  disabled_rules:
    - S001  # Line length (using black)
  
  django_specific:
    check_migrations: true
    validate_models: true
    security_middleware: true
    check_settings: true

# Performance settings
performance:
  parallel_processing: true
  max_workers: 6
  cache_enabled: true

# Integration settings
integration:
  external_tools:
    - ruff
    - mypy
  
  exclude_patterns:
    - "migrations/"
    - "static/"
    - "media/"
    - "locale/"
    - "__pycache__/"
    - "*.pyc"
    - ".git/"
    - "venv/"
    - "node_modules/"

# Django-specific settings
django:
  settings_module: "myproject.settings.local"
  check_migrations: true
  validate_templates: true

# Output settings
output:
  format: "detailed"
  show_progress: true
  quiet_mode: false
