# Contributing to <PERSON><PERSON> Check

Thank you for your interest in contributing to Vibe Check! This document provides guidelines and instructions for contributing to the project.

## 🚨 **CRITICAL: Phase 0 Completion Required**

**BEFORE MAKING ANY CONTRIBUTIONS**, please note that the project is currently in Phase 0 (Foundation) which is INCOMPLETE. All Phase 1 development is BLOCKED until foundational issues are resolved.

### Required Actions Before Contributing:
1. **Check Phase 0 Status**: Run `python scripts/verify_phase_0_completion.py`
2. **Review Completion Plan**: See [Phase 0 Completion Plan](docs/roadmap/PHASE_0_COMPLETION_PLAN.md)
3. **Focus on Foundation**: Only work on Phase 0 completion tasks unless specifically approved

### Current Critical Issues:
- ❌ Production print statements in codebase
- ❌ Incomplete actor system removal
- ❌ Broken test coverage system
- ⚠️ 35+ files over 600 lines requiring refactoring

**All pull requests will be automatically checked against Phase 0 completion criteria.**

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Environment](#development-environment)
- [Development Workflow](#development-workflow)
- [Coding Standards](#coding-standards)
- [Testing](#testing)
- [Documentation](#documentation)
- [Pull Request Process](#pull-request-process)
- [Release Process](#release-process)

## Code of Conduct

We are committed to providing a friendly, safe, and welcoming environment for all contributors. Please be respectful and considerate of others when participating in the project.

## Getting Started

1. Fork the repository on GitHub
2. Clone your fork locally:
   ```bash
   git clone https://github.com/your-username/vibe-check.git
   cd vibe-check
   ```
3. Set up the upstream remote:
   ```bash
   git remote add upstream https://github.com/original-owner/vibe-check.git
   ```
4. Create a branch for your work:
   ```bash
   git checkout -b feature/your-feature-name
   ```

## Development Environment

### Prerequisites

- Python 3.8 or higher
- pip
- virtualenv or conda (recommended)

### Setup

1. Create and activate a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. Install the package in development mode with all extras:
   ```bash
   pip install -e ".[full,dev]"
   ```

3. Install pre-commit hooks:
   ```bash
   pre-commit install
   ```

## Development Workflow

1. Make sure your main branch is up to date:
   ```bash
   git checkout main
   git pull upstream main
   ```

2. Create a new branch for your feature or bugfix:
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. Make your changes, following the [coding standards](#coding-standards)

4. Run tests to ensure your changes don't break existing functionality:
   ```bash
   pytest
   ```

5. Format your code:
   ```bash
   black .
   ```

6. Check types:
   ```bash
   mypy .
   ```

7. Lint your code:
   ```bash
   ruff check .
   ```

8. Commit your changes with a descriptive message:
   ```bash
   git commit -m "Add feature: your feature description"
   ```

9. Push your changes to your fork:
   ```bash
   git push origin feature/your-feature-name
   ```

10. Create a pull request on GitHub

## Coding Standards

We follow PEP 8 and use Black for code formatting. Here are some key guidelines:

### General

- Use 4 spaces for indentation (no tabs)
- Maximum line length is 88 characters (as per Black defaults)
- Use meaningful variable and function names
- Write docstrings for all public modules, functions, classes, and methods

### Imports

- Group imports in the following order:
  1. Standard library imports
  2. Related third-party imports
  3. Local application/library specific imports
- Within each group, imports should be sorted alphabetically

### Type Hints

- Use type hints for all function parameters and return values
- Use `Optional[T]` for parameters that can be None
- Use `Union[T1, T2]` for parameters that can be multiple types
- Use `Any` sparingly and only when necessary

### Docstrings

- Use Google-style docstrings
- Include descriptions for all parameters, return values, and exceptions
- Add examples for complex functions

Example:

```python
def analyze_file(file_path: str, config: Optional[Dict[str, Any]] = None) -> FileMetrics:
    """
    Analyze a single file and return metrics.
    
    Args:
        file_path: Path to the file to analyze
        config: Optional configuration dictionary
        
    Returns:
        FileMetrics object containing analysis results
        
    Raises:
        FileNotFoundError: If the file does not exist
        PermissionError: If the file cannot be read
        
    Example:
        >>> metrics = analyze_file("example.py")
        >>> print(metrics.complexity)
        5
    """
    # Implementation
```

## Testing

We use pytest for testing. All new features should include tests, and all tests must pass before a pull request can be merged.

### Running Tests

```bash
# Run all tests
pytest

# Run tests with coverage
pytest --cov=vibe_check

# Run specific tests
pytest tests/test_specific_module.py
```

### Writing Tests

- Place tests in the `tests/` directory
- Name test files with the `test_` prefix
- Name test functions with the `test_` prefix
- Use descriptive test names that explain what is being tested
- Use fixtures for common setup and teardown
- Mock external dependencies when appropriate

Example:

```python
def test_analyze_file_returns_correct_metrics():
    # Setup
    test_file = "tests/fixtures/example.py"
    
    # Execute
    metrics = analyze_file(test_file)
    
    # Assert
    assert metrics.line_count == 42
    assert metrics.complexity > 0
    assert len(metrics.issues) == 0
```

## Documentation

Good documentation is essential for the project. Please follow these guidelines:

- Update the README.md when adding new features
- Add or update docstrings for all public APIs
- Include examples for complex functionality
- Update the CHANGELOG.md for significant changes

## Pull Request Process

1. Ensure all tests pass and code is properly formatted
2. Update documentation as necessary
3. Submit the pull request to the `main` branch
4. Include a clear description of the changes and any relevant issue numbers
5. Wait for code review and address any feedback
6. Once approved, a maintainer will merge your pull request

## Release Process

Releases are managed by the project maintainers. The process is as follows:

1. Update version number in `vibe_check/__init__.py`
2. Update CHANGELOG.md with the new version and changes
3. Create a new release on GitHub with release notes
4. Build and upload the package to PyPI

If you have any questions or need help with the contribution process, please open an issue or reach out to the maintainers.

Thank you for contributing to Vibe Check!
