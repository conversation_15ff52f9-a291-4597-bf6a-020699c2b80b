"""
Test Suite for Preset Management System
=======================================

Comprehensive tests for the Vibe Check preset management system including
data models, validation, CRUD operations, and CLI integration.
"""

import pytest
import tempfile
import json
import yaml
from pathlib import Path
from datetime import datetime, timezone
from typing import Any
from unittest.mock import patch, MagicMock

from vibe_check.core.presets import (
    PresetManager, PresetValidator, PresetDataModel, PresetMetadata,
    RuleConfiguration, ExternalToolConfiguration, AnalysisConfiguration
)
from vibe_check.core.presets.exceptions import (
    PresetError, PresetNotFoundError, PresetValidationError,
    PresetIOError, PresetInheritanceError
)
from vibe_check.core.presets.models import PresetType, PresetStatus
from vibe_check.core.constants import AnalysisProfile


class TestPresetDataModels:
    """Test preset data model structures and validation."""
    
    def test_preset_metadata_creation(self) -> None:
        """Test PresetMetadata creation with required fields."""
        now = datetime.now(timezone.utc).isoformat()
        
        metadata = PresetMetadata(
            name="test-preset",
            description="Test preset for unit testing",
            version="1.0.0",
            author="Test Author",
            created_at=now,
            updated_at=now,
            tags=["test", "unit"],
            compatibility_version="1.0.0"
        )
        
        assert metadata["name"] == "test-preset"
        assert metadata["description"] == "Test preset for unit testing"
        assert metadata["version"] == "1.0.0"
        assert metadata["author"] == "Test Author"
        assert metadata["tags"] == ["test", "unit"]
    
    def test_rule_configuration_creation(self) -> None:
        """Test RuleConfiguration with various options."""
        rule_config = RuleConfiguration(
            categories=["style", "security"],
            exclude_categories=["performance"],
            rule_ids=["S001", "S002"],
            exclude_rules=["C001"],
            severity_filter="warning",
            auto_fix=True,
            vcs_rules_only=False,
            enable_framework_rules=True
        )
        
        assert rule_config["categories"] == ["style", "security"]
        assert rule_config["exclude_categories"] == ["performance"]
        assert rule_config["auto_fix"] is True
        assert rule_config["vcs_rules_only"] is False
    
    def test_complete_preset_data_model(self) -> None:
        """Test complete PresetDataModel structure."""
        now = datetime.now(timezone.utc).isoformat()
        
        preset_data: PresetDataModel = {
            "metadata": PresetMetadata(
                name="complete-test",
                description="Complete test preset",
                version="1.0.0",
                author="Test",
                created_at=now,
                updated_at=now,
                tags=["test"],
                compatibility_version="1.0.0"
            ),
            "rule_config": RuleConfiguration(
                categories=["style"],
                vcs_rules_only=True
            ),
            "tool_config": ExternalToolConfiguration(
                tools=["ruff", "mypy"],
                parallel_execution=True
            ),
            "analysis_config": AnalysisConfiguration(
                profile=AnalysisProfile.STANDARD,
                vcs_mode=True,
                detailed=True
            ),
            "inheritance": None,
            "overrides": {},
            "schema_version": "1.0"
        }
        
        assert preset_data["metadata"]["name"] == "complete-test"
        assert preset_data["rule_config"]["categories"] == ["style"]
        assert preset_data["tool_config"]["tools"] == ["ruff", "mypy"]
        assert preset_data["analysis_config"]["profile"] == AnalysisProfile.STANDARD


class TestPresetValidator:
    """Test preset validation framework."""
    
    def setup_method(self) -> None:
        """Setup test fixtures."""
        self.validator = PresetValidator()
        self.now = datetime.now(timezone.utc).isoformat()
    
    def create_valid_preset(self) -> PresetDataModel:
        """Create a valid preset for testing."""
        return {
            "metadata": {
                "name": "valid-preset",
                "description": "Valid test preset",
                "version": "1.0.0",
                "author": "Test",
                "created_at": self.now,
                "updated_at": self.now,
                "tags": ["test"],
                "compatibility_version": "1.0.0"
            },
            "rule_config": {
                "categories": ["style", "security"],
                "severity_filter": "warning",
                "auto_fix": False,
                "vcs_rules_only": False,
                "enable_framework_rules": True
            },
            "tool_config": {
                "tools": ["ruff", "mypy"],
                "parallel_execution": True,
                "timeout_seconds": 300
            },
            "analysis_config": {
                "profile": "standard",
                "vcs_mode": True,
                "detailed": False,
                "output_formats": ["markdown"],
                "use_gitignore": True
            },
            "inheritance": None,
            "overrides": {},
            "schema_version": "1.0"
        }
    
    def test_valid_preset_validation(self):
        """Test validation of a valid preset."""
        preset = self.create_valid_preset()
        result = self.validator.validate_preset(preset)
        
        assert result["valid"] is True
        assert result["status"] == PresetStatus.VALID.value
        assert len(result["errors"]) == 0
    
    def test_missing_required_sections(self):
        """Test validation with missing required sections."""
        preset = {"metadata": {}}  # Missing other required sections
        
        with pytest.raises(PresetValidationError) as exc_info:
            self.validator.validate_preset(preset, "test-preset")
        
        assert "Missing required section" in str(exc_info.value)
    
    def test_invalid_preset_name(self):
        """Test validation with invalid preset name."""
        preset = self.create_valid_preset()
        preset["metadata"]["name"] = "invalid name with spaces!"
        
        with pytest.raises(PresetValidationError) as exc_info:
            self.validator.validate_preset(preset, "test-preset")
        
        assert "alphanumeric characters" in str(exc_info.value)
    
    def test_invalid_categories(self):
        """Test validation with invalid rule categories."""
        preset = self.create_valid_preset()
        preset["rule_config"]["categories"] = ["invalid-category"]
        
        with pytest.raises(PresetValidationError) as exc_info:
            self.validator.validate_preset(preset, "test-preset")
        
        assert "Invalid rule categories" in str(exc_info.value)
    
    def test_conflicting_categories(self):
        """Test validation with conflicting category selections."""
        preset = self.create_valid_preset()
        preset["rule_config"]["categories"] = ["style", "security"]
        preset["rule_config"]["exclude_categories"] = ["style"]
        
        with pytest.raises(PresetValidationError) as exc_info:
            self.validator.validate_preset(preset, "test-preset")
        
        assert "cannot be both included and excluded" in str(exc_info.value)
    
    def test_invalid_tools(self):
        """Test validation with invalid external tools."""
        preset = self.create_valid_preset()
        preset["tool_config"]["tools"] = ["invalid-tool"]
        
        with pytest.raises(PresetValidationError) as exc_info:
            self.validator.validate_preset(preset, "test-preset")
        
        assert "Invalid external tools" in str(exc_info.value)
    
    def test_cross_section_compatibility_warnings(self):
        """Test cross-section compatibility validation."""
        preset = self.create_valid_preset()
        preset["rule_config"]["vcs_rules_only"] = True
        preset["analysis_config"]["vcs_mode"] = False
        
        result = self.validator.validate_preset(preset)
        
        assert result["valid"] is True  # Should be valid but with warnings
        assert len(result["warnings"]) > 0
        assert any("vcs_rules_only" in warning for warning in result["warnings"])


class TestPresetManager:
    """Test preset manager CRUD operations."""
    
    def setup_method(self):
        """Setup test fixtures with temporary directories."""
        self.temp_dir = tempfile.mkdtemp()
        self.user_presets_dir = Path(self.temp_dir) / "user_presets"
        self.manager = PresetManager(user_presets_dir=self.user_presets_dir)
        self.now = datetime.now(timezone.utc).isoformat()
    
    def create_test_preset(self, name: str = "test-preset") -> PresetDataModel:
        """Create a test preset."""
        return {
            "metadata": {
                "name": name,
                "description": f"Test preset {name}",
                "version": "1.0.0",
                "author": "Test",
                "created_at": self.now,
                "updated_at": self.now,
                "tags": ["test"],
                "compatibility_version": "1.0.0"
            },
            "rule_config": {
                "categories": ["style"],
                "vcs_rules_only": True,
                "enable_framework_rules": True
            },
            "tool_config": {
                "tools": ["ruff"],
                "parallel_execution": True,
                "timeout_seconds": 300
            },
            "analysis_config": {
                "profile": "standard",
                "vcs_mode": True,
                "detailed": False,
                "use_gitignore": True
            },
            "inheritance": None,
            "overrides": {},
            "schema_version": "1.0"
        }
    
    @pytest.mark.asyncio
    async def test_save_and_load_preset(self):
        """Test saving and loading a preset."""
        preset_data = self.create_test_preset()
        
        # Save preset
        preset_file = await self.manager.save_preset(preset_data)
        assert preset_file.exists()
        assert preset_file.name == "test-preset.yaml"
        
        # Load preset
        loaded_preset = await self.manager.load_preset("test-preset")
        assert loaded_preset["metadata"]["name"] == "test-preset"
        assert loaded_preset["rule_config"]["categories"] == ["style"]
    
    @pytest.mark.asyncio
    async def test_list_presets(self):
        """Test listing presets."""
        # Create test presets
        preset1 = self.create_test_preset("preset-1")
        preset2 = self.create_test_preset("preset-2")
        
        await self.manager.save_preset(preset1)
        await self.manager.save_preset(preset2)
        
        # List presets
        presets = await self.manager.list_presets(PresetType.USER)
        
        assert "preset-1" in presets
        assert "preset-2" in presets
        assert presets["preset-1"]["type"] == "user"
        assert presets["preset-2"]["type"] == "user"
    
    @pytest.mark.asyncio
    async def test_delete_preset(self):
        """Test deleting a preset."""
        preset_data = self.create_test_preset()
        
        # Save and then delete
        await self.manager.save_preset(preset_data)
        deleted = await self.manager.delete_preset("test-preset")
        
        assert deleted is True
        
        # Verify it's gone
        with pytest.raises(PresetNotFoundError):
            await self.manager.load_preset("test-preset")
    
    @pytest.mark.asyncio
    async def test_preset_not_found(self):
        """Test loading non-existent preset."""
        with pytest.raises(PresetNotFoundError) as exc_info:
            await self.manager.load_preset("non-existent")
        
        assert "non-existent" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_export_import_preset(self):
        """Test exporting and importing presets."""
        preset_data = self.create_test_preset()
        await self.manager.save_preset(preset_data)
        
        # Export preset
        export_file = Path(self.temp_dir) / "exported.yaml"
        exported_path = await self.manager.export_preset("test-preset", export_file)
        
        assert exported_path.exists()
        
        # Delete original and import
        await self.manager.delete_preset("test-preset")
        imported_name = await self.manager.import_preset(export_file)
        
        assert imported_name == "test-preset"
        
        # Verify imported preset
        loaded_preset = await self.manager.load_preset("test-preset")
        assert loaded_preset["metadata"]["name"] == "test-preset"


class TestPresetCLI:
    """Test preset CLI commands."""

    def setup_method(self):
        """Setup test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.user_presets_dir = Path(self.temp_dir) / "user_presets"

    @patch('vibe_check.cli.preset_commands.PresetManager')
    def test_list_presets_command(self, mock_manager_class):
        """Test preset list CLI command."""
        from click.testing import CliRunner
        from vibe_check.cli.preset_commands import list_presets

        # Mock manager
        mock_manager = MagicMock()
        mock_manager_class.return_value = mock_manager

        # Mock async method
        async def mock_list_presets(preset_type=None):
            return {
                "test-preset": {
                    "type": "user",
                    "metadata": {
                        "name": "test-preset",
                        "description": "Test preset",
                        "version": "1.0.0"
                    },
                    "status": "valid"
                }
            }

        mock_manager.list_presets = mock_list_presets

        runner = CliRunner()
        result = runner.invoke(list_presets, ['--format', 'table'])

        assert result.exit_code == 0
        assert "test-preset" in result.output
        assert "Test preset" in result.output

    @patch('vibe_check.cli.preset_commands.PresetManager')
    def test_create_preset_command(self, mock_manager_class):
        """Test preset create CLI command."""
        from click.testing import CliRunner
        from vibe_check.cli.preset_commands import create_preset

        # Mock manager
        mock_manager = MagicMock()
        mock_manager_class.return_value = mock_manager

        # Mock async method
        async def mock_save_preset(preset_data, overwrite=False):
            return Path("/tmp/test-preset.yaml")

        mock_manager.save_preset = mock_save_preset

        runner = CliRunner()
        result = runner.invoke(create_preset, [
            'test-preset',
            '--description', 'Test preset description',
            '--profile', 'standard',
            '--categories', 'style,security',
            '--tools', 'ruff,mypy',
            '--vcs-mode'
        ])

        assert result.exit_code == 0
        assert "Created preset 'test-preset'" in result.output

    @patch('vibe_check.cli.preset_commands.PresetManager')
    def test_validate_preset_command(self, mock_manager_class):
        """Test preset validate CLI command."""
        from click.testing import CliRunner
        from vibe_check.cli.preset_commands import validate_preset

        # Mock manager
        mock_manager = MagicMock()
        mock_manager_class.return_value = mock_manager

        # Mock async methods
        async def mock_find_preset_file(name):
            return Path("/tmp/test-preset.yaml")

        async def mock_validate_preset_file(preset_file):
            return {
                "valid": True,
                "status": "valid",
                "errors": [],
                "warnings": [],
                "preset_name": "test-preset"
            }

        mock_manager._find_preset_file = mock_find_preset_file
        mock_manager.validate_preset_file = mock_validate_preset_file

        runner = CliRunner()
        result = runner.invoke(validate_preset, ['test-preset'])

        assert result.exit_code == 0
        assert "is valid" in result.output


class TestPresetInheritance:
    """Test preset inheritance functionality."""

    def setup_method(self):
        """Setup test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.user_presets_dir = Path(self.temp_dir) / "user_presets"
        self.manager = PresetManager(user_presets_dir=self.user_presets_dir)
        self.now = datetime.now(timezone.utc).isoformat()

    def create_base_preset(self) -> PresetDataModel:
        """Create a base preset for inheritance testing."""
        return {
            "metadata": {
                "name": "base-preset",
                "description": "Base preset for inheritance",
                "version": "1.0.0",
                "author": "Test",
                "created_at": self.now,
                "updated_at": self.now,
                "tags": ["base"],
                "compatibility_version": "1.0.0"
            },
            "rule_config": {
                "categories": ["style", "security"],
                "auto_fix": False,
                "vcs_rules_only": False
            },
            "tool_config": {
                "tools": ["ruff"],
                "parallel_execution": True,
                "timeout_seconds": 300
            },
            "analysis_config": {
                "profile": "standard",
                "vcs_mode": False,
                "detailed": False
            },
            "inheritance": None,
            "overrides": {},
            "schema_version": "1.0"
        }

    def create_derived_preset(self) -> PresetDataModel:
        """Create a derived preset that inherits from base."""
        return {
            "metadata": {
                "name": "derived-preset",
                "description": "Derived preset with inheritance",
                "version": "1.0.0",
                "author": "Test",
                "created_at": self.now,
                "updated_at": self.now,
                "tags": ["derived"],
                "compatibility_version": "1.0.0"
            },
            "rule_config": {
                "categories": ["style", "security", "performance"],  # Extends base
                "auto_fix": True  # Overrides base
            },
            "tool_config": {
                "tools": ["ruff", "mypy"]  # Extends base
            },
            "analysis_config": {
                "detailed": True  # Overrides base
            },
            "inheritance": "base-preset",
            "overrides": {},
            "schema_version": "1.0"
        }

    @pytest.mark.asyncio
    async def test_preset_inheritance_resolution(self):
        """Test that preset inheritance is properly resolved."""
        # Save base preset
        base_preset = self.create_base_preset()
        await self.manager.save_preset(base_preset)

        # Save derived preset
        derived_preset = self.create_derived_preset()
        await self.manager.save_preset(derived_preset)

        # Load derived preset (should resolve inheritance)
        loaded_preset = await self.manager.load_preset("derived-preset")

        # Check that inheritance was resolved
        assert loaded_preset["inheritance"] is None  # Should be cleared after resolution

        # Check that base values are inherited
        assert loaded_preset["tool_config"]["parallel_execution"] is True  # From base
        assert loaded_preset["analysis_config"]["profile"] == "standard"  # From base

        # Check that overrides work
        assert loaded_preset["rule_config"]["auto_fix"] is True  # Overridden
        assert loaded_preset["analysis_config"]["detailed"] is True  # Overridden

        # Check that extensions work
        assert "performance" in loaded_preset["rule_config"]["categories"]  # Extended
        assert "mypy" in loaded_preset["tool_config"]["tools"]  # Extended
