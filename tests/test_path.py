"""
Test the Python path in the tests directory.
"""

import sys
import os
import pytest


def test_python_path():
    """Test the Python path."""
    print("\nPython Path:")
    for path in sys.path:
        print(f"  {path}")
    assert True


def test_current_working_directory():
    """Test the current working directory."""
    print("\nCurrent Working Directory:")
    print(f"  {os.getcwd()}")
    assert True


def test_check_module_files():
    """Check if the core module files exist."""
    print("\nChecking core module files:")

    # Check if the simple_analyzer.py file exists (replacement for actor system)
    simple_analyzer_path = os.path.join(os.getcwd(), "vibe_check", "core", "simple_analyzer.py")
    print(f"  simple_analyzer.py exists: {os.path.exists(simple_analyzer_path)}")

    # Check if the core __init__.py file exists
    core_init_path = os.path.join(os.getcwd(), "vibe_check", "core", "__init__.py")
    print(f"  core/__init__.py exists: {os.path.exists(core_init_path)}")

    # Verify actor_system directory has been removed
    actor_system_dir = os.path.join(os.getcwd(), "vibe_check", "core", "actor_system")
    print(f"  actor_system directory removed: {not os.path.exists(actor_system_dir)}")

    assert True
