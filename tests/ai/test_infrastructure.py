"""
AI Infrastructure Tests
=======================

Basic tests for AI infrastructure components including:
- AIModelManager basic functionality
- Model configuration and types
- Basic initialization testing

Tests focus on core functionality without complex external dependencies.
"""

import pytest
import asyncio
from pathlib import Path
from typing import Any

# Try to import AI components, skip tests if not available
try:
    from vibe_check.ai.infrastructure.model_manager import ModelConfig, ModelType, ModelStatus, AIModelManager
    AI_AVAILABLE = True
except ImportError as e:
    AI_AVAILABLE = False
    IMPORT_ERROR = str(e)


@pytest.mark.skipif(not AI_AVAILABLE, reason=f"AI infrastructure not available: {IMPORT_ERROR if not AI_AVAILABLE else ''}")
class TestModelConfig:
    """Test model configuration functionality."""

    def test_model_config_creation(self) -> None:
        """Test model configuration creation."""
        config = ModelConfig(
            model_id="test_model",
            name="Test Model",
            model_type=ModelType.CODE_ANALYSIS,
            description="Test model configuration"
        )

        assert config.model_id == "test_model"
        assert config.name == "Test Model"
        assert config.model_type == ModelType.CODE_ANALYSIS
        assert config.description == "Test model configuration"
        assert config.version == "1.0.0"  # Default value
        assert config.privacy_level == "high"  # Default value
        assert config.max_context_length == 4096  # Default value
        assert config.supports_streaming is True  # Default value

    def test_model_config_to_dict(self) -> None:
        """Test model configuration dictionary conversion."""
        config = ModelConfig(
            model_id="test_model",
            name="Test Model",
            model_type=ModelType.CODE_EXPLANATION,
            description="Test model"
        )

        config_dict = config.to_dict()

        assert isinstance(config_dict, dict)
        assert config_dict["model_id"] == "test_model"
        assert config_dict["name"] == "Test Model"
        assert config_dict["model_type"] == "code_explanation"
        assert config_dict["description"] == "Test model"


@pytest.mark.skipif(not AI_AVAILABLE, reason=f"AI infrastructure not available: {IMPORT_ERROR if not AI_AVAILABLE else ''}")
@pytest.mark.asyncio
class TestAIModelManager:
    """Test AI model manager functionality."""

    async def test_basic_initialization(self, tmp_path: Path) -> None:
        """Test basic AI model manager initialization."""
        manager = AIModelManager(models_dir=tmp_path / "ai_models")

        assert not manager._initialized
        assert manager.models_dir == tmp_path / "ai_models"
        assert manager.max_loaded_models == 3
        assert manager.auto_unload_timeout == 3600

        # Test that manager can be created without errors
        assert manager.model_configs == {}
        assert manager.model_instances == {}

    async def test_initialization_with_cleanup(self, tmp_path: Path) -> None:
        """Test AI model manager initialization and cleanup."""
        manager = AIModelManager(models_dir=tmp_path / "ai_models")

        try:
            await manager.initialize()

            assert manager._initialized
            assert len(manager.model_configs) >= 3  # Default models
            assert manager._cleanup_task is not None

        finally:
            # Clean up
            if hasattr(manager, '_cleanup_task') and manager._cleanup_task:
                manager._cleanup_task.cancel()
                try:
                    await manager._cleanup_task
                except asyncio.CancelledError:
                    pass


# Basic test to verify AI system can be imported
def test_ai_import_availability() -> None:
    """Test that AI components can be imported."""
    if AI_AVAILABLE:
        assert ModelType.CODE_ANALYSIS is not None
        assert ModelStatus.AVAILABLE is not None
        assert ModelConfig is not None
        assert AIModelManager is not None
    else:
        pytest.skip(f"AI infrastructure not available: {IMPORT_ERROR}")