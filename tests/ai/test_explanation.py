"""
AI Explanation Engine Tests
===========================

Comprehensive tests for the code explanation engine including:
- Code analysis and explanation generation
- Different explanation types and levels
- Pattern recognition and documentation
- Error handling and edge cases

Tests use mocked AI models to avoid external dependencies.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, patch

from vibe_check.ai.explanation import CodeExplanationEngine
from vibe_check.ai.explanation.explanation_engine import ExplanationType, ExplanationLevel


@pytest.mark.asyncio
class TestCodeExplanationEngine:
    """Test code explanation engine functionality."""
    
    async def test_initialization(self, mock_ai_model_manager):
        """Test explanation engine initialization."""
        engine = CodeExplanationEngine(model_manager=mock_ai_model_manager)
        
        assert not engine._initialized
        assert engine.model_manager == mock_ai_model_manager
        
        await engine.initialize()
        
        assert engine._initialized
    
    async def test_basic_code_explanation(self, mock_explanation_engine, sample_python_code):
        """Test basic code explanation generation."""
        explanation = await mock_explanation_engine.explain_code(
            code=sample_python_code,
            explanation_type=ExplanationType.OVERVIEW,
            level=ExplanationLevel.DETAILED
        )
        
        assert explanation is not None
        assert isinstance(explanation, dict)
        assert "explanation" in explanation
        assert "code_structure" in explanation
        assert "patterns_identified" in explanation
        assert "complexity_analysis" in explanation
        
        assert isinstance(explanation["explanation"], str)
        assert len(explanation["explanation"]) > 0
    
    async def test_function_explanation(self, mock_explanation_engine):
        """Test function-specific explanation."""
        function_code = '''
def calculate_fibonacci(n):
    """Calculate the nth Fibonacci number using recursion."""
    if n <= 1:
        return n
    return calculate_fibonacci(n-1) + calculate_fibonacci(n-2)
'''
        
        explanation = await mock_explanation_engine.explain_code(
            code=function_code,
            explanation_type=ExplanationType.FUNCTION,
            level=ExplanationLevel.COMPREHENSIVE
        )
        
        assert explanation is not None
        assert "explanation" in explanation
        assert "function_analysis" in explanation
        assert "parameters" in explanation
        assert "return_value" in explanation
        assert "complexity" in explanation
    
    async def test_class_explanation(self, mock_explanation_engine):
        """Test class-specific explanation."""
        class_code = '''
class MathUtils:
    """Utility class for mathematical operations."""
    
    def __init__(self):
        self.cache = {}
    
    def factorial(self, n):
        """Calculate factorial of n with caching."""
        if n in self.cache:
            return self.cache[n]
        
        if n <= 1:
            result = 1
        else:
            result = n * self.factorial(n-1)
        
        self.cache[n] = result
        return result
'''
        
        explanation = await mock_explanation_engine.explain_code(
            code=class_code,
            explanation_type=ExplanationType.CLASS,
            level=ExplanationLevel.DETAILED
        )
        
        assert explanation is not None
        assert "explanation" in explanation
        assert "class_analysis" in explanation
        assert "methods" in explanation
        assert "attributes" in explanation
        assert "design_patterns" in explanation
    
    async def test_algorithm_explanation(self, mock_explanation_engine):
        """Test algorithm-specific explanation."""
        algorithm_code = '''
def quicksort(arr):
    """Quick sort implementation."""
    if len(arr) <= 1:
        return arr
    
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]
    
    return quicksort(left) + middle + quicksort(right)
'''
        
        explanation = await mock_explanation_engine.explain_code(
            code=algorithm_code,
            explanation_type=ExplanationType.ALGORITHM,
            level=ExplanationLevel.COMPREHENSIVE
        )
        
        assert explanation is not None
        assert "explanation" in explanation
        assert "algorithm_analysis" in explanation
        assert "time_complexity" in explanation
        assert "space_complexity" in explanation
        assert "algorithm_type" in explanation
    
    async def test_pattern_explanation(self, mock_explanation_engine):
        """Test design pattern explanation."""
        pattern_code = '''
class Singleton:
    """Singleton design pattern implementation."""
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.initialized = True
            self.data = {}
'''
        
        explanation = await mock_explanation_engine.explain_code(
            code=pattern_code,
            explanation_type=ExplanationType.PATTERN,
            level=ExplanationLevel.DETAILED
        )
        
        assert explanation is not None
        assert "explanation" in explanation
        assert "pattern_analysis" in explanation
        assert "pattern_type" in explanation
        assert "implementation_details" in explanation
    
    async def test_security_explanation(self, mock_explanation_engine):
        """Test security-focused explanation."""
        security_code = '''
import hashlib
import secrets

def hash_password(password: str, salt: bytes = None) -> tuple:
    """Securely hash a password with salt."""
    if salt is None:
        salt = secrets.token_bytes(32)
    
    # Use PBKDF2 for password hashing
    hashed = hashlib.pbkdf2_hmac('sha256', 
                                password.encode('utf-8'), 
                                salt, 
                                100000)
    return hashed, salt
'''
        
        explanation = await mock_explanation_engine.explain_code(
            code=security_code,
            explanation_type=ExplanationType.SECURITY,
            level=ExplanationLevel.COMPREHENSIVE
        )
        
        assert explanation is not None
        assert "explanation" in explanation
        assert "security_analysis" in explanation
        assert "vulnerabilities" in explanation
        assert "best_practices" in explanation
        assert "recommendations" in explanation
    
    async def test_different_explanation_levels(self, mock_explanation_engine, sample_python_code):
        """Test different explanation detail levels."""
        # Test brief explanation
        brief_explanation = await mock_explanation_engine.explain_code(
            code=sample_python_code,
            explanation_type=ExplanationType.OVERVIEW,
            level=ExplanationLevel.BRIEF
        )
        
        assert brief_explanation is not None
        assert "explanation" in brief_explanation
        
        # Test detailed explanation
        detailed_explanation = await mock_explanation_engine.explain_code(
            code=sample_python_code,
            explanation_type=ExplanationType.OVERVIEW,
            level=ExplanationLevel.DETAILED
        )
        
        assert detailed_explanation is not None
        assert "explanation" in detailed_explanation
        
        # Test comprehensive explanation
        comprehensive_explanation = await mock_explanation_engine.explain_code(
            code=sample_python_code,
            explanation_type=ExplanationType.OVERVIEW,
            level=ExplanationLevel.COMPREHENSIVE
        )
        
        assert comprehensive_explanation is not None
        assert "explanation" in comprehensive_explanation
        
        # Test beginner explanation
        beginner_explanation = await mock_explanation_engine.explain_code(
            code=sample_python_code,
            explanation_type=ExplanationType.OVERVIEW,
            level=ExplanationLevel.BEGINNER
        )
        
        assert beginner_explanation is not None
        assert "explanation" in beginner_explanation
    
    async def test_code_structure_analysis(self, mock_explanation_engine, sample_complex_code):
        """Test code structure analysis."""
        explanation = await mock_explanation_engine.explain_code(
            code=sample_complex_code,
            explanation_type=ExplanationType.OVERVIEW,
            level=ExplanationLevel.DETAILED
        )
        
        assert explanation is not None
        assert "code_structure" in explanation
        
        structure = explanation["code_structure"]
        assert isinstance(structure, dict)
        assert "imports" in structure
        assert "classes" in structure
        assert "functions" in structure
        assert "complexity_metrics" in structure
    
    async def test_pattern_identification(self, mock_explanation_engine, sample_complex_code):
        """Test pattern identification in code."""
        explanation = await mock_explanation_engine.explain_code(
            code=sample_complex_code,
            explanation_type=ExplanationType.PATTERN,
            level=ExplanationLevel.DETAILED
        )
        
        assert explanation is not None
        assert "patterns_identified" in explanation
        
        patterns = explanation["patterns_identified"]
        assert isinstance(patterns, list)
    
    async def test_complexity_analysis(self, mock_explanation_engine, sample_complex_code):
        """Test complexity analysis."""
        explanation = await mock_explanation_engine.explain_code(
            code=sample_complex_code,
            explanation_type=ExplanationType.ALGORITHM,
            level=ExplanationLevel.COMPREHENSIVE
        )
        
        assert explanation is not None
        assert "complexity_analysis" in explanation
        
        complexity = explanation["complexity_analysis"]
        assert isinstance(complexity, dict)
        assert "cyclomatic_complexity" in complexity
        assert "cognitive_complexity" in complexity
        assert "maintainability_index" in complexity
    
    async def test_error_handling(self, mock_explanation_engine):
        """Test error handling for invalid code."""
        invalid_code = "def invalid_function(\n    # Incomplete function"
        
        # Should handle invalid code gracefully
        explanation = await mock_explanation_engine.explain_code(
            code=invalid_code,
            explanation_type=ExplanationType.OVERVIEW,
            level=ExplanationLevel.BRIEF
        )
        
        # Should still return some form of explanation or error info
        assert explanation is not None
    
    async def test_empty_code_handling(self, mock_explanation_engine):
        """Test handling of empty code."""
        empty_code = ""
        
        explanation = await mock_explanation_engine.explain_code(
            code=empty_code,
            explanation_type=ExplanationType.OVERVIEW,
            level=ExplanationLevel.BRIEF
        )
        
        assert explanation is not None
    
    async def test_large_code_handling(self, mock_explanation_engine):
        """Test handling of large code files."""
        # Generate a large code sample
        large_code = "\n".join([
            f"def function_{i}():",
            f"    '''Function number {i}'''",
            f"    return {i}"
        ] for i in range(100))
        
        explanation = await mock_explanation_engine.explain_code(
            code=large_code,
            explanation_type=ExplanationType.OVERVIEW,
            level=ExplanationLevel.BRIEF
        )
        
        assert explanation is not None
        assert "explanation" in explanation
    
    async def test_statistics(self, mock_explanation_engine):
        """Test explanation engine statistics."""
        stats = mock_explanation_engine.get_statistics()
        
        assert isinstance(stats, dict)
        assert "total_explanations" in stats
        assert "explanations_by_type" in stats
        assert "explanations_by_level" in stats
        assert "average_processing_time" in stats
        assert "cache_hit_rate" in stats
