"""
AI System Test Configuration
============================

Shared fixtures and configuration for AI system tests.
Provides mocked AI models and infrastructure components.
"""

import pytest
import asyncio
from pathlib import Path
from typing import Dict, Any, Optional
from unittest.mock import AsyncMock, MagicMock, patch

from vibe_check.ai.infrastructure import AIModelManager, PrivacyPreservingProcessor, ModelOptimizer
from vibe_check.ai.infrastructure.model_manager import ModelConfig, ModelType, ModelStatus
from vibe_check.ai.explanation import CodeExplanationEngine
from vibe_check.ai.refactoring import RefactoringSuggestionEngine
from vibe_check.ai.temporal import TemporalAnalysisEngine


@pytest.fixture
def mock_model_config():
    """Create a mock model configuration."""
    return ModelConfig(
        model_id="test_model_v1",
        name="Test Model v1",
        model_type=ModelType.CODE_ANALYSIS,
        description="Test model for unit testing",
        parameters={
            "temperature": 0.1,
            "max_tokens": 1024,
            "top_p": 0.9
        },
        requirements={
            "min_memory_gb": 1,
            "gpu_required": False
        },
        max_context_length=4096
    )


@pytest.fixture
async def mock_ai_model_manager(tmp_path, mock_model_config):
    """Create a mocked AI model manager."""
    manager = AIModelManager(models_dir=tmp_path / "ai_models")
    
    # Mock the model loading and response generation
    with patch.object(manager, 'generate_response', new_callable=AsyncMock) as mock_generate:
        mock_generate.return_value = "Mocked AI response for testing"
        
        with patch.object(manager, 'load_model', new_callable=AsyncMock) as mock_load:
            mock_load.return_value = True
            
            # Initialize with test configuration
            manager.model_configs[mock_model_config.model_id] = mock_model_config
            await manager.initialize()
            
            yield manager


@pytest.fixture
async def mock_privacy_processor():
    """Create a mocked privacy processor."""
    processor = PrivacyPreservingProcessor()
    
    # Mock the privacy processing methods
    with patch.object(processor, 'process_code_safely', new_callable=AsyncMock) as mock_process:
        mock_process.return_value = {
            "processed_code": "# Privacy-processed code",
            "privacy_level": "standard",
            "redacted_items": []
        }
        
        await processor.initialize()
        yield processor


@pytest.fixture
async def mock_explanation_engine(mock_ai_model_manager):
    """Create a mocked code explanation engine."""
    engine = CodeExplanationEngine(model_manager=mock_ai_model_manager)
    await engine.initialize()
    yield engine


@pytest.fixture
async def mock_refactoring_engine(mock_ai_model_manager):
    """Create a mocked refactoring suggestion engine."""
    engine = RefactoringSuggestionEngine(model_manager=mock_ai_model_manager)
    await engine.initialize()
    yield engine


@pytest.fixture
async def mock_temporal_engine(mock_ai_model_manager):
    """Create a mocked temporal analysis engine."""
    engine = TemporalAnalysisEngine(model_manager=mock_ai_model_manager)
    await engine.initialize()
    yield engine


@pytest.fixture
def sample_python_code():
    """Sample Python code for testing."""
    return '''
def calculate_fibonacci(n):
    """Calculate the nth Fibonacci number."""
    if n <= 1:
        return n
    return calculate_fibonacci(n-1) + calculate_fibonacci(n-2)

class MathUtils:
    """Utility class for mathematical operations."""
    
    def __init__(self):
        self.cache = {}
    
    def factorial(self, n):
        """Calculate factorial of n."""
        if n in self.cache:
            return self.cache[n]
        
        if n <= 1:
            result = 1
        else:
            result = n * self.factorial(n-1)
        
        self.cache[n] = result
        return result
'''


@pytest.fixture
def sample_complex_code():
    """Sample complex Python code for testing."""
    return '''
import asyncio
import json
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from pathlib import Path

@dataclass
class DataProcessor:
    """Advanced data processing class with async capabilities."""
    
    def __init__(self, config_path: Path):
        self.config_path = config_path
        self.data_cache: Dict[str, Any] = {}
        self.processing_queue: List[str] = []
    
    async def load_config(self) -> Dict[str, Any]:
        """Load configuration from file."""
        try:
            with open(self.config_path, 'r') as f:
                config = json.load(f)
            return config
        except FileNotFoundError:
            return {"default": True}
    
    async def process_data_batch(self, data_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process a batch of data items asynchronously."""
        tasks = []
        for item in data_items:
            task = asyncio.create_task(self._process_single_item(item))
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        return [r for r in results if not isinstance(r, Exception)]
    
    async def _process_single_item(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Process a single data item."""
        # Simulate processing time
        await asyncio.sleep(0.1)
        
        processed_item = {
            "id": item.get("id", "unknown"),
            "processed": True,
            "timestamp": item.get("timestamp"),
            "data": item.get("data", {})
        }
        
        return processed_item
'''


@pytest.fixture
def ai_test_data():
    """Test data for AI system testing."""
    return {
        "code_samples": [
            "def hello(): print('Hello, World!')",
            "class TestClass: pass",
            "import os\nprint(os.getcwd())",
            "for i in range(10): print(i)",
            "try: x = 1/0\nexcept: pass"
        ],
        "expected_patterns": [
            "Function Definition",
            "Object-Oriented Programming", 
            "Module Imports",
            "Loops and Iteration",
            "Exception Handling"
        ],
        "complexity_levels": ["Low", "Medium", "High", "Very high"],
        "explanation_types": ["overview", "function", "class", "algorithm", "pattern", "security"],
        "explanation_levels": ["brief", "detailed", "comprehensive", "beginner"]
    }


# Mock external AI model dependencies
@pytest.fixture(autouse=True)
def mock_external_ai_dependencies():
    """Mock external AI model dependencies to avoid actual model loading."""
    with patch('vibe_check.ai.infrastructure.model_manager.asyncio.sleep', new_callable=AsyncMock):
        yield
