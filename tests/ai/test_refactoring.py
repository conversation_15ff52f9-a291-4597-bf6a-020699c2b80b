"""
AI Refactoring Tests
====================

Basic tests for AI refactoring components including:
- RefactoringSuggestionEngine functionality
- Refactoring types and severity levels
- Basic refactoring workflow testing

Tests focus on core functionality without complex external dependencies.
"""

# Try to import AI components, skip tests if not available
try:
    from vibe_check.ai.refactoring import RefactoringSuggestionEngine
    from vibe_check.ai.refactoring.refactoring_engine import RefactoringType, RefactoringSeverity
    AI_AVAILABLE = True
except ImportError as e:
    AI_AVAILABLE = False
    IMPORT_ERROR = str(e)

import pytest
import asyncio
from pathlib import Path
from typing import Any


@pytest.mark.skipif(not AI_AVAILABLE, reason=f"AI infrastructure not available: {IMPORT_ERROR if not AI_AVAILABLE else ''}")
@pytest.mark.asyncio
class TestRefactoringSuggestionEngine:
    """Test refactoring suggestion engine functionality."""

    async def test_initialization(self, tmp_path: Path) -> None:
        """Test refactoring engine initialization."""
        from vibe_check.ai.infrastructure import AIModelManager

        model_manager = AIModelManager(models_dir=tmp_path / "ai_models")
        await model_manager.initialize()

        engine = RefactoringSuggestionEngine(model_manager=model_manager)

        assert not engine._initialized
        assert engine.model_manager == model_manager

        await engine.initialize()

        assert engine._initialized

        # Clean up
        if hasattr(model_manager, '_cleanup_task') and model_manager._cleanup_task:
            model_manager._cleanup_task.cancel()
            try:
                await model_manager._cleanup_task
            except asyncio.CancelledError:
                pass


# Basic test to verify refactoring components can be imported
def test_refactoring_import_availability() -> None:
    """Test that refactoring components can be imported."""
    if AI_AVAILABLE:
        assert RefactoringSuggestionEngine is not None
        assert RefactoringType.EXTRACT_METHOD is not None
        assert RefactoringSeverity.LOW is not None
    else:
        pytest.skip(f"AI infrastructure not available: {IMPORT_ERROR}")