"""
Test Suite for Advanced Preset Features (Phase 2)
=================================================

Comprehensive tests for advanced preset features including inheritance,
templates, dynamic generation, and enhanced validation.
"""

import pytest
import tempfile
import asyncio
from pathlib import Path
from datetime import datetime, timezone
from typing import Any, Dict
from unittest.mock import patch, MagicMock

from vibe_check.core.presets import (
    PresetManager, PresetValidator, PresetTemplateManager,
    DynamicPresetGenerator, ProjectAnalyzer, PresetDataModel
)
from vibe_check.core.presets.exceptions import PresetInheritanceError
from vibe_check.core.constants import AnalysisProfile


class TestAdvancedInheritance:
    """Test advanced preset inheritance system."""
    
    def setup_method(self) -> None:
        """Setup test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.user_presets_dir = Path(self.temp_dir) / "user_presets"
        self.manager = PresetManager(user_presets_dir=self.user_presets_dir)
        self.now = datetime.now(timezone.utc).isoformat()
    
    def create_base_preset(self) -> PresetDataModel:
        """Create a base preset for inheritance testing."""
        return {
            "metadata": {
                "name": "base-preset",
                "description": "Base preset for inheritance",
                "version": "1.0.0",
                "author": "Test",
                "created_at": self.now,
                "updated_at": self.now,
                "tags": ["base"],
                "compatibility_version": "1.0.0"
            },
            "rule_config": {
                "categories": ["style", "security"],
                "auto_fix": False,
                "vcs_rules_only": False,
                "enable_framework_rules": True
            },
            "tool_config": {
                "tools": ["ruff"],
                "parallel_execution": True,
                "timeout_seconds": 300
            },
            "analysis_config": {
                "profile": "standard",
                "vcs_mode": False,
                "detailed": False,
                "use_gitignore": True
            },
            "inheritance": None,
            "overrides": {},
            "schema_version": "1.0"
        }
    
    def create_derived_preset(self) -> PresetDataModel:
        """Create a derived preset that inherits from base."""
        return {
            "metadata": {
                "name": "derived-preset",
                "description": "Derived preset with inheritance",
                "version": "1.0.0",
                "author": "Test",
                "created_at": self.now,
                "updated_at": self.now,
                "tags": ["derived"],
                "compatibility_version": "1.0.0"
            },
            "rule_config": {
                "categories": ["style", "security", "performance"],  # Extends base
                "auto_fix": True  # Overrides base
            },
            "tool_config": {
                "tools": ["ruff", "mypy"]  # Extends base
            },
            "analysis_config": {
                "detailed": True  # Overrides base
            },
            "inheritance": "base-preset",
            "overrides": {},
            "schema_version": "1.0"
        }
    
    @pytest.mark.asyncio
    async def test_advanced_inheritance_resolution(self) -> None:
        """Test advanced inheritance resolution with conflict detection."""
        # Save base preset
        base_preset = self.create_base_preset()
        await self.manager.save_preset(base_preset)
        
        # Save derived preset
        derived_preset = self.create_derived_preset()
        await self.manager.save_preset(derived_preset)
        
        # Load derived preset (should resolve inheritance)
        loaded_preset = await self.manager.load_preset("derived-preset")
        
        # Check that inheritance was resolved
        assert loaded_preset["inheritance"] is None
        
        # Check that base values are inherited
        assert loaded_preset["tool_config"]["parallel_execution"] is True  # From base
        assert loaded_preset["analysis_config"]["profile"] == "standard"  # From base
        
        # Check that overrides work
        assert loaded_preset["rule_config"]["auto_fix"] is True  # Overridden
        assert loaded_preset["analysis_config"]["detailed"] is True  # Overridden
        
        # Check that categories are merged (union)
        categories = loaded_preset["rule_config"]["categories"]
        assert "style" in categories  # From base
        assert "security" in categories  # From base
        assert "performance" in categories  # From derived
        
        # Check that tools are merged (union)
        tools = loaded_preset["tool_config"]["tools"]
        assert "ruff" in tools  # From base
        assert "mypy" in tools  # From derived
    
    @pytest.mark.asyncio
    async def test_inheritance_conflict_detection(self) -> None:
        """Test detection of inheritance conflicts."""
        # Create conflicting preset
        conflicting_preset = {
            "metadata": {
                "name": "conflicting-preset",
                "description": "Preset with conflicts",
                "version": "1.0.0",
                "author": "Test",
                "created_at": self.now,
                "updated_at": self.now,
                "tags": ["conflict"],
                "compatibility_version": "1.0.0"
            },
            "rule_config": {
                "categories": ["style"],
                "exclude_categories": ["style"]  # Conflict!
            },
            "tool_config": {
                "tools": ["ruff"],
                "exclude_tools": ["ruff"]  # Conflict!
            },
            "analysis_config": {
                "profile": "standard",
                "fast_mode": True,
                "detailed": True  # Conflict with fast_mode!
            },
            "inheritance": None,
            "overrides": {},
            "schema_version": "1.0"
        }
        
        # Test conflict detection
        base_preset = self.create_base_preset()
        conflicts = await self.manager._detect_inheritance_conflicts(
            base_preset, conflicting_preset, conflicting_preset
        )
        
        assert len(conflicts) > 0
        assert any("both included and excluded" in conflict for conflict in conflicts)


class TestPresetTemplates:
    """Test preset template system."""
    
    def setup_method(self) -> None:
        """Setup test fixtures."""
        self.template_manager = PresetTemplateManager()
    
    def test_template_initialization(self) -> None:
        """Test that templates are properly initialized."""
        templates = self.template_manager.list_templates()
        
        # Check that expected templates exist
        expected_templates = ["web-dev", "data-science", "security-focused", "library-dev"]
        for template_name in expected_templates:
            assert template_name in templates
            
            template_info = templates[template_name]
            assert "description" in template_info
            assert "tags" in template_info
            assert "use_case" in template_info
    
    def test_get_template(self) -> None:
        """Test getting a specific template."""
        web_dev_template = self.template_manager.get_template("web-dev")
        
        assert web_dev_template is not None
        assert web_dev_template["metadata"]["name"] == "web-dev"
        assert "web" in web_dev_template["metadata"]["tags"]
        
        # Check web-dev specific configurations
        rule_config = web_dev_template["rule_config"]
        assert "security" in rule_config["categories"]
        assert rule_config["auto_fix"] is True
        
        tool_config = web_dev_template["tool_config"]
        assert "bandit" in tool_config["tools"]  # Security tool for web apps
    
    def test_data_science_template(self) -> None:
        """Test data science template specifics."""
        ds_template = self.template_manager.get_template("data-science")
        
        assert ds_template is not None
        
        # Check data science specific configurations
        rule_config = ds_template["rule_config"]
        assert "security" in rule_config.get("exclude_categories", [])  # Less security focus
        
        analysis_config = ds_template["analysis_config"]
        assert analysis_config["profile"] == AnalysisProfile.MINIMAL.value  # Fast analysis
        assert ".ipynb" in analysis_config["file_extensions"]  # Jupyter notebooks
    
    def test_security_focused_template(self) -> None:
        """Test security-focused template specifics."""
        security_template = self.template_manager.get_template("security-focused")
        
        assert security_template is not None
        
        # Check security specific configurations
        rule_config = security_template["rule_config"]
        assert rule_config["severity_filter"] == "info"  # Catch everything
        assert rule_config["auto_fix"] is False  # Manual review required
        
        tool_config = security_template["tool_config"]
        assert "bandit" in tool_config["tools"]
        assert "safety" in tool_config["tools"]
        assert tool_config["parallel_execution"] is False  # Sequential for thoroughness
    
    def test_create_preset_from_template(self) -> None:
        """Test creating a new preset from a template."""
        new_preset = self.template_manager.create_preset_from_template(
            "web-dev", "my-web-preset", "Custom web development preset", "Test Author"
        )
        
        assert new_preset is not None
        assert new_preset["metadata"]["name"] == "my-web-preset"
        assert new_preset["metadata"]["description"] == "Custom web development preset"
        assert new_preset["metadata"]["author"] == "Test Author"
        assert "custom" in new_preset["metadata"]["tags"]
        
        # Should inherit web-dev configuration
        assert "security" in new_preset["rule_config"]["categories"]
        assert "bandit" in new_preset["tool_config"]["tools"]


class TestDynamicPresetGeneration:
    """Test dynamic preset generation system."""
    
    def setup_method(self) -> None:
        """Setup test fixtures."""
        self.temp_project_dir = Path(tempfile.mkdtemp())
        self.generator = DynamicPresetGenerator()
    
    def create_mock_project(self, project_type: str) -> None:
        """Create a mock project structure."""
        if project_type == "web":
            # Create Flask-like structure
            (self.temp_project_dir / "app.py").touch()
            (self.temp_project_dir / "requirements.txt").write_text("flask==2.0.1\nrequests==2.25.1")
            
        elif project_type == "data-science":
            # Create data science structure
            (self.temp_project_dir / "analysis.ipynb").touch()
            (self.temp_project_dir / "data_processing.py").touch()
            (self.temp_project_dir / "requirements.txt").write_text("pandas==1.3.0\nnumpy==1.21.0\njupyter==1.0.0")
            
        elif project_type == "library":
            # Create library structure
            (self.temp_project_dir / "setup.py").touch()
            (self.temp_project_dir / "src").mkdir()
            (self.temp_project_dir / "src" / "__init__.py").touch()
            (self.temp_project_dir / "tests").mkdir()
            (self.temp_project_dir / "tests" / "test_main.py").touch()
    
    @pytest.mark.asyncio
    async def test_project_analysis_web(self) -> None:
        """Test project analysis for web application."""
        self.create_mock_project("web")
        
        analyzer = ProjectAnalyzer(self.temp_project_dir)
        analysis = await analyzer.analyze_project()
        
        assert "flask" in analysis["frameworks"]
        assert analysis["project_type"] == "web-application"
        assert analysis["security_needs"] == "high"  # Web apps need high security
        
        file_patterns = analysis["file_patterns"]
        assert file_patterns["python_files"] > 0
    
    @pytest.mark.asyncio
    async def test_project_analysis_data_science(self) -> None:
        """Test project analysis for data science project."""
        self.create_mock_project("data-science")
        
        analyzer = ProjectAnalyzer(self.temp_project_dir)
        analysis = await analyzer.analyze_project()
        
        assert "jupyter" in analysis["frameworks"]
        assert analysis["project_type"] == "data-science"
        assert analysis["security_needs"] == "low"  # Data science less security focused
        assert analysis["performance_focus"] == "high"  # Performance important for data
        
        file_patterns = analysis["file_patterns"]
        assert file_patterns["notebook_files"] > 0
    
    @pytest.mark.asyncio
    async def test_dynamic_preset_generation(self) -> None:
        """Test dynamic preset generation based on project analysis."""
        self.create_mock_project("web")
        
        preset = await self.generator.generate_preset(
            self.temp_project_dir, "auto-web-preset", "Test Author"
        )
        
        assert preset["metadata"]["name"] == "auto-web-preset"
        assert preset["metadata"]["author"] == "Test Author"
        assert "auto-generated" in preset["metadata"]["tags"]
        
        # Should be based on web-dev template
        rule_config = preset["rule_config"]
        assert "security" in rule_config["categories"]
        
        tool_config = preset["tool_config"]
        assert "ruff" in tool_config["tools"]
        assert "mypy" in tool_config["tools"]


class TestEnhancedValidation:
    """Test enhanced validation framework."""
    
    def setup_method(self) -> None:
        """Setup test fixtures."""
        self.validator = PresetValidator()
        self.now = datetime.now(timezone.utc).isoformat()
    
    def create_test_preset(self, **overrides: Any) -> PresetDataModel:
        """Create a test preset with optional overrides."""
        base_preset: PresetDataModel = {
            "metadata": {
                "name": "test-preset",
                "description": "Test preset",
                "version": "1.0.0",
                "author": "Test",
                "created_at": self.now,
                "updated_at": self.now,
                "tags": ["test"],
                "compatibility_version": "1.0.0"
            },
            "rule_config": {
                "categories": ["style"],
                "auto_fix": False,
                "vcs_rules_only": False
            },
            "tool_config": {
                "tools": ["ruff"],
                "parallel_execution": True,
                "timeout_seconds": 300
            },
            "analysis_config": {
                "profile": "standard",
                "vcs_mode": False,
                "detailed": False
            },
            "inheritance": None,
            "overrides": {},
            "schema_version": "1.0"
        }
        
        # Apply overrides
        for section, values in overrides.items():
            if section in base_preset:
                base_preset[section].update(values)
        
        return base_preset
    
    def test_dependency_validation(self) -> None:
        """Test dependency validation."""
        # Test VCS rules without VCS mode
        preset = self.create_test_preset(
            rule_config={"vcs_rules_only": True},
            analysis_config={"vcs_mode": False}
        )
        
        with pytest.raises(Exception):  # Should raise validation error
            self.validator.validate_preset(preset, "test-preset")
    
    def test_conflict_validation(self) -> None:
        """Test advanced conflict validation."""
        # Test fast mode with detailed output
        preset = self.create_test_preset(
            analysis_config={"fast_mode": True, "detailed": True}
        )
        
        result = self.validator.validate_preset(preset)
        assert len(result["warnings"]) > 0
        assert any("fast mode" in warning.lower() for warning in result["warnings"])
    
    def test_performance_validation(self) -> None:
        """Test performance implications validation."""
        # Test many tools
        preset = self.create_test_preset(
            tool_config={"tools": ["ruff", "mypy", "bandit", "safety", "pylint", "flake8"]}
        )
        
        result = self.validator.validate_preset(preset)
        assert len(result["warnings"]) > 0
        assert any("large number of tools" in warning.lower() for warning in result["warnings"])
    
    def test_jupyter_notebook_validation(self) -> None:
        """Test Jupyter notebook specific validation."""
        preset = self.create_test_preset(
            analysis_config={
                "file_extensions": [".py", ".ipynb"],
                "max_workers": 8  # High worker count with notebooks
            }
        )
        
        result = self.validator.validate_preset(preset)
        assert len(result["warnings"]) > 0
        assert any("jupyter" in warning.lower() for warning in result["warnings"])
