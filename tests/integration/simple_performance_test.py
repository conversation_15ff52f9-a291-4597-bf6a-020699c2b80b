#!/usr/bin/env python3
"""
Simple Performance Test
=======================

A simplified performance test that establishes baseline metrics without
complex imports that might fail.
"""

import time
import psutil
import os
import sys
import subprocess
import json
from pathlib import Path
from typing import Dict, Any


def measure_startup_time() -> float:
    """Measure Python startup time"""
    print("📏 Measuring Python startup time...")
    
    startup_times = []
    
    for i in range(3):
        start_time = time.time()
        
        # Test basic Python startup
        result = subprocess.run([
            sys.executable, '-c', 'print("Hello")'
        ], capture_output=True, text=True)
        
        end_time = time.time()
        startup_time = end_time - start_time
        startup_times.append(startup_time)
    
    avg_startup = sum(startup_times) / len(startup_times)
    print(f"  ✅ Average Python startup: {avg_startup:.3f}s")
    
    return avg_startup


def measure_memory_usage() -> Dict[str, float]:
    """Measure current memory usage"""
    print("💾 Measuring memory usage...")
    
    process = psutil.Process()
    memory_info = process.memory_info()
    
    metrics = {
        'rss_mb': memory_info.rss / 1024 / 1024,
        'vms_mb': memory_info.vms / 1024 / 1024,
    }
    
    print(f"  ✅ RSS Memory: {metrics['rss_mb']:.1f} MB")
    print(f"  ✅ VMS Memory: {metrics['vms_mb']:.1f} MB")
    
    return metrics


def measure_file_processing_speed() -> Dict[str, float]:
    """Measure file processing speed"""
    print("⚡ Measuring file processing speed...")
    
    # Count Python files in the project
    project_root = Path(".")
    python_files = list(project_root.rglob("*.py"))
    
    # Filter out virtual environment files
    python_files = [f for f in python_files if '.venv' not in str(f) and 'venv' not in str(f)]
    
    print(f"  📊 Found {len(python_files)} Python files")
    
    # Measure time to read all files
    start_time = time.time()
    total_lines = 0
    files_processed = 0
    
    for file_path in python_files[:100]:  # Limit to first 100 files
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = len(f.readlines())
                total_lines += lines
                files_processed += 1
        except Exception:
            pass
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    files_per_second = files_processed / processing_time if processing_time > 0 else 0
    lines_per_second = total_lines / processing_time if processing_time > 0 else 0
    
    metrics = {
        'processing_time': processing_time,
        'files_processed': files_processed,
        'total_lines': total_lines,
        'files_per_second': files_per_second,
        'lines_per_second': lines_per_second,
    }
    
    print(f"  ✅ Processed {files_processed} files in {processing_time:.3f}s")
    print(f"  ✅ Speed: {files_per_second:.1f} files/sec, {lines_per_second:.0f} lines/sec")
    
    return metrics


def measure_system_resources() -> Dict[str, float]:
    """Measure system resource usage"""
    print("🖥️  Measuring system resources...")
    
    # CPU usage
    cpu_percent = psutil.cpu_percent(interval=1)
    
    # Memory usage
    memory = psutil.virtual_memory()
    
    # Disk usage
    disk = psutil.disk_usage('/')
    
    metrics = {
        'cpu_percent': cpu_percent,
        'memory_total_gb': memory.total / 1024 / 1024 / 1024,
        'memory_available_gb': memory.available / 1024 / 1024 / 1024,
        'memory_percent': memory.percent,
        'disk_total_gb': disk.total / 1024 / 1024 / 1024,
        'disk_free_gb': disk.free / 1024 / 1024 / 1024,
        'disk_percent': (disk.used / disk.total) * 100,
    }
    
    print(f"  ✅ CPU: {cpu_percent:.1f}%")
    print(f"  ✅ Memory: {memory.percent:.1f}% ({memory.available / 1024 / 1024 / 1024:.1f}GB available)")
    print(f"  ✅ Disk: {metrics['disk_percent']:.1f}% ({metrics['disk_free_gb']:.1f}GB free)")
    
    return metrics


def test_monitoring_system_performance() -> Dict[str, float]:
    """Test our new monitoring system performance"""
    print("📊 Testing monitoring system performance...")
    
    try:
        # Test the demo monitoring system we created
        start_time = time.time()
        
        result = subprocess.run([
            sys.executable, 'demo_monitoring_system.py'
        ], capture_output=True, text=True, timeout=30)
        
        end_time = time.time()
        monitoring_time = end_time - start_time
        
        if result.returncode == 0:
            print(f"  ✅ Monitoring system test completed in {monitoring_time:.3f}s")
            return {'monitoring_test_time': monitoring_time, 'monitoring_test_success': 1}
        else:
            print(f"  ⚠️  Monitoring system test failed")
            return {'monitoring_test_time': monitoring_time, 'monitoring_test_success': 0}
            
    except subprocess.TimeoutExpired:
        print(f"  ⚠️  Monitoring system test timed out")
        return {'monitoring_test_time': 30.0, 'monitoring_test_success': 0}
    except Exception as e:
        print(f"  ⚠️  Monitoring system test error: {e}")
        return {'monitoring_test_time': 0, 'monitoring_test_success': 0}


def generate_performance_baseline() -> Dict[str, Any]:
    """Generate comprehensive performance baseline"""
    print("🚀 Generating Performance Baseline")
    print("=" * 40)
    
    # Collect all metrics
    startup_metrics = {'startup_time': measure_startup_time()}
    memory_metrics = measure_memory_usage()
    processing_metrics = measure_file_processing_speed()
    system_metrics = measure_system_resources()
    monitoring_metrics = test_monitoring_system_performance()
    
    # System information
    system_info = {
        'cpu_count': psutil.cpu_count(),
        'python_version': sys.version,
        'platform': sys.platform,
        'timestamp': time.time(),
    }
    
    # Combine all metrics
    baseline = {
        'startup': startup_metrics,
        'memory': memory_metrics,
        'processing': processing_metrics,
        'system': system_metrics,
        'monitoring': monitoring_metrics,
        'system_info': system_info,
    }
    
    # Calculate performance scores
    scores = calculate_performance_scores(baseline)
    baseline['performance_scores'] = scores
    
    return baseline


def calculate_performance_scores(baseline: Dict[str, Any]) -> Dict[str, float]:
    """Calculate performance scores (0-10 scale)"""
    
    # Performance targets
    targets = {
        'startup_time': 1.0,  # seconds
        'memory_usage_mb': 100,  # MB
        'files_per_second': 100,  # files/sec
        'cpu_percent': 50,  # %
    }
    
    scores = {}
    
    # Startup score
    startup_time = baseline['startup']['startup_time']
    scores['startup_score'] = max(0, 10 - (startup_time / targets['startup_time']) * 5)
    
    # Memory score
    memory_mb = baseline['memory']['rss_mb']
    scores['memory_score'] = max(0, 10 - (memory_mb / targets['memory_usage_mb']) * 2)
    
    # Processing speed score
    files_per_sec = baseline['processing']['files_per_second']
    scores['speed_score'] = min(10, (files_per_sec / targets['files_per_second']) * 10)
    
    # CPU score
    cpu_percent = baseline['system']['cpu_percent']
    scores['cpu_score'] = max(0, 10 - (cpu_percent / targets['cpu_percent']) * 5)
    
    # Monitoring system score
    monitoring_success = baseline['monitoring']['monitoring_test_success']
    monitoring_time = baseline['monitoring']['monitoring_test_time']
    scores['monitoring_score'] = monitoring_success * max(0, 10 - monitoring_time / 5)
    
    # Overall score
    scores['overall_score'] = sum(scores.values()) / len(scores)
    
    return scores


def generate_recommendations(baseline: Dict[str, Any]) -> list[str]:
    """Generate performance optimization recommendations"""
    recommendations = []
    
    startup_time = baseline['startup']['startup_time']
    if startup_time > 1.0:
        recommendations.append(f"🚀 Optimize startup time: {startup_time:.3f}s > 1.0s target")
    
    memory_mb = baseline['memory']['rss_mb']
    if memory_mb > 200:
        recommendations.append(f"💾 Reduce memory usage: {memory_mb:.1f}MB > 200MB target")
    
    files_per_sec = baseline['processing']['files_per_second']
    if files_per_sec < 50:
        recommendations.append(f"⚡ Improve processing speed: {files_per_sec:.1f} < 50 files/sec target")
    
    cpu_percent = baseline['system']['cpu_percent']
    if cpu_percent > 80:
        recommendations.append(f"🖥️  Optimize CPU usage: {cpu_percent:.1f}% is high")
    
    monitoring_success = baseline['monitoring']['monitoring_test_success']
    if not monitoring_success:
        recommendations.append("📊 Fix monitoring system test failures")
    
    if not recommendations:
        recommendations.append("✅ Performance is within acceptable targets")
    
    return recommendations


def main():
    """Main function"""
    print("🎯 Vibe Check Performance Baseline Test")
    print("=" * 50)
    
    # Generate baseline
    baseline = generate_performance_baseline()
    
    # Generate recommendations
    recommendations = generate_recommendations(baseline)
    
    # Display summary
    print("\n" + "=" * 50)
    print("📊 PERFORMANCE BASELINE SUMMARY")
    print("=" * 50)
    
    scores = baseline['performance_scores']
    print(f"Overall Score: {scores['overall_score']:.1f}/10")
    print(f"Startup Score: {scores['startup_score']:.1f}/10")
    print(f"Memory Score: {scores['memory_score']:.1f}/10")
    print(f"Speed Score: {scores['speed_score']:.1f}/10")
    print(f"CPU Score: {scores['cpu_score']:.1f}/10")
    print(f"Monitoring Score: {scores['monitoring_score']:.1f}/10")
    
    print(f"\n📈 KEY METRICS:")
    print(f"Startup Time: {baseline['startup']['startup_time']:.3f}s")
    print(f"Memory Usage: {baseline['memory']['rss_mb']:.1f}MB")
    print(f"Processing Speed: {baseline['processing']['files_per_second']:.1f} files/sec")
    print(f"CPU Usage: {baseline['system']['cpu_percent']:.1f}%")
    
    print(f"\n💡 RECOMMENDATIONS:")
    for rec in recommendations:
        print(f"  {rec}")
    
    # Save baseline
    with open('performance_baseline_simple.json', 'w') as f:
        json.dump(baseline, f, indent=2)
    
    print(f"\n📄 Baseline saved to: performance_baseline_simple.json")
    print("✅ Performance baseline establishment complete!")


if __name__ == "__main__":
    main()
