#!/usr/bin/env python3
"""
Time-Series Charts Test
=======================

Test advanced time-series chart components with multi-metric overlays and correlation visualization.
"""

import time
import math
import random
from pathlib import Path
import sys

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

def print_header(title: str, level: int = 1) -> None:
    """Print formatted section header"""
    symbols = ["🚀", "🔧", "📊", "✅", "⚡"]
    symbol = symbols[min(level-1, len(symbols)-1)]
    separator = "=" * 60 if level == 1 else "-" * 50
    print(f"\n{symbol} {title}")
    print(separator)

def print_result(test_name: str, success: bool, details: str = "") -> None:
    """Print test result with formatting"""
    status = "✅" if success else "❌"
    print(f"  {status} {test_name}")
    if details:
        for line in details.split('\n'):
            if line.strip():
                print(f"    • {line.strip()}")

def generate_test_data(metric_name: str, base_time: float, points: int = 100) -> list:
    """Generate test time-series data"""
    from vibe_check.monitoring.visualization.charts import TimeSeriesPoint
    
    data = []
    for i in range(points):
        timestamp = base_time + i * 60  # 1-minute intervals
        
        # Generate different patterns for different metrics
        if "cpu" in metric_name:
            # CPU with daily pattern + noise
            value = 50 + 20 * math.sin(2 * math.pi * i / 24) + random.gauss(0, 5)
        elif "memory" in metric_name:
            # Memory with gradual increase + noise
            value = 60 + i * 0.1 + random.gauss(0, 3)
        elif "network" in metric_name:
            # Network with spikes + noise
            value = 100 + (50 if i % 20 == 0 else 0) + random.gauss(0, 10)
        else:
            # Default pattern
            value = 50 + random.gauss(0, 10)
        
        data.append(TimeSeriesPoint(
            timestamp=timestamp,
            value=max(0, value),  # Ensure non-negative
            labels={"instance": "server1", "job": "monitoring"}
        ))
    
    return data

def test_time_series_processor():
    """Test time-series data processing"""
    print_header("Time-Series Processor Test", 2)
    
    try:
        from vibe_check.monitoring.visualization.charts import (
            TimeSeriesProcessor, TimeSeriesPoint, AggregationType
        )
        
        # Generate test data
        base_time = time.time()
        test_points = generate_test_data("cpu_usage", base_time, 50)
        
        # Test resampling
        resampled = TimeSeriesProcessor.resample_data(test_points, 300)  # 5-minute intervals
        
        # Test aggregation
        mean_value = TimeSeriesProcessor.aggregate_data(test_points, AggregationType.MEAN)
        max_value = TimeSeriesProcessor.aggregate_data(test_points, AggregationType.MAX)
        p95_value = TimeSeriesProcessor.aggregate_data(test_points, AggregationType.PERCENTILE_95)
        
        # Test alignment
        test_points2 = generate_test_data("memory_usage", base_time + 30, 50)  # 30s offset
        aligned1, aligned2 = TimeSeriesProcessor.align_time_series(test_points, test_points2)
        
        success = (
            len(resampled) < len(test_points) and  # Resampling reduces points
            mean_value is not None and
            max_value is not None and
            p95_value is not None and
            len(aligned1) == len(aligned2) and  # Alignment works
            len(aligned1) > 0  # Some points aligned
        )
        
        details = f"""Processing results:
Original points: {len(test_points)}
Resampled points: {len(resampled)} (5-min intervals)
Aggregations:
- Mean: {mean_value:.2f}
- Max: {max_value:.2f}
- P95: {p95_value:.2f}
Alignment:
- Series 1 points: {len(test_points)}
- Series 2 points: {len(test_points2)}
- Aligned points: {len(aligned1)}"""
        
        print_result("Time-Series Processor", success, details)
        return success
        
    except Exception as e:
        print_result("Time-Series Processor", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_correlation_analyzer():
    """Test correlation analysis"""
    print_header("Correlation Analyzer Test", 2)
    
    try:
        from vibe_check.monitoring.visualization.charts import (
            CorrelationAnalyzer, TimeSeriesData, CorrelationType
        )
        
        # Generate correlated test data
        base_time = time.time()
        
        from vibe_check.monitoring.visualization.charts import TimeSeriesPoint

        # Create positively correlated data
        cpu_points = generate_test_data("cpu_usage", base_time, 50)
        memory_points = []

        for i, cpu_point in enumerate(cpu_points):
            # Memory correlates with CPU + some noise
            memory_value = cpu_point.value * 0.8 + 20 + random.gauss(0, 5)
            memory_points.append(TimeSeriesPoint(
                timestamp=cpu_point.timestamp,
                value=max(0, memory_value),
                labels={"instance": "server1", "job": "monitoring"}
            ))
        
        # Create time-series datasets
        cpu_series = TimeSeriesData("cpu_usage", cpu_points)
        memory_series = TimeSeriesData("memory_usage", memory_points)
        
        # Test Pearson correlation
        pearson_result = CorrelationAnalyzer.analyze_correlation(
            cpu_series, memory_series, CorrelationType.PEARSON
        )
        
        # Test cross-correlation
        cross_corr_result = CorrelationAnalyzer.analyze_correlation(
            cpu_series, memory_series, CorrelationType.CROSS_CORRELATION
        )
        
        # Test direct correlation calculation
        cpu_values = [p.value for p in cpu_points]
        memory_values = [p.value for p in memory_points]
        direct_corr = CorrelationAnalyzer.calculate_pearson_correlation(cpu_values, memory_values)
        
        success = (
            0.3 <= pearson_result.coefficient <= 1.0 and  # Should be positively correlated
            cross_corr_result.coefficient is not None and
            0.3 <= direct_corr <= 1.0 and  # Direct calculation should match
            abs(pearson_result.coefficient - direct_corr) < 0.1  # Results should be similar
        )
        
        details = f"""Correlation analysis:
Pearson correlation: {pearson_result.coefficient:.3f}
Cross-correlation: {cross_corr_result.coefficient:.3f} (lag: {cross_corr_result.lag})
Direct calculation: {direct_corr:.3f}
Data points: {len(cpu_points)}

Expected: Positive correlation (CPU and memory should correlate)
Pearson valid: {'✓' if 0.3 <= pearson_result.coefficient <= 1.0 else '✗'}
Results consistent: {'✓' if abs(pearson_result.coefficient - direct_corr) < 0.1 else '✗'}"""
        
        print_result("Correlation Analyzer", success, details)
        return success
        
    except Exception as e:
        print_result("Correlation Analyzer", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_time_series_chart():
    """Test time-series chart component"""
    print_header("Time-Series Chart Test", 2)
    
    try:
        from vibe_check.monitoring.visualization.charts import (
            TimeSeriesChart, ChartConfig, ChartType, TimeSeriesData, CorrelationType
        )
        
        # Create chart configuration
        config = ChartConfig(
            chart_type=ChartType.LINE,
            title="System Metrics Dashboard",
            width=1200,
            height=600,
            show_correlation=True,
            correlation_type=CorrelationType.PEARSON,
            enable_dual_axis=True,
            secondary_metrics=["network_usage"]
        )
        
        # Create chart
        chart = TimeSeriesChart(config)
        
        # Generate test datasets
        base_time = time.time()
        
        cpu_data = TimeSeriesData("cpu_usage", generate_test_data("cpu_usage", base_time, 30))
        memory_data = TimeSeriesData("memory_usage", generate_test_data("memory_usage", base_time, 30))
        network_data = TimeSeriesData("network_usage", generate_test_data("network_usage", base_time, 30))
        
        # Add datasets to chart
        chart.add_dataset(cpu_data)
        chart.add_dataset(memory_data)
        chart.add_dataset(network_data)
        
        # Calculate correlations
        correlations = chart.calculate_correlations()
        
        # Generate chart data
        chart_data = chart.generate_chart_data()
        
        # Test export functionality
        json_export = chart.export_data("json")
        csv_export = chart.export_data("csv")
        
        # Get statistics
        stats = chart.get_statistics()
        
        success = (
            len(chart.datasets) == 3 and
            len(correlations) == 3 and  # 3 datasets = 3 pairwise correlations
            "datasets" in chart_data and
            len(chart_data["datasets"]) == 3 and
            "correlations" in chart_data and
            len(json_export) > 100 and  # Non-empty export
            len(csv_export) > 50 and
            stats["total_datasets"] == 3
        )
        
        details = f"""Chart component:
Configuration: {config.chart_type.value}, {config.width}x{config.height}
Datasets: {len(chart.datasets)}
Correlations calculated: {len(correlations)}
Chart data structure: {'✓' if 'datasets' in chart_data else '✗'}
Export formats: JSON ({len(json_export)} chars), CSV ({len(csv_export)} chars)

Statistics:
- Total datasets: {stats['total_datasets']}
- Total points: {stats['total_points']}
- Correlations: {stats['correlations_count']}

Features tested:
- Multi-metric overlays: ✓
- Correlation analysis: ✓
- Dual-axis support: ✓
- Data export: ✓"""
        
        print_result("Time-Series Chart", success, details)
        return success
        
    except Exception as e:
        print_result("Time-Series Chart", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_chart_data_generation():
    """Test chart data generation for visualization"""
    print_header("Chart Data Generation Test", 2)
    
    try:
        from vibe_check.monitoring.visualization.charts import (
            TimeSeriesChart, ChartConfig, ChartType, TimeSeriesData
        )
        
        # Test different chart types
        chart_types = [ChartType.LINE, ChartType.AREA, ChartType.BAR, ChartType.SCATTER]
        results = {}
        
        for chart_type in chart_types:
            config = ChartConfig(
                chart_type=chart_type,
                title=f"{chart_type.value.title()} Chart Test",
                show_legend=True,
                show_grid=True,
                enable_zoom=True
            )
            
            chart = TimeSeriesChart(config)
            
            # Add test data
            base_time = time.time()
            test_data = TimeSeriesData("test_metric", generate_test_data("test_metric", base_time, 20))
            chart.add_dataset(test_data)
            
            # Generate chart data
            chart_data = chart.generate_chart_data()
            
            results[chart_type.value] = {
                "valid": "datasets" in chart_data and len(chart_data["datasets"]) > 0,
                "type": chart_data.get("type") == chart_type.value,
                "config": "config" in chart_data,
                "axes": "axes" in chart_data
            }
        
        # Check all chart types generated valid data
        all_valid = all(
            result["valid"] and result["type"] and result["config"] and result["axes"]
            for result in results.values()
        )
        
        success = all_valid and len(results) == len(chart_types)
        
        details = f"""Chart data generation:
Chart types tested: {len(chart_types)}

Results:"""
        
        for chart_type, result in results.items():
            status = "✓" if all(result.values()) else "✗"
            details += f"\n- {chart_type}: {status}"
        
        details += f"\n\nFeatures validated:"
        details += f"\n- Multiple chart types: ✓"
        details += f"\n- Data structure consistency: ✓"
        details += f"\n- Configuration options: ✓"
        details += f"\n- Axis configuration: ✓"
        
        print_result("Chart Data Generation", success, details)
        return success
        
    except Exception as e:
        print_result("Chart Data Generation", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_metric_overlays():
    """Test multi-metric overlay functionality"""
    print_header("Multi-Metric Overlays Test", 2)
    
    try:
        from vibe_check.monitoring.visualization.charts import (
            TimeSeriesChart, ChartConfig, TimeSeriesData
        )
        
        # Create chart with dual-axis configuration
        config = ChartConfig(
            title="Multi-Metric Dashboard",
            enable_dual_axis=True,
            secondary_metrics=["network_bytes", "disk_io"],
            show_correlation=True
        )
        
        chart = TimeSeriesChart(config)
        
        # Add multiple metrics with different scales
        base_time = time.time()
        
        # Primary metrics (similar scale)
        cpu_data = TimeSeriesData("cpu_percent", generate_test_data("cpu_usage", base_time, 25))
        memory_data = TimeSeriesData("memory_percent", generate_test_data("memory_usage", base_time, 25))
        
        from vibe_check.monitoring.visualization.charts import TimeSeriesPoint

        # Secondary metrics (different scale)
        network_points = []
        disk_points = []

        for i in range(25):
            timestamp = base_time + i * 60

            # Network in MB/s (larger scale)
            network_value = 1000 + random.gauss(0, 200)
            network_points.append(TimeSeriesPoint(
                timestamp=timestamp,
                value=max(0, network_value),
                labels={"interface": "eth0"}
            ))

            # Disk I/O in MB/s (larger scale)
            disk_value = 500 + random.gauss(0, 100)
            disk_points.append(TimeSeriesPoint(
                timestamp=timestamp,
                value=max(0, disk_value),
                labels={"device": "sda"}
            ))
        
        network_data = TimeSeriesData("network_bytes", network_points)
        disk_data = TimeSeriesData("disk_io", disk_points)
        
        # Add all datasets
        chart.add_dataset(cpu_data)
        chart.add_dataset(memory_data)
        chart.add_dataset(network_data)
        chart.add_dataset(disk_data)
        
        # Generate chart data
        chart_data = chart.generate_chart_data()
        
        # Verify dual-axis configuration
        has_secondary_axis = "y2" in chart_data.get("axes", {})
        
        # Check dataset axis assignments
        primary_datasets = []
        secondary_datasets = []
        
        for dataset in chart_data.get("datasets", []):
            if dataset.get("yAxisID") == "y2":
                secondary_datasets.append(dataset["name"])
            else:
                primary_datasets.append(dataset["name"])
        
        # Calculate correlations
        correlations = chart.calculate_correlations()
        
        success = (
            len(chart.datasets) == 4 and
            has_secondary_axis and
            len(secondary_datasets) == 2 and  # network_bytes and disk_io
            len(primary_datasets) == 2 and   # cpu_percent and memory_percent
            len(correlations) == 6  # 4 datasets = 6 pairwise correlations
        )
        
        details = f"""Multi-metric overlays:
Total datasets: {len(chart.datasets)}
Primary axis metrics: {primary_datasets}
Secondary axis metrics: {secondary_datasets}
Dual-axis enabled: {'✓' if has_secondary_axis else '✗'}
Correlations calculated: {len(correlations)}

Chart configuration:
- Dual-axis support: ✓
- Metric assignment: ✓
- Scale separation: ✓
- Correlation analysis: ✓"""
        
        print_result("Multi-Metric Overlays", success, details)
        return success
        
    except Exception as e:
        print_result("Multi-Metric Overlays", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run time-series charts tests"""
    print_header("Time-Series Charts Test", 1)
    print("Testing advanced time-series chart components with multi-metric overlays and correlation")
    print("Validating data processing, correlation analysis, chart generation, and visualization features")
    
    # Track test results
    test_results = {}
    
    # Run tests
    test_results['processor'] = test_time_series_processor()
    test_results['correlation'] = test_correlation_analyzer()
    test_results['chart'] = test_time_series_chart()
    test_results['data_generation'] = test_chart_data_generation()
    test_results['multi_metric'] = test_multi_metric_overlays()
    
    # Summary
    print_header("Time-Series Charts Test Summary", 1)
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    success_rate = (passed_tests / total_tests) * 100
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name.upper()} test")
    
    print(f"\n📊 Overall Results:")
    print(f"  • Tests passed: {passed_tests}/{total_tests}")
    print(f"  • Success rate: {success_rate:.1f}%")
    
    if success_rate >= 75:
        print(f"  ✅ Time-series charts system SUCCESSFUL")
        print(f"  🚀 Ready for code quality visualization development")
    else:
        print(f"  ❌ Time-series charts system FAILED")
        print(f"  🔧 Remediation required before proceeding")
    
    return success_rate >= 75

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
