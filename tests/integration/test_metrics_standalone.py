#!/usr/bin/env python3
"""
Standalone Metrics Collection Test
=================================

Standalone test for metrics collection framework that directly tests
the monitoring modules without complex import dependencies.
"""

import asyncio
import time
import sys
import tempfile
import shutil
import platform
from pathlib import Path
from typing import Dict, Any, List
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))


async def test_base_collector_framework():
    """Test base collector framework"""
    print("🧪 Testing Base Collector Framework")
    print("=" * 36)
    
    try:
        # Direct import of monitoring modules
        from vibe_check.monitoring.collectors.base_collector import (
            MetricsCollector, MetricDefinition, MetricValue, MetricType,
            CollectorConfig, MetricsRegistry, CollectionInterval
        )
        
        # Test metric types
        print(f"  ✅ Metric types:")
        print(f"    • Available types: {[t.value for t in MetricType]}")
        print(f"    • Collection intervals: {[i.value for i in CollectionInterval]}")
        
        # Test metric definition
        metric_def = MetricDefinition(
            name="test_metric",
            metric_type=MetricType.GAUGE,
            description="Test metric for validation",
            labels={"test": "true", "component": "framework"}
        )
        
        print(f"  ✅ Metric definition:")
        print(f"    • Name: {metric_def.name}")
        print(f"    • Type: {metric_def.metric_type.value}")
        print(f"    • Labels: {metric_def.labels}")
        
        # Test metric value
        metric_value = MetricValue(
            name="test_metric",
            value=42.5,
            labels={"instance": "test", "status": "active"}
        )
        
        print(f"  ✅ Metric value:")
        print(f"    • Name: {metric_value.name}")
        print(f"    • Value: {metric_value.value}")
        print(f"    • Labels: {metric_value.labels}")
        print(f"    • Timestamp: {metric_value.timestamp:.3f}")
        
        # Test collector config
        config = CollectorConfig(
            enabled=True,
            collection_interval=CollectionInterval.FAST.value,
            max_collection_time=5.0,
            labels={"collector": "test"}
        )
        
        print(f"  ✅ Collector config:")
        print(f"    • Enabled: {config.enabled}")
        print(f"    • Interval: {config.collection_interval}s")
        print(f"    • Max time: {config.max_collection_time}s")
        
        # Test metrics registry
        registry = MetricsRegistry()
        
        print(f"  ✅ Metrics registry:")
        print(f"    • Initial collectors: {len(registry.collectors)}")
        print(f"    • Registry created: {isinstance(registry, MetricsRegistry)}")
        
        return {
            'metric_types': len(MetricType) >= 4,
            'metric_definition': metric_def.name == "test_metric",
            'metric_value': metric_value.value == 42.5,
            'collector_config': config.enabled,
            'registry_created': isinstance(registry, MetricsRegistry)
        }
        
    except Exception as e:
        print(f"❌ Base collector framework test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def test_system_metrics_collector():
    """Test system metrics collector"""
    print("\n🧪 Testing System Metrics Collector")
    print("=" * 37)
    
    try:
        from vibe_check.monitoring.collectors.system_collector import SystemMetricsCollector
        from vibe_check.monitoring.collectors.base_collector import CollectorConfig, CollectionInterval
        
        # Create collector with fast collection
        config = CollectorConfig(
            collection_interval=CollectionInterval.REALTIME.value,
            max_collection_time=3.0,
            labels={"host": platform.node(), "test": "true"}
        )
        
        collector = SystemMetricsCollector(config)
        
        print(f"  ✅ Collector initialization:")
        print(f"    • Name: {collector.name}")
        print(f"    • Platform: {collector.platform}")
        print(f"    • Enabled: {collector.enabled}")
        print(f"    • Is Linux: {collector.is_linux}")
        print(f"    • Is Darwin: {collector.is_darwin}")
        
        # Test metric registration
        metric_definitions = collector.get_metric_definitions()
        
        cpu_metrics = [name for name in metric_definitions if 'cpu' in name]
        memory_metrics = [name for name in metric_definitions if 'memory' in name]
        disk_metrics = [name for name in metric_definitions if 'disk' in name]
        
        print(f"  ✅ Metric definitions:")
        print(f"    • Total metrics: {len(metric_definitions)}")
        print(f"    • CPU metrics: {len(cpu_metrics)}")
        print(f"    • Memory metrics: {len(memory_metrics)}")
        print(f"    • Disk metrics: {len(disk_metrics)}")
        
        # Test metrics collection
        print("  🔄 Collecting system metrics...")
        start_time = time.time()
        
        metrics = await collector.collect_metrics()
        
        collection_time = time.time() - start_time
        
        print(f"    • Metrics collected: {len(metrics)}")
        print(f"    • Collection time: {collection_time:.3f}s")
        
        # Analyze collected metrics
        if metrics:
            metric_names = [m.name for m in metrics]
            unique_names = set(metric_names)
            
            cpu_collected = [m for m in metrics if 'cpu' in m.name]
            memory_collected = [m for m in metrics if 'memory' in m.name]
            disk_collected = [m for m in metrics if 'disk' in m.name]
            
            print(f"  ✅ Collected metrics analysis:")
            print(f"    • Unique metric names: {len(unique_names)}")
            print(f"    • CPU metrics: {len(cpu_collected)}")
            print(f"    • Memory metrics: {len(memory_collected)}")
            print(f"    • Disk metrics: {len(disk_collected)}")
            
            # Show sample values
            if memory_collected:
                memory_metric = memory_collected[0]
                print(f"    • Sample memory metric: {memory_metric.name}={memory_metric.value}")
            
            if cpu_collected:
                cpu_metric = cpu_collected[0]
                print(f"    • Sample CPU metric: {cpu_metric.name}={cpu_metric.value}")
        
        # Test collection statistics
        stats = collector.get_collection_stats()
        print(f"  📊 Collection statistics:")
        print(f"    • Metrics registered: {stats['metrics_registered']}")
        print(f"    • Collection count: {stats['collection_count']}")
        print(f"    • Error count: {stats['error_count']}")
        
        collector.cleanup()
        
        return {
            'collector_created': collector.name == "SystemMetricsCollector",
            'platform_detected': collector.platform in ['linux', 'darwin', 'windows'],
            'metrics_registered': len(metric_definitions) >= 10,
            'metrics_collected': len(metrics) >= 3,
            'collection_time': collection_time < 5.0,
            'cpu_metrics': len(cpu_collected) > 0 if metrics else False,
            'memory_metrics': len(memory_collected) > 0 if metrics else False
        }
        
    except Exception as e:
        print(f"❌ System metrics collector test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def test_code_quality_collector():
    """Test code quality collector"""
    print("\n🧪 Testing Code Quality Collector")
    print("=" * 34)
    
    try:
        from vibe_check.monitoring.collectors.code_quality_collector import CodeQualityMetricsCollector
        from vibe_check.monitoring.collectors.base_collector import CollectorConfig
        
        # Create test project
        test_dir = Path("test_quality_project")
        test_dir.mkdir(exist_ok=True)
        
        # Create test files
        test_files = {
            "simple.py": '''
def hello_world():
    """Simple hello world function"""
    print("Hello, World!")
    return "success"

class SimpleClass:
    def __init__(self, name):
        self.name = name
    
    def greet(self):
        return f"Hello, {self.name}!"
''',
            "complex.py": '''
import os
import sys

def complex_function(a, b, c, d=None):
    """A more complex function with multiple paths"""
    result = 0
    
    if a > 0:
        if b > 0:
            if c > 0:
                for i in range(a):
                    for j in range(b):
                        try:
                            if d is not None:
                                result += i * j * c * d
                            else:
                                result += i * j * c
                        except Exception as e:
                            print(f"Error: {e}")
                            continue
            else:
                result = -1
        else:
            result = -2
    else:
        result = -3
    
    return result

class ComplexClass:
    def __init__(self, x, y, z):
        self.x = x
        self.y = y
        self.z = z
        self.data = []
    
    def process_data(self):
        while len(self.data) < 100:
            if self.x > self.y:
                self.data.append(self.x * self.z)
                self.x -= 1
            elif self.y > self.z:
                self.data.append(self.y * self.x)
                self.y -= 1
            else:
                self.data.append(self.z * self.y)
                self.z -= 1
        
        return sum(self.data)
'''
        }
        
        for filename, content in test_files.items():
            (test_dir / filename).write_text(content)
        
        print(f"  📁 Created test project:")
        print(f"    • Directory: {test_dir}")
        print(f"    • Files: {len(test_files)}")
        
        # Create collector
        config = CollectorConfig(
            collection_interval=30.0,
            max_collection_time=10.0,
            labels={"project_type": "test"}
        )
        
        collector = CodeQualityMetricsCollector([test_dir], config)
        
        print(f"  ✅ Collector initialization:")
        print(f"    • Name: {collector.name}")
        print(f"    • Projects monitored: {len(collector.project_paths)}")
        
        # Test metric registration
        metric_definitions = collector.get_metric_definitions()
        
        quality_metrics = [name for name in metric_definitions if 'quality' in name]
        analysis_metrics = [name for name in metric_definitions if 'analysis' in name]
        
        print(f"  ✅ Metric definitions:")
        print(f"    • Total metrics: {len(metric_definitions)}")
        print(f"    • Quality metrics: {len(quality_metrics)}")
        print(f"    • Analysis metrics: {len(analysis_metrics)}")
        
        # Test metrics collection
        print("  🔄 Collecting code quality metrics...")
        start_time = time.time()
        
        metrics = await collector.collect_metrics()
        
        collection_time = time.time() - start_time
        
        print(f"    • Metrics collected: {len(metrics)}")
        print(f"    • Collection time: {collection_time:.3f}s")
        
        # Analyze collected metrics
        if metrics:
            files_metrics = [m for m in metrics if m.name == "code_quality_files_total"]
            lines_metrics = [m for m in metrics if m.name == "code_quality_lines_total"]
            score_metrics = [m for m in metrics if m.name == "code_quality_score_average"]
            complexity_metrics = [m for m in metrics if m.name == "code_quality_complexity_average"]
            
            print(f"  ✅ Collected metrics analysis:")
            print(f"    • Files metrics: {len(files_metrics)}")
            print(f"    • Lines metrics: {len(lines_metrics)}")
            print(f"    • Score metrics: {len(score_metrics)}")
            print(f"    • Complexity metrics: {len(complexity_metrics)}")
            
            if files_metrics:
                print(f"    • Files analyzed: {files_metrics[0].value}")
            if lines_metrics:
                print(f"    • Total lines: {lines_metrics[0].value}")
            if score_metrics:
                print(f"    • Quality score: {score_metrics[0].value:.2f}")
            if complexity_metrics:
                print(f"    • Avg complexity: {complexity_metrics[0].value:.2f}")
        
        # Test project management
        new_project = Path("test_new_project")
        collector.add_project_path(new_project)
        
        print(f"  ✅ Project management:")
        print(f"    • Projects after add: {len(collector.get_monitored_projects())}")
        
        collector.remove_project_path(new_project)
        print(f"    • Projects after remove: {len(collector.get_monitored_projects())}")
        
        # Cleanup
        shutil.rmtree(test_dir, ignore_errors=True)
        collector.cleanup()
        
        return {
            'collector_created': collector.name == "CodeQualityMetricsCollector",
            'metrics_registered': len(metric_definitions) >= 8,
            'metrics_collected': len(metrics) >= 5,
            'collection_time': collection_time < 15.0,
            'files_analyzed': len(files_metrics) > 0 and files_metrics[0].value >= 2 if metrics and files_metrics else False,
            'quality_calculated': len(score_metrics) > 0 if metrics else False
        }
        
    except Exception as e:
        print(f"❌ Code quality collector test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def main():
    """Main test function"""
    print("🚀 Standalone Metrics Collection Test - Task 4.2")
    print("=" * 55)
    
    # Run tests
    base_results = await test_base_collector_framework()
    system_results = await test_system_metrics_collector()
    code_quality_results = await test_code_quality_collector()
    
    print("\n" + "=" * 55)
    print("📊 STANDALONE METRICS COLLECTION SUMMARY")
    print("=" * 55)
    
    # Evaluate results
    targets_met = 0
    total_targets = 4
    
    # Target 1: Base framework
    if (base_results.get('metric_types') and 
        base_results.get('metric_definition') and 
        base_results.get('registry_created')):
        print("  ✅ Base collector framework working")
        targets_met += 1
    else:
        print("  ❌ Base collector framework issues")
    
    # Target 2: System metrics
    if (system_results.get('collector_created') and 
        system_results.get('metrics_collected') and 
        system_results.get('platform_detected')):
        print("  ✅ System metrics collection working")
        targets_met += 1
    else:
        print("  ❌ System metrics collection issues")
    
    # Target 3: Code quality metrics
    if (code_quality_results.get('collector_created') and 
        code_quality_results.get('metrics_collected') and 
        code_quality_results.get('files_analyzed')):
        print("  ✅ Code quality metrics collection working")
        targets_met += 1
    else:
        print("  ❌ Code quality metrics collection issues")
    
    # Target 4: Overall performance
    if (system_results.get('collection_time', 10) < 5.0 and 
        code_quality_results.get('collection_time', 20) < 15.0):
        print("  ✅ Overall collection performance good")
        targets_met += 1
    else:
        print("  ❌ Overall collection performance issues")
    
    print(f"\n🎯 Overall Score: {targets_met}/{total_targets} targets met")
    
    if targets_met >= 3:
        print("✅ Task 4.2: Basic Metrics Collection Framework SUCCESSFUL")
        print("🚀 Ready to proceed with Task 4.3: Real-Time Data Pipelines")
        
        print(f"\n🏆 Key Achievements:")
        if system_results:
            print(f"  • System metrics: {system_results.get('metrics_collected', False)} collected")
            print(f"  • Platform support: {system_results.get('platform_detected', False)}")
            print(f"  • Collection time: {system_results.get('collection_time', 0):.3f}s")
        if code_quality_results:
            print(f"  • Code quality: {code_quality_results.get('files_analyzed', False)} files analyzed")
            print(f"  • Quality metrics: {code_quality_results.get('quality_calculated', False)}")
        print(f"  • Extensible collector framework")
        print(f"  • Multi-platform system monitoring")
        print(f"  • Code quality analysis integration")
        print(f"  • Configurable collection intervals")
        print(f"  • Error handling and statistics")
        
        return 0
    else:
        print("⚠️  Task 4.2: Basic Metrics Collection Framework needs improvement")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
