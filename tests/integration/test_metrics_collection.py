#!/usr/bin/env python3
"""
Metrics Collection Framework Test Suite
======================================

Comprehensive test suite for Task 4.2: Basic Metrics Collection Framework.
Tests system metrics, code quality metrics, and TSDB integration.
"""

import asyncio
import time
import sys
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Any, List
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))


async def test_base_collector_framework():
    """Test the base collector framework"""
    print("🧪 Testing Base Collector Framework")
    print("=" * 36)
    
    try:
        from vibe_check.monitoring.collectors.base_collector import (
            MetricsCollector, MetricDefinition, MetricValue, MetricType,
            CollectorConfig, MetricsRegistry
        )
        
        # Test metric definitions
        metric_def = MetricDefinition(
            name="test_metric",
            metric_type=MetricType.GAUGE,
            description="Test metric",
            labels={"test": "true"}
        )
        
        print(f"  ✅ Metric definition:")
        print(f"    • Name: {metric_def.name}")
        print(f"    • Type: {metric_def.metric_type.value}")
        print(f"    • Description: {metric_def.description}")
        
        # Test metric values
        metric_value = MetricValue(
            name="test_metric",
            value=42.0,
            labels={"instance": "test"}
        )
        
        print(f"  ✅ Metric value:")
        print(f"    • Name: {metric_value.name}")
        print(f"    • Value: {metric_value.value}")
        print(f"    • Labels: {metric_value.labels}")
        print(f"    • Timestamp: {metric_value.timestamp}")
        
        # Test registry
        registry = MetricsRegistry()
        
        print(f"  ✅ Metrics registry:")
        print(f"    • Initial collectors: {len(registry.collectors)}")
        
        return {
            'metric_definition': metric_def.name == "test_metric",
            'metric_value': metric_value.value == 42.0,
            'registry_created': isinstance(registry, MetricsRegistry),
            'metric_types': len(MetricType) >= 4
        }
        
    except Exception as e:
        print(f"❌ Base collector framework test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def test_system_metrics_collector():
    """Test system metrics collection"""
    print("\n🧪 Testing System Metrics Collector")
    print("=" * 37)
    
    try:
        from vibe_check.monitoring.collectors.system_collector import SystemMetricsCollector
        from vibe_check.monitoring.collectors.base_collector import CollectorConfig, CollectionInterval
        
        # Create collector with fast collection for testing
        config = CollectorConfig(
            collection_interval=CollectionInterval.REALTIME.value,
            max_collection_time=5.0
        )
        
        collector = SystemMetricsCollector(config)
        
        print(f"  ✅ Collector initialization:")
        print(f"    • Name: {collector.name}")
        print(f"    • Platform: {collector.platform}")
        print(f"    • Enabled: {collector.enabled}")
        
        # Test metric registration
        metric_definitions = collector.get_metric_definitions()
        
        print(f"  ✅ Metric definitions:")
        print(f"    • Total metrics: {len(metric_definitions)}")
        print(f"    • CPU metrics: {len([m for m in metric_definitions if 'cpu' in m])}")
        print(f"    • Memory metrics: {len([m for m in metric_definitions if 'memory' in m])}")
        print(f"    • Disk metrics: {len([m for m in metric_definitions if 'disk' in m])}")
        
        # Test metrics collection
        print("  🔄 Collecting system metrics...")
        start_time = time.time()
        
        metrics = await collector.collect_metrics()
        
        collection_time = time.time() - start_time
        
        print(f"    • Metrics collected: {len(metrics)}")
        print(f"    • Collection time: {collection_time:.3f}s")
        
        # Analyze collected metrics
        metric_names = [m.name for m in metrics]
        cpu_metrics = [m for m in metrics if 'cpu' in m.name]
        memory_metrics = [m for m in metrics if 'memory' in m.name]
        
        print(f"  ✅ Collected metrics analysis:")
        print(f"    • CPU metrics: {len(cpu_metrics)}")
        print(f"    • Memory metrics: {len(memory_metrics)}")
        print(f"    • Unique metric names: {len(set(metric_names))}")
        
        # Test collection statistics
        stats = collector.get_collection_stats()
        print(f"  📊 Collection statistics:")
        print(f"    • Collection count: {stats['collection_count']}")
        print(f"    • Error count: {stats['error_count']}")
        print(f"    • Metrics registered: {stats['metrics_registered']}")
        
        collector.cleanup()
        
        return {
            'collector_created': collector.name == "SystemMetricsCollector",
            'metrics_registered': len(metric_definitions) >= 10,
            'metrics_collected': len(metrics) >= 5,
            'collection_time': collection_time < 5.0,
            'cpu_metrics': len(cpu_metrics) > 0,
            'memory_metrics': len(memory_metrics) > 0
        }
        
    except Exception as e:
        print(f"❌ System metrics collector test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def test_code_quality_collector():
    """Test code quality metrics collection"""
    print("\n🧪 Testing Code Quality Collector")
    print("=" * 34)
    
    try:
        from vibe_check.monitoring.collectors.code_quality_collector import CodeQualityMetricsCollector
        from vibe_check.monitoring.collectors.base_collector import CollectorConfig
        
        # Create test project
        test_dir = Path("test_code_quality_project")
        test_dir.mkdir(exist_ok=True)
        
        # Create test Python files
        test_files = [
            ("simple.py", '''
def simple_function():
    """A simple function"""
    return 42

class SimpleClass:
    def __init__(self):
        self.value = 10
    
    def get_value(self):
        return self.value
'''),
            ("complex.py", '''
def complex_function(x, y, z):
    """A more complex function"""
    if x > 0:
        if y > 0:
            if z > 0:
                for i in range(x):
                    for j in range(y):
                        try:
                            result = i * j * z
                            if result > 100:
                                return result
                        except Exception:
                            pass
    return 0

class ComplexClass:
    def __init__(self, a, b, c):
        self.a = a
        self.b = b
        self.c = c
    
    def complex_method(self):
        while self.a > 0:
            if self.b > self.c:
                self.a -= 1
            else:
                self.b += 1
        return self.a + self.b + self.c
''')
        ]
        
        for filename, content in test_files:
            (test_dir / filename).write_text(content)
        
        print(f"  📁 Created test project:")
        print(f"    • Directory: {test_dir}")
        print(f"    • Files: {len(test_files)}")
        
        # Create collector
        config = CollectorConfig(
            collection_interval=30.0,  # Slower for code quality
            max_collection_time=15.0
        )
        
        collector = CodeQualityMetricsCollector([test_dir], config)
        
        print(f"  ✅ Collector initialization:")
        print(f"    • Name: {collector.name}")
        print(f"    • Projects: {len(collector.project_paths)}")
        
        # Test metric registration
        metric_definitions = collector.get_metric_definitions()
        
        print(f"  ✅ Metric definitions:")
        print(f"    • Total metrics: {len(metric_definitions)}")
        print(f"    • Quality metrics: {len([m for m in metric_definitions if 'quality' in m])}")
        
        # Test metrics collection
        print("  🔄 Collecting code quality metrics...")
        start_time = time.time()
        
        metrics = await collector.collect_metrics()
        
        collection_time = time.time() - start_time
        
        print(f"    • Metrics collected: {len(metrics)}")
        print(f"    • Collection time: {collection_time:.3f}s")
        
        # Analyze collected metrics
        files_metrics = [m for m in metrics if m.name == "code_quality_files_total"]
        lines_metrics = [m for m in metrics if m.name == "code_quality_lines_total"]
        quality_metrics = [m for m in metrics if m.name == "code_quality_score_average"]
        
        print(f"  ✅ Collected metrics analysis:")
        print(f"    • Files metrics: {len(files_metrics)}")
        print(f"    • Lines metrics: {len(lines_metrics)}")
        print(f"    • Quality metrics: {len(quality_metrics)}")
        
        if files_metrics:
            print(f"    • Files analyzed: {files_metrics[0].value}")
        if lines_metrics:
            print(f"    • Total lines: {lines_metrics[0].value}")
        if quality_metrics:
            print(f"    • Quality score: {quality_metrics[0].value:.2f}")
        
        # Cleanup
        shutil.rmtree(test_dir, ignore_errors=True)
        collector.cleanup()
        
        return {
            'collector_created': collector.name == "CodeQualityMetricsCollector",
            'metrics_registered': len(metric_definitions) >= 8,
            'metrics_collected': len(metrics) >= 5,
            'collection_time': collection_time < 15.0,
            'files_analyzed': len(files_metrics) > 0 and files_metrics[0].value >= 2,
            'quality_calculated': len(quality_metrics) > 0
        }
        
    except Exception as e:
        print(f"❌ Code quality collector test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def test_metrics_manager_integration():
    """Test metrics manager and TSDB integration"""
    print("\n🧪 Testing Metrics Manager Integration")
    print("=" * 39)
    
    try:
        from vibe_check.monitoring.collectors.metrics_manager import MetricsManager, MetricsManagerConfig
        from vibe_check.monitoring.collectors.system_collector import SystemMetricsCollector
        
        # Create temporary directory for TSDB
        with tempfile.TemporaryDirectory() as temp_dir:
            config = MetricsManagerConfig(
                tsdb_data_dir=Path(temp_dir),
                enable_system_metrics=True,
                enable_code_quality_metrics=False,  # Disable for faster testing
                max_ingestion_batch_size=100,
                ingestion_flush_interval=1.0
            )
            
            manager = MetricsManager(config)
            
            print(f"  ✅ Manager initialization:")
            print(f"    • Config created: {config.enable_system_metrics}")
            print(f"    • TSDB directory: {config.tsdb_data_dir}")
            
            # Start manager
            print("  🔄 Starting metrics manager...")
            start_time = time.time()
            
            await manager.start()
            
            startup_time = time.time() - start_time
            
            print(f"    • Startup time: {startup_time:.3f}s")
            print(f"    • TSDB initialized: {manager.tsdb is not None}")
            print(f"    • Collectors registered: {len(manager.registry.collectors)}")
            
            # Wait for some metrics collection
            print("  ⏱️  Waiting for metrics collection...")
            await asyncio.sleep(3.0)
            
            # Check manager statistics
            stats = manager.get_manager_stats()
            
            print(f"  📊 Manager statistics:")
            print(f"    • Uptime: {stats['uptime_seconds']:.1f}s")
            print(f"    • Metrics ingested: {stats['metrics_ingested']}")
            print(f"    • Ingestion rate: {stats['ingestion_rate']:.1f} metrics/sec")
            print(f"    • Ingestion errors: {stats['ingestion_errors']}")
            
            # Test TSDB integration
            if manager.tsdb:
                tsdb_stats = manager.tsdb.get_stats()
                print(f"  🗄️  TSDB statistics:")
                print(f"    • Series count: {tsdb_stats['series_count']}")
                print(f"    • Total samples: {tsdb_stats['total_samples']}")
                print(f"    • Ingestion rate: {tsdb_stats['ingestion_rate']:.1f} samples/sec")
            
            # Stop manager
            print("  🛑 Stopping metrics manager...")
            await manager.stop()
            
            return {
                'manager_created': isinstance(manager, MetricsManager),
                'startup_successful': startup_time < 10.0,
                'tsdb_initialized': manager.tsdb is not None,
                'collectors_registered': len(manager.registry.collectors) > 0,
                'metrics_ingested': stats['metrics_ingested'] > 0,
                'no_ingestion_errors': stats['ingestion_errors'] == 0
            }
    
    except Exception as e:
        print(f"❌ Metrics manager integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def main():
    """Main test function"""
    print("🚀 Metrics Collection Framework Test Suite - Task 4.2")
    print("=" * 65)
    
    # Run all tests
    base_results = await test_base_collector_framework()
    system_results = await test_system_metrics_collector()
    code_quality_results = await test_code_quality_collector()
    manager_results = await test_metrics_manager_integration()
    
    print("\n" + "=" * 65)
    print("📊 METRICS COLLECTION FRAMEWORK SUMMARY")
    print("=" * 65)
    
    # Evaluate results
    targets_met = 0
    total_targets = 5
    
    # Target 1: Base collector framework
    if (base_results.get('metric_definition') and 
        base_results.get('metric_value') and 
        base_results.get('registry_created')):
        print("  ✅ Base collector framework working")
        targets_met += 1
    else:
        print("  ❌ Base collector framework issues")
    
    # Target 2: System metrics collection
    if (system_results.get('collector_created') and 
        system_results.get('metrics_collected') and 
        system_results.get('cpu_metrics') and 
        system_results.get('memory_metrics')):
        print("  ✅ System metrics collection working")
        targets_met += 1
    else:
        print("  ❌ System metrics collection issues")
    
    # Target 3: Code quality metrics collection
    if (code_quality_results.get('collector_created') and 
        code_quality_results.get('metrics_collected') and 
        code_quality_results.get('files_analyzed')):
        print("  ✅ Code quality metrics collection working")
        targets_met += 1
    else:
        print("  ❌ Code quality metrics collection issues")
    
    # Target 4: TSDB integration
    if (manager_results.get('manager_created') and 
        manager_results.get('tsdb_initialized') and 
        manager_results.get('metrics_ingested')):
        print("  ✅ TSDB integration working")
        targets_met += 1
    else:
        print("  ❌ TSDB integration issues")
    
    # Target 5: Overall system performance
    if (system_results.get('collection_time', 10) < 5.0 and 
        manager_results.get('startup_successful', False)):
        print("  ✅ Overall system performance good")
        targets_met += 1
    else:
        print("  ❌ Overall system performance issues")
    
    print(f"\n🎯 Overall Score: {targets_met}/{total_targets} targets met")
    
    if targets_met >= 4:
        print("✅ Task 4.2: Basic Metrics Collection Framework SUCCESSFUL")
        print("🚀 Ready to proceed with Task 4.3: Real-Time Data Pipelines")
        
        # Show key achievements
        print(f"\n🏆 Key Achievements:")
        if system_results:
            print(f"  • System metrics: {system_results.get('metrics_collected', 0)} metrics collected")
            print(f"  • Collection time: {system_results.get('collection_time', 0):.3f}s")
        if code_quality_results:
            print(f"  • Code quality: {code_quality_results.get('files_analyzed', False)} files analyzed")
        if manager_results:
            print(f"  • TSDB integration: {manager_results.get('metrics_ingested', 0)} metrics ingested")
            print(f"  • Startup time: {manager_results.get('startup_successful', False)}")
        print(f"  • Extensible collector framework")
        print(f"  • Multi-platform system metrics")
        print(f"  • Code quality monitoring")
        print(f"  • Real-time metrics ingestion")
        print(f"  • Self-monitoring capabilities")
        
        return 0
    else:
        print("⚠️  Task 4.2: Basic Metrics Collection Framework needs improvement")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
