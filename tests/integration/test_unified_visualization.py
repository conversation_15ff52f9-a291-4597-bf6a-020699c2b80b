#!/usr/bin/env python3
"""
Test Unified Visualization System
=================================

Test the new unified visualization system that replaces both ai/visualization
and ui/visualization modules.
"""

import time
import json
from pathlib import Path

# Import the unified visualization system
from vibe_check.core.visualization.unified_charts import (
    UnifiedChartEngine, ChartType, ChartConfig, ChartData,
    TimeSeriesChart, BarChart, LineChart, PieChart
)
from vibe_check.core.visualization.dashboard_engine import (
    UnifiedDashboardEngine, Dashboard, DashboardPanel, PanelType
)


def test_chart_engine():
    """Test the unified chart engine"""
    print("🧪 Testing Unified Chart Engine")
    print("=" * 35)
    
    engine = UnifiedChartEngine()
    
    # Test creating different chart types
    chart_types = [
        (ChartType.LINE, "Line Chart"),
        (ChartType.BAR, "Bar Chart"),
        (ChartType.PIE, "Pie Chart"),
        (ChartType.TIME_SERIES, "Time Series Chart"),
    ]
    
    for chart_type, name in chart_types:
        print(f"📊 Testing {name}...")
        
        # Create chart
        chart = engine.create_chart(
            chart_type,
            f"test_{chart_type.value}",
            ChartConfig(title=name, width=600, height=400)
        )
        
        # Add sample data
        if chart_type == ChartType.TIME_SERIES:
            # Time series data
            timestamps = [time.time() - i * 60 for i in range(10, 0, -1)]
            values = [50 + i * 5 for i in range(10)]
            chart.set_data(ChartData(timestamps, values))
        elif chart_type == ChartType.PIE:
            # Pie chart data
            chart.set_data(ChartData(
                x_values=['Category A', 'Category B', 'Category C'],
                y_values=[30, 45, 25],
                colors=['#ff6384', '#36a2eb', '#ffce56']
            ))
        else:
            # Regular chart data
            chart.set_data(ChartData(
                x_values=['Jan', 'Feb', 'Mar', 'Apr', 'May'],
                y_values=[10, 20, 15, 25, 30]
            ))
        
        # Test HTML generation
        html = chart.to_html()
        print(f"  ✅ HTML generated: {len(html)} characters")
        
        # Test JSON export
        json_data = chart.to_json()
        print(f"  ✅ JSON exported: {len(json_data)} characters")
        
        # Verify chart is stored in engine
        retrieved_chart = engine.get_chart(f"test_{chart_type.value}")
        if retrieved_chart:
            print(f"  ✅ Chart stored and retrieved successfully")
        else:
            print(f"  ❌ Chart storage failed")
    
    print(f"\n📈 Chart Engine Summary:")
    print(f"  • Charts created: {len(engine.charts)}")
    print(f"  • Themes available: {len(engine.themes)}")
    
    return engine


def test_dashboard_engine():
    """Test the unified dashboard engine"""
    print("\n🧪 Testing Unified Dashboard Engine")
    print("=" * 38)
    
    engine = UnifiedDashboardEngine()
    
    # Create a test dashboard
    dashboard = engine.create_dashboard(
        "test_dashboard",
        "Test Dashboard",
        "A test dashboard for unified visualization system"
    )
    
    print(f"📊 Created dashboard: {dashboard.title}")
    
    # Add various panel types
    panels_added = 0
    
    # Add metric panels
    metrics = [
        ("cpu_usage", "CPU Usage", {'x': 0, 'y': 0, 'width': 3, 'height': 2}),
        ("memory_usage", "Memory Usage", {'x': 3, 'y': 0, 'width': 3, 'height': 2}),
        ("disk_usage", "Disk Usage", {'x': 6, 'y': 0, 'width': 3, 'height': 2}),
        ("process_count", "Process Count", {'x': 9, 'y': 0, 'width': 3, 'height': 2}),
    ]
    
    for panel_id, title, position in metrics:
        success = engine.add_metric_panel(
            "test_dashboard", panel_id, title, position, panel_id
        )
        if success:
            panels_added += 1
            print(f"  ✅ Added metric panel: {title}")
        else:
            print(f"  ❌ Failed to add metric panel: {title}")
    
    # Add chart panels
    charts = [
        ("cpu_chart", "CPU Over Time", ChartType.TIME_SERIES, {'x': 0, 'y': 2, 'width': 6, 'height': 4}),
        ("memory_chart", "Memory Over Time", ChartType.LINE, {'x': 6, 'y': 2, 'width': 6, 'height': 4}),
        ("usage_breakdown", "Usage Breakdown", ChartType.PIE, {'x': 0, 'y': 6, 'width': 6, 'height': 4}),
        ("performance_bars", "Performance Metrics", ChartType.BAR, {'x': 6, 'y': 6, 'width': 6, 'height': 4}),
    ]
    
    for panel_id, title, chart_type, position in charts:
        success = engine.add_chart_panel(
            "test_dashboard", panel_id, title, chart_type, position
        )
        if success:
            panels_added += 1
            print(f"  ✅ Added chart panel: {title} ({chart_type.value})")
        else:
            print(f"  ❌ Failed to add chart panel: {title}")
    
    print(f"\n📊 Dashboard Summary:")
    print(f"  • Panels added: {panels_added}")
    print(f"  • Total panels: {len(dashboard.panels)}")
    
    # Test HTML rendering
    print(f"\n🎨 Testing HTML rendering...")
    
    # Sample data for dashboard
    sample_data = {
        'cpu_usage': {'value': 45.2},
        'memory_usage': {'value': 67.8},
        'disk_usage': {'value': 23.1},
        'process_count': {'value': 156},
        'cpu_chart': {
            'x': [time.time() - i * 60 for i in range(10, 0, -1)],
            'y': [40 + i * 2 for i in range(10)]
        },
        'memory_chart': {
            'x': ['1h', '2h', '3h', '4h', '5h'],
            'y': [60, 65, 70, 68, 72]
        },
        'usage_breakdown': {
            'x': ['CPU', 'Memory', 'Disk', 'Network'],
            'y': [25, 35, 20, 20]
        },
        'performance_bars': {
            'x': ['Throughput', 'Latency', 'Errors', 'Uptime'],
            'y': [85, 92, 3, 99.9]
        }
    }
    
    html = engine.render_dashboard_html("test_dashboard", sample_data)
    print(f"  ✅ HTML rendered: {len(html)} characters")
    
    # Save HTML to file for inspection
    html_file = Path("test_dashboard.html")
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(html)
    print(f"  ✅ HTML saved to: {html_file}")
    
    # Test JSON export
    json_export = engine.export_dashboard("test_dashboard", "json")
    print(f"  ✅ JSON exported: {len(json_export)} characters")
    
    # Save JSON to file
    json_file = Path("test_dashboard.json")
    with open(json_file, 'w', encoding='utf-8') as f:
        f.write(json_export)
    print(f"  ✅ JSON saved to: {json_file}")
    
    return engine


def test_monitoring_dashboard():
    """Test the monitoring dashboard creation"""
    print("\n🧪 Testing Monitoring Dashboard")
    print("=" * 32)
    
    engine = UnifiedDashboardEngine()
    
    # Create monitoring dashboard
    dashboard = engine.create_monitoring_dashboard("monitoring")
    
    print(f"📊 Created monitoring dashboard: {dashboard.title}")
    print(f"  • Panels: {len(dashboard.panels)}")
    print(f"  • Auto-refresh: {dashboard.auto_refresh}")
    print(f"  • Refresh interval: {dashboard.refresh_interval}s")
    
    # List panels
    print(f"\n📋 Dashboard Panels:")
    for panel in dashboard.panels:
        print(f"  • {panel.title} ({panel.panel_type.value}) at {panel.position}")
    
    # Generate sample monitoring data
    monitoring_data = {
        'cpu_usage': {'value': 42.5},
        'memory_usage': {'value': 68.3},
        'disk_usage': {'value': 15.7},
        'process_count': {'value': 234},
        'cpu_chart': {
            'x': [f"{i}m ago" for i in range(30, 0, -5)],
            'y': [35, 40, 45, 42, 38, 41]
        },
        'memory_chart': {
            'x': [f"{i}m ago" for i in range(30, 0, -5)],
            'y': [65, 67, 70, 68, 66, 69]
        }
    }
    
    # Render monitoring dashboard
    html = engine.render_dashboard_html("monitoring", monitoring_data)
    
    # Save monitoring dashboard
    monitoring_file = Path("monitoring_dashboard.html")
    with open(monitoring_file, 'w', encoding='utf-8') as f:
        f.write(html)
    
    print(f"  ✅ Monitoring dashboard saved to: {monitoring_file}")
    print(f"  ✅ Dashboard size: {len(html)} characters")
    
    return engine


def test_performance():
    """Test visualization system performance"""
    print("\n🧪 Testing Performance")
    print("=" * 22)
    
    chart_engine = UnifiedChartEngine()
    dashboard_engine = UnifiedDashboardEngine()
    
    # Test chart creation performance
    start_time = time.time()
    
    for i in range(100):
        chart = chart_engine.create_chart(
            ChartType.LINE,
            f"perf_chart_{i}",
            ChartConfig(title=f"Chart {i}")
        )
        chart.set_data(ChartData(
            x_values=list(range(10)),
            y_values=[j * i for j in range(10)]
        ))
    
    chart_time = time.time() - start_time
    print(f"📊 Created 100 charts in {chart_time:.3f}s ({100/chart_time:.1f} charts/sec)")
    
    # Test dashboard creation performance
    start_time = time.time()
    
    for i in range(10):
        dashboard = dashboard_engine.create_dashboard(
            f"perf_dashboard_{i}",
            f"Performance Dashboard {i}"
        )
        
        # Add 10 panels to each dashboard
        for j in range(10):
            dashboard_engine.add_metric_panel(
                f"perf_dashboard_{i}",
                f"panel_{j}",
                f"Panel {j}",
                {'x': j % 4, 'y': j // 4, 'width': 3, 'height': 2},
                f"metric_{j}"
            )
    
    dashboard_time = time.time() - start_time
    print(f"📊 Created 10 dashboards (100 panels) in {dashboard_time:.3f}s")
    
    # Test HTML rendering performance
    start_time = time.time()
    
    for i in range(10):
        html = dashboard_engine.render_dashboard_html(f"perf_dashboard_{i}")
    
    render_time = time.time() - start_time
    print(f"🎨 Rendered 10 dashboards in {render_time:.3f}s ({10/render_time:.1f} dashboards/sec)")
    
    return {
        'chart_creation_rate': 100 / chart_time,
        'dashboard_creation_time': dashboard_time,
        'dashboard_render_rate': 10 / render_time
    }


def main():
    """Main test function"""
    print("🚀 Unified Visualization System Test Suite")
    print("=" * 50)
    
    try:
        # Test chart engine
        chart_engine = test_chart_engine()
        
        # Test dashboard engine
        dashboard_engine = test_dashboard_engine()
        
        # Test monitoring dashboard
        monitoring_engine = test_monitoring_dashboard()
        
        # Test performance
        perf_results = test_performance()
        
        print("\n" + "=" * 50)
        print("✅ All Visualization Tests Completed!")
        print("=" * 50)
        
        print(f"\n📊 Performance Summary:")
        print(f"  • Chart creation: {perf_results['chart_creation_rate']:.1f} charts/sec")
        print(f"  • Dashboard creation: {perf_results['dashboard_creation_time']:.3f}s for 10 dashboards")
        print(f"  • Dashboard rendering: {perf_results['dashboard_render_rate']:.1f} dashboards/sec")
        
        print(f"\n🎉 Unified Visualization System is operational!")
        print(f"   Ready to replace Grafana dashboards!")
        print(f"   Files generated:")
        print(f"   • test_dashboard.html - Test dashboard")
        print(f"   • test_dashboard.json - Dashboard configuration")
        print(f"   • monitoring_dashboard.html - Monitoring dashboard")
        
        return 0
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
