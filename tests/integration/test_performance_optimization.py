#!/usr/bin/env python3
"""
Performance Optimization Test
=============================

Test comprehensive performance optimization system with caching, indexing, and data structures.
"""

import asyncio
import time
import random
from pathlib import Path
import sys

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

def print_header(title: str, level: int = 1) -> None:
    """Print formatted section header"""
    symbols = ["🚀", "🔧", "📊", "✅", "⚡"]
    symbol = symbols[min(level-1, len(symbols)-1)]
    separator = "=" * 60 if level == 1 else "-" * 50
    print(f"\n{symbol} {title}")
    print(separator)

def print_result(test_name: str, success: bool, details: str = "") -> None:
    """Print test result with formatting"""
    status = "✅" if success else "❌"
    print(f"  {status} {test_name}")
    if details:
        for line in details.split('\n'):
            if line.strip():
                print(f"    • {line.strip()}")

async def test_lru_cache():
    """Test LRU cache implementation"""
    print_header("LRU Cache Test", 2)
    
    try:
        from vibe_check.monitoring.optimization import LRUCache
        
        # Create cache with small size for testing
        cache = LRUCache(max_size=3)
        
        # Test basic operations
        cache.put("key1", "value1")
        cache.put("key2", "value2")
        cache.put("key3", "value3")
        
        # Test hits
        result1 = cache.get("key1")
        result2 = cache.get("key2")
        result3 = cache.get("key3")
        
        # Test miss
        result4 = cache.get("nonexistent")
        
        # Test eviction (add 4th item, should evict LRU)
        cache.put("key4", "value4")
        evicted_result = cache.get("key1")  # Should be evicted
        
        # Test statistics
        hit_ratio = cache.hit_ratio()
        size = cache.size()
        
        success = (
            result1 == "value1" and
            result2 == "value2" and
            result3 == "value3" and
            result4 is None and
            evicted_result is None and  # key1 should be evicted
            size == 3 and
            hit_ratio > 0
        )
        
        details = f"""Cache operations:
- Put 3 items: ✓
- Get existing items: {result1}, {result2}, {result3}
- Get non-existent: {result4}
- Eviction test: key1 evicted = {'✓' if evicted_result is None else '✗'}
- Cache size: {size}/3
- Hit ratio: {hit_ratio:.2f}
- Hits: {cache.hits}, Misses: {cache.misses}"""
        
        print_result("LRU Cache", success, details)
        return success
        
    except Exception as e:
        print_result("LRU Cache", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_bloom_filter():
    """Test Bloom filter implementation"""
    print_header("Bloom Filter Test", 2)
    
    try:
        from vibe_check.monitoring.optimization import BloomFilter
        
        # Create bloom filter
        bloom = BloomFilter(size=1000, hash_functions=3)
        
        # Add items
        test_items = ["metric1", "metric2", "metric3", "metric4", "metric5"]
        for item in test_items:
            bloom.add(item)
        
        # Test positive cases (should all return True)
        positive_results = [bloom.contains(item) for item in test_items]
        
        # Test negative cases (should mostly return False, but may have false positives)
        negative_items = ["nonexistent1", "nonexistent2", "nonexistent3"]
        negative_results = [bloom.contains(item) for item in negative_items]
        
        # Calculate false positive rate
        false_positives = sum(negative_results)
        false_positive_rate = false_positives / len(negative_results)
        
        success = (
            all(positive_results) and  # All added items should be found
            false_positive_rate <= 0.5 and  # Reasonable false positive rate
            bloom.items_added == len(test_items)
        )
        
        details = f"""Bloom filter operations:
- Items added: {bloom.items_added}
- Filter size: {bloom.size}
- Hash functions: {bloom.hash_functions}
- Positive tests: {sum(positive_results)}/{len(positive_results)} (should be {len(positive_results)})
- False positives: {false_positives}/{len(negative_results)}
- False positive rate: {false_positive_rate:.2f} (should be low)"""
        
        print_result("Bloom Filter", success, details)
        return success
        
    except Exception as e:
        print_result("Bloom Filter", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_metric_index():
    """Test metric index implementation"""
    print_header("Metric Index Test", 2)
    
    try:
        from vibe_check.monitoring.optimization import MetricIndex, IndexConfig
        
        # Create index
        config = IndexConfig(enabled=True)
        index = MetricIndex(config)
        
        # Add test metrics
        test_metrics = [
            ("cpu_usage", {"instance": "server1", "job": "node"}),
            ("cpu_usage", {"instance": "server2", "job": "node"}),
            ("memory_usage", {"instance": "server1", "job": "node"}),
            ("memory_usage", {"instance": "server2", "job": "node"}),
            ("disk_usage", {"instance": "server1", "job": "disk"}),
        ]
        
        for metric_name, labels in test_metrics:
            index.add_metric(metric_name, labels)
        
        # Test queries
        # Find all metrics for server1
        server1_metrics = index.find_metrics({"instance": "server1"})
        
        # Find all node job metrics
        node_metrics = index.find_metrics({"job": "node"})
        
        # Find specific metric
        specific_metrics = index.find_metrics({"instance": "server1", "job": "node"})
        
        # Find non-existent
        empty_metrics = index.find_metrics({"instance": "nonexistent"})
        
        # Get statistics
        stats = index.get_stats()
        
        success = (
            len(server1_metrics) == 3 and  # cpu, memory, disk for server1
            len(node_metrics) == 4 and     # cpu and memory for both servers
            len(specific_metrics) == 2 and # cpu and memory for server1 with job=node
            len(empty_metrics) == 0 and    # no metrics for nonexistent instance
            stats['total_metrics'] == len(test_metrics)
        )
        
        details = f"""Index operations:
- Metrics added: {stats['total_metrics']}
- Label indexes: {stats['label_indexes']}
- Bloom filter items: {stats['bloom_filter_items']}
- Query results:
  - server1 metrics: {len(server1_metrics)}
  - node job metrics: {len(node_metrics)}
  - specific metrics: {len(specific_metrics)}
  - empty query: {len(empty_metrics)}
- Index hit ratio: {stats['hit_ratio']:.2f}"""
        
        print_result("Metric Index", success, details)
        return success
        
    except Exception as e:
        print_result("Metric Index", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_query_cache():
    """Test query cache implementation"""
    print_header("Query Cache Test", 2)
    
    try:
        from vibe_check.monitoring.optimization import QueryCache, CacheConfig, CacheStrategy
        
        # Create cache with TTL strategy
        config = CacheConfig(
            strategy=CacheStrategy.TTL,
            max_size=100,
            ttl_seconds=1.0,  # Short TTL for testing
            enabled=True
        )
        cache = QueryCache(config)
        
        # Test caching
        query1 = "SELECT * FROM metrics WHERE name='cpu'"
        result1 = {"data": [1, 2, 3]}
        
        # Cache miss
        cached_result = await cache.get(query1)
        
        # Cache the result
        await cache.put(query1, result1)
        
        # Cache hit
        cached_result2 = await cache.get(query1)
        
        # Test with parameters
        query2 = "SELECT * FROM metrics WHERE name=?"
        params = {"name": "memory"}
        result2 = {"data": [4, 5, 6]}
        
        await cache.put(query2, result2, params)
        cached_result3 = await cache.get(query2, params)
        
        # Test TTL expiration
        await asyncio.sleep(1.1)  # Wait for TTL to expire
        expired_result = await cache.get(query1)
        
        # Get statistics
        stats = cache.get_stats()
        
        success = (
            cached_result is None and      # Initial miss
            cached_result2 == result1 and  # Cache hit
            cached_result3 == result2 and  # Parameterized cache hit
            expired_result is None and     # TTL expiration
            stats['enabled'] and
            stats['hits'] > 0
        )
        
        details = f"""Cache operations:
- Strategy: {stats['strategy']}
- Cache size: {stats['size']}/{stats['max_size']}
- Hits: {stats['hits']}
- Misses: {stats['misses']}
- Hit ratio: {stats['hit_ratio']:.2f}
- TTL entries: {stats['ttl_entries']}
- Initial miss: {'✓' if cached_result is None else '✗'}
- Cache hit: {'✓' if cached_result2 == result1 else '✗'}
- Parameterized hit: {'✓' if cached_result3 == result2 else '✗'}
- TTL expiration: {'✓' if expired_result is None else '✗'}"""
        
        print_result("Query Cache", success, details)
        
        # Cleanup
        await cache.shutdown()
        return success
        
    except Exception as e:
        print_result("Query Cache", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_performance_optimizer_integration():
    """Test integrated performance optimizer"""
    print_header("Performance Optimizer Integration Test", 2)
    
    try:
        from vibe_check.monitoring.optimization import (
            PerformanceOptimizer, OptimizationConfig, CacheConfig, IndexConfig
        )
        
        # Create optimizer with custom config
        config = OptimizationConfig(
            cache=CacheConfig(enabled=True, max_size=50),
            index=IndexConfig(enabled=True),
            enable_batching=True,
            batch_size=5
        )
        optimizer = PerformanceOptimizer(config)
        
        # Test metric indexing
        test_metrics = [
            ("cpu_usage", {"instance": "web1", "env": "prod"}),
            ("cpu_usage", {"instance": "web2", "env": "prod"}),
            ("memory_usage", {"instance": "web1", "env": "prod"}),
            ("cpu_usage", {"instance": "db1", "env": "staging"}),
        ]
        
        for metric_name, labels in test_metrics:
            optimizer.add_metric_to_index(metric_name, labels)
        
        # Test metric lookup optimization
        prod_metrics = optimizer.optimize_metric_lookup({"env": "prod"})
        web1_metrics = optimizer.optimize_metric_lookup({"instance": "web1"})
        
        # Test query caching
        query = "SELECT avg(cpu_usage) FROM metrics"
        params = {"time_range": "1h"}
        
        # Cache miss
        cached_result = await optimizer.optimize_query(query, params)
        
        # Cache the result
        result_data = {"avg_cpu": 45.2}
        await optimizer.cache_result(query, result_data, params)
        
        # Cache hit
        cached_result2 = await optimizer.optimize_query(query, params)
        
        # Test batching
        for i in range(7):  # More than batch_size (5)
            await optimizer.batch_operation("test_ops", f"operation_{i}")
        
        # Get comprehensive statistics
        stats = optimizer.get_stats()
        
        success = (
            len(prod_metrics) == 3 and      # 3 prod metrics
            len(web1_metrics) == 2 and      # 2 web1 metrics
            cached_result is None and       # Initial cache miss
            cached_result2 == result_data and # Cache hit
            stats['optimizations']['cache_optimizations'] > 0 and
            stats['optimizations']['index_optimizations'] > 0 and
            stats['cache']['enabled'] and
            stats['index']['enabled']
        )
        
        details = f"""Optimizer integration:
- Config enabled: cache={stats['config']['caching_enabled']}, index={stats['config']['indexing_enabled']}
- Metrics indexed: {stats['index']['total_metrics']}
- Cache size: {stats['cache']['size']}/{stats['cache']['max_size']}
- Cache hit ratio: {stats['cache']['hit_ratio']:.2f}
- Index hit ratio: {stats['index']['hit_ratio']:.2f}

Optimizations performed:
- Cache optimizations: {stats['optimizations']['cache_optimizations']}
- Index optimizations: {stats['optimizations']['index_optimizations']}
- Batch optimizations: {stats['optimizations']['batch_optimizations']}

Query results:
- Prod metrics found: {len(prod_metrics)}
- Web1 metrics found: {len(web1_metrics)}
- Cache miss/hit: {'✓' if cached_result is None and cached_result2 == result_data else '✗'}"""
        
        print_result("Performance Optimizer Integration", success, details)
        
        # Cleanup
        await optimizer.shutdown()
        return success
        
    except Exception as e:
        print_result("Performance Optimizer Integration", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_performance_benchmarks():
    """Test performance benchmarks"""
    print_header("Performance Benchmarks Test", 2)
    
    try:
        from vibe_check.monitoring.optimization import (
            PerformanceOptimizer, OptimizationConfig, LRUCache
        )
        
        # Benchmark cache performance
        cache = LRUCache(max_size=1000)
        
        # Warm up cache
        for i in range(500):
            cache.put(f"key_{i}", f"value_{i}")
        
        # Benchmark cache operations
        start_time = time.perf_counter()
        for i in range(1000):
            key = f"key_{i % 500}"  # 50% hit rate
            cache.get(key)
        cache_time = time.perf_counter() - start_time
        
        # Benchmark without cache (direct dictionary)
        test_dict = {f"key_{i}": f"value_{i}" for i in range(500)}
        
        start_time = time.perf_counter()
        for i in range(1000):
            key = f"key_{i % 500}"
            test_dict.get(key)
        dict_time = time.perf_counter() - start_time
        
        # Performance metrics
        cache_ops_per_sec = 1000 / cache_time
        dict_ops_per_sec = 1000 / dict_time
        overhead_ratio = cache_time / dict_time
        
        # Test large-scale indexing
        optimizer = PerformanceOptimizer()
        
        start_time = time.perf_counter()
        for i in range(1000):
            optimizer.add_metric_to_index(
                f"metric_{i % 10}",
                {"instance": f"server_{i % 20}", "env": "prod" if i % 2 == 0 else "staging"}
            )
        indexing_time = time.perf_counter() - start_time
        
        indexing_ops_per_sec = 1000 / indexing_time
        
        success = (
            cache_ops_per_sec > 10000 and    # At least 10k ops/sec
            overhead_ratio < 5.0 and         # Cache overhead < 5x
            indexing_ops_per_sec > 1000      # At least 1k indexing ops/sec
        )
        
        details = f"""Performance benchmarks:
Cache performance:
- Cache operations: {cache_ops_per_sec:.0f} ops/sec
- Dict operations: {dict_ops_per_sec:.0f} ops/sec
- Cache overhead: {overhead_ratio:.2f}x
- Cache hit ratio: {cache.hit_ratio():.2f}

Indexing performance:
- Indexing operations: {indexing_ops_per_sec:.0f} ops/sec
- Total metrics indexed: 1000

Performance targets:
- Cache ops/sec: {'✓' if cache_ops_per_sec > 10000 else '✗'} (>10k)
- Cache overhead: {'✓' if overhead_ratio < 5.0 else '✗'} (<5x)
- Indexing ops/sec: {'✓' if indexing_ops_per_sec > 1000 else '✗'} (>1k)"""
        
        print_result("Performance Benchmarks", success, details)
        
        await optimizer.shutdown()
        return success
        
    except Exception as e:
        print_result("Performance Benchmarks", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run performance optimization tests"""
    print_header("Performance Optimization System Test", 1)
    print("Testing comprehensive performance optimization with caching, indexing, and data structures")
    print("Validating LRU cache, Bloom filter, metric indexing, query caching, and performance benchmarks")
    
    # Track test results
    test_results = {}
    
    # Run tests
    test_results['lru_cache'] = await test_lru_cache()
    test_results['bloom_filter'] = await test_bloom_filter()
    test_results['metric_index'] = await test_metric_index()
    test_results['query_cache'] = await test_query_cache()
    test_results['integration'] = await test_performance_optimizer_integration()
    test_results['benchmarks'] = await test_performance_benchmarks()
    
    # Summary
    print_header("Performance Optimization Test Summary", 1)
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    success_rate = (passed_tests / total_tests) * 100
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name.upper()} test")
    
    print(f"\n📊 Overall Results:")
    print(f"  • Tests passed: {passed_tests}/{total_tests}")
    print(f"  • Success rate: {success_rate:.1f}%")
    
    if success_rate >= 75:
        print(f"  ✅ Performance optimization system SUCCESSFUL")
        print(f"  🚀 Ready for Week 10 development")
    else:
        print(f"  ❌ Performance optimization system FAILED")
        print(f"  🔧 Remediation required before proceeding")
    
    return success_rate >= 75

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
