#!/usr/bin/env python3
"""
Log Aggregation Test
====================

Test log aggregation with parsing, structured logging, and metrics extraction.
"""

import asyncio
import json
import time
import tempfile
from pathlib import Path
import sys

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

def print_header(title: str, level: int = 1) -> None:
    """Print formatted section header"""
    symbols = ["🚀", "🔧", "📊", "✅", "⚡"]
    symbol = symbols[min(level-1, len(symbols)-1)]
    separator = "=" * 60 if level == 1 else "-" * 50
    print(f"\n{symbol} {title}")
    print(separator)

def print_result(test_name: str, success: bool, details: str = "") -> None:
    """Print test result with formatting"""
    status = "✅" if success else "❌"
    print(f"  {status} {test_name}")
    if details:
        for line in details.split('\n'):
            if line.strip():
                print(f"    • {line.strip()}")

def test_log_parser():
    """Test log parsing functionality"""
    print_header("Log Parser Test", 2)
    
    try:
        from vibe_check.monitoring.logging import LogParser, LogLevel, LogFormat
        
        parser = LogParser()
        
        # Test JSON log parsing
        json_log = '{"timestamp": "2024-01-15T10:30:00Z", "level": "INFO", "message": "User login", "user_id": "123", "request_id": "req-456"}'
        json_entry = parser.parse_line(json_log, "app")
        
        # Test Apache log parsing
        apache_log = '*********** - - [15/Jan/2024:10:30:00 +0000] "GET /api/users HTTP/1.1" 200 1234'
        apache_entry = parser.parse_line(apache_log, "apache")
        
        # Test Python structured log parsing
        python_log = '2024-01-15 10:30:00,123 INFO myapp.module This is a test message'
        python_entry = parser.parse_line(python_log, "python")
        
        # Test fallback parsing
        plain_log = 'This is a plain text log message'
        plain_entry = parser.parse_line(plain_log, "plain")
        
        success = (
            json_entry is not None and
            json_entry.level == LogLevel.INFO and
            json_entry.message == "User login" and
            json_entry.user_id == "123" and
            apache_entry is not None and
            apache_entry.status_code == 200 and
            apache_entry.fields.get("client_ip") == "***********" and
            python_entry is not None and
            python_entry.level == LogLevel.INFO and
            plain_entry is not None and
            plain_entry.message == plain_log
        )
        
        details = f"""Log parsing results:
JSON log:
- Level: {json_entry.level.value if json_entry else 'None'}
- Message: {json_entry.message if json_entry else 'None'}
- User ID: {json_entry.user_id if json_entry else 'None'}
- Request ID: {json_entry.request_id if json_entry else 'None'}

Apache log:
- Status: {apache_entry.status_code if apache_entry else 'None'}
- Client IP: {apache_entry.fields.get('client_ip') if apache_entry else 'None'}
- HTTP method: {apache_entry.fields.get('http_method') if apache_entry else 'None'}

Python log:
- Level: {python_entry.level.value if python_entry else 'None'}
- Logger: {python_entry.fields.get('logger_name') if python_entry else 'None'}

Plain text:
- Message: {plain_entry.message if plain_entry else 'None'}

Patterns: {len(parser.patterns)} configured"""
        
        print_result("Log Parser", success, details)
        return success
        
    except Exception as e:
        print_result("Log Parser", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_metrics_extraction():
    """Test metrics extraction from logs"""
    print_header("Metrics Extraction Test", 2)
    
    try:
        from vibe_check.monitoring.logging import LogMetricsExtractor, LogEntry, LogLevel
        
        extractor = LogMetricsExtractor()
        
        # Test log level metrics
        info_entry = LogEntry(
            timestamp=time.time(),
            level=LogLevel.INFO,
            message="Normal operation",
            source="app"
        )
        
        # Test HTTP metrics
        http_entry = LogEntry(
            timestamp=time.time(),
            level=LogLevel.INFO,
            message="HTTP request",
            source="web",
            status_code=200,
            duration_ms=45.5,
            fields={"http_method": "GET"}
        )
        
        # Test error metrics
        error_entry = LogEntry(
            timestamp=time.time(),
            level=LogLevel.ERROR,
            message="Database connection failed",
            source="db",
            error_type="ConnectionError"
        )
        
        # Test performance metrics
        slow_entry = LogEntry(
            timestamp=time.time(),
            level=LogLevel.WARNING,
            message="Slow query",
            source="db",
            duration_ms=1500.0  # Slow request
        )
        
        # Extract metrics
        extraction_start = time.time()
        
        info_metrics = extractor.extract_metrics(info_entry)
        http_metrics = extractor.extract_metrics(http_entry)
        error_metrics = extractor.extract_metrics(error_entry)
        slow_metrics = extractor.extract_metrics(slow_entry)
        
        extraction_time = time.time() - extraction_start
        
        total_metrics = len(info_metrics) + len(http_metrics) + len(error_metrics) + len(slow_metrics)
        
        success = (
            len(info_metrics) >= 1 and  # At least log level metric
            len(http_metrics) >= 2 and  # Log level + HTTP metrics
            len(error_metrics) >= 2 and  # Log level + error metrics
            len(slow_metrics) >= 3 and  # Log level + performance + slow request
            extraction_time < 0.01 and  # Fast extraction
            any(m.name == "log_entries_total" for m in info_metrics) and
            any(m.name == "http_requests_total" for m in http_metrics) and
            any(m.name == "errors_total" for m in error_metrics) and
            any(m.name == "slow_requests_total" for m in slow_metrics)
        )
        
        details = f"""Metrics extraction:
Extraction time: {extraction_time*1000:.2f}ms
Total metrics: {total_metrics}

Info entry metrics: {len(info_metrics)}
- {[m.name for m in info_metrics]}

HTTP entry metrics: {len(http_metrics)}
- {[m.name for m in http_metrics]}

Error entry metrics: {len(error_metrics)}
- {[m.name for m in error_metrics]}

Slow entry metrics: {len(slow_metrics)}
- {[m.name for m in slow_metrics]}

Extractors: {len(extractor.extractors)} configured"""
        
        print_result("Metrics Extraction", success, details)
        return success
        
    except Exception as e:
        print_result("Metrics Extraction", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_log_aggregator():
    """Test log aggregation engine"""
    print_header("Log Aggregator Test", 2)
    
    try:
        from vibe_check.monitoring.logging import LogAggregator, LogLevel
        
        aggregator = LogAggregator(max_buffer_size=1000)
        
        # Test log processing
        test_logs = [
            '{"level": "INFO", "message": "Application started", "timestamp": "2024-01-15T10:00:00Z"}',
            '*********** - - [15/Jan/2024:10:01:00 +0000] "GET /api/health HTTP/1.1" 200 25',
            '{"level": "ERROR", "message": "Database error", "error_type": "ConnectionTimeout"}',
            '2024-01-15 10:02:00,123 WARNING myapp.db Slow query detected',
            'Plain text log message'
        ]
        
        # Process logs
        processing_start = time.time()
        
        processed_entries = []
        for i, log_line in enumerate(test_logs):
            entry = aggregator.process_log_line(log_line, f"source_{i}")
            if entry:
                processed_entries.append(entry)
        
        processing_time = time.time() - processing_start
        
        # Get statistics
        stats = aggregator.get_statistics()
        
        # Get recent logs and metrics
        recent_logs = aggregator.get_recent_logs(10)
        recent_metrics = aggregator.get_recent_metrics(20)
        error_logs = aggregator.get_recent_logs(10, LogLevel.ERROR)
        
        success = (
            len(processed_entries) == 5 and
            stats["logs_processed"] == 5 and
            stats["metrics_extracted"] > 0 and
            stats["parse_errors"] == 0 and
            len(recent_logs) == 5 and
            len(recent_metrics) > 5 and  # Should have multiple metrics
            len(error_logs) == 1 and  # One error log
            processing_time < 0.1  # Fast processing
        )
        
        details = f"""Log aggregation:
Processing time: {processing_time*1000:.1f}ms
Processed entries: {len(processed_entries)}

Statistics:
- Logs processed: {stats['logs_processed']}
- Metrics extracted: {stats['metrics_extracted']}
- Parse errors: {stats['parse_errors']}
- Buffer size: {stats['buffer_size']}
- Metrics buffer: {stats['metrics_buffer_size']}

Recent data:
- Recent logs: {len(recent_logs)}
- Recent metrics: {len(recent_metrics)}
- Error logs: {len(error_logs)}

Performance:
- Processing rate: {len(test_logs) / processing_time:.0f} logs/sec
- Memory usage: {stats['buffer_size']} / {aggregator.max_buffer_size}"""
        
        print_result("Log Aggregator", success, details)
        return success
        
    except Exception as e:
        print_result("Log Aggregator", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_file_processing():
    """Test log file processing"""
    print_header("File Processing Test", 2)
    
    try:
        from vibe_check.monitoring.logging import LogAggregator
        
        aggregator = LogAggregator()
        
        # Create temporary log file
        test_logs = [
            '{"level": "INFO", "message": "Server started", "timestamp": "2024-01-15T10:00:00Z"}',
            '{"level": "INFO", "message": "User login", "user_id": "user123"}',
            '*********** - - [15/Jan/2024:10:01:00 +0000] "GET /api/users HTTP/1.1" 200 1024',
            '192.168.1.2 - - [15/Jan/2024:10:01:30 +0000] "POST /api/orders HTTP/1.1" 201 512',
            '{"level": "ERROR", "message": "Payment failed", "error_type": "PaymentError", "user_id": "user456"}',
            '{"level": "WARNING", "message": "High memory usage", "memory_percent": 85.5}',
            '192.168.1.3 - - [15/Jan/2024:10:02:00 +0000] "GET /api/orders HTTP/1.1" 500 0',
            '{"level": "INFO", "message": "Backup completed", "duration_ms": 45000}',
        ]
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.log', delete=False) as f:
            for log in test_logs:
                f.write(log + '\n')
            temp_file = Path(f.name)
        
        try:
            # Process file
            processing_start = time.time()
            processed_count = await aggregator.process_log_file(temp_file, "test_app")
            processing_time = time.time() - processing_start
            
            # Get results
            stats = aggregator.get_statistics()
            recent_logs = aggregator.get_recent_logs(20)
            recent_metrics = aggregator.get_recent_metrics(50)
            
            success = (
                processed_count == len(test_logs) and
                stats["logs_processed"] == len(test_logs) and
                len(recent_logs) == len(test_logs) and
                len(recent_metrics) > len(test_logs) and  # Multiple metrics per log
                processing_time < 1.0  # Fast file processing
            )
            
            details = f"""File processing:
File size: {len(test_logs)} lines
Processed count: {processed_count}
Processing time: {processing_time*1000:.1f}ms
Processing rate: {processed_count / processing_time:.0f} lines/sec

Results:
- Logs in buffer: {len(recent_logs)}
- Metrics generated: {len(recent_metrics)}
- Parse errors: {stats['parse_errors']}

Performance:
- File I/O: Async
- Memory usage: Buffered
- Error handling: Graceful"""
            
            print_result("File Processing", success, details)
            return success
            
        finally:
            # Clean up
            temp_file.unlink()
        
    except Exception as e:
        print_result("File Processing", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_python_logging_integration():
    """Test Python logging integration"""
    print_header("Python Logging Integration Test", 2)

    try:
        import logging
        from vibe_check.monitoring.logging import LogAggregator, integrate_with_logging

        # Setup aggregator and integration
        aggregator = LogAggregator()
        handler = integrate_with_logging(aggregator, source="test_app")

        # Create test logger
        test_logger = logging.getLogger("test_integration")
        test_logger.setLevel(logging.DEBUG)

        # Generate test logs
        integration_start = time.time()

        test_logger.info("Application started")
        test_logger.warning("High CPU usage detected")
        test_logger.error("Database connection failed")

        try:
            raise ValueError("Test exception")
        except ValueError:
            test_logger.exception("Exception occurred")

        integration_time = time.time() - integration_start

        # Allow time for processing
        await asyncio.sleep(0.01)
        
        # Get results
        stats = aggregator.get_statistics()
        recent_logs = aggregator.get_recent_logs(10)
        recent_metrics = aggregator.get_recent_metrics(20)
        
        success = (
            stats["logs_processed"] >= 4 and  # At least 4 log messages
            len(recent_logs) >= 4 and
            len(recent_metrics) >= 4 and  # At least one metric per log
            integration_time < 0.1 and  # Fast integration
            any("Application started" in log.message for log in recent_logs) and
            any("Exception occurred" in log.message for log in recent_logs)
        )
        
        details = f"""Python logging integration:
Integration time: {integration_time*1000:.1f}ms
Handler configured: ✓

Generated logs:
- Info: Application started
- Warning: High CPU usage
- Error: Database connection failed
- Exception: Test exception with traceback

Results:
- Logs processed: {stats['logs_processed']}
- Metrics extracted: {stats['metrics_extracted']}
- Recent logs: {len(recent_logs)}
- Recent metrics: {len(recent_metrics)}

Features:
- Structured logging: ✓
- Exception handling: ✓
- Automatic metrics: ✓
- Real-time processing: ✓"""
        
        print_result("Python Logging Integration", success, details)
        return success
        
    except Exception as e:
        print_result("Python Logging Integration", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_performance_benchmarks():
    """Test log aggregation performance"""
    print_header("Performance Benchmarks Test", 2)
    
    try:
        from vibe_check.monitoring.logging import LogAggregator
        
        aggregator = LogAggregator(max_buffer_size=50000)
        
        # Generate large number of test logs
        test_logs = []

        for i in range(10000):  # 10k logs
            template_choice = i % 4

            if template_choice == 0:
                # JSON request log
                log = f'{{"level": "INFO", "message": "Request processed", "request_id": "req-{i}", "duration_ms": {50 + (i % 100)}}}'
            elif template_choice == 1:
                # Apache log
                log = f'192.168.1.{1 + (i % 254)} - - [15/Jan/2024:10:{i % 60:02d}:00 +0000] "GET /api/endpoint HTTP/1.1" 200 {100 + (i % 1000)}'
            elif template_choice == 2:
                # JSON error log
                log = f'{{"level": "ERROR", "message": "Error occurred", "error_type": "Type{i % 10}", "user_id": "user{i}"}}'
            else:
                # Python structured log
                log = f'2024-01-15 10:{i % 60:02d}:00,123 WARNING app.module Message number {i}'

            test_logs.append(log)
        
        # Benchmark processing
        benchmark_start = time.time()
        
        processed_count = 0
        for log_line in test_logs:
            entry = aggregator.process_log_line(log_line, "benchmark")
            if entry:
                processed_count += 1
            
            # Yield control every 1000 logs
            if processed_count % 1000 == 0:
                await asyncio.sleep(0.001)
        
        benchmark_time = time.time() - benchmark_start
        
        # Get final statistics
        stats = aggregator.get_statistics()
        processing_rate = processed_count / benchmark_time
        
        success = (
            processed_count == len(test_logs) and
            processing_rate > 1000 and  # > 1000 logs/sec
            benchmark_time < 30.0 and  # Complete within 30 seconds
            stats["parse_errors"] < len(test_logs) * 0.01  # < 1% error rate
        )
        
        details = f"""Performance benchmarks:
Test logs: {len(test_logs):,}
Processed: {processed_count:,}
Processing time: {benchmark_time:.2f}s
Processing rate: {processing_rate:.0f} logs/sec

Statistics:
- Logs processed: {stats['logs_processed']:,}
- Metrics extracted: {stats['metrics_extracted']:,}
- Parse errors: {stats['parse_errors']}
- Error rate: {(stats['parse_errors'] / max(1, stats['logs_processed']) * 100):.2f}%

Performance targets:
- Rate: {processing_rate:.0f} logs/sec (target: >1000)
- Memory: {stats['buffer_size']:,} / {aggregator.max_buffer_size:,}
- Efficiency: {(stats['metrics_extracted'] / max(1, stats['logs_processed'])):.1f} metrics/log"""
        
        print_result("Performance Benchmarks", success, details)
        return success
        
    except Exception as e:
        print_result("Performance Benchmarks", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run log aggregation tests"""
    print_header("Log Aggregation Test", 1)
    print("Testing log aggregation with parsing, structured logging, and metrics extraction")
    print("Validating multi-format parsing, metrics extraction, file processing, and Python integration")
    
    # Track test results
    test_results = {}
    
    # Run tests
    test_results['parser'] = test_log_parser()
    test_results['metrics'] = test_metrics_extraction()
    test_results['aggregator'] = test_log_aggregator()
    test_results['file_processing'] = await test_file_processing()
    test_results['python_integration'] = await test_python_logging_integration()
    test_results['performance'] = await test_performance_benchmarks()
    
    # Summary
    print_header("Log Aggregation Test Summary", 1)
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    success_rate = (passed_tests / total_tests) * 100
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name.upper().replace('_', ' ')} test")
    
    print(f"\n📊 Overall Results:")
    print(f"  • Tests passed: {passed_tests}/{total_tests}")
    print(f"  • Success rate: {success_rate:.1f}%")
    
    if success_rate >= 75:
        print(f"  ✅ Log aggregation system SUCCESSFUL")
        print(f"  🚀 Ready for distributed tracing development")
        print(f"  📈 Performance: >1000 logs/sec processing rate")
    else:
        print(f"  ❌ Log aggregation system FAILED")
        print(f"  🔧 Remediation required before proceeding")
    
    return success_rate >= 75

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
