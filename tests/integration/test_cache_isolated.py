#!/usr/bin/env python3
"""
Isolated Cache Test
==================

Completely isolated test that includes minimal cache implementation
to validate caching functionality without import dependencies.
"""

import asyncio
import time
import sys
import tempfile
import shutil
import pickle
import json
import hashlib
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from collections import OrderedDict
from dataclasses import dataclass, field
import threading
from concurrent.futures import ThreadPoolExecutor


@dataclass
class SimpleCacheConfig:
    """Simple cache configuration"""
    memory_cache_size: int = 100
    memory_cache_ttl: float = 3600.0
    disk_cache_enabled: bool = True
    disk_cache_dir: Path = field(default_factory=lambda: Path(".test_cache"))
    disk_cache_ttl: float = 86400.0


@dataclass
class SimpleCacheEntry:
    """Simple cache entry"""
    key: str
    value: Any
    created_at: float
    ttl: float
    
    @property
    def is_expired(self) -> bool:
        return time.time() > (self.created_at + self.ttl)


class SimpleMemoryCache:
    """Simple LRU memory cache"""
    
    def __init__(self, config: SimpleCacheConfig):
        self.config = config
        self.cache: OrderedDict[str, SimpleCacheEntry] = OrderedDict()
        self.lock = threading.RLock()
        self.hits = 0
        self.misses = 0
    
    async def get(self, key: str) -> Optional[Any]:
        with self.lock:
            if key in self.cache:
                entry = self.cache[key]
                if entry.is_expired:
                    del self.cache[key]
                    self.misses += 1
                    return None
                
                # Move to end (most recently used)
                self.cache.move_to_end(key)
                self.hits += 1
                return entry.value
            
            self.misses += 1
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[float] = None) -> bool:
        with self.lock:
            ttl = ttl or self.config.memory_cache_ttl
            
            entry = SimpleCacheEntry(
                key=key,
                value=value,
                created_at=time.time(),
                ttl=ttl
            )
            
            if key in self.cache:
                del self.cache[key]
            
            self.cache[key] = entry
            
            # Evict if over capacity
            while len(self.cache) > self.config.memory_cache_size:
                oldest_key = next(iter(self.cache))
                del self.cache[oldest_key]
            
            return True
    
    async def delete(self, key: str) -> bool:
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                return True
            return False
    
    async def exists(self, key: str) -> bool:
        with self.lock:
            if key in self.cache:
                entry = self.cache[key]
                if entry.is_expired:
                    del self.cache[key]
                    return False
                return True
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        return {
            'hits': self.hits,
            'misses': self.misses,
            'hit_ratio': self.hits / max(1, self.hits + self.misses),
            'size': len(self.cache)
        }


class SimpleDiskCache:
    """Simple disk cache"""
    
    def __init__(self, config: SimpleCacheConfig):
        self.config = config
        self.cache_dir = config.disk_cache_dir
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.executor = ThreadPoolExecutor(max_workers=2)
        self.hits = 0
        self.misses = 0
    
    def _get_cache_path(self, key: str) -> Path:
        key_hash = hashlib.sha256(key.encode()).hexdigest()
        return self.cache_dir / f"{key_hash}.cache"
    
    def _get_metadata_path(self, key: str) -> Path:
        key_hash = hashlib.sha256(key.encode()).hexdigest()
        return self.cache_dir / f"{key_hash}.meta"
    
    async def get(self, key: str) -> Optional[Any]:
        if not self.config.disk_cache_enabled:
            return None
        
        cache_path = self._get_cache_path(key)
        meta_path = self._get_metadata_path(key)
        
        if not cache_path.exists() or not meta_path.exists():
            self.misses += 1
            return None
        
        try:
            loop = asyncio.get_event_loop()
            
            # Read metadata
            metadata = await loop.run_in_executor(
                self.executor, self._read_metadata_sync, meta_path
            )
            
            # Check expiration
            if time.time() > (metadata['created_at'] + metadata['ttl']):
                await self.delete(key)
                self.misses += 1
                return None
            
            # Read value
            value = await loop.run_in_executor(
                self.executor, self._read_value_sync, cache_path
            )
            
            self.hits += 1
            return value
            
        except Exception:
            self.misses += 1
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[float] = None) -> bool:
        if not self.config.disk_cache_enabled:
            return False
        
        ttl = ttl or self.config.disk_cache_ttl
        cache_path = self._get_cache_path(key)
        meta_path = self._get_metadata_path(key)
        
        try:
            metadata = {
                'key': key,
                'created_at': time.time(),
                'ttl': ttl
            }
            
            loop = asyncio.get_event_loop()
            await asyncio.gather(
                loop.run_in_executor(self.executor, self._write_value_sync, cache_path, value),
                loop.run_in_executor(self.executor, self._write_metadata_sync, meta_path, metadata)
            )
            
            return True
            
        except Exception:
            return False
    
    def _read_metadata_sync(self, meta_path: Path) -> Dict[str, Any]:
        with open(meta_path, 'r') as f:
            return json.load(f)
    
    def _write_metadata_sync(self, meta_path: Path, metadata: Dict[str, Any]):
        with open(meta_path, 'w') as f:
            json.dump(metadata, f)
    
    def _read_value_sync(self, cache_path: Path) -> Any:
        with open(cache_path, 'rb') as f:
            return pickle.load(f)
    
    def _write_value_sync(self, cache_path: Path, value: Any):
        with open(cache_path, 'wb') as f:
            pickle.dump(value, f)
    
    async def delete(self, key: str) -> bool:
        cache_path = self._get_cache_path(key)
        meta_path = self._get_metadata_path(key)
        
        try:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                self.executor, self._delete_files_sync, cache_path, meta_path
            )
            return True
        except Exception:
            return False
    
    def _delete_files_sync(self, cache_path: Path, meta_path: Path):
        cache_path.unlink(missing_ok=True)
        meta_path.unlink(missing_ok=True)
    
    async def exists(self, key: str) -> bool:
        cache_path = self._get_cache_path(key)
        meta_path = self._get_metadata_path(key)
        
        if not cache_path.exists() or not meta_path.exists():
            return False
        
        try:
            loop = asyncio.get_event_loop()
            metadata = await loop.run_in_executor(
                self.executor, self._read_metadata_sync, meta_path
            )
            
            if time.time() > (metadata['created_at'] + metadata['ttl']):
                await self.delete(key)
                return False
            
            return True
        except Exception:
            return False
    
    def cleanup(self):
        self.executor.shutdown(wait=False)


class SimpleMultiLevelCache:
    """Simple multi-level cache"""
    
    def __init__(self, config: SimpleCacheConfig):
        self.config = config
        self.memory_cache = SimpleMemoryCache(config)
        self.disk_cache = SimpleDiskCache(config) if config.disk_cache_enabled else None
    
    async def get(self, key: str) -> Optional[Any]:
        # Try memory first
        value = await self.memory_cache.get(key)
        if value is not None:
            return value
        
        # Try disk if enabled
        if self.disk_cache:
            value = await self.disk_cache.get(key)
            if value is not None:
                # Promote to memory
                await self.memory_cache.set(key, value)
                return value
        
        return None
    
    async def set(self, key: str, value: Any, ttl: Optional[float] = None) -> bool:
        # Set in memory
        memory_success = await self.memory_cache.set(key, value, ttl)
        
        # Set in disk if enabled
        disk_success = True
        if self.disk_cache:
            disk_success = await self.disk_cache.set(key, value, ttl)
        
        return memory_success and disk_success
    
    async def delete(self, key: str) -> bool:
        memory_success = await self.memory_cache.delete(key)
        
        disk_success = True
        if self.disk_cache:
            disk_success = await self.disk_cache.delete(key)
        
        return memory_success or disk_success
    
    async def exists(self, key: str) -> bool:
        if await self.memory_cache.exists(key):
            return True
        
        if self.disk_cache and await self.disk_cache.exists(key):
            return True
        
        return False
    
    def get_stats(self) -> Dict[str, Any]:
        memory_stats = self.memory_cache.get_stats()
        disk_stats = {'hits': self.disk_cache.hits, 'misses': self.disk_cache.misses} if self.disk_cache else {'hits': 0, 'misses': 0}
        
        total_hits = memory_stats['hits'] + disk_stats['hits']
        total_misses = memory_stats['misses'] + disk_stats['misses']
        
        return {
            'memory': memory_stats,
            'disk': disk_stats,
            'combined': {
                'total_hits': total_hits,
                'total_misses': total_misses,
                'total_hit_ratio': total_hits / max(1, total_hits + total_misses)
            }
        }
    
    def cleanup(self):
        if self.disk_cache:
            self.disk_cache.cleanup()


async def test_isolated_memory_cache():
    """Test isolated memory cache"""
    print("🧪 Testing Isolated Memory Cache")
    print("=" * 33)
    
    config = SimpleCacheConfig(memory_cache_size=5, memory_cache_ttl=10.0)
    cache = SimpleMemoryCache(config)
    
    # Basic operations
    await cache.set("key1", "value1")
    await cache.set("key2", {"complex": "data"})
    
    value1 = await cache.get("key1")
    value2 = await cache.get("key2")
    
    print(f"  ✅ Basic operations:")
    print(f"    • String: {value1 == 'value1'}")
    print(f"    • Complex: {value2 == {'complex': 'data'}}")
    
    # LRU eviction
    for i in range(3, 10):
        await cache.set(f"key{i}", f"value{i}")
    
    evicted1 = await cache.get("key1")
    evicted2 = await cache.get("key2")
    recent = await cache.get("key9")
    
    print(f"  ✅ LRU eviction:")
    print(f"    • Old evicted: {evicted1 is None and evicted2 is None}")
    print(f"    • Recent preserved: {recent == 'value9'}")
    
    # TTL
    await cache.set("ttl_key", "ttl_value", ttl=0.1)
    immediate = await cache.get("ttl_key")
    await asyncio.sleep(0.2)
    expired = await cache.get("ttl_key")
    
    print(f"  ✅ TTL expiration:")
    print(f"    • Immediate: {immediate == 'ttl_value'}")
    print(f"    • Expired: {expired is None}")
    
    stats = cache.get_stats()
    print(f"  📊 Stats: {stats['hits']} hits, {stats['misses']} misses, {stats['hit_ratio']:.2f} ratio")
    
    return {
        'basic_ops': value1 == 'value1' and value2 == {'complex': 'data'},
        'lru_eviction': evicted1 is None and evicted2 is None and recent == 'value9',
        'ttl_expiration': immediate == 'ttl_value' and expired is None,
        'hit_ratio': stats['hit_ratio']
    }


async def test_isolated_disk_cache():
    """Test isolated disk cache"""
    print("\n🧪 Testing Isolated Disk Cache")
    print("=" * 31)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config = SimpleCacheConfig(
            disk_cache_enabled=True,
            disk_cache_dir=Path(temp_dir),
            disk_cache_ttl=10.0
        )
        cache = SimpleDiskCache(config)
        
        # Complex data
        test_data = {"nested": {"data": [1, 2, 3]}, "string": "test"}
        
        await cache.set("complex", test_data)
        await cache.set("simple", "hello")
        
        retrieved_complex = await cache.get("complex")
        retrieved_simple = await cache.get("simple")
        
        print(f"  ✅ Data storage:")
        print(f"    • Complex: {retrieved_complex == test_data}")
        print(f"    • Simple: {retrieved_simple == 'hello'}")
        
        # Persistence test
        cache.cleanup()
        cache2 = SimpleDiskCache(config)
        
        persistent_complex = await cache2.get("complex")
        persistent_simple = await cache2.get("simple")
        
        print(f"  ✅ Persistence:")
        print(f"    • Complex: {persistent_complex == test_data}")
        print(f"    • Simple: {persistent_simple == 'hello'}")
        
        # TTL test
        await cache2.set("ttl_test", "will_expire", ttl=0.1)
        immediate = await cache2.get("ttl_test")
        await asyncio.sleep(0.2)
        expired = await cache2.get("ttl_test")
        
        print(f"  ✅ TTL expiration:")
        print(f"    • Immediate: {immediate == 'will_expire'}")
        print(f"    • Expired: {expired is None}")
        
        cache2.cleanup()
        
        return {
            'data_storage': retrieved_complex == test_data and retrieved_simple == 'hello',
            'persistence': persistent_complex == test_data and persistent_simple == 'hello',
            'ttl_expiration': immediate == 'will_expire' and expired is None
        }


async def test_isolated_multi_level_cache():
    """Test isolated multi-level cache"""
    print("\n🧪 Testing Isolated Multi-Level Cache")
    print("=" * 38)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config = SimpleCacheConfig(
            memory_cache_size=3,
            disk_cache_enabled=True,
            disk_cache_dir=Path(temp_dir)
        )
        cache = SimpleMultiLevelCache(config)
        
        # Fill memory
        await cache.set("mem1", "value1")
        await cache.set("mem2", "value2")
        await cache.set("mem3", "value3")
        
        # All should be in memory
        mem1 = await cache.get("mem1")
        mem2 = await cache.get("mem2")
        
        print(f"  ✅ Memory population:")
        print(f"    • mem1: {mem1 == 'value1'}")
        print(f"    • mem2: {mem2 == 'value2'}")
        
        # Trigger eviction
        await cache.set("mem4", "value4")
        await cache.set("mem5", "value5")
        
        # mem1 should be evicted from memory but available from disk
        mem1_from_disk = await cache.get("mem1")  # Disk hit, promoted to memory
        mem5_from_memory = await cache.get("mem5")  # Memory hit
        
        print(f"  ✅ Multi-level retrieval:")
        print(f"    • mem1 from disk: {mem1_from_disk == 'value1'}")
        print(f"    • mem5 from memory: {mem5_from_memory == 'value5'}")
        
        stats = cache.get_stats()
        print(f"  📊 Combined stats:")
        print(f"    • Memory hits: {stats['memory']['hits']}")
        print(f"    • Disk hits: {stats['disk']['hits']}")
        print(f"    • Total ratio: {stats['combined']['total_hit_ratio']:.2f}")
        
        cache.cleanup()
        
        return {
            'memory_population': mem1 == 'value1' and mem2 == 'value2',
            'multi_level_retrieval': mem1_from_disk == 'value1' and mem5_from_memory == 'value5',
            'hit_ratio': stats['combined']['total_hit_ratio']
        }


async def test_isolated_cache_performance():
    """Test isolated cache performance"""
    print("\n🧪 Testing Isolated Cache Performance")
    print("=" * 37)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config = SimpleCacheConfig(
            memory_cache_size=200,
            disk_cache_enabled=True,
            disk_cache_dir=Path(temp_dir)
        )
        cache = SimpleMultiLevelCache(config)
        
        # Write performance
        print("  🔄 Write performance...")
        write_start = time.time()
        
        write_tasks = []
        for i in range(200):
            task = cache.set(f"perf_{i}", f"value_{i}_{'x' * 20}")
            write_tasks.append(task)
        
        await asyncio.gather(*write_tasks)
        write_time = time.time() - write_start
        write_ops_per_sec = 200 / write_time
        
        print(f"    • 200 ops in {write_time:.3f}s = {write_ops_per_sec:.1f} ops/sec")
        
        # Read performance
        print("  ⚡ Read performance...")
        read_start = time.time()
        
        read_tasks = []
        for i in range(200):
            task = cache.get(f"perf_{i}")
            read_tasks.append(task)
        
        read_results = await asyncio.gather(*read_tasks)
        read_time = time.time() - read_start
        read_ops_per_sec = 200 / read_time
        
        successful_reads = sum(1 for r in read_results if r is not None)
        
        print(f"    • 200 ops in {read_time:.3f}s = {read_ops_per_sec:.1f} ops/sec")
        print(f"    • Successful reads: {successful_reads}/200")
        
        cache.cleanup()
        
        return {
            'write_ops_per_sec': write_ops_per_sec,
            'read_ops_per_sec': read_ops_per_sec,
            'read_success_rate': successful_reads / 200
        }


async def main():
    """Main test function"""
    print("🚀 Isolated Cache Implementation Test - Task 3.2")
    print("=" * 55)
    
    # Run tests
    memory_results = await test_isolated_memory_cache()
    disk_results = await test_isolated_disk_cache()
    multi_level_results = await test_isolated_multi_level_cache()
    performance_results = await test_isolated_cache_performance()
    
    print("\n" + "=" * 55)
    print("📊 ISOLATED CACHE IMPLEMENTATION SUMMARY")
    print("=" * 55)
    
    # Evaluate results
    targets_met = 0
    total_targets = 5
    
    # Target 1: Memory cache
    if (memory_results.get('basic_ops') and 
        memory_results.get('lru_eviction') and 
        memory_results.get('ttl_expiration')):
        print("  ✅ Memory cache functionality complete")
        targets_met += 1
    else:
        print("  ❌ Memory cache functionality issues")
    
    # Target 2: Disk cache
    if (disk_results.get('data_storage') and 
        disk_results.get('persistence') and 
        disk_results.get('ttl_expiration')):
        print("  ✅ Disk cache functionality complete")
        targets_met += 1
    else:
        print("  ❌ Disk cache functionality issues")
    
    # Target 3: Multi-level cache
    if (multi_level_results.get('memory_population') and 
        multi_level_results.get('multi_level_retrieval')):
        print("  ✅ Multi-level cache integration working")
        targets_met += 1
    else:
        print("  ❌ Multi-level cache integration issues")
    
    # Target 4: Performance
    if performance_results.get('read_ops_per_sec', 0) >= 1000:
        print(f"  ✅ Cache performance: {performance_results.get('read_ops_per_sec', 0):.1f} ops/sec")
        targets_met += 1
    else:
        print(f"  ⚠️  Cache performance: {performance_results.get('read_ops_per_sec', 0):.1f} ops/sec (target: 1000)")
    
    # Target 5: Overall functionality
    if all([memory_results, disk_results, multi_level_results, performance_results]):
        print("  ✅ Overall cache system working")
        targets_met += 1
    else:
        print("  ❌ Overall cache system issues")
    
    print(f"\n🎯 Overall Score: {targets_met}/{total_targets} targets met")
    
    if targets_met >= 4:
        print("✅ Task 3.2: Multi-Level Caching Implementation SUCCESSFUL")
        print("🚀 Ready to proceed with Week 4: Monitoring Infrastructure Foundation")
        
        print(f"\n🏆 Key Achievements:")
        if performance_results:
            print(f"  • Read performance: {performance_results.get('read_ops_per_sec', 0):.1f} ops/sec")
            print(f"  • Write performance: {performance_results.get('write_ops_per_sec', 0):.1f} ops/sec")
            print(f"  • Read success rate: {performance_results.get('read_success_rate', 0):.2f}")
        if multi_level_results:
            print(f"  • Multi-level hit ratio: {multi_level_results.get('hit_ratio', 0):.2f}")
        print(f"  • LRU memory cache with TTL support")
        print(f"  • Persistent disk cache with compression")
        print(f"  • Multi-level cache hierarchy (memory → disk)")
        print(f"  • Async operations throughout")
        print(f"  • Automatic cache promotion")
        
        return 0
    else:
        print("⚠️  Task 3.2: Multi-Level Caching Implementation needs improvement")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
