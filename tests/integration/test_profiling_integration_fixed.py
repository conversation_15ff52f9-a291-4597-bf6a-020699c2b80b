#!/usr/bin/env python3
"""
Profiling Integration Fixed Test
===============================

Test to verify the profiling collector integration works after fixing import issues.
This test validates the critical path for profiling data to flow to TSDB.
"""

import asyncio
import time
import sys
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


async def test_profiling_imports_fixed():
    """Test that profiling imports work after fixes"""
    print("🧪 Testing Profiling Imports Fixed")
    print("=" * 35)
    
    try:
        # Test direct imports using absolute paths
        from vibe_check.monitoring.profiling.execution_profiler import ExecutionProfiler, get_profiler
        from vibe_check.monitoring.collectors.profiling_collector import ProfilingMetricsCollector
        from vibe_check.monitoring.collectors.base_collector import CollectorConfig, CollectionInterval
        
        print(f"  ✅ Imports successful:")
        print(f"    • ExecutionProfiler imported")
        print(f"    • ProfilingMetricsCollector imported")
        print(f"    • Base collector components imported")
        
        # Test object creation
        profiler = ExecutionProfiler(enable_memory_tracking=True)
        
        config = CollectorConfig(
            collection_interval=CollectionInterval.FAST.value,
            max_collection_time=3.0,
            labels={"test": "fixed_integration"}
        )
        
        collector = ProfilingMetricsCollector(profiler, config)
        
        print(f"  ✅ Objects created:")
        print(f"    • Profiler: {type(profiler).__name__}")
        print(f"    • Collector: {collector.name}")
        print(f"    • Config: {config.enabled}")
        
        # Test metric registration
        metric_definitions = collector.get_metric_definitions()
        
        print(f"  ✅ Metrics registered:")
        print(f"    • Total metrics: {len(metric_definitions)}")
        print(f"    • Session metrics: {len([m for m in metric_definitions if 'session' in m])}")
        print(f"    • Function metrics: {len([m for m in metric_definitions if 'function' in m])}")
        
        collector.cleanup()
        
        return {
            'imports_successful': True,
            'objects_created': True,
            'metrics_registered': len(metric_definitions) > 0,
            'collector_functional': collector.name == "ProfilingMetricsCollector"
        }
        
    except Exception as e:
        print(f"❌ Profiling imports test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def test_profiling_data_pipeline():
    """Test the complete profiling data pipeline"""
    print("\n🧪 Testing Profiling Data Pipeline")
    print("=" * 35)
    
    try:
        from vibe_check.monitoring.profiling.execution_profiler import ExecutionProfiler
        from vibe_check.monitoring.collectors.profiling_collector import ProfilingMetricsCollector
        from vibe_check.monitoring.collectors.base_collector import CollectorConfig, CollectionInterval
        
        # Create profiler and collector
        profiler = ExecutionProfiler(
            enable_memory_tracking=True,
            bottleneck_threshold=0.005  # 5ms threshold
        )
        
        config = CollectorConfig(
            collection_interval=CollectionInterval.FAST.value,
            max_collection_time=5.0,
            labels={"test": "data_pipeline"}
        )
        
        collector = ProfilingMetricsCollector(profiler, config)
        
        print(f"  ✅ Pipeline setup:")
        print(f"    • Profiler configured")
        print(f"    • Collector configured")
        print(f"    • Bottleneck threshold: {profiler.bottleneck_threshold}s")
        
        # Start profiling session
        session_id = profiler.start_profiling("pipeline_test")
        
        # Define test functions with different performance characteristics
        @profiler.profile_function(name="fast_operation")
        def fast_operation():
            """Fast operation"""
            return sum(range(100))
        
        @profiler.profile_function(name="slow_operation")
        def slow_operation():
            """Slow operation that should be a bottleneck"""
            time.sleep(0.01)  # 10ms - should be detected
            return "slow_complete"
        
        @profiler.profile_function(name="nested_operation")
        def nested_operation():
            """Operation that calls other functions"""
            fast_operation()
            slow_operation()
            return "nested_complete"
        
        print(f"  🔄 Executing profiled functions...")
        
        # Execute functions to generate profiling data
        for i in range(3):
            fast_operation()
        
        result = nested_operation()
        
        # Stop profiling
        session = profiler.stop_profiling()
        
        print(f"  ✅ Profiling session completed:")
        print(f"    • Session ID: {session.session_id}")
        print(f"    • Total functions: {session.total_functions}")
        print(f"    • Duration: {session.total_duration:.6f}s")
        
        # Collect metrics from profiling data
        print("  🔄 Collecting metrics from profiling data...")
        start_time = time.time()
        
        metrics = await collector.collect_metrics()
        
        collection_time = time.time() - start_time
        
        print(f"    • Metrics collected: {len(metrics)}")
        print(f"    • Collection time: {collection_time:.3f}s")
        
        # Analyze collected metrics
        if metrics:
            session_metrics = [m for m in metrics if 'session' in m.name]
            function_metrics = [m for m in metrics if 'function' in m.name]
            bottleneck_metrics = [m for m in metrics if 'bottleneck' in m.name]
            overhead_metrics = [m for m in metrics if 'overhead' in m.name]
            
            print(f"  ✅ Metrics analysis:")
            print(f"    • Session metrics: {len(session_metrics)}")
            print(f"    • Function metrics: {len(function_metrics)}")
            print(f"    • Bottleneck metrics: {len(bottleneck_metrics)}")
            print(f"    • Overhead metrics: {len(overhead_metrics)}")
            
            # Show sample metrics
            if function_metrics:
                sample_metric = function_metrics[0]
                print(f"    • Sample function metric: {sample_metric.name}={sample_metric.value}")
            
            if bottleneck_metrics:
                print(f"    • Bottlenecks detected: {len(bottleneck_metrics)}")
        
        # Get call graph and bottlenecks
        call_graph = profiler.get_call_graph(session_id)
        bottlenecks = profiler.get_bottlenecks(session_id)
        
        print(f"  📊 Profiling analysis:")
        print(f"    • Call graph generated: {call_graph is not None}")
        print(f"    • Bottlenecks found: {len(bottlenecks)}")
        
        if bottlenecks:
            for i, bottleneck in enumerate(bottlenecks[:2]):
                print(f"    • Bottleneck {i+1}: {bottleneck['function']} ({bottleneck['duration']:.6f}s)")
        
        # Get summary
        summary = collector.get_profiling_summary()
        
        print(f"  📈 Pipeline summary:")
        print(f"    • Total sessions: {summary['total_sessions']}")
        print(f"    • Function calls: {summary['total_function_calls']}")
        print(f"    • Execution time: {summary['total_execution_time']:.6f}s")
        print(f"    • Profiler overhead: {summary['profiler_overhead']:.4f}%")
        
        collector.cleanup()
        
        return {
            'pipeline_setup': True,
            'session_completed': session is not None,
            'metrics_collected': len(metrics) > 0,
            'call_graph_generated': call_graph is not None,
            'bottlenecks_detected': len(bottlenecks) > 0,
            'function_calls_tracked': summary['total_function_calls'] > 0,
            'data_pipeline_working': True
        }
        
    except Exception as e:
        print(f"❌ Profiling data pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def test_tsdb_integration_readiness():
    """Test that profiling data is ready for TSDB integration"""
    print("\n🧪 Testing TSDB Integration Readiness")
    print("=" * 37)
    
    try:
        from vibe_check.monitoring.profiling.execution_profiler import ExecutionProfiler
        from vibe_check.monitoring.collectors.profiling_collector import ProfilingMetricsCollector
        
        # Create minimal setup
        profiler = ExecutionProfiler()
        collector = ProfilingMetricsCollector(profiler)
        
        # Quick profiling session
        session_id = profiler.start_profiling("tsdb_test")
        
        @profiler.profile_function(name="tsdb_test_function")
        def tsdb_test_function():
            time.sleep(0.001)
            return "tsdb_ready"
        
        # Execute function
        result = tsdb_test_function()
        
        # Stop profiling
        session = profiler.stop_profiling()
        
        # Collect metrics
        metrics = await collector.collect_metrics()
        
        print(f"  ✅ TSDB readiness check:")
        print(f"    • Metrics generated: {len(metrics)}")
        
        # Validate metric format for TSDB
        tsdb_ready_metrics = 0
        for metric in metrics:
            if (hasattr(metric, 'name') and 
                hasattr(metric, 'value') and 
                hasattr(metric, 'labels') and 
                hasattr(metric, 'timestamp')):
                tsdb_ready_metrics += 1
        
        print(f"    • TSDB-compatible metrics: {tsdb_ready_metrics}")
        print(f"    • Metric format validation: {tsdb_ready_metrics == len(metrics)}")
        
        # Show sample metric structure
        if metrics:
            sample = metrics[0]
            print(f"    • Sample metric structure:")
            print(f"      - Name: {sample.name}")
            print(f"      - Value: {sample.value}")
            print(f"      - Labels: {sample.labels}")
            print(f"      - Timestamp: {sample.timestamp}")
        
        collector.cleanup()
        
        return {
            'metrics_generated': len(metrics) > 0,
            'tsdb_format_valid': tsdb_ready_metrics == len(metrics),
            'metric_structure_correct': len(metrics) > 0 and hasattr(metrics[0], 'name'),
            'integration_ready': tsdb_ready_metrics > 0
        }
        
    except Exception as e:
        print(f"❌ TSDB integration readiness test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def main():
    """Main test function"""
    print("🚀 Profiling Integration Fixed Test Suite")
    print("=" * 50)
    
    # Run tests
    import_results = await test_profiling_imports_fixed()
    pipeline_results = await test_profiling_data_pipeline()
    tsdb_results = await test_tsdb_integration_readiness()
    
    print("\n" + "=" * 50)
    print("📊 PROFILING INTEGRATION FIX SUMMARY")
    print("=" * 50)
    
    # Evaluate results
    targets_met = 0
    total_targets = 3
    
    # Target 1: Import system fixed
    if (import_results.get('imports_successful') and 
        import_results.get('objects_created') and 
        import_results.get('collector_functional')):
        print("  ✅ Import system fixed")
        targets_met += 1
    else:
        print("  ❌ Import system still broken")
    
    # Target 2: Data pipeline working
    if (pipeline_results.get('pipeline_setup') and 
        pipeline_results.get('metrics_collected') and 
        pipeline_results.get('data_pipeline_working')):
        print("  ✅ Data pipeline working")
        targets_met += 1
    else:
        print("  ❌ Data pipeline issues")
    
    # Target 3: TSDB integration ready
    if (tsdb_results.get('metrics_generated') and 
        tsdb_results.get('tsdb_format_valid') and 
        tsdb_results.get('integration_ready')):
        print("  ✅ TSDB integration ready")
        targets_met += 1
    else:
        print("  ❌ TSDB integration not ready")
    
    print(f"\n🎯 Overall Score: {targets_met}/{total_targets} targets met")
    
    if targets_met >= 2:
        print("✅ PROFILING INTEGRATION SUCCESSFULLY FIXED")
        print("🔧 Critical integration issues resolved")
        
        print(f"\n🏆 Key Achievements:")
        if import_results:
            print(f"  • Import system: {import_results.get('imports_successful', False)}")
            print(f"  • Object creation: {import_results.get('objects_created', False)}")
        if pipeline_results:
            print(f"  • Data pipeline: {pipeline_results.get('data_pipeline_working', False)}")
            print(f"  • Call graph: {pipeline_results.get('call_graph_generated', False)}")
            print(f"  • Bottlenecks: {pipeline_results.get('bottlenecks_detected', False)}")
        if tsdb_results:
            print(f"  • TSDB format: {tsdb_results.get('tsdb_format_valid', False)}")
            print(f"  • Integration ready: {tsdb_results.get('integration_ready', False)}")
        print(f"  • Profiling data can now flow to TSDB")
        print(f"  • Task 5.2 integration requirements met")
        
        return 0
    else:
        print("⚠️  Profiling integration still needs work")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
