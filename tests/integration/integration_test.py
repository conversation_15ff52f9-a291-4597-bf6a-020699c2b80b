#!/usr/bin/env python3
"""
Integration Test for Week 2 Consolidation
==========================================

Test that all consolidated modules work together properly:
- CLI integration with unified analysis engine
- Visualization system displaying analysis results
- Backward compatibility verification
"""

import sys
import time
import json
from pathlib import Path
from typing import Dict, Any


def test_cli_analysis_integration():
    """Test CLI integration with unified analysis engine"""
    print("🧪 Testing CLI + Analysis Integration")
    print("=" * 40)
    
    try:
        # Test if we can import the CLI components
        from vibe_check.cli.main import main as cli_main
        from vibe_check.cli.unified_formatters import get_formatter
        print("  ✅ CLI imports successful")
        
        # Test formatter functionality
        formatter = get_formatter('json')
        test_data = {'test': 'data', 'value': 42}
        formatted = formatter.format(test_data)
        
        if len(formatted) > 10 and 'test' in formatted:
            print("  ✅ Unified formatters working")
        else:
            print("  ❌ Unified formatters failed")
            return False
        
        return True
        
    except ImportError as e:
        print(f"  ❌ CLI import failed: {e}")
        return False
    except Exception as e:
        print(f"  ❌ CLI integration error: {e}")
        return False


def test_analysis_visualization_integration():
    """Test analysis engine with visualization system"""
    print("\n🧪 Testing Analysis + Visualization Integration")
    print("=" * 50)
    
    try:
        # Import the simple test implementations
        from test_unified_analyzer_simple import SimpleUnifiedAnalyzer, SimpleAnalysisResult, SimpleProjectMetrics, SimpleFileMetrics
        from test_visualization_simple import SimpleDashboardEngine, ChartType
        
        print("  ✅ Imports successful")
        
        # Run analysis
        analyzer = SimpleUnifiedAnalyzer(max_workers=2, use_async=False)
        
        # Analyze a small subset
        test_path = Path("vibe_check/core")
        if not test_path.exists():
            test_path = Path("vibe_check")
        
        print(f"  🔍 Analyzing: {test_path}")
        
        # Use asyncio for the async method
        import asyncio
        result = asyncio.run(analyzer.analyze_project(test_path))
        
        print(f"  ✅ Analysis completed: {result.files_analyzed} files")
        
        # Create visualization dashboard
        dashboard_engine = SimpleDashboardEngine()
        dashboard = dashboard_engine.create_dashboard(
            "integration_test",
            "Integration Test Dashboard",
            "Testing analysis + visualization integration"
        )
        
        # Add panels based on analysis results
        dashboard_engine.add_metric_panel(
            "integration_test", "total_files", "Total Files",
            {'width': 200, 'height': 150}, "total_files"
        )
        
        dashboard_engine.add_metric_panel(
            "integration_test", "avg_complexity", "Average Complexity",
            {'width': 200, 'height': 150}, "avg_complexity"
        )
        
        dashboard_engine.add_chart_panel(
            "integration_test", "complexity_chart", "Complexity Distribution",
            ChartType.BAR, {'width': 400, 'height': 300}
        )
        
        # Prepare data from analysis results
        dashboard_data = {
            'total_files': {'value': result.files_analyzed},
            'avg_complexity': {'value': round(result.project_metrics.average_complexity, 1)},
            'complexity_chart': {
                'x': ['Low', 'Medium', 'High', 'Very High'],
                'y': [
                    len([fm for fm in result.file_metrics if fm.complexity < 10]),
                    len([fm for fm in result.file_metrics if 10 <= fm.complexity < 20]),
                    len([fm for fm in result.file_metrics if 20 <= fm.complexity < 40]),
                    len([fm for fm in result.file_metrics if fm.complexity >= 40])
                ]
            }
        }
        
        # Render dashboard
        html = dashboard_engine.render_dashboard_html("integration_test", dashboard_data)
        
        # Save integration test dashboard
        integration_file = Path("integration_test_dashboard.html")
        with open(integration_file, 'w', encoding='utf-8') as f:
            f.write(html)
        
        print(f"  ✅ Dashboard created with analysis data")
        print(f"  ✅ Integration dashboard saved: {integration_file}")
        print(f"  📊 Dashboard shows {result.files_analyzed} files with {result.project_metrics.average_complexity:.1f} avg complexity")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_backward_compatibility():
    """Test backward compatibility claims"""
    print("\n🧪 Testing Backward Compatibility")
    print("=" * 35)
    
    compatibility_issues = []
    
    # Test 1: Check if old import paths still work (with fallbacks)
    try:
        # These should work or have compatibility layers
        from vibe_check.cli import main
        print("  ✅ CLI main import works")
    except ImportError as e:
        compatibility_issues.append(f"CLI main import failed: {e}")
        print(f"  ⚠️  CLI main import issue: {e}")
    
    # Test 2: Check if core analysis functions are accessible
    try:
        from vibe_check.core.unified_analyzer import analyze_project_sync
        print("  ✅ Unified analyzer accessible")
    except ImportError as e:
        compatibility_issues.append(f"Unified analyzer import failed: {e}")
        print(f"  ⚠️  Unified analyzer import issue: {e}")
    
    # Test 3: Check if visualization components are accessible
    try:
        from vibe_check.core.visualization import UnifiedChartEngine
        print("  ✅ Visualization components accessible")
    except ImportError as e:
        compatibility_issues.append(f"Visualization import failed: {e}")
        print(f"  ⚠️  Visualization import issue: {e}")
    
    # Test 4: Check if old CLI commands still work
    try:
        # Test if we can access CLI commands
        from vibe_check.cli.commands import analyze_command
        print("  ✅ CLI commands accessible")
    except ImportError as e:
        compatibility_issues.append(f"CLI commands import failed: {e}")
        print(f"  ⚠️  CLI commands import issue: {e}")
    except AttributeError as e:
        compatibility_issues.append(f"CLI commands not found: {e}")
        print(f"  ⚠️  CLI commands not found: {e}")
    
    if compatibility_issues:
        print(f"\n  ⚠️  Compatibility Issues Found: {len(compatibility_issues)}")
        for issue in compatibility_issues:
            print(f"    • {issue}")
        return False
    else:
        print(f"  ✅ All backward compatibility tests passed")
        return True


def test_file_consolidation_verification():
    """Verify that file consolidation was actually performed"""
    print("\n🧪 Testing File Consolidation Verification")
    print("=" * 45)
    
    # Check CLI consolidation
    cli_main_files = len(list(Path("vibe_check/cli").glob("*.py")))
    cli_ui_files = len(list(Path("vibe_check/ui/cli").glob("*.py"))) if Path("vibe_check/ui/cli").exists() else 0
    
    print(f"  📁 CLI main files: {cli_main_files}")
    print(f"  📁 CLI UI files: {cli_ui_files}")
    
    # Check if unified formatters exists
    unified_formatters_exists = Path("vibe_check/cli/unified_formatters.py").exists()
    print(f"  📄 Unified formatters created: {unified_formatters_exists}")
    
    # Check visualization consolidation
    core_viz_files = len(list(Path("vibe_check/core/visualization").glob("*.py"))) if Path("vibe_check/core/visualization").exists() else 0
    ai_viz_files = len(list(Path("vibe_check/ai/visualization").glob("*.py"))) if Path("vibe_check/ai/visualization").exists() else 0
    ui_viz_files = len(list(Path("vibe_check/ui/visualization").glob("*.py"))) if Path("vibe_check/ui/visualization").exists() else 0
    
    print(f"  📁 Core visualization files: {core_viz_files}")
    print(f"  📁 AI visualization files: {ai_viz_files}")
    print(f"  📁 UI visualization files: {ui_viz_files}")
    
    # Check unified analyzer
    unified_analyzer_exists = Path("vibe_check/core/unified_analyzer.py").exists()
    print(f"  📄 Unified analyzer created: {unified_analyzer_exists}")
    
    # Consolidation assessment
    consolidation_score = 0
    total_checks = 4
    
    if unified_formatters_exists:
        consolidation_score += 1
        print("  ✅ CLI consolidation evidence found")
    else:
        print("  ❌ CLI consolidation evidence missing")
    
    if unified_analyzer_exists:
        consolidation_score += 1
        print("  ✅ Analysis engine consolidation evidence found")
    else:
        print("  ❌ Analysis engine consolidation evidence missing")
    
    if core_viz_files > 0:
        consolidation_score += 1
        print("  ✅ Visualization consolidation evidence found")
    else:
        print("  ❌ Visualization consolidation evidence missing")
    
    if cli_ui_files <= 2:  # Should be mostly empty after consolidation
        consolidation_score += 1
        print("  ✅ UI/CLI directory properly consolidated")
    else:
        print("  ❌ UI/CLI directory still has many files")
    
    print(f"\n  📊 Consolidation Score: {consolidation_score}/{total_checks}")
    
    return consolidation_score >= 3


def test_performance_regression():
    """Test for performance regressions"""
    print("\n🧪 Testing Performance Regression")
    print("=" * 35)
    
    try:
        # Test baseline performance
        from test_unified_analyzer_simple import SimpleUnifiedAnalyzer
        
        analyzer = SimpleUnifiedAnalyzer(max_workers=1, use_async=False)
        
        # Small performance test
        test_path = Path("vibe_check/core")
        if not test_path.exists():
            test_path = Path(".")
        
        start_time = time.time()
        
        import asyncio
        result = asyncio.run(analyzer.analyze_project(test_path))
        
        analysis_time = time.time() - start_time
        files_per_second = result.files_analyzed / analysis_time if analysis_time > 0 else 0
        
        print(f"  📊 Performance Test Results:")
        print(f"    • Files analyzed: {result.files_analyzed}")
        print(f"    • Analysis time: {analysis_time:.3f}s")
        print(f"    • Speed: {files_per_second:.1f} files/sec")
        
        # Performance targets
        min_speed = 100  # files/sec minimum
        max_time = 5.0   # seconds maximum for reasonable dataset
        
        performance_ok = files_per_second >= min_speed and analysis_time <= max_time
        
        if performance_ok:
            print(f"  ✅ Performance targets met")
            return True
        else:
            print(f"  ⚠️  Performance targets missed")
            if files_per_second < min_speed:
                print(f"    • Speed too low: {files_per_second:.1f} < {min_speed}")
            if analysis_time > max_time:
                print(f"    • Time too high: {analysis_time:.3f} > {max_time}")
            return False
        
    except Exception as e:
        print(f"  ❌ Performance test failed: {e}")
        return False


def main():
    """Main integration test function"""
    print("🚀 Week 2 Consolidation Integration Test")
    print("=" * 50)
    
    test_results = {}
    
    # Run all integration tests
    test_results['cli_integration'] = test_cli_analysis_integration()
    test_results['analysis_visualization'] = test_analysis_visualization_integration()
    test_results['backward_compatibility'] = test_backward_compatibility()
    test_results['file_consolidation'] = test_file_consolidation_verification()
    test_results['performance_regression'] = test_performance_regression()
    
    # Calculate overall score
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    
    print("\n" + "=" * 50)
    print("📊 INTEGRATION TEST SUMMARY")
    print("=" * 50)
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name.replace('_', ' ').title()}: {status}")
    
    print(f"\n🎯 Overall Score: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests >= 4:
        print("✅ Integration tests PASSED - Week 2 consolidation is successful")
        print("🚀 Ready to proceed with Week 3: Async Implementation & Caching")
        return 0
    else:
        print("❌ Integration tests FAILED - Issues need to be addressed")
        print("⚠️  Week 2 consolidation needs fixes before proceeding")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
