#!/usr/bin/env python3
"""
Code Quality Visualization Test
===============================

Test code quality visualization components including heatmaps, dependency graphs, and technical debt.
"""

import random
from pathlib import Path
import sys

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

def print_header(title: str, level: int = 1) -> None:
    """Print formatted section header"""
    symbols = ["🚀", "🔧", "📊", "✅", "⚡"]
    symbol = symbols[min(level-1, len(symbols)-1)]
    separator = "=" * 60 if level == 1 else "-" * 50
    print(f"\n{symbol} {title}")
    print(separator)

def print_result(test_name: str, success: bool, details: str = "") -> None:
    """Print test result with formatting"""
    status = "✅" if success else "❌"
    print(f"  {status} {test_name}")
    if details:
        for line in details.split('\n'):
            if line.strip():
                print(f"    • {line.strip()}")

def generate_test_quality_data():
    """Generate test code quality data"""
    from vibe_check.monitoring.visualization.charts import CodeQualityData, QualityMetric
    
    test_files = [
        "src/main.py", "src/utils.py", "src/models/user.py", "src/models/product.py",
        "src/api/routes.py", "src/api/auth.py", "tests/test_main.py", "tests/test_utils.py",
        "config/settings.py", "scripts/deploy.py"
    ]
    
    quality_data = []
    
    for file_path in test_files:
        # Generate realistic quality metrics
        complexity = random.uniform(1, 50)
        maintainability = random.uniform(20, 100)
        coverage = random.uniform(0, 100)
        debt_minutes = random.uniform(0, 120)
        
        data = CodeQualityData(
            file_path=file_path,
            metrics={
                QualityMetric.COMPLEXITY: complexity,
                QualityMetric.MAINTAINABILITY: maintainability,
                QualityMetric.COVERAGE: coverage
            },
            lines_of_code=random.randint(50, 500),
            complexity_score=complexity,
            maintainability_index=maintainability,
            test_coverage=coverage,
            technical_debt_minutes=debt_minutes,
            language="python"
        )
        
        quality_data.append(data)
    
    return quality_data

def generate_test_dependencies():
    """Generate test dependency relationships"""
    from vibe_check.monitoring.visualization.charts import DependencyRelation
    
    dependencies = [
        DependencyRelation("src/main.py", "src/utils.py", "import", 0.8),
        DependencyRelation("src/main.py", "src/api/routes.py", "import", 0.9),
        DependencyRelation("src/api/routes.py", "src/models/user.py", "import", 0.7),
        DependencyRelation("src/api/routes.py", "src/models/product.py", "import", 0.6),
        DependencyRelation("src/api/auth.py", "src/models/user.py", "import", 0.8),
        DependencyRelation("src/models/user.py", "src/utils.py", "import", 0.5),
        DependencyRelation("tests/test_main.py", "src/main.py", "import", 0.9),
        DependencyRelation("tests/test_utils.py", "src/utils.py", "import", 0.9),
        # Circular dependency
        DependencyRelation("src/utils.py", "src/models/user.py", "import", 0.3, True)
    ]
    
    return dependencies

def generate_test_debt_items():
    """Generate test technical debt items"""
    from vibe_check.monitoring.visualization.charts import TechnicalDebtItem, SeverityLevel
    
    debt_items = [
        TechnicalDebtItem("src/main.py", 45, "code_smell", SeverityLevel.MEDIUM, "Complex function needs refactoring", 30),
        TechnicalDebtItem("src/utils.py", 12, "duplication", SeverityLevel.HIGH, "Duplicated code block", 45),
        TechnicalDebtItem("src/api/routes.py", 78, "bug", SeverityLevel.CRITICAL, "Potential null pointer exception", 60),
        TechnicalDebtItem("src/models/user.py", 23, "vulnerability", SeverityLevel.HIGH, "SQL injection risk", 90),
        TechnicalDebtItem("src/api/auth.py", 56, "code_smell", SeverityLevel.LOW, "Long parameter list", 15),
        TechnicalDebtItem("config/settings.py", 8, "code_smell", SeverityLevel.MEDIUM, "Magic numbers", 20)
    ]
    
    return debt_items

def test_code_quality_processor():
    """Test code quality data processing"""
    print_header("Code Quality Processor Test", 2)
    
    try:
        from vibe_check.monitoring.visualization.charts import CodeQualityProcessor
        
        # Generate test data
        quality_data = generate_test_quality_data()
        
        # Test quality score calculation
        scores = []
        categories = []
        
        for data in quality_data:
            score = CodeQualityProcessor.calculate_quality_score(data)
            category = CodeQualityProcessor.categorize_file_by_quality(score)
            scores.append(score)
            categories.append(category)
        
        # Test technical debt ratio
        debt_ratio = CodeQualityProcessor.calculate_technical_debt_ratio(quality_data)
        
        # Test hotspot identification
        hotspots = CodeQualityProcessor.identify_hotspots(quality_data, threshold=30.0)
        
        success = (
            len(scores) == len(quality_data) and
            all(0 <= score <= 100 for score in scores) and
            len(set(categories)) > 1 and  # Should have multiple categories
            debt_ratio >= 0 and
            len(hotspots) >= 0  # May or may not have hotspots
        )
        
        details = f"""Processing results:
Files processed: {len(quality_data)}
Quality scores: {len(scores)} calculated
Score range: {min(scores):.1f} - {max(scores):.1f}
Categories found: {set(categories)}
Technical debt ratio: {debt_ratio:.2f} minutes/1000 LOC
Hotspots identified: {len(hotspots)}

Quality distribution:"""
        
        category_counts = {}
        for cat in categories:
            category_counts[cat] = category_counts.get(cat, 0) + 1
        
        for cat, count in category_counts.items():
            details += f"\n- {cat}: {count} files"
        
        print_result("Code Quality Processor", success, details)
        return success
        
    except Exception as e:
        print_result("Code Quality Processor", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_heatmap_generator():
    """Test heatmap generation"""
    print_header("Heatmap Generator Test", 2)
    
    try:
        from vibe_check.monitoring.visualization.charts import (
            HeatmapGenerator, QualityVisualizationConfig, VisualizationType, QualityMetric
        )
        
        # Generate test data
        quality_data = generate_test_quality_data()
        
        # Create configuration
        config = QualityVisualizationConfig(
            visualization_type=VisualizationType.HEATMAP,
            title="Test Quality Heatmap",
            width=800,
            height=600,
            color_scheme="viridis"
        )
        
        # Generate file heatmap
        heatmap_data = HeatmapGenerator.generate_file_heatmap(
            quality_data, QualityMetric.COMPLEXITY, config
        )
        
        # Generate directory treemap
        treemap_data = HeatmapGenerator.generate_directory_treemap(quality_data, config)
        
        success = (
            heatmap_data["type"] == "heatmap" and
            len(heatmap_data["data"]) == len(quality_data) and
            treemap_data["type"] == "treemap" and
            "children" in treemap_data["data"] and
            len(treemap_data["data"]["children"]) > 0
        )
        
        details = f"""Heatmap generation:
File heatmap:
- Type: {heatmap_data['type']}
- Data points: {len(heatmap_data['data'])}
- Title: {heatmap_data['title']}
- Color scheme: {heatmap_data['colorScheme']}

Directory treemap:
- Type: {treemap_data['type']}
- Root children: {len(treemap_data['data']['children'])}
- Title: {treemap_data['title']}

Features validated:
- Grid layout calculation: ✓
- Quality categorization: ✓
- Metadata inclusion: ✓
- Directory grouping: ✓"""
        
        print_result("Heatmap Generator", success, details)
        return success
        
    except Exception as e:
        print_result("Heatmap Generator", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_dependency_graph_generator():
    """Test dependency graph generation"""
    print_header("Dependency Graph Generator Test", 2)
    
    try:
        from vibe_check.monitoring.visualization.charts import (
            DependencyGraphGenerator, QualityVisualizationConfig, VisualizationType
        )
        
        # Generate test data
        quality_data = generate_test_quality_data()
        dependencies = generate_test_dependencies()
        
        # Create configuration
        config = QualityVisualizationConfig(
            visualization_type=VisualizationType.DEPENDENCY_GRAPH,
            title="Test Dependency Graph",
            hierarchical_layout=True,
            cluster_related=True
        )
        
        # Generate dependency graph
        graph_data = DependencyGraphGenerator.generate_dependency_graph(
            dependencies, quality_data, config
        )
        
        # Count circular dependencies
        circular_deps = sum(1 for dep in dependencies if dep.is_circular)
        circular_edges = sum(1 for edge in graph_data["edges"] if edge["isCircular"])
        
        success = (
            graph_data["type"] == "dependency_graph" and
            len(graph_data["nodes"]) > 0 and
            len(graph_data["edges"]) == len(dependencies) and
            circular_edges == circular_deps and
            all("quality" in node for node in graph_data["nodes"])
        )
        
        details = f"""Dependency graph:
Type: {graph_data['type']}
Nodes: {len(graph_data['nodes'])}
Edges: {len(graph_data['edges'])}
Circular dependencies: {circular_edges}
Hierarchical layout: {graph_data['config']['hierarchicalLayout']}
Cluster related: {graph_data['config']['clusterRelated']}

Node features:
- Quality scoring: ✓
- Size based on quality: ✓
- Color coding: ✓
- Category assignment: ✓

Edge features:
- Strength visualization: ✓
- Circular dependency marking: ✓
- Type classification: ✓"""
        
        print_result("Dependency Graph Generator", success, details)
        return success
        
    except Exception as e:
        print_result("Dependency Graph Generator", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_technical_debt_visualizer():
    """Test technical debt visualization"""
    print_header("Technical Debt Visualizer Test", 2)
    
    try:
        from vibe_check.monitoring.visualization.charts import (
            TechnicalDebtVisualizer, QualityVisualizationConfig, VisualizationType
        )
        
        # Generate test data
        debt_items = generate_test_debt_items()
        
        # Create configuration
        config = QualityVisualizationConfig(
            visualization_type=VisualizationType.HEATMAP,
            title="Test Technical Debt Overview"
        )
        
        # Generate debt overview
        debt_data = TechnicalDebtVisualizer.generate_debt_overview(debt_items, config)
        
        # Validate summary
        summary = debt_data["summary"]
        total_effort = sum(item.effort_minutes for item in debt_items)
        
        success = (
            debt_data["type"] == "debt_overview" and
            summary["totalItems"] == len(debt_items) and
            summary["totalEffort"] == total_effort and
            len(debt_data["files"]) > 0 and
            all("severityCounts" in file_info for file_info in debt_data["files"])
        )
        
        details = f"""Technical debt overview:
Type: {debt_data['type']}
Total items: {summary['totalItems']}
Total effort: {summary['totalEffort']:.1f} minutes
Average effort per item: {summary['averageEffortPerItem']:.1f} minutes
Files with debt: {len(debt_data['files'])}

Severity breakdown:"""
        
        for severity, count in summary["severityBreakdown"].items():
            details += f"\n- {severity}: {count} items"
        
        details += f"\n\nFeatures validated:"
        details += f"\n- File grouping: ✓"
        details += f"\n- Effort calculation: ✓"
        details += f"\n- Severity analysis: ✓"
        details += f"\n- Debt density calculation: ✓"
        
        print_result("Technical Debt Visualizer", success, details)
        return success
        
    except Exception as e:
        print_result("Technical Debt Visualizer", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_code_quality_dashboard():
    """Test integrated code quality dashboard"""
    print_header("Code Quality Dashboard Test", 2)
    
    try:
        from vibe_check.monitoring.visualization.charts import (
            CodeQualityDashboard, QualityVisualizationConfig, VisualizationType
        )
        
        # Create dashboard
        config = QualityVisualizationConfig(
            visualization_type=VisualizationType.HEATMAP,
            title="Test Dashboard"
        )
        dashboard = CodeQualityDashboard(config)
        
        # Add test data
        quality_data = generate_test_quality_data()
        dependencies = generate_test_dependencies()
        debt_items = generate_test_debt_items()
        
        for data in quality_data:
            dashboard.add_quality_data(data)
        
        for dep in dependencies:
            dashboard.add_dependency(dep)
        
        for debt in debt_items:
            dashboard.add_debt_item(debt)
        
        # Generate dashboard data
        dashboard_data = dashboard.generate_dashboard_data()
        
        # Export data
        json_export = dashboard.export_data("json")
        
        # Get statistics
        stats = dashboard.get_statistics()
        
        success = (
            "summary" in dashboard_data and
            "visualizations" in dashboard_data and
            len(dashboard_data["visualizations"]) >= 3 and  # heatmap, treemap, dependency_graph, technical_debt
            "heatmap" in dashboard_data["visualizations"] and
            "dependency_graph" in dashboard_data["visualizations"] and
            "technical_debt" in dashboard_data["visualizations"] and
            len(json_export) > 1000 and  # Substantial export
            stats["quality_data_count"] == len(quality_data)
        )
        
        summary = dashboard_data["summary"]
        
        details = f"""Dashboard integration:
Total files: {summary['totalFiles']}
Total dependencies: {summary['totalDependencies']}
Total debt items: {summary['totalDebtItems']}
Average quality: {summary['averageQuality']:.1f}
Technical debt ratio: {summary['technicalDebtRatio']:.2f}
Hotspots identified: {len(summary['hotspots'])}

Visualizations generated:
- Heatmap: {'✓' if 'heatmap' in dashboard_data['visualizations'] else '✗'}
- Treemap: {'✓' if 'treemap' in dashboard_data['visualizations'] else '✗'}
- Dependency graph: {'✓' if 'dependency_graph' in dashboard_data['visualizations'] else '✗'}
- Technical debt: {'✓' if 'technical_debt' in dashboard_data['visualizations'] else '✗'}

Export size: {len(json_export)} characters
Statistics: {stats['quality_data_count']} quality data points"""
        
        print_result("Code Quality Dashboard", success, details)
        return success
        
    except Exception as e:
        print_result("Code Quality Dashboard", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run code quality visualization tests"""
    print_header("Code Quality Visualization Test", 1)
    print("Testing code quality visualization components including heatmaps, dependency graphs, and technical debt")
    print("Validating quality processing, visualization generation, and dashboard integration")
    
    # Track test results
    test_results = {}
    
    # Run tests
    test_results['processor'] = test_code_quality_processor()
    test_results['heatmap'] = test_heatmap_generator()
    test_results['dependency_graph'] = test_dependency_graph_generator()
    test_results['technical_debt'] = test_technical_debt_visualizer()
    test_results['dashboard'] = test_code_quality_dashboard()
    
    # Summary
    print_header("Code Quality Visualization Test Summary", 1)
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    success_rate = (passed_tests / total_tests) * 100
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name.upper()} test")
    
    print(f"\n📊 Overall Results:")
    print(f"  • Tests passed: {passed_tests}/{total_tests}")
    print(f"  • Success rate: {success_rate:.1f}%")
    
    if success_rate >= 75:
        print(f"  ✅ Code quality visualization system SUCCESSFUL")
        print(f"  🚀 Ready for infrastructure topology development")
    else:
        print(f"  ❌ Code quality visualization system FAILED")
        print(f"  🔧 Remediation required before proceeding")
    
    return success_rate >= 75

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
