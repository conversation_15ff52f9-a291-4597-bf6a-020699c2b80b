"""
Integration tests for the analyze_project function.
"""

import pytest
import os
import tempfile
import shutil
import yaml
from pathlib import Path

from vibe_check import analyze_project


@pytest.fixture
def test_project():
    """Create a temporary test project."""
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()

    try:
        # Create a simple Python file
        with open(os.path.join(temp_dir, "main.py"), "w") as f:
            f.write("""
def hello_world():
    print("Hello, world!")

if __name__ == "__main__":
    hello_world()
""")

        # Create a file with issues
        with open(os.path.join(temp_dir, "issues.py"), "w") as f:
            f.write("""
import sys, os  # Multiple imports on one line

def unused_arg(x, y):
    # Unused argument
    return x

def complex_function(a, b, c, d, e):
    # Complex function with high cyclomatic complexity
    if a > 0:
        if b > 0:
            if c > 0:
                if d > 0:
                    if e > 0:
                        return a + b + c + d + e
                    else:
                        return a + b + c + d
                else:
                    return a + b + c
            else:
                return a + b
        else:
            return a
    else:
        return 0

# Unused import
import json
""")

        # Create a directory with a Python file
        os.makedirs(os.path.join(temp_dir, "utils"))
        with open(os.path.join(temp_dir, "utils", "helpers.py"), "w") as f:
            f.write("""
def add(a, b):
    return a + b

def subtract(a, b):
    return a - b
""")

        yield temp_dir
    finally:
        # Clean up the temporary directory
        shutil.rmtree(temp_dir)


class TestAnalyzeProject:
    """Integration tests for analyze_project function."""

    def test_analyze_project_basic(self, test_project):
        """Test basic project analysis."""
        # Run the analysis
        metrics = analyze_project(
            project_path=test_project
        )

        # Verify that metrics is a ProjectMetrics object
        from vibe_check.core.models.project_metrics import ProjectMetrics
        assert isinstance(metrics, ProjectMetrics)

        # Use metrics directly
        project_metrics = metrics

        # Verify project metrics
        assert project_metrics.project_path == test_project
        assert project_metrics.total_file_count == 3
        assert project_metrics.total_line_count > 0

        # Verify file metrics - we should have at least one file
        assert len(project_metrics.file_metrics) > 0

        # The test project has a main.py file
        assert any("main.py" in Path(p).name for p in project_metrics.file_metrics.keys())

        # No need to verify issues since we're just testing basic functionality

    def test_analyze_project_with_config(self, test_project):
        """Test project analysis with custom configuration."""
        # Create a temporary config file
        config_dir = tempfile.mkdtemp()
        config_path = os.path.join(config_dir, "test_config.yaml")

        try:
            # Write the config to a file
            with open(config_path, "w") as f:
                yaml.dump({
                    "tools": {
                        "ruff": {
                            "enabled": True,
                            "args": ["--select=E,F,W,I"]
                        },
                        "mypy": {
                            "enabled": False
                        },
                        "bandit": {
                            "enabled": False
                        },
                        "complexity": {
                            "enabled": True,
                            "threshold": 5
                        }
                    }
                }, f)

            # Load the config and run the analysis
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)

            metrics = analyze_project(
                project_path=test_project,
                config=config
            )
        finally:
            # Clean up the temporary directory
            shutil.rmtree(config_dir)

        # Verify that metrics is a ProjectMetrics object
        from vibe_check.core.models.project_metrics import ProjectMetrics
        assert isinstance(metrics, ProjectMetrics)

        # Use metrics directly
        project_metrics = metrics

        # Verify file metrics - we should have at least one file
        assert len(project_metrics.file_metrics) > 0

        # The test project has a main.py file
        assert any("main.py" in Path(p).name for p in project_metrics.file_metrics.keys())

    def test_analyze_project_with_output_dir(self, test_project):
        """Test project analysis with custom output directory."""
        # Create a temporary output directory
        output_dir = tempfile.mkdtemp()

        try:
            # Run the analysis with custom output directory
            metrics = analyze_project(
                project_path=test_project,
                output_dir=output_dir
            )

            # Verify that metrics is a ProjectMetrics object
            from vibe_check.core.models.project_metrics import ProjectMetrics
            assert isinstance(metrics, ProjectMetrics)

            # Use metrics directly
            project_metrics = metrics

            # Verify file metrics - we should have at least one file
            assert len(project_metrics.file_metrics) > 0

            # The test project has a main.py file
            assert any("main.py" in Path(p).name for p in project_metrics.file_metrics.keys())
        finally:
            # Clean up the temporary directory
            shutil.rmtree(output_dir)
