"""
Integration tests for CLI workflows.
"""

import pytest
import tempfile
import os
from pathlib import Path
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from unittest.mock import patch, MagicMock

from vibe_check.cli.main import cli


class TestCLIWorkflows:
    """Integration tests for complete CLI workflows."""

    def test_analyze_command_end_to_end_simple(self) -> None:
        """Test complete analyze command workflow with simple analyzer."""
        runner = CliRunner()
        
        with runner.isolated_filesystem():
            # Create a simple Python project
            os.makedirs("src", exist_ok=True)
            
            # Create main.py
            with open("src/main.py", "w") as f:
                f.write("""
def hello_world():
    '''Simple hello world function.'''
    print("Hello, World!")

if __name__ == "__main__":
    hello_world()
""")
            
            # Create utils.py
            with open("src/utils.py", "w") as f:
                f.write("""
def add_numbers(a, b):
    '''Add two numbers together.'''
    return a + b

def multiply_numbers(a, b):
    '''Multiply two numbers together.'''
    return a * b
""")
            
            # Create output directory
            os.makedirs("vibe_check_output", exist_ok=True)
            
            # Mock external tool availability to avoid dependencies
            with patch('vibe_check.tools.runners.tool_registry.check_command_availability') as mock_check:
                mock_check.return_value = False  # No external tools available
                
                # Run the analyze command
                result = runner.invoke(cli, [
                    "analyze",
                    ".",
                    "--output", "vibe_check_output",
                    "--profile", "minimal",  # Use minimal profile for faster execution
                    "--no-semantic"  # Disable semantic analysis to avoid complex dependencies
                ])
                
                # Check that command completed successfully
                assert result.exit_code == 0
                
                # Check that output files were created
                output_dir = Path("vibe_check_output")
                assert output_dir.exists()
                
                # Check for expected output files
                expected_files = [
                    "project_analysis.json",
                    "project_analysis.txt"
                ]
                
                for expected_file in expected_files:
                    file_path = output_dir / expected_file
                    if file_path.exists():
                        assert file_path.stat().st_size > 0  # File should not be empty

    def test_analyze_command_with_configuration(self) -> None:
        """Test analyze command with custom configuration."""
        runner = CliRunner()
        
        with runner.isolated_filesystem():
            # Create a Python project
            os.makedirs("src", exist_ok=True)
            
            with open("src/test.py", "w") as f:
                f.write("""
import os
import sys

def long_function_name_that_exceeds_normal_length():
    very_long_variable_name_that_exceeds_normal_length = "test"
    return very_long_variable_name_that_exceeds_normal_length
""")
            
            # Create configuration file
            config_content = """
{
    "tools": {
        "ruff": {"enabled": false},
        "mypy": {"enabled": false},
        "bandit": {"enabled": false}
    },
    "output": {
        "formats": ["json", "txt"]
    }
}
"""
            with open("vibe_check_config.json", "w") as f:
                f.write(config_content)
            
            os.makedirs("output", exist_ok=True)
            
            # Mock external dependencies
            with patch('vibe_check.tools.runners.tool_registry.check_command_availability') as mock_check:
                mock_check.return_value = False
                
                result = runner.invoke(cli, [
                    "analyze",
                    ".",
                    "--config", "vibe_check_config.json",
                    "--output", "output",
                    "--profile", "minimal",
                    "--no-semantic"
                ])
                
                # Should complete successfully even with custom config
                assert result.exit_code == 0

    def test_analyze_command_graceful_degradation(self) -> None:
        """Test that analyze command gracefully handles missing external tools."""
        runner = CliRunner()
        
        with runner.isolated_filesystem():
            # Create a simple Python file
            with open("test.py", "w") as f:
                f.write("print('hello world')")
            
            os.makedirs("output", exist_ok=True)
            
            # Mock all external tools as unavailable
            with patch('vibe_check.tools.runners.tool_registry.check_command_availability') as mock_check:
                mock_check.return_value = False
                
                result = runner.invoke(cli, [
                    "analyze",
                    ".",
                    "--output", "output",
                    "--profile", "minimal",
                    "--no-semantic"
                ])
                
                # Should still complete successfully
                assert result.exit_code == 0
                
                # Should have created some output
                output_dir = Path("output")
                assert output_dir.exists()

    def test_analyze_command_with_verbose_output(self) -> None:
        """Test analyze command with verbose output."""
        runner = CliRunner()
        
        with runner.isolated_filesystem():
            with open("simple.py", "w") as f:
                f.write("def test(): pass")
            
            os.makedirs("output", exist_ok=True)
            
            with patch('vibe_check.tools.runners.tool_registry.check_command_availability') as mock_check:
                mock_check.return_value = False
                
                result = runner.invoke(cli, [
                    "analyze",
                    ".",
                    "--output", "output",
                    "--verbose",
                    "--profile", "minimal",
                    "--no-semantic"
                ])
                
                assert result.exit_code == 0
                # Verbose mode should produce more output
                assert len(result.output) > 0

    def test_analyze_command_with_security_focus(self) -> None:
        """Test analyze command with security-focused analysis."""
        runner = CliRunner()
        
        with runner.isolated_filesystem():
            # Create a file with potential security issues
            with open("security_test.py", "w") as f:
                f.write("""
import subprocess
import os

def risky_function():
    # This would normally trigger security warnings
    subprocess.call("ls", shell=True)
    eval("print('hello')")
    exec("x = 1")
""")
            
            os.makedirs("output", exist_ok=True)
            
            with patch('vibe_check.tools.runners.tool_registry.check_command_availability') as mock_check:
                mock_check.return_value = False
                
                result = runner.invoke(cli, [
                    "analyze",
                    ".",
                    "--output", "output",
                    "--security-focused",
                    "--profile", "minimal",
                    "--no-semantic"
                ])
                
                assert result.exit_code == 0

    def test_analyze_command_error_handling(self) -> None:
        """Test analyze command error handling for invalid inputs."""
        runner = CliRunner()
        
        # Test with non-existent directory
        result = runner.invoke(cli, [
            "analyze",
            "/non/existent/path",
            "--profile", "minimal",
            "--no-semantic"
        ])
        
        # Should handle error gracefully
        assert result.exit_code != 0

    def test_analyze_command_with_project_structure(self) -> None:
        """Test analyze command with complex project structure."""
        runner = CliRunner()

        with runner.isolated_filesystem():
            # Create project structure
            os.makedirs("src", exist_ok=True)
            os.makedirs("tests", exist_ok=True)

            # Create files
            with open("src/main.py", "w") as f:
                f.write("def main(): pass")

            with open("tests/test_main.py", "w") as f:
                f.write("def test_main(): pass")

            os.makedirs("output", exist_ok=True)

            with patch('vibe_check.tools.runners.tool_registry.check_command_availability') as mock_check:
                mock_check.return_value = False

                result = runner.invoke(cli, [
                    "analyze",
                    ".",
                    "--output", "output",
                    "--profile", "minimal",
                    "--no-semantic"
                ])

                assert result.exit_code == 0

    def test_analyze_command_output_formats(self) -> None:
        """Test analyze command with different output formats."""
        runner = CliRunner()

        with runner.isolated_filesystem():
            with open("test.py", "w") as f:
                f.write("def test(): pass")

            os.makedirs("output", exist_ok=True)

            with patch('vibe_check.tools.runners.tool_registry.check_command_availability') as mock_check:
                mock_check.return_value = False

                # Test basic output
                result = runner.invoke(cli, [
                    "analyze",
                    ".",
                    "--output", "output",
                    "--profile", "minimal",
                    "--no-semantic"
                ])

                assert result.exit_code == 0

    def test_analyze_command_with_preset(self) -> None:
        """Test analyze command with analysis preset."""
        runner = CliRunner()
        
        with runner.isolated_filesystem():
            with open("test.py", "w") as f:
                f.write("def test(): pass")
            
            os.makedirs("output", exist_ok=True)
            
            with patch('vibe_check.tools.runners.tool_registry.check_command_availability') as mock_check:
                mock_check.return_value = False
                
                result = runner.invoke(cli, [
                    "analyze",
                    ".",
                    "--output", "output",
                    "--preset", "quick",
                    "--profile", "minimal",
                    "--no-semantic"
                ])
                
                assert result.exit_code == 0

    def test_analyze_command_project_structure_detection(self) -> None:
        """Test that analyze command properly detects different project structures."""
        runner = CliRunner()
        
        with runner.isolated_filesystem():
            # Create a typical Python package structure
            os.makedirs("mypackage", exist_ok=True)
            
            with open("setup.py", "w") as f:
                f.write("""
from setuptools import setup, find_packages

setup(
    name="mypackage",
    version="0.1.0",
    packages=find_packages(),
)
""")
            
            with open("mypackage/__init__.py", "w") as f:
                f.write("__version__ = '0.1.0'")
            
            with open("mypackage/module.py", "w") as f:
                f.write("""
def function():
    '''A simple function.'''
    return True
""")
            
            with open("requirements.txt", "w") as f:
                f.write("requests>=2.25.0\nnumpy>=1.20.0")
            
            os.makedirs("output", exist_ok=True)
            
            with patch('vibe_check.tools.runners.tool_registry.check_command_availability') as mock_check:
                mock_check.return_value = False
                
                result = runner.invoke(cli, [
                    "analyze",
                    ".",
                    "--output", "output",
                    "--profile", "minimal",
                    "--no-semantic"
                ])
                
                assert result.exit_code == 0
