"""
Integration Tests for Pre-commit System
=======================================

Comprehensive test suite for pre-commit integration including hook generation,
installation, execution modes, and compatibility testing.

Test Coverage:
- Hook generation system
- Install-hooks command functionality
- Pre-commit configuration validation
- Performance target compliance
- Conflict detection and resolution
- Backup and rollback mechanisms
"""

import pytest
import tempfile
import subprocess
import yaml
from pathlib import Path
from unittest.mock import patch, MagicMock

from vibe_check.core.precommit.generator import (
    PreCommitHookGenerator, ValidationLevel, create_sample_config
)
from vibe_check.core.precommit.installer import (
    PreCommitInstaller, InstallMode, InstallResult
)
from vibe_check.core.precommit.analyzer import PreCommitAnalyzer
from vibe_check.core.precommit.cache import IncrementalCacheManager
from vibe_check.cli.commands import install_hooks_command


class TestPreCommitHookGeneration:
    """Test hook generation system functionality."""
    
    def test_hook_generator_initialization(self, tmp_path):
        """Test PreCommitHookGenerator initialization."""
        generator = PreCommitHookGenerator(tmp_path)
        assert generator.project_path == tmp_path
        # Note: hooks_file is not a stored attribute, it's computed in methods
    
    def test_hooks_config_generation(self, tmp_path):
        """Test generation of .pre-commit-hooks.yaml content."""
        generator = PreCommitHookGenerator(tmp_path)
        config = generator.generate_hooks_config()
        
        # Validate YAML structure
        hooks = yaml.safe_load(config)
        assert isinstance(hooks, list)
        assert len(hooks) == 3  # minimal, standard, strict
        
        # Validate hook structure
        for hook in hooks:
            assert 'id' in hook
            assert 'name' in hook
            assert 'description' in hook
            assert 'entry' in hook
            assert 'language' in hook
            assert 'types_or' in hook
            assert hook['language'] == 'python'
    
    def test_project_config_generation(self, tmp_path):
        """Test generation of project-specific pre-commit config."""
        generator = PreCommitHookGenerator(tmp_path)
        
        for level in ValidationLevel:
            config = generator.generate_project_config(
                validation_level=level,
                custom_args=["--fix-safe"],
                repo_rev="v1.0.0"
            )
            
            # Validate YAML structure
            parsed = yaml.safe_load(config)
            assert 'repos' in parsed
            assert len(parsed['repos']) == 1
            
            repo = parsed['repos'][0]
            assert repo['repo'] == "https://github.com/ptzajac/vibe_check"
            assert repo['rev'] == "v1.0.0"
            assert len(repo['hooks']) == 1
            
            hook = repo['hooks'][0]
            assert hook['id'] == f"vibe-check-{level.value}"
            assert hook['args'] == ["--fix-safe"]
    
    def test_validation_rules(self, tmp_path):
        """Test validation rules for different levels."""
        generator = PreCommitHookGenerator(tmp_path)
        
        minimal_rules = generator.get_validation_rules(ValidationLevel.MINIMAL)
        standard_rules = generator.get_validation_rules(ValidationLevel.STANDARD)
        strict_rules = generator.get_validation_rules(ValidationLevel.STRICT)
        
        # Validate rule progression
        assert len(minimal_rules) < len(standard_rules) < len(strict_rules)
        
        # Validate core rules are present
        for rules in [minimal_rules, standard_rules, strict_rules]:
            assert 'syntax_errors' in rules
            assert 'import_errors' in rules
            assert 'security_critical' in rules
    
    def test_performance_estimation(self, tmp_path):
        """Test performance estimation and target validation."""
        generator = PreCommitHookGenerator(tmp_path)
        
        # Test performance targets
        minimal_time = generator.estimate_execution_time(ValidationLevel.MINIMAL, 10)
        standard_time = generator.estimate_execution_time(ValidationLevel.STANDARD, 10)
        strict_time = generator.estimate_execution_time(ValidationLevel.STRICT, 10)
        
        # Validate time progression
        assert minimal_time < standard_time < strict_time
        
        # Validate target compliance
        assert generator.validate_performance_target(ValidationLevel.MINIMAL, minimal_time)
        assert generator.validate_performance_target(ValidationLevel.STANDARD, standard_time)
        assert generator.validate_performance_target(ValidationLevel.STRICT, strict_time)
    
    def test_sample_config_creation(self):
        """Test sample configuration creation."""
        config = create_sample_config()
        
        # Validate structure
        parsed = yaml.safe_load(config)
        assert 'repos' in parsed
        assert len(parsed['repos']) == 1
        
        repo = parsed['repos'][0]
        assert 'vibe_check' in repo['repo']
        assert 'hooks' in repo
        assert len(repo['hooks']) == 1


class TestPreCommitInstaller:
    """Test pre-commit installation system."""
    
    def test_installer_initialization(self, tmp_path):
        """Test PreCommitInstaller initialization."""
        installer = PreCommitInstaller(tmp_path)
        assert installer.project_path == tmp_path
        assert installer.config_path == tmp_path / ".pre-commit-config.yaml"
    
    @patch('subprocess.run')
    def test_prerequisites_check(self, mock_run, tmp_path):
        """Test prerequisite checking."""
        # Mock successful pre-commit and git commands
        mock_run.return_value = MagicMock(returncode=0)
        
        installer = PreCommitInstaller(tmp_path)
        
        # Should not raise exception
        installer._check_prerequisites()
        
        # Verify commands were called
        assert mock_run.call_count >= 2  # pre-commit --version and git rev-parse
    
    def test_mode_determination(self, tmp_path):
        """Test installation mode determination."""
        installer = PreCommitInstaller(tmp_path)
        
        # Test fresh installation (no existing config)
        mode = installer._determine_mode(InstallMode.MERGE)
        assert mode == InstallMode.FRESH
        
        # Test with existing config
        config_file = tmp_path / ".pre-commit-config.yaml"
        config_file.write_text("repos: []")
        
        mode = installer._determine_mode(InstallMode.MERGE)
        assert mode == InstallMode.MERGE
    
    def test_conflict_detection(self, tmp_path):
        """Test conflict detection with existing tools."""
        installer = PreCommitInstaller(tmp_path)
        
        # Create config with conflicting tools
        existing_config = {
            'repos': [
                {
                    'repo': 'https://github.com/psf/black',
                    'rev': 'stable',
                    'hooks': [{'id': 'black'}]
                },
                {
                    'repo': 'https://github.com/pycqa/flake8',
                    'rev': 'stable',
                    'hooks': [{'id': 'flake8'}]
                }
            ]
        }
        
        config_file = tmp_path / ".pre-commit-config.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(existing_config, f)
        
        conflicts = installer._detect_conflicts(ValidationLevel.STANDARD)
        
        # Should detect flake8 conflict (replaced by Vibe Check)
        conflict_ids = [c.hook_id for c in conflicts]
        assert 'flake8' in conflict_ids
    
    def test_backup_creation(self, tmp_path):
        """Test configuration backup creation."""
        installer = PreCommitInstaller(tmp_path)
        
        # Create existing config
        config_file = tmp_path / ".pre-commit-config.yaml"
        original_content = "repos: []"
        config_file.write_text(original_content)
        
        # Create backup
        backup_path = installer._create_backup()
        
        # Verify backup exists and has correct content
        assert backup_path.exists()
        assert backup_path.read_text() == original_content
        assert backup_path.name == ".pre-commit-config.yaml.backup"


class TestPreCommitAnalyzer:
    """Test pre-commit analysis functionality."""
    
    def test_analyzer_initialization(self, tmp_path):
        """Test PreCommitAnalyzer initialization."""
        from vibe_check.core.precommit.analyzer import PreCommitConfig
        from vibe_check.core.precommit.generator import ValidationLevel
        config = PreCommitConfig(project_path=tmp_path, validation_level=ValidationLevel.STANDARD)
        analyzer = PreCommitAnalyzer(config)
        assert analyzer.config.project_path == tmp_path

    def test_changed_files_detection(self, tmp_path):
        """Test detection of changed files for incremental analysis."""
        from vibe_check.core.precommit.analyzer import PreCommitConfig
        from vibe_check.core.precommit.generator import ValidationLevel
        config = PreCommitConfig(project_path=tmp_path, validation_level=ValidationLevel.STANDARD)
        analyzer = PreCommitAnalyzer(config)

        # Mock git command
        with patch('subprocess.run') as mock_run:
            mock_run.return_value = MagicMock(
                returncode=0,
                stdout="file1.py\nfile2.py\n"
            )

            changed_files = analyzer.analyze_changed_files()

            # The method returns analysis results, not just file paths
            assert changed_files is not None


class TestIncrementalCache:
    """Test incremental caching system."""
    
    def test_cache_initialization(self, tmp_path):
        """Test cache manager initialization."""
        cache = IncrementalCacheManager(tmp_path)
        assert cache.project_path == tmp_path
        assert cache.cache_dir == tmp_path / ".vibe_check_cache"
    
    def test_cache_operations(self, tmp_path):
        """Test basic cache operations."""
        cache = IncrementalCacheManager(tmp_path, max_entries=10)
        
        # Create test file
        test_file = tmp_path / "test.py"
        test_file.write_text("print('hello')")
        
        # Test cache miss
        result = cache.get_cached_result(test_file, "standard")
        assert result is None
        
        # Store result
        from vibe_check.core.models import AnalysisIssue
        issues = [
            AnalysisIssue(
                rule_id="test-rule",
                message="Test issue",
                severity="warning",
                file_path=test_file,
                line_number=1
            )
        ]
        cache.store_result(test_file, "standard", issues)
        
        # Test cache hit
        cached_result = cache.get_cached_result(test_file, "standard")
        assert cached_result is not None
        assert len(cached_result) == 1
        assert cached_result[0].rule_id == "test-rule"
    
    def test_cache_invalidation(self, tmp_path):
        """Test cache invalidation on file changes."""
        cache = IncrementalCacheManager(tmp_path)
        
        # Create and cache file
        test_file = tmp_path / "test.py"
        test_file.write_text("print('hello')")
        
        from vibe_check.core.models import AnalysisIssue
        issues = [AnalysisIssue(
            rule_id="test-rule",
            message="Test issue", 
            severity="warning",
            file_path=test_file
        )]
        cache.store_result(test_file, "standard", issues)
        
        # Verify cache hit
        assert cache.get_cached_result(test_file, "standard") is not None
        
        # Modify file
        test_file.write_text("print('modified')")
        
        # Verify cache miss (file changed)
        assert cache.get_cached_result(test_file, "standard") is None


class TestCLIIntegration:
    """Test CLI command integration."""
    
    def test_install_hooks_command_import(self):
        """Test that install_hooks_command can be imported."""
        # Should not raise exception
        from vibe_check.cli.commands import install_hooks_command
        assert callable(install_hooks_command)
    
    @patch('vibe_check.core.precommit.installer.PreCommitInstaller')
    def test_install_hooks_command_execution(self, mock_installer_class, tmp_path):
        """Test install_hooks_command execution."""
        # Mock installer
        mock_installer = MagicMock()
        mock_installer_class.return_value = mock_installer
        
        # Mock successful installation
        mock_result = InstallResult(
            success=True,
            mode=InstallMode.FRESH,
            config_path=tmp_path / ".pre-commit-config.yaml",
            validation_level=ValidationLevel.STANDARD,
            conflicts_resolved=[]
        )
        mock_installer.install.return_value = mock_result
        
        # Test command execution
        install_hooks_command(
            project_path=str(tmp_path),
            validation_level="standard",
            merge_existing=True,
            dry_run=False,
            custom_args=["--fix-safe"]
        )
        
        # Verify installer was called correctly
        mock_installer_class.assert_called_once_with(tmp_path)
        mock_installer.install.assert_called_once()


@pytest.mark.integration
class TestEndToEndIntegration:
    """End-to-end integration tests."""
    
    def test_complete_installation_workflow(self, tmp_path):
        """Test complete installation workflow."""
        # Create git repository
        subprocess.run(['git', 'init'], cwd=tmp_path, check=True)
        
        # Create Python file
        test_file = tmp_path / "test.py"
        test_file.write_text("print('hello world')")
        
        # Test hook generation
        generator = PreCommitHookGenerator(tmp_path)
        hooks_config = generator.generate_hooks_config()
        
        # Validate generated config
        hooks = yaml.safe_load(hooks_config)
        assert len(hooks) == 3
        
        # Test project config generation
        project_config = generator.generate_project_config(
            validation_level=ValidationLevel.STANDARD
        )
        
        # Validate project config
        config = yaml.safe_load(project_config)
        assert 'repos' in config
        assert len(config['repos']) == 1
    
    @pytest.mark.skipif(
        subprocess.run(['which', 'pre-commit'], capture_output=True).returncode != 0,
        reason="pre-commit not available"
    )
    def test_precommit_validation(self, tmp_path):
        """Test that generated project config passes pre-commit validation."""
        # Generate project config (not hooks config)
        generator = PreCommitHookGenerator(tmp_path)
        project_config = generator.generate_project_config(
            validation_level=ValidationLevel.STANDARD
        )

        # Save to file
        config_file = tmp_path / ".pre-commit-config.yaml"
        config_file.write_text(project_config)

        # Validate with pre-commit
        result = subprocess.run(
            ['pre-commit', 'validate-config', str(config_file)],
            capture_output=True,
            text=True
        )

        assert result.returncode == 0, f"Config validation failed: {result.stderr}"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
