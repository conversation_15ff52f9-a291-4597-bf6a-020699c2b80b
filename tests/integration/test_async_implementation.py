#!/usr/bin/env python3
"""
Async Implementation Test Suite
==============================

Comprehensive test suite for Week 3 Task 3.1: Async Conversion.
Tests performance improvements, async functionality, and backward compatibility.
"""

import asyncio
import time
import sys
from pathlib import Path
from typing import Dict, Any, List
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_async_analysis_engine():
    """Test the async unified analysis engine"""
    print("🧪 Testing Async Analysis Engine")
    print("=" * 35)
    
    try:
        from vibe_check.core.async_unified_analyzer import AsyncUnifiedAnalysisEngine, AsyncAnalysisConfig
        
        # Test different configurations
        configs = [
            ("Standard Async", AsyncAnalysisConfig(max_workers=4, max_concurrent_files=20, enable_streaming=False)),
            ("High Concurrency", AsyncAnalysisConfig(max_workers=8, max_concurrent_files=50, enable_streaming=False)),
            ("Streaming Mode", AsyncAnalysisConfig(max_workers=4, max_concurrent_files=30, enable_streaming=True)),
        ]
        
        results = {}
        
        # Create test files
        test_dir = Path("async_test_files")
        test_dir.mkdir(exist_ok=True)
        
        # Create 100 test files with varying complexity
        for i in range(100):
            complexity_factor = i % 5 + 1
            test_file = test_dir / f"test_{i}.py"
            test_file.write_text(f'''
def function_{i}():
    """Function {i} with complexity factor {complexity_factor}"""
    result = []
    for j in range({10 * complexity_factor}):
        if j % 2 == 0:
            for k in range({complexity_factor}):
                try:
                    if j > {5 * complexity_factor}:
                        result.append(j * k * {i})
                    elif j < {2 * complexity_factor}:
                        result.append(j + k + {i})
                    else:
                        result.append(j - k - {i})
                except Exception as e:
                    continue
                finally:
                    k += 1
        else:
            with open("temp_{i}.txt", "w") as f:
                f.write(str(j))
    return result

class Class_{i}:
    def __init__(self):
        self.data = list(range({20 * complexity_factor}))
        self.factor = {complexity_factor}
    
    def process(self):
        return sum(self.data) * self.factor
    
    def analyze(self):
        result = []
        for item in self.data:
            if item % self.factor == 0:
                result.append(item)
        return result
''')
        
        print(f"📁 Created {len(list(test_dir.glob('*.py')))} test files")
        
        # Test each configuration
        for config_name, config in configs:
            print(f"\n🔄 Testing: {config_name}")
            
            engine = AsyncUnifiedAnalysisEngine(config)
            
            start_time = time.time()
            
            if config.enable_streaming:
                # Test streaming mode
                file_count = 0
                async for file_metrics in engine.analyze_project_streaming(test_dir):
                    file_count += 1
                
                # Get final results
                result = await engine.analyze_project(test_dir)
            else:
                # Standard analysis
                result = await engine.analyze_project(test_dir)
            
            analysis_time = time.time() - start_time
            files_per_second = result.files_analyzed / analysis_time if analysis_time > 0 else 0
            
            results[config_name] = {
                'files_analyzed': result.files_analyzed,
                'analysis_time': analysis_time,
                'files_per_second': files_per_second,
                'avg_complexity': result.project_metrics.average_complexity,
                'quality_score': result.project_metrics.quality_score
            }
            
            print(f"  ✅ {config_name}:")
            print(f"    • Files: {result.files_analyzed}")
            print(f"    • Time: {analysis_time:.3f}s")
            print(f"    • Speed: {files_per_second:.1f} files/sec")
            print(f"    • Avg Complexity: {result.project_metrics.average_complexity:.1f}")
            print(f"    • Quality: {result.project_metrics.quality_score:.1f}/10")
        
        # Cleanup
        import shutil
        shutil.rmtree(test_dir, ignore_errors=True)
        
        return results
        
    except Exception as e:
        print(f"❌ Async analysis engine test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def test_async_visualization():
    """Test the async visualization system"""
    print("\n🧪 Testing Async Visualization System")
    print("=" * 40)
    
    try:
        from vibe_check.core.visualization.async_dashboard_engine import AsyncDashboardEngine, AsyncRenderConfig
        from vibe_check.core.visualization.unified_charts import ChartType
        
        config = AsyncRenderConfig(
            max_concurrent_panels=10,
            render_timeout=30.0,
            enable_streaming=True,
            cache_rendered_panels=True
        )
        
        async with AsyncDashboardEngine(config) as engine:
            # Create test dashboard
            dashboard = engine.create_dashboard(
                "async_test_dashboard",
                "Async Test Dashboard",
                "Testing async visualization capabilities"
            )
            
            # Add various panel types
            from vibe_check.core.visualization.dashboard_engine import PanelType, DashboardPanel
            
            panels_to_add = [
                ("cpu_metric", "CPU Usage", PanelType.METRIC, {'x': 0, 'y': 0, 'width': 3, 'height': 2}),
                ("memory_metric", "Memory Usage", PanelType.METRIC, {'x': 3, 'y': 0, 'width': 3, 'height': 2}),
                ("performance_chart", "Performance Chart", PanelType.CHART, {'x': 0, 'y': 2, 'width': 6, 'height': 4}),
                ("distribution_chart", "Distribution", PanelType.CHART, {'x': 6, 'y': 0, 'width': 6, 'height': 6}),
            ]
            
            for panel_id, title, panel_type, position in panels_to_add:
                panel = DashboardPanel(
                    id=panel_id,
                    title=title,
                    panel_type=panel_type,
                    position=position,
                    config={'chart_type': 'line' if panel_type == PanelType.CHART else 'metric'}
                )
                dashboard.add_panel(panel)
            
            print(f"📊 Created dashboard with {len(dashboard.panels)} panels")
            
            # Test concurrent rendering
            test_data = {
                'cpu_metric': {'value': 45.2},
                'memory_metric': {'value': 67.8},
                'performance_chart': {
                    'x': ['1h', '2h', '3h', '4h', '5h'],
                    'y': [85, 92, 88, 95, 90]
                },
                'distribution_chart': {
                    'x': ['Low', 'Medium', 'High', 'Critical'],
                    'y': [25, 45, 25, 5]
                }
            }
            
            # Measure rendering performance
            start_time = time.time()
            
            html = await engine.render_dashboard_async("async_test_dashboard", test_data)
            
            render_time = time.time() - start_time
            
            print(f"  ✅ Dashboard rendered in {render_time:.3f}s")
            print(f"  📄 HTML size: {len(html)} characters")
            
            # Save dashboard
            dashboard_file = Path("async_test_dashboard.html")
            await engine.save_dashboard_async("async_test_dashboard", dashboard_file, test_data)
            
            print(f"  💾 Dashboard saved to: {dashboard_file}")
            
            # Test multiple dashboard rendering (concurrency test)
            print(f"\n🔄 Testing concurrent dashboard rendering...")
            
            dashboards_to_render = []
            for i in range(5):
                dash_id = f"concurrent_test_{i}"
                dash = engine.create_dashboard(dash_id, f"Concurrent Test {i}")
                
                # Add a few panels
                dash.add_panel(DashboardPanel(
                    id=f"metric_{i}",
                    title=f"Metric {i}",
                    panel_type=PanelType.METRIC,
                    position={'x': 0, 'y': 0, 'width': 6, 'height': 3},
                    config={'metric_name': f'test_metric_{i}'}
                ))
                
                dashboards_to_render.append(dash_id)
            
            # Render all dashboards concurrently
            start_time = time.time()
            
            render_tasks = [
                engine.render_dashboard_async(dash_id, {f'metric_{i}': {'value': i * 10}})
                for i, dash_id in enumerate(dashboards_to_render)
            ]
            
            rendered_htmls = await asyncio.gather(*render_tasks)
            
            concurrent_render_time = time.time() - start_time
            dashboards_per_second = len(dashboards_to_render) / concurrent_render_time
            
            print(f"  ✅ Rendered {len(dashboards_to_render)} dashboards in {concurrent_render_time:.3f}s")
            print(f"  ⚡ Speed: {dashboards_per_second:.1f} dashboards/sec")
            
            return {
                'single_render_time': render_time,
                'concurrent_render_time': concurrent_render_time,
                'dashboards_per_second': dashboards_per_second,
                'html_size': len(html)
            }
    
    except Exception as e:
        print(f"❌ Async visualization test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def test_performance_comparison():
    """Compare async vs sync performance"""
    print("\n🧪 Testing Performance Comparison (Async vs Sync)")
    print("=" * 50)
    
    try:
        # Import both engines
        from vibe_check.core.async_unified_analyzer import AsyncUnifiedAnalysisEngine, AsyncAnalysisConfig
        from test_unified_analyzer_simple import SimpleUnifiedAnalyzer
        
        # Create test dataset
        test_dir = Path("performance_comparison_test")
        test_dir.mkdir(exist_ok=True)
        
        # Create 200 files for meaningful comparison
        for i in range(200):
            test_file = test_dir / f"perf_test_{i}.py"
            test_file.write_text(f'''
def performance_function_{i}():
    """Performance test function {i}"""
    result = []
    for j in range(15):
        if j % 3 == 0:
            try:
                for k in range(5):
                    if k % 2 == 0:
                        result.append(j * k * {i})
                    else:
                        result.append(j + k + {i})
            except Exception:
                continue
        elif j % 3 == 1:
            with open(f"temp_{{j}}.txt", "w") as f:
                f.write(str(j * {i}))
        else:
            result.extend([x for x in range(j) if x % 2 == 0])
    return result

class PerformanceClass_{i}:
    def __init__(self):
        self.data = list(range(30))
        self.multiplier = {i}
    
    def compute(self):
        return [x * self.multiplier for x in self.data if x % 3 == 0]
''')
        
        print(f"📁 Created {len(list(test_dir.glob('*.py')))} test files")
        
        # Test sync performance (baseline)
        print(f"\n🔄 Testing Sync Performance (Baseline)...")
        sync_analyzer = SimpleUnifiedAnalyzer(max_workers=4, use_async=False)
        
        start_time = time.time()
        sync_result = await sync_analyzer.analyze_project(test_dir)
        sync_time = time.time() - start_time
        sync_speed = sync_result.files_analyzed / sync_time
        
        print(f"  📊 Sync Results:")
        print(f"    • Files: {sync_result.files_analyzed}")
        print(f"    • Time: {sync_time:.3f}s")
        print(f"    • Speed: {sync_speed:.1f} files/sec")
        
        # Test async performance
        print(f"\n⚡ Testing Async Performance...")
        async_config = AsyncAnalysisConfig(
            max_workers=4,
            max_concurrent_files=50,
            enable_streaming=False
        )
        async_analyzer = AsyncUnifiedAnalysisEngine(async_config)
        
        start_time = time.time()
        async_result = await async_analyzer.analyze_project(test_dir)
        async_time = time.time() - start_time
        async_speed = async_result.files_analyzed / async_time
        
        print(f"  📊 Async Results:")
        print(f"    • Files: {async_result.files_analyzed}")
        print(f"    • Time: {async_time:.3f}s")
        print(f"    • Speed: {async_speed:.1f} files/sec")
        
        # Calculate improvement
        time_improvement = ((sync_time - async_time) / sync_time) * 100
        speed_improvement = ((async_speed - sync_speed) / sync_speed) * 100
        
        print(f"\n🎯 Performance Improvement:")
        print(f"  • Time reduction: {time_improvement:+.1f}%")
        print(f"  • Speed increase: {speed_improvement:+.1f}%")
        
        # Cleanup
        import shutil
        shutil.rmtree(test_dir, ignore_errors=True)
        
        return {
            'sync_time': sync_time,
            'async_time': async_time,
            'sync_speed': sync_speed,
            'async_speed': async_speed,
            'time_improvement': time_improvement,
            'speed_improvement': speed_improvement
        }
    
    except Exception as e:
        print(f"❌ Performance comparison test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def test_backward_compatibility():
    """Test backward compatibility with sync interfaces"""
    print("\n🧪 Testing Backward Compatibility")
    print("=" * 35)
    
    try:
        from vibe_check.core.async_unified_analyzer import analyze_project_sync
        
        # Create small test
        test_dir = Path("compatibility_test")
        test_dir.mkdir(exist_ok=True)
        
        test_file = test_dir / "compat_test.py"
        test_file.write_text('''
def compatibility_test():
    """Test backward compatibility"""
    return "working"

class CompatibilityClass:
    def test(self):
        return True
''')
        
        # Test sync wrapper
        print("🔄 Testing sync wrapper...")
        start_time = time.time()
        
        result = analyze_project_sync(test_dir)
        
        sync_wrapper_time = time.time() - start_time
        
        print(f"  ✅ Sync wrapper working:")
        print(f"    • Files: {result.files_analyzed}")
        print(f"    • Time: {sync_wrapper_time:.3f}s")
        print(f"    • Quality: {result.project_metrics.quality_score:.1f}/10")
        
        # Cleanup
        import shutil
        shutil.rmtree(test_dir, ignore_errors=True)
        
        return {
            'sync_wrapper_working': True,
            'sync_wrapper_time': sync_wrapper_time
        }
    
    except Exception as e:
        print(f"❌ Backward compatibility test failed: {e}")
        return {'sync_wrapper_working': False}


async def main():
    """Main test function"""
    print("🚀 Async Implementation Test Suite - Task 3.1")
    print("=" * 55)
    
    # Run all tests
    analysis_results = await test_async_analysis_engine()
    visualization_results = await test_async_visualization()
    performance_results = await test_performance_comparison()
    compatibility_results = await test_backward_compatibility()
    
    print("\n" + "=" * 55)
    print("📊 ASYNC IMPLEMENTATION TEST SUMMARY")
    print("=" * 55)
    
    # Analysis Engine Results
    if analysis_results:
        print(f"\n📈 Analysis Engine Performance:")
        best_config = max(analysis_results.items(), key=lambda x: x[1]['files_per_second'])
        print(f"  • Best configuration: {best_config[0]}")
        print(f"  • Best speed: {best_config[1]['files_per_second']:.1f} files/sec")
        
        avg_speed = sum(r['files_per_second'] for r in analysis_results.values()) / len(analysis_results)
        print(f"  • Average speed: {avg_speed:.1f} files/sec")
    
    # Visualization Results
    if visualization_results:
        print(f"\n🎨 Visualization Performance:")
        print(f"  • Single dashboard: {visualization_results['single_render_time']:.3f}s")
        print(f"  • Concurrent rendering: {visualization_results['dashboards_per_second']:.1f} dashboards/sec")
    
    # Performance Comparison
    if performance_results:
        print(f"\n⚡ Performance vs Baseline:")
        print(f"  • Speed improvement: {performance_results['speed_improvement']:+.1f}%")
        print(f"  • Time reduction: {performance_results['time_improvement']:+.1f}%")
        
        # Check if 50% target is met
        target_met = performance_results['speed_improvement'] >= 50
        print(f"  • 50% target: {'✅ ACHIEVED' if target_met else '⚠️  Not yet achieved'}")
    
    # Compatibility
    if compatibility_results:
        compat_status = "✅ Working" if compatibility_results['sync_wrapper_working'] else "❌ Failed"
        print(f"\n🔄 Backward Compatibility: {compat_status}")
    
    # Overall Assessment
    tests_passed = sum([
        bool(analysis_results),
        bool(visualization_results),
        bool(performance_results),
        compatibility_results.get('sync_wrapper_working', False)
    ])
    
    print(f"\n🎯 Overall Assessment: {tests_passed}/4 tests passed")
    
    if tests_passed >= 3:
        print("✅ Task 3.1: Async Conversion COMPLETED SUCCESSFULLY")
        print("🚀 Ready to proceed with Task 3.2: Caching Implementation")
        return 0
    else:
        print("❌ Task 3.1: Async Conversion needs more work")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
