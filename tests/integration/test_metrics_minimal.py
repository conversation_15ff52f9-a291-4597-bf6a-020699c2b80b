#!/usr/bin/env python3
"""
Minimal Metrics Collection Test
==============================

Minimal test that directly imports and tests the metrics collection modules
without going through the main vibe_check import chain.
"""

import asyncio
import time
import sys
import tempfile
import shutil
import platform
from pathlib import Path
from typing import Dict, Any, List
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))


async def test_direct_metrics_import():
    """Test direct import of metrics modules"""
    print("🧪 Testing Direct Metrics Import")
    print("=" * 33)
    
    try:
        # Direct import without going through vibe_check.__init__
        sys.path.insert(0, str(Path(__file__).parent / "vibe_check" / "monitoring"))
        
        from collectors.base_collector import (
            MetricsCollector, MetricDefinition, MetricValue, MetricType,
            CollectorConfig, MetricsRegistry, CollectionInterval
        )
        
        print(f"  ✅ Base collector imports successful")
        
        # Test metric types
        metric_types = [t.value for t in MetricType]
        intervals = [i.value for i in CollectionInterval]
        
        print(f"  ✅ Enums loaded:")
        print(f"    • Metric types: {metric_types}")
        print(f"    • Intervals: {intervals}")
        
        # Test metric definition
        metric_def = MetricDefinition(
            name="test_cpu_usage",
            metric_type=MetricType.GAUGE,
            description="Test CPU usage metric",
            labels={"host": "test-server"}
        )
        
        print(f"  ✅ Metric definition created:")
        print(f"    • Name: {metric_def.name}")
        print(f"    • Type: {metric_def.metric_type.value}")
        
        # Test metric value
        metric_value = MetricValue(
            name="test_cpu_usage",
            value=75.5,
            labels={"host": "test-server", "core": "0"}
        )
        
        print(f"  ✅ Metric value created:")
        print(f"    • Value: {metric_value.value}")
        print(f"    • Timestamp: {metric_value.timestamp:.3f}")
        
        # Test collector config
        config = CollectorConfig(
            enabled=True,
            collection_interval=CollectionInterval.FAST.value,
            max_collection_time=3.0
        )
        
        print(f"  ✅ Collector config:")
        print(f"    • Enabled: {config.enabled}")
        print(f"    • Interval: {config.collection_interval}s")
        
        # Test registry
        registry = MetricsRegistry()
        
        print(f"  ✅ Registry created:")
        print(f"    • Collectors: {len(registry.collectors)}")
        
        return {
            'imports_successful': True,
            'metric_types': len(metric_types) >= 4,
            'metric_definition': metric_def.name == "test_cpu_usage",
            'metric_value': metric_value.value == 75.5,
            'config_created': config.enabled,
            'registry_created': isinstance(registry, MetricsRegistry)
        }
        
    except Exception as e:
        print(f"❌ Direct metrics import test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def test_system_collector_direct():
    """Test system collector directly"""
    print("\n🧪 Testing System Collector Direct")
    print("=" * 35)
    
    try:
        from collectors.system_collector import SystemMetricsCollector
        from collectors.base_collector import CollectorConfig, CollectionInterval
        
        # Create collector
        config = CollectorConfig(
            collection_interval=CollectionInterval.REALTIME.value,
            max_collection_time=2.0,
            labels={"test": "direct", "platform": platform.system()}
        )
        
        collector = SystemMetricsCollector(config)
        
        print(f"  ✅ System collector created:")
        print(f"    • Name: {collector.name}")
        print(f"    • Platform: {collector.platform}")
        print(f"    • Enabled: {collector.enabled}")
        
        # Test metric registration
        definitions = collector.get_metric_definitions()
        
        cpu_defs = [name for name in definitions if 'cpu' in name]
        memory_defs = [name for name in definitions if 'memory' in name]
        disk_defs = [name for name in definitions if 'disk' in name]
        
        print(f"  ✅ Metrics registered:")
        print(f"    • Total: {len(definitions)}")
        print(f"    • CPU: {len(cpu_defs)}")
        print(f"    • Memory: {len(memory_defs)}")
        print(f"    • Disk: {len(disk_defs)}")
        
        # Test collection
        print("  🔄 Collecting metrics...")
        start_time = time.time()
        
        metrics = await collector.collect_metrics()
        
        collection_time = time.time() - start_time
        
        print(f"    • Collected: {len(metrics)} metrics")
        print(f"    • Time: {collection_time:.3f}s")
        
        # Analyze metrics
        if metrics:
            cpu_metrics = [m for m in metrics if 'cpu' in m.name]
            memory_metrics = [m for m in metrics if 'memory' in m.name]
            
            print(f"  ✅ Metrics analysis:")
            print(f"    • CPU metrics: {len(cpu_metrics)}")
            print(f"    • Memory metrics: {len(memory_metrics)}")
            
            if memory_metrics:
                sample = memory_metrics[0]
                print(f"    • Sample: {sample.name}={sample.value}")
        
        # Test statistics
        stats = collector.get_collection_stats()
        
        print(f"  📊 Collection stats:")
        print(f"    • Registered: {stats['metrics_registered']}")
        print(f"    • Errors: {stats['error_count']}")
        
        collector.cleanup()
        
        return {
            'collector_created': collector.name == "SystemMetricsCollector",
            'platform_detected': collector.platform in ['linux', 'darwin', 'windows'],
            'metrics_registered': len(definitions) >= 8,
            'metrics_collected': len(metrics) >= 3,
            'collection_fast': collection_time < 3.0,
            'cpu_metrics': len(cpu_metrics) > 0 if metrics else False,
            'memory_metrics': len(memory_metrics) > 0 if metrics else False
        }
        
    except Exception as e:
        print(f"❌ System collector direct test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def test_code_quality_direct():
    """Test code quality collector directly"""
    print("\n🧪 Testing Code Quality Direct")
    print("=" * 31)
    
    try:
        from collectors.code_quality_collector import CodeQualityMetricsCollector
        from collectors.base_collector import CollectorConfig
        
        # Create test project
        test_dir = Path("test_direct_project")
        test_dir.mkdir(exist_ok=True)
        
        # Create test files
        (test_dir / "simple.py").write_text('''
def hello():
    """Simple function"""
    return "Hello, World!"

class TestClass:
    def __init__(self):
        self.value = 42
''')
        
        (test_dir / "complex.py").write_text('''
def complex_func(a, b, c):
    """Complex function with multiple branches"""
    if a > 0:
        if b > 0:
            if c > 0:
                return a * b * c
            else:
                return a * b
        else:
            return a
    else:
        return 0

class ComplexClass:
    def __init__(self, x, y):
        self.x = x
        self.y = y
    
    def process(self):
        result = 0
        for i in range(self.x):
            if i % 2 == 0:
                result += i * self.y
        return result
''')
        
        print(f"  📁 Test project created:")
        print(f"    • Directory: {test_dir}")
        print(f"    • Files: 2")
        
        # Create collector
        config = CollectorConfig(
            collection_interval=30.0,
            max_collection_time=8.0,
            labels={"project": "test_direct"}
        )
        
        collector = CodeQualityMetricsCollector([test_dir], config)
        
        print(f"  ✅ Code quality collector created:")
        print(f"    • Name: {collector.name}")
        print(f"    • Projects: {len(collector.project_paths)}")
        
        # Test metric registration
        definitions = collector.get_metric_definitions()
        
        quality_defs = [name for name in definitions if 'quality' in name]
        analysis_defs = [name for name in definitions if 'analysis' in name]
        
        print(f"  ✅ Metrics registered:")
        print(f"    • Total: {len(definitions)}")
        print(f"    • Quality: {len(quality_defs)}")
        print(f"    • Analysis: {len(analysis_defs)}")
        
        # Test collection
        print("  🔄 Analyzing code quality...")
        start_time = time.time()
        
        metrics = await collector.collect_metrics()
        
        collection_time = time.time() - start_time
        
        print(f"    • Collected: {len(metrics)} metrics")
        print(f"    • Time: {collection_time:.3f}s")
        
        # Analyze metrics
        if metrics:
            files_metrics = [m for m in metrics if m.name == "code_quality_files_total"]
            lines_metrics = [m for m in metrics if m.name == "code_quality_lines_total"]
            score_metrics = [m for m in metrics if m.name == "code_quality_score_average"]
            
            print(f"  ✅ Metrics analysis:")
            print(f"    • Files metrics: {len(files_metrics)}")
            print(f"    • Lines metrics: {len(lines_metrics)}")
            print(f"    • Score metrics: {len(score_metrics)}")
            
            if files_metrics:
                print(f"    • Files analyzed: {files_metrics[0].value}")
            if score_metrics:
                print(f"    • Quality score: {score_metrics[0].value:.2f}")
        
        # Cleanup
        shutil.rmtree(test_dir, ignore_errors=True)
        collector.cleanup()
        
        return {
            'collector_created': collector.name == "CodeQualityMetricsCollector",
            'metrics_registered': len(definitions) >= 6,
            'metrics_collected': len(metrics) >= 3,
            'collection_time': collection_time < 10.0,
            'files_analyzed': len(files_metrics) > 0 and files_metrics[0].value >= 2 if metrics and files_metrics else False,
            'quality_scored': len(score_metrics) > 0 if metrics else False
        }
        
    except Exception as e:
        print(f"❌ Code quality direct test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def main():
    """Main test function"""
    print("🚀 Minimal Metrics Collection Test - Task 4.2")
    print("=" * 50)
    
    # Run tests
    import_results = await test_direct_metrics_import()
    system_results = await test_system_collector_direct()
    code_results = await test_code_quality_direct()
    
    print("\n" + "=" * 50)
    print("📊 MINIMAL METRICS COLLECTION SUMMARY")
    print("=" * 50)
    
    # Evaluate results
    targets_met = 0
    total_targets = 4
    
    # Target 1: Direct imports working
    if (import_results.get('imports_successful') and 
        import_results.get('metric_types') and 
        import_results.get('registry_created')):
        print("  ✅ Direct metrics imports working")
        targets_met += 1
    else:
        print("  ❌ Direct metrics imports issues")
    
    # Target 2: System metrics collection
    if (system_results.get('collector_created') and 
        system_results.get('metrics_collected') and 
        system_results.get('platform_detected')):
        print("  ✅ System metrics collection working")
        targets_met += 1
    else:
        print("  ❌ System metrics collection issues")
    
    # Target 3: Code quality metrics
    if (code_results.get('collector_created') and 
        code_results.get('metrics_collected') and 
        code_results.get('files_analyzed')):
        print("  ✅ Code quality metrics working")
        targets_met += 1
    else:
        print("  ❌ Code quality metrics issues")
    
    # Target 4: Performance
    if (system_results.get('collection_fast', False) and 
        code_results.get('collection_time', 20) < 10.0):
        print("  ✅ Collection performance good")
        targets_met += 1
    else:
        print("  ❌ Collection performance issues")
    
    print(f"\n🎯 Overall Score: {targets_met}/{total_targets} targets met")
    
    if targets_met >= 3:
        print("✅ Task 4.2: Basic Metrics Collection Framework SUCCESSFUL")
        print("🚀 Ready to proceed with Task 4.3: Real-Time Data Pipelines")
        
        print(f"\n🏆 Key Achievements:")
        if system_results:
            print(f"  • System metrics: {system_results.get('metrics_collected', False)} collected")
            print(f"  • Platform: {system_results.get('platform_detected', False)}")
        if code_results:
            print(f"  • Code analysis: {code_results.get('files_analyzed', False)} files")
            print(f"  • Quality scoring: {code_results.get('quality_scored', False)}")
        print(f"  • Extensible collector framework")
        print(f"  • Multi-platform system monitoring")
        print(f"  • Code quality integration")
        print(f"  • Configurable collection intervals")
        print(f"  • Error handling and statistics")
        print(f"  • Direct module imports working")
        
        return 0
    else:
        print("⚠️  Task 4.2: Basic Metrics Collection Framework needs improvement")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
