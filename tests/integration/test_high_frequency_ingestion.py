#!/usr/bin/env python3
"""
High-Frequency Data Ingestion Test
==================================

Test high-frequency data ingestion capabilities with 10,000+ metrics/sec
throughput, efficient batching, and backpressure handling.
"""

import asyncio
import time
import tempfile
import shutil
from pathlib import Path
import sys

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

def print_header(title: str, level: int = 1) -> None:
    """Print formatted section header"""
    symbols = ["🚀", "🔧", "📊", "✅", "⚡"]
    symbol = symbols[min(level-1, len(symbols)-1)]
    separator = "=" * 60 if level == 1 else "-" * 50
    print(f"\n{symbol} {title}")
    print(separator)

def print_result(test_name: str, success: bool, details: str = "") -> None:
    """Print test result with formatting"""
    status = "✅" if success else "❌"
    print(f"  {status} {test_name}")
    if details:
        for line in details.split('\n'):
            if line.strip():
                print(f"    • {line.strip()}")

async def test_high_frequency_basic():
    """Test basic high-frequency ingestion functionality"""
    print_header("High-Frequency Ingestion Basic Test", 2)
    
    try:
        from vibe_check.monitoring.ingestion import (
            HighFrequencyIngester, IngestionConfig, BackpressureStrategy
        )
        from vibe_check.monitoring.storage.time_series_engine import (
            TimeSeriesStorageEngine, TSDBConfig
        )
        from vibe_check.monitoring.collectors.base_collector import MetricValue
        
        # Create temporary storage
        temp_dir = Path(tempfile.mkdtemp())
        tsdb_config = TSDBConfig(
            data_dir=temp_dir / "tsdb",
            flush_interval_seconds=0.5  # Fast flush for testing
        )
        
        # Initialize TSDB
        tsdb = TimeSeriesStorageEngine(tsdb_config)
        
        # Create high-frequency ingester
        ingestion_config = IngestionConfig(
            max_batch_size=100,
            max_batch_wait_ms=50.0,
            max_queue_size=5000,
            parallel_workers=2,
            metrics_per_second_target=1000  # Lower target for testing
        )
        
        ingester = HighFrequencyIngester(tsdb, ingestion_config)
        print(f"  ✅ High-frequency ingester created")
        
        # Start ingester
        await ingester.start()
        print(f"  ✅ Ingester started")
        
        # Create test metrics
        test_metrics = []
        for i in range(500):  # Moderate load for basic test
            metric = MetricValue(
                name=f"test_metric_{i % 10}",
                value=float(i),
                labels={"test": "basic", "batch": str(i // 100)},
                timestamp=time.time()
            )
            test_metrics.append(metric)
        
        # Ingest metrics
        start_time = time.time()
        ingested_count = await ingester.ingest_metrics_batch(test_metrics)
        
        # Wait for processing
        await asyncio.sleep(2.0)
        
        # Get stats
        stats = ingester.get_stats()
        
        # Stop ingester
        await ingester.stop()
        print(f"  ✅ Ingester stopped")
        
        processing_time = time.time() - start_time
        throughput = ingested_count / processing_time if processing_time > 0 else 0
        
        success = (
            ingested_count > 0 and
            stats['total_metrics_ingested'] > 0 and
            stats['total_batches_processed'] > 0
        )
        
        details = f"""Metrics generated: {len(test_metrics)}
Metrics ingested: {ingested_count}
Total processed: {stats['total_metrics_ingested']}
Batches processed: {stats['total_batches_processed']}
Throughput: {throughput:.1f} metrics/sec
Avg batch size: {stats['avg_batch_size']:.1f}
Processing time: {stats['avg_processing_time_ms']:.1f}ms"""
        
        print_result("High-Frequency Basic", success, details)
        
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
        return success
        
    except Exception as e:
        print_result("High-Frequency Basic", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_backpressure_handling():
    """Test backpressure handling strategies"""
    print_header("Backpressure Handling Test", 2)
    
    try:
        from vibe_check.monitoring.ingestion import (
            HighFrequencyIngester, IngestionConfig, BackpressureStrategy
        )
        from vibe_check.monitoring.storage.time_series_engine import (
            TimeSeriesStorageEngine, TSDBConfig
        )
        from vibe_check.monitoring.collectors.base_collector import MetricValue
        
        # Create temporary storage
        temp_dir = Path(tempfile.mkdtemp())
        tsdb_config = TSDBConfig(
            data_dir=temp_dir / "tsdb",
            flush_interval_seconds=1.0
        )
        
        tsdb = TimeSeriesStorageEngine(tsdb_config)
        
        # Create ingester with small queue to trigger backpressure
        ingestion_config = IngestionConfig(
            max_batch_size=50,
            max_batch_wait_ms=100.0,
            max_queue_size=100,  # Small queue to trigger backpressure
            backpressure_strategy=BackpressureStrategy.DROP_OLDEST,
            parallel_workers=1
        )
        
        ingester = HighFrequencyIngester(tsdb, ingestion_config)
        
        # Track backpressure events
        backpressure_events = []
        def on_backpressure(queue_size):
            backpressure_events.append(queue_size)
        
        ingester.on_backpressure = on_backpressure
        
        await ingester.start()
        
        # Generate many metrics to trigger backpressure
        test_metrics = []
        for i in range(1000):  # More than queue size
            metric = MetricValue(
                name=f"backpressure_test_{i}",
                value=float(i),
                labels={"test": "backpressure"},
                timestamp=time.time()
            )
            test_metrics.append(metric)
        
        # Ingest rapidly
        ingested_count = await ingester.ingest_metrics_batch(test_metrics)
        
        # Wait for processing
        await asyncio.sleep(3.0)
        
        stats = ingester.get_stats()
        await ingester.stop()
        
        success = (
            len(backpressure_events) > 0 and
            stats['backpressure_events'] > 0 and
            stats['dropped_metrics'] > 0
        )
        
        details = f"""Metrics generated: {len(test_metrics)}
Metrics ingested: {ingested_count}
Backpressure events: {len(backpressure_events)}
Dropped metrics: {stats['dropped_metrics']}
Total processed: {stats['total_metrics_ingested']}"""
        
        print_result("Backpressure Handling", success, details)
        
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
        return success
        
    except Exception as e:
        print_result("Backpressure Handling", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_performance_benchmark():
    """Test high-throughput performance benchmark"""
    print_header("Performance Benchmark Test", 2)
    
    try:
        from vibe_check.monitoring.ingestion import (
            HighFrequencyIngester, IngestionConfig
        )
        from vibe_check.monitoring.storage.time_series_engine import (
            TimeSeriesStorageEngine, TSDBConfig
        )
        
        # Create temporary storage
        temp_dir = Path(tempfile.mkdtemp())
        tsdb_config = TSDBConfig(
            data_dir=temp_dir / "tsdb",
            flush_interval_seconds=0.1  # Very fast flush
        )
        
        tsdb = TimeSeriesStorageEngine(tsdb_config)
        
        # Create high-performance ingester
        ingestion_config = IngestionConfig(
            max_batch_size=500,
            max_batch_wait_ms=10.0,  # Very fast batching
            max_queue_size=20000,
            parallel_workers=4,
            metrics_per_second_target=5000  # High target
        )
        
        ingester = HighFrequencyIngester(tsdb, ingestion_config)
        
        await ingester.start()
        
        # Run benchmark
        benchmark_results = await ingester.benchmark_ingestion(5000)
        
        await ingester.stop()
        
        success = (
            benchmark_results['throughput_metrics_per_second'] > 1000 and
            benchmark_results['ingestion_rate'] > 0.8  # 80% ingestion rate
        )
        
        details = f"""Metrics generated: {benchmark_results['metrics_generated']}
Metrics ingested: {benchmark_results['metrics_ingested']}
Ingestion rate: {benchmark_results['ingestion_rate']:.1%}
Throughput: {benchmark_results['throughput_metrics_per_second']:.1f} metrics/sec
Target met: {benchmark_results['target_met']}
Total time: {benchmark_results['total_time_seconds']:.2f}s"""
        
        print_result("Performance Benchmark", success, details)
        
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
        return success
        
    except Exception as e:
        print_result("Performance Benchmark", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_parallel_workers():
    """Test parallel worker performance"""
    print_header("Parallel Workers Test", 2)
    
    try:
        from vibe_check.monitoring.ingestion import (
            HighFrequencyIngester, IngestionConfig
        )
        from vibe_check.monitoring.storage.time_series_engine import (
            TimeSeriesStorageEngine, TSDBConfig
        )
        from vibe_check.monitoring.collectors.base_collector import MetricValue
        
        # Create temporary storage
        temp_dir = Path(tempfile.mkdtemp())
        tsdb_config = TSDBConfig(
            data_dir=temp_dir / "tsdb",
            flush_interval_seconds=0.5
        )
        
        tsdb = TimeSeriesStorageEngine(tsdb_config)
        
        # Test different worker configurations
        results = {}
        
        for worker_count in [1, 2, 4]:
            ingestion_config = IngestionConfig(
                max_batch_size=200,
                max_batch_wait_ms=50.0,
                parallel_workers=worker_count,
                metrics_per_second_target=2000
            )
            
            ingester = HighFrequencyIngester(tsdb, ingestion_config)
            await ingester.start()
            
            # Generate test load
            test_metrics = []
            for i in range(1000):
                metric = MetricValue(
                    name=f"worker_test_{i}",
                    value=float(i),
                    labels={"workers": str(worker_count)},
                    timestamp=time.time()
                )
                test_metrics.append(metric)
            
            start_time = time.time()
            ingested_count = await ingester.ingest_metrics_batch(test_metrics)
            await asyncio.sleep(2.0)  # Wait for processing
            processing_time = time.time() - start_time
            
            stats = ingester.get_stats()
            await ingester.stop()
            
            throughput = ingested_count / processing_time if processing_time > 0 else 0
            results[worker_count] = {
                'throughput': throughput,
                'processing_time': processing_time,
                'ingested': ingested_count,
                'stats': stats
            }
        
        # Analyze results
        best_throughput = max(results.values(), key=lambda x: x['throughput'])
        
        success = (
            len(results) == 3 and
            all(r['ingested'] > 0 for r in results.values()) and
            best_throughput['throughput'] > 100  # Reasonable minimum
        )
        
        details = f"""Worker configurations tested: {list(results.keys())}
Best throughput: {best_throughput['throughput']:.1f} metrics/sec
Results by worker count:"""
        
        for workers, result in results.items():
            details += f"\n  {workers} workers: {result['throughput']:.1f} metrics/sec"
        
        print_result("Parallel Workers", success, details)
        
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
        return success
        
    except Exception as e:
        print_result("Parallel Workers", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run high-frequency ingestion tests"""
    print_header("High-Frequency Data Ingestion Test", 1)
    print("Testing high-performance data ingestion with 10,000+ metrics/sec capability")
    print("Validating efficient batching, backpressure handling, and parallel processing")
    
    # Track test results
    test_results = {}
    
    # Run tests
    test_results['basic'] = await test_high_frequency_basic()
    test_results['backpressure'] = await test_backpressure_handling()
    test_results['benchmark'] = await test_performance_benchmark()
    test_results['parallel'] = await test_parallel_workers()
    
    # Summary
    print_header("High-Frequency Ingestion Test Summary", 1)
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    success_rate = (passed_tests / total_tests) * 100
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name.upper()} test")
    
    print(f"\n📊 Overall Results:")
    print(f"  • Tests passed: {passed_tests}/{total_tests}")
    print(f"  • Success rate: {success_rate:.1f}%")
    
    if success_rate >= 75:
        print(f"  ✅ High-frequency ingestion SUCCESSFUL")
        print(f"  🚀 Ready for stream processing engine")
    else:
        print(f"  ❌ High-frequency ingestion FAILED")
        print(f"  🔧 Remediation required before proceeding")
    
    return success_rate >= 75

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
