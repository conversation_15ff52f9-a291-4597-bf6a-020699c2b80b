#!/usr/bin/env python3
"""
Caching Implementation Test Suite
================================

Comprehensive test suite for Task 3.2: Multi-Level Caching Implementation.
Tests cache performance, invalidation, and integration with the async analysis engine.
"""

import asyncio
import time
import sys
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Any, List
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_memory_cache():
    """Test LRU memory cache functionality"""
    print("🧪 Testing LRU Memory Cache")
    print("=" * 28)
    
    try:
        from vibe_check.core.caching.cache_engine import LRUMemoryCache, CacheConfig
        
        config = CacheConfig(memory_cache_size=5, memory_cache_ttl=2.0)
        cache = LRUMemoryCache(config)
        
        # Test basic operations
        await cache.set("key1", "value1")
        await cache.set("key2", "value2")
        await cache.set("key3", "value3")
        
        # Test retrieval
        value1 = await cache.get("key1")
        value2 = await cache.get("key2")
        
        print(f"  ✅ Basic operations:")
        print(f"    • Set/Get working: {value1 == 'value1' and value2 == 'value2'}")
        
        # Test LRU eviction
        for i in range(4, 10):
            await cache.set(f"key{i}", f"value{i}")
        
        # key1 should be evicted
        evicted_value = await cache.get("key1")
        recent_value = await cache.get("key8")
        
        print(f"  ✅ LRU eviction:")
        print(f"    • Old key evicted: {evicted_value is None}")
        print(f"    • Recent key preserved: {recent_value == 'value8'}")
        
        # Test TTL expiration
        await cache.set("ttl_key", "ttl_value", ttl=0.1)
        immediate_value = await cache.get("ttl_key")
        
        await asyncio.sleep(0.2)
        expired_value = await cache.get("ttl_key")
        
        print(f"  ✅ TTL expiration:")
        print(f"    • Immediate retrieval: {immediate_value == 'ttl_value'}")
        print(f"    • After expiration: {expired_value is None}")
        
        # Test statistics
        stats = cache.get_stats()
        print(f"  📊 Cache statistics:")
        print(f"    • Hits: {stats.hits}")
        print(f"    • Misses: {stats.misses}")
        print(f"    • Hit ratio: {stats.hit_ratio:.2f}")
        
        return {
            'basic_operations': value1 == 'value1' and value2 == 'value2',
            'lru_eviction': evicted_value is None and recent_value == 'value8',
            'ttl_expiration': immediate_value == 'ttl_value' and expired_value is None,
            'hit_ratio': stats.hit_ratio
        }
        
    except Exception as e:
        print(f"❌ Memory cache test failed: {e}")
        return {}


async def test_disk_cache():
    """Test disk cache functionality"""
    print("\n🧪 Testing Disk Cache")
    print("=" * 21)
    
    try:
        from vibe_check.core.caching.cache_engine import DiskCache, CacheConfig
        
        # Use temporary directory
        with tempfile.TemporaryDirectory() as temp_dir:
            config = CacheConfig(
                disk_cache_enabled=True,
                disk_cache_dir=Path(temp_dir),
                disk_cache_ttl=2.0,
                compression_enabled=True
            )
            cache = DiskCache(config)
            
            # Test basic operations
            test_data = {"complex": "data", "with": [1, 2, 3], "nested": {"dict": True}}
            
            await cache.set("disk_key1", test_data)
            await cache.set("disk_key2", "simple_string")
            
            # Test retrieval
            retrieved_data = await cache.get("disk_key1")
            retrieved_string = await cache.get("disk_key2")
            
            print(f"  ✅ Basic operations:")
            print(f"    • Complex data: {retrieved_data == test_data}")
            print(f"    • Simple string: {retrieved_string == 'simple_string'}")
            
            # Test persistence (simulate restart)
            cache.cleanup()
            cache2 = DiskCache(config)
            
            persistent_data = await cache2.get("disk_key1")
            print(f"  ✅ Persistence: {persistent_data == test_data}")
            
            # Test TTL expiration
            await cache2.set("ttl_disk_key", "ttl_value", ttl=0.1)
            immediate_value = await cache2.get("ttl_disk_key")
            
            await asyncio.sleep(0.2)
            expired_value = await cache2.get("ttl_disk_key")
            
            print(f"  ✅ TTL expiration:")
            print(f"    • Immediate: {immediate_value == 'ttl_value'}")
            print(f"    • Expired: {expired_value is None}")
            
            cache2.cleanup()
            
            return {
                'basic_operations': retrieved_data == test_data and retrieved_string == 'simple_string',
                'persistence': persistent_data == test_data,
                'ttl_expiration': immediate_value == 'ttl_value' and expired_value is None
            }
    
    except Exception as e:
        print(f"❌ Disk cache test failed: {e}")
        return {}


async def test_multi_level_cache():
    """Test multi-level cache functionality"""
    print("\n🧪 Testing Multi-Level Cache")
    print("=" * 30)
    
    try:
        from vibe_check.core.caching.cache_engine import MultiLevelCache, CacheConfig
        
        with tempfile.TemporaryDirectory() as temp_dir:
            config = CacheConfig(
                memory_cache_size=3,
                memory_cache_ttl=10.0,
                disk_cache_enabled=True,
                disk_cache_dir=Path(temp_dir),
                disk_cache_ttl=20.0
            )
            cache = MultiLevelCache(config)
            
            # Test cache hierarchy
            await cache.set("ml_key1", "value1")
            await cache.set("ml_key2", "value2")
            await cache.set("ml_key3", "value3")
            
            # All should be in memory
            value1 = await cache.get("ml_key1")  # Memory hit
            
            # Add more items to evict from memory
            await cache.set("ml_key4", "value4")
            await cache.set("ml_key5", "value5")
            
            # key1 should be evicted from memory but available from disk
            value1_from_disk = await cache.get("ml_key1")  # Disk hit, promoted to memory
            value5_from_memory = await cache.get("ml_key5")  # Memory hit
            
            print(f"  ✅ Multi-level retrieval:")
            print(f"    • Memory hit: {value1 == 'value1'}")
            print(f"    • Disk hit (promoted): {value1_from_disk == 'value1'}")
            print(f"    • Memory hit after promotion: {value5_from_memory == 'value5'}")
            
            # Test statistics
            stats = cache.get_combined_stats()
            print(f"  📊 Combined statistics:")
            print(f"    • Memory hits: {stats['memory']['hits']}")
            print(f"    • Disk hits: {stats['disk']['hits']}")
            print(f"    • Combined hit ratio: {stats['combined']['total_hit_ratio']:.2f}")
            
            cache.cleanup()
            
            return {
                'multi_level_retrieval': all([
                    value1 == 'value1',
                    value1_from_disk == 'value1',
                    value5_from_memory == 'value5'
                ]),
                'combined_hit_ratio': stats['combined']['total_hit_ratio']
            }
    
    except Exception as e:
        print(f"❌ Multi-level cache test failed: {e}")
        return {}


async def test_cache_invalidation():
    """Test intelligent cache invalidation"""
    print("\n🧪 Testing Cache Invalidation")
    print("=" * 31)
    
    try:
        from vibe_check.core.caching.cache_engine import MultiLevelCache, CacheConfig
        from vibe_check.core.caching.cache_invalidation import SmartCacheManager
        
        with tempfile.TemporaryDirectory() as temp_dir:
            config = CacheConfig(
                memory_cache_size=10,
                disk_cache_enabled=True,
                disk_cache_dir=Path(temp_dir),
                dependency_tracking=True,
                auto_invalidation=True
            )
            
            cache = MultiLevelCache(config)
            manager = SmartCacheManager(cache, config)
            
            # Test dependency tracking
            await manager.set("parent_key", "parent_value", dependencies=["dep1", "dep2"])
            await manager.set("child_key", "child_value", dependencies=["dep1"])
            await manager.set("independent_key", "independent_value")
            
            # Verify initial state
            parent_value = await manager.get("parent_key")
            child_value = await manager.get("child_key")
            independent_value = await manager.get("independent_key")
            
            print(f"  ✅ Initial state:")
            print(f"    • Parent: {parent_value == 'parent_value'}")
            print(f"    • Child: {child_value == 'child_value'}")
            print(f"    • Independent: {independent_value == 'independent_value'}")
            
            # Test dependency invalidation
            invalidated_count = await manager.invalidate_dependency("dep1")
            
            # Check what was invalidated
            parent_after = await manager.get("parent_key")
            child_after = await manager.get("child_key")
            independent_after = await manager.get("independent_key")
            
            print(f"  ✅ Dependency invalidation:")
            print(f"    • Invalidated count: {invalidated_count}")
            print(f"    • Parent invalidated: {parent_after is None}")
            print(f"    • Child invalidated: {child_after is None}")
            print(f"    • Independent preserved: {independent_after == 'independent_value'}")
            
            # Test invalidation statistics
            stats = manager.get_stats()
            print(f"  📊 Invalidation statistics:")
            print(f"    • Invalidations: {stats['invalidation']['invalidations_count']}")
            print(f"    • Dependencies tracked: {stats['invalidation']['dependency_count']}")
            
            manager.cleanup()
            
            return {
                'dependency_tracking': all([
                    parent_value == 'parent_value',
                    child_value == 'child_value',
                    independent_value == 'independent_value'
                ]),
                'dependency_invalidation': all([
                    invalidated_count >= 2,
                    parent_after is None,
                    child_after is None,
                    independent_after == 'independent_value'
                ])
            }
    
    except Exception as e:
        print(f"❌ Cache invalidation test failed: {e}")
        return {}


async def test_cached_analysis_engine():
    """Test cached analysis engine performance"""
    print("\n🧪 Testing Cached Analysis Engine")
    print("=" * 35)
    
    try:
        from vibe_check.core.caching.cached_analyzer import CachedAsyncAnalysisEngine, CachedAnalysisConfig
        
        # Create test files
        test_dir = Path("cached_analysis_test")
        test_dir.mkdir(exist_ok=True)
        
        # Create 50 test files
        for i in range(50):
            test_file = test_dir / f"cached_test_{i}.py"
            test_file.write_text(f'''
def cached_function_{i}():
    """Cached test function {i}"""
    result = []
    for j in range(10):
        if j % 2 == 0:
            try:
                result.append(j * {i})
            except Exception:
                pass
        else:
            result.append(j + {i})
    return result

class CachedClass_{i}:
    def __init__(self):
        self.value = {i}
    
    def process(self):
        return self.value * 2
''')
        
        print(f"📁 Created {len(list(test_dir.glob('*.py')))} test files")
        
        # Test cached analysis
        config = CachedAnalysisConfig(
            max_workers=4,
            max_concurrent_files=20,
            enable_file_cache=True,
            enable_project_cache=True,
            file_cache_ttl=60.0,
            project_cache_ttl=120.0
        )
        
        engine = CachedAsyncAnalysisEngine(config)
        
        # First analysis (cache miss)
        print("  🔄 First analysis (cache miss)...")
        start_time = time.time()
        result1 = await engine.analyze_project_cached(test_dir)
        first_analysis_time = time.time() - start_time
        
        print(f"    • Files analyzed: {result1.files_analyzed}")
        print(f"    • Analysis time: {first_analysis_time:.3f}s")
        print(f"    • Speed: {result1.files_analyzed / first_analysis_time:.1f} files/sec")
        
        # Second analysis (cache hit)
        print("  ⚡ Second analysis (cache hit)...")
        start_time = time.time()
        result2 = await engine.analyze_project_cached(test_dir)
        second_analysis_time = time.time() - start_time
        
        print(f"    • Files analyzed: {result2.files_analyzed}")
        print(f"    • Analysis time: {second_analysis_time:.3f}s")
        print(f"    • Speed: {result2.files_analyzed / second_analysis_time:.1f} files/sec")
        
        # Calculate cache performance improvement
        cache_speedup = first_analysis_time / second_analysis_time if second_analysis_time > 0 else 0
        
        print(f"  🚀 Cache performance:")
        print(f"    • Speedup: {cache_speedup:.1f}x")
        print(f"    • Time reduction: {((first_analysis_time - second_analysis_time) / first_analysis_time * 100):.1f}%")
        
        # Test cache statistics
        cache_stats = engine.get_cache_stats()
        print(f"  📊 Cache statistics:")
        print(f"    • Cache hits: {cache_stats['engine_stats']['cache_hits']}")
        print(f"    • Cache misses: {cache_stats['engine_stats']['cache_misses']}")
        print(f"    • Hit ratio: {cache_stats['engine_stats']['hit_ratio']:.2f}")
        
        engine.cleanup()
        
        # Cleanup
        shutil.rmtree(test_dir, ignore_errors=True)
        
        return {
            'first_analysis_time': first_analysis_time,
            'second_analysis_time': second_analysis_time,
            'cache_speedup': cache_speedup,
            'files_analyzed': result1.files_analyzed,
            'cache_hit_ratio': cache_stats['engine_stats']['hit_ratio']
        }
    
    except Exception as e:
        print(f"❌ Cached analysis engine test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def test_cache_performance():
    """Test overall cache performance"""
    print("\n🧪 Testing Cache Performance")
    print("=" * 29)
    
    try:
        from vibe_check.core.caching.cache_engine import MultiLevelCache, CacheConfig
        
        with tempfile.TemporaryDirectory() as temp_dir:
            config = CacheConfig(
                memory_cache_size=1000,
                disk_cache_enabled=True,
                disk_cache_dir=Path(temp_dir),
                compression_enabled=True,
                async_write_enabled=True
            )
            cache = MultiLevelCache(config)
            
            # Test write performance
            print("  🔄 Testing write performance...")
            write_start = time.time()
            
            write_tasks = []
            for i in range(500):
                task = cache.set(f"perf_key_{i}", f"performance_value_{i}" * 10)
                write_tasks.append(task)
            
            await asyncio.gather(*write_tasks)
            write_time = time.time() - write_start
            write_ops_per_sec = 500 / write_time
            
            print(f"    • Write operations: 500")
            print(f"    • Write time: {write_time:.3f}s")
            print(f"    • Write speed: {write_ops_per_sec:.1f} ops/sec")
            
            # Test read performance
            print("  ⚡ Testing read performance...")
            read_start = time.time()
            
            read_tasks = []
            for i in range(500):
                task = cache.get(f"perf_key_{i}")
                read_tasks.append(task)
            
            read_results = await asyncio.gather(*read_tasks)
            read_time = time.time() - read_start
            read_ops_per_sec = 500 / read_time
            
            successful_reads = sum(1 for result in read_results if result is not None)
            
            print(f"    • Read operations: 500")
            print(f"    • Successful reads: {successful_reads}")
            print(f"    • Read time: {read_time:.3f}s")
            print(f"    • Read speed: {read_ops_per_sec:.1f} ops/sec")
            
            # Test mixed workload
            print("  🔀 Testing mixed workload...")
            mixed_start = time.time()
            
            mixed_tasks = []
            for i in range(200):
                # 70% reads, 30% writes
                if i % 10 < 7:
                    task = cache.get(f"perf_key_{i % 500}")
                else:
                    task = cache.set(f"mixed_key_{i}", f"mixed_value_{i}")
                mixed_tasks.append(task)
            
            await asyncio.gather(*mixed_tasks)
            mixed_time = time.time() - mixed_start
            mixed_ops_per_sec = 200 / mixed_time
            
            print(f"    • Mixed operations: 200")
            print(f"    • Mixed time: {mixed_time:.3f}s")
            print(f"    • Mixed speed: {mixed_ops_per_sec:.1f} ops/sec")
            
            cache.cleanup()
            
            return {
                'write_ops_per_sec': write_ops_per_sec,
                'read_ops_per_sec': read_ops_per_sec,
                'mixed_ops_per_sec': mixed_ops_per_sec,
                'successful_reads': successful_reads
            }
    
    except Exception as e:
        print(f"❌ Cache performance test failed: {e}")
        return {}


async def main():
    """Main test function"""
    print("🚀 Caching Implementation Test Suite - Task 3.2")
    print("=" * 55)
    
    # Run all tests
    memory_results = await test_memory_cache()
    disk_results = await test_disk_cache()
    multi_level_results = await test_multi_level_cache()
    invalidation_results = await test_cache_invalidation()
    analysis_results = await test_cached_analysis_engine()
    performance_results = await test_cache_performance()
    
    print("\n" + "=" * 55)
    print("📊 CACHING IMPLEMENTATION SUMMARY")
    print("=" * 55)
    
    # Evaluate results
    targets_met = 0
    total_targets = 6
    
    # Target 1: Memory cache functionality
    if memory_results.get('basic_operations') and memory_results.get('lru_eviction'):
        print("  ✅ Memory cache functionality working")
        targets_met += 1
    else:
        print("  ❌ Memory cache functionality issues")
    
    # Target 2: Disk cache functionality
    if disk_results.get('basic_operations') and disk_results.get('persistence'):
        print("  ✅ Disk cache functionality working")
        targets_met += 1
    else:
        print("  ❌ Disk cache functionality issues")
    
    # Target 3: Multi-level cache integration
    if multi_level_results.get('multi_level_retrieval'):
        print("  ✅ Multi-level cache integration working")
        targets_met += 1
    else:
        print("  ❌ Multi-level cache integration issues")
    
    # Target 4: Cache invalidation
    if invalidation_results.get('dependency_tracking') and invalidation_results.get('dependency_invalidation'):
        print("  ✅ Cache invalidation working")
        targets_met += 1
    else:
        print("  ❌ Cache invalidation issues")
    
    # Target 5: Cached analysis performance
    if analysis_results.get('cache_speedup', 0) >= 2.0:  # At least 2x speedup
        print(f"  ✅ Cached analysis performance: {analysis_results.get('cache_speedup', 0):.1f}x speedup")
        targets_met += 1
    else:
        print(f"  ⚠️  Cached analysis speedup: {analysis_results.get('cache_speedup', 0):.1f}x (target: 2.0x)")
    
    # Target 6: Cache performance
    if performance_results.get('read_ops_per_sec', 0) >= 1000:  # At least 1000 ops/sec
        print(f"  ✅ Cache performance: {performance_results.get('read_ops_per_sec', 0):.1f} ops/sec")
        targets_met += 1
    else:
        print(f"  ⚠️  Cache performance: {performance_results.get('read_ops_per_sec', 0):.1f} ops/sec (target: 1000)")
    
    print(f"\n🎯 Overall Score: {targets_met}/{total_targets} targets met")
    
    if targets_met >= 5:
        print("✅ Task 3.2: Multi-Level Caching Implementation SUCCESSFUL")
        print("🚀 Ready to proceed with Task 3.3: Performance Optimization")
        
        # Show key achievements
        print(f"\n🏆 Key Achievements:")
        if analysis_results:
            print(f"  • Analysis speedup: {analysis_results.get('cache_speedup', 0):.1f}x")
            print(f"  • Cache hit ratio: {analysis_results.get('cache_hit_ratio', 0):.2f}")
        if performance_results:
            print(f"  • Read performance: {performance_results.get('read_ops_per_sec', 0):.1f} ops/sec")
            print(f"  • Write performance: {performance_results.get('write_ops_per_sec', 0):.1f} ops/sec")
        print(f"  • Multi-level caching with LRU + disk")
        print(f"  • Intelligent invalidation with dependency tracking")
        print(f"  • Seamless integration with async analysis engine")
        
        return 0
    else:
        print("⚠️  Task 3.2: Multi-Level Caching Implementation needs improvement")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
