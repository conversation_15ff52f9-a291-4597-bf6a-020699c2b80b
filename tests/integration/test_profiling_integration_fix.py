#!/usr/bin/env python3
"""
Profiling Integration Fix Test
=============================

Test to verify the profiling collector integration fix works.
"""

import asyncio
import time
import sys
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))


async def test_profiling_collector_import_fix():
    """Test that profiling collector can be imported after fix"""
    print("🧪 Testing Profiling Collector Import Fix")
    print("=" * 42)
    
    try:
        # Direct import test
        sys.path.insert(0, str(Path(__file__).parent / "vibe_check" / "monitoring"))
        
        # Test profiling collector import
        from collectors.profiling_collector import ProfilingMetricsCollector
        from profiling.execution_profiler import ExecutionProfiler
        
        print(f"  ✅ Import successful:")
        print(f"    • ProfilingMetricsCollector imported")
        print(f"    • ExecutionProfiler imported")
        
        # Test collector creation
        profiler = ExecutionProfiler(enable_memory_tracking=True)
        collector = ProfilingMetricsCollector(profiler)
        
        print(f"  ✅ Collector created:")
        print(f"    • Name: {collector.name}")
        print(f"    • Profiler attached: {collector.profiler is not None}")
        
        # Test metric registration
        metric_definitions = collector.get_metric_definitions()
        
        print(f"  ✅ Metrics registered:")
        print(f"    • Total metrics: {len(metric_definitions)}")
        
        # Test basic profiling session
        session_id = profiler.start_profiling("integration_test")
        
        @profiler.profile_function(name="test_function")
        def test_function():
            time.sleep(0.001)
            return "test_complete"
        
        # Execute function
        result = test_function()
        
        # Stop profiling
        session = profiler.stop_profiling()
        
        # Test metrics collection
        metrics = await collector.collect_metrics()
        
        print(f"  ✅ Integration test:")
        print(f"    • Session completed: {session is not None}")
        print(f"    • Metrics collected: {len(metrics)}")
        print(f"    • Function result: {result}")
        
        collector.cleanup()
        
        return {
            'import_successful': True,
            'collector_created': collector.name == "ProfilingMetricsCollector",
            'metrics_registered': len(metric_definitions) > 0,
            'profiling_works': session is not None,
            'metrics_collected': len(metrics) > 0,
            'integration_complete': True
        }
        
    except Exception as e:
        print(f"❌ Profiling collector import fix test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def test_end_to_end_profiling():
    """Test end-to-end profiling with collector integration"""
    print("\n🧪 Testing End-to-End Profiling")
    print("=" * 32)
    
    try:
        from collectors.profiling_collector import ProfilingMetricsCollector
        from profiling.execution_profiler import ExecutionProfiler
        from collectors.base_collector import CollectorConfig, CollectionInterval
        
        # Create profiler and collector
        profiler = ExecutionProfiler(
            enable_memory_tracking=True,
            bottleneck_threshold=0.005
        )
        
        config = CollectorConfig(
            collection_interval=CollectionInterval.FAST.value,
            max_collection_time=3.0,
            labels={"test": "end_to_end"}
        )
        
        collector = ProfilingMetricsCollector(profiler, config)
        
        print(f"  ✅ Setup complete:")
        print(f"    • Profiler created")
        print(f"    • Collector configured")
        
        # Start profiling session
        session_id = profiler.start_profiling("end_to_end_test")
        
        # Define test functions
        @profiler.profile_function(name="fast_function")
        def fast_function():
            return sum(range(100))
        
        @profiler.profile_function(name="slow_function")
        def slow_function():
            time.sleep(0.01)  # 10ms - should be bottleneck
            return "slow_complete"
        
        @profiler.profile_function(name="main_function")
        def main_function():
            fast_function()
            slow_function()
            return "main_complete"
        
        print(f"  🔄 Executing profiled functions...")
        
        # Execute functions
        result = main_function()
        
        # Stop profiling
        session = profiler.stop_profiling()
        
        # Collect metrics
        metrics = await collector.collect_metrics()
        
        # Analyze results
        if metrics:
            session_metrics = [m for m in metrics if 'session' in m.name]
            function_metrics = [m for m in metrics if 'function' in m.name]
            bottleneck_metrics = [m for m in metrics if 'bottleneck' in m.name]
            
            print(f"  ✅ Results analysis:")
            print(f"    • Session metrics: {len(session_metrics)}")
            print(f"    • Function metrics: {len(function_metrics)}")
            print(f"    • Bottleneck metrics: {len(bottleneck_metrics)}")
        
        # Get call graph
        call_graph = profiler.get_call_graph(session_id)
        
        # Get bottlenecks
        bottlenecks = profiler.get_bottlenecks(session_id)
        
        print(f"  📊 Profiling results:")
        print(f"    • Call graph generated: {call_graph is not None}")
        print(f"    • Bottlenecks detected: {len(bottlenecks)}")
        print(f"    • Total functions: {call_graph['total_functions'] if call_graph else 0}")
        
        # Get summary
        summary = collector.get_profiling_summary()
        
        print(f"  📈 Summary:")
        print(f"    • Total sessions: {summary['total_sessions']}")
        print(f"    • Function calls: {summary['total_function_calls']}")
        print(f"    • Execution time: {summary['total_execution_time']:.6f}s")
        
        collector.cleanup()
        
        return {
            'end_to_end_successful': True,
            'session_completed': session is not None,
            'metrics_collected': len(metrics) > 0,
            'call_graph_generated': call_graph is not None,
            'bottlenecks_detected': len(bottlenecks) > 0,
            'function_calls_tracked': summary['total_function_calls'] > 0,
            'integration_working': True
        }
        
    except Exception as e:
        print(f"❌ End-to-end profiling test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def main():
    """Main test function"""
    print("🚀 Profiling Integration Fix Test Suite")
    print("=" * 45)
    
    # Run tests
    import_results = await test_profiling_collector_import_fix()
    e2e_results = await test_end_to_end_profiling()
    
    print("\n" + "=" * 45)
    print("📊 PROFILING INTEGRATION FIX SUMMARY")
    print("=" * 45)
    
    # Evaluate results
    targets_met = 0
    total_targets = 3
    
    # Target 1: Import fix working
    if (import_results.get('import_successful') and 
        import_results.get('collector_created') and 
        import_results.get('integration_complete')):
        print("  ✅ Import fix working")
        targets_met += 1
    else:
        print("  ❌ Import fix issues")
    
    # Target 2: Metrics collection working
    if (import_results.get('metrics_collected') and 
        e2e_results.get('metrics_collected')):
        print("  ✅ Metrics collection working")
        targets_met += 1
    else:
        print("  ❌ Metrics collection issues")
    
    # Target 3: End-to-end integration working
    if (e2e_results.get('end_to_end_successful') and 
        e2e_results.get('call_graph_generated') and 
        e2e_results.get('bottlenecks_detected')):
        print("  ✅ End-to-end integration working")
        targets_met += 1
    else:
        print("  ❌ End-to-end integration issues")
    
    print(f"\n🎯 Overall Score: {targets_met}/{total_targets} targets met")
    
    if targets_met >= 2:
        print("✅ Profiling Collector Integration Fix SUCCESSFUL")
        print("🔧 Critical integration issues resolved")
        
        print(f"\n🏆 Key Fixes:")
        if import_results:
            print(f"  • Import fix: {import_results.get('import_successful', False)}")
            print(f"  • Collector creation: {import_results.get('collector_created', False)}")
        if e2e_results:
            print(f"  • End-to-end flow: {e2e_results.get('integration_working', False)}")
            print(f"  • Call graph: {e2e_results.get('call_graph_generated', False)}")
            print(f"  • Bottlenecks: {e2e_results.get('bottlenecks_detected', False)}")
        print(f"  • Metrics pipeline to TSDB restored")
        print(f"  • Profiling data flow working")
        
        return 0
    else:
        print("⚠️  Profiling Collector Integration Fix needs more work")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
