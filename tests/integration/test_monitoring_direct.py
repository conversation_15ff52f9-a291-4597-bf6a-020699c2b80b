#!/usr/bin/env python3
"""
Direct Monitoring Module Test
============================

Test the monitoring module directly without importing the main vibe_check package.
This isolates the profiling integration testing from other package issues.
"""

import asyncio
import time
import sys
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add monitoring module directly to path
monitoring_path = Path(__file__).parent / "vibe_check" / "monitoring"
sys.path.insert(0, str(monitoring_path))


async def test_direct_profiling_imports():
    """Test profiling imports directly from monitoring module"""
    print("🧪 Testing Direct Profiling Imports")
    print("=" * 36)
    
    try:
        # Import directly from monitoring module
        from profiling.execution_profiler import ExecutionProfiler, get_profiler
        from collectors.profiling_collector import ProfilingMetricsCollector
        from collectors.base_collector import CollectorConfig, CollectionInterval
        
        print(f"  ✅ Direct imports successful:")
        print(f"    • ExecutionProfiler imported")
        print(f"    • ProfilingMetricsCollector imported")
        print(f"    • Base collector components imported")
        
        # Test object creation
        profiler = ExecutionProfiler(enable_memory_tracking=True)
        
        config = CollectorConfig(
            collection_interval=CollectionInterval.FAST.value,
            max_collection_time=3.0,
            labels={"test": "direct_monitoring"}
        )
        
        collector = ProfilingMetricsCollector(profiler, config)
        
        print(f"  ✅ Objects created successfully:")
        print(f"    • Profiler type: {type(profiler).__name__}")
        print(f"    • Collector name: {collector.name}")
        print(f"    • Config enabled: {config.enabled}")
        
        # Test metric registration
        metric_definitions = collector.get_metric_definitions()
        
        session_metrics = [name for name in metric_definitions if 'session' in name]
        function_metrics = [name for name in metric_definitions if 'function' in name]
        bottleneck_metrics = [name for name in metric_definitions if 'bottleneck' in name]
        
        print(f"  ✅ Metrics registered:")
        print(f"    • Total metrics: {len(metric_definitions)}")
        print(f"    • Session metrics: {len(session_metrics)}")
        print(f"    • Function metrics: {len(function_metrics)}")
        print(f"    • Bottleneck metrics: {len(bottleneck_metrics)}")
        
        collector.cleanup()
        
        return {
            'direct_imports_successful': True,
            'objects_created': True,
            'metrics_registered': len(metric_definitions) > 0,
            'collector_functional': collector.name == "ProfilingMetricsCollector",
            'metric_categories': len(session_metrics) > 0 and len(function_metrics) > 0
        }
        
    except Exception as e:
        print(f"❌ Direct profiling imports test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def test_profiling_execution_flow():
    """Test complete profiling execution flow"""
    print("\n🧪 Testing Profiling Execution Flow")
    print("=" * 36)
    
    try:
        from profiling.execution_profiler import ExecutionProfiler
        from collectors.profiling_collector import ProfilingMetricsCollector
        from collectors.base_collector import CollectorConfig, CollectionInterval
        
        # Create profiler with specific settings
        profiler = ExecutionProfiler(
            enable_memory_tracking=True,
            max_call_depth=20,
            bottleneck_threshold=0.003  # 3ms threshold
        )
        
        config = CollectorConfig(
            collection_interval=CollectionInterval.FAST.value,
            max_collection_time=5.0,
            labels={"test": "execution_flow"}
        )
        
        collector = ProfilingMetricsCollector(profiler, config)
        
        print(f"  ✅ Setup completed:")
        print(f"    • Profiler configured with {profiler.max_call_depth} max depth")
        print(f"    • Bottleneck threshold: {profiler.bottleneck_threshold}s")
        print(f"    • Memory tracking: {profiler.enable_memory_tracking}")
        
        # Start profiling session
        session_id = profiler.start_profiling("execution_flow_test")
        
        # Define test functions with clear performance characteristics
        @profiler.profile_function(name="quick_calculation")
        def quick_calculation():
            """Quick calculation - should not be bottleneck"""
            return sum(x * x for x in range(50))
        
        @profiler.profile_function(name="slow_operation")
        def slow_operation():
            """Slow operation - should be bottleneck"""
            time.sleep(0.005)  # 5ms - above threshold
            return "slow_done"
        
        @profiler.profile_function(name="recursive_function")
        def recursive_function(n: int) -> int:
            """Recursive function to test call graph"""
            if n <= 1:
                return n
            return recursive_function(n - 1) + recursive_function(n - 2)
        
        @profiler.profile_function(name="main_workflow")
        def main_workflow():
            """Main workflow that calls other functions"""
            quick_calculation()
            slow_operation()
            fib_result = recursive_function(6)  # Small fibonacci
            return f"workflow_complete_fib_{fib_result}"
        
        print(f"  🔄 Executing profiled workflow...")
        
        # Execute the workflow
        start_time = time.time()
        result = main_workflow()
        execution_time = time.time() - start_time
        
        print(f"    • Workflow result: {result}")
        print(f"    • Total execution time: {execution_time:.6f}s")
        
        # Stop profiling
        session = profiler.stop_profiling()
        
        print(f"  ✅ Profiling session completed:")
        print(f"    • Session ID: {session.session_id}")
        print(f"    • Total functions called: {session.total_functions}")
        print(f"    • Session duration: {session.total_duration:.6f}s")
        print(f"    • Root frames: {len(session.root_frames)}")
        
        # Collect metrics
        print("  🔄 Collecting profiling metrics...")
        metrics_start = time.time()
        
        metrics = await collector.collect_metrics()
        
        metrics_time = time.time() - metrics_start
        
        print(f"    • Metrics collected: {len(metrics)}")
        print(f"    • Collection time: {metrics_time:.3f}s")
        
        # Analyze metrics
        if metrics:
            session_count = len([m for m in metrics if 'session' in m.name])
            function_calls = len([m for m in metrics if 'function_calls' in m.name])
            duration_metrics = len([m for m in metrics if 'duration' in m.name])
            bottleneck_count = len([m for m in metrics if 'bottleneck' in m.name])
            
            print(f"  📊 Metrics breakdown:")
            print(f"    • Session metrics: {session_count}")
            print(f"    • Function call metrics: {function_calls}")
            print(f"    • Duration metrics: {duration_metrics}")
            print(f"    • Bottleneck metrics: {bottleneck_count}")
        
        # Get call graph
        call_graph = profiler.get_call_graph(session_id)
        
        # Get bottlenecks
        bottlenecks = profiler.get_bottlenecks(session_id)
        
        print(f"  🌳 Call graph analysis:")
        print(f"    • Call graph generated: {call_graph is not None}")
        if call_graph:
            print(f"    • Total functions in graph: {call_graph['total_functions']}")
            print(f"    • Root frames: {len(call_graph['root_frames'])}")
        
        print(f"  🔍 Bottleneck analysis:")
        print(f"    • Bottlenecks detected: {len(bottlenecks)}")
        for i, bottleneck in enumerate(bottlenecks[:3]):
            print(f"    • Bottleneck {i+1}: {bottleneck['function']} ({bottleneck['duration']:.6f}s)")
        
        # Get function statistics
        function_stats = profiler.get_function_statistics()
        
        print(f"  📈 Function statistics:")
        print(f"    • Unique functions tracked: {len(function_stats)}")
        
        for func_name, stats in list(function_stats.items())[:3]:
            print(f"    • {func_name}: {stats['call_count']} calls, {stats['avg_time']:.6f}s avg")
        
        # Get summary
        summary = collector.get_profiling_summary()
        
        print(f"  📋 Execution flow summary:")
        print(f"    • Total sessions: {summary['total_sessions']}")
        print(f"    • Total function calls: {summary['total_function_calls']}")
        print(f"    • Total execution time: {summary['total_execution_time']:.6f}s")
        print(f"    • Profiler overhead: {summary['profiler_overhead']:.4f}%")
        
        collector.cleanup()
        
        return {
            'execution_flow_successful': True,
            'session_completed': session is not None,
            'metrics_collected': len(metrics) > 0,
            'call_graph_generated': call_graph is not None,
            'bottlenecks_detected': len(bottlenecks) > 0,
            'function_stats_available': len(function_stats) > 0,
            'recursive_calls_tracked': session.total_functions > 10,  # Fibonacci should create multiple calls
            'workflow_executed': result.startswith("workflow_complete")
        }
        
    except Exception as e:
        print(f"❌ Profiling execution flow test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def test_metrics_tsdb_compatibility():
    """Test that metrics are compatible with TSDB format"""
    print("\n🧪 Testing Metrics TSDB Compatibility")
    print("=" * 38)
    
    try:
        from profiling.execution_profiler import ExecutionProfiler
        from collectors.profiling_collector import ProfilingMetricsCollector
        
        # Minimal setup for TSDB compatibility test
        profiler = ExecutionProfiler()
        collector = ProfilingMetricsCollector(profiler)
        
        # Quick profiling session
        session_id = profiler.start_profiling("tsdb_compatibility")
        
        @profiler.profile_function(name="tsdb_test_func")
        def tsdb_test_func():
            time.sleep(0.001)
            return "tsdb_test_complete"
        
        # Execute function
        result = tsdb_test_func()
        
        # Stop profiling
        session = profiler.stop_profiling()
        
        # Collect metrics
        metrics = await collector.collect_metrics()
        
        print(f"  ✅ TSDB compatibility test:")
        print(f"    • Metrics generated: {len(metrics)}")
        
        # Validate TSDB format requirements
        valid_metrics = 0
        sample_metric = None
        
        for metric in metrics:
            # Check required attributes for TSDB
            has_name = hasattr(metric, 'name') and metric.name
            has_value = hasattr(metric, 'value') and metric.value is not None
            has_labels = hasattr(metric, 'labels') and isinstance(metric.labels, dict)
            has_timestamp = hasattr(metric, 'timestamp') and metric.timestamp is not None
            
            if has_name and has_value and has_labels and has_timestamp:
                valid_metrics += 1
                if sample_metric is None:
                    sample_metric = metric
        
        print(f"    • TSDB-compatible metrics: {valid_metrics}/{len(metrics)}")
        print(f"    • Compatibility rate: {(valid_metrics/len(metrics)*100):.1f}%" if metrics else "0%")
        
        # Show sample metric structure
        if sample_metric:
            print(f"    • Sample metric structure:")
            print(f"      - Name: '{sample_metric.name}'")
            print(f"      - Value: {sample_metric.value}")
            print(f"      - Labels: {sample_metric.labels}")
            print(f"      - Timestamp: {sample_metric.timestamp}")
            print(f"      - Type: {type(sample_metric.value).__name__}")
        
        # Test metric name patterns
        metric_names = [m.name for m in metrics]
        profiling_metrics = [name for name in metric_names if 'profiling_' in name]
        
        print(f"    • Metric naming:")
        print(f"      - Total metrics: {len(metric_names)}")
        print(f"      - Profiling metrics: {len(profiling_metrics)}")
        print(f"      - Sample names: {metric_names[:3]}")
        
        collector.cleanup()
        
        return {
            'metrics_generated': len(metrics) > 0,
            'tsdb_compatible': valid_metrics == len(metrics),
            'metric_structure_valid': sample_metric is not None,
            'profiling_metrics_present': len(profiling_metrics) > 0,
            'compatibility_rate': (valid_metrics/len(metrics)*100) if metrics else 0
        }
        
    except Exception as e:
        print(f"❌ Metrics TSDB compatibility test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}


async def main():
    """Main test function"""
    print("🚀 Direct Monitoring Module Test Suite")
    print("=" * 45)
    
    # Run tests
    import_results = await test_direct_profiling_imports()
    flow_results = await test_profiling_execution_flow()
    tsdb_results = await test_metrics_tsdb_compatibility()
    
    print("\n" + "=" * 45)
    print("📊 DIRECT MONITORING TEST SUMMARY")
    print("=" * 45)
    
    # Evaluate results
    targets_met = 0
    total_targets = 3
    
    # Target 1: Direct imports working
    if (import_results.get('direct_imports_successful') and 
        import_results.get('objects_created') and 
        import_results.get('collector_functional')):
        print("  ✅ Direct imports working")
        targets_met += 1
    else:
        print("  ❌ Direct imports failed")
    
    # Target 2: Execution flow working
    if (flow_results.get('execution_flow_successful') and 
        flow_results.get('metrics_collected') and 
        flow_results.get('call_graph_generated')):
        print("  ✅ Execution flow working")
        targets_met += 1
    else:
        print("  ❌ Execution flow failed")
    
    # Target 3: TSDB compatibility
    if (tsdb_results.get('metrics_generated') and 
        tsdb_results.get('tsdb_compatible') and 
        tsdb_results.get('compatibility_rate', 0) >= 90):
        print("  ✅ TSDB compatibility confirmed")
        targets_met += 1
    else:
        print("  ❌ TSDB compatibility issues")
    
    print(f"\n🎯 Overall Score: {targets_met}/{total_targets} targets met")
    
    if targets_met >= 2:
        print("✅ PROFILING INTEGRATION SUCCESSFULLY WORKING")
        print("🔧 Critical integration path validated")
        
        print(f"\n🏆 Key Validations:")
        if import_results:
            print(f"  • Direct imports: {import_results.get('direct_imports_successful', False)}")
            print(f"  • Metric registration: {import_results.get('metrics_registered', False)}")
        if flow_results:
            print(f"  • Execution flow: {flow_results.get('execution_flow_successful', False)}")
            print(f"  • Call graph: {flow_results.get('call_graph_generated', False)}")
            print(f"  • Bottlenecks: {flow_results.get('bottlenecks_detected', False)}")
            print(f"  • Recursive tracking: {flow_results.get('recursive_calls_tracked', False)}")
        if tsdb_results:
            print(f"  • TSDB format: {tsdb_results.get('tsdb_compatible', False)}")
            print(f"  • Compatibility: {tsdb_results.get('compatibility_rate', 0):.1f}%")
        print(f"  • Profiling data pipeline functional")
        print(f"  • Ready for TSDB integration")
        
        return 0
    else:
        print("⚠️  Profiling integration needs more work")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
