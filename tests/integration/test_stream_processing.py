#!/usr/bin/env python3
"""
Stream Processing Engine Test
=============================

Test real-time stream processing with aggregation functions and windowing operations.
"""

import asyncio
import time
from pathlib import Path
import sys

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

def print_header(title: str, level: int = 1) -> None:
    """Print formatted section header"""
    symbols = ["🚀", "🔧", "📊", "✅", "⚡"]
    symbol = symbols[min(level-1, len(symbols)-1)]
    separator = "=" * 60 if level == 1 else "-" * 50
    print(f"\n{symbol} {title}")
    print(separator)

def print_result(test_name: str, success: bool, details: str = "") -> None:
    """Print test result with formatting"""
    status = "✅" if success else "❌"
    print(f"  {status} {test_name}")
    if details:
        for line in details.split('\n'):
            if line.strip():
                print(f"    • {line.strip()}")

async def test_tumbling_windows():
    """Test tumbling window processing"""
    print_header("Tumbling Windows Test", 2)
    
    try:
        from vibe_check.monitoring.streaming import (
            StreamProcessor, WindowConfig, AggregationConfig,
            WindowType, AggregationType
        )
        from vibe_check.monitoring.collectors.base_collector import MetricValue
        
        # Create stream processor with tumbling windows
        window_config = WindowConfig(
            window_type=WindowType.TUMBLING,
            size_seconds=2.0  # 2-second windows
        )
        
        processor = StreamProcessor(window_config)
        
        # Add aggregation
        agg_config = AggregationConfig(
            aggregation_type=AggregationType.SUM,
            metric_pattern="test_metric.*",
            output_metric_name="test_sum"
        )
        processor.add_aggregation(agg_config)
        
        # Track results
        results = []
        def result_handler(result):
            results.append(result)
        
        processor.add_result_handler(result_handler)
        
        # Start processor
        await processor.start()
        
        # Generate test metrics across multiple windows
        base_time = time.time()
        test_metrics = []
        
        for i in range(10):
            metric = MetricValue(
                name="test_metric_1",
                value=float(i + 1),
                labels={"test": "tumbling"},
                timestamp=base_time + (i * 0.5)  # Spread across 5 seconds
            )
            test_metrics.append(metric)
        
        # Process metrics
        await processor.process_metrics_batch(test_metrics)
        
        # Wait for processing
        await asyncio.sleep(3.0)
        
        # Stop processor
        await processor.stop()
        
        stats = processor.get_stats()
        
        success = (
            len(results) > 0 and
            stats['processed_metrics'] == 10 and
            stats['generated_aggregations'] > 0
        )
        
        details = f"""Metrics processed: {stats['processed_metrics']}
Aggregations generated: {stats['generated_aggregations']}
Results collected: {len(results)}
Active windows: {stats['active_windows']}
Sample result: {results[0].value if results else 'None'}"""
        
        print_result("Tumbling Windows", success, details)
        return success
        
    except Exception as e:
        print_result("Tumbling Windows", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_aggregation_functions():
    """Test different aggregation functions"""
    print_header("Aggregation Functions Test", 2)
    
    try:
        from vibe_check.monitoring.streaming import (
            StreamProcessor, WindowConfig, AggregationConfig,
            WindowType, AggregationType
        )
        from vibe_check.monitoring.collectors.base_collector import MetricValue
        
        # Create processor
        window_config = WindowConfig(
            window_type=WindowType.TUMBLING,
            size_seconds=1.0
        )
        
        processor = StreamProcessor(window_config)
        
        # Add multiple aggregations
        aggregations = [
            (AggregationType.SUM, "sum_result"),
            (AggregationType.AVG, "avg_result"),
            (AggregationType.MIN, "min_result"),
            (AggregationType.MAX, "max_result"),
            (AggregationType.COUNT, "count_result")
        ]
        
        for agg_type, output_name in aggregations:
            config = AggregationConfig(
                aggregation_type=agg_type,
                metric_pattern="agg_test.*",
                output_metric_name=output_name
            )
            processor.add_aggregation(config)
        
        # Track results
        results = []
        def result_handler(result):
            results.append(result)
        
        processor.add_result_handler(result_handler)
        
        await processor.start()
        
        # Generate test data with known values
        test_values = [1.0, 2.0, 3.0, 4.0, 5.0]
        base_time = time.time()
        
        for i, value in enumerate(test_values):
            metric = MetricValue(
                name="agg_test_metric",
                value=value,
                labels={"test": "aggregation"},
                timestamp=base_time + (i * 0.1)
            )
            await processor.process_metric(metric)
        
        # Wait for window to close
        await asyncio.sleep(2.0)
        await processor.stop()
        
        # Verify results
        result_by_type = {r.aggregation_type: r.value for r in results}
        
        expected_results = {
            AggregationType.SUM: 15.0,
            AggregationType.AVG: 3.0,
            AggregationType.MIN: 1.0,
            AggregationType.MAX: 5.0,
            AggregationType.COUNT: 5.0
        }
        
        success = True
        for agg_type, expected in expected_results.items():
            if agg_type not in result_by_type:
                success = False
                break
            actual = result_by_type[agg_type]
            if abs(actual - expected) > 0.001:  # Allow small floating point errors
                success = False
                break
        
        details = f"""Aggregations configured: {len(aggregations)}
Results generated: {len(results)}
Expected vs Actual:"""
        
        for agg_type, expected in expected_results.items():
            actual = result_by_type.get(agg_type, "Missing")
            details += f"\n  {agg_type.value}: {expected} vs {actual}"
        
        print_result("Aggregation Functions", success, details)
        return success
        
    except Exception as e:
        print_result("Aggregation Functions", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_sliding_windows():
    """Test sliding window processing"""
    print_header("Sliding Windows Test", 2)
    
    try:
        from vibe_check.monitoring.streaming import (
            StreamProcessor, WindowConfig, AggregationConfig,
            WindowType, AggregationType
        )
        from vibe_check.monitoring.collectors.base_collector import MetricValue
        
        # Create sliding window processor
        window_config = WindowConfig(
            window_type=WindowType.SLIDING,
            size_seconds=2.0,
            slide_seconds=1.0  # 2-second windows, sliding every 1 second
        )
        
        processor = StreamProcessor(window_config)
        
        # Add aggregation
        agg_config = AggregationConfig(
            aggregation_type=AggregationType.COUNT,
            metric_pattern="sliding_test.*",
            output_metric_name="sliding_count"
        )
        processor.add_aggregation(agg_config)
        
        # Track results
        results = []
        windows = []
        
        def result_handler(result):
            results.append(result)
        
        def window_handler(window):
            windows.append(window)
        
        processor.add_result_handler(result_handler)
        processor.add_window_handler(window_handler)
        
        await processor.start()
        
        # Generate metrics over time
        base_time = time.time()
        for i in range(5):
            metric = MetricValue(
                name="sliding_test_metric",
                value=float(i),
                labels={"test": "sliding"},
                timestamp=base_time + (i * 0.8)  # Spread over 4 seconds
            )
            await processor.process_metric(metric)
        
        # Wait for processing
        await asyncio.sleep(4.0)
        await processor.stop()
        
        stats = processor.get_stats()
        
        success = (
            len(results) > 1 and  # Should have multiple overlapping windows
            stats['processed_metrics'] == 5
        )
        
        details = f"""Metrics processed: {stats['processed_metrics']}
Windows created: {len(windows)}
Results generated: {len(results)}
Overlapping windows detected: {len(results) > 1}"""
        
        print_result("Sliding Windows", success, details)
        return success
        
    except Exception as e:
        print_result("Sliding Windows", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_performance():
    """Test stream processing performance"""
    print_header("Stream Processing Performance Test", 2)
    
    try:
        from vibe_check.monitoring.streaming import (
            StreamProcessor, WindowConfig, AggregationConfig,
            WindowType, AggregationType
        )
        from vibe_check.monitoring.collectors.base_collector import MetricValue
        
        # Create high-performance processor
        window_config = WindowConfig(
            window_type=WindowType.TUMBLING,
            size_seconds=0.5  # Fast windows
        )
        
        processor = StreamProcessor(window_config)
        
        # Add simple aggregation
        agg_config = AggregationConfig(
            aggregation_type=AggregationType.COUNT,
            metric_pattern="perf_test.*"
        )
        processor.add_aggregation(agg_config)
        
        await processor.start()
        
        # Generate many metrics quickly
        start_time = time.time()
        num_metrics = 1000
        
        for i in range(num_metrics):
            metric = MetricValue(
                name="perf_test_metric",
                value=float(i),
                labels={"batch": str(i // 100)},
                timestamp=time.time()
            )
            await processor.process_metric(metric)
        
        # Wait for processing
        await asyncio.sleep(2.0)
        
        processing_time = time.time() - start_time
        await processor.stop()
        
        stats = processor.get_stats()
        throughput = stats['processed_metrics'] / processing_time
        
        success = (
            stats['processed_metrics'] == num_metrics and
            throughput > 100  # At least 100 metrics/second
        )
        
        details = f"""Metrics processed: {stats['processed_metrics']}
Processing time: {processing_time:.2f}s
Throughput: {throughput:.1f} metrics/sec
Avg processing time: {stats['avg_processing_time_ms']:.2f}ms
Aggregations generated: {stats['generated_aggregations']}"""
        
        print_result("Stream Processing Performance", success, details)
        return success
        
    except Exception as e:
        print_result("Stream Processing Performance", False, f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run stream processing tests"""
    print_header("Stream Processing Engine Test", 1)
    print("Testing real-time stream processing with aggregation and windowing")
    print("Validating tumbling windows, sliding windows, and aggregation functions")
    
    # Track test results
    test_results = {}
    
    # Run tests
    test_results['tumbling'] = await test_tumbling_windows()
    test_results['aggregation'] = await test_aggregation_functions()
    test_results['sliding'] = await test_sliding_windows()
    test_results['performance'] = await test_performance()
    
    # Summary
    print_header("Stream Processing Test Summary", 1)
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    success_rate = (passed_tests / total_tests) * 100
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name.upper()} test")
    
    print(f"\n📊 Overall Results:")
    print(f"  • Tests passed: {passed_tests}/{total_tests}")
    print(f"  • Success rate: {success_rate:.1f}%")
    
    if success_rate >= 75:
        print(f"  ✅ Stream processing engine SUCCESSFUL")
        print(f"  🚀 Ready for data compression & retention")
    else:
        print(f"  ❌ Stream processing engine FAILED")
        print(f"  🔧 Remediation required before proceeding")
    
    return success_rate >= 75

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
