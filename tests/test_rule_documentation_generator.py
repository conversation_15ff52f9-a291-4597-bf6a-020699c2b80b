"""
Tests for Rule Documentation Generator
======================================

Comprehensive tests for the VCS rule documentation generation system.
"""

import pytest
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

from vibe_check.core.vcs.documentation import (
    RuleDocumentationGenerator,
    RuleDocumentationExtractor,
    ExampleCodeGenerator,
    MarkdownDocumentationRenderer,
    RuleDocumentation
)
from vibe_check.core.vcs.models import RuleCategory, IssueSeverity
from vibe_check.core.vcs.config.rule_config import LineLengthConfig


class TestRuleDocumentationExtractor:
    """Test the rule documentation extractor."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.extractor = RuleDocumentationExtractor()
    
    def test_extract_rule_documentation_success(self):
        """Test successful rule documentation extraction."""
        # Import rules to trigger registration
        from vibe_check.core.vcs.rules import style_rules
        
        doc = self.extractor.extract_rule_documentation('S001')
        
        assert doc is not None
        assert doc.rule_id == 'S001'
        assert doc.name == 'Line Length Check'
        assert doc.category == 'style'
        assert doc.severity == 'warning'
        assert 'pep8' in doc.tags
        assert doc.auto_fixable is False
        assert doc.performance_impact == 'low'
        assert doc.version == '1.0.0'
        assert doc.class_name == 'LineLengthRule'
        assert 'style_rules' in doc.module
    
    def test_extract_rule_documentation_nonexistent(self):
        """Test extraction for non-existent rule."""
        doc = self.extractor.extract_rule_documentation('NONEXISTENT')
        assert doc is None
    
    def test_extract_configuration_schema(self):
        """Test configuration schema extraction."""
        # Import rules to trigger registration
        from vibe_check.core.vcs.rules import style_rules
        
        schema = self.extractor._extract_configuration_schema('S001')
        
        assert isinstance(schema, dict)
        assert 'max_line_length' in schema
        assert 'ignore_urls' in schema
        assert 'ignore_comments' in schema
        assert 'ignore_imports' in schema
        assert 'enforce_pep8' in schema
        
        # Check field details
        max_length_field = schema['max_line_length']
        assert max_length_field['type'] == 'integer'
        assert max_length_field['default'] == 100
        assert max_length_field['min_value'] == 50
        assert max_length_field['max_value'] == 200
    
    def test_generate_rule_examples(self):
        """Test rule example generation."""
        from vibe_check.core.vcs.rules.style_rules import LineLengthRule
        
        examples = self.extractor._generate_rule_examples('S001', LineLengthRule)
        
        assert len(examples) >= 2
        
        # Check example structure
        for example in examples:
            assert 'title' in example
            assert 'code' in example
            assert 'violation' in example
            assert 'explanation' in example
            assert isinstance(example['violation'], bool)
    
    def test_find_related_rules(self):
        """Test finding related rules."""
        # Import rules to trigger registration
        from vibe_check.core.vcs.rules import style_rules
        
        metadata = {
            'category': RuleCategory.STYLE,
            'tags': ['pep8', 'formatting']
        }
        
        related = self.extractor._find_related_rules('S001', metadata)
        
        # Should be a list (may be empty if only one rule is registered)
        assert isinstance(related, list)


class TestExampleCodeGenerator:
    """Test the example code generator."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.generator = ExampleCodeGenerator()
    
    def test_generate_configuration_examples(self):
        """Test configuration example generation."""
        config_schema = {
            'max_line_length': {
                'type': 'integer',
                'default': 100,
                'min_value': 50,
                'max_value': 200
            },
            'ignore_urls': {
                'type': 'boolean',
                'default': True
            }
        }
        
        examples = self.generator.generate_configuration_examples('S001', config_schema)
        
        assert len(examples) >= 1
        
        # Check default configuration example
        default_example = examples[0]
        assert default_example['title'] == 'Default Configuration'
        assert 'max_line_length' in default_example['config']
        assert 'ignore_urls' in default_example['config']
    
    def test_format_config_dict(self):
        """Test configuration dictionary formatting."""
        config = {
            'max_line_length': 100,
            'ignore_urls': True,
            'pattern': 'test_pattern'
        }
        
        formatted = self.generator._format_config_dict(config)
        
        assert '"max_line_length": 100' in formatted
        assert '"ignore_urls": True' in formatted
        assert '"pattern": "test_pattern"' in formatted
        assert formatted.startswith('{')
        assert formatted.endswith('}')


class TestMarkdownDocumentationRenderer:
    """Test the Markdown documentation renderer."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.renderer = MarkdownDocumentationRenderer()
        
        # Create sample documentation object
        self.sample_doc = RuleDocumentation(
            rule_id='S001',
            name='Test Rule',
            category='style',
            severity='warning',
            description='Test rule description',
            tags=['test', 'example'],
            auto_fixable=True,
            performance_impact='low',
            dependencies=[],
            version='1.0.0',
            class_name='TestRule',
            module='test.module',
            configuration_schema={
                'test_option': {
                    'type': 'integer',
                    'default': 100,
                    'description': 'Test option'
                }
            },
            examples=[
                {
                    'title': 'Test Example',
                    'code': 'print("test")',
                    'violation': True,
                    'explanation': 'Test explanation'
                }
            ],
            usage_notes=['Test usage note'],
            related_rules=['S002']
        )
    
    def test_render_rule_documentation(self):
        """Test complete rule documentation rendering."""
        markdown = self.renderer.render_rule_documentation(self.sample_doc)
        
        assert '# S001: Test Rule' in markdown
        assert '⚠️ **WARNING**' in markdown
        assert '🎨 **STYLE**' in markdown
        assert '🔧 **Auto-fixable**' in markdown
        assert 'Test rule description' in markdown
        assert '## Overview' in markdown
        assert '## Configuration' in markdown
        assert '## Examples' in markdown
        assert '## Usage Notes' in markdown
        assert '## Related Rules' in markdown
        assert '## Technical Details' in markdown
    
    def test_render_header(self):
        """Test header rendering."""
        header = self.renderer._render_header(self.sample_doc)
        
        assert '# S001: Test Rule' in header
        assert '⚠️ **WARNING**' in header
        assert '🎨 **STYLE**' in header
        assert '🔧 **Auto-fixable**' in header
        assert 'Test rule description' in header
    
    def test_render_overview(self):
        """Test overview section rendering."""
        overview = self.renderer._render_overview(self.sample_doc)
        
        assert '## Overview' in overview
        assert '| **Rule ID** | `S001` |' in overview
        assert '| **Category** | Style |' in overview
        assert '| **Severity** | Warning |' in overview
        assert '| **Auto-fixable** | Yes |' in overview
        assert '**Tags:** `test`, `example`' in overview
    
    def test_render_configuration(self):
        """Test configuration section rendering."""
        config_section = self.renderer._render_configuration(self.sample_doc)
        
        assert '## Configuration' in config_section
        assert '| Option | Type | Default | Description |' in config_section
        assert '| `test_option` | integer | `100` | Test option |' in config_section
    
    def test_render_examples(self):
        """Test examples section rendering."""
        examples_section = self.renderer._render_examples(self.sample_doc)
        
        assert '## Examples' in examples_section
        assert '### Example 1: Test Example' in examples_section
        assert '❌ **Violation**' in examples_section
        assert '```python' in examples_section
        assert 'print("test")' in examples_section
        assert 'Test explanation' in examples_section


class TestRuleDocumentationGenerator:
    """Test the main rule documentation generator."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.generator = RuleDocumentationGenerator()
    
    def test_generate_rule_documentation_success(self):
        """Test successful rule documentation generation."""
        # Import rules to trigger registration
        from vibe_check.core.vcs.rules import style_rules
        
        doc_content = self.generator.generate_rule_documentation('S001')
        
        assert doc_content is not None
        assert len(doc_content) > 100
        assert '# S001:' in doc_content
        assert '## Overview' in doc_content
        assert '## Configuration' in doc_content
        assert '## Examples' in doc_content
    
    def test_generate_rule_documentation_nonexistent(self):
        """Test documentation generation for non-existent rule."""
        doc_content = self.generator.generate_rule_documentation('NONEXISTENT')
        assert doc_content is None
    
    def test_generate_all_rules_documentation(self):
        """Test generating documentation for all rules."""
        # Import rules to trigger registration
        from vibe_check.core.vcs.rules import style_rules
        
        all_docs = self.generator.generate_all_rules_documentation()
        
        assert isinstance(all_docs, dict)
        assert 'S001' in all_docs
        assert len(all_docs['S001']) > 100
    
    def test_generate_rules_index(self):
        """Test rules index generation."""
        # Import rules to trigger registration
        from vibe_check.core.vcs.rules import style_rules
        
        index_content = self.generator.generate_rules_index()
        
        assert '# VCS Rules Documentation' in index_content
        assert '## Style Rules' in index_content
        assert '**[S001]' in index_content
        assert '## Statistics' in index_content
        assert '**Total Rules:**' in index_content
    
    def test_save_documentation(self):
        """Test saving documentation to files."""
        # Import rules to trigger registration
        from vibe_check.core.vcs.rules import style_rules
        
        with tempfile.TemporaryDirectory() as temp_dir:
            output_dir = Path(temp_dir)
            
            saved_files = self.generator.save_documentation(output_dir, include_index=True)
            
            assert isinstance(saved_files, dict)
            assert 'S001' in saved_files
            assert 'index' in saved_files
            
            # Check that files were actually created
            s001_file = saved_files['S001']
            index_file = saved_files['index']
            
            assert s001_file.exists()
            assert index_file.exists()
            
            # Check file contents
            s001_content = s001_file.read_text(encoding='utf-8')
            assert '# S001:' in s001_content
            
            index_content = index_file.read_text(encoding='utf-8')
            assert '# VCS Rules Documentation' in index_content


class TestIntegrationWithTypedConfiguration:
    """Test integration with typed rule configuration system."""
    
    def test_configuration_schema_integration(self):
        """Test that configuration schemas are properly extracted."""
        # Import rules to trigger registration
        from vibe_check.core.vcs.rules import style_rules
        
        generator = RuleDocumentationGenerator()
        doc = generator.extractor.extract_rule_documentation('S001')
        
        assert doc is not None
        assert len(doc.configuration_schema) > 0
        
        # Check that schema matches typed configuration
        schema = doc.configuration_schema
        assert 'max_line_length' in schema
        
        max_length_field = schema['max_line_length']
        assert max_length_field['type'] == 'integer'
        assert max_length_field['default'] == 100
        assert max_length_field['min_value'] == 50
        assert max_length_field['max_value'] == 200
    
    def test_configuration_examples_generation(self):
        """Test that configuration examples are generated correctly."""
        # Import rules to trigger registration
        from vibe_check.core.vcs.rules import style_rules
        
        generator = RuleDocumentationGenerator()
        doc_content = generator.generate_rule_documentation('S001')
        
        assert '### Configuration Examples' in doc_content
        assert 'Default Configuration' in doc_content
        assert 'Strict Configuration' in doc_content
        assert 'Lenient Configuration' in doc_content


@pytest.mark.asyncio
class TestPerformanceAndScalability:
    """Test performance and scalability of documentation generation."""
    
    async def test_documentation_generation_performance(self):
        """Test that documentation generation is reasonably fast."""
        # Import rules to trigger registration
        from vibe_check.core.vcs.rules import style_rules
        
        import time
        
        generator = RuleDocumentationGenerator()
        
        start_time = time.perf_counter()
        doc_content = generator.generate_rule_documentation('S001')
        end_time = time.perf_counter()
        
        generation_time = (end_time - start_time) * 1000  # Convert to milliseconds
        
        assert doc_content is not None
        assert generation_time < 100  # Should be under 100ms
    
    async def test_batch_documentation_generation_performance(self):
        """Test performance of generating all documentation."""
        # Import rules to trigger registration
        from vibe_check.core.vcs.rules import style_rules
        
        import time
        
        generator = RuleDocumentationGenerator()
        
        start_time = time.perf_counter()
        all_docs = generator.generate_all_rules_documentation()
        end_time = time.perf_counter()
        
        generation_time = (end_time - start_time) * 1000  # Convert to milliseconds
        
        assert len(all_docs) > 0
        assert generation_time < 500  # Should be under 500ms for all rules


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
