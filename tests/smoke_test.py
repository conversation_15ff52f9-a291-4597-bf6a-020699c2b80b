"""
Smoke Test for Vibe Check
=========================

Basic smoke test to verify core functionality is working after import fixes.
"""

import sys
import pytest
from pathlib import Path


def test_core_imports():
    """Test that core modules can be imported without errors."""
    modules_to_test = [
        'vibe_check',
        'vibe_check.cli',
        'vibe_check.cli.main',
        'vibe_check.cli.commands',
        'vibe_check.core.simple_analyzer',
        'vibe_check.core.monitoring.query_engine',
    ]
    
    failed_imports = []
    
    for module in modules_to_test:
        try:
            __import__(module)
        except Exception as e:
            failed_imports.append((module, str(e)))
    
    if failed_imports:
        error_msg = "Failed to import modules:\n"
        for module, error in failed_imports:
            error_msg += f"  - {module}: {error}\n"
        pytest.fail(error_msg)


def test_cli_entry_point():
    """Test that CLI entry point is accessible."""
    try:
        from vibe_check.cli import main
        assert callable(main), "main function should be callable"
    except ImportError as e:
        pytest.fail(f"Failed to import CLI main: {e}")


def test_simple_analyzer_basic():
    """Test that Simple Analyzer function can be imported."""
    try:
        from vibe_check.core.simple_analyzer import simple_analyze_project
        assert callable(simple_analyze_project), "simple_analyze_project should be callable"
    except ImportError as e:
        pytest.fail(f"Failed to import simple_analyze_project: {e}")
    except Exception as e:
        pytest.fail(f"Failed to access simple_analyze_project: {e}")


def test_basic_analysis_workflow():
    """Test basic analysis workflow without external dependencies."""
    try:
        from vibe_check.core.simple_analyzer import simple_analyze_project

        # Test that function is callable
        assert callable(simple_analyze_project), "simple_analyze_project should be callable"

        # Note: We don't run the actual analysis here as it requires a real project
        # and may have dependencies. This test just verifies the function is accessible.

    except Exception as e:
        pytest.fail(f"Basic analysis workflow failed: {e}")


if __name__ == "__main__":
    # Run smoke test directly
    print("Running Vibe Check smoke test...")
    
    try:
        test_core_imports()
        print("✅ Core imports test passed")
        
        test_cli_entry_point()
        print("✅ CLI entry point test passed")
        
        test_simple_analyzer_basic()
        print("✅ Simple analyzer basic test passed")
        
        test_basic_analysis_workflow()
        print("✅ Basic analysis workflow test passed")
        
        print("\n🎉 All smoke tests passed! Core functionality is working.")
        
    except Exception as e:
        print(f"\n❌ Smoke test failed: {e}")
        sys.exit(1)
