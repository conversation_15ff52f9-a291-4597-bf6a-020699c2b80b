"""
Test imports to verify module structure.

Note: The actor system has been removed as part of Phase 0 stabilization.
These tests now verify the core components that are actually available.
"""

import pytest


def test_core_models_import() -> None:
    """Test importing core models."""
    try:
        from vibe_check.core.models import FileMetrics, ProjectMetrics, DirectoryMetrics
        assert FileMetrics is not None
        assert ProjectMetrics is not None
        assert DirectoryMetrics is not None
    except ImportError as e:
        pytest.fail(f"Failed to import core models: {e}")


def test_simple_analyzer_import() -> None:
    """Test importing simple analyzer."""
    try:
        from vibe_check.core.simple_analyzer import simple_analyze_project
        assert simple_analyze_project is not None
    except ImportError as e:
        pytest.fail(f"Failed to import simple analyzer: {e}")


def test_main_api_import() -> None:
    """Test importing main API functions."""
    try:
        from vibe_check import analyze_project
        assert analyze_project is not None
    except ImportError as e:
        pytest.fail(f"Failed to import main API: {e}")


def test_analysis_components_import() -> None:
    """Test importing analysis components."""
    try:
        from vibe_check.core.analysis import (
            FileAnalyzer,
            ProjectAnalyzer,
            ToolExecutor,
        )
        assert FileAnalyzer is not None
        assert ProjectAnalyzer is not None
        assert ToolExecutor is not None
    except ImportError as e:
        pytest.fail(f"Failed to import analysis components: {e}")


def test_tools_import() -> None:
    """Test importing tool runners and parsers."""
    try:
        from vibe_check.tools.runners import get_runner_for_tool, ToolRunner
        from vibe_check.tools.parsers import get_parser, ToolParser
        assert get_runner_for_tool is not None
        assert ToolRunner is not None
        assert get_parser is not None
        assert ToolParser is not None
    except ImportError as e:
        pytest.fail(f"Failed to import tool components: {e}")


def test_cli_import() -> None:
    """Test importing CLI components."""
    try:
        from vibe_check.cli.main import main
        assert main is not None
    except ImportError as e:
        pytest.fail(f"Failed to import CLI components: {e}")
