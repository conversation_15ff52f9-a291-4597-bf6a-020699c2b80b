"""
Tests for Automated Enforcement System
======================================

Comprehensive tests for the quality enforcement and validation system.
"""

import pytest
import tempfile
from pathlib import Path
from typing import List

from vibe_check.core.quality import (
    AutomatedEnforcementEngine,
    QualityViolation,
    ViolationType,
    ConstantsValidator,
    TerminologyValidator,
    ConfigSchemaValidator
)


class TestConstantsValidator:
    """Test constants usage validation."""
    
    def test_detects_hardcoded_tool_names(self):
        """Test detection of hardcoded tool names."""
        validator = ConstantsValidator()
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write('''
def run_analysis():
    tool = "ruff"  # Should use ToolNames.RUFF
    return tool
''')
            f.flush()
            
            violations = validator.validate_file(Path(f.name))
            
        assert len(violations) > 0
        assert any("ToolNames.RUFF" in v.message for v in violations)
        assert violations[0].violation_type == ViolationType.CONSTANTS_USAGE
    
    def test_detects_hardcoded_file_extensions(self):
        """Test detection of hardcoded file extensions."""
        validator = ConstantsValidator()
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write('''
def find_python_files():
    return [f for f in files if f.endswith(".py")]
''')
            f.flush()
            
            violations = validator.validate_file(Path(f.name))
            
        assert len(violations) > 0
        assert any("FileExtensions.PYTHON" in v.message for v in violations)
    
    def test_ignores_constants_files(self):
        """Test that constants files are ignored."""
        validator = ConstantsValidator()
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='_constants.py', delete=False) as f:
            f.write('''
TOOL_NAME = "ruff"  # This should be allowed in constants files
''')
            f.flush()
            
            violations = validator.validate_file(Path(f.name))
            
        assert len(violations) == 0


class TestTerminologyValidator:
    """Test terminology consistency validation."""
    
    def test_detects_british_spelling(self):
        """Test detection of British spelling."""
        validator = TerminologyValidator()
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write('''
def analyse_project():  # Should be "analyze"
    return analyser.run()  # Should be "analyzer"
''')
            f.flush()
            
            violations = validator.validate_file(Path(f.name))
            
        assert len(violations) >= 2
        assert any("analyze" in v.message for v in violations)
        assert any("analyzer" in v.message for v in violations)
    
    def test_detects_inconsistent_terminology(self):
        """Test detection of inconsistent terminology."""
        validator = TerminologyValidator()
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write('''
def load_configuration():  # Should be "config"
    settings = get_settings()  # Should be "config"
    return configuration
''')
            f.flush()
            
            violations = validator.validate_file(Path(f.name))
            
        assert len(violations) >= 2
        assert any("config" in v.message for v in violations)
    
    def test_ignores_error_handling_context(self):
        """Test that error handling contexts are ignored."""
        validator = TerminologyValidator()
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write('''
try:
    do_something()
except ValueError as error:  # This should be allowed
    handle_error(error)
''')
            f.flush()
            
            violations = validator.validate_file(Path(f.name))
            
        # Should not flag "error" in exception handling context
        error_violations = [v for v in violations if "error" in v.message.lower()]
        assert len(error_violations) == 0


class TestConfigSchemaValidator:
    """Test configuration schema validation."""
    
    def test_validates_yaml_config(self):
        """Test YAML configuration validation."""
        validator = ConfigSchemaValidator()
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='_vibe_check.yaml', delete=False) as f:
            f.write('''
# Missing required fields
version: "1.0"
''')
            f.flush()
            
            violations = validator.validate_file(Path(f.name))
            
        # Should detect missing required fields
        assert len(violations) >= 1
        assert violations[0].violation_type == ViolationType.CONFIG_SCHEMA
    
    def test_detects_invalid_yaml(self):
        """Test detection of invalid YAML syntax."""
        validator = ConfigSchemaValidator()
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write('''
invalid: yaml: syntax:
  - missing
    - proper
  indentation
''')
            f.flush()
            
            violations = validator.validate_file(Path(f.name))
            
        assert len(violations) > 0
        assert violations[0].severity == "error"
    
    def test_ignores_non_config_files(self):
        """Test that non-configuration files are ignored."""
        validator = ConfigSchemaValidator()
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write('print("not a config file")')
            f.flush()
            
            violations = validator.validate_file(Path(f.name))
            
        assert len(violations) == 0


class TestAutomatedEnforcementEngine:
    """Test the main enforcement engine."""
    
    def test_validates_multiple_files(self):
        """Test validation of multiple files."""
        engine = AutomatedEnforcementEngine()
        
        # Create test files
        files = []
        
        # File with constants violation
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write('tool = "ruff"')
            f.flush()
            files.append(Path(f.name))
        
        # File with terminology violation
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write('def analyse(): pass')
            f.flush()
            files.append(Path(f.name))
        
        violations = engine.validate_files(files)
        
        assert len(violations) >= 2
        violation_types = {v.violation_type for v in violations}
        assert ViolationType.CONSTANTS_USAGE in violation_types
        assert ViolationType.TERMINOLOGY in violation_types
    
    def test_generates_comprehensive_report(self):
        """Test report generation."""
        engine = AutomatedEnforcementEngine()
        
        # Create sample violations
        violations = [
            QualityViolation(
                file_path="test.py",
                line_number=1,
                violation_type=ViolationType.CONSTANTS_USAGE,
                message="Use ToolNames.RUFF",
                severity="error"
            ),
            QualityViolation(
                file_path="test.py",
                line_number=2,
                violation_type=ViolationType.TERMINOLOGY,
                message="Use 'analyze' instead of 'analyse'",
                severity="warning"
            )
        ]
        
        report = engine.generate_report(violations)
        
        assert "AUTOMATED QUALITY ENFORCEMENT REPORT" in report
        assert "CONSTANTS_USAGE VIOLATIONS" in report
        assert "TERMINOLOGY VIOLATIONS" in report
        assert "SUMMARY" in report
        assert "Errors: 1" in report
        assert "Warnings: 1" in report
    
    def test_empty_violations_report(self):
        """Test report generation with no violations."""
        engine = AutomatedEnforcementEngine()
        
        report = engine.generate_report([])
        
        assert "No quality violations found!" in report


class TestIntegration:
    """Integration tests for the enforcement system."""
    
    def test_project_validation(self):
        """Test validation of an entire project."""
        engine = AutomatedEnforcementEngine()
        
        # Create temporary project structure
        with tempfile.TemporaryDirectory() as temp_dir:
            project_path = Path(temp_dir)
            
            # Create Python file with violations
            (project_path / "main.py").write_text('''
def analyse_with_ruff():
    tool = "ruff"  # Multiple violations
    return tool
''')
            
            # Create config file with violations
            (project_path / "config.yaml").write_text('''
# Invalid config
invalid: syntax
''')
            
            violations = engine.validate_project(project_path)
            
            assert len(violations) >= 2
            
            # Should have violations from both files
            file_paths = {v.file_path for v in violations}
            assert any("main.py" in fp for fp in file_paths)
            assert any("config.yaml" in fp for fp in file_paths)
    
    def test_performance_with_large_project(self):
        """Test performance with a larger project structure."""
        engine = AutomatedEnforcementEngine()
        
        with tempfile.TemporaryDirectory() as temp_dir:
            project_path = Path(temp_dir)
            
            # Create multiple files
            for i in range(10):
                (project_path / f"file_{i}.py").write_text(f'''
def function_{i}():
    tool = "ruff"  # Violation in each file
    return tool
''')
            
            import time
            start_time = time.time()
            violations = engine.validate_project(project_path)
            end_time = time.time()
            
            # Should complete quickly
            assert end_time - start_time < 5.0  # Less than 5 seconds
            
            # Should find violations in all files
            assert len(violations) >= 10


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
