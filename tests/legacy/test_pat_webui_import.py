#!/usr/bin/env python3
"""
Test script to verify that the modularized pat_webui package can be imported correctly.
"""
import os
import sys

print(f"Current directory: {os.getcwd()}")
print(f"Python executable: {sys.executable}")

try:
    from pat_webui import run_app
    print("SUCCESS: Successfully imported run_app from pat_webui")
except ImportError as e:
    print(f"ERROR: Failed to import run_app from pat_webui: {e}")
    
    # Try with explicit path
    try:
        sys.path.insert(0, os.path.abspath('.'))
        print(f"Added {os.path.abspath('.')} to sys.path")
        from pat_webui import run_app
        print("SUCCESS: Successfully imported run_app with explicit path")
    except ImportError as e:
        print(f"ERROR: Failed to import run_app with explicit path: {e}")
