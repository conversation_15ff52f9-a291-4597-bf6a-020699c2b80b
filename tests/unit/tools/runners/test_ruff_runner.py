"""
Unit tests for the ruff runner module.
"""

import pytest
import json
import tempfile
from pathlib import Path
from typing import Any, Dict
from unittest.mock import AsyncMock, MagicMock, patch

from vibe_check.tools.runners.ruff_runner import RuffRunner


class TestRuffRunner:
    """Tests for the RuffRunner class."""

    def test_ruff_runner_initialization(self) -> None:
        """Test RuffRunner initialization."""
        runner = RuffRunner()
        
        assert runner.name == "ruff"
        assert runner.config == {}

    def test_ruff_runner_initialization_with_config(self) -> None:
        """Test RuffRunner initialization with config."""
        config = {"args": ["--select=E", "--ignore=E501"]}
        runner = RuffRunner(config=config)
        
        assert runner.name == "ruff"
        assert runner.config == config

    def test_ruff_runner_initialization_with_name(self) -> None:
        """Test RuffRunner initialization with custom name."""
        runner = RuffRunner(name="custom_ruff")
        
        assert runner.name == "custom_ruff"

    @pytest.mark.asyncio
    async def test_run_no_issues(self) -> None:
        """Test running ruff with no issues found."""
        runner = RuffRunner()
        
        # Mock the process result for no issues
        mock_process_result = {
            "returncode": 0,
            "stdout": "",
            "stderr": ""
        }
        
        with patch.object(runner, 'create_temp_file') as mock_create_temp, \
             patch.object(runner, 'run_process', new_callable=AsyncMock) as mock_run_process, \
             patch.object(runner, 'cleanup_temp_file') as mock_cleanup, \
             patch.object(runner, 'get_config_args') as mock_get_args:
            
            mock_create_temp.return_value = "/tmp/test.py"
            mock_run_process.return_value = mock_process_result
            mock_get_args.return_value = []
            
            result = await runner.run("test.py", "print('hello')")
            
            assert result["issues"] == []
            assert result["summary"]["total"] == 0
            assert result["summary"]["by_type"] == {}
            
            mock_create_temp.assert_called_once_with("print('hello')", suffix='.py')
            mock_run_process.assert_called_once_with(["ruff", "check", "--format=json", "/tmp/test.py"])
            mock_cleanup.assert_called_once_with("/tmp/test.py")

    @pytest.mark.asyncio
    async def test_run_with_json_issues(self) -> None:
        """Test running ruff with JSON formatted issues."""
        runner = RuffRunner()
        
        # Mock JSON output with issues
        mock_issues = [
            {
                "code": "E501",
                "message": "line too long (89 > 88 characters)",
                "line": 1,
                "column": 89,
                "severity": "error"
            },
            {
                "code": "F401",
                "message": "'os' imported but unused",
                "line": 2,
                "column": 1,
                "severity": "warning"
            }
        ]
        
        mock_process_result = {
            "returncode": 1,
            "stdout": json.dumps(mock_issues),
            "stderr": ""
        }
        
        with patch.object(runner, 'create_temp_file') as mock_create_temp, \
             patch.object(runner, 'run_process', new_callable=AsyncMock) as mock_run_process, \
             patch.object(runner, 'cleanup_temp_file') as mock_cleanup, \
             patch.object(runner, 'get_config_args') as mock_get_args:
            
            mock_create_temp.return_value = "/tmp/test.py"
            mock_run_process.return_value = mock_process_result
            mock_get_args.return_value = []
            
            result = await runner.run("test.py", "import os\nprint('very long line that exceeds the maximum allowed length')")
            
            assert len(result["issues"]) == 2
            assert result["summary"]["total"] == 2
            assert result["summary"]["by_type"]["E501"] == 1
            assert result["summary"]["by_type"]["F401"] == 1

    @pytest.mark.asyncio
    async def test_run_with_config_args(self) -> None:
        """Test running ruff with configuration arguments."""
        config = {"args": ["--select=E", "--ignore=E501"]}
        runner = RuffRunner(config=config)
        
        mock_process_result = {
            "returncode": 0,
            "stdout": "",
            "stderr": ""
        }
        
        with patch.object(runner, 'create_temp_file') as mock_create_temp, \
             patch.object(runner, 'run_process', new_callable=AsyncMock) as mock_run_process, \
             patch.object(runner, 'cleanup_temp_file') as mock_cleanup, \
             patch.object(runner, 'get_config_args') as mock_get_args:
            
            mock_create_temp.return_value = "/tmp/test.py"
            mock_run_process.return_value = mock_process_result
            mock_get_args.return_value = ["--select=E", "--ignore=E501"]
            
            result = await runner.run("test.py", "print('hello')")
            
            expected_cmd = ["ruff", "check", "--format=json", "/tmp/test.py", "--select=E", "--ignore=E501"]
            mock_run_process.assert_called_once_with(expected_cmd)

    @pytest.mark.asyncio
    async def test_run_with_json_decode_error(self) -> None:
        """Test running ruff when JSON parsing fails."""
        runner = RuffRunner()
        
        # Mock invalid JSON output
        mock_process_result = {
            "returncode": 1,
            "stdout": "test.py:1:89: E501 line too long (89 > 88 characters)\ntest.py:2:1: F401 'os' imported but unused",
            "stderr": ""
        }
        
        with patch.object(runner, 'create_temp_file') as mock_create_temp, \
             patch.object(runner, 'run_process', new_callable=AsyncMock) as mock_run_process, \
             patch.object(runner, 'cleanup_temp_file') as mock_cleanup, \
             patch.object(runner, 'get_config_args') as mock_get_args:
            
            mock_create_temp.return_value = "/tmp/test.py"
            mock_run_process.return_value = mock_process_result
            mock_get_args.return_value = []
            
            result = await runner.run("test.py", "import os\nprint('long line')")
            
            assert len(result["issues"]) == 2
            assert result["summary"]["total"] == 2
            assert result["issues"][0]["file"] == "test.py"
            assert result["issues"][0]["line"] == 1
            assert result["issues"][0]["col"] == 89

    @pytest.mark.asyncio
    async def test_run_with_malformed_text_output(self) -> None:
        """Test running ruff with malformed text output."""
        runner = RuffRunner()
        
        # Mock malformed output
        mock_process_result = {
            "returncode": 1,
            "stdout": "malformed output without colons",
            "stderr": ""
        }
        
        with patch.object(runner, 'create_temp_file') as mock_create_temp, \
             patch.object(runner, 'run_process', new_callable=AsyncMock) as mock_run_process, \
             patch.object(runner, 'cleanup_temp_file') as mock_cleanup, \
             patch.object(runner, 'get_config_args') as mock_get_args:
            
            mock_create_temp.return_value = "/tmp/test.py"
            mock_run_process.return_value = mock_process_result
            mock_get_args.return_value = []
            
            result = await runner.run("test.py", "print('hello')")
            
            assert result["issues"] == []
            assert result["summary"]["total"] == 0

    @pytest.mark.asyncio
    async def test_run_with_exception(self) -> None:
        """Test running ruff when an exception occurs."""
        runner = RuffRunner()
        
        with patch.object(runner, 'create_temp_file') as mock_create_temp, \
             patch.object(runner, 'run_process', new_callable=AsyncMock) as mock_run_process, \
             patch.object(runner, 'cleanup_temp_file') as mock_cleanup:
            
            mock_create_temp.return_value = "/tmp/test.py"
            mock_run_process.side_effect = Exception("Process failed")
            
            result = await runner.run("test.py", "print('hello')")
            
            assert result["issues"] == []
            assert result["summary"]["total"] == 0
            assert "error" in result
            assert "Process failed" in result["error"]
            mock_cleanup.assert_called_once_with("/tmp/test.py")

    def test_is_available_true(self) -> None:
        """Test is_available when ruff is available."""
        runner = RuffRunner()
        
        with patch('vibe_check.tools.runners.ruff_runner.check_command_availability') as mock_check:
            mock_check.return_value = True
            
            assert runner.is_available() is True
            mock_check.assert_called_once_with("ruff")

    def test_is_available_false(self) -> None:
        """Test is_available when ruff is not available."""
        runner = RuffRunner()
        
        with patch('vibe_check.tools.runners.ruff_runner.check_command_availability') as mock_check:
            mock_check.return_value = False
            
            assert runner.is_available() is False
            mock_check.assert_called_once_with("ruff")

    def test_get_fallback_analysis_valid_code(self) -> None:
        """Test fallback analysis with valid Python code."""
        runner = RuffRunner()
        
        code = """import os
def function_without_docstring():
    very_long_line_that_exceeds_the_maximum_allowed_length_of_eighty_eight_characters_according_to_pep8 = True
    return True

class ClassWithoutDocstring:
    pass
"""
        
        result = runner.get_fallback_analysis("test.py", code)
        
        assert len(result["issues"]) >= 3  # Long line, missing docstrings
        assert result["tool_status"] == "unavailable"
        assert result["fallback_used"] is True
        assert "Ruff not available" in result["message"]
        
        # Check for specific issue types
        issue_codes = [issue["code"] for issue in result["issues"]]
        assert "E501" in issue_codes  # Long line
        assert "D100" in issue_codes  # Missing docstring

    def test_get_fallback_analysis_syntax_error(self) -> None:
        """Test fallback analysis with syntax error."""
        runner = RuffRunner()
        
        code = "def invalid_syntax(\n    pass"  # Missing closing parenthesis
        
        result = runner.get_fallback_analysis("test.py", code)
        
        assert len(result["issues"]) >= 1
        assert any(issue["code"] == "E999" for issue in result["issues"])
        assert any("SyntaxError" in issue["message"] for issue in result["issues"])

    def test_get_fallback_analysis_empty_code(self) -> None:
        """Test fallback analysis with empty code."""
        runner = RuffRunner()
        
        result = runner.get_fallback_analysis("test.py", "")
        
        assert result["issues"] == []
        assert result["summary"]["total"] == 0
        assert result["tool_status"] == "unavailable"
        assert result["fallback_used"] is True

    def test_get_fallback_analysis_exception_handling(self) -> None:
        """Test fallback analysis exception handling."""
        runner = RuffRunner()
        
        # This should not raise an exception even with problematic input
        result = runner.get_fallback_analysis("test.py", None)
        
        assert "issues" in result
        assert "summary" in result
        assert result["tool_status"] == "unavailable"
        assert result["fallback_used"] is True
