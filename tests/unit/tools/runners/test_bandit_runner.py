"""
Unit tests for the bandit runner module.
"""

import pytest
import json
from pathlib import Path
from typing import Any, Dict
from unittest.mock import AsyncMock, MagicMock, patch

from vibe_check.tools.runners.bandit_runner import BanditRunner


class TestBanditRunner:
    """Tests for the BanditRunner class."""

    def test_bandit_runner_initialization(self) -> None:
        """Test BanditRunner initialization."""
        runner = BanditRunner()
        
        assert runner.name == "bandit"
        assert runner.config == {}

    def test_bandit_runner_initialization_with_config(self) -> None:
        """Test BanditRunner initialization with config."""
        config = {"args": ["-ll", "-i"]}
        runner = BanditRunner(config=config)
        
        assert runner.name == "bandit"
        assert runner.config == config

    def test_bandit_runner_initialization_with_name(self) -> None:
        """Test BanditRunner initialization with custom name."""
        runner = BanditRunner(name="custom_bandit")
        
        assert runner.name == "custom_bandit"

    @pytest.mark.asyncio
    async def test_run_no_issues(self) -> None:
        """Test running bandit with no issues found."""
        runner = BanditRunner()
        
        # Mock the process result for no issues
        mock_process_result = {
            "returncode": 0,
            "stdout": "",
            "stderr": ""
        }
        
        with patch.object(runner, 'create_temp_file') as mock_create_temp, \
             patch.object(runner, 'run_process', new_callable=AsyncMock) as mock_run_process, \
             patch.object(runner, 'cleanup_temp_file') as mock_cleanup, \
             patch.object(runner, 'get_config_args') as mock_get_args:
            
            mock_create_temp.return_value = "/tmp/test.py"
            mock_run_process.return_value = mock_process_result
            mock_get_args.return_value = []
            
            result = await runner.run("test.py", "print('hello')")
            
            assert result["issues"] == []
            assert result["summary"]["total"] == 0
            assert result["summary"]["high"] == 0
            assert result["summary"]["medium"] == 0
            assert result["summary"]["low"] == 0
            
            mock_create_temp.assert_called_once_with("print('hello')", suffix='.py')
            mock_run_process.assert_called_once_with(["bandit", "-f", "json", "/tmp/test.py"])
            mock_cleanup.assert_called_once_with("/tmp/test.py")

    @pytest.mark.asyncio
    async def test_run_with_json_issues(self) -> None:
        """Test running bandit with JSON formatted issues."""
        runner = BanditRunner()
        
        # Mock JSON output with issues
        mock_data = {
            "results": [
                {
                    "test_id": "B301",
                    "test_name": "blacklist_calls",
                    "line_number": 5,
                    "issue_text": "Use of shell=True is dangerous",
                    "issue_severity": "HIGH",
                    "issue_confidence": "HIGH",
                    "more_info": "https://bandit.readthedocs.io/en/latest/plugins/b301_blacklist_calls.html"
                },
                {
                    "test_id": "B102",
                    "test_name": "exec_used",
                    "line_number": 10,
                    "issue_text": "Use of exec detected",
                    "issue_severity": "MEDIUM",
                    "issue_confidence": "HIGH",
                    "more_info": "https://bandit.readthedocs.io/en/latest/plugins/b102_exec_used.html"
                }
            ],
            "metrics": {
                "SEVERITY.HIGH": 1,
                "SEVERITY.MEDIUM": 1,
                "SEVERITY.LOW": 0
            }
        }
        
        mock_process_result = {
            "returncode": 1,
            "stdout": json.dumps(mock_data),
            "stderr": ""
        }
        
        with patch.object(runner, 'create_temp_file') as mock_create_temp, \
             patch.object(runner, 'run_process', new_callable=AsyncMock) as mock_run_process, \
             patch.object(runner, 'cleanup_temp_file') as mock_cleanup, \
             patch.object(runner, 'get_config_args') as mock_get_args:
            
            mock_create_temp.return_value = "/tmp/test.py"
            mock_run_process.return_value = mock_process_result
            mock_get_args.return_value = []
            
            result = await runner.run("test.py", "import subprocess\nsubprocess.call('ls', shell=True)")
            
            assert len(result["issues"]) == 2
            assert result["summary"]["total"] == 2
            assert result["summary"]["high"] == 1
            assert result["summary"]["medium"] == 1
            assert result["summary"]["low"] == 0
            
            # Check first issue
            assert result["issues"][0]["code"] == "B301"
            assert result["issues"][0]["name"] == "blacklist_calls"
            assert result["issues"][0]["line"] == 5
            assert result["issues"][0]["severity"] == "HIGH"

    @pytest.mark.asyncio
    async def test_run_with_config_args(self) -> None:
        """Test running bandit with configuration arguments."""
        config = {"args": ["-ll", "-i"]}
        runner = BanditRunner(config=config)
        
        mock_process_result = {
            "returncode": 0,
            "stdout": "",
            "stderr": ""
        }
        
        with patch.object(runner, 'create_temp_file') as mock_create_temp, \
             patch.object(runner, 'run_process', new_callable=AsyncMock) as mock_run_process, \
             patch.object(runner, 'cleanup_temp_file') as mock_cleanup, \
             patch.object(runner, 'get_config_args') as mock_get_args:
            
            mock_create_temp.return_value = "/tmp/test.py"
            mock_run_process.return_value = mock_process_result
            mock_get_args.return_value = ["-ll", "-i"]
            
            result = await runner.run("test.py", "print('hello')")
            
            expected_cmd = ["bandit", "-f", "json", "/tmp/test.py", "-ll", "-i"]
            mock_run_process.assert_called_once_with(expected_cmd)

    @pytest.mark.asyncio
    async def test_run_with_json_decode_error(self) -> None:
        """Test running bandit when JSON parsing fails."""
        runner = BanditRunner()
        
        # Mock invalid JSON output with text format
        mock_process_result = {
            "returncode": 1,
            "stdout": "[B301:blacklist_calls] [CWE-78] [HIGH] Use of shell=True is dangerous\n[B102:exec_used] [CWE-78] [MEDIUM] Use of exec detected",
            "stderr": ""
        }
        
        with patch.object(runner, 'create_temp_file') as mock_create_temp, \
             patch.object(runner, 'run_process', new_callable=AsyncMock) as mock_run_process, \
             patch.object(runner, 'cleanup_temp_file') as mock_cleanup, \
             patch.object(runner, 'get_config_args') as mock_get_args:
            
            mock_create_temp.return_value = "/tmp/test.py"
            mock_run_process.return_value = mock_process_result
            mock_get_args.return_value = []
            
            result = await runner.run("test.py", "import subprocess")
            
            assert len(result["issues"]) == 2
            assert result["summary"]["total"] == 2
            assert result["summary"]["high"] == 1
            assert result["summary"]["medium"] == 1
            assert result["summary"]["low"] == 0
            
            # Check parsed issues
            assert result["issues"][0]["code"] == "B301:blacklist_calls"
            assert result["issues"][0]["severity"] == "HIGH"
            assert result["issues"][1]["code"] == "B102:exec_used"
            assert result["issues"][1]["severity"] == "MEDIUM"

    @pytest.mark.asyncio
    async def test_run_with_malformed_text_output(self) -> None:
        """Test running bandit with malformed text output."""
        runner = BanditRunner()
        
        # Mock malformed output
        mock_process_result = {
            "returncode": 1,
            "stdout": "malformed output without proper format",
            "stderr": ""
        }
        
        with patch.object(runner, 'create_temp_file') as mock_create_temp, \
             patch.object(runner, 'run_process', new_callable=AsyncMock) as mock_run_process, \
             patch.object(runner, 'cleanup_temp_file') as mock_cleanup, \
             patch.object(runner, 'get_config_args') as mock_get_args:
            
            mock_create_temp.return_value = "/tmp/test.py"
            mock_run_process.return_value = mock_process_result
            mock_get_args.return_value = []
            
            result = await runner.run("test.py", "print('hello')")
            
            assert result["issues"] == []
            assert result["summary"]["total"] == 0

    @pytest.mark.asyncio
    async def test_run_with_empty_stdout(self) -> None:
        """Test running bandit with empty stdout but non-zero return code."""
        runner = BanditRunner()
        
        mock_process_result = {
            "returncode": 1,
            "stdout": "   ",  # Empty/whitespace only
            "stderr": ""
        }
        
        with patch.object(runner, 'create_temp_file') as mock_create_temp, \
             patch.object(runner, 'run_process', new_callable=AsyncMock) as mock_run_process, \
             patch.object(runner, 'cleanup_temp_file') as mock_cleanup, \
             patch.object(runner, 'get_config_args') as mock_get_args:
            
            mock_create_temp.return_value = "/tmp/test.py"
            mock_run_process.return_value = mock_process_result
            mock_get_args.return_value = []
            
            result = await runner.run("test.py", "print('hello')")
            
            assert result["issues"] == []
            assert result["summary"]["total"] == 0

    @pytest.mark.asyncio
    async def test_run_with_exception(self) -> None:
        """Test running bandit when an exception occurs."""
        runner = BanditRunner()
        
        with patch.object(runner, 'create_temp_file') as mock_create_temp, \
             patch.object(runner, 'run_process', new_callable=AsyncMock) as mock_run_process, \
             patch.object(runner, 'cleanup_temp_file') as mock_cleanup:
            
            mock_create_temp.return_value = "/tmp/test.py"
            mock_run_process.side_effect = Exception("Process failed")
            
            result = await runner.run("test.py", "print('hello')")
            
            assert result["issues"] == []
            assert result["summary"]["total"] == 0
            assert result["summary"]["high"] == 0
            assert result["summary"]["medium"] == 0
            assert result["summary"]["low"] == 0
            assert "error" in result
            assert "Process failed" in result["error"]
            mock_cleanup.assert_called_once_with("/tmp/test.py")

    def test_is_available_true(self) -> None:
        """Test is_available when bandit is available."""
        runner = BanditRunner()
        
        with patch('vibe_check.tools.runners.bandit_runner.check_command_availability') as mock_check:
            mock_check.return_value = True
            
            assert runner.is_available() is True
            mock_check.assert_called_once_with("bandit")

    def test_is_available_false(self) -> None:
        """Test is_available when bandit is not available."""
        runner = BanditRunner()
        
        with patch('vibe_check.tools.runners.bandit_runner.check_command_availability') as mock_check:
            mock_check.return_value = False
            
            assert runner.is_available() is False
            mock_check.assert_called_once_with("bandit")

    @pytest.mark.asyncio
    async def test_run_with_partial_json_data(self) -> None:
        """Test running bandit with partial JSON data (missing metrics)."""
        runner = BanditRunner()
        
        # Mock JSON output without metrics
        mock_data = {
            "results": [
                {
                    "test_id": "B301",
                    "test_name": "blacklist_calls",
                    "line_number": 5,
                    "issue_text": "Use of shell=True is dangerous",
                    "issue_severity": "HIGH",
                    "issue_confidence": "HIGH"
                }
            ]
            # No metrics field
        }
        
        mock_process_result = {
            "returncode": 1,
            "stdout": json.dumps(mock_data),
            "stderr": ""
        }
        
        with patch.object(runner, 'create_temp_file') as mock_create_temp, \
             patch.object(runner, 'run_process', new_callable=AsyncMock) as mock_run_process, \
             patch.object(runner, 'cleanup_temp_file') as mock_cleanup, \
             patch.object(runner, 'get_config_args') as mock_get_args:
            
            mock_create_temp.return_value = "/tmp/test.py"
            mock_run_process.return_value = mock_process_result
            mock_get_args.return_value = []
            
            result = await runner.run("test.py", "import subprocess")
            
            assert len(result["issues"]) == 1
            assert result["summary"]["total"] == 1
            # Should default to 0 when metrics are missing
            assert result["summary"]["high"] == 0
            assert result["summary"]["medium"] == 0
            assert result["summary"]["low"] == 0
