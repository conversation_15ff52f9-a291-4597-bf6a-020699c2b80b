"""
Unit tests for the CLI main module.
"""

import pytest
from unittest.mock import MagicMock, patch
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from vibe_check.cli.main import cli


class TestCli:
    """Tests for the CLI main module."""

    def test_cli_version(self):
        """Test the CLI version command."""
        runner = CliRunner()
        result = runner.invoke(cli, ["--version"])

        assert result.exit_code == 0
        assert "version" in result.output.lower()

        # Verify the version is included in the output
        from vibe_check import __version__
        assert __version__ in result.output

    def test_cli_help(self):
        """Test the CLI help command."""
        runner = CliRunner()
        result = runner.invoke(cli, ["--help"])

        assert result.exit_code == 0
        assert "Usage:" in result.output
        assert "Options:" in result.output
        assert "Commands:" in result.output

        # Verify the commands are included in the output
        assert "analyze" in result.output
        assert "tui" in result.output
        assert "web" in result.output
        assert "plugin" in result.output

    @patch("vibe_check.cli.main.analyze_command")
    def test_cli_analyze(self, mock_analyze_command):
        """Test the CLI analyze command."""
        # Mock the analyze command
        mock_metrics = MagicMock()
        mock_metrics.total_file_count = 10
        mock_metrics.total_line_count = 1000
        mock_metrics.avg_complexity = 5.0
        mock_metrics.issue_count = 20
        mock_analyze_command.return_value = mock_metrics

        # Run the command
        runner = CliRunner()
        with runner.isolated_filesystem():
            result = runner.invoke(cli, ["analyze", "./"])

            # For debugging
            if result.exit_code != 0:
                print(f"Exit code: {result.exit_code}")
                print(f"Exception: {result.exception}")
                print(f"Output: {result.output}")

        # Verify the command was called
        mock_analyze_command.assert_called_once()

    @patch("vibe_check.cli.main.tui_command")
    def test_cli_tui(self, mock_tui_command):
        """Test the CLI TUI command."""
        # Mock the TUI command
        mock_tui_command.return_value = None

        # Run the command
        runner = CliRunner()
        with runner.isolated_filesystem():
            result = runner.invoke(cli, ["tui", "./"])

            # For debugging
            if result.exit_code != 0:
                print(f"Exit code: {result.exit_code}")
                print(f"Exception: {result.exception}")
                print(f"Output: {result.output}")

        # Verify the command was called
        mock_tui_command.assert_called_once()

    @patch("vibe_check.cli.main.web_command")
    def test_cli_web(self, mock_web_command):
        """Test the CLI web command."""
        # Mock the web command
        mock_web_command.return_value = None

        # Run the command
        runner = CliRunner()
        with runner.isolated_filesystem():
            result = runner.invoke(cli, ["web", "./"])

            # For debugging
            if result.exit_code != 0:
                print(f"Exit code: {result.exit_code}")
                print(f"Exception: {result.exception}")
                print(f"Output: {result.output}")

        # Verify the command was called
        mock_web_command.assert_called_once()

    @patch("vibe_check.cli.main.plugin_command")
    def test_cli_plugin(self, mock_plugin_command):
        """Test the CLI plugin command."""
        # Mock the plugin command
        mock_plugin_command.return_value = None

        # Run the command
        runner = CliRunner()
        result = runner.invoke(cli, ["plugin", "list"])

        # For debugging
        if result.exit_code != 0:
            print(f"Exit code: {result.exit_code}")
            print(f"Exception: {result.exception}")
            print(f"Output: {result.output}")

        # Verify the command was called
        mock_plugin_command.assert_called_once_with("list")

    def test_cli_invalid_command(self):
        """Test the CLI with an invalid command."""
        runner = CliRunner()
        result = runner.invoke(cli, ["invalid"])

        # Verify the command failed
        assert result.exit_code != 0
        assert "Error:" in result.output
        assert "No such command" in result.output
