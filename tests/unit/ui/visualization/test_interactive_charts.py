"""
Unit tests for the interactive charts.
"""

import os
import tempfile
from pathlib import Path

import pytest

from vibe_check.core.models.project_metrics import ProjectMetrics
from vibe_check.core.models.file_metrics import FileMetrics
from vibe_check.core.models.directory_metrics import DirectoryMetrics
from vibe_check.ui.visualization.interactive_charts import (
    create_interactive_chart,
    create_interactive_complexity_chart,
    create_interactive_issues_chart,
    create_interactive_issues_by_tool_chart,
    create_interactive_coverage_chart,
    create_interactive_dependency_graph,
    create_interactive_complexity_heatmap,
    export_interactive_chart,
    export_interactive_dependency_graph,
    export_interactive_complexity_heatmap
)


class TestInteractiveCharts:
    """Tests for the interactive charts."""
    
    def setup_method(self):
        """Set up test fixtures."""
        # Create a temporary directory for output
        self.temp_dir = tempfile.TemporaryDirectory()
        self.output_dir = Path(self.temp_dir.name)
        
        # Create test metrics
        self.project_metrics = self._create_test_metrics()
    
    def teardown_method(self):
        """Tear down test fixtures."""
        self.temp_dir.cleanup()
    
    def _create_test_metrics(self) -> ProjectMetrics:
        """Create test metrics for testing."""
        # Create file metrics
        file_metrics = {}
        for i in range(5):
            file_path = f"/test/file_{i}.py"
            metrics = FileMetrics(
                path=file_path,
                lines=100 + i * 10,
                complexity=5 + i,
                docstring_coverage=50 + i * 10,
                type_coverage=60 + i * 8,
                issues=[
                    {
                        "tool": "ruff",
                        "code": f"E{i}01",
                        "message": f"Test issue {i}",
                        "location": {"file": file_path, "line": i + 1, "column": 0},
                        "severity": "MEDIUM" if i % 2 == 0 else "LOW"
                    }
                ]
            )
            file_metrics[file_path] = metrics
        
        # Create directory metrics
        directory_metrics = {}
        for i in range(2):
            dir_path = f"/test/dir_{i}"
            dir_metrics = DirectoryMetrics(path=dir_path)
            # Set the total_lines directly since line_count is a property
            dir_metrics.total_lines = 300 + i * 100
            # Add some files to make file_count work
            for j in range(3 + i):
                dir_metrics.files.append(f"{dir_path}/file_{j}.py")
            directory_metrics[dir_path] = dir_metrics
        
        # Create project metrics
        project_metrics = ProjectMetrics(
            project_path="/test",
            files=file_metrics,
            directories=directory_metrics
        )

        # Populate complexity_scores dictionary
        for file_path, metrics in file_metrics.items():
            project_metrics.complexity_scores[file_path] = metrics.complexity

        # Add imports and imported_by relationships
        for i in range(4):
            file_path = f"/test/file_{i}.py"
            imported_file = f"/test/file_{i+1}.py"
            file_metrics[file_path].imports.append(imported_file)
            file_metrics[imported_file].imported_by.append(file_path)

        return project_metrics
    
    def test_create_interactive_chart(self):
        """Test creating an interactive chart."""
        # Create a simple chart
        data = {
            "labels": ["A", "B", "C"],
            "datasets": [
                {
                    "label": "Test",
                    "data": [1, 2, 3],
                    "backgroundColor": "rgba(255, 99, 132, 0.5)"
                }
            ]
        }
        
        options = {
            "plugins": {
                "title": {
                    "display": True,
                    "text": "Test Chart"
                }
            }
        }
        
        chart = create_interactive_chart("bar", data, options)
        
        # Check that the chart has the expected properties
        assert chart["type"] == "bar"
        assert chart["data"] == data
        assert chart["options"]["plugins"]["title"]["text"] == "Test Chart"
    
    def test_create_interactive_complexity_chart(self):
        """Test creating an interactive complexity chart."""
        chart = create_interactive_complexity_chart(self.project_metrics)
        
        # Check that the chart has the expected properties
        assert chart["type"] == "bar"
        assert len(chart["data"]["labels"]) == 5
        assert len(chart["data"]["datasets"]) == 1
        assert len(chart["data"]["datasets"][0]["data"]) == 5
    
    def test_create_interactive_issues_chart(self):
        """Test creating an interactive issues chart."""
        chart = create_interactive_issues_chart(self.project_metrics)
        
        # Check that the chart has the expected properties
        assert chart["type"] == "pie"
        assert len(chart["data"]["labels"]) > 0
        assert len(chart["data"]["datasets"]) == 1
        assert len(chart["data"]["datasets"][0]["data"]) > 0
    
    def test_create_interactive_issues_by_tool_chart(self):
        """Test creating an interactive issues by tool chart."""
        chart = create_interactive_issues_by_tool_chart(self.project_metrics)
        
        # Check that the chart has the expected properties
        assert chart["type"] == "bar"
        assert len(chart["data"]["labels"]) > 0
        assert len(chart["data"]["datasets"]) == 1
        assert len(chart["data"]["datasets"][0]["data"]) > 0
    
    def test_create_interactive_coverage_chart(self):
        """Test creating an interactive coverage chart."""
        chart = create_interactive_coverage_chart(self.project_metrics)
        
        # Check that the chart has the expected properties
        assert chart["type"] == "bar"
        assert len(chart["data"]["labels"]) > 0
        assert len(chart["data"]["datasets"]) == 2
        assert len(chart["data"]["datasets"][0]["data"]) > 0
        assert len(chart["data"]["datasets"][1]["data"]) > 0
    
    def test_create_interactive_dependency_graph(self):
        """Test creating an interactive dependency graph."""
        graph = create_interactive_dependency_graph(self.project_metrics)
        
        # Check that the graph has the expected properties
        assert "nodes" in graph
        assert "edges" in graph
        assert "options" in graph
        assert len(graph["nodes"]) > 0
        assert len(graph["edges"]) > 0
    
    def test_create_interactive_complexity_heatmap(self):
        """Test creating an interactive complexity heatmap."""
        heatmap = create_interactive_complexity_heatmap(self.project_metrics)
        
        # Check that the heatmap has the expected properties
        assert "data" in heatmap
        assert "options" in heatmap
        assert len(heatmap["data"]) > 0
    
    def test_export_interactive_chart(self):
        """Test exporting an interactive chart."""
        # Create a simple chart
        data = {
            "labels": ["A", "B", "C"],
            "datasets": [
                {
                    "label": "Test",
                    "data": [1, 2, 3],
                    "backgroundColor": "rgba(255, 99, 132, 0.5)"
                }
            ]
        }
        
        chart = create_interactive_chart("bar", data)
        
        # Export the chart
        output_path = self.output_dir / "test_chart.html"
        result_path = export_interactive_chart(chart, output_path)
        
        # Check that the file was created
        assert os.path.exists(result_path)
        
        # Check the content of the file
        with open(result_path, "r", encoding="utf-8") as f:
            content = f.read()
            assert "<!DOCTYPE html>" in content
            assert "<canvas id=\"chart\"></canvas>" in content
            assert "new Chart(" in content
    
    def test_export_interactive_dependency_graph(self):
        """Test exporting an interactive dependency graph."""
        graph = create_interactive_dependency_graph(self.project_metrics)
        
        # Export the graph
        output_path = self.output_dir / "test_graph.html"
        result_path = export_interactive_dependency_graph(graph, output_path)
        
        # Check that the file was created
        assert os.path.exists(result_path)
        
        # Check the content of the file
        with open(result_path, "r", encoding="utf-8") as f:
            content = f.read()
            assert "<!DOCTYPE html>" in content
            assert "<div id=\"graph-container\"></div>" in content
            assert "vis.Network(" in content
    
    def test_export_interactive_complexity_heatmap(self):
        """Test exporting an interactive complexity heatmap."""
        heatmap = create_interactive_complexity_heatmap(self.project_metrics)
        
        # Export the heatmap
        output_path = self.output_dir / "test_heatmap.html"
        result_path = export_interactive_complexity_heatmap(heatmap, output_path)
        
        # Check that the file was created
        assert os.path.exists(result_path)
        
        # Check the content of the file
        with open(result_path, "r", encoding="utf-8") as f:
            content = f.read()
            assert "<!DOCTYPE html>" in content
            assert "<div id=\"heatmap-container\"></div>" in content
            assert "Highcharts.chart(" in content
