"""
Unit tests for the enhanced CLI features.
"""

import os
import tempfile
from pathlib import Path
from typing import Any
from unittest.mock import patch, MagicMock

import pytest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from vibe_check.cli.main import cli


class TestEnhancedCLI:
    """Tests for the enhanced CLI features."""

    def setup_method(self) -> None:
        """Set up test fixtures."""
        self.runner = CliRunner()
        self.temp_dir = tempfile.TemporaryDirectory()
        self.project_dir = Path(self.temp_dir.name) / "test_project"
        self.project_dir.mkdir(parents=True, exist_ok=True)

        # Create a simple Python file for testing
        test_file = self.project_dir / "test_file.py"
        with open(test_file, "w") as f:
            f.write("def test_function():\n    pass\n")

    def teardown_method(self) -> None:
        """Tear down test fixtures."""
        self.temp_dir.cleanup()
    
    @patch("vibe_check.cli.commands.analyze_command")
    def test_analyze_with_preset(self, mock_analyze_command: Any) -> None:
        """Test the analyze command with a preset."""
        # Mock the analyze_command function
        mock_metrics = MagicMock()
        mock_metrics.total_file_count = 1
        mock_metrics.total_line_count = 2
        mock_metrics.avg_complexity = 1.0
        mock_metrics.issue_count = 0
        mock_metrics.avg_type_coverage = 0.0
        mock_metrics.avg_doc_coverage = 0.0
        mock_metrics.issues_by_severity = {}
        mock_analyze_command.return_value = mock_metrics

        # Run the command
        result = self.runner.invoke(cli, [
            "analyze",
            str(self.project_dir),
            "--preset", "enhanced"
        ])

        # Check that the command ran successfully
        assert result.exit_code == 0

        # Check that analyze_command was called with the correct arguments
        mock_analyze_command.assert_called_once()
        args, kwargs = mock_analyze_command.call_args
        assert kwargs["project_path"] == str(self.project_dir)
        assert kwargs["config_override"]["preset"] == "enhanced"

    @patch("vibe_check.cli.commands.analyze_command")
    def test_analyze_with_trends(self, mock_analyze_command: Any) -> None:
        """Test the analyze command with trend analysis."""
        # Mock the analyze_command function
        mock_metrics = MagicMock()
        mock_metrics.total_file_count = 1
        mock_metrics.total_line_count = 2
        mock_metrics.avg_complexity = 1.0
        mock_metrics.issue_count = 0
        mock_metrics.avg_type_coverage = 0.0
        mock_metrics.avg_doc_coverage = 0.0
        mock_metrics.issues_by_severity = {}
        mock_metrics.trend_results = {
            "has_historical_data": False,
            "message": "No historical data available. Current metrics stored for future analysis."
        }
        mock_analyze_command.return_value = mock_metrics

        # Run the command
        result = self.runner.invoke(cli, [
            "analyze",
            str(self.project_dir),
            "--analyze-trends"
        ])

        # Check that the command ran successfully
        assert result.exit_code == 0

        # Check that analyze_command was called with the correct arguments
        mock_analyze_command.assert_called_once()
        args, kwargs = mock_analyze_command.call_args
        assert kwargs["project_path"] == str(self.project_dir)
        assert kwargs["analyze_trends"] is True

        # Check that the trend message is in the output
        assert "No historical data available" in result.output

    @patch("vibe_check.cli.commands.analyze_command")
    def test_analyze_with_custom_report(self, mock_analyze_command: Any) -> None:
        """Test the analyze command with custom report."""
        # Mock the analyze_command function
        mock_metrics = MagicMock()
        mock_metrics.total_file_count = 1
        mock_metrics.total_line_count = 2
        mock_metrics.avg_complexity = 1.0
        mock_metrics.issue_count = 0
        mock_metrics.avg_type_coverage = 0.0
        mock_metrics.avg_doc_coverage = 0.0
        mock_metrics.issues_by_severity = {}
        mock_analyze_command.return_value = mock_metrics

        # Run the command
        result = self.runner.invoke(cli, [
            "analyze",
            str(self.project_dir),
            "--custom-report"
        ])

        # Check that the command ran successfully
        assert result.exit_code == 0

        # Check that analyze_command was called with the correct arguments
        mock_analyze_command.assert_called_once()
        args, kwargs = mock_analyze_command.call_args
        assert kwargs["project_path"] == str(self.project_dir)
        assert kwargs["config_override"]["reporting"]["generate_custom_report"] is True
