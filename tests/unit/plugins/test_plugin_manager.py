"""
Unit tests for the plugin manager module.
"""

import pytest
import os
import tempfile
import shutil
from unittest.mock import <PERSON>Mock, patch

from vibe_check.plugins.manager import PluginManager
from vibe_check.plugins.plugin_base import Plugin


class TestPlugin:
    """Tests for the Plugin class."""

    def test_init(self):
        """Test initialization of Plugin."""
        plugin = Plugin("test-plugin", "1.0.0", "Test plugin")

        assert plugin.name == "test-plugin"
        assert plugin.version == "1.0.0"
        assert plugin.description == "Test plugin"

    def test_initialize(self):
        """Test initializing a plugin."""
        plugin = Plugin("test-plugin", "1.0.0", "Test plugin")

        # Mock config
        config = {"test_setting": "test_value"}

        # Initialize plugin
        plugin.initialize(config)

        # Base class does nothing, so just verify it doesn't raise an exception
        assert True

    def test_register_tools(self):
        """Test registering tools."""
        plugin = Plugin("test-plugin", "1.0.0", "Test plugin")

        # Register tools
        tools = plugin.register_tools()

        # Base class returns an empty list
        assert tools == []


class TestPluginManager:
    """Tests for the PluginManager class."""

    def test_init(self):
        """Test initialization of PluginManager."""
        manager = PluginManager()

        assert manager.plugins == {}
        assert manager.plugin_dir is not None

    def test_list_plugins(self):
        """Test listing plugins."""
        manager = PluginManager()

        # Add some plugins
        plugin1 = Plugin("plugin1", "1.0.0", "Plugin 1")
        plugin2 = Plugin("plugin2", "2.0.0", "Plugin 2")

        manager.plugins = {
            "plugin1": plugin1,
            "plugin2": plugin2
        }

        # List plugins
        plugins = manager.list_plugins()

        # Verify plugins
        assert len(plugins) == 2
        assert plugin1 in plugins
        assert plugin2 in plugins

    @patch("vibe_check.plugins.manager.importlib.import_module")
    def test_load_plugin(self, mock_import_module):
        """Test loading a plugin."""
        manager = PluginManager()

        # Mock import_module
        mock_module = MagicMock()
        mock_plugin = Plugin("test-plugin", "1.0.0", "Test plugin")
        mock_module.get_plugin.return_value = mock_plugin
        mock_import_module.return_value = mock_module

        # Load plugin
        plugin = manager.load_plugin("test-plugin")

        # Verify plugin was loaded
        assert plugin == mock_plugin
        assert "test-plugin" in manager.plugins
        assert manager.plugins["test-plugin"] == mock_plugin

        # Verify import_module was called
        mock_import_module.assert_called_once()

    @patch("vibe_check.plugins.manager.importlib.import_module")
    def test_load_plugin_not_found(self, mock_import_module):
        """Test loading a plugin that doesn't exist."""
        manager = PluginManager()

        # Mock import_module to raise ImportError
        mock_import_module.side_effect = ImportError("No module named 'test-plugin'")

        # Load plugin
        plugin = manager.load_plugin("test-plugin")

        # Verify plugin was not loaded
        assert plugin is None
        assert "test-plugin" not in manager.plugins

        # Verify import_module was called
        mock_import_module.assert_called_once()

    @patch("vibe_check.plugins.manager.importlib.import_module")
    def test_load_plugin_no_get_plugin(self, mock_import_module):
        """Test loading a plugin that doesn't have a get_plugin function."""
        manager = PluginManager()

        # Mock import_module
        mock_module = MagicMock()
        mock_module.get_plugin = None
        mock_import_module.return_value = mock_module

        # Load plugin
        plugin = manager.load_plugin("test-plugin")

        # Verify plugin was not loaded
        assert plugin is None
        assert "test-plugin" not in manager.plugins

        # Verify import_module was called
        mock_import_module.assert_called_once()

    @patch("vibe_check.plugins.manager.importlib.import_module")
    def test_load_plugin_invalid_plugin(self, mock_import_module):
        """Test loading a plugin that doesn't return a valid Plugin object."""
        manager = PluginManager()

        # Mock import_module
        mock_module = MagicMock()
        mock_module.get_plugin.return_value = "not a plugin"
        mock_import_module.return_value = mock_module

        # Load plugin
        plugin = manager.load_plugin("test-plugin")

        # Verify plugin was not loaded
        assert plugin is None
        assert "test-plugin" not in manager.plugins

        # Verify import_module was called
        mock_import_module.assert_called_once()

    @patch("vibe_check.plugins.manager.importlib.import_module")
    def test_load_plugin_already_loaded(self, mock_import_module):
        """Test loading a plugin that is already loaded."""
        manager = PluginManager()

        # Add a plugin
        plugin = Plugin("test-plugin", "1.0.0", "Test plugin")
        manager.plugins["test-plugin"] = plugin

        # Load plugin
        loaded_plugin = manager.load_plugin("test-plugin")

        # Verify plugin was returned
        assert loaded_plugin == plugin

        # Verify import_module was not called
        mock_import_module.assert_not_called()

    @patch("importlib.import_module")
    def test_install_plugin(self, mock_import_module):
        """Test installing a plugin."""
        manager = PluginManager()

        # Mock pip module
        mock_pip = MagicMock()
        mock_pip.main.return_value = 0
        mock_import_module.return_value = mock_pip

        # Mock load_plugin
        manager.load_plugin = MagicMock(return_value=Plugin("test-plugin", "1.0.0", "Test plugin"))

        # Install plugin
        result = manager.install_plugin("test-plugin")

        # Verify plugin was installed
        assert result is True

        # Verify pip.main was called
        mock_pip.main.assert_called_once()

        # Verify load_plugin was called
        manager.load_plugin.assert_called_once_with("test-plugin")

    @patch("importlib.import_module")
    def test_install_plugin_failed(self, mock_import_module):
        """Test installing a plugin that fails."""
        manager = PluginManager()

        # Mock pip module
        mock_pip = MagicMock()
        mock_pip.main.return_value = 1
        mock_import_module.return_value = mock_pip

        # Install plugin
        result = manager.install_plugin("test-plugin")

        # Verify plugin was not installed
        assert result is False

        # Verify pip.main was called
        mock_pip.main.assert_called_once()

    @patch("importlib.import_module")
    def test_uninstall_plugin(self, mock_import_module):
        """Test uninstalling a plugin."""
        manager = PluginManager()

        # Add a plugin
        plugin = Plugin("test-plugin", "1.0.0", "Test plugin")
        manager.plugins["test-plugin"] = plugin

        # Mock pip module
        mock_pip = MagicMock()
        mock_pip.main.return_value = 0
        mock_import_module.return_value = mock_pip

        # Uninstall plugin
        result = manager.uninstall_plugin("test-plugin")

        # Verify plugin was uninstalled
        assert result is True
        assert "test-plugin" not in manager.plugins

        # Verify pip.main was called
        mock_pip.main.assert_called_once()

    @patch("importlib.import_module")
    def test_uninstall_plugin_not_loaded(self, mock_import_module):
        """Test uninstalling a plugin that is not loaded."""
        manager = PluginManager()

        # Mock pip module
        mock_pip = MagicMock()
        mock_pip.main.return_value = 0
        mock_import_module.return_value = mock_pip

        # Uninstall plugin
        result = manager.uninstall_plugin("test-plugin")

        # Verify plugin was uninstalled
        assert result is True

        # Verify pip.main was called
        mock_pip.main.assert_called_once()

    @patch("importlib.import_module")
    def test_uninstall_plugin_failed(self, mock_import_module):
        """Test uninstalling a plugin that fails."""
        manager = PluginManager()

        # Add a plugin
        plugin = Plugin("test-plugin", "1.0.0", "Test plugin")
        manager.plugins["test-plugin"] = plugin

        # Mock pip module
        mock_pip = MagicMock()
        mock_pip.main.return_value = 1
        mock_import_module.return_value = mock_pip

        # Uninstall plugin
        result = manager.uninstall_plugin("test-plugin")

        # Verify plugin was not uninstalled
        assert result is False
        assert "test-plugin" in manager.plugins

        # Verify pip.main was called
        mock_pip.main.assert_called_once()

    def test_initialize_plugins(self):
        """Test initializing plugins."""
        manager = PluginManager()

        # Add some plugins
        plugin1 = MagicMock()
        plugin2 = MagicMock()

        manager.plugins = {
            "plugin1": plugin1,
            "plugin2": plugin2
        }

        # Mock config
        config = {"test_setting": "test_value"}

        # Initialize plugins
        manager.initialize_plugins(config)

        # Verify plugins were initialized
        plugin1.initialize.assert_called_once_with(config)
        plugin2.initialize.assert_called_once_with(config)

    def test_register_tools(self):
        """Test registering tools."""
        manager = PluginManager()

        # Add some plugins
        plugin1 = MagicMock()
        plugin1.register_tools.return_value = ["tool1", "tool2"]

        plugin2 = MagicMock()
        plugin2.register_tools.return_value = ["tool3"]

        manager.plugins = {
            "plugin1": plugin1,
            "plugin2": plugin2
        }

        # Register tools
        tools = manager.register_tools()

        # Verify tools were registered
        assert len(tools) == 3
        assert "tool1" in tools
        assert "tool2" in tools
        assert "tool3" in tools

        # Verify register_tools was called
        plugin1.register_tools.assert_called_once()
        plugin2.register_tools.assert_called_once()
