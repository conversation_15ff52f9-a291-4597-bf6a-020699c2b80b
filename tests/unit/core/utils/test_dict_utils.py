"""
Unit tests for the dictionary utilities module.
"""

import pytest
from typing import Any, Dict, List

from vibe_check.core.utils.dict_utils import (
    deep_merge,
    get_nested_value,
    set_nested_value,
    flatten_dict,
    unflatten_dict,
    filter_dict,
    exclude_keys,
    dict_to_list,
    list_to_dict
)


class TestDeepMerge:
    """Tests for the deep_merge function."""

    def test_deep_merge_simple(self) -> None:
        """Test deep merge with simple dictionaries."""
        dict1 = {"a": 1, "b": 2}
        dict2 = {"b": 3, "c": 4}
        
        result = deep_merge(dict1, dict2)
        
        assert result == {"a": 1, "b": 3, "c": 4}
        # Ensure original dictionaries are not modified
        assert dict1 == {"a": 1, "b": 2}
        assert dict2 == {"b": 3, "c": 4}

    def test_deep_merge_nested(self) -> None:
        """Test deep merge with nested dictionaries."""
        dict1 = {
            "a": 1,
            "nested": {
                "x": 10,
                "y": 20
            }
        }
        dict2 = {
            "b": 2,
            "nested": {
                "y": 30,
                "z": 40
            }
        }
        
        result = deep_merge(dict1, dict2)
        
        expected = {
            "a": 1,
            "b": 2,
            "nested": {
                "x": 10,
                "y": 30,
                "z": 40
            }
        }
        assert result == expected

    def test_deep_merge_empty_dicts(self) -> None:
        """Test deep merge with empty dictionaries."""
        dict1 = {"a": 1}
        dict2: Dict[str, Any] = {}
        
        result = deep_merge(dict1, dict2)
        assert result == {"a": 1}
        
        result = deep_merge({}, dict1)
        assert result == {"a": 1}
        
        result = deep_merge({}, {})
        assert result == {}

    def test_deep_merge_override_non_dict(self) -> None:
        """Test that non-dict values are overridden."""
        dict1 = {"a": {"nested": "value1"}}
        dict2 = {"a": "simple_value"}
        
        result = deep_merge(dict1, dict2)
        assert result == {"a": "simple_value"}


class TestGetNestedValue:
    """Tests for the get_nested_value function."""

    def test_get_nested_value_simple(self) -> None:
        """Test getting a simple nested value."""
        data = {"a": {"b": {"c": 42}}}
        
        result = get_nested_value(data, "a.b.c")
        assert result == 42

    def test_get_nested_value_single_level(self) -> None:
        """Test getting a single-level value."""
        data = {"key": "value"}
        
        result = get_nested_value(data, "key")
        assert result == "value"

    def test_get_nested_value_missing_path(self) -> None:
        """Test getting a value with missing path."""
        data = {"a": {"b": 1}}
        
        result = get_nested_value(data, "a.b.c")
        assert result is None
        
        result = get_nested_value(data, "a.b.c", "default")
        assert result == "default"

    def test_get_nested_value_non_dict_intermediate(self) -> None:
        """Test getting a value when intermediate value is not a dict."""
        data = {"a": {"b": "not_a_dict"}}
        
        result = get_nested_value(data, "a.b.c")
        assert result is None

    def test_get_nested_value_empty_path(self) -> None:
        """Test getting a value with empty path."""
        data = {"key": "value"}

        result = get_nested_value(data, "")
        # Empty path returns None since it splits to [''] and '' is not in data
        assert result is None


class TestSetNestedValue:
    """Tests for the set_nested_value function."""

    def test_set_nested_value_new_path(self) -> None:
        """Test setting a value in a new nested path."""
        data: Dict[str, Any] = {}
        
        result = set_nested_value(data, "a.b.c", 42)
        
        expected = {"a": {"b": {"c": 42}}}
        assert result == expected
        assert data == expected  # Original dict is modified

    def test_set_nested_value_existing_path(self) -> None:
        """Test setting a value in an existing path."""
        data = {"a": {"b": {"c": 1}}}
        
        result = set_nested_value(data, "a.b.c", 42)
        
        expected = {"a": {"b": {"c": 42}}}
        assert result == expected

    def test_set_nested_value_partial_path(self) -> None:
        """Test setting a value when part of the path exists."""
        data = {"a": {"x": 1}}
        
        result = set_nested_value(data, "a.b.c", 42)
        
        expected = {"a": {"x": 1, "b": {"c": 42}}}
        assert result == expected

    def test_set_nested_value_override_non_dict(self) -> None:
        """Test setting a value that overrides a non-dict value."""
        data = {"a": {"b": "not_a_dict"}}
        
        result = set_nested_value(data, "a.b.c", 42)
        
        expected = {"a": {"b": {"c": 42}}}
        assert result == expected

    def test_set_nested_value_single_level(self) -> None:
        """Test setting a single-level value."""
        data: Dict[str, Any] = {}
        
        result = set_nested_value(data, "key", "value")
        
        assert result == {"key": "value"}


class TestFlattenDict:
    """Tests for the flatten_dict function."""

    def test_flatten_dict_simple(self) -> None:
        """Test flattening a simple nested dictionary."""
        data = {
            "a": 1,
            "b": {
                "c": 2,
                "d": 3
            }
        }
        
        result = flatten_dict(data)
        
        expected = {
            "a": 1,
            "b.c": 2,
            "b.d": 3
        }
        assert result == expected

    def test_flatten_dict_deeply_nested(self) -> None:
        """Test flattening a deeply nested dictionary."""
        data = {
            "a": {
                "b": {
                    "c": {
                        "d": 42
                    }
                }
            }
        }
        
        result = flatten_dict(data)
        
        expected = {"a.b.c.d": 42}
        assert result == expected

    def test_flatten_dict_with_prefix(self) -> None:
        """Test flattening with a prefix."""
        data = {"a": {"b": 1}}
        
        result = flatten_dict(data, prefix="root")
        
        expected = {"root.a.b": 1}
        assert result == expected

    def test_flatten_dict_custom_separator(self) -> None:
        """Test flattening with a custom separator."""
        data = {"a": {"b": 1}}
        
        result = flatten_dict(data, sep="_")
        
        expected = {"a_b": 1}
        assert result == expected

    def test_flatten_dict_empty(self) -> None:
        """Test flattening an empty dictionary."""
        result = flatten_dict({})
        assert result == {}

    def test_flatten_dict_no_nesting(self) -> None:
        """Test flattening a dictionary with no nesting."""
        data = {"a": 1, "b": 2}
        
        result = flatten_dict(data)
        
        assert result == data


class TestUnflattenDict:
    """Tests for the unflatten_dict function."""

    def test_unflatten_dict_simple(self) -> None:
        """Test unflattening a simple flattened dictionary."""
        data = {
            "a": 1,
            "b.c": 2,
            "b.d": 3
        }
        
        result = unflatten_dict(data)
        
        expected = {
            "a": 1,
            "b": {
                "c": 2,
                "d": 3
            }
        }
        assert result == expected

    def test_unflatten_dict_deeply_nested(self) -> None:
        """Test unflattening a deeply nested flattened dictionary."""
        data = {"a.b.c.d": 42}
        
        result = unflatten_dict(data)
        
        expected = {
            "a": {
                "b": {
                    "c": {
                        "d": 42
                    }
                }
            }
        }
        assert result == expected

    def test_unflatten_dict_custom_separator(self) -> None:
        """Test unflattening with a custom separator."""
        data = {"a_b": 1}
        
        result = unflatten_dict(data, sep="_")
        
        expected = {"a": {"b": 1}}
        assert result == expected

    def test_unflatten_dict_empty(self) -> None:
        """Test unflattening an empty dictionary."""
        result = unflatten_dict({})
        assert result == {}

    def test_unflatten_dict_no_separator(self) -> None:
        """Test unflattening a dictionary with no separators."""
        data = {"a": 1, "b": 2}
        
        result = unflatten_dict(data)
        
        assert result == data


class TestFilterDict:
    """Tests for the filter_dict function."""

    def test_filter_dict_basic(self) -> None:
        """Test basic dictionary filtering."""
        data = {"a": 1, "b": 2, "c": 3}
        keys = ["a", "c"]
        
        result = filter_dict(data, keys)
        
        expected = {"a": 1, "c": 3}
        assert result == expected

    def test_filter_dict_missing_keys(self) -> None:
        """Test filtering with keys that don't exist."""
        data = {"a": 1, "b": 2}
        keys = ["a", "c", "d"]
        
        result = filter_dict(data, keys)
        
        expected = {"a": 1}
        assert result == expected

    def test_filter_dict_empty_keys(self) -> None:
        """Test filtering with empty key list."""
        data = {"a": 1, "b": 2}
        keys: List[str] = []
        
        result = filter_dict(data, keys)
        
        assert result == {}

    def test_filter_dict_empty_dict(self) -> None:
        """Test filtering an empty dictionary."""
        data: Dict[str, Any] = {}
        keys = ["a", "b"]
        
        result = filter_dict(data, keys)
        
        assert result == {}


class TestExcludeKeys:
    """Tests for the exclude_keys function."""

    def test_exclude_keys_basic(self) -> None:
        """Test basic key exclusion."""
        data = {"a": 1, "b": 2, "c": 3}
        keys = ["b"]
        
        result = exclude_keys(data, keys)
        
        expected = {"a": 1, "c": 3}
        assert result == expected

    def test_exclude_keys_multiple(self) -> None:
        """Test excluding multiple keys."""
        data = {"a": 1, "b": 2, "c": 3, "d": 4}
        keys = ["b", "d"]
        
        result = exclude_keys(data, keys)
        
        expected = {"a": 1, "c": 3}
        assert result == expected

    def test_exclude_keys_missing_keys(self) -> None:
        """Test excluding keys that don't exist."""
        data = {"a": 1, "b": 2}
        keys = ["c", "d"]
        
        result = exclude_keys(data, keys)
        
        assert result == data

    def test_exclude_keys_empty_keys(self) -> None:
        """Test excluding with empty key list."""
        data = {"a": 1, "b": 2}
        keys: List[str] = []
        
        result = exclude_keys(data, keys)
        
        assert result == data


class TestDictToList:
    """Tests for the dict_to_list function."""

    def test_dict_to_list_basic(self) -> None:
        """Test basic dictionary to list conversion."""
        data = {"a": 1, "b": 2}
        
        result = dict_to_list(data)
        
        expected = [
            {"key": "a", "value": 1},
            {"key": "b", "value": 2}
        ]
        # Sort both lists since dict order might vary
        assert sorted(result, key=lambda x: x["key"]) == sorted(expected, key=lambda x: x["key"])

    def test_dict_to_list_custom_fields(self) -> None:
        """Test conversion with custom field names."""
        data = {"x": 10, "y": 20}
        
        result = dict_to_list(data, key_field="name", value_field="amount")
        
        expected = [
            {"name": "x", "amount": 10},
            {"name": "y", "amount": 20}
        ]
        assert sorted(result, key=lambda x: x["name"]) == sorted(expected, key=lambda x: x["name"])

    def test_dict_to_list_empty(self) -> None:
        """Test conversion of empty dictionary."""
        result = dict_to_list({})
        assert result == []


class TestListToDict:
    """Tests for the list_to_dict function."""

    def test_list_to_dict_basic(self) -> None:
        """Test basic list to dictionary conversion."""
        data = [
            {"key": "a", "value": 1},
            {"key": "b", "value": 2}
        ]
        
        result = list_to_dict(data)
        
        expected = {"a": 1, "b": 2}
        assert result == expected

    def test_list_to_dict_custom_fields(self) -> None:
        """Test conversion with custom field names."""
        data = [
            {"name": "x", "amount": 10},
            {"name": "y", "amount": 20}
        ]
        
        result = list_to_dict(data, key_field="name", value_field="amount")
        
        expected = {"x": 10, "y": 20}
        assert result == expected

    def test_list_to_dict_empty(self) -> None:
        """Test conversion of empty list."""
        result = list_to_dict([])
        assert result == {}

    def test_list_to_dict_duplicate_keys(self) -> None:
        """Test conversion with duplicate keys (last one wins)."""
        data = [
            {"key": "a", "value": 1},
            {"key": "a", "value": 2}
        ]
        
        result = list_to_dict(data)
        
        expected = {"a": 2}  # Last value wins
        assert result == expected
