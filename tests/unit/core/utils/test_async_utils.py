"""
Unit tests for the async utilities module.
"""

import asyncio
import pytest
import time
from typing import Any
from unittest.mock import patch, MagicMock

from vibe_check.core.utils.async_utils import (
    VibeCheckTimeoutError,
    run_async,
    run_async_with_timeout,
    ensure_event_loop,
    with_timeout,
    to_thread,
    gather_with_concurrency
)


class TestVibeCheckTimeoutError:
    """Tests for the VibeCheckTimeoutError class."""

    def test_timeout_error_creation(self) -> None:
        """Test VibeCheckTimeoutError creation."""
        error = VibeCheckTimeoutError("Test timeout", operation="test_op", timeout=5.0)
        
        assert str(error) == "Test timeout"
        assert error.operation == "test_op"
        assert error.timeout == 5.0

    def test_timeout_error_minimal(self) -> None:
        """Test VibeCheckTimeoutError with minimal parameters."""
        error = VibeCheckTimeoutError("Test timeout")
        
        assert str(error) == "Test timeout"
        assert error.operation is None
        assert error.timeout is None


class TestRunAsync:
    """Tests for the run_async function."""

    def test_run_async_with_coroutine(self) -> None:
        """Test running an async coroutine."""
        async def test_coro() -> str:
            return "test_result"
        
        result = run_async(test_coro())
        assert result == "test_result"

    def test_run_async_with_function(self) -> None:
        """Test running an async function with arguments."""
        async def test_func(x: int, y: int) -> int:
            return x + y
        
        result = run_async(test_func, 3, 5)
        assert result == 8

    def test_run_async_with_kwargs(self) -> None:
        """Test running an async function with keyword arguments."""
        async def test_func(x: int, y: int = 10) -> int:
            return x + y
        
        result = run_async(test_func, 3, y=7)
        assert result == 10

    def test_run_async_with_exception(self) -> None:
        """Test that exceptions are properly propagated."""
        async def failing_coro() -> None:
            raise ValueError("Test error")
        
        with pytest.raises(ValueError, match="Test error"):
            run_async(failing_coro())

    @patch('asyncio.get_running_loop')
    def test_run_async_no_loop(self, mock_get_loop: MagicMock) -> None:
        """Test running async when no event loop exists."""
        mock_get_loop.side_effect = RuntimeError("No running event loop")
        
        async def test_coro() -> str:
            return "no_loop_result"
        
        with patch('asyncio.run') as mock_run:
            mock_run.return_value = "no_loop_result"
            result = run_async(test_coro())
            assert result == "no_loop_result"
            mock_run.assert_called_once()


class TestRunAsyncWithTimeout:
    """Tests for the run_async_with_timeout function."""

    @pytest.mark.asyncio
    async def test_run_with_timeout_success(self) -> None:
        """Test successful execution within timeout."""
        async def quick_task() -> str:
            await asyncio.sleep(0.1)
            return "success"
        
        result = await run_async_with_timeout(quick_task(), timeout=1.0)
        assert result == "success"

    @pytest.mark.asyncio
    async def test_run_with_timeout_default_return(self) -> None:
        """Test timeout with default return value."""
        async def slow_task() -> str:
            await asyncio.sleep(1.0)
            return "should_not_reach"
        
        result = await run_async_with_timeout(
            slow_task(), 
            timeout=0.1, 
            default="timeout_default"
        )
        assert result == "timeout_default"

    @pytest.mark.asyncio
    async def test_run_with_timeout_raise_exception(self) -> None:
        """Test timeout with exception raising."""
        async def slow_task() -> str:
            await asyncio.sleep(1.0)
            return "should_not_reach"
        
        with pytest.raises(VibeCheckTimeoutError) as exc_info:
            await run_async_with_timeout(
                slow_task(), 
                timeout=0.1, 
                operation_name="test_operation",
                raise_timeout=True
            )
        
        assert exc_info.value.operation == "test_operation"
        assert exc_info.value.timeout == 0.1

    @pytest.mark.asyncio
    async def test_run_with_timeout_task_exception(self) -> None:
        """Test handling of exceptions from the task itself."""
        async def failing_task() -> str:
            raise ValueError("Task failed")
        
        # With raise_timeout=False, should return default
        result = await run_async_with_timeout(
            failing_task(), 
            timeout=1.0, 
            default="error_default"
        )
        assert result == "error_default"
        
        # With raise_timeout=True, should propagate the exception
        with pytest.raises(ValueError, match="Task failed"):
            await run_async_with_timeout(
                failing_task(), 
                timeout=1.0, 
                raise_timeout=True
            )


class TestEnsureEventLoop:
    """Tests for the ensure_event_loop function."""

    @patch('asyncio.get_running_loop')
    @patch('asyncio.new_event_loop')
    @patch('asyncio.set_event_loop')
    def test_ensure_event_loop_no_loop(
        self, 
        mock_set_loop: MagicMock, 
        mock_new_loop: MagicMock, 
        mock_get_loop: MagicMock
    ) -> None:
        """Test creating a new event loop when none exists."""
        mock_get_loop.side_effect = RuntimeError("No running event loop")
        mock_loop = MagicMock()
        mock_new_loop.return_value = mock_loop
        
        result = ensure_event_loop()
        
        assert result == mock_loop
        mock_new_loop.assert_called_once()
        mock_set_loop.assert_called_once_with(mock_loop)

    @patch('asyncio.get_running_loop')
    def test_ensure_event_loop_existing_loop(self, mock_get_loop: MagicMock) -> None:
        """Test using existing event loop."""
        mock_loop = MagicMock()
        mock_loop.is_closed.return_value = False
        mock_get_loop.return_value = mock_loop
        
        result = ensure_event_loop()
        
        assert result == mock_loop

    @patch('asyncio.get_running_loop')
    @patch('asyncio.new_event_loop')
    @patch('asyncio.set_event_loop')
    def test_ensure_event_loop_closed_loop(
        self, 
        mock_set_loop: MagicMock, 
        mock_new_loop: MagicMock, 
        mock_get_loop: MagicMock
    ) -> None:
        """Test creating new loop when existing loop is closed."""
        mock_old_loop = MagicMock()
        mock_old_loop.is_closed.return_value = True
        mock_get_loop.return_value = mock_old_loop
        
        mock_new_loop_obj = MagicMock()
        mock_new_loop.return_value = mock_new_loop_obj
        
        result = ensure_event_loop()
        
        assert result == mock_new_loop_obj
        mock_new_loop.assert_called_once()
        mock_set_loop.assert_called_once_with(mock_new_loop_obj)


class TestWithTimeout:
    """Tests for the with_timeout decorator."""

    @pytest.mark.asyncio
    async def test_with_timeout_success(self) -> None:
        """Test successful execution within timeout."""
        @with_timeout(1.0, "test_operation")
        async def quick_task(value: str) -> str:
            await asyncio.sleep(0.1)
            return f"result_{value}"
        
        result = await quick_task("test")
        assert result == "result_test"

    @pytest.mark.asyncio
    async def test_with_timeout_raises_exception(self) -> None:
        """Test timeout raises exception."""
        @with_timeout(0.1, "slow_operation")
        async def slow_task() -> str:
            await asyncio.sleep(1.0)
            return "should_not_reach"
        
        with pytest.raises(VibeCheckTimeoutError) as exc_info:
            await slow_task()
        
        assert exc_info.value.operation == "slow_operation"
        assert exc_info.value.timeout == 0.1


class TestToThread:
    """Tests for the to_thread decorator."""

    @pytest.mark.asyncio
    async def test_to_thread_basic(self) -> None:
        """Test running a sync function in a thread."""
        @to_thread
        def sync_function(x: int, y: int) -> int:
            return x * y
        
        result = await sync_function(3, 4)
        assert result == 12

    @pytest.mark.asyncio
    async def test_to_thread_with_kwargs(self) -> None:
        """Test running a sync function with kwargs in a thread."""
        @to_thread
        def sync_function(x: int, y: int = 5) -> int:
            return x + y
        
        result = await sync_function(3, y=7)
        assert result == 10

    @pytest.mark.asyncio
    async def test_to_thread_exception(self) -> None:
        """Test that exceptions are properly propagated from thread."""
        @to_thread
        def failing_function() -> None:
            raise ValueError("Thread error")
        
        with pytest.raises(ValueError, match="Thread error"):
            await failing_function()


class TestGatherWithConcurrency:
    """Tests for the gather_with_concurrency function."""

    @pytest.mark.asyncio
    async def test_gather_with_concurrency_basic(self) -> None:
        """Test basic concurrency limiting."""
        async def task(value: int) -> int:
            await asyncio.sleep(0.1)
            return value * 2
        
        tasks = [task(i) for i in range(5)]
        results = await gather_with_concurrency(2, *tasks)
        
        assert results == [0, 2, 4, 6, 8]

    @pytest.mark.asyncio
    async def test_gather_with_concurrency_exceptions(self) -> None:
        """Test handling exceptions with return_exceptions=True."""
        async def task(value: int) -> int:
            if value == 2:
                raise ValueError(f"Error for {value}")
            return value * 2
        
        tasks = [task(i) for i in range(4)]
        results = await gather_with_concurrency(2, *tasks, return_exceptions=True)
        
        assert results[0] == 0
        assert results[1] == 2
        assert isinstance(results[2], ValueError)
        assert results[3] == 6

    @pytest.mark.asyncio
    async def test_gather_with_concurrency_no_exceptions(self) -> None:
        """Test that exceptions are handled by the error decorator."""
        async def task(value: int) -> int:
            if value == 1:
                raise ValueError(f"Error for {value}")
            return value * 2

        tasks = [task(i) for i in range(3)]

        # The function has an @async_catch_errors decorator, so it returns None on error
        result = await gather_with_concurrency(2, *tasks, return_exceptions=False)
        assert result is None  # Error handler returns None as default
