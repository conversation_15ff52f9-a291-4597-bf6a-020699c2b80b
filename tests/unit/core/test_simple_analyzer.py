"""
Tests for the simple analyzer module.
"""

import pytest
import tempfile
import os
from pathlib import Path
from unittest.mock import patch, MagicMock

from vibe_check.core.simple_analyzer import simple_analyze_project
from vibe_check.core.config import load_config


class TestSimpleAnalyzer:
    """Test cases for the simple analyzer."""

    def test_simple_analyze_project_basic(self, sample_project_dir, sample_config):
        """Test basic project analysis functionality."""
        project_path = Path(sample_project_dir)
        output_dir = Path(tempfile.mkdtemp())
        
        try:
            # Run analysis
            result = simple_analyze_project(
                project_path=project_path,
                output_dir=output_dir,
                config=sample_config
            )
            
            # Check that result is returned
            assert result is not None
            assert hasattr(result, 'total_file_count')
            assert result.total_file_count >= 0
            
        finally:
            # Clean up
            import shutil
            shutil.rmtree(output_dir, ignore_errors=True)

    def test_simple_analyze_project_with_python_files(self, sample_project_dir, sample_config):
        """Test analysis with Python files."""
        project_path = Path(sample_project_dir)
        output_dir = Path(tempfile.mkdtemp())
        
        try:
            # Run analysis
            result = simple_analyze_project(
                project_path=project_path,
                output_dir=output_dir,
                config=sample_config
            )
            
            # Should find at least the Python files we created
            assert result.total_file_count >= 2  # main.py and utils/helpers.py
            assert result.python_file_count >= 2
            
        finally:
            # Clean up
            import shutil
            shutil.rmtree(output_dir, ignore_errors=True)

    def test_simple_analyze_project_nonexistent_path(self, sample_config):
        """Test analysis with nonexistent project path."""
        project_path = Path("/nonexistent/path")
        output_dir = Path(tempfile.mkdtemp())
        
        try:
            # Should handle nonexistent path gracefully
            with pytest.raises((FileNotFoundError, OSError)):
                simple_analyze_project(
                    project_path=project_path,
                    output_dir=output_dir,
                    config=sample_config
                )
        finally:
            # Clean up
            import shutil
            shutil.rmtree(output_dir, ignore_errors=True)

    def test_simple_analyze_project_empty_directory(self, sample_config):
        """Test analysis with empty directory."""
        project_path = Path(tempfile.mkdtemp())
        output_dir = Path(tempfile.mkdtemp())
        
        try:
            # Run analysis on empty directory
            result = simple_analyze_project(
                project_path=project_path,
                output_dir=output_dir,
                config=sample_config
            )
            
            # Should return valid result with zero files
            assert result is not None
            assert result.total_file_count == 0
            assert result.python_file_count == 0
            
        finally:
            # Clean up
            import shutil
            shutil.rmtree(project_path, ignore_errors=True)
            shutil.rmtree(output_dir, ignore_errors=True)

    @patch('vibe_check.core.simple_analyzer.ProjectAnalyzer')
    def test_simple_analyze_project_with_mock(self, mock_analyzer_class, sample_config):
        """Test analysis with mocked ProjectAnalyzer."""
        # Setup mock
        mock_analyzer = MagicMock()
        mock_analyzer_class.return_value = mock_analyzer
        
        # Mock the analyze_project method to return a simple result
        mock_result = MagicMock()
        mock_result.total_file_count = 5
        mock_result.python_file_count = 3
        mock_analyzer.analyze_project.return_value = mock_result
        
        project_path = Path("/test/path")
        output_dir = Path("/test/output")
        
        # Run analysis
        result = simple_analyze_project(
            project_path=project_path,
            output_dir=output_dir,
            config=sample_config
        )
        
        # Verify mock was called correctly
        mock_analyzer_class.assert_called_once_with(
            project_path=project_path,
            config=sample_config,
            output_dir=output_dir
        )
        mock_analyzer.analyze_project.assert_called_once()
        
        # Verify result
        assert result == mock_result
        assert result.total_file_count == 5
        assert result.python_file_count == 3

    def test_simple_analyze_project_with_import_analysis_disabled(self, sample_project_dir):
        """Test analysis with import analysis disabled."""
        project_path = Path(sample_project_dir)
        output_dir = Path(tempfile.mkdtemp())
        
        # Create config with import analysis disabled
        config = {
            "file_extensions": [".py"],
            "import_analysis": {"enabled": False},
            "tools": {
                "ruff": {"enabled": True},
                "mypy": {"enabled": False},  # Disable mypy to avoid dependency issues
                "bandit": {"enabled": False},
                "complexity": {"enabled": True}
            }
        }
        
        try:
            # Run analysis
            result = simple_analyze_project(
                project_path=project_path,
                output_dir=output_dir,
                config=config
            )
            
            # Should complete successfully
            assert result is not None
            assert result.total_file_count >= 0
            
        finally:
            # Clean up
            import shutil
            shutil.rmtree(output_dir, ignore_errors=True)

    def test_simple_analyze_project_with_default_config(self, sample_project_dir):
        """Test analysis with default configuration."""
        project_path = Path(sample_project_dir)
        output_dir = Path(tempfile.mkdtemp())
        
        try:
            # Load default config
            config = load_config()
            
            # Run analysis
            result = simple_analyze_project(
                project_path=project_path,
                output_dir=output_dir,
                config=config
            )
            
            # Should complete successfully
            assert result is not None
            assert result.total_file_count >= 0
            
        finally:
            # Clean up
            import shutil
            shutil.rmtree(output_dir, ignore_errors=True)
