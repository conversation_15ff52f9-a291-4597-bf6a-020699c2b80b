"""
Unit tests for the file metrics module.
"""

import pytest
from pathlib import Path
from typing import Any, Dict

from vibe_check.core.models.file_metrics import FileMetrics


class TestFileMetrics:
    """Tests for the FileMetrics class."""

    def test_init(self) -> None:
        """Test initialization of FileMetrics."""
        file_metrics = FileMetrics(path="test.py")

        assert file_metrics.path == "test.py"
        assert file_metrics.line_count == 0
        assert file_metrics.complexity == 0
        assert file_metrics.issues == []
        assert file_metrics.imports == []
        assert file_metrics.dependencies == []
        assert file_metrics.docstring_coverage == 0
        assert file_metrics.type_coverage == 0

    def test_init_with_values(self) -> None:
        """Test initialization of FileMetrics with values."""
        file_metrics = FileMetrics(
            path="test.py",
            line_count=100,
            complexity=5,
            docstring_coverage=80,
            type_coverage=90
        )

        assert file_metrics.path == "test.py"
        assert file_metrics.line_count == 100
        assert file_metrics.complexity == 5
        assert file_metrics.issues == []
        assert file_metrics.imports == []
        assert file_metrics.dependencies == []
        assert file_metrics.docstring_coverage == 80
        assert file_metrics.type_coverage == 90

    def test_add_issue(self) -> None:
        """Test adding an issue to FileMetrics."""
        file_metrics = FileMetrics(path="test.py")

        file_metrics.add_issue(
            code="E101",
            message="Indentation contains mixed spaces and tabs",
            line=10,
            severity="error"
        )

        assert len(file_metrics.issues) == 1
        assert file_metrics.issues[0]["code"] == "E101"
        assert file_metrics.issues[0]["message"] == "Indentation contains mixed spaces and tabs"
        assert file_metrics.issues[0]["line"] == 10
        assert file_metrics.issues[0]["severity"] == "error"

    def test_add_dependency(self) -> None:
        """Test adding a dependency to FileMetrics."""
        file_metrics = FileMetrics(path="test.py")

        file_metrics.add_dependency("utils.py")
        file_metrics.add_dependency("helpers.py")

        assert len(file_metrics.dependencies) == 2
        assert "utils.py" in file_metrics.dependencies
        assert "helpers.py" in file_metrics.dependencies

    def test_to_dict(self) -> None:
        """Test conversion to dictionary."""
        file_metrics = FileMetrics(
            path="test.py",
            line_count=100,
            complexity=5,
            docstring_coverage=80,
            type_coverage=90
        )

        file_metrics.add_issue(
            code="E101",
            message="Indentation contains mixed spaces and tabs",
            line=10,
            severity="error"
        )

        file_metrics.add_dependency("utils.py")

        file_dict = file_metrics.to_dict()

        assert file_dict["path"] == "test.py"
        assert file_dict["line_count"] == 100
        assert file_dict["complexity"] == 5
        assert file_dict["docstring_coverage"] == 80
        assert file_dict["type_coverage"] == 90
        assert len(file_dict["issues"]) == 1
        assert file_dict["issues"][0]["code"] == "E101"
        assert len(file_dict["dependencies"]) == 1
        assert "utils.py" in file_dict["dependencies"]

    def test_from_dict(self) -> None:
        """Test creation from dictionary."""
        file_dict = {
            "path": "test.py",
            "line_count": 100,
            "complexity": 5,
            "docstring_coverage": 80,
            "type_coverage": 90,
            "issues": [
                {
                    "code": "E101",
                    "message": "Indentation contains mixed spaces and tabs",
                    "line": 10,
                    "severity": "error"
                }
            ],
            "imports": ["os"],
            "dependencies": ["utils.py"]
        }

        file_metrics = FileMetrics.from_dict(file_dict)

        assert file_metrics.path == "test.py"
        assert file_metrics.line_count == 100
        assert file_metrics.complexity == 5
        assert file_metrics.docstring_coverage == 80
        assert file_metrics.type_coverage == 90
        assert len(file_metrics.issues) == 1
        assert file_metrics.issues[0]["code"] == "E101"
        assert len(file_metrics.imports) == 1
        assert "os" in file_metrics.imports
        assert len(file_metrics.dependencies) == 1
        assert "utils.py" in file_metrics.dependencies

    def test_from_path(self) -> None:
        """Test creation from path."""
        file_metrics = FileMetrics.from_path("example.py")

        assert file_metrics.path == "example.py"
        assert file_metrics.name == "example.py"
        assert file_metrics.is_test is False
        assert file_metrics.is_package is False
        assert file_metrics.is_documentation is False

        # Test with a test file
        test_file_metrics = FileMetrics.from_path("test_example.py")
        assert test_file_metrics.is_test is True

        # Test with a package file
        package_file_metrics = FileMetrics.from_path("__init__.py")
        assert package_file_metrics.is_package is True

        # Test with a documentation file
        doc_file_metrics = FileMetrics.from_path("README.md")
        assert doc_file_metrics.is_documentation is True

    def test_properties(self) -> None:
        """Test file properties."""
        file_metrics = FileMetrics(path="src/utils/helper.py")

        assert file_metrics.is_python is True
        assert file_metrics.is_markdown is False
        assert file_metrics.basename == "helper.py"
        assert file_metrics.directory == "src/utils"

    def test_calculate_health_score(self) -> None:
        """Test health score calculation."""
        file_metrics = FileMetrics(
            path="test.py",
            complexity=10,
            docstring_coverage=50,
            security_issues=2,
            high_severity_issues=1
        )

        health_score = file_metrics.calculate_health_score()

        # Should be less than 100 due to complexity, poor docs, and security issues
        assert 0 <= health_score < 100
