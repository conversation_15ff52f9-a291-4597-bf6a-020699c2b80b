"""
Comprehensive Tests for VCS Security Rules
===========================================

Detailed unit tests for all security rules with edge cases and accuracy validation.
"""

import pytest
from pathlib import Path

from vibe_check.core.vcs.models import AnalysisTarget, AnalysisContext, EngineMode, IssueSeverity
from vibe_check.core.vcs.rules.security_rules import (
    HardcodedPasswordRule, SqlInjectionRule, UnsafeEvalRule,
    WeakCryptographyRule, InsecureRandomRule
)


class TestHardcodedPasswordRule:
    """Comprehensive tests for hardcoded password detection."""
    
    @pytest.fixture
    def rule(self):
        return HardcodedPasswordRule()
    
    @pytest.fixture
    def context(self):
        return AnalysisContext.create_default(EngineMode.STANDALONE)
    
    @pytest.mark.asyncio
    async def test_no_hardcoded_secrets(self, rule, context):
        """Test code without hardcoded secrets."""
        content = """
import os
password = os.getenv('PASSWORD')
api_key = get_config('api_key')
"""
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        issues = await rule.analyze(target, content, target.get_ast(), context)
        assert len(issues) == 0
    
    @pytest.mark.asyncio
    async def test_hardcoded_password(self, rule, context):
        """Test detection of hardcoded passwords."""
        content = """
password = "secret123"
passwd = 'mypassword'
user_password = "admin123"
"""
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        issues = await rule.analyze(target, content, target.get_ast(), context)
        assert len(issues) >= 2  # Should detect multiple password patterns
        assert all(issue.rule_id == "SEC001" for issue in issues)
        assert all(issue.severity == IssueSeverity.ERROR for issue in issues)
        assert any("password" in issue.message.lower() for issue in issues)
    
    @pytest.mark.asyncio
    async def test_hardcoded_api_key(self, rule, context):
        """Test detection of hardcoded API keys."""
        content = """
api_key = "sk-1234567890abcdef"
secret_key = "abc123def456ghi789"
token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"
"""
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        issues = await rule.analyze(target, content, target.get_ast(), context)
        assert len(issues) >= 2  # Should detect API key and secret patterns
        assert all(issue.rule_id == "SEC001" for issue in issues)
    
    @pytest.mark.asyncio
    async def test_short_values_ignored(self, rule, context):
        """Test that short values are ignored."""
        content = """
password = "x"  # Too short
api_key = "ab"  # Too short
secret = "abc"  # Still too short
"""
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        issues = await rule.analyze(target, content, target.get_ast(), context)
        assert len(issues) == 0  # All values too short to be considered secrets
    
    @pytest.mark.asyncio
    async def test_case_insensitive_detection(self, rule, context):
        """Test case-insensitive detection."""
        content = """
PASSWORD = "secret123"
Api_Key = "myapikey123"
SECRET = "mysecret456"
"""
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        issues = await rule.analyze(target, content, target.get_ast(), context)
        assert len(issues) >= 2  # Should detect uppercase variants


class TestSqlInjectionRule:
    """Comprehensive tests for SQL injection detection."""
    
    @pytest.fixture
    def rule(self):
        return SqlInjectionRule()
    
    @pytest.fixture
    def context(self):
        return AnalysisContext.create_default(EngineMode.STANDALONE)
    
    @pytest.mark.asyncio
    async def test_safe_sql_queries(self, rule, context):
        """Test safe parameterized queries."""
        content = """
cursor.execute("SELECT * FROM users WHERE id = ?", (user_id,))
cursor.execute("INSERT INTO logs VALUES (?, ?)", (timestamp, message))
"""
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        issues = await rule.analyze(target, content, target.get_ast(), context)
        assert len(issues) == 0
    
    @pytest.mark.asyncio
    async def test_string_concatenation_sql(self, rule, context):
        """Test SQL injection via string concatenation."""
        content = """
query = "SELECT * FROM users WHERE name = '" + user_input + "'"
sql = "DELETE FROM logs WHERE id = " + str(log_id)
"""
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        issues = await rule.analyze(target, content, target.get_ast(), context)
        assert len(issues) >= 1  # Should detect concatenation with SQL keywords
        assert all(issue.rule_id == "SEC002" for issue in issues)
        assert all(issue.severity == IssueSeverity.ERROR for issue in issues)
    
    @pytest.mark.asyncio
    async def test_format_string_sql(self, rule, context):
        """Test SQL injection via format strings."""
        content = """
query = "SELECT * FROM users WHERE id = {}".format(user_id)
sql = f"INSERT INTO logs VALUES ('{message}')"
"""
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        issues = await rule.analyze(target, content, target.get_ast(), context)
        # Note: Current implementation may not catch all format string cases
        # This test documents current behavior and can be enhanced
    
    @pytest.mark.asyncio
    async def test_non_sql_concatenation(self, rule, context):
        """Test that non-SQL string concatenation is ignored."""
        content = """
message = "Hello " + user_name
path = "/home/" + username + "/files"
"""
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        issues = await rule.analyze(target, content, target.get_ast(), context)
        assert len(issues) == 0  # No SQL keywords, should be ignored


class TestUnsafeEvalRule:
    """Comprehensive tests for unsafe eval detection."""
    
    @pytest.fixture
    def rule(self):
        return UnsafeEvalRule()
    
    @pytest.fixture
    def context(self):
        return AnalysisContext.create_default(EngineMode.STANDALONE)
    
    @pytest.mark.asyncio
    async def test_safe_code(self, rule, context):
        """Test code without eval/exec."""
        content = """
result = calculate(expression)
data = json.loads(json_string)
"""
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        issues = await rule.analyze(target, content, target.get_ast(), context)
        assert len(issues) == 0
    
    @pytest.mark.asyncio
    async def test_eval_usage(self, rule, context):
        """Test detection of eval() usage."""
        content = """
result = eval(user_input)
value = eval("1 + 2")
"""
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        issues = await rule.analyze(target, content, target.get_ast(), context)
        assert len(issues) == 2
        assert all(issue.rule_id == "SEC003" for issue in issues)
        assert all(issue.severity == IssueSeverity.ERROR for issue in issues)
        assert all("eval" in issue.message.lower() for issue in issues)
    
    @pytest.mark.asyncio
    async def test_exec_usage(self, rule, context):
        """Test detection of exec() usage."""
        content = """
exec(code_string)
exec("print('hello')")
"""
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        issues = await rule.analyze(target, content, target.get_ast(), context)
        assert len(issues) == 2
        assert all(issue.rule_id == "SEC003" for issue in issues)
        assert all("exec" in issue.message.lower() for issue in issues)
    
    @pytest.mark.asyncio
    async def test_compile_with_eval_mode(self, rule, context):
        """Test detection of compile() with eval mode."""
        content = """
code = compile(source, '<string>', 'eval')
bytecode = compile(expression, 'file.py', 'exec')
"""
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        issues = await rule.analyze(target, content, target.get_ast(), context)
        assert len(issues) >= 1  # Should detect compile with eval/exec mode
        assert all(issue.rule_id == "SEC003" for issue in issues)


class TestWeakCryptographyRule:
    """Comprehensive tests for weak cryptography detection."""
    
    @pytest.fixture
    def rule(self):
        return WeakCryptographyRule()
    
    @pytest.fixture
    def context(self):
        return AnalysisContext.create_default(EngineMode.STANDALONE)
    
    @pytest.mark.asyncio
    async def test_strong_cryptography(self, rule, context):
        """Test strong cryptographic algorithms."""
        content = """
import hashlib
hash_obj = hashlib.sha256()
secure_hash = hashlib.sha3_256()
"""
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        issues = await rule.analyze(target, content, target.get_ast(), context)
        assert len(issues) == 0
    
    @pytest.mark.asyncio
    async def test_weak_hash_algorithms(self, rule, context):
        """Test detection of weak hash algorithms."""
        content = """
import hashlib
md5_hash = hashlib.md5()
sha1_hash = hashlib.sha1()
"""
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        issues = await rule.analyze(target, content, target.get_ast(), context)
        assert len(issues) == 2
        assert all(issue.rule_id == "SEC004" for issue in issues)
        assert all(issue.severity == IssueSeverity.WARNING for issue in issues)
        assert any("md5" in issue.message.lower() for issue in issues)
        assert any("sha1" in issue.message.lower() for issue in issues)
    
    @pytest.mark.asyncio
    async def test_hashlib_new_weak(self, rule, context):
        """Test detection of weak algorithms via hashlib.new()."""
        content = """
import hashlib
hash1 = hashlib.new('md5')
hash2 = hashlib.new('sha1')
hash3 = hashlib.new('sha256')  # This should be fine
"""
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        issues = await rule.analyze(target, content, target.get_ast(), context)
        assert len(issues) == 2  # Only md5 and sha1 should trigger
        assert all(issue.rule_id == "SEC004" for issue in issues)
    
    @pytest.mark.asyncio
    async def test_case_insensitive_detection(self, rule, context):
        """Test case-insensitive algorithm detection."""
        content = """
import hashlib
hash1 = hashlib.new('MD5')
hash2 = hashlib.new('SHA1')
"""
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        issues = await rule.analyze(target, content, target.get_ast(), context)
        assert len(issues) == 2  # Should detect uppercase variants


class TestInsecureRandomRule:
    """Comprehensive tests for insecure random detection."""
    
    @pytest.fixture
    def rule(self):
        return InsecureRandomRule()
    
    @pytest.fixture
    def context(self):
        return AnalysisContext.create_default(EngineMode.STANDALONE)
    
    @pytest.mark.asyncio
    async def test_secure_random(self, rule, context):
        """Test secure random usage."""
        content = """
import secrets
token = secrets.token_hex(16)
password = secrets.token_urlsafe(32)
"""
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        issues = await rule.analyze(target, content, target.get_ast(), context)
        assert len(issues) == 0
    
    @pytest.mark.asyncio
    async def test_insecure_random_functions(self, rule, context):
        """Test detection of insecure random functions."""
        content = """
import random
value = random.random()
number = random.randint(1, 100)
choice = random.choice(['a', 'b', 'c'])
"""
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        issues = await rule.analyze(target, content, target.get_ast(), context)
        assert len(issues) == 3  # All three random functions should trigger
        assert all(issue.rule_id == "SEC005" for issue in issues)
        assert all(issue.severity == IssueSeverity.WARNING for issue in issues)
        assert all("random" in issue.message.lower() for issue in issues)
    
    @pytest.mark.asyncio
    async def test_random_shuffle_and_sample(self, rule, context):
        """Test detection of random.shuffle and random.sample."""
        content = """
import random
random.shuffle(my_list)
sample = random.sample(population, 5)
"""
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        issues = await rule.analyze(target, content, target.get_ast(), context)
        assert len(issues) == 2
        assert all(issue.rule_id == "SEC005" for issue in issues)
    
    @pytest.mark.asyncio
    async def test_non_random_functions_ignored(self, rule, context):
        """Test that non-random functions are ignored."""
        content = """
import random
random.seed(42)  # This might be acceptable for reproducibility
import math
value = math.random()  # This doesn't exist, but shouldn't trigger
"""
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        issues = await rule.analyze(target, content, target.get_ast(), context)
        # Current implementation might detect seed() - this documents behavior
        # The rule focuses on generation functions, not seeding


if __name__ == "__main__":
    pytest.main([__file__])
