"""
Tests for VCS Engine Core
==========================

Unit tests for the VibeCheckEngine class and core VCS functionality.
"""

import asyncio
import pytest
from pathlib import Path
from unittest.mock import Mock, patch

from vibe_check.core.vcs.engine import VibeCheck<PERSON>ngine
from vibe_check.core.vcs.models import EngineMode, AnalysisTarget, AnalysisContext
from vibe_check.core.vcs.config import VCSConfig


class TestVibeCheckEngine:
    """Test cases for VibeCheckEngine."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        return VCSConfig(
            mode=EngineMode.STANDALONE,
            cache_enabled=False,  # Disable cache for testing
            performance_mode=True
        )
    
    @pytest.fixture
    def engine(self, config):
        """Create test engine."""
        return VibeCheckEngine(mode=EngineMode.STANDALONE, config=config)
    
    def test_engine_initialization(self, engine):
        """Test engine initialization."""
        assert engine.mode == EngineMode.STANDALONE
        assert not engine._initialized
        assert not engine._running
        assert engine.rule_registry is not None
        assert engine.performance_monitor is not None
    
    @pytest.mark.asyncio
    async def test_engine_lifecycle(self, engine):
        """Test engine start/stop lifecycle."""
        # Initially not running
        assert not engine.is_running()
        assert not engine.is_enabled()
        
        # Start engine
        await engine.start()
        assert engine.is_running()
        assert engine.is_enabled()
        assert engine._initialized
        
        # Stop engine
        await engine.stop()
        assert not engine.is_running()
        assert not engine.is_enabled()
    
    @pytest.mark.asyncio
    async def test_engine_restart(self, engine):
        """Test engine restart functionality."""
        await engine.start()
        assert engine.is_running()
        
        await engine.restart()
        assert engine.is_running()
        assert engine.is_enabled()
    
    @pytest.mark.asyncio
    async def test_analyze_not_running(self, engine):
        """Test analysis when engine is not running."""
        target = AnalysisTarget.from_content(Path("test.py"), "print('hello')")
        
        with pytest.raises(RuntimeError, match="Engine is not running"):
            await engine.analyze(target)
    
    @pytest.mark.asyncio
    async def test_analyze_basic(self, engine, tmp_path):
        """Test basic analysis functionality."""
        # Create test file
        test_file = tmp_path / "test.py"
        test_file.write_text("print('hello world')\n")
        
        # Start engine
        await engine.start()
        
        # Create target
        target = AnalysisTarget.from_file(test_file)
        
        # Analyze
        result = await engine.analyze(target)
        
        # Verify result
        assert result is not None
        assert result.target == target
        assert result.success
        assert result.execution_time > 0
        assert isinstance(result.issues, list)
        assert isinstance(result.rules_executed, list)
        
        # Stop engine
        await engine.stop()
    
    @pytest.mark.asyncio
    async def test_analyze_with_context(self, engine, tmp_path):
        """Test analysis with custom context."""
        # Create test file
        test_file = tmp_path / "test.py"
        test_file.write_text("x = 1\ny = 2\n")
        
        # Start engine
        await engine.start()
        
        # Create target and context
        target = AnalysisTarget.from_file(test_file)
        context = AnalysisContext.create_default(EngineMode.STANDALONE)
        context.performance_mode = True
        
        # Analyze
        result = await engine.analyze(target, context)
        
        # Verify result
        assert result.success
        assert result.target == target
        
        # Stop engine
        await engine.stop()
    
    @pytest.mark.asyncio
    async def test_analyze_syntax_error(self, engine, tmp_path):
        """Test analysis with syntax error."""
        # Create test file with syntax error
        test_file = tmp_path / "test.py"
        test_file.write_text("def invalid_syntax(\n")  # Missing closing parenthesis
        
        # Start engine
        await engine.start()
        
        # Create target
        target = AnalysisTarget.from_file(test_file)
        
        # Analyze
        result = await engine.analyze(target)
        
        # Verify result
        assert result is not None
        assert not result.success
        assert result.error_message is not None
        assert "Syntax error" in result.error_message or "syntax" in result.error_message.lower()
        
        # Stop engine
        await engine.stop()
    
    def test_performance_stats(self, engine):
        """Test performance statistics."""
        stats = engine.get_performance_stats()
        
        assert "analyses" in stats
        assert "total_time" in stats
        assert "average_time" in stats
        assert stats["analyses"] == 0
        assert stats["total_time"] == 0.0
    
    def test_engine_status(self, engine):
        """Test engine status information."""
        status = engine.get_status()
        
        assert "mode" in status
        assert "initialized" in status
        assert "running" in status
        assert "cache_enabled" in status
        assert "rules_loaded" in status
        assert "performance" in status
        
        assert status["mode"] == "standalone"
        assert not status["initialized"]
        assert not status["running"]
    
    @pytest.mark.asyncio
    async def test_multiple_analyses(self, engine, tmp_path):
        """Test multiple analyses for performance tracking."""
        # Create test files
        files = []
        for i in range(3):
            test_file = tmp_path / f"test_{i}.py"
            test_file.write_text(f"x = {i}\nprint(x)\n")
            files.append(test_file)
        
        # Start engine
        await engine.start()
        
        # Analyze multiple files
        results = []
        for file_path in files:
            target = AnalysisTarget.from_file(file_path)
            result = await engine.analyze(target)
            results.append(result)
        
        # Verify all results
        for result in results:
            assert result.success
            assert result.execution_time > 0
        
        # Check performance stats
        stats = engine.get_performance_stats()
        assert stats["analyses"] == 3
        assert stats["total_time"] > 0
        assert stats["average_time"] > 0
        
        # Stop engine
        await engine.stop()


class TestAnalysisTarget:
    """Test cases for AnalysisTarget."""
    
    def test_from_file(self, tmp_path):
        """Test creating target from file."""
        test_file = tmp_path / "test.py"
        test_file.write_text("print('hello')")
        
        target = AnalysisTarget.from_file(test_file)
        
        assert target.path == test_file
        assert target.content is None
        assert target.encoding == "utf-8"
    
    def test_from_content(self):
        """Test creating target from content."""
        content = "print('hello world')"
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        assert target.path == Path("test.py")
        assert target.content == content
    
    def test_get_content_from_file(self, tmp_path):
        """Test getting content from file."""
        test_file = tmp_path / "test.py"
        content = "x = 1\ny = 2\n"
        test_file.write_text(content)
        
        target = AnalysisTarget.from_file(test_file)
        assert target.get_content() == content
    
    def test_get_content_from_memory(self):
        """Test getting content from memory."""
        content = "print('hello')"
        target = AnalysisTarget.from_content(Path("test.py"), content)
        assert target.get_content() == content
    
    def test_get_ast(self):
        """Test getting AST from content."""
        content = "x = 1\ny = x + 1\n"
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        ast_tree = target.get_ast()
        assert ast_tree is not None
        # Basic AST validation
        import ast
        assert isinstance(ast_tree, ast.Module)
    
    def test_get_ast_syntax_error(self):
        """Test getting AST with syntax error."""
        content = "def invalid(\n"  # Missing closing parenthesis
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        with pytest.raises(ValueError, match="Syntax error"):
            target.get_ast()


if __name__ == "__main__":
    pytest.main([__file__])
