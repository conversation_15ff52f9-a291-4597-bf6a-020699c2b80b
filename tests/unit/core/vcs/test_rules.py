"""
Tests for VCS Rules System
===========================

Unit tests for the VCS built-in analysis rules and rule registry.
"""

import asyncio
import pytest
from pathlib import Path

from vibe_check.core.vcs.engine import VibeCheckEngine
from vibe_check.core.vcs.models import EngineMode, AnalysisTarget, AnalysisContext, RuleCategory
from vibe_check.core.vcs.config import VCSConfig
from vibe_check.core.vcs.registry import RuleRegistry
from vibe_check.core.vcs.rules.rule_loader import load_built_in_rules, get_rule_summary


class TestRuleRegistry:
    """Test cases for RuleRegistry."""
    
    @pytest.fixture
    def registry(self):
        """Create test registry."""
        return RuleRegistry()
    
    def test_registry_initialization(self, registry):
        """Test registry initialization."""
        assert len(registry.rules) == 0
        assert len(registry.categories) == 6  # 6 rule categories
        assert all(len(rules) == 0 for rules in registry.categories.values())
    
    def test_load_built_in_rules(self, registry):
        """Test loading built-in rules."""
        loaded_count = load_built_in_rules(registry)
        
        # Should load 32 rules total (6+5+5+4+6+6)
        assert loaded_count == 32
        assert len(registry.rules) == 32
        
        # Check that all categories have rules
        for category in RuleCategory:
            rules = registry.get_rules_for_category(category)
            assert len(rules) > 0
    
    def test_rule_summary(self):
        """Test rule summary generation."""
        summary = get_rule_summary()
        
        assert summary["total_rules"] == 32
        assert len(summary["categories"]) == 6
        assert summary["categories"]["style"] == 6
        assert summary["categories"]["security"] == 5
        assert summary["categories"]["complexity"] == 5
        assert summary["categories"]["documentation"] == 4
        assert summary["categories"]["imports"] == 6
        assert summary["categories"]["types"] == 6
    
    def test_registry_stats(self, registry):
        """Test registry statistics."""
        load_built_in_rules(registry)
        stats = registry.get_registry_stats()
        
        assert stats["total_rules"] == 32
        assert stats["enabled_rules"] == 32  # All rules enabled by default
        assert stats["disabled_rules"] == 0


class TestStyleRules:
    """Test cases for style rules."""
    
    @pytest.fixture
    def engine(self):
        """Create test engine with rules loaded."""
        config = VCSConfig(mode=EngineMode.STANDALONE, cache_enabled=False)
        return VibeCheckEngine(mode=EngineMode.STANDALONE, config=config)
    
    @pytest.mark.asyncio
    async def test_line_length_rule(self, engine):
        """Test line length rule."""
        await engine.start()

        # Create content with long line
        content = "x = " + "a" * 100  # 104 characters total
        target = AnalysisTarget.from_content(Path("test.py"), content)

        result = await engine.analyze(target)
        
        assert result.success
        # Should find line length issue
        line_length_issues = [issue for issue in result.issues if issue.rule_id == "S001"]
        assert len(line_length_issues) > 0
        assert "Line too long" in line_length_issues[0].message
    
    @pytest.mark.asyncio
    async def test_trailing_whitespace_rule(self, engine):
        """Test trailing whitespace rule."""
        await engine.start()

        content = "x = 1   \ny = 2\n"  # First line has trailing spaces
        target = AnalysisTarget.from_content(Path("test.py"), content)

        result = await engine.analyze(target)
        
        assert result.success
        # Should find trailing whitespace issue
        whitespace_issues = [issue for issue in result.issues if issue.rule_id == "S002"]
        assert len(whitespace_issues) > 0
        assert "Trailing whitespace" in whitespace_issues[0].message
    
    @pytest.mark.asyncio
    async def test_indentation_rule(self, engine):
        """Test indentation rule."""
        content = "if True:\n\tx = 1\n"  # Uses tab instead of spaces
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        result = await engine.analyze(target)
        
        assert result.success
        # Should find indentation issue
        indent_issues = [issue for issue in result.issues if issue.rule_id == "S003"]
        assert len(indent_issues) > 0
        assert "spaces instead of tabs" in indent_issues[0].message


class TestSecurityRules:
    """Test cases for security rules."""
    
    @pytest.fixture
    async def engine(self):
        """Create test engine with rules loaded."""
        config = VCSConfig(mode=EngineMode.STANDALONE, cache_enabled=False)
        engine = VibeCheckEngine(mode=EngineMode.STANDALONE, config=config)
        await engine.start()
        yield engine
        if engine.is_enabled():
            await engine.stop()
    
    @pytest.mark.asyncio
    async def test_hardcoded_password_rule(self, engine):
        """Test hardcoded password detection."""
        content = 'password = "secret123"\napi_key = "abc123def456"\n'
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        result = await engine.analyze(target)
        
        assert result.success
        # Should find hardcoded password issues
        password_issues = [issue for issue in result.issues if issue.rule_id == "SEC001"]
        assert len(password_issues) >= 1  # At least one hardcoded secret
        assert any("password" in issue.message.lower() for issue in password_issues)
    
    @pytest.mark.asyncio
    async def test_unsafe_eval_rule(self, engine):
        """Test unsafe eval detection."""
        content = 'result = eval(user_input)\nexec("print(1)")\n'
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        result = await engine.analyze(target)
        
        assert result.success
        # Should find unsafe eval issues
        eval_issues = [issue for issue in result.issues if issue.rule_id == "SEC003"]
        assert len(eval_issues) >= 1
        assert any("eval" in issue.message.lower() for issue in eval_issues)


class TestComplexityRules:
    """Test cases for complexity rules."""
    
    @pytest.fixture
    async def engine(self):
        """Create test engine with rules loaded."""
        config = VCSConfig(mode=EngineMode.STANDALONE, cache_enabled=False)
        engine = VibeCheckEngine(mode=EngineMode.STANDALONE, config=config)
        await engine.start()
        yield engine
        if engine.is_enabled():
            await engine.stop()
    
    @pytest.mark.asyncio
    async def test_cyclomatic_complexity_rule(self, engine):
        """Test cyclomatic complexity detection."""
        # Create function with high complexity
        content = """
def complex_function(x):
    if x > 0:
        if x > 10:
            if x > 20:
                if x > 30:
                    if x > 40:
                        if x > 50:
                            return "very high"
                        return "high"
                    return "medium-high"
                return "medium"
            return "low-medium"
        return "low"
    return "zero"
"""
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        result = await engine.analyze(target)
        
        assert result.success
        # Should find complexity issue
        complexity_issues = [issue for issue in result.issues if issue.rule_id == "C001"]
        assert len(complexity_issues) >= 1
        assert "complexity" in complexity_issues[0].message.lower()
    
    @pytest.mark.asyncio
    async def test_parameter_count_rule(self, engine):
        """Test parameter count rule."""
        content = "def many_params(a, b, c, d, e, f, g, h):\n    pass\n"
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        result = await engine.analyze(target)
        
        assert result.success
        # Should find parameter count issue
        param_issues = [issue for issue in result.issues if issue.rule_id == "C004"]
        assert len(param_issues) >= 1
        assert "parameters" in param_issues[0].message.lower()


class TestDocumentationRules:
    """Test cases for documentation rules."""
    
    @pytest.fixture
    async def engine(self):
        """Create test engine with rules loaded."""
        config = VCSConfig(mode=EngineMode.STANDALONE, cache_enabled=False)
        engine = VibeCheckEngine(mode=EngineMode.STANDALONE, config=config)
        await engine.start()
        yield engine
        if engine.is_enabled():
            await engine.stop()
    
    @pytest.mark.asyncio
    async def test_missing_docstring_rule(self, engine):
        """Test missing docstring detection."""
        content = """
def public_function():
    return 42

class PublicClass:
    def method(self):
        pass
"""
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        result = await engine.analyze(target)
        
        assert result.success
        # Should find missing docstring issues
        docstring_issues = [issue for issue in result.issues if issue.rule_id == "D001"]
        assert len(docstring_issues) >= 2  # Function and class missing docstrings


class TestImportRules:
    """Test cases for import rules."""
    
    @pytest.fixture
    async def engine(self):
        """Create test engine with rules loaded."""
        config = VCSConfig(mode=EngineMode.STANDALONE, cache_enabled=False)
        engine = VibeCheckEngine(mode=EngineMode.STANDALONE, config=config)
        await engine.start()
        yield engine
        if engine.is_enabled():
            await engine.stop()
    
    @pytest.mark.asyncio
    async def test_unused_import_rule(self, engine):
        """Test unused import detection."""
        content = "import os\nimport sys\nprint('hello')\n"  # os is unused
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        result = await engine.analyze(target)
        
        assert result.success
        # Should find unused import issue
        unused_issues = [issue for issue in result.issues if issue.rule_id == "I001"]
        assert len(unused_issues) >= 1
        assert "unused import" in unused_issues[0].message.lower()
    
    @pytest.mark.asyncio
    async def test_wildcard_import_rule(self, engine):
        """Test wildcard import detection."""
        content = "from os import *\nprint('hello')\n"
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        result = await engine.analyze(target)
        
        assert result.success
        # Should find wildcard import issue
        wildcard_issues = [issue for issue in result.issues if issue.rule_id == "I003"]
        assert len(wildcard_issues) >= 1
        assert "wildcard import" in wildcard_issues[0].message.lower()


class TestTypeRules:
    """Test cases for type rules."""
    
    @pytest.fixture
    async def engine(self):
        """Create test engine with rules loaded."""
        config = VCSConfig(mode=EngineMode.STANDALONE, cache_enabled=False)
        engine = VibeCheckEngine(mode=EngineMode.STANDALONE, config=config)
        await engine.start()
        yield engine
        if engine.is_enabled():
            await engine.stop()
    
    @pytest.mark.asyncio
    async def test_missing_type_hints_rule(self, engine):
        """Test missing type hints detection."""
        content = """
def public_function(x, y):
    return x + y
"""
        target = AnalysisTarget.from_content(Path("test.py"), content)
        
        result = await engine.analyze(target)
        
        assert result.success
        # Should find missing type hints issue
        type_issues = [issue for issue in result.issues if issue.rule_id == "T001"]
        assert len(type_issues) >= 1
        assert "type hints" in type_issues[0].message.lower()


class TestRuleIntegration:
    """Test cases for rule integration with engine."""
    
    @pytest.mark.asyncio
    async def test_engine_with_all_rules(self):
        """Test engine with all rules loaded."""
        config = VCSConfig(mode=EngineMode.STANDALONE, cache_enabled=False)
        engine = VibeCheckEngine(mode=EngineMode.STANDALONE, config=config)
        
        await engine.start()
        
        # Check that rules are loaded
        assert len(engine.rule_registry.rules) == 32
        
        # Test with complex code that should trigger multiple rules
        content = """
import os
import sys
password="secret123"
def badFunction(a,b,c,d,e,f):
    if a>0:
        if b>0:
            if c>0:
                if d>0:
                    result=eval("1+1")
                    return result
    return None
"""
        target = AnalysisTarget.from_content(Path("test.py"), content)
        result = await engine.analyze(target)
        
        assert result.success
        assert len(result.issues) > 5  # Should find multiple issues
        
        # Check that different rule categories are represented
        categories = {issue.category for issue in result.issues}
        assert len(categories) >= 3  # At least 3 different categories
        
        await engine.stop()


if __name__ == "__main__":
    pytest.main([__file__])
