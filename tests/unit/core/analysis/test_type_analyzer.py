"""
Unit tests for the type analyzer module.
"""

import ast
import pytest
from pathlib import Path
from typing import List

from vibe_check.core.analysis.type_analyzer import (
    TypeAnalyzer,
    TypeInfo,
    TypeAnalysisResult,
    MissingTypeAnnotationRule,
    InconsistentTypeUsageRule
)
from vibe_check.core.analysis.python_semantic_analyzer import SemanticContext


class TestTypeInfo:
    """Tests for the TypeInfo dataclass."""

    def test_type_info_creation(self) -> None:
        """Test TypeInfo creation with default values."""
        type_info = TypeInfo(
            annotation="str",
            line_number=10,
            column_number=5
        )
        
        assert type_info.annotation == "str"
        assert type_info.line_number == 10
        assert type_info.column_number == 5
        assert type_info.is_generic is False
        assert type_info.is_union is False
        assert type_info.is_optional is False
        assert type_info.complexity == 1

    def test_type_info_with_flags(self) -> None:
        """Test TypeInfo creation with custom flags."""
        type_info = TypeInfo(
            annotation="Optional[List[str]]",
            line_number=15,
            column_number=8,
            is_generic=True,
            is_union=False,
            is_optional=True,
            complexity=3
        )
        
        assert type_info.annotation == "Optional[List[str]]"
        assert type_info.is_generic is True
        assert type_info.is_optional is True
        assert type_info.complexity == 3


class TestTypeAnalysisResult:
    """Tests for the TypeAnalysisResult dataclass."""

    def test_empty_result(self) -> None:
        """Test empty TypeAnalysisResult."""
        result = TypeAnalysisResult()
        
        assert result.total_functions == 0
        assert result.typed_functions == 0
        assert result.total_parameters == 0
        assert result.typed_parameters == 0
        assert result.total_returns == 0
        assert result.typed_returns == 0
        assert result.type_annotations == []
        assert result.type_issues == []

    def test_function_type_coverage_empty(self) -> None:
        """Test function type coverage with no functions."""
        result = TypeAnalysisResult()
        assert result.function_type_coverage == 100.0

    def test_function_type_coverage_partial(self) -> None:
        """Test function type coverage with partial typing."""
        result = TypeAnalysisResult(
            total_functions=4,
            typed_functions=3
        )
        assert result.function_type_coverage == 75.0

    def test_parameter_type_coverage(self) -> None:
        """Test parameter type coverage calculation."""
        result = TypeAnalysisResult(
            total_parameters=10,
            typed_parameters=8
        )
        assert result.parameter_type_coverage == 80.0

    def test_return_type_coverage(self) -> None:
        """Test return type coverage calculation."""
        result = TypeAnalysisResult(
            total_returns=5,
            typed_returns=4
        )
        assert result.return_type_coverage == 80.0


class TestTypeAnalyzer:
    """Tests for the TypeAnalyzer class."""

    def test_analyzer_initialization(self) -> None:
        """Test TypeAnalyzer initialization."""
        analyzer = TypeAnalyzer()
        
        assert isinstance(analyzer.result, TypeAnalysisResult)
        assert analyzer.current_function is None
        assert analyzer.scope_stack == []

    def test_analyze_simple_function(self) -> None:
        """Test analysis of a simple typed function."""
        source_code = '''
def add(a: int, b: int) -> int:
    return a + b
'''
        analyzer = TypeAnalyzer()
        result = analyzer.analyze_source(source_code, Path("test.py"))
        
        assert result.total_functions == 1
        assert result.typed_functions == 1
        assert result.total_parameters == 2
        assert result.typed_parameters == 2
        assert result.total_returns == 1
        assert result.typed_returns == 1
        assert result.function_type_coverage == 100.0
        assert result.parameter_type_coverage == 100.0
        assert result.return_type_coverage == 100.0

    def test_analyze_untyped_function(self) -> None:
        """Test analysis of an untyped function."""
        source_code = '''
def add(a, b):
    return a + b
'''
        analyzer = TypeAnalyzer()
        result = analyzer.analyze_source(source_code, Path("test.py"))
        
        assert result.total_functions == 1
        assert result.typed_functions == 0
        assert result.total_parameters == 2
        assert result.typed_parameters == 0
        assert result.total_returns == 1
        assert result.typed_returns == 0
        assert result.function_type_coverage == 0.0
        assert result.parameter_type_coverage == 0.0
        assert result.return_type_coverage == 0.0

    def test_analyze_partially_typed_function(self) -> None:
        """Test analysis of a partially typed function."""
        source_code = '''
def process(data: str, count):
    return data * count
'''
        analyzer = TypeAnalyzer()
        result = analyzer.analyze_source(source_code, Path("test.py"))
        
        assert result.total_functions == 1
        assert result.typed_functions == 0  # Not fully typed
        assert result.total_parameters == 2
        assert result.typed_parameters == 1
        assert result.total_returns == 1
        assert result.typed_returns == 0
        assert result.parameter_type_coverage == 50.0

    def test_analyze_async_function(self) -> None:
        """Test analysis of async functions."""
        source_code = '''
async def fetch_data(url: str) -> dict:
    return {"data": "example"}
'''
        analyzer = TypeAnalyzer()
        result = analyzer.analyze_source(source_code, Path("test.py"))
        
        assert result.total_functions == 1
        assert result.typed_functions == 1
        assert result.total_parameters == 1
        assert result.typed_parameters == 1
        assert result.function_type_coverage == 100.0

    def test_analyze_method_with_self(self) -> None:
        """Test analysis of class methods (self parameter handling)."""
        source_code = '''
class Calculator:
    def add(self, a: int, b: int) -> int:
        return a + b
'''
        analyzer = TypeAnalyzer()
        result = analyzer.analyze_source(source_code, Path("test.py"))

        assert result.total_functions == 1
        assert result.typed_functions == 1
        assert result.total_parameters == 3  # Counts 'self', 'a', 'b'
        assert result.typed_parameters == 2  # Only 'a' and 'b' are typed

    def test_analyze_complex_types(self) -> None:
        """Test analysis of complex type annotations."""
        source_code = '''
from typing import List, Dict, Optional, Union

def process_data(
    items: List[Dict[str, Union[int, str]]],
    config: Optional[Dict[str, Any]] = None
) -> List[str]:
    return []
'''
        analyzer = TypeAnalyzer()
        result = analyzer.analyze_source(source_code, Path("test.py"))
        
        assert result.total_functions == 1
        assert result.typed_functions == 1
        assert len(result.type_annotations) >= 3  # Should capture complex types
        
        # Check for complex type detection
        complex_annotations = [
            ann for ann in result.type_annotations 
            if ann.is_generic or ann.is_union or ann.is_optional
        ]
        assert len(complex_annotations) > 0

    def test_analyze_syntax_error(self) -> None:
        """Test handling of syntax errors."""
        source_code = '''
def broken_function(
    # Missing closing parenthesis and colon
'''
        analyzer = TypeAnalyzer()
        result = analyzer.analyze_source(source_code, Path("test.py"))
        
        # Should return empty result for syntax errors
        assert result.total_functions == 0
        assert result.total_parameters == 0

    def test_analyze_varargs_kwargs(self) -> None:
        """Test analysis of *args and **kwargs."""
        source_code = '''
def flexible_func(
    required: str,
    *args: int,
    **kwargs: str
) -> None:
    pass
'''
        analyzer = TypeAnalyzer()
        result = analyzer.analyze_source(source_code, Path("test.py"))
        
        assert result.total_functions == 1
        assert result.typed_functions == 1
        assert result.total_parameters == 3  # required, *args, **kwargs
        assert result.typed_parameters == 3
        assert result.parameter_type_coverage == 100.0


class TestMissingTypeAnnotationRule:
    """Tests for the MissingTypeAnnotationRule."""

    def test_rule_initialization(self) -> None:
        """Test rule initialization."""
        rule = MissingTypeAnnotationRule()
        
        assert rule.rule_id == "missing_type_annotation"
        assert rule.description == "Detects functions missing type annotations"
        assert rule.severity == "info"
        assert rule.require_return_types is True
        assert rule.require_param_types is True

    def test_check_untyped_function(self) -> None:
        """Test detection of untyped function."""
        source_code = '''
def untyped_function(param):
    return param
'''
        tree = ast.parse(source_code)
        func_node = tree.body[0]

        rule = MissingTypeAnnotationRule()
        context = SemanticContext(file_path=Path("test.py"), module_name="test")
        issues = rule.check(func_node, context)

        assert len(issues) == 2  # Missing return type and parameter type
        assert any("missing return type annotation" in issue.message for issue in issues)
        assert any("parameters without type annotations" in issue.message for issue in issues)

    def test_check_fully_typed_function(self) -> None:
        """Test that fully typed functions don't trigger issues."""
        source_code = '''
def typed_function(param: str) -> str:
    return param
'''
        tree = ast.parse(source_code)
        func_node = tree.body[0]

        rule = MissingTypeAnnotationRule()
        context = SemanticContext(file_path=Path("test.py"), module_name="test")
        issues = rule.check(func_node, context)

        assert len(issues) == 0

    def test_check_special_methods_ignored(self) -> None:
        """Test that special methods are ignored."""
        source_code = '''
def __init__(self, value):
    self.value = value
'''
        tree = ast.parse(source_code)
        func_node = tree.body[0]

        rule = MissingTypeAnnotationRule()
        context = SemanticContext(file_path=Path("test.py"), module_name="test")
        issues = rule.check(func_node, context)

        assert len(issues) == 0  # Special methods should be ignored


class TestInconsistentTypeUsageRule:
    """Tests for the InconsistentTypeUsageRule."""

    def test_rule_initialization(self) -> None:
        """Test rule initialization."""
        rule = InconsistentTypeUsageRule()
        
        assert rule.rule_id == "inconsistent_type_usage"
        assert rule.description == "Detects inconsistent type annotation patterns"
        assert rule.severity == "warning"

    def test_check_returns_empty_for_now(self) -> None:
        """Test that the rule currently returns empty issues."""
        source_code = '''
def some_function(param: str) -> str:
    return param
'''
        tree = ast.parse(source_code)
        func_node = tree.body[0]

        rule = InconsistentTypeUsageRule()
        context = SemanticContext(file_path=Path("test.py"), module_name="test")
        issues = rule.check(func_node, context)

        # Currently returns empty as noted in implementation
        assert len(issues) == 0
