"""
Tests for the project analyzer module.
"""

import pytest
import tempfile
import os
import asyncio
from pathlib import Path
from unittest.mock import patch, MagicMock, AsyncMock
from typing import Any

from vibe_check.core.analysis.project_analyzer import ProjectAnalyzer
from vibe_check.core.models import ProjectMetrics, FileMetrics


class TestProjectAnalyzer:
    """Test cases for the project analyzer."""

    def test_project_analyzer_initialization(self) -> None:
        """Test ProjectAnalyzer initialization."""
        with tempfile.TemporaryDirectory() as temp_dir:
            project_path = Path(temp_dir)
            output_dir = Path(temp_dir) / "output"
            config = {"tools": {"ruff": {"enabled": True}}}

            # Mock PerformanceOptimizer to avoid event loop issues
            with patch('vibe_check.core.analysis.project_analyzer.PerformanceOptimizer'):
                analyzer = ProjectAnalyzer(
                    project_path=project_path,
                    config=config,
                    output_dir=output_dir
                )

                assert analyzer.project_path == project_path.absolute()
                assert analyzer.config == config
                assert analyzer.output_dir == output_dir
                assert output_dir.exists()  # Should be created

    def test_project_analyzer_initialization_semantic_disabled(self) -> None:
        """Test ProjectAnalyzer initialization with semantic analysis disabled."""
        with tempfile.TemporaryDirectory() as temp_dir:
            analyzer = ProjectAnalyzer(temp_dir, enable_semantic_analysis=False)

            assert analyzer.enable_semantic_analysis is False
            assert analyzer.framework_analyzer is None
            assert analyzer.meritocracy_analyzer is None
            assert analyzer.output_formatter is None
            assert analyzer.performance_optimizer is None

    @pytest.mark.asyncio
    async def test_analyze_project_empty_directory(self) -> None:
        """Test analyzing an empty project directory."""
        with tempfile.TemporaryDirectory() as temp_dir:
            with patch('vibe_check.core.utils.file_utils.find_python_files') as mock_find_files:
                mock_find_files.return_value = []

                analyzer = ProjectAnalyzer(temp_dir, enable_semantic_analysis=False)
                result = await analyzer.analyze_project()

                assert isinstance(result, ProjectMetrics)
                assert result.project_path == str(Path(temp_dir).absolute())
                assert len(result.files) == 0

    @pytest.mark.asyncio
    async def test_analyze_project_single_file(self) -> None:
        """Test analyzing a project with a single Python file."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a test Python file
            test_file = Path(temp_dir) / "test.py"
            test_file.write_text("print('hello world')")

            # Mock file analyzer
            mock_file_metrics = FileMetrics(
                path="test.py",
                line_count=1,
                complexity=1,
                type_coverage=0,
                docstring_coverage=0
            )

            with patch('vibe_check.core.utils.file_utils.find_python_files') as mock_find_files, \
                 patch('vibe_check.core.analysis.project_analyzer.FileAnalyzer') as mock_analyzer_class:

                mock_find_files.return_value = [test_file]
                mock_analyzer = AsyncMock()
                mock_analyzer.analyze_file.return_value = mock_file_metrics
                mock_analyzer_class.return_value = mock_analyzer

                analyzer = ProjectAnalyzer(temp_dir, enable_semantic_analysis=False)
                result = await analyzer.analyze_project()

                assert len(result.files) == 1
                assert "test.py" in result.files
                assert result.files["test.py"] == mock_file_metrics

    @pytest.mark.asyncio
    async def test_analyze_project_multiple_files(self) -> None:
        """Test analyzing a project with multiple Python files."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create test Python files
            src_dir = Path(temp_dir) / "src"
            src_dir.mkdir()

            main_file = src_dir / "main.py"
            main_file.write_text("def main(): pass")

            utils_file = src_dir / "utils.py"
            utils_file.write_text("def helper(): pass")

            # Mock file analyzer
            main_metrics = FileMetrics(path="src/main.py", line_count=1, complexity=1)
            utils_metrics = FileMetrics(path="src/utils.py", line_count=1, complexity=1)

            with patch('vibe_check.core.utils.file_utils.find_python_files') as mock_find_files, \
                 patch('vibe_check.core.analysis.project_analyzer.FileAnalyzer') as mock_analyzer_class:

                mock_find_files.return_value = [main_file, utils_file]
                mock_analyzer = AsyncMock()
                mock_analyzer.analyze_file.side_effect = [main_metrics, utils_metrics]
                mock_analyzer_class.return_value = mock_analyzer

                analyzer = ProjectAnalyzer(temp_dir, enable_semantic_analysis=False)
                result = await analyzer.analyze_project()

                assert len(result.files) == 2
                assert "src/main.py" in result.files
                assert "src/utils.py" in result.files

    @pytest.mark.asyncio
    async def test_analyze_project_path_resolution_error(self) -> None:
        """Test handling of path resolution errors."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a test file
            test_file = Path(temp_dir) / "test.py"
            test_file.write_text("print('hello')")

            # Mock file analyzer
            mock_file_metrics = FileMetrics(path="test.py", line_count=1, complexity=1)

            with patch('vibe_check.core.utils.file_utils.find_python_files') as mock_find_files, \
                 patch('vibe_check.core.analysis.project_analyzer.FileAnalyzer') as mock_analyzer_class:

                # Create a file path that will cause relative_to to fail
                problematic_path = Path("/some/other/path/test.py")
                mock_find_files.return_value = [problematic_path]

                mock_analyzer = AsyncMock()
                mock_analyzer.analyze_file.return_value = mock_file_metrics
                mock_analyzer_class.return_value = mock_analyzer

                analyzer = ProjectAnalyzer(temp_dir, enable_semantic_analysis=False)
                result = await analyzer.analyze_project()

                # Should handle the error gracefully and use filename
                assert len(result.files) == 1
                assert "test.py" in result.files

    @pytest.mark.asyncio
    async def test_analyze_project_with_semantic_analysis(self) -> None:
        """Test analyzing a project with semantic analysis enabled."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a test Python file
            test_file = Path(temp_dir) / "test.py"
            test_file.write_text("import flask\napp = flask.Flask(__name__)")

            # Mock file analyzer
            mock_file_metrics = FileMetrics(path="test.py", line_count=2, complexity=1)

            # Mock semantic analysis results
            mock_framework_analysis = {
                'framework_analysis': {'detected_frameworks': ['flask']},
                'semantic_analysis': {'patterns': ['web_framework']},
                'framework_specific_issues': {'flask': []},
                'recommendations': ['Use blueprints for larger applications']
            }

            mock_meritocracy_result = MagicMock()
            mock_meritocracy_result.overall_score = 7.5
            mock_meritocracy_result.architectural_patterns = ['mvc']

            with patch('vibe_check.core.utils.file_utils.find_python_files') as mock_find_files, \
                 patch('vibe_check.core.analysis.project_analyzer.FileAnalyzer') as mock_analyzer_class, \
                 patch('vibe_check.core.analysis.project_analyzer.FrameworkSpecificAnalyzer') as mock_framework_class, \
                 patch('vibe_check.core.analysis.project_analyzer.ProjectMeritocracyAnalyzer') as mock_meritocracy_class, \
                 patch('vibe_check.core.analysis.project_analyzer.SemanticOutputFormatter') as mock_formatter_class, \
                 patch('vibe_check.core.analysis.project_analyzer.PerformanceOptimizer'):

                mock_find_files.return_value = [test_file]

                mock_analyzer = AsyncMock()
                mock_analyzer.analyze_file.return_value = mock_file_metrics
                mock_analyzer_class.return_value = mock_analyzer

                mock_framework = MagicMock()
                mock_framework.analyze_project_with_frameworks.return_value = mock_framework_analysis
                mock_framework_class.return_value = mock_framework

                mock_meritocracy = MagicMock()
                mock_meritocracy.analyze_project_meritocracy.return_value = mock_meritocracy_result
                mock_meritocracy_class.return_value = mock_meritocracy

                mock_formatter = MagicMock()
                mock_formatter.format_comprehensive_report.return_value = "Comprehensive report"
                mock_formatter_class.return_value = mock_formatter

                analyzer = ProjectAnalyzer(temp_dir, enable_semantic_analysis=True)
                result = await analyzer.analyze_project()

                assert hasattr(result, 'semantic_analysis')
                assert 'framework_analysis' in result.semantic_analysis
                assert 'meritocracy_result' in result.semantic_analysis
                assert result.semantic_analysis['meritocracy_result'] == mock_meritocracy_result

    @pytest.mark.asyncio
    async def test_analyze_project_file_read_error(self) -> None:
        """Test handling of file read errors during semantic analysis."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a test file
            test_file = Path(temp_dir) / "test.py"
            test_file.write_text("print('hello')")

            # Mock file analyzer
            mock_file_metrics = FileMetrics(path="test.py", line_count=1, complexity=1)

            with patch('vibe_check.core.utils.file_utils.find_python_files') as mock_find_files, \
                 patch('vibe_check.core.analysis.project_analyzer.FileAnalyzer') as mock_analyzer_class, \
                 patch('vibe_check.core.analysis.project_analyzer.PerformanceOptimizer'), \
                 patch('builtins.open', side_effect=IOError("Permission denied")):

                mock_find_files.return_value = [test_file]

                mock_analyzer = AsyncMock()
                mock_analyzer.analyze_file.return_value = mock_file_metrics
                mock_analyzer_class.return_value = mock_analyzer

                analyzer = ProjectAnalyzer(temp_dir, enable_semantic_analysis=True)
                result = await analyzer.analyze_project()

                # Should handle the error gracefully
                assert len(result.files) == 1

    def test_project_analyzer_string_path(self) -> None:
        """Test ProjectAnalyzer with string path."""
        with tempfile.TemporaryDirectory() as temp_dir:
            with patch('vibe_check.core.analysis.project_analyzer.PerformanceOptimizer'):
                analyzer = ProjectAnalyzer(temp_dir)  # String path

                assert analyzer.project_path == Path(temp_dir).absolute()

    def test_project_analyzer_path_object(self) -> None:
        """Test ProjectAnalyzer with Path object."""
        with tempfile.TemporaryDirectory() as temp_dir:
            path_obj = Path(temp_dir)
            with patch('vibe_check.core.analysis.project_analyzer.PerformanceOptimizer'):
                analyzer = ProjectAnalyzer(path_obj)  # Path object

                assert analyzer.project_path == path_obj.absolute()
