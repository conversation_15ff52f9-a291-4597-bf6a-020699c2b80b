"""
Unit tests for the tool executor module.
"""

import pytest
from pathlib import Path
from typing import Any, Dict
from unittest.mock import AsyncMock, MagicMock, patch

from vibe_check.core.analysis.tool_executor import ToolExecutor


class TestToolExecutor:
    """Tests for the ToolExecutor class."""

    def test_tool_executor_initialization(self) -> None:
        """Test ToolExecutor initialization."""
        executor = ToolExecutor()
        
        assert executor.config == {}
        assert isinstance(executor.available_tools, list)

    def test_tool_executor_initialization_with_config(self) -> None:
        """Test ToolExecutor initialization with config."""
        config = {
            "tools": {
                "ruff": {"enabled": True},
                "mypy": {"enabled": False}
            }
        }
        
        executor = ToolExecutor(config)
        
        assert executor.config == config

    @patch('vibe_check.core.analysis.tool_executor.list_available_tools')
    def test_tool_executor_available_tools(self, mock_list_tools: MagicMock) -> None:
        """Test that available tools are loaded correctly."""
        mock_list_tools.return_value = ["ruff", "mypy", "pylint"]
        
        executor = ToolExecutor()
        
        assert executor.available_tools == ["ruff", "mypy", "pylint"]
        mock_list_tools.assert_called_once()

    @pytest.mark.asyncio
    async def test_run_tools_no_enabled_tools(self) -> None:
        """Test running tools when no tools are enabled."""
        config = {
            "tools": {
                "ruff": {"enabled": False},
                "mypy": {"enabled": False}
            }
        }
        
        with patch('vibe_check.core.analysis.tool_executor.list_available_tools') as mock_list:
            mock_list.return_value = ["ruff", "mypy"]
            executor = ToolExecutor(config)
            
            result = await executor.run_tools("test.py", "print('hello')")
            
            assert result == {}

    @pytest.mark.asyncio
    async def test_run_tools_with_enabled_tools(self) -> None:
        """Test running tools with enabled tools."""
        config = {
            "tools": {
                "ruff": {"enabled": True},
                "mypy": {"enabled": False}
            }
        }
        
        mock_result = {
            "tool": "ruff",
            "issues": [{"code": "E101", "message": "Test issue"}],
            "summary": {"total": 1}
        }
        
        with patch('vibe_check.core.analysis.tool_executor.list_available_tools') as mock_list, \
             patch.object(ToolExecutor, 'run_tool', new_callable=AsyncMock) as mock_run_tool:
            
            mock_list.return_value = ["ruff", "mypy"]
            mock_run_tool.return_value = mock_result
            
            executor = ToolExecutor(config)
            result = await executor.run_tools("test.py", "print('hello')")
            
            assert "ruff" in result
            assert result["ruff"] == mock_result
            mock_run_tool.assert_called_once_with("ruff", "test.py", "print('hello')", {"enabled": True})

    @pytest.mark.asyncio
    async def test_run_tools_with_meta_analysis(self) -> None:
        """Test running tools with meta-analysis."""
        config = {
            "tools": {
                "ruff": {"enabled": True},
                "mypy": {"enabled": True}
            }
        }
        
        mock_ruff_result = {
            "tool": "ruff",
            "issues": [{"code": "E101", "message": "Ruff issue"}],
            "summary": {"total": 1}
        }
        
        mock_mypy_result = {
            "tool": "mypy",
            "issues": [{"code": "error", "message": "Mypy issue"}],
            "summary": {"total": 1}
        }
        
        mock_meta_result = {
            "combined_issues": 2,
            "tool_agreement": 0.5
        }
        
        with patch('vibe_check.core.analysis.tool_executor.list_available_tools') as mock_list, \
             patch.object(ToolExecutor, 'run_tool', new_callable=AsyncMock) as mock_run_tool, \
             patch('vibe_check.core.analysis.meta_analyzer.MetaAnalyzer') as mock_meta_analyzer:
            
            mock_list.return_value = ["ruff", "mypy"]
            mock_run_tool.side_effect = [mock_ruff_result, mock_mypy_result]
            
            mock_analyzer_instance = MagicMock()
            mock_analyzer_instance.analyze_combined_results.return_value = mock_meta_result
            mock_meta_analyzer.return_value = mock_analyzer_instance
            
            executor = ToolExecutor(config)
            result = await executor.run_tools("test.py", "print('hello')")
            
            assert "ruff" in result
            assert "mypy" in result
            assert "_meta_analysis" in result
            assert result["_meta_analysis"] == mock_meta_result

    @pytest.mark.asyncio
    async def test_run_tool_success(self) -> None:
        """Test successful tool execution."""
        mock_runner = AsyncMock()
        mock_result = {
            "issues": [{"code": "E101", "message": "Test issue"}],
            "summary": {"total": 1}
        }
        mock_runner.run.return_value = mock_result
        
        with patch('vibe_check.core.analysis.tool_executor.get_runner_for_tool') as mock_get_runner:
            mock_get_runner.return_value = mock_runner
            
            executor = ToolExecutor()
            result = await executor.run_tool("ruff", "test.py", "print('hello')")
            
            assert result["tool"] == "ruff"
            assert result["tool_status"] == "available"
            assert result["issues"] == mock_result["issues"]
            mock_runner.run.assert_called_once_with(file_path="test.py", content="print('hello')")

    @pytest.mark.asyncio
    async def test_run_tool_with_args(self) -> None:
        """Test tool execution with arguments."""
        mock_runner = AsyncMock()
        mock_result = {
            "issues": [{"code": "E101", "message": "Test issue"}],
            "summary": {"total": 1}
        }
        mock_runner.run_with_args.return_value = mock_result
        
        tool_config = {"args": ["--strict", "--verbose"]}
        
        with patch('vibe_check.core.analysis.tool_executor.get_runner_for_tool') as mock_get_runner:
            mock_get_runner.return_value = mock_runner
            
            executor = ToolExecutor()
            result = await executor.run_tool("mypy", "test.py", "print('hello')", tool_config)
            
            assert result["tool"] == "mypy"
            assert result["tool_status"] == "available"
            mock_runner.run_with_args.assert_called_once_with(
                file_path="test.py", 
                content="print('hello')", 
                args=["--strict", "--verbose"]
            )

    @pytest.mark.asyncio
    async def test_run_tool_no_runner(self) -> None:
        """Test tool execution when no runner is found."""
        with patch('vibe_check.core.analysis.tool_executor.get_runner_for_tool') as mock_get_runner:
            mock_get_runner.return_value = None
            
            executor = ToolExecutor()
            result = await executor.run_tool("unknown_tool", "test.py", "print('hello')")
            
            assert "error" in result
            assert "No runner found for tool: unknown_tool" in result["error"]
            assert result["issues"] == []

    @pytest.mark.asyncio
    async def test_run_tool_unavailable_with_fallback(self) -> None:
        """Test tool execution when tool is unavailable but has fallback."""
        mock_runner = MagicMock()
        mock_runner.is_available.return_value = False
        mock_fallback_result = {
            "issues": [{"code": "fallback", "message": "Fallback analysis"}],
            "summary": {"total": 1}
        }
        mock_runner.get_fallback_analysis.return_value = mock_fallback_result
        
        with patch('vibe_check.core.analysis.tool_executor.get_runner_for_tool') as mock_get_runner:
            mock_get_runner.return_value = mock_runner
            
            executor = ToolExecutor()
            result = await executor.run_tool("pylint", "test.py", "print('hello')")
            
            assert result["tool"] == "pylint"
            assert result["issues"] == mock_fallback_result["issues"]
            mock_runner.get_fallback_analysis.assert_called_once_with("test.py", "print('hello')")

    @pytest.mark.asyncio
    async def test_run_tool_unavailable_no_fallback(self) -> None:
        """Test tool execution when tool is unavailable and no fallback."""
        mock_runner = MagicMock()
        mock_runner.is_available.return_value = False
        # Ensure the runner doesn't have get_fallback_analysis method
        del mock_runner.get_fallback_analysis

        with patch('vibe_check.core.analysis.tool_executor.get_runner_for_tool') as mock_get_runner:
            mock_get_runner.return_value = mock_runner

            executor = ToolExecutor()
            result = await executor.run_tool("bandit", "test.py", "print('hello')")

            assert result["tool"] == "bandit"
            assert result["tool_status"] == "unavailable"
            assert result["fallback_used"] is False
            assert "not available and no fallback provided" in result["message"]
            assert result["issues"] == []

    @pytest.mark.asyncio
    async def test_run_tool_exception_handling(self) -> None:
        """Test tool execution exception handling."""
        mock_runner = AsyncMock()
        mock_runner.run.side_effect = Exception("Tool execution failed")
        
        with patch('vibe_check.core.analysis.tool_executor.get_runner_for_tool') as mock_get_runner:
            mock_get_runner.return_value = mock_runner
            
            executor = ToolExecutor()
            result = await executor.run_tool("ruff", "test.py", "print('hello')")
            
            assert "error" in result
            assert "Tool execution failed" in result["error"]
            assert result["issues"] == []

    @pytest.mark.asyncio
    async def test_run_tools_empty_result_filtered(self) -> None:
        """Test that empty results are filtered out."""
        config = {
            "tools": {
                "ruff": {"enabled": True},
                "mypy": {"enabled": True}
            }
        }

        with patch('vibe_check.core.analysis.tool_executor.list_available_tools') as mock_list, \
             patch.object(ToolExecutor, 'run_tool', new_callable=AsyncMock) as mock_run_tool:

            mock_list.return_value = ["ruff", "mypy"]
            # First call returns valid result, second returns None/empty
            mock_run_tool.side_effect = [
                {"tool": "ruff", "issues": [{"code": "E101"}]},
                None
            ]

            executor = ToolExecutor(config)
            result = await executor.run_tools("test.py", "print('hello')")

            assert "ruff" in result
            assert "mypy" not in result
            # Meta-analysis is performed when there are results, so it should be present
            assert "_meta_analysis" in result

    def test_tool_executor_path_handling(self) -> None:
        """Test that Path objects are handled correctly."""
        executor = ToolExecutor()
        
        # Should work with both string and Path objects
        assert isinstance(executor.config, dict)
        
        # The actual path handling is tested in the async methods above
