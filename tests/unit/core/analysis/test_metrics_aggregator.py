"""
Unit tests for the metrics aggregator module.
"""

import pytest
from pathlib import Path
from typing import Any, Dict

from vibe_check.core.analysis.metrics_aggregator import MetricsAggregator
from vibe_check.core.models.project_metrics import ProjectMetrics
from vibe_check.core.models.file_metrics import FileMetrics
from vibe_check.core.models.directory_metrics import DirectoryMetrics


class TestMetricsAggregator:
    """Tests for the MetricsAggregator class."""

    def test_metrics_aggregator_initialization(self) -> None:
        """Test MetricsAggregator initialization."""
        aggregator = MetricsAggregator()
        
        # Should initialize without errors
        assert aggregator is not None

    def test_aggregate_metrics_empty_project(self) -> None:
        """Test aggregating metrics for an empty project."""
        aggregator = MetricsAggregator()
        project_metrics = ProjectMetrics(project_path="test_project")
        
        aggregator.aggregate_metrics(project_metrics)
        
        # Should handle empty project gracefully
        assert len(project_metrics.directories) == 0
        assert len(project_metrics.files) == 0

    def test_aggregate_metrics_single_file(self) -> None:
        """Test aggregating metrics for a project with a single file."""
        aggregator = MetricsAggregator()
        project_metrics = ProjectMetrics(project_path="test_project")
        
        # Add a single file
        file_metrics = FileMetrics(
            path="src/main.py",
            line_count=100,
            complexity=5,
            type_coverage=80,
            docstring_coverage=60
        )
        file_metrics.add_issue("E101", "Test issue", 10, "error")
        project_metrics.files["src/main.py"] = file_metrics
        
        aggregator.aggregate_metrics(project_metrics)
        
        # Check directory metrics
        assert "src" in project_metrics.directories
        dir_metrics = project_metrics.directories["src"]
        assert dir_metrics.path == "src"
        assert len(dir_metrics.files) == 1
        assert "src/main.py" in dir_metrics.files
        assert dir_metrics.total_lines == 100
        assert dir_metrics.avg_lines == 100.0
        assert dir_metrics.max_file_lines == 100
        assert dir_metrics.max_file == "src/main.py"
        
        # Check project-level metrics
        assert "src/main.py" in project_metrics.complexity_scores
        assert project_metrics.complexity_scores["src/main.py"] == 5
        assert "src/main.py" in project_metrics.type_coverage
        assert project_metrics.type_coverage["src/main.py"] == 80
        assert "src/main.py" in project_metrics.doc_coverage
        assert project_metrics.doc_coverage["src/main.py"] == 60

    def test_aggregate_metrics_multiple_files_same_directory(self) -> None:
        """Test aggregating metrics for multiple files in the same directory."""
        aggregator = MetricsAggregator()
        project_metrics = ProjectMetrics(project_path="test_project")
        
        # Add multiple files in the same directory
        file1 = FileMetrics(
            path="src/main.py",
            line_count=100,
            complexity=5,
            type_coverage=80,
            docstring_coverage=60
        )
        file1.add_issue("E101", "Test issue 1", 10, "error")
        
        file2 = FileMetrics(
            path="src/utils.py",
            line_count=50,
            complexity=3,
            type_coverage=90,
            docstring_coverage=70
        )
        file2.add_issue("W201", "Test issue 2", 5, "warning")
        file2.add_issue("E102", "Test issue 3", 15, "error")
        
        project_metrics.files["src/main.py"] = file1
        project_metrics.files["src/utils.py"] = file2
        
        aggregator.aggregate_metrics(project_metrics)
        
        # Check directory metrics
        assert "src" in project_metrics.directories
        dir_metrics = project_metrics.directories["src"]
        assert len(dir_metrics.files) == 2
        assert "src/main.py" in dir_metrics.files
        assert "src/utils.py" in dir_metrics.files
        assert dir_metrics.total_lines == 150  # 100 + 50
        assert dir_metrics.avg_lines == 75.0  # 150 / 2
        assert dir_metrics.max_file_lines == 100
        assert dir_metrics.max_file == "src/main.py"

    def test_aggregate_metrics_multiple_directories(self) -> None:
        """Test aggregating metrics for files in multiple directories."""
        aggregator = MetricsAggregator()
        project_metrics = ProjectMetrics(project_path="test_project")
        
        # Add files in different directories
        file1 = FileMetrics(
            path="src/main.py",
            line_count=100,
            complexity=5,
            type_coverage=80,
            docstring_coverage=60
        )
        
        file2 = FileMetrics(
            path="tests/test_main.py",
            line_count=80,
            complexity=2,
            type_coverage=70,
            docstring_coverage=40
        )
        
        file3 = FileMetrics(
            path="docs/readme.md",
            line_count=20,
            complexity=0,
            type_coverage=0,
            docstring_coverage=0
        )
        
        project_metrics.files["src/main.py"] = file1
        project_metrics.files["tests/test_main.py"] = file2
        project_metrics.files["docs/readme.md"] = file3
        
        aggregator.aggregate_metrics(project_metrics)
        
        # Check that all directories are created
        assert "src" in project_metrics.directories
        assert "tests" in project_metrics.directories
        assert "docs" in project_metrics.directories
        
        # Check src directory
        src_dir = project_metrics.directories["src"]
        assert len(src_dir.files) == 1
        assert src_dir.total_lines == 100
        assert src_dir.avg_lines == 100.0
        
        # Check tests directory
        tests_dir = project_metrics.directories["tests"]
        assert len(tests_dir.files) == 1
        assert tests_dir.total_lines == 80
        assert tests_dir.avg_lines == 80.0
        
        # Check docs directory
        docs_dir = project_metrics.directories["docs"]
        assert len(docs_dir.files) == 1
        assert docs_dir.total_lines == 20
        assert docs_dir.avg_lines == 20.0

    def test_aggregate_metrics_complexity_tracking(self) -> None:
        """Test that complexity metrics are properly tracked."""
        aggregator = MetricsAggregator()
        project_metrics = ProjectMetrics(project_path="test_project")
        
        # Add files with different complexity levels
        file1 = FileMetrics(path="src/simple.py", line_count=50, complexity=2)
        file2 = FileMetrics(path="src/complex.py", line_count=200, complexity=15)
        file3 = FileMetrics(path="src/medium.py", line_count=100, complexity=8)
        
        project_metrics.files["src/simple.py"] = file1
        project_metrics.files["src/complex.py"] = file2
        project_metrics.files["src/medium.py"] = file3
        
        aggregator.aggregate_metrics(project_metrics)
        
        # Check directory complexity metrics
        dir_metrics = project_metrics.directories["src"]
        assert dir_metrics._max_complexity == 15  # Highest complexity
        assert dir_metrics._avg_complexity == (2 + 15 + 8) / 3  # Average complexity
        
        # Check project-level complexity tracking
        assert project_metrics.complexity_scores["src/simple.py"] == 2
        assert project_metrics.complexity_scores["src/complex.py"] == 15
        assert project_metrics.complexity_scores["src/medium.py"] == 8

    def test_aggregate_metrics_issue_tracking(self) -> None:
        """Test that issues are properly tracked by severity and tool."""
        aggregator = MetricsAggregator()
        project_metrics = ProjectMetrics(project_path="test_project")
        
        # Add files with different types of issues
        file1 = FileMetrics(path="src/file1.py", line_count=100)
        file1.add_issue("E101", "Error issue", 10, "error")
        file1.add_issue("W201", "Warning issue", 20, "warning")
        
        file2 = FileMetrics(path="src/file2.py", line_count=80)
        file2.add_issue("E102", "Another error", 5, "error")
        file2.add_issue("I301", "Info issue", 15, "info")
        
        # Add issues with source/tool information
        file1.issues[0]["source"] = "ruff"
        file1.issues[1]["source"] = "pylint"
        file2.issues[0]["source"] = "ruff"
        file2.issues[1]["source"] = "mypy"
        
        project_metrics.files["src/file1.py"] = file1
        project_metrics.files["src/file2.py"] = file2
        
        aggregator.aggregate_metrics(project_metrics)
        
        # Check directory issue count
        dir_metrics = project_metrics.directories["src"]
        assert dir_metrics._issue_count == 4  # Total issues in directory
        
        # Check issues by severity
        assert project_metrics._issues_by_severity["error"] == 2
        assert project_metrics._issues_by_severity["warning"] == 1
        assert project_metrics._issues_by_severity["info"] == 1
        
        # Check issues by tool
        assert project_metrics._issues_by_tool["ruff"] == 2
        assert project_metrics._issues_by_tool["pylint"] == 1
        assert project_metrics._issues_by_tool["mypy"] == 1

    def test_aggregate_metrics_coverage_tracking(self) -> None:
        """Test that type and docstring coverage are properly tracked."""
        aggregator = MetricsAggregator()
        project_metrics = ProjectMetrics(project_path="test_project")
        
        # Add files with different coverage levels
        file1 = FileMetrics(
            path="src/well_typed.py",
            line_count=100,
            type_coverage=95,
            docstring_coverage=85
        )
        
        file2 = FileMetrics(
            path="src/poorly_typed.py",
            line_count=80,
            type_coverage=30,
            docstring_coverage=20
        )
        
        project_metrics.files["src/well_typed.py"] = file1
        project_metrics.files["src/poorly_typed.py"] = file2
        
        aggregator.aggregate_metrics(project_metrics)
        
        # Check type coverage tracking
        assert project_metrics.type_coverage["src/well_typed.py"] == 95
        assert project_metrics.type_coverage["src/poorly_typed.py"] == 30
        
        # Check docstring coverage tracking
        assert project_metrics.doc_coverage["src/well_typed.py"] == 85
        assert project_metrics.doc_coverage["src/poorly_typed.py"] == 20

    def test_aggregate_metrics_root_directory_files(self) -> None:
        """Test aggregating metrics for files in the root directory."""
        aggregator = MetricsAggregator()
        project_metrics = ProjectMetrics(project_path="test_project")
        
        # Add a file in the root directory (empty dirname)
        file_metrics = FileMetrics(
            path="main.py",
            line_count=50,
            complexity=3
        )
        project_metrics.files["main.py"] = file_metrics
        
        aggregator.aggregate_metrics(project_metrics)
        
        # Check that root directory is handled correctly
        assert "" in project_metrics.directories  # Empty string for root
        root_dir = project_metrics.directories[""]
        assert len(root_dir.files) == 1
        assert "main.py" in root_dir.files
        assert root_dir.total_lines == 50

    def test_aggregate_metrics_edge_cases(self) -> None:
        """Test edge cases in metrics aggregation."""
        aggregator = MetricsAggregator()
        project_metrics = ProjectMetrics(project_path="test_project")
        
        # Add file with zero values
        file_metrics = FileMetrics(
            path="src/empty.py",
            line_count=0,
            complexity=0,
            type_coverage=0,
            docstring_coverage=0
        )
        project_metrics.files["src/empty.py"] = file_metrics
        
        aggregator.aggregate_metrics(project_metrics)
        
        # Should handle zero values gracefully
        dir_metrics = project_metrics.directories["src"]
        assert dir_metrics.total_lines == 0
        assert dir_metrics.avg_lines == 0.0
        assert dir_metrics._max_complexity == 0
        assert dir_metrics._avg_complexity == 0.0

    def test_aggregate_metrics_issues_without_severity(self) -> None:
        """Test handling of issues without severity information."""
        aggregator = MetricsAggregator()
        project_metrics = ProjectMetrics(project_path="test_project")
        
        # Add file with issue missing severity
        file_metrics = FileMetrics(path="src/test.py", line_count=50)
        file_metrics.issues.append({
            "code": "TEST001",
            "message": "Test issue without severity",
            "line": 10
            # No severity field
        })
        project_metrics.files["src/test.py"] = file_metrics
        
        aggregator.aggregate_metrics(project_metrics)
        
        # Should default to 'medium' severity
        assert project_metrics._issues_by_severity.get("medium", 0) == 1

    def test_aggregate_metrics_issues_without_source(self) -> None:
        """Test handling of issues without source/tool information."""
        aggregator = MetricsAggregator()
        project_metrics = ProjectMetrics(project_path="test_project")
        
        # Add file with issue missing source
        file_metrics = FileMetrics(path="src/test.py", line_count=50)
        file_metrics.issues.append({
            "code": "TEST001",
            "message": "Test issue without source",
            "line": 10,
            "severity": "error"
            # No source field
        })
        project_metrics.files["src/test.py"] = file_metrics
        
        aggregator.aggregate_metrics(project_metrics)
        
        # Should default to 'unknown' tool
        assert project_metrics._issues_by_tool.get("unknown", 0) == 1
