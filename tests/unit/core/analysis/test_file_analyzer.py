"""
Tests for the file analyzer module.
"""

import asyncio
import pytest
import tempfile
import os
from pathlib import Path
from typing import Any, Dict
from unittest.mock import patch, MagicMock

from vibe_check.core.analysis.file_analyzer import FileAnalyzer
from vibe_check.core.models import FileMetrics


class TestFileAnalyzer:
    """Test cases for the file analyzer."""

    def test_file_analyzer_initialization(self, sample_config: Dict[str, Any]) -> None:
        """Test FileAnalyzer initialization."""
        project_path = Path("/tmp/test_project")
        analyzer = FileAnalyzer(project_path=project_path, tools_config=sample_config)
        assert analyzer.project_path == project_path
        assert analyzer.tools_config == sample_config

    @pytest.mark.asyncio
    async def test_file_analyzer_analyze_python_file(self, sample_python_file: str, sample_config: Dict[str, Any]) -> None:
        """Test analysis of a Python file."""
        file_path = Path(sample_python_file)
        project_path = file_path.parent

        analyzer = FileAnalyzer(project_path=project_path, tools_config=sample_config)
        result = await analyzer.analyze_file(file_path)

        # Check result type and basic properties
        assert isinstance(result, FileMetrics)
        assert result.lines > 0
        assert result.complexity >= 0
        assert hasattr(result, 'issues')
        assert isinstance(result.issues, list)

    @pytest.mark.asyncio
    async def test_file_analyzer_analyze_simple_file(self, sample_config: Dict[str, Any]) -> None:
        """Test analysis of a simple Python file."""
        # Create a simple Python file
        fd, file_path = tempfile.mkstemp(suffix=".py")
        try:
            with os.fdopen(fd, 'w') as f:
                f.write("""
def simple_function():
    return "hello world"

x = 1 + 2
""")

            file_path_obj = Path(file_path)
            project_path = file_path_obj.parent
            analyzer = FileAnalyzer(project_path=project_path, tools_config=sample_config)
            result = await analyzer.analyze_file(file_path_obj)

            assert result.lines > 0
            assert result.complexity >= 1  # At least one function

        finally:
            os.unlink(file_path)

    @pytest.mark.asyncio
    async def test_file_analyzer_analyze_complex_file(self, sample_config: Dict[str, Any]) -> None:
        """Test analysis of a more complex Python file."""
        # Create a complex Python file
        fd, file_path = tempfile.mkstemp(suffix=".py")
        try:
            with os.fdopen(fd, 'w') as f:
                f.write("""
def complex_function(x, y, z):
    if x > 0:
        if y > 0:
            if z > 0:
                return x + y + z
            else:
                return x + y
        else:
            return x
    else:
        return 0

class TestClass:
    def __init__(self):
        self.value = 0

    def method1(self):
        for i in range(10):
            if i % 2 == 0:
                self.value += i
            else:
                self.value -= i
        return self.value
""")

            file_path_obj = Path(file_path)
            project_path = file_path_obj.parent
            analyzer = FileAnalyzer(project_path=project_path, tools_config=sample_config)
            result = await analyzer.analyze_file(file_path_obj)

            assert result.lines > 10
            assert result.complexity > 1  # Multiple functions and complexity

        finally:
            os.unlink(file_path)

    @pytest.mark.asyncio
    async def test_file_analyzer_nonexistent_file(self, sample_config: Dict[str, Any]) -> None:
        """Test analysis of nonexistent file."""
        project_path = Path("/tmp")
        file_path = project_path / "nonexistent_file.py"

        analyzer = FileAnalyzer(project_path=project_path, tools_config=sample_config)

        # Should handle nonexistent file gracefully by returning a result with error info
        result = await analyzer.analyze_file(file_path)

        # Should return a FileMetrics object with basic info
        assert isinstance(result, FileMetrics)
        assert result.lines == 0  # No lines since file doesn't exist
        assert result.complexity == 0  # No complexity since file doesn't exist

    @pytest.mark.asyncio
    async def test_file_analyzer_empty_file(self, sample_config: Dict[str, Any]) -> None:
        """Test analysis of empty file."""
        # Create an empty Python file
        fd, file_path = tempfile.mkstemp(suffix=".py")
        try:
            os.close(fd)  # Close without writing anything

            file_path_obj = Path(file_path)
            project_path = file_path_obj.parent
            analyzer = FileAnalyzer(project_path=project_path, tools_config=sample_config)
            result = await analyzer.analyze_file(file_path_obj)

            assert isinstance(result, FileMetrics)
            assert result.lines == 0
            assert result.complexity == 0

        finally:
            os.unlink(file_path)

    @pytest.mark.asyncio
    async def test_file_analyzer_with_syntax_error(self, sample_config: Dict[str, Any]) -> None:
        """Test analysis of file with syntax error."""
        # Create a Python file with syntax error
        fd, file_path = tempfile.mkstemp(suffix=".py")
        try:
            with os.fdopen(fd, 'w') as f:
                f.write("""
def broken_function(
    # Missing closing parenthesis and colon
    return "this will cause syntax error"
""")

            file_path_obj = Path(file_path)
            project_path = file_path_obj.parent
            analyzer = FileAnalyzer(project_path=project_path, tools_config=sample_config)
            result = await analyzer.analyze_file(file_path_obj)

            # Should still return a result, even with syntax errors
            assert isinstance(result, FileMetrics)
            assert result.lines > 0

        finally:
            os.unlink(file_path)

    @patch('vibe_check.core.analysis.file_analyzer.ToolRunner')
    def test_file_analyzer_with_mock_tools(self, mock_tool_runner_class, sample_python_file, sample_config):
        """Test file analyzer with mocked tool runner."""
        # Setup mock
        mock_tool_runner = MagicMock()
        mock_tool_runner_class.return_value = mock_tool_runner
        
        # Mock tool results
        mock_tool_runner.run_tools.return_value = {
            'ruff': {'issues': []},
            'mypy': {'issues': []},
            'complexity': {'score': 5}
        }
        
        file_path = Path(sample_python_file)
        
        analyzer = FileAnalyzer(config=sample_config)
        result = analyzer.analyze(file_path)
        
        # Verify mock was called
        mock_tool_runner_class.assert_called_once_with(sample_config)
        mock_tool_runner.run_tools.assert_called_once_with(file_path)
        
        # Check result
        assert isinstance(result, FileMetrics)
        assert 'ruff' in result.tool_results
        assert 'mypy' in result.tool_results
        assert 'complexity' in result.tool_results

    @pytest.mark.asyncio
    async def test_file_analyzer_with_disabled_tools(self, sample_python_file: str) -> None:
        """Test file analyzer with all tools disabled."""
        file_path = Path(sample_python_file)
        project_path = file_path.parent

        # Create config with all tools disabled
        config = {
            "tools": {
                "ruff": {"enabled": False},
                "mypy": {"enabled": False},
                "bandit": {"enabled": False},
                "complexity": {"enabled": False}
            }
        }

        analyzer = FileAnalyzer(project_path=project_path, tools_config=config)
        result = await analyzer.analyze_file(file_path)

        # Should still return valid result
        assert isinstance(result, FileMetrics)
        assert result.lines > 0

    @pytest.mark.asyncio
    async def test_file_analyzer_non_python_file(self, sample_config: Dict[str, Any]) -> None:
        """Test analysis of non-Python file."""
        # Create a text file
        fd, file_path = tempfile.mkstemp(suffix=".txt")
        try:
            with os.fdopen(fd, 'w') as f:
                f.write("This is a text file\nwith multiple lines\n")

            file_path_obj = Path(file_path)
            project_path = file_path_obj.parent
            analyzer = FileAnalyzer(project_path=project_path, tools_config=sample_config)
            result = await analyzer.analyze_file(file_path_obj)

            # Should handle non-Python files
            assert isinstance(result, FileMetrics)
            assert result.lines > 0
            # Complexity might be 0 for non-Python files
            assert result.complexity >= 0

        finally:
            os.unlink(file_path)
