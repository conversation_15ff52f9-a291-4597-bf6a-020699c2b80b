"""
Tests for VCS Complexity Analysis Implementation.

Validates that all 5 complexity rules (C001-C005) are working correctly
and detecting complexity issues as expected.
"""

import ast
import pytest
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

from vibe_check.core.vcs.rules.complexity_rules import (
    CyclomaticComplexityRule, FunctionLengthRule, NestedComplexityRule,
    ParameterCountRule, ClassComplexityRule
)
from vibe_check.core.vcs.registry import AnalysisTarget, AnalysisContext
from vibe_check.core.vcs.models import RuleCategory, IssueSeverity


class TestComplexityRules:
    """Test suite for complexity analysis rules."""

    @pytest.fixture
    def mock_context(self):
        """Create a mock analysis context."""
        context = Mock(spec=AnalysisContext)
        context.project_path = "/test/project"
        context.config = Mock()
        context.config.get_rule_config.return_value = {}
        return context

    @pytest.fixture
    def mock_target(self):
        """Create a mock analysis target."""
        target = Mock(spec=AnalysisTarget)
        target.file_path = "/test/file.py"
        target.relative_path = "test/file.py"
        return target

    async def test_cyclomatic_complexity_rule_c001(self, mock_context, mock_target):
        """Test C001: Cyclomatic Complexity Rule."""
        rule = CyclomaticComplexityRule()

        # Test code with high cyclomatic complexity
        complex_code = '''
def complex_function(x):
    if x > 0:
        if x > 10:
            if x > 20:
                if x > 30:
                    if x > 40:
                        if x > 50:
                            return "very high"
                        else:
                            return "high"
                    else:
                        return "medium-high"
                else:
                    return "medium"
            else:
                return "low-medium"
        else:
            return "low"
    else:
        return "negative"
'''

        # Parse the code into AST
        ast_tree = ast.parse(complex_code)

        issues = await rule.analyze(mock_target, complex_code, ast_tree, mock_context)

        # Should detect high complexity
        assert len(issues) > 0
        assert any(issue.rule_id == "C001" for issue in issues)
        assert any("cyclomatic complexity" in issue.message.lower() for issue in issues)

    async def test_function_length_rule_c002(self, mock_context, mock_target):
        """Test C002: Function Length Rule."""
        rule = FunctionLengthRule()

        # Test code with very long function (60+ lines)
        long_function_lines = ["def long_function():"] + [f"    line_{i} = {i}" for i in range(60)]
        long_code = "\n".join(long_function_lines)

        # Parse the code into AST
        ast_tree = ast.parse(long_code)

        issues = await rule.analyze(mock_target, long_code, ast_tree, mock_context)

        # Should detect long function
        assert len(issues) > 0
        assert any(issue.rule_id == "C002" for issue in issues)
        assert any("too long" in issue.message.lower() for issue in issues)

    async def test_nesting_depth_rule_c003(self, mock_context, mock_target):
        """Test C003: Nesting Depth Rule."""
        rule = NestedComplexityRule()

        # Test code with excessive nesting (6+ levels)
        nested_code = '''
def deeply_nested():
    if True:
        if True:
            if True:
                if True:
                    if True:
                        if True:
                            return "too deep"
'''

        # Parse the code into AST
        ast_tree = ast.parse(nested_code)

        issues = await rule.analyze(mock_target, nested_code, ast_tree, mock_context)

        # Should detect excessive nesting
        assert len(issues) > 0
        assert any(issue.rule_id == "C003" for issue in issues)
        assert any("nesting depth" in issue.message.lower() for issue in issues)

    async def test_parameter_count_rule_c004(self, mock_context, mock_target):
        """Test C004: Parameter Count Rule."""
        rule = ParameterCountRule()

        # Test code with too many parameters (8 parameters)
        many_params_code = '''
def many_params_function(a, b, c, d, e, f, g, h):
    return a + b + c + d + e + f + g + h
'''

        # Parse the code into AST
        ast_tree = ast.parse(many_params_code)

        issues = await rule.analyze(mock_target, many_params_code, ast_tree, mock_context)

        # Should detect too many parameters
        assert len(issues) > 0
        assert any(issue.rule_id == "C004" for issue in issues)
        assert any("too many parameters" in issue.message.lower() for issue in issues)

    async def test_class_complexity_rule_c005(self, mock_context, mock_target):
        """Test C005: Class Complexity Rule."""
        rule = ClassComplexityRule()

        # Test code with class having too many methods (25+ methods)
        method_lines = [f"    def method_{i}(self): pass" for i in range(25)]
        complex_class_code = "class ComplexClass:\n" + "\n".join(method_lines)

        # Parse the code into AST
        ast_tree = ast.parse(complex_class_code)

        issues = await rule.analyze(mock_target, complex_class_code, ast_tree, mock_context)

        # Should detect complex class
        assert len(issues) > 0
        assert any(issue.rule_id == "C005" for issue in issues)
        assert any("complex" in issue.message.lower() for issue in issues)

    async def test_complexity_rules_severity_levels(self, mock_context, mock_target):
        """Test that complexity rules assign appropriate severity levels."""
        # Test with moderately complex code
        moderate_code = '''
def moderate_function(a, b, c, d, e, f):  # 6 params (should be INFO/WARNING)
    if a > 0:
        if b > 0:
            if c > 0:
                if d > 0:
                    if e > 0:  # 5 levels nesting (should be WARNING)
                        return f
    return 0
'''

        # Parse the code into AST
        ast_tree = ast.parse(moderate_code)

        # Test parameter count rule
        param_rule = ParameterCountRule()
        param_issues = await param_rule.analyze(mock_target, moderate_code, ast_tree, mock_context)

        # Test nesting rule
        nesting_rule = NestedComplexityRule()
        nesting_issues = await nesting_rule.analyze(mock_target, moderate_code, ast_tree, mock_context)

        # Verify issues are detected with appropriate severity
        if param_issues:
            assert param_issues[0].severity in [IssueSeverity.INFO, IssueSeverity.WARNING]

        if nesting_issues:
            assert nesting_issues[0].severity in [IssueSeverity.WARNING, IssueSeverity.ERROR]

    async def test_complexity_rules_no_false_positives(self, mock_context, mock_target):
        """Test that complexity rules don't generate false positives for simple code."""
        simple_code = '''
def simple_function(x, y):
    """A simple function with low complexity."""
    if x > y:
        return x
    return y

class SimpleClass:
    def __init__(self):
        pass

    def method1(self):
        return 1

    def method2(self):
        return 2
'''

        # Parse the code into AST
        ast_tree = ast.parse(simple_code)

        # Test all complexity rules
        rules = [
            CyclomaticComplexityRule(),
            FunctionLengthRule(),
            NestedComplexityRule(),
            ParameterCountRule(),
            ClassComplexityRule()
        ]

        total_issues = 0
        for rule in rules:
            issues = await rule.analyze(mock_target, simple_code, ast_tree, mock_context)
            total_issues += len(issues)

        # Simple code should have minimal or no complexity issues
        assert total_issues <= 2  # Allow for very minor issues

    async def test_complexity_rules_integration(self, mock_context, mock_target):
        """Test that all complexity rules work together correctly."""
        # Test code that should trigger multiple complexity rules
        complex_code = '''
def very_complex_function(a, b, c, d, e, f, g, h, i, j):  # C004: Too many params
    """This function has multiple complexity issues."""  # C002: Will be long
    result = 0
    if a > 0:
        if b > 0:
            if c > 0:
                if d > 0:
                    if e > 0:
                        if f > 0:  # C003: Deep nesting
                            for i in range(10):  # C001: High cyclomatic complexity
                                if i % 2 == 0:
                                    if i % 4 == 0:
                                        if i % 8 == 0:
                                            result += i * a * b * c * d * e * f
                                        else:
                                            result += i * a * b * c * d * e
                                    else:
                                        result += i * a * b * c * d
                                else:
                                    result += i * a * b * c
                            return result
    # Add more lines to make function long
    line1 = "padding"
    line2 = "padding"
    line3 = "padding"
    line4 = "padding"
    line5 = "padding"
    line6 = "padding"
    line7 = "padding"
    line8 = "padding"
    line9 = "padding"
    line10 = "padding"
    line11 = "padding"
    line12 = "padding"
    line13 = "padding"
    line14 = "padding"
    line15 = "padding"
    line16 = "padding"
    line17 = "padding"
    line18 = "padding"
    line19 = "padding"
    line20 = "padding"
    line21 = "padding"
    line22 = "padding"
    line23 = "padding"
    line24 = "padding"
    line25 = "padding"
    line26 = "padding"
    line27 = "padding"
    line28 = "padding"
    line29 = "padding"
    line30 = "padding"
    return 0

class VeryComplexClass:  # C005: Too many methods
    def method1(self): pass
    def method2(self): pass
    def method3(self): pass
    def method4(self): pass
    def method5(self): pass
    def method6(self): pass
    def method7(self): pass
    def method8(self): pass
    def method9(self): pass
    def method10(self): pass
    def method11(self): pass
    def method12(self): pass
    def method13(self): pass
    def method14(self): pass
    def method15(self): pass
    def method16(self): pass
    def method17(self): pass
    def method18(self): pass
    def method19(self): pass
    def method20(self): pass
    def method21(self): pass
    def method22(self): pass
    def method23(self): pass
    def method24(self): pass
    def method25(self): pass
'''

        # Parse the code into AST
        ast_tree = ast.parse(complex_code)

        # Test all complexity rules
        rules = [
            CyclomaticComplexityRule(),
            FunctionLengthRule(),
            NestedComplexityRule(),
            ParameterCountRule(),
            ClassComplexityRule()
        ]

        all_issues = []
        rule_ids_found = set()

        for rule in rules:
            issues = await rule.analyze(mock_target, complex_code, ast_tree, mock_context)
            all_issues.extend(issues)
            for issue in issues:
                rule_ids_found.add(issue.rule_id)

        # Should detect issues from multiple complexity rules
        assert len(all_issues) >= 4  # Expect at least 4 different complexity issues
        assert len(rule_ids_found) >= 3  # At least 3 different rule types should trigger

        # Verify specific rule IDs are present
        expected_rules = {"C001", "C002", "C003", "C004", "C005"}
        assert len(rule_ids_found.intersection(expected_rules)) >= 3


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
