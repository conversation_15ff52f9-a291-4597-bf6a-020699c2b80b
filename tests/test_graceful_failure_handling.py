"""
Tests for Graceful Failure Handling System.

Tests verify that the failure handling system correctly diagnoses, recovers from,
and prevents various types of analysis failures.
"""

import asyncio
import pytest
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from vibe_check.core.error_handling.graceful_failure_handler import (
    GracefulFailureHandler, FailureCategory, FailureSeverity, FailureContext
)
from vibe_check.core.error_handling.failure_integration import (
    VCSFailureIntegration, PluginFailureIntegration, CLIFailureIntegration,
    get_failure_handler, with_graceful_failure_handling
)
from vibe_check.core.error_handling.exceptions import (
    VibeCheckError, ToolError, FileError, ConfigurationError
)


class TestGracefulFailureHandler:
    """Test suite for the graceful failure handler."""

    @pytest.fixture
    def handler(self):
        """Create a fresh failure handler for testing."""
        return GracefulFailureHandler()

    @pytest.fixture
    def mock_vcs_engine(self):
        """Create a mock VCS engine."""
        engine = Mock()
        engine.config = Mock()
        engine.config.use_shared_ast_traversal = True
        return engine

    def test_failure_categorization(self, handler):
        """Test that failures are correctly categorized."""
        # Test tool error
        tool_error = ToolError("ruff", "Tool execution failed")
        category = handler._categorize_failure(tool_error, {})
        assert category == FailureCategory.EXTERNAL_TOOL

        # Test file error
        file_error = FileError("/path/to/file", "Permission denied")
        category = handler._categorize_failure(file_error, {})
        assert category == FailureCategory.FILE_ACCESS

        # Test configuration error
        config_error = ConfigurationError("Invalid configuration")
        category = handler._categorize_failure(config_error, {})
        assert category == FailureCategory.CONFIGURATION

        # Test memory error
        memory_error = Exception("Out of memory")
        category = handler._categorize_failure(memory_error, {})
        assert category == FailureCategory.MEMORY_PRESSURE

        # Test timeout error
        timeout_error = Exception("Operation timed out")
        category = handler._categorize_failure(timeout_error, {})
        assert category == FailureCategory.TIMEOUT

    def test_severity_determination(self, handler):
        """Test that failure severity is correctly determined."""
        # Critical failures
        init_error = Exception("initialization failed")
        severity = handler._determine_severity(init_error, FailureCategory.INITIALIZATION, {})
        assert severity == FailureSeverity.CRITICAL

        # High severity failures
        memory_error = Exception("memory exhausted")
        severity = handler._determine_severity(memory_error, FailureCategory.MEMORY_PRESSURE, {})
        assert severity == FailureSeverity.HIGH

        # Medium severity failures
        tool_error = ToolError("ruff", "execution failed")
        severity = handler._determine_severity(tool_error, FailureCategory.EXTERNAL_TOOL, {})
        assert severity == FailureSeverity.MEDIUM

        # Low severity failures
        file_error = FileError("/path", "not found")
        severity = handler._determine_severity(file_error, FailureCategory.FILE_ACCESS, {})
        assert severity == FailureSeverity.LOW

    @pytest.mark.asyncio
    async def test_failure_diagnosis(self, handler):
        """Test comprehensive failure diagnosis."""
        error = ToolError("mypy", "Type checking failed")
        context = {"file_path": "/test/file.py", "operation": "type_check"}
        
        failure_context = await handler._diagnose_failure(
            error, context, "plugin_analyzer", "type_checking"
        )
        
        assert failure_context.category == FailureCategory.EXTERNAL_TOOL
        assert failure_context.severity == FailureSeverity.MEDIUM
        assert failure_context.component == "plugin_analyzer"
        assert failure_context.operation == "type_checking"
        assert "mypy" in failure_context.error_message
        assert failure_context.metadata["tool_name"] == "mypy"

    @pytest.mark.asyncio
    async def test_recovery_strategy_execution(self, handler):
        """Test that recovery strategies are executed correctly."""
        # Create a failure context for external tool failure
        failure_context = FailureContext(
            timestamp=datetime.now(),
            category=FailureCategory.EXTERNAL_TOOL,
            severity=FailureSeverity.MEDIUM,
            component="plugin_analyzer",
            operation="tool_execution",
            error_message="Tool failed",
            metadata={"tool_name": "ruff"}
        )
        
        # Attempt recovery
        recovery_result = await handler._attempt_recovery(failure_context)
        
        assert recovery_result is not None
        assert recovery_result["recovery_type"] == "tool_skip"
        assert recovery_result["action"] == "skip_tool_ruff"
        assert failure_context.recovery_attempted
        assert failure_context.recovery_successful

    @pytest.mark.asyncio
    async def test_memory_pressure_recovery(self, handler):
        """Test memory pressure recovery."""
        failure_context = FailureContext(
            timestamp=datetime.now(),
            category=FailureCategory.MEMORY_PRESSURE,
            severity=FailureSeverity.HIGH,
            component="vcs_engine",
            operation="analysis",
            error_message="High memory usage",
            metadata={}
        )
        
        recovery_result = await handler._recover_memory_pressure(failure_context)
        
        assert recovery_result["recovery_type"] == "memory_optimization"
        assert recovery_result["action"] == "reduce_batch_size"
        assert recovery_result["new_batch_size"] == 2

    def test_prevention_rules(self, handler):
        """Test failure prevention rules."""
        # Test memory exhaustion prevention
        with patch('psutil.virtual_memory') as mock_memory:
            mock_memory.return_value.percent = 90.0
            result = handler._prevent_memory_exhaustion({})
            assert not result  # Should prevent due to high memory

            mock_memory.return_value.percent = 70.0
            result = handler._prevent_memory_exhaustion({})
            assert result  # Should allow due to normal memory

    def test_failure_statistics(self, handler):
        """Test failure statistics collection."""
        # Add some test failures
        handler.failure_history = [
            FailureContext(
                timestamp=datetime.now(),
                category=FailureCategory.EXTERNAL_TOOL,
                severity=FailureSeverity.MEDIUM,
                component="test",
                operation="test",
                error_message="test",
                recovery_successful=True
            ),
            FailureContext(
                timestamp=datetime.now(),
                category=FailureCategory.FILE_ACCESS,
                severity=FailureSeverity.LOW,
                component="test",
                operation="test",
                error_message="test",
                recovery_successful=False
            )
        ]
        
        stats = handler.get_failure_statistics()
        
        assert stats["total_failures"] == 2
        assert stats["successful_recoveries"] == 1
        assert stats["recovery_rate"] == 0.5
        assert stats["failure_categories"]["external_tool"] == 1
        assert stats["failure_categories"]["file_access"] == 1


class TestVCSFailureIntegration:
    """Test suite for VCS failure integration."""

    @pytest.fixture
    def mock_engine(self):
        """Create a mock VCS engine."""
        engine = Mock()
        engine.config = Mock()
        engine.config.use_shared_ast_traversal = True
        return engine

    @pytest.fixture
    def integration(self, mock_engine):
        """Create VCS failure integration."""
        return VCSFailureIntegration(mock_engine)

    @pytest.mark.asyncio
    async def test_rule_execution_failure_handling(self, integration, mock_engine):
        """Test handling of rule execution failures."""
        error = Exception("Rule execution failed")
        context = {"target": "/test/file.py"}
        
        result = await integration.handle_rule_execution_failure("S001", error, context)
        
        assert result is True
        assert mock_engine.config.use_shared_ast_traversal is False

    @pytest.mark.asyncio
    async def test_framework_detection_failure_handling(self, integration):
        """Test handling of framework detection failures."""
        error = Exception("Framework detection failed")
        
        result = await integration.handle_framework_detection_failure(error, "/test/project")
        
        assert result is True

    @pytest.mark.asyncio
    async def test_memory_pressure_handling(self, integration):
        """Test memory pressure handling."""
        result = await integration.handle_memory_pressure(2048.0)
        
        assert "batch_size" in result
        assert result["cleanup_performed"] is True
        assert result["continue_analysis"] is True


class TestPluginFailureIntegration:
    """Test suite for Plugin failure integration."""

    @pytest.fixture
    def mock_analyzer(self):
        """Create a mock plugin analyzer."""
        return Mock()

    @pytest.fixture
    def integration(self, mock_analyzer):
        """Create Plugin failure integration."""
        return PluginFailureIntegration(mock_analyzer)

    @pytest.mark.asyncio
    async def test_external_tool_failure_handling(self, integration):
        """Test handling of external tool failures."""
        error = ToolError("ruff", "Execution failed")
        context = {"project_path": "/test"}
        
        result = await integration.handle_external_tool_failure("ruff", error, context)
        
        assert result is True

    @pytest.mark.asyncio
    async def test_import_analysis_failure_handling(self, integration):
        """Test handling of import analysis failures."""
        error = Exception("Import analysis failed")
        
        result = await integration.handle_import_analysis_failure(error, "/test/project")
        
        assert result is True


class TestCLIFailureIntegration:
    """Test suite for CLI failure integration."""

    @pytest.fixture
    def integration(self):
        """Create CLI failure integration."""
        return CLIFailureIntegration()

    @pytest.mark.asyncio
    async def test_analysis_mode_failure_handling(self, integration):
        """Test handling of analysis mode failures."""
        error = Exception("VCS mode failed")
        context = {"project_path": "/test"}
        
        result = await integration.handle_analysis_mode_failure("vcs", error, context)
        
        assert result == "plugin"

    @pytest.mark.asyncio
    async def test_configuration_failure_handling(self, integration):
        """Test handling of configuration failures."""
        error = ConfigurationError("Invalid config")
        
        result = await integration.handle_configuration_failure(error, "/test/config.yaml")
        
        assert result is True

    def test_user_friendly_error_messages(self, integration):
        """Test generation of user-friendly error messages."""
        error = Exception("Permission denied")
        context = {"operation": "file_access"}
        
        message = integration.get_user_friendly_error_message(error, context)
        
        assert "❌ Analysis failed" in message
        assert "💡 Suggestions:" in message
        assert "Check file permissions" in message


class TestFailureHandlingDecorator:
    """Test suite for the failure handling decorator."""

    @pytest.mark.asyncio
    async def test_async_function_decoration(self):
        """Test decorator with async functions."""
        @with_graceful_failure_handling("test_component", "test_operation")
        async def failing_async_function():
            raise Exception("Test failure")
        
        # Should handle the exception gracefully
        with pytest.raises(Exception):
            await failing_async_function()

    def test_sync_function_decoration(self):
        """Test decorator with sync functions."""
        @with_graceful_failure_handling("test_component", "test_operation")
        def failing_sync_function():
            raise Exception("Test failure")
        
        # Should handle the exception gracefully
        with pytest.raises(Exception):
            failing_sync_function()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
