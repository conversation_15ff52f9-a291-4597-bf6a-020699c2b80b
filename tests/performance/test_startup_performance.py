"""
Performance tests for Vibe Check startup time.
"""

import time
import subprocess
import sys
from pathlib import Path


def test_cli_startup_time():
    """Test that CLI startup time is under 3 seconds."""
    start_time = time.time()
    
    # Test CLI help command (minimal operation)
    result = subprocess.run([
        sys.executable, "-c", 
        "from vibe_check.cli.main import cli; cli(['--help'])"
    ], capture_output=True, text=True)
    
    end_time = time.time()
    startup_time = end_time - start_time
    
    print(f"CLI startup time: {startup_time:.2f} seconds")
    
    # Verify it completed successfully
    assert result.returncode == 0, f"CLI failed with: {result.stderr}"
    
    # Verify startup time is under 3 seconds
    assert startup_time < 3.0, f"CLI startup took {startup_time:.2f}s, should be < 3.0s"


def test_import_time():
    """Test that importing core modules is fast."""
    start_time = time.time()
    
    # Test importing core modules
    result = subprocess.run([
        sys.executable, "-c", 
        "from vibe_check.core.simple_analyzer import simple_analyze_project"
    ], capture_output=True, text=True)
    
    end_time = time.time()
    import_time = end_time - start_time
    
    print(f"Core import time: {import_time:.2f} seconds")
    
    # Verify it completed successfully
    assert result.returncode == 0, f"Import failed with: {result.stderr}"
    
    # Verify import time is reasonable (under 2 seconds)
    assert import_time < 2.0, f"Core import took {import_time:.2f}s, should be < 2.0s"


def test_simple_analysis_performance():
    """Test performance of analyzing a small project."""
    import tempfile
    import shutil
    
    # Create a small test project
    test_dir = Path(tempfile.mkdtemp())
    output_dir = Path(tempfile.mkdtemp())
    
    try:
        # Create a few Python files
        (test_dir / "main.py").write_text("""
def main():
    print("Hello, world!")

if __name__ == "__main__":
    main()
""")
        
        (test_dir / "utils.py").write_text("""
def helper_function():
    return "helper"

class UtilityClass:
    def __init__(self):
        self.value = 42
""")
        
        start_time = time.time()
        
        # Run analysis
        result = subprocess.run([
            sys.executable, "-c", f"""
import sys
sys.path.insert(0, '{Path.cwd()}')
from vibe_check.cli.main import cli
cli(['analyze', '{test_dir}', '--output', '{output_dir}', '--preset', 'minimal'])
"""
        ], capture_output=True, text=True)
        
        end_time = time.time()
        analysis_time = end_time - start_time
        
        print(f"Small project analysis time: {analysis_time:.2f} seconds")
        
        # Verify it completed successfully
        assert result.returncode == 0, f"Analysis failed with: {result.stderr}"
        
        # Verify analysis time is reasonable (under 10 seconds for small project)
        assert analysis_time < 10.0, f"Analysis took {analysis_time:.2f}s, should be < 10.0s"
        
        # Verify output files were created
        assert (output_dir / "report.html").exists(), "HTML report not created"
        assert (output_dir / "report.json").exists(), "JSON report not created"
        
    finally:
        shutil.rmtree(test_dir, ignore_errors=True)
        shutil.rmtree(output_dir, ignore_errors=True)


if __name__ == "__main__":
    print("Running performance benchmarks...")
    
    print("\n1. Testing CLI startup time...")
    test_cli_startup_time()
    
    print("\n2. Testing import time...")
    test_import_time()
    
    print("\n3. Testing analysis performance...")
    test_simple_analysis_performance()
    
    print("\n✅ All performance tests passed!")
