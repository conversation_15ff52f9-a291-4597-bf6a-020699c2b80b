"""
Performance validation tests for Sprint 7.3: Performance & Integration.

Tests verify that VCS mode meets performance targets and integration works correctly.
"""

import asyncio
import time
import pytest
from pathlib import Path
from typing import Dict, Any

from vibe_check.core.vcs.engine import VibeCheckEngine
from vibe_check.core.vcs.models import EngineMode, AnalysisContext
from vibe_check.core.persistence.storage import ResultStorage
from vibe_check.core.persistence.models import AnalysisMode


class TestSprint73Performance:
    """Test suite for Sprint 7.3 performance optimizations."""

    @pytest.fixture
    def test_project_path(self) -> Path:
        """Get test project path."""
        return Path("vibe_check/core/vcs/rules")

    @pytest.fixture
    def vcs_engine(self) -> VibeCheckEngine:
        """Create VCS engine for testing."""
        return VibeCheckEngine(mode=EngineMode.STANDALONE)

    @pytest.fixture
    def storage(self) -> ResultStorage:
        """Create storage for testing."""
        return ResultStorage()

    @pytest.mark.asyncio
    async def test_vcs_performance_targets(self, vcs_engine, test_project_path):
        """Test that VCS mode meets performance targets."""
        start_time = time.time()
        
        # Run VCS analysis
        results = await vcs_engine.analyze_project(test_project_path)
        
        total_time = time.time() - start_time
        
        # Performance assertions
        assert total_time < 5.0, f"VCS analysis took {total_time:.2f}s, should be <5s"
        assert total_time < 3.0, f"VCS analysis took {total_time:.2f}s, should be <3s (optimized)"
        
        # Results validation
        assert results is not None
        assert len(results) > 0, "Should detect some issues"
        
        print(f"✅ VCS Performance: {total_time:.2f}s (target: <5s, optimized: <3s)")

    @pytest.mark.asyncio
    async def test_initialization_optimization(self, vcs_engine, test_project_path):
        """Test that VCS initialization is optimized."""
        # Measure initialization time
        init_start = time.time()
        await vcs_engine.initialize()
        init_time = time.time() - init_start
        
        # Measure analysis time
        analysis_start = time.time()
        results = await vcs_engine.analyze_project(test_project_path)
        analysis_time = time.time() - analysis_start
        
        # Assertions
        assert init_time < 2.5, f"Initialization took {init_time:.2f}s, should be <2.5s"
        assert analysis_time < 1.0, f"Analysis took {analysis_time:.2f}s, should be <1s"
        
        print(f"✅ Initialization: {init_time:.2f}s, Analysis: {analysis_time:.2f}s")

    @pytest.mark.asyncio
    async def test_memory_efficiency(self, vcs_engine, test_project_path):
        """Test memory efficiency during analysis."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        
        # Measure memory before analysis
        memory_before = process.memory_info().rss / 1024 / 1024  # MB
        
        # Run analysis
        results = await vcs_engine.analyze_project(test_project_path)
        
        # Measure memory after analysis
        memory_after = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = memory_after - memory_before
        
        # Memory assertions
        assert memory_increase < 100, f"Memory increased by {memory_increase:.1f}MB, should be <100MB"
        assert memory_after < 500, f"Total memory {memory_after:.1f}MB, should be <500MB"
        
        print(f"✅ Memory: {memory_before:.1f}MB → {memory_after:.1f}MB (+{memory_increase:.1f}MB)")

    @pytest.mark.asyncio
    async def test_persistence_integration(self, vcs_engine, storage, test_project_path):
        """Test seamless persistence system integration."""
        # Run analysis
        results = await vcs_engine.analyze_project(test_project_path)
        
        # Create analysis run for persistence
        from vibe_check.core.persistence.result_adapter import ResultAdapter
        from datetime import datetime
        
        analysis_run = ResultAdapter.convert_analysis_results(
            results, str(test_project_path), AnalysisMode.VCS, "test", datetime.now()
        )
        
        # Save to database
        run_id = storage.save_analysis_run(analysis_run)
        
        # Verify persistence
        assert run_id is not None
        assert run_id > 0
        
        # Retrieve and verify
        retrieved_run = storage.get_analysis_run(run_id)
        assert retrieved_run is not None
        assert retrieved_run.analysis_mode == AnalysisMode.VCS
        assert len(retrieved_run.file_results) > 0
        
        print(f"✅ Persistence: Saved run {run_id} with {len(retrieved_run.file_results)} files")

    @pytest.mark.asyncio
    async def test_framework_detection_optimization(self, vcs_engine, test_project_path):
        """Test optimized framework detection performance."""
        from vibe_check.core.vcs.rules.framework_rules.framework_detector import FrameworkDetector
        
        detector = FrameworkDetector()
        
        # Measure framework detection time
        start_time = time.time()
        frameworks = detector.detect_project_framework(test_project_path)
        detection_time = time.time() - start_time
        
        # Performance assertion
        assert detection_time < 0.5, f"Framework detection took {detection_time:.2f}s, should be <0.5s"
        
        # Functionality assertion
        assert isinstance(frameworks, dict)
        assert len(frameworks) > 0
        
        print(f"✅ Framework Detection: {detection_time:.3f}s, detected: {list(frameworks.keys())}")

    def test_performance_regression_prevention(self, storage):
        """Test that performance hasn't regressed compared to baseline."""
        # Get recent VCS runs
        recent_runs = storage.get_recent_runs(limit=5)
        vcs_runs = [run for run in recent_runs if run.analysis_mode == AnalysisMode.VCS]
        
        if len(vcs_runs) >= 2:
            latest_run = vcs_runs[0]
            previous_run = vcs_runs[1]
            
            # Compare performance
            latest_duration = latest_run.analysis_duration
            previous_duration = previous_run.analysis_duration
            
            # Allow 20% performance variation
            performance_ratio = latest_duration / previous_duration
            assert performance_ratio < 1.2, f"Performance regressed: {performance_ratio:.2f}x slower"
            
            print(f"✅ Performance Regression: {performance_ratio:.2f}x (latest: {latest_duration:.2f}s)")

    @pytest.mark.asyncio
    async def test_concurrent_analysis_performance(self, test_project_path):
        """Test performance under concurrent analysis load."""
        engines = [VibeCheckEngine(mode=EngineMode.STANDALONE) for _ in range(3)]
        
        # Initialize all engines
        for engine in engines:
            await engine.initialize()
        
        # Run concurrent analyses
        start_time = time.time()
        tasks = [engine.analyze_project(test_project_path) for engine in engines]
        results = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        # Performance assertions
        assert total_time < 10.0, f"Concurrent analysis took {total_time:.2f}s, should be <10s"
        assert all(len(result) > 0 for result in results), "All analyses should produce results"
        
        print(f"✅ Concurrent Performance: {total_time:.2f}s for 3 parallel analyses")

    def test_cli_integration_performance(self, test_project_path):
        """Test CLI integration performance."""
        import subprocess
        import time
        
        # Test VCS mode CLI performance
        start_time = time.time()
        result = subprocess.run([
            "vibe-check", "analyze", str(test_project_path), 
            "--vcs-mode", "--profile", "minimal", "--no-save"
        ], capture_output=True, text=True)
        cli_time = time.time() - start_time
        
        # Assertions
        assert result.returncode == 0, f"CLI failed: {result.stderr}"
        assert cli_time < 5.0, f"CLI took {cli_time:.2f}s, should be <5s"
        assert "Analysis completed" in result.stdout
        
        print(f"✅ CLI Integration: {cli_time:.2f}s")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
