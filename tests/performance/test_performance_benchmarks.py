"""
Performance regression tests for core analysis functions.
"""

import pytest
import tempfile
import os
from pathlib import Path
from typing import Any, Dict
from unittest.mock import patch, MagicMock

from vibe_check.core.analysis.file_analyzer import <PERSON>Analyzer
from vibe_check.core.analysis.project_analyzer import <PERSON>Analyzer
from vibe_check.core.analysis.metrics_aggregator import MetricsAggregator
from vibe_check.core.models.project_metrics import ProjectMetrics
from vibe_check.core.models.file_metrics import FileMetrics


class TestPerformanceBenchmarks:
    """Performance benchmarks for core analysis functions."""

    def test_file_analyzer_performance(self, benchmark: Any) -> None:
        """Benchmark file analysis performance."""
        # Create a moderately complex Python file
        test_code = """
import os
import sys
import json
from typing import List, Dict, Optional, Union
from pathlib import Path

class DataProcessor:
    '''A class for processing data files.'''
    
    def __init__(self, config: Dict[str, Any]) -> None:
        '''Initialize the data processor.'''
        self.config = config
        self.processed_files: List[str] = []
        self.errors: List[str] = []
    
    def process_file(self, file_path: Path) -> Optional[Dict[str, Any]]:
        '''Process a single file and return results.'''
        try:
            if not file_path.exists():
                self.errors.append(f"File not found: {file_path}")
                return None
            
            with open(file_path, 'r') as f:
                content = f.read()
            
            # Simulate complex processing
            result = {
                'file_path': str(file_path),
                'size': len(content),
                'lines': len(content.split('\\n')),
                'words': len(content.split()),
                'processed': True
            }
            
            self.processed_files.append(str(file_path))
            return result
            
        except Exception as e:
            self.errors.append(f"Error processing {file_path}: {e}")
            return None
    
    def process_directory(self, directory: Path) -> List[Dict[str, Any]]:
        '''Process all files in a directory.'''
        results = []
        
        for file_path in directory.rglob("*.py"):
            result = self.process_file(file_path)
            if result:
                results.append(result)
        
        return results
    
    def get_statistics(self) -> Dict[str, int]:
        '''Get processing statistics.'''
        return {
            'processed_files': len(self.processed_files),
            'errors': len(self.errors),
            'success_rate': len(self.processed_files) / (len(self.processed_files) + len(self.errors)) if (len(self.processed_files) + len(self.errors)) > 0 else 0
        }

def main():
    '''Main function for data processing.'''
    config = {
        'input_dir': '/path/to/input',
        'output_dir': '/path/to/output',
        'batch_size': 100,
        'parallel': True
    }
    
    processor = DataProcessor(config)
    
    input_path = Path(config['input_dir'])
    if input_path.exists():
        results = processor.process_directory(input_path)
        stats = processor.get_statistics()
        
        print(f"Processed {stats['processed_files']} files")
        print(f"Errors: {stats['errors']}")
        print(f"Success rate: {stats['success_rate']:.2%}")
    else:
        print(f"Input directory not found: {input_path}")

if __name__ == "__main__":
    main()
"""
        
        analyzer = FileAnalyzer(project_path=".", enable_semantic_analysis=False)

        # Benchmark the file analysis
        result = benchmark(analyzer.analyze_file, "test_performance.py", test_code)
        
        # Verify the result is valid
        assert isinstance(result, FileMetrics)
        assert result.line_count > 0
        assert result.complexity >= 0

    def test_metrics_aggregator_performance(self, benchmark: Any) -> None:
        """Benchmark metrics aggregation performance."""
        # Create a project with multiple files
        project_metrics = ProjectMetrics(project_path="test_project")
        
        # Add 50 files with various metrics
        for i in range(50):
            file_metrics = FileMetrics(
                path=f"src/module_{i}.py",
                line_count=100 + (i * 10),
                complexity=5 + (i % 10),
                type_coverage=80 + (i % 20),
                docstring_coverage=60 + (i % 30)
            )
            
            # Add some issues
            for j in range(i % 5):
                file_metrics.add_issue(
                    f"E{100 + j}",
                    f"Test issue {j} in file {i}",
                    10 + j,
                    "error" if j % 2 == 0 else "warning"
                )
            
            project_metrics.files[f"src/module_{i}.py"] = file_metrics
        
        aggregator = MetricsAggregator()
        
        # Benchmark the aggregation
        result = benchmark(aggregator.aggregate_metrics, project_metrics)
        
        # Verify the aggregation worked
        assert len(project_metrics.directories) > 0
        assert "src" in project_metrics.directories

    @pytest.mark.asyncio
    async def test_project_analyzer_performance(self, benchmark: Any) -> None:
        """Benchmark project analysis performance."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a small project structure
            src_dir = Path(temp_dir) / "src"
            src_dir.mkdir()
            
            # Create 10 Python files
            for i in range(10):
                file_path = src_dir / f"module_{i}.py"
                file_path.write_text(f"""
def function_{i}():
    '''Function {i} documentation.'''
    result = {i} * 2
    return result

class Class{i}:
    '''Class {i} documentation.'''
    
    def method_{i}(self):
        '''Method {i} documentation.'''
        return {i}
""")
            
            # Mock external dependencies to focus on core performance
            with patch('vibe_check.core.analysis.project_analyzer.PerformanceOptimizer'):
                analyzer = ProjectAnalyzer(temp_dir, enable_semantic_analysis=False)
                
                # Benchmark the project analysis
                async def analyze_project():
                    return await analyzer.analyze_project()
                
                result = benchmark(lambda: analyze_project())
                
                # Note: benchmark doesn't work directly with async functions,
                # so we'll test the sync parts separately

    def test_file_analysis_memory_usage(self, benchmark: Any) -> None:
        """Test memory usage during file analysis."""
        # Create a large file content
        large_code = """
import os
import sys
""" + "\n".join([f"def function_{i}(): pass" for i in range(1000)])
        
        analyzer = FileAnalyzer(project_path=".", enable_semantic_analysis=False)

        # Benchmark memory usage
        result = benchmark(analyzer.analyze_file, "large_file.py", large_code)
        
        assert isinstance(result, FileMetrics)
        assert result.line_count > 1000

    def test_complexity_calculation_performance(self, benchmark: Any) -> None:
        """Benchmark complexity calculation performance."""
        # Create code with nested complexity
        complex_code = """
def complex_function(data):
    '''A function with high complexity.'''
    result = []
    
    for item in data:
        if item > 0:
            if item % 2 == 0:
                for i in range(item):
                    if i % 3 == 0:
                        try:
                            result.append(i * 2)
                        except Exception:
                            continue
                    else:
                        result.append(i)
            else:
                for j in range(item // 2):
                    if j % 5 == 0:
                        result.append(j * 3)
        elif item < 0:
            for k in range(abs(item)):
                if k % 7 == 0:
                    result.append(k * -1)
        else:
            result.append(0)
    
    return result
"""
        
        analyzer = FileAnalyzer(project_path=".", enable_semantic_analysis=False)

        # Benchmark complexity calculation
        result = benchmark(analyzer.analyze_file, "complex_file.py", complex_code)
        
        assert isinstance(result, FileMetrics)
        assert result.complexity > 10  # Should detect high complexity

    def test_large_project_handling(self, benchmark: Any) -> None:
        """Test handling of projects with many files."""
        project_metrics = ProjectMetrics(project_path="large_project")
        
        # Simulate a large project with 200 files
        for i in range(200):
            directory = f"module_{i // 20}"
            file_metrics = FileMetrics(
                path=f"{directory}/file_{i}.py",
                line_count=50 + (i % 100),
                complexity=1 + (i % 15),
                type_coverage=70 + (i % 30),
                docstring_coverage=50 + (i % 40)
            )
            
            # Add random issues
            for j in range(i % 3):
                file_metrics.add_issue(
                    f"W{200 + j}",
                    f"Warning {j} in file {i}",
                    5 + j,
                    "warning"
                )
            
            project_metrics.files[f"{directory}/file_{i}.py"] = file_metrics
        
        aggregator = MetricsAggregator()
        
        # Benchmark large project aggregation
        result = benchmark(aggregator.aggregate_metrics, project_metrics)
        
        # Verify it handled the large project
        assert len(project_metrics.directories) >= 10  # Should have multiple directories

    def test_issue_processing_performance(self, benchmark: Any) -> None:
        """Benchmark issue processing performance."""
        file_metrics = FileMetrics(path="test.py", line_count=100)
        
        # Add many issues
        def add_many_issues():
            for i in range(100):
                file_metrics.add_issue(
                    f"E{i:03d}",
                    f"Test issue number {i} with detailed message",
                    i % 100 + 1,
                    "error" if i % 3 == 0 else "warning" if i % 3 == 1 else "info"
                )
        
        # Benchmark issue addition
        result = benchmark(add_many_issues)
        
        # Verify issues were added
        assert len(file_metrics.issues) == 100

    def test_type_analysis_performance(self, benchmark: Any) -> None:
        """Benchmark type analysis performance."""
        typed_code = """
from typing import List, Dict, Optional, Union, Tuple, Set
import asyncio

class TypedClass:
    '''A class with comprehensive type annotations.'''
    
    def __init__(self, data: Dict[str, Union[int, str]]) -> None:
        self.data: Dict[str, Union[int, str]] = data
        self.cache: Dict[str, Optional[List[int]]] = {}
        self.results: List[Tuple[str, int]] = []
    
    async def process_data(self, items: List[Dict[str, Any]]) -> Optional[Set[str]]:
        '''Process data asynchronously with type annotations.'''
        processed: Set[str] = set()
        
        for item in items:
            if isinstance(item, dict):
                key: str = item.get('key', '')
                value: Union[int, str] = item.get('value', 0)
                
                if key and value:
                    processed.add(key)
                    self.results.append((key, hash(value)))
        
        return processed if processed else None
    
    def get_statistics(self) -> Dict[str, Union[int, float]]:
        '''Get processing statistics with type annotations.'''
        total_items: int = len(self.results)
        unique_keys: int = len(set(key for key, _ in self.results))
        
        return {
            'total_items': total_items,
            'unique_keys': unique_keys,
            'uniqueness_ratio': unique_keys / total_items if total_items > 0 else 0.0
        }
"""
        
        analyzer = FileAnalyzer(project_path=".", enable_semantic_analysis=False)

        # Benchmark type analysis
        result = benchmark(analyzer.analyze_file, "typed_file.py", typed_code)
        
        assert isinstance(result, FileMetrics)
        assert result.type_coverage >= 0
