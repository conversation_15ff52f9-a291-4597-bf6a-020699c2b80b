"""
Test Suite for Typed Rule Configuration System
==============================================

Comprehensive tests for the typed rule configuration system including:
- Configuration creation and validation
- Type safety and error handling
- Registry functionality
- Integration with VCS rules
"""

import pytest
from typing import Dict, Any
from pathlib import Path

from vibe_check.core.vcs.config.rule_config import (
    RuleConfig,
    ConfigValidationError,
    ConfigField,
    ConfigFieldType,
    LineLengthConfig,
    NamingConventionConfig,
    CyclomaticComplexityConfig,
    FunctionLengthConfig,
    StyleRuleConfig,
    ComplexityRuleConfig,
    RuleConfigRegistry,
    get_config_registry,
    create_rule_config,
    register_rule_config
)


class TestConfigField:
    """Test ConfigField validation and type conversion."""
    
    def test_integer_field_validation(self):
        """Test integer field validation with boundaries."""
        field = ConfigField(
            name='test_int',
            field_type=ConfigFieldType.INTEGER,
            default=100,
            description='Test integer field',
            min_value=50,
            max_value=200
        )
        
        # Valid values
        assert field.validate(100, 'TEST') == 100
        assert field.validate('150', 'TEST') == 150  # String conversion
        assert field.validate(50, 'TEST') == 50      # Minimum boundary
        assert field.validate(200, 'TEST') == 200    # Maximum boundary
        
        # Invalid values
        with pytest.raises(ConfigValidationError) as exc_info:
            field.validate(25, 'TEST')
        assert 'below minimum' in str(exc_info.value)
        
        with pytest.raises(ConfigValidationError) as exc_info:
            field.validate(300, 'TEST')
        assert 'above maximum' in str(exc_info.value)
        
        with pytest.raises(ConfigValidationError) as exc_info:
            field.validate('invalid', 'TEST')
        assert 'Type conversion error' in str(exc_info.value)
    
    def test_boolean_field_validation(self):
        """Test boolean field validation and conversion."""
        field = ConfigField(
            name='test_bool',
            field_type=ConfigFieldType.BOOLEAN,
            default=True,
            description='Test boolean field'
        )
        
        # Valid boolean values
        assert field.validate(True, 'TEST') is True
        assert field.validate(False, 'TEST') is False
        assert field.validate('true', 'TEST') is True
        assert field.validate('false', 'TEST') is False
        assert field.validate('1', 'TEST') is True
        assert field.validate('0', 'TEST') is False
        assert field.validate(1, 'TEST') is True
        assert field.validate(0, 'TEST') is False
    
    def test_string_field_validation(self):
        """Test string field validation with patterns."""
        field = ConfigField(
            name='test_string',
            field_type=ConfigFieldType.STRING,
            default='default',
            description='Test string field',
            validation_pattern=r'^[a-z_][a-z0-9_]*$'  # Python identifier pattern
        )
        
        # Valid strings
        assert field.validate('valid_name', 'TEST') == 'valid_name'
        assert field.validate('_private', 'TEST') == '_private'
        assert field.validate('name123', 'TEST') == 'name123'
        
        # Invalid strings
        with pytest.raises(ConfigValidationError) as exc_info:
            field.validate('123invalid', 'TEST')
        assert 'does not match pattern' in str(exc_info.value)
        
        with pytest.raises(ConfigValidationError) as exc_info:
            field.validate('Invalid-Name', 'TEST')
        assert 'does not match pattern' in str(exc_info.value)


class TestLineLengthConfig:
    """Test LineLengthConfig specific functionality."""
    
    def test_valid_configuration(self):
        """Test creation with valid configuration."""
        config_data = {
            'max_line_length': 120,
            'ignore_urls': True,
            'ignore_comments': False,
            'ignore_imports': True
        }
        
        config = LineLengthConfig('S001', config_data)
        
        assert config.rule_id == 'S001'
        assert config.max_line_length == 120
        assert config.ignore_urls is True
        assert config.ignore_comments is False
        assert config.ignore_imports is True
        assert config.is_valid()
        assert len(config.get_validation_errors()) == 0
    
    def test_invalid_configuration(self):
        """Test creation with invalid configuration."""
        config_data = {
            'max_line_length': 'invalid',  # Should be integer
            'ignore_urls': 'maybe',        # Should be boolean
            'unknown_field': 'ignored'     # Should be ignored
        }
        
        config = LineLengthConfig('S001', config_data)
        
        assert config.rule_id == 'S001'
        assert not config.is_valid()
        
        errors = config.get_validation_errors()
        assert len(errors) >= 1  # At least max_line_length error

        error_fields = [error.field for error in errors]
        assert 'max_line_length' in error_fields
        # Note: ignore_urls might convert 'maybe' to False, so may not error
    
    def test_default_values(self):
        """Test default values are applied correctly."""
        config = LineLengthConfig('S001', {})
        
        assert config.max_line_length == 100  # From AnalysisThresholds
        assert config.ignore_urls is True
        assert config.ignore_comments is False
        assert config.ignore_imports is False
        assert config.enforce_pep8 is True  # From base class
    
    def test_configuration_schema(self):
        """Test configuration schema generation."""
        config = LineLengthConfig('S001', {})
        schema = config.get_config_schema()
        
        assert 'max_line_length' in schema
        assert 'ignore_urls' in schema
        assert 'ignore_comments' in schema
        assert 'ignore_imports' in schema
        assert 'enforce_pep8' in schema
        
        # Check field details
        max_length_field = schema['max_line_length']
        assert max_length_field['type'] == 'integer'
        assert max_length_field['default'] == 100
        assert max_length_field['min_value'] == 50
        assert max_length_field['max_value'] == 200


class TestRuleConfigRegistry:
    """Test RuleConfigRegistry functionality."""
    
    def test_registry_initialization(self):
        """Test registry initializes with built-in configurations."""
        registry = RuleConfigRegistry()
        
        # Check built-in configurations are registered
        assert registry.get_config_class('S001') == LineLengthConfig
        assert registry.get_config_class('S004') == NamingConventionConfig
        assert registry.get_config_class('C001') == CyclomaticComplexityConfig
        assert registry.get_config_class('C002') == FunctionLengthConfig
    
    def test_config_creation(self):
        """Test configuration creation through registry."""
        registry = RuleConfigRegistry()
        
        # Test specific rule configuration
        config = registry.create_config('S001', {'max_line_length': 88})
        assert isinstance(config, LineLengthConfig)
        assert config.max_line_length == 88
        
        # Test fallback to base configuration
        config = registry.create_config('S999', {'max_line_length': 120})
        assert isinstance(config, StyleRuleConfig)
        assert config.max_line_length == 120
    
    def test_custom_registration(self):
        """Test registering custom configuration classes."""
        registry = RuleConfigRegistry()
        
        class CustomConfig(StyleRuleConfig):
            def get_config_fields(self):
                fields = super().get_config_fields()
                fields['custom_field'] = ConfigField(
                    name='custom_field',
                    field_type=ConfigFieldType.STRING,
                    default='custom_default',
                    description='Custom field'
                )
                return fields
        
        registry.register('CUSTOM001', CustomConfig)
        
        config = registry.create_config('CUSTOM001', {'custom_field': 'test_value'})
        assert isinstance(config, CustomConfig)
        assert hasattr(config, 'custom_field')
    
    def test_validation_all_configs(self):
        """Test validation of multiple configurations."""
        registry = RuleConfigRegistry()
        
        rule_configs = {
            'S001': {'max_line_length': 120, 'ignore_urls': True},
            'S004': {'class_name_pattern': r'^[A-Z][a-zA-Z0-9]*$'},
            'C001': {'complexity_threshold': 15},
            'INVALID': {'max_line_length': 'invalid_value'}
        }
        
        errors = registry.validate_all_configs(rule_configs)
        
        # Should have errors only for INVALID rule
        assert 'INVALID' in errors
        assert len(errors['INVALID']) > 0
        
        # Other rules should be valid
        assert 'S001' not in errors
        assert 'S004' not in errors
        assert 'C001' not in errors


class TestGlobalFunctions:
    """Test global convenience functions."""
    
    def test_get_config_registry(self):
        """Test global registry access."""
        registry = get_config_registry()
        assert isinstance(registry, RuleConfigRegistry)
        
        # Should return same instance (singleton pattern)
        registry2 = get_config_registry()
        assert registry is registry2
    
    def test_create_rule_config(self):
        """Test global config creation function."""
        config = create_rule_config('S001', {'max_line_length': 88})
        assert isinstance(config, LineLengthConfig)
        assert config.max_line_length == 88
    
    def test_register_rule_config(self):
        """Test global registration function."""
        class TestConfig(StyleRuleConfig):
            pass
        
        register_rule_config('TEST001', TestConfig)
        
        # Should be able to create config with new class
        config = create_rule_config('TEST001', {})
        assert isinstance(config, TestConfig)


class TestIntegrationWithVCSRules:
    """Test integration with actual VCS rules."""
    
    def test_line_length_rule_integration(self):
        """Test LineLengthRule uses typed configuration."""
        from vibe_check.core.vcs.rules.style_rules import LineLengthRule
        
        rule = LineLengthRule()
        
        # Test typed configuration access
        typed_config = rule.get_typed_config()
        assert isinstance(typed_config, LineLengthConfig)
        assert typed_config.rule_id == 'S001'
        
        # Test configuration update
        rule.update_config({'max_line_length': 88, 'ignore_urls': False})
        updated_config = rule.get_typed_config()
        assert updated_config.max_line_length == 88
        assert updated_config.ignore_urls is False


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
