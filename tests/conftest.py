"""
Pytest configuration and fixtures.
"""

import pytest
import os
import sys
import tempfile
import shutil
from pathlib import Path

# Add the project root directory to the Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


@pytest.fixture
def sample_python_file():
    """Create a temporary Python file for testing."""
    # Create a temporary file
    fd, path = tempfile.mkstemp(suffix=".py")
    os.close(fd)

    # Write content to the file
    with open(path, "w") as f:
        f.write("""
def hello_world():
    print("Hello, world!")

if __name__ == "__main__":
    hello_world()
""")

    yield path

    # Clean up the temporary file
    os.unlink(path)


@pytest.fixture
def sample_project_dir():
    """Create a temporary project directory for testing."""
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()

    try:
        # Create a simple Python file
        with open(os.path.join(temp_dir, "main.py"), "w") as f:
            f.write("""
def hello_world():
    print("Hello, world!")

if __name__ == "__main__":
    hello_world()
""")

        # Create a directory with a Python file
        os.makedirs(os.path.join(temp_dir, "utils"))
        with open(os.path.join(temp_dir, "utils", "helpers.py"), "w") as f:
            f.write("""
def add(a, b):
    return a + b

def subtract(a, b):
    return a - b
""")

        yield temp_dir
    finally:
        # Clean up the temporary directory
        shutil.rmtree(temp_dir)


@pytest.fixture
def sample_config():
    """Create a sample configuration for testing."""
    return {
        "file_extensions": [".py"],
        "exclude_patterns": ["**/venv/**", "**/__pycache__/**"],
        "tools": {
            "ruff": {
                "enabled": True,
                "args": ["--select=E,F,W,I"]
            },
            "mypy": {
                "enabled": True,
                "args": ["--ignore-missing-imports"]
            },
            "bandit": {
                "enabled": True,
                "args": ["--recursive"]
            },
            "complexity": {
                "enabled": True,
                "threshold": 10
            }
        },
        "analyze_docs": False,
        "max_workers": 4
    }
