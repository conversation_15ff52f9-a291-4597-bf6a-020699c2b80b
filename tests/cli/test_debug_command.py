"""
End-to-end tests for the debug command.

This module tests the CLI debug command, which provides simplified debugging
without the actor system.
"""

import os
import tempfile
import pytest
from typing import Any, Dict
from unittest.mock import patch, MagicMock

from vibe_check.cli.commands import debug_command


class TestDebugCommand:
    """Test class for the debug command."""

    def setup_method(self) -> None:
        """Set up the test environment."""
        # Create a temporary directory for output
        self.temp_dir = tempfile.mkdtemp()

        # Create a test project directory
        self.test_project = os.path.join(self.temp_dir, "test_project")
        os.makedirs(self.test_project)

        # Create a simple Python file for testing
        with open(os.path.join(self.test_project, "main.py"), "w") as f:
            f.write("print('Hello, World!')\n")

    def teardown_method(self) -> None:
        """Clean up after the test."""
        # Clean up temporary directory
        import shutil
        shutil.rmtree(self.temp_dir)

    @patch('vibe_check.core.simple_analyzer.simple_analyze_project')
    def test_debug_command(self, mock_analyze) -> None:
        """Test the simplified debug command."""
        # Setup mock for simple_analyze_project
        mock_result = MagicMock()
        mock_result.total_file_count = 1
        mock_result.python_file_count = 1
        mock_analyze.return_value = mock_result

        # Run the debug command
        results = debug_command(
            project_path=self.test_project,
            output_dir=self.temp_dir,
            verbose=True,
            timeout=10.0
        )

        # Verify that the command completed without error
        assert "error" not in results or results.get("success", False)

        # Verify that analysis was called
        mock_analyze.assert_called_once()
