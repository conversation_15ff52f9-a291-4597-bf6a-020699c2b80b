"""
Tests for the main CLI functionality.
"""

import pytest
import tempfile
import os
from pathlib import Path
from unittest.mock import patch, MagicMock
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from vibe_check.cli.main import cli, analyze


class TestMainCLI:
    """Test cases for the main CLI."""

    def test_cli_help(self):
        """Test CLI help command."""
        runner = CliRunner()
        result = runner.invoke(cli, ['--help'])
        
        assert result.exit_code == 0
        assert 'analyze' in result.output
        assert 'Usage:' in result.output

    def test_cli_version(self):
        """Test CLI version command."""
        runner = CliRunner()
        result = runner.invoke(cli, ['--version'])
        
        assert result.exit_code == 0
        # Should contain version information
        assert len(result.output.strip()) > 0

    def test_analyze_command_help(self):
        """Test analyze command help."""
        runner = CliRunner()
        result = runner.invoke(cli, ['analyze', '--help'])
        
        assert result.exit_code == 0
        assert 'PROJECT_PATH' in result.output
        assert '--output' in result.output
        assert '--config' in result.output

    def test_analyze_command_missing_project_path(self):
        """Test analyze command without project path."""
        runner = CliRunner()
        result = runner.invoke(cli, ['analyze'])
        
        # Should fail with missing argument
        assert result.exit_code != 0
        assert 'Missing argument' in result.output or 'Usage:' in result.output

    def test_analyze_command_nonexistent_project(self):
        """Test analyze command with nonexistent project path."""
        runner = CliRunner()
        result = runner.invoke(cli, ['analyze', '/nonexistent/path'])
        
        # Should fail gracefully
        assert result.exit_code != 0

    @patch('vibe_check.cli.main.simple_analyze_project')
    def test_analyze_command_with_mock(self, mock_analyze):
        """Test analyze command with mocked analyzer."""
        # Setup mock
        mock_result = MagicMock()
        mock_result.total_file_count = 5
        mock_result.python_file_count = 3
        mock_analyze.return_value = mock_result
        
        # Create temporary directories
        project_dir = tempfile.mkdtemp()
        output_dir = tempfile.mkdtemp()
        
        try:
            runner = CliRunner()
            result = runner.invoke(cli, [
                'analyze', 
                project_dir,
                '--output', output_dir
            ])
            
            # Should succeed
            assert result.exit_code == 0
            
            # Verify mock was called
            mock_analyze.assert_called_once()
            call_args = mock_analyze.call_args
            assert call_args[1]['project_path'] == Path(project_dir)
            assert call_args[1]['output_dir'] == Path(output_dir)
            
        finally:
            import shutil
            shutil.rmtree(project_dir, ignore_errors=True)
            shutil.rmtree(output_dir, ignore_errors=True)

    def test_analyze_command_with_sample_project(self, sample_project_dir):
        """Test analyze command with sample project."""
        output_dir = tempfile.mkdtemp()
        
        try:
            runner = CliRunner()
            result = runner.invoke(cli, [
                'analyze',
                sample_project_dir,
                '--output', output_dir
            ])
            
            # Should complete successfully
            assert result.exit_code == 0
            
            # Should create output files
            output_path = Path(output_dir)
            assert output_path.exists()
            
        finally:
            import shutil
            shutil.rmtree(output_dir, ignore_errors=True)

    def test_analyze_command_with_config_file(self, sample_project_dir):
        """Test analyze command with custom config file."""
        output_dir = tempfile.mkdtemp()
        config_file = tempfile.mktemp(suffix='.json')
        
        try:
            # Create a simple config file
            import json
            config = {
                "file_extensions": [".py"],
                "tools": {
                    "ruff": {"enabled": True},
                    "mypy": {"enabled": False},
                    "bandit": {"enabled": False},
                    "complexity": {"enabled": True}
                }
            }
            with open(config_file, 'w') as f:
                json.dump(config, f)
            
            runner = CliRunner()
            result = runner.invoke(cli, [
                'analyze',
                sample_project_dir,
                '--output', output_dir,
                '--config', config_file
            ])
            
            # Should complete successfully
            assert result.exit_code == 0
            
        finally:
            import shutil
            shutil.rmtree(output_dir, ignore_errors=True)
            if os.path.exists(config_file):
                os.unlink(config_file)

    def test_analyze_command_with_preset(self, sample_project_dir):
        """Test analyze command with preset."""
        output_dir = tempfile.mkdtemp()
        
        try:
            runner = CliRunner()
            result = runner.invoke(cli, [
                'analyze',
                sample_project_dir,
                '--output', output_dir,
                '--preset', 'basic'
            ])
            
            # Should complete successfully
            assert result.exit_code == 0
            
        finally:
            import shutil
            shutil.rmtree(output_dir, ignore_errors=True)

    def test_analyze_command_with_verbose_flag(self, sample_project_dir):
        """Test analyze command with verbose flag."""
        output_dir = tempfile.mkdtemp()
        
        try:
            runner = CliRunner()
            result = runner.invoke(cli, [
                'analyze',
                sample_project_dir,
                '--output', output_dir,
                '--verbose'
            ])
            
            # Should complete successfully
            assert result.exit_code == 0
            
        finally:
            import shutil
            shutil.rmtree(output_dir, ignore_errors=True)

    def test_analyze_command_with_quiet_flag(self, sample_project_dir):
        """Test analyze command with quiet flag."""
        output_dir = tempfile.mkdtemp()
        
        try:
            runner = CliRunner()
            result = runner.invoke(cli, [
                'analyze',
                sample_project_dir,
                '--output', output_dir,
                '--quiet'
            ])
            
            # Should complete successfully
            assert result.exit_code == 0
            
        finally:
            import shutil
            shutil.rmtree(output_dir, ignore_errors=True)

    def test_analyze_command_invalid_config_file(self, sample_project_dir):
        """Test analyze command with invalid config file."""
        output_dir = tempfile.mkdtemp()
        config_file = tempfile.mktemp(suffix='.json')
        
        try:
            # Create an invalid config file
            with open(config_file, 'w') as f:
                f.write("invalid json content {")
            
            runner = CliRunner()
            result = runner.invoke(cli, [
                'analyze',
                sample_project_dir,
                '--output', output_dir,
                '--config', config_file
            ])
            
            # Should fail gracefully
            assert result.exit_code != 0
            
        finally:
            import shutil
            shutil.rmtree(output_dir, ignore_errors=True)
            if os.path.exists(config_file):
                os.unlink(config_file)

    def test_analyze_command_nonexistent_config_file(self, sample_project_dir):
        """Test analyze command with nonexistent config file."""
        output_dir = tempfile.mkdtemp()
        
        try:
            runner = CliRunner()
            result = runner.invoke(cli, [
                'analyze',
                sample_project_dir,
                '--output', output_dir,
                '--config', '/nonexistent/config.json'
            ])
            
            # Should fail gracefully
            assert result.exit_code != 0
            
        finally:
            import shutil
            shutil.rmtree(output_dir, ignore_errors=True)
