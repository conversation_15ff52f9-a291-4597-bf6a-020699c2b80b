"""
Tests for CLI commands.
"""

import pytest
import tempfile
import os
from pathlib import Path
from unittest.mock import patch, MagicMock
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from vibe_check.cli.commands import analyze_command
from vibe_check.cli.main import analyze


class TestAnalyzeCommand:
    """Test cases for the analyze command."""

    @patch('vibe_check.core.simple_analyzer.simple_analyze_project')
    def test_analyze_command_basic(self, mock_analyze):
        """Test basic analyze command functionality."""
        # Setup mock
        mock_result = MagicMock()
        mock_result.total_file_count = 10
        mock_result.python_file_count = 5
        mock_analyze.return_value = mock_result

        project_dir = tempfile.mkdtemp()
        output_dir = tempfile.mkdtemp()

        try:
            # Call the function directly
            result = analyze_command(
                project_path=project_dir,
                output_dir=output_dir
            )

            # Should return a result dictionary
            assert isinstance(result, dict)
            mock_analyze.assert_called_once()

        finally:
            import shutil
            shutil.rmtree(project_dir, ignore_errors=True)
            shutil.rmtree(output_dir, ignore_errors=True)

    @patch('vibe_check.core.config.load_config')
    @patch('vibe_check.core.simple_analyzer.simple_analyze_project')
    def test_analyze_command_with_config(self, mock_analyze, mock_load_config):
        """Test analyze command with custom config."""
        # Setup mocks
        mock_config = {"tools": {"ruff": {"enabled": True}}}
        mock_load_config.return_value = mock_config

        mock_result = MagicMock()
        mock_analyze.return_value = mock_result

        project_dir = tempfile.mkdtemp()
        output_dir = tempfile.mkdtemp()
        config_file = tempfile.mktemp(suffix='.json')

        try:
            # Create config file
            import json
            with open(config_file, 'w') as f:
                json.dump(mock_config, f)

            # Call the function directly
            result = analyze_command(
                project_path=project_dir,
                config_path=config_file,
                output_dir=output_dir
            )

            assert isinstance(result, dict)
            mock_load_config.assert_called_once_with(config_file)
            mock_analyze.assert_called_once()

        finally:
            import shutil
            shutil.rmtree(project_dir, ignore_errors=True)
            shutil.rmtree(output_dir, ignore_errors=True)
            if os.path.exists(config_file):
                os.unlink(config_file)

    @patch('vibe_check.core.config.load_preset')
    @patch('vibe_check.core.simple_analyzer.simple_analyze_project')
    def test_analyze_command_with_preset(self, mock_analyze, mock_load_preset):
        """Test analyze command with preset."""
        # Setup mocks
        mock_config = {"tools": {"ruff": {"enabled": True}}}
        mock_load_preset.return_value = mock_config
        
        mock_result = MagicMock()
        mock_analyze.return_value = mock_result
        
        project_dir = tempfile.mkdtemp()
        output_dir = tempfile.mkdtemp()
        
        try:
            runner = CliRunner()
            result = runner.invoke(analyze, [
                project_dir,
                '--output', output_dir,
                '--preset', 'minimal'
            ])

            if result.exit_code != 0:
                print(f"Command failed with exit code {result.exit_code}")
                print(f"Output: {result.output}")
                print(f"Exception: {result.exception}")
            assert result.exit_code == 0
            mock_load_preset.assert_called_once_with('minimal')
            mock_analyze.assert_called_once()
            
        finally:
            import shutil
            shutil.rmtree(project_dir, ignore_errors=True)
            shutil.rmtree(output_dir, ignore_errors=True)

    def test_analyze_command_missing_project_path(self):
        """Test analyze command without project path."""
        runner = CliRunner()
        result = runner.invoke(analyze, [])

        # Should fail with missing argument
        assert result.exit_code != 0

    def test_analyze_command_nonexistent_project(self):
        """Test analyze command with nonexistent project."""
        runner = CliRunner()
        result = runner.invoke(analyze, ['/nonexistent/path'])

        # Should fail gracefully
        assert result.exit_code != 0

    @patch('vibe_check.core.simple_analyzer.simple_analyze_project')
    def test_analyze_command_with_exception(self, mock_analyze):
        """Test analyze command when analysis raises exception."""
        # Setup mock to raise exception
        mock_analyze.side_effect = Exception("Analysis failed")
        
        project_dir = tempfile.mkdtemp()
        output_dir = tempfile.mkdtemp()
        
        try:
            runner = CliRunner()
            result = runner.invoke(analyze, [
                project_dir,
                '--output', output_dir
            ])

            # Should fail gracefully
            assert result.exit_code != 0
            
        finally:
            import shutil
            shutil.rmtree(project_dir, ignore_errors=True)
            shutil.rmtree(output_dir, ignore_errors=True)

    @patch('vibe_check.core.simple_analyzer.simple_analyze_project')
    def test_analyze_command_verbose_mode(self, mock_analyze):
        """Test analyze command in verbose mode."""
        mock_result = MagicMock()
        mock_analyze.return_value = mock_result
        
        project_dir = tempfile.mkdtemp()
        output_dir = tempfile.mkdtemp()
        
        try:
            runner = CliRunner()
            result = runner.invoke(analyze, [
                project_dir,
                '--output', output_dir,
                '--verbose'
            ])

            assert result.exit_code == 0
            # In verbose mode, should see more output
            assert len(result.output) > 0
            
        finally:
            import shutil
            shutil.rmtree(project_dir, ignore_errors=True)
            shutil.rmtree(output_dir, ignore_errors=True)

    @patch('vibe_check.core.simple_analyzer.simple_analyze_project')
    def test_analyze_command_quiet_mode(self, mock_analyze):
        """Test analyze command in quiet mode."""
        mock_result = MagicMock()
        mock_analyze.return_value = mock_result
        
        project_dir = tempfile.mkdtemp()
        output_dir = tempfile.mkdtemp()
        
        try:
            runner = CliRunner()
            result = runner.invoke(analyze, [
                project_dir,
                '--output', output_dir,
                '--quiet'
            ])

            assert result.exit_code == 0
            
        finally:
            import shutil
            shutil.rmtree(project_dir, ignore_errors=True)
            shutil.rmtree(output_dir, ignore_errors=True)


class TestCLIUtilities:
    """Test cases for CLI utility functions."""

    def test_setup_logging_verbose(self):
        """Test logging setup in verbose mode."""
        from vibe_check.core.logging import setup_logging

        # Should not raise exception
        setup_logging(debug=True)

    def test_setup_logging_quiet(self):
        """Test logging setup in quiet mode."""
        from vibe_check.core.logging import setup_logging

        # Should not raise exception
        setup_logging(quiet=True)

    def test_setup_logging_normal(self):
        """Test logging setup in normal mode."""
        from vibe_check.core.logging import setup_logging

        # Should not raise exception
        setup_logging()
