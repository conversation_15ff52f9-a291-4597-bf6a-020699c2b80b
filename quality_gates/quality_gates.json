[{"gate_id": "basic_quality", "name": "Basic Quality Gate", "description": "Basic quality checks for code analysis", "enabled": true, "thresholds": [{"metric_name": "error_count", "operator": "lte", "value": 0, "severity": "high", "description": "No critical errors allowed"}, {"metric_name": "warning_count", "operator": "lte", "value": 10, "severity": "medium", "description": "Maximum 10 warnings allowed"}, {"metric_name": "quality_score", "operator": "gte", "value": 70, "severity": "medium", "description": "Minimum quality score of 70"}], "conditions": {}, "tags": ["default", "basic"], "created_at": "2025-06-22T19:13:40.940677", "updated_at": "2025-06-22T19:13:40.940678"}, {"gate_id": "strict_quality", "name": "Strict Quality Gate", "description": "Strict quality checks for production code", "enabled": true, "thresholds": [{"metric_name": "error_count", "operator": "eq", "value": 0, "severity": "critical", "description": "Zero errors required"}, {"metric_name": "warning_count", "operator": "lte", "value": 5, "severity": "high", "description": "Maximum 5 warnings allowed"}, {"metric_name": "quality_score", "operator": "gte", "value": 85, "severity": "high", "description": "Minimum quality score of 85"}, {"metric_name": "code_coverage", "operator": "gte", "value": 80, "severity": "medium", "description": "Minimum 80% code coverage"}], "conditions": {}, "tags": ["strict", "production"], "created_at": "2025-06-22T19:13:40.940685", "updated_at": "2025-06-22T19:13:40.940685"}, {"gate_id": "security_gate", "name": "Security Quality Gate", "description": "Security-focused quality checks", "enabled": true, "thresholds": [{"metric_name": "security_issues", "operator": "eq", "value": 0, "severity": "critical", "description": "No security issues allowed"}, {"metric_name": "error_count", "operator": "lte", "value": 2, "severity": "high", "description": "Maximum 2 critical errors"}, {"metric_name": "quality_score", "operator": "gte", "value": 75, "severity": "medium", "description": "Minimum quality score of 75"}], "conditions": {}, "tags": ["security", "compliance"], "created_at": "2025-06-22T19:13:40.940689", "updated_at": "2025-06-22T19:13:40.940690"}]