[{"dashboard_id": "enterprise_overview", "name": "Enterprise Overview", "description": "Comprehensive overview of Vibe Check enterprise features and metrics", "theme": "light", "layout": {"grid_columns": 12, "grid_rows": 8, "widget_margin": 10, "responsive": true, "breakpoints": {"xs": 480, "sm": 768, "md": 1024, "lg": 1280, "xl": 1920}}, "widgets": [{"widget_id": "system_overview", "title": "System Overview", "widget_type": "metrics_card", "size": "large", "position": {"x": 0, "y": 0, "width": 4, "height": 2}, "chart_config": null, "data_config": {"metrics": ["cpu", "memory", "disk", "network"]}, "style_config": {}, "enabled": true}, {"widget_id": "analysis_trends", "title": "Analysis Trends", "widget_type": "chart", "size": "wide", "position": {"x": 4, "y": 0, "width": 8, "height": 2}, "chart_config": {"chart_type": "line", "title": "Analysis Trends", "data_source": "analysis_metrics", "x_axis": "timestamp", "y_axis": "count", "color_scheme": ["#3B82F6", "#EF4444", "#10B981", "#F59E0B", "#8B5CF6"], "show_legend": true, "show_grid": true, "animation": true, "refresh_interval": 30}, "data_config": {}, "style_config": {}, "enabled": true}, {"widget_id": "quality_gates", "title": "Quality Gates Status", "widget_type": "chart", "size": "medium", "position": {"x": 0, "y": 2, "width": 6, "height": 2}, "chart_config": {"chart_type": "pie", "title": "Quality Gates", "data_source": "quality_gate_results", "x_axis": "", "y_axis": "", "color_scheme": ["#3B82F6", "#EF4444", "#10B981", "#F59E0B", "#8B5CF6"], "show_legend": true, "show_grid": true, "animation": true, "refresh_interval": 30}, "data_config": {}, "style_config": {}, "enabled": true}, {"widget_id": "active_alerts", "title": "Active Alerts", "widget_type": "alert_list", "size": "medium", "position": {"x": 6, "y": 2, "width": 6, "height": 2}, "chart_config": null, "data_config": {"max_items": 10, "severity_filter": ["high", "critical"]}, "style_config": {}, "enabled": true}, {"widget_id": "team_activity", "title": "Team Activity", "widget_type": "chart", "size": "wide", "position": {"x": 0, "y": 4, "width": 12, "height": 2}, "chart_config": {"chart_type": "bar", "title": "Team Activity", "data_source": "team_metrics", "x_axis": "team", "y_axis": "activity", "color_scheme": ["#3B82F6", "#EF4444", "#10B981", "#F59E0B", "#8B5CF6"], "show_legend": true, "show_grid": true, "animation": true, "refresh_interval": 30}, "data_config": {}, "style_config": {}, "enabled": true}, {"widget_id": "cicd_pipelines", "title": "CI/CD Pipeline Status", "widget_type": "pipeline_status", "size": "large", "position": {"x": 0, "y": 6, "width": 6, "height": 2}, "chart_config": null, "data_config": {"platforms": ["github", "gitlab", "jenkins", "azure"]}, "style_config": {}, "enabled": true}, {"widget_id": "performance_metrics", "title": "Performance Metrics", "widget_type": "chart", "size": "large", "position": {"x": 6, "y": 6, "width": 6, "height": 2}, "chart_config": {"chart_type": "gauge", "title": "Performance", "data_source": "performance_metrics", "x_axis": "", "y_axis": "", "color_scheme": ["#3B82F6", "#EF4444", "#10B981", "#F59E0B", "#8B5CF6"], "show_legend": true, "show_grid": true, "animation": true, "refresh_interval": 30}, "data_config": {}, "style_config": {}, "enabled": true}], "permissions": {"admin": ["read", "write"], "user": ["read"]}, "auto_refresh": true, "refresh_interval": 30, "created_at": "2025-06-22T19:37:56.007410", "updated_at": "2025-06-22T19:37:56.007411"}]