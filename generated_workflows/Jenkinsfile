pipeline {
    agent any
    
    triggers {
        pollSCM('H/5 * * * *')
cron('H 2 * * 1')
    }
    
    environment {
        VIBE_CHECK_OUTPUT_DIR = 'vibe-check-reports'
PYTHONPATH = '${WORKSPACE}'
    }
    
    options {
        buildDiscarder(logRotator(numToKeepStr: '10'))
        timeout(time: 30, unit: 'MINUTES')
        skipStagesAfterUnstable()
        parallelsAlwaysFailFast()
    }
    
    stages {

        stage('Prepare') {
            steps {
                echo 'Preparing Vibe Check analysis environment...'
                sh '''
                    python3 --version
                    pip3 install --upgrade pip
                    pip3 install vibe-check
                    mkdir -p ${VIBE_CHECK_OUTPUT_DIR}
                '''
            }
        }

        stage('Vibe Check Analysis') {
            steps {
                echo 'Running comprehensive Vibe Check analysis...'
                sh '''
                    vibe-check-standalone \
                    . \
                    --categories \
                    style,security,complexity,types \
                    --severity \
                    warning \
                    --format \
                    html \
                    --output \
                    ${VIBE_CHECK_OUTPUT_DIR}/report.html \
                    --junit-output \
                    ${VIBE_CHECK_OUTPUT_DIR}/junit.xml \
                    --json-output \
                    ${VIBE_CHECK_OUTPUT_DIR}/results.json \
                    --fail-on-error
                '''
            }
            post {
                always {
                    publishHTML([
                        allowMissing: false,
                        alwaysLinkToLastBuild: true,
                        keepAll: true,
                        reportDir: '${VIBE_CHECK_OUTPUT_DIR}',
                        reportFiles: 'report.html',
                        reportName: 'Vibe Check Report'
                    ])
                    
                    junit testResults: '${VIBE_CHECK_OUTPUT_DIR}/junit.xml', allowEmptyResults: true
                    
                    archiveArtifacts artifacts: '${VIBE_CHECK_OUTPUT_DIR}/**/*', allowEmptyArchive: true
                }
            }
        }

        stage('Generate Reports') {
            steps {
                echo 'Generating comprehensive analysis reports...'
                sh '''
                    vibe-check-report \
                        --input ${VIBE_CHECK_OUTPUT_DIR} \
                        --format html,json,pdf \
                        --include-trends \
                        --include-recommendations
                '''
            }
            post {
                always {
                    publishHTML([
                        allowMissing: false,
                        alwaysLinkToLastBuild: true,
                        keepAll: true,
                        reportDir: '${VIBE_CHECK_OUTPUT_DIR}',
                        reportFiles: '*.html',
                        reportName: 'Comprehensive Analysis Report'
                    ])
                }
            }
        }
    }
    
    
    post {
        always {
            echo 'Cleaning up workspace...'
            cleanWs()
        }
        success {
            echo 'Pipeline completed successfully!'
            emailext (
                subject: "✅ Vibe Check Analysis - SUCCESS: ${env.JOB_NAME} - ${env.BUILD_NUMBER}",
                body: "The Vibe Check analysis completed successfully. View the report at: ${env.BUILD_URL}Vibe_Check_Report/",
                to: "${env.CHANGE_AUTHOR_EMAIL ?: env.DEFAULT_RECIPIENTS}"
            )
        }
        failure {
            echo 'Pipeline failed!'
            emailext (
                subject: "❌ Vibe Check Analysis - FAILURE: ${env.JOB_NAME} - ${env.BUILD_NUMBER}",
                body: "The Vibe Check analysis failed. Check the console output at: ${env.BUILD_URL}console",
                to: "${env.CHANGE_AUTHOR_EMAIL ?: env.DEFAULT_RECIPIENTS}"
            )
        }
        unstable {
            echo 'Pipeline is unstable!'
            emailext (
                subject: "⚠️ Vibe Check Analysis - UNSTABLE: ${env.JOB_NAME} - ${env.BUILD_NUMBER}",
                body: "The Vibe Check analysis completed with warnings. Review the report at: ${env.BUILD_URL}Vibe_Check_Report/",
                to: "${env.CHANGE_AUTHOR_EMAIL ?: env.DEFAULT_RECIPIENTS}"
            )
        }
    }
}