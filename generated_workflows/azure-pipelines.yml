trigger:
  branches:
    include:
    - main
    - develop
  paths:
    exclude:
    - docs/*
    - '*.md'
  schedules:
  - cron: 0 2 * * 1
    displayName: Weekly Vibe Check
    branches:
      include:
      - main
pr:
  branches:
    include:
    - main
    - develop
  paths:
    exclude:
    - docs/*
    - '*.md'
pool:
  vmImage: ubuntu-latest
variables:
  pythonVersion: '3.11'
  vibeCheckOutputDir: $(Build.ArtifactStagingDirectory)/vibe-check-reports
stages:
- stage: Analysis
  displayName: Vibe Check Analysis
  jobs:
  - job: VibeCheckAnalysis
    displayName: Vibe Check Analysis
    steps:
    - task: UsePythonVersion@0
      inputs:
        versionSpec: $(pythonVersion)
      displayName: Use Python $(pythonVersion)
    - script: python -m pip install --upgrade pip && pip install vibe-check
      displayName: Install Vibe Check
    - script: "vibe-check-standalone \\\n  . \\\n  --categories \\\n  style,security,complexity,docs,imports,types\
        \ \\\n  --severity \\\n  info \\\n  --format \\\n  html \\\n  --output \\\n\
        \  $(vibeCheckOutputDir)/report.html \\\n  --junit-output \\\n  $(vibeCheckOutputDir)/junit.xml\
        \ \\\n  --json-output \\\n  $(vibeCheckOutputDir)/results.json \\\n  --fail-on-error"
      displayName: Run Vibe Check Analysis
    - task: PublishTestResults@2
      condition: succeededOrFailed()
      inputs:
        testResultsFiles: $(vibeCheckOutputDir)/junit.xml
        testRunTitle: Vibe Check Analysis Results
    - task: PublishHtmlReport@1
      condition: succeededOrFailed()
      inputs:
        reportDir: $(vibeCheckOutputDir)
        tabName: Vibe Check Report
    - task: PublishBuildArtifacts@1
      condition: succeededOrFailed()
      inputs:
        pathToPublish: $(vibeCheckOutputDir)
        artifactName: vibe-check-reports
- stage: Report
  displayName: Generate Reports
  dependsOn: Analysis
  jobs:
  - job: GenerateReports
    displayName: Generate Comprehensive Reports
    steps:
    - task: UsePythonVersion@0
      inputs:
        versionSpec: $(pythonVersion)
    - script: pip install vibe-check
      displayName: Install Vibe Check
    - script: "vibe-check-report \\\n  --input $(vibeCheckOutputDir) \\\n  --format\
        \ html,json,pdf \\\n  --include-trends \\\n  --include-recommendations"
      displayName: Generate Comprehensive Reports
    - task: PublishBuildArtifacts@1
      inputs:
        pathToPublish: $(vibeCheckOutputDir)
        artifactName: comprehensive-reports
