name: Vibe Check - GitHub Actions Analysis Pipeline
'on':
  push:
    branches:
    - main
    - develop
    paths-ignore:
    - docs/**
    - '*.md'
  pull_request:
    branches:
    - main
    - develop
    types:
    - opened
    - synchronize
    - reopened
jobs:
  vibe-check:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    - name: Install Vibe Check
      run: pip install vibe-check
    - name: Run Vibe Check Analysis
      run: vibe-check-standalone . --categories style,security,complexity --severity
        warning --format html --output vibe-check-report.html --fail-on-error
    - name: Upload Analysis Report
      uses: actions/upload-artifact@v3
      with:
        name: vibe-check-report
        path: vibe-check-report.*
        retention-days: 30
      if: always()
    - name: Comment PR with Results
      uses: actions/github-script@v6
      if: github.event_name == 'pull_request'
      with:
        script: "const fs = require('fs');\nconst path = require('path');\n\ntry {\n\
          \  const reportPath = 'vibe-check-report.html';\n  if (fs.existsSync(reportPath))\
          \ {\n    const reportContent = fs.readFileSync(reportPath, 'utf8');\n  \
          \  const summary = extractSummary(reportContent);\n    \n    const comment\
          \ = `## \U0001F50D Vibe Check Analysis Results\n    \n${summary}\n\n\U0001F4CA\
          \ [View Full Report](${process.env.GITHUB_SERVER_URL}/${process.env.GITHUB_REPOSITORY}/actions/runs/${process.env.GITHUB_RUN_ID})\n\
          \    `;\n    \n    github.rest.issues.createComment({\n      issue_number:\
          \ context.issue.number,\n      owner: context.repo.owner,\n      repo: context.repo.repo,\n\
          \      body: comment\n    });\n  }\n} catch (error) {\n  console.error('Failed\
          \ to post PR comment:', error);\n}\n\nfunction extractSummary(html) {\n\
          \  // Extract key metrics from HTML report\n  const metrics = html.match(/total_issues_found.*?(\\\
          d+)/);\n  const files = html.match(/total_files_analyzed.*?(\\d+)/);\n \
          \ \n  return `- **Files Analyzed:** ${files ? files[1] : 'N/A'}\n- **Issues\
          \ Found:** ${metrics ? metrics[1] : 'N/A'}`;\n}"
