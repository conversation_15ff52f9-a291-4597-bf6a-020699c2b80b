stages:
- prepare
- analyze
- quality-gates
- report
variables:
  PIP_CACHE_DIR: $CI_PROJECT_DIR/.cache/pip
  VIBE_CHECK_OUTPUT_DIR: vibe-check-reports
before_script:
- python --version
- pip install --upgrade pip
- pip install vibe-check
prepare:
  stage: prepare
  script:
  - echo 'Preparing Vibe Check analysis...'
  - mkdir -p $VIBE_CHECK_OUTPUT_DIR
  cache:
    paths:
    - .cache/pip/
vibe-check-analysis:
  stage: analyze
  script:
  - echo 'Running Vibe Check analysis...'
  - vibe-check-standalone . --categories security,complexity,docs --severity error
    --format html --output $VIBE_CHECK_OUTPUT_DIR/report.html --junit-output $VIBE_CHECK_OUTPUT_DIR/junit.xml
    --fail-on-error
  artifacts:
    reports:
      junit: $VIBE_CHECK_OUTPUT_DIR/junit.xml
    paths:
    - $VIBE_CHECK_OUTPUT_DIR/
    expire_in: 1 week
    when: always
  rules:
  - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    when: always
generate-report:
  stage: report
  script:
  - echo 'Generating comprehensive report...'
  - vibe-check-report --input $VIBE_CHECK_OUTPUT_DIR --format html
  - vibe-check-report --input $VIBE_CHECK_OUTPUT_DIR --format json
  artifacts:
    paths:
    - $VIBE_CHECK_OUTPUT_DIR/
    reports:
      coverage_report:
        coverage_format: cobertura
        path: $VIBE_CHECK_OUTPUT_DIR/coverage.xml
    expire_in: 30 days
  dependencies:
  - vibe-check-analysis
  rules:
  - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    when: always
