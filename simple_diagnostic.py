#!/usr/bin/env python3
"""
Simple Database Diagnostic
=========================

Quick diagnostic to validate the root cause assumptions.
"""

import sys
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

def diagnose_project_metrics_issue():
    """Diagnose the ProjectMetrics property setter issue"""
    print("=== DIAGNOSING PROJECT METRICS PROPERTY ISSUE ===")
    
    try:
        from vibe_check.core.models.project_metrics import ProjectMetrics
        
        # Create a ProjectMetrics instance
        metrics = ProjectMetrics(project_path="/test/path")
        print("✓ ProjectMetrics creation successful")
        
        # Check the total_file_count property
        print(f"total_file_count value: {metrics.total_file_count}")
        
        # Check if it has a setter
        prop = getattr(ProjectMetrics, 'total_file_count', None)
        print(f"Property type: {type(prop)}")
        
        if isinstance(prop, property):
            print(f"Property fget: {prop.fget}")
            print(f"Property fset: {prop.fset}")
            
            if prop.fset is None:
                print("✗ CONFIRMED: total_file_count has no setter!")
            else:
                print("✓ total_file_count has a setter")
        
        # Try to set the property to trigger the error
        print("Attempting to set total_file_count...")
        try:
            setattr(metrics, 'total_file_count', 10)
            print("✓ Property setting successful")
        except Exception as e:
            print(f"✗ Property setting failed: {e}")
            print(f"Error type: {type(e)}")
        
    except Exception as e:
        print(f"ProjectMetrics diagnostic failed: {e}")

def main():
    """Run diagnostic"""
    print("Starting simple database diagnostic analysis...")
    diagnose_project_metrics_issue()
    print("Diagnostic analysis completed")

if __name__ == "__main__":
    main()