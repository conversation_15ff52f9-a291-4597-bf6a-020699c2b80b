repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict
      - id: check-toml
      - id: debug-statements
      - id: mixed-line-ending

  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        language_version: python3
        exclude: ^legacy/

  - repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: v0.0.270
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]
        exclude: ^legacy/

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.3.0
    hooks:
      - id: mypy
        additional_dependencies: [types-PyYAML, types-requests]
        exclude: ^(legacy/|tests/|scripts/)

  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: [-c, pyproject.toml]
        additional_dependencies: ["bandit[toml]"]
        exclude: ^(legacy/|tests/)

  # Enhanced isort for import sorting
  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        name: Sort Python imports with isort
        args: [--profile=black, --line-length=88]

  # Documentation style checking
  - repo: https://github.com/pycqa/pydocstyle
    rev: 6.3.0
    hooks:
      - id: pydocstyle
        name: Check docstring style
        args: [--convention=google]
        exclude: ^(legacy/|tests/)

  # Enhanced Vibe Check quality enforcement
  - repo: local
    hooks:
      - id: vibe-check-quality-enforcement
        name: Comprehensive Quality Enforcement
        entry: python3 scripts/pre_commit_quality_check.py
        language: system
        files: \.(py|yaml|yml|json)$
        pass_filenames: true
        args: [--severity=warning]

      - id: vibe-check-constants-strict
        name: Constants Usage Validation (Strict)
        entry: python3 scripts/pre_commit_quality_check.py
        language: system
        files: \.py$
        pass_filenames: true
        args: [--types=constants_usage, --severity=error]

      - id: vibe-check-terminology-check
        name: Terminology Consistency Check
        entry: python3 scripts/pre_commit_quality_check.py
        language: system
        files: \.py$
        pass_filenames: true
        args: [--types=terminology, --severity=info]

      - id: vibe-check-config-validation
        name: Configuration Schema Validation
        entry: python3 scripts/pre_commit_quality_check.py
        language: system
        files: \.(yaml|yml|json|toml)$
        pass_filenames: true
        args: [--types=config_schema, --severity=error]

      - id: pytest-check
        name: pytest-check
        entry: pytest
        language: system
        pass_filenames: false
        always_run: true
        stages: [push]
