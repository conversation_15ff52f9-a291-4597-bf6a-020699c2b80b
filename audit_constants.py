#!/usr/bin/env python3
"""
Constants Discovery & Analysis Tool
===================================

This script systematically analyzes the Vibe Check codebase to identify:
1. Hardcoded values and magic numbers
2. String literals that should be constants
3. Repeated values across multiple files
4. Constants that need centralization

Strategic Approach:
- Pattern recognition for systematic issues
- Categorization by scope (system-wide, meta-system, component)
- Identification of naming convention violations
- Security-sensitive hardcoded values
"""

import ast
import re
import os
from pathlib import Path
from collections import defaultdict, Counter
from typing import Dict, List, Set, Tuple, Any
from dataclasses import dataclass


@dataclass
class ConstantCandidate:
    """Represents a potential constant that should be centralized."""
    value: str
    type: str  # 'string', 'number', 'boolean'
    files: List[str]
    lines: List[int]
    contexts: List[str]  # Function/class context where found
    category: str  # 'magic_number', 'hardcoded_string', 'repeated_value'
    scope: str  # 'system', 'meta_system', 'component'
    priority: str  # 'high', 'medium', 'low'


class ConstantsAnalyzer(ast.NodeVisitor):
    """AST visitor to analyze constants in Python files."""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.constants = []
        self.current_context = []
        
    def visit_Constant(self, node: ast.Constant) -> None:
        """Visit constant nodes (strings, numbers, booleans)."""
        if isinstance(node.value, (str, int, float, bool)):
            self._analyze_constant(node.value, node.lineno)
        self.generic_visit(node)
    
    def visit_Str(self, node: ast.Str) -> None:
        """Visit string nodes (for older Python versions)."""
        self._analyze_constant(node.s, node.lineno)
        self.generic_visit(node)
    
    def visit_Num(self, node: ast.Num) -> None:
        """Visit number nodes (for older Python versions)."""
        self._analyze_constant(node.n, node.lineno)
        self.generic_visit(node)
    
    def visit_FunctionDef(self, node: ast.FunctionDef) -> None:
        """Track function context."""
        self.current_context.append(f"function:{node.name}")
        self.generic_visit(node)
        self.current_context.pop()
    
    def visit_ClassDef(self, node: ast.ClassDef) -> None:
        """Track class context."""
        self.current_context.append(f"class:{node.name}")
        self.generic_visit(node)
        self.current_context.pop()
    
    def _analyze_constant(self, value: Any, line_number: int) -> None:
        """Analyze a constant value to determine if it should be centralized."""
        context = ".".join(self.current_context) if self.current_context else "module"
        
        # Skip very common values that are unlikely to need centralization
        if value in (None, True, False, 0, 1, -1, "", " ", "\n", "\t"):
            return
        
        # Analyze strings
        if isinstance(value, str):
            category = self._categorize_string(value)
            if category:
                self.constants.append({
                    'value': value,
                    'type': 'string',
                    'line': line_number,
                    'context': context,
                    'category': category,
                    'file': self.file_path
                })
        
        # Analyze numbers
        elif isinstance(value, (int, float)):
            category = self._categorize_number(value)
            if category:
                self.constants.append({
                    'value': value,
                    'type': 'number',
                    'line': line_number,
                    'context': context,
                    'category': category,
                    'file': self.file_path
                })
    
    def _categorize_string(self, value: str) -> str:
        """Categorize string constants."""
        # Skip very short strings
        if len(value) < 3:
            return None
        
        # Error messages and user-facing text
        if any(keyword in value.lower() for keyword in 
               ['error', 'warning', 'failed', 'success', 'invalid', 'missing']):
            return 'error_message'
        
        # File extensions and formats
        if re.match(r'^\.[a-z]+$', value):
            return 'file_extension'
        
        # URLs and paths
        if value.startswith(('http://', 'https://', '/', './')):
            return 'url_or_path'
        
        # Configuration keys
        if re.match(r'^[a-z_]+$', value) and '_' in value:
            return 'config_key'
        
        # Tool names and identifiers
        if value in ['ruff', 'mypy', 'bandit', 'black', 'isort', 'pylint']:
            return 'tool_name'
        
        # Default values and placeholders
        if value in ['default', 'unknown', 'none', 'auto', 'minimal', 'standard', 'comprehensive']:
            return 'default_value'
        
        return 'hardcoded_string'
    
    def _categorize_number(self, value: float) -> str:
        """Categorize numeric constants."""
        # Common magic numbers that should be constants
        magic_numbers = {
            100: 'percentage_base',
            1000: 'milliseconds_per_second',
            60: 'seconds_per_minute',
            24: 'hours_per_day',
            80: 'default_line_length',
            120: 'max_line_length',
            10: 'decimal_base',
            2: 'binary_base',
            8: 'octal_base',
            16: 'hex_base'
        }
        
        if value in magic_numbers:
            return magic_numbers[value]
        
        # Large numbers that might be thresholds
        if isinstance(value, int) and value > 100:
            return 'threshold_value'
        
        # Decimal values that might be ratios or percentages
        if isinstance(value, float) and 0 < value < 1:
            return 'ratio_or_percentage'
        
        return 'magic_number'


def analyze_file(file_path: Path) -> List[Dict]:
    """Analyze a single Python file for constants."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content, filename=str(file_path))
        analyzer = ConstantsAnalyzer(str(file_path))
        analyzer.visit(tree)
        return analyzer.constants
        
    except (SyntaxError, UnicodeDecodeError) as e:
        print(f"Warning: Could not analyze {file_path}: {e}")
        return []


def find_repeated_constants(all_constants: List[Dict]) -> Dict[str, List[Dict]]:
    """Find constants that appear in multiple files."""
    value_to_occurrences = defaultdict(list)
    
    for const in all_constants:
        key = f"{const['type']}:{const['value']}"
        value_to_occurrences[key].append(const)
    
    # Return only values that appear in multiple files or multiple times
    repeated = {}
    for key, occurrences in value_to_occurrences.items():
        files = set(occ['file'] for occ in occurrences)
        if len(files) > 1 or len(occurrences) > 3:  # Multiple files or many occurrences
            repeated[key] = occurrences
    
    return repeated


def categorize_by_scope(file_path: str) -> str:
    """Determine the scope category based on file path."""
    path_parts = Path(file_path).parts
    
    if 'core' in path_parts:
        return 'system'
    elif any(meta in path_parts for meta in ['enterprise', 'ui', 'tools']):
        return 'meta_system'
    else:
        return 'component'


def prioritize_constants(repeated_constants: Dict[str, List[Dict]]) -> List[ConstantCandidate]:
    """Prioritize constants for centralization based on impact and usage."""
    candidates = []
    
    for key, occurrences in repeated_constants.items():
        const_type, value = key.split(':', 1)
        files = list(set(occ['file'] for occ in occurrences))
        lines = [occ['line'] for occ in occurrences]
        contexts = [occ['context'] for occ in occurrences]
        categories = [occ['category'] for occ in occurrences]
        
        # Determine priority
        priority = 'low'
        if len(files) > 3:  # Used in many files
            priority = 'high'
        elif any(cat in ['error_message', 'tool_name', 'config_key'] for cat in categories):
            priority = 'high'
        elif len(files) > 1:
            priority = 'medium'
        
        # Determine scope
        scopes = [categorize_by_scope(f) for f in files]
        if 'system' in scopes:
            scope = 'system'
        elif len(set(scopes)) > 1:
            scope = 'system'  # Cross-scope usage
        else:
            scope = scopes[0]
        
        candidate = ConstantCandidate(
            value=value,
            type=const_type,
            files=files,
            lines=lines,
            contexts=contexts,
            category=categories[0],  # Use first category
            scope=scope,
            priority=priority
        )
        candidates.append(candidate)
    
    # Sort by priority and impact
    priority_order = {'high': 0, 'medium': 1, 'low': 2}
    candidates.sort(key=lambda c: (priority_order[c.priority], -len(c.files)))
    
    return candidates


def main():
    """Main analysis function."""
    print("🔍 Starting Constants Discovery & Analysis")
    print("=" * 50)
    
    # Find all Python files
    vibe_check_dir = Path("vibe_check")
    if not vibe_check_dir.exists():
        print("Error: vibe_check directory not found")
        return
    
    python_files = list(vibe_check_dir.rglob("*.py"))
    print(f"Found {len(python_files)} Python files to analyze")
    
    # Analyze all files
    all_constants = []
    for file_path in python_files:
        constants = analyze_file(file_path)
        all_constants.extend(constants)
    
    print(f"Found {len(all_constants)} constant candidates")
    
    # Find repeated constants
    repeated = find_repeated_constants(all_constants)
    print(f"Found {len(repeated)} repeated constant patterns")
    
    # Prioritize for centralization
    candidates = prioritize_constants(repeated)
    
    # Generate report
    print("\n📊 Constants Centralization Report")
    print("=" * 50)
    
    high_priority = [c for c in candidates if c.priority == 'high']
    medium_priority = [c for c in candidates if c.priority == 'medium']
    
    print(f"\n🚨 HIGH PRIORITY ({len(high_priority)} items):")
    for candidate in high_priority[:10]:  # Show top 10
        print(f"  • {candidate.type}:{candidate.value}")
        print(f"    Files: {len(candidate.files)}, Category: {candidate.category}, Scope: {candidate.scope}")
        print(f"    Sample files: {', '.join(candidate.files[:3])}")
        print()
    
    print(f"\n⚠️  MEDIUM PRIORITY ({len(medium_priority)} items):")
    for candidate in medium_priority[:5]:  # Show top 5
        print(f"  • {candidate.type}:{candidate.value}")
        print(f"    Files: {len(candidate.files)}, Category: {candidate.category}")
        print()
    
    return candidates


if __name__ == "__main__":
    candidates = main()
