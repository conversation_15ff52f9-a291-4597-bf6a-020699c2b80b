"""
Compatibility Module
=================

This module provides compatibility functions and classes for different Python versions
and operating systems.
"""

import os
import platform
import sys
from typing import Any, Dict, Optional

# Import models for compatibility

# Python version information
PY_VERSION = sys.version_info
PY_MAJOR = PY_VERSION.major
PY_MINOR = PY_VERSION.minor
PY_MICRO = PY_VERSION.micro

# Platform information
PLATFORM = platform.system().lower()
IS_WINDOWS = PLATFORM == "windows"
IS_MACOS = PLATFORM == "darwin"
IS_LINUX = PLATFORM == "linux"


def get_platform_info() -> Dict[str, str]:
    """
    Get information about the current platform.

    Returns:
        Dictionary with platform information
    """
    return {
        "system": platform.system(),
        "release": platform.release(),
        "version": platform.version(),
        "machine": platform.machine(),
        "processor": platform.processor(),
        "python_version": platform.python_version(),
        "python_implementation": platform.python_implementation()
    }


def get_env_var(name: str, default: Optional[str] = None) -> Optional[str]:
    """
    Get an environment variable, with a default value if not set.

    Args:
        name: Name of the environment variable
        default: Default value if the environment variable is not set

    Returns:
        Value of the environment variable, or the default value
    """
    return os.environ.get(name, default)


def set_env_var(name: str, value: str) -> None:
    """
    Set an environment variable.

    Args:
        name: Name of the environment variable
        value: Value to set
    """
    os.environ[name] = value


def get_path_separator() -> str:
    """
    Get the path separator for the current platform.

    Returns:
        Path separator
    """
    return os.path.sep


def analyze_project(project_path: str, config_path: Optional[str] = None, output_dir: Optional[str] = None,
                 config_override: Optional[Dict[str, Any]] = None, show_progress: bool = False,
                 **kwargs) -> Dict[str, Any]:
    """
    Analyze a project.

    This is a compatibility function that forwards to the new API.

    Args:
        project_path: Path to the project to analyze
        config_path: Optional path to a configuration file
        output_dir: Optional directory to write output files to
        config_override: Optional dictionary to override configuration values
        show_progress: Whether to show progress during analysis
        **kwargs: Additional keyword arguments

    Returns:
        Analysis results
    """
    # Import here to avoid circular imports
    import vibe_check

    # Forward to the main analyze_project function
    return vibe_check.analyze_project(
        project_path=project_path,
        config_path=config_path,
        output_dir=output_dir,
        config_override=config_override,
        show_progress=show_progress,
        **kwargs
    )


def get_line_separator() -> str:
    """
    Get the line separator for the current platform.

    Returns:
        Line separator
    """
    return os.linesep


def get_temp_dir() -> str:
    """
    Get the temporary directory for the current platform.

    Returns:
        Temporary directory
    """
    return os.path.normpath(os.getenv("TMPDIR") or os.getenv("TMP") or os.getenv("TEMP") or "/tmp")


def get_home_dir() -> str:
    """
    Get the home directory for the current user.

    Returns:
        Home directory
    """
    return os.path.expanduser("~")


def get_config_dir() -> str:
    """
    Get the configuration directory for the current platform.

    Returns:
        Configuration directory
    """
    if IS_WINDOWS:
        return os.path.join(os.getenv("APPDATA", ""), "vibe_check")
    elif IS_MACOS:
        return os.path.join(get_home_dir(), "Library", "Application Support", "vibe_check")
    else:
        return os.path.join(get_home_dir(), ".config", "vibe_check")


def get_data_dir() -> str:
    """
    Get the data directory for the current platform.

    Returns:
        Data directory
    """
    if IS_WINDOWS:
        return os.path.join(os.getenv("APPDATA", ""), "vibe_check", "data")
    elif IS_MACOS:
        return os.path.join(get_home_dir(), "Library", "Application Support", "vibe_check", "data")
    else:
        return os.path.join(get_home_dir(), ".local", "share", "vibe_check")


def get_cache_dir() -> str:
    """
    Get the cache directory for the current platform.

    Returns:
        Cache directory
    """
    if IS_WINDOWS:
        return os.path.join(os.getenv("LOCALAPPDATA", ""), "vibe_check", "cache")
    elif IS_MACOS:
        return os.path.join(get_home_dir(), "Library", "Caches", "vibe_check")
    else:
        return os.path.join(get_home_dir(), ".cache", "vibe_check")


def get_log_dir() -> str:
    """
    Get the log directory for the current platform.

    Returns:
        Log directory
    """
    if IS_WINDOWS:
        return os.path.join(os.getenv("LOCALAPPDATA", ""), "vibe_check", "logs")
    elif IS_MACOS:
        return os.path.join(get_home_dir(), "Library", "Logs", "vibe_check")
    else:
        return os.path.join(get_home_dir(), ".local", "share", "vibe_check", "logs")


def ensure_dir_exists(path: str) -> None:
    """
    Ensure a directory exists, creating it if necessary.

    Args:
        path: Directory path
    """
    if not os.path.exists(path):
        os.makedirs(path, exist_ok=True)


def is_executable(path: str) -> bool:
    """
    Check if a file is executable.

    Args:
        path: File path

    Returns:
        True if the file is executable, False otherwise
    """
    return os.path.isfile(path) and os.access(path, os.X_OK)


def which(program: str) -> Optional[str]:
    """
    Find the path to an executable.

    Args:
        program: Name of the program

    Returns:
        Path to the executable, or None if not found
    """
    # Check if the program is an absolute path
    if os.path.isabs(program) and is_executable(program):
        return program

    # Check if the program is in the current directory
    if is_executable(program):
        return os.path.abspath(program)

    # Check if the program is in the PATH
    path_env = os.getenv("PATH", "")
    for path in path_env.split(os.pathsep):
        path = path.strip('"')
        exe_file = os.path.join(path, program)
        if is_executable(exe_file):
            return exe_file

    # Program not found
    return None
