"""
Vibe Check - Project Analysis Tool
========================

Vibe Check is a comprehensive analysis tool for Python projects that provides insights
into code quality, dependencies, complexity, and more. It uses a simple, efficient
analysis engine to provide fast and accurate results.

This is the main package for Vibe Check, exposing the public API for analysis.

Example usage:

```python
from vibe_check import analyze_project

metrics = analyze_project('/path/to/your/project')
```

See the documentation for more information on configuration options and
interpreting the analysis results.
"""

from vibe_check.core.simple_analyzer import (
    simple_analyze_project as analyze_project,
)
from vibe_check.core.models import (
    FileMetrics,
    ProjectMetrics,
    DirectoryMetrics,
)
from vibe_check.core.version import __version__

__all__ = [
    'analyze_project',
    'FileMetrics',
    'ProjectMetrics',
    'DirectoryMetrics',
    '__version__',
]
