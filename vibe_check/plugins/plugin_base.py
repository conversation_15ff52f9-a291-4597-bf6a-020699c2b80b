"""
Plugin Base Classes
================

This module defines the base classes for all Vibe Check plugins.
These classes provide the interface that plugins must implement
to work with the Vibe Check system.

This module has been updated to use the new plugin interfaces
defined in plugin_interface.py. The base classes here provide
concrete implementations of the interfaces.
"""

import logging
from abc import ABC, abstractmethod
from pathlib import Path
from typing import List, Dict, Any, Optional


logger = logging.getLogger("vibe_check_plugins")


class Plugin(ABC):
    """Base class for all plugins."""

    def __init__(self, name: str, version: str, description: str):
        """
        Initialize the plugin.

        Args:
            name: Plugin name
            version: Plugin version
            description: Plugin description
        """
        self._name = name
        self._version = version
        self._description = description
        self._enabled = True

    @property
    def name(self) -> str:
        """
        Get the name of the plugin.

        Returns:
            Plugin name
        """
        return self._name

    @property
    def version(self) -> str:
        """
        Get the version of the plugin.

        Returns:
            Plugin version
        """
        return self._version

    @property
    def description(self) -> str:
        """
        Get the description of the plugin.

        Returns:
            Plugin description
        """
        return self._description

    @property
    def enabled(self) -> bool:
        """
        Get whether the plugin is enabled.

        Returns:
            True if the plugin is enabled, False otherwise
        """
        return self._enabled

    @enabled.setter
    def enabled(self, value: bool) -> None:
        """
        Set whether the plugin is enabled.

        Args:
            value: True to enable the plugin, False to disable it
        """
        self._enabled = value

    @property
    def dependencies(self) -> List[str]:
        """
        Get the list of plugin dependencies.

        Returns:
            List of plugin names that this plugin depends on
        """
        return []

    def initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the plugin with the given configuration.

        Args:
            config: Plugin configuration
        """
        pass

    def register_tools(self) -> List[str]:
        """
        Register tools provided by this plugin.

        Returns:
            List of tool names
        """
        return []

    def shutdown(self) -> None:
        """Clean up resources when the plugin is unloaded."""
        pass


class AnalyzerPlugin(Plugin):
    """Base class for analyzer plugins."""

    @abstractmethod
    async def analyze_file(self, file_path: Path, content: str,
                          context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze a file and return the results.

        Args:
            file_path: Path to the file to analyze
            content: File content
            context: Analysis context

        Returns:
            Analysis results
        """
        pass


class ReporterPlugin(Plugin):
    """Base class for reporter plugins."""

    @abstractmethod
    def generate_report(self, metrics: Any,
                       output_dir: Path) -> Dict[str, Path]:
        """
        Generate reports from the analysis results.

        Args:
            metrics: Analysis metrics
            output_dir: Directory to write reports to

        Returns:
            Dictionary mapping report names to file paths
        """
        pass


class VisualizerPlugin(Plugin):
    """Base class for visualizer plugins."""

    @abstractmethod
    def generate_visualization(self, data: Any,
                             output_path: Path) -> Path:
        """
        Generate a visualization from the analysis data.

        Args:
            data: Data to visualize
            output_path: Path to write the visualization to

        Returns:
            Path to the generated visualization
        """
        pass


class ToolPlugin(Plugin):
    """Base class for tool plugins."""

    @abstractmethod
    async def run_tool(self, file_path: Path, content: str,
                     config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Run the tool on a file.

        Args:
            file_path: Path to the file to analyze
            content: File content
            config: Tool configuration

        Returns:
            Tool results
        """
        pass

    @abstractmethod
    def parse_results(self, results: Dict[str, Any],
                     file_path: Path) -> Dict[str, Any]:
        """
        Parse the raw tool results into a standardized format.

        Args:
            results: Raw tool results
            file_path: Path to the file that was analyzed

        Returns:
            Parsed results
        """
        pass


class UIPlugin(Plugin):
    """Base class for UI plugins."""

    @abstractmethod
    def register_views(self) -> Dict[str, Any]:
        """
        Register custom views with the UI system.

        Returns:
            Dictionary mapping view names to view implementations
        """
        pass

    @abstractmethod
    def handle_event(self, event_name: str,
                   event_data: Dict[str, Any]) -> Optional[Any]:
        """
        Handle a UI event.

        Args:
            event_name: Name of the event
            event_data: Event data

        Returns:
            Optional response data
        """
        pass
