"""
Vibe Check Plugin System
==============

This module provides a plugin system for Vibe Check, enabling users to extend
the functionality of the tool with custom analyzers, runners, parsers,
and integrations.

Plugins follow a consistent interface and can be dynamically loaded
at runtime based on configuration.
"""


__all__ = [
    "PluginManager",
    "plugin_hook",
    "Plugin",
    "AnalyzerPlugin",
    "ReporterPlugin",
    "VisualizerPlugin",
    "list_plugins",
    "load_plugin",
    "register_plugin"
]
