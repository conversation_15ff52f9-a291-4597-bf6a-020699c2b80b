"""
Plugin Interface Module
==================

This module defines the interfaces for Vibe Check plugins.
These interfaces provide a consistent way to define and use plugins.
"""

import logging
from pathlib import Path

logger = logging.getLogger("vibe_check_plugin_interface")


@runtime_checkable
class Plugin(Protocol):
    """Protocol for all plugins."""
    
    @property
    def name(self) -> str:
        """
        Get the name of the plugin.
        
        Returns:
            Plugin name
        """
        ...
        
    @property
    def version(self) -> str:
        """
        Get the version of the plugin.
        
        Returns:
            Plugin version
        """
        ...
        
    @property
    def description(self) -> str:
        """
        Get the description of the plugin.
        
        Returns:
            Plugin description
        """
        ...
        
    @property
    def enabled(self) -> bool:
        """
        Get whether the plugin is enabled.
        
        Returns:
            True if the plugin is enabled, False otherwise
        """
        ...
        
    @enabled.setter
    def enabled(self, value: bool) -> None:
        """
        Set whether the plugin is enabled.
        
        Args:
            value: True to enable the plugin, False to disable it
        """
        ...
        
    @property
    def dependencies(self) -> List[str]:
        """
        Get the list of plugin dependencies.
        
        Returns:
            List of plugin names that this plugin depends on
        """
        ...
        
    def initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the plugin with the given configuration.
        
        Args:
            config: Plugin configuration
        """
        ...
        
    def register_tools(self) -> List[str]:
        """
        Register tools provided by this plugin.
        
        Returns:
            List of tool names
        """
        ...
        
    def shutdown(self) -> None:
        """Clean up resources when the plugin is unloaded."""
        ...


@runtime_checkable
class ToolPlugin(Plugin, Protocol):
    """Protocol for tool plugins."""
    
    @abstractmethod
    async def run_tool(self, 
                      file_path: Path, 
                      content: str,
                      config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Run the tool on a file.
        
        Args:
            file_path: Path to the file to analyze
            content: File content
            config: Tool configuration
            
        Returns:
            Tool results
        """
        ...
        
    @abstractmethod
    def parse_results(self, 
                     results: Dict[str, Any],
                     file_path: Path) -> Dict[str, Any]:
        """
        Parse the raw tool results into a standardized format.
        
        Args:
            results: Raw tool results
            file_path: Path to the file that was analyzed
            
        Returns:
            Parsed results
        """
        ...


@runtime_checkable
class ReportPlugin(Plugin, Protocol):
    """Protocol for report plugins."""
    
    @abstractmethod
    def generate_report(self, 
                       metrics: Any,
                       output_dir: Path) -> Dict[str, Path]:
        """
        Generate reports from the analysis results.
        
        Args:
            metrics: Analysis metrics
            output_dir: Directory to write reports to
            
        Returns:
            Dictionary mapping report names to file paths
        """
        ...


@runtime_checkable
class HookPlugin(Plugin, Protocol):
    """Protocol for hook plugins."""
    
    @abstractmethod
    def register_hooks(self) -> Dict[str, Any]:
        """
        Register hooks with the plugin system.
        
        Returns:
            Dictionary mapping hook names to hook implementations
        """
        ...
        
    @abstractmethod
    def execute_hook(self, 
                    hook_name: str,
                    *args: Any, 
                    **kwargs: Any) -> Any:
        """
        Execute a hook.
        
        Args:
            hook_name: Name of the hook to execute
            *args: Positional arguments to pass to the hook
            **kwargs: Keyword arguments to pass to the hook
            
        Returns:
            Hook result
        """
        ...
