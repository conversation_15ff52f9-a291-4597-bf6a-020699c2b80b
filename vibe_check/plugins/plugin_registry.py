"""
Plugin Registry Module
=================

This module provides the PluginRegistry class, which is responsible for
registering and managing plugins. It provides a more robust and extensible
way to work with plugins than the existing PluginManager.
"""

import logging
from collections import defaultdict

from .plugin_base import Plugin
from typing import Any, Callable, Dict, List, Optional, Set, TypeVar

logger = logging.getLogger("vibe_check_plugin_registry")

# Type variables
T = TypeVar('T', bound=Plugin)
HookFunc = Callable[..., Any]


class PluginRegistry:
    """
    Registry for plugins.
    
    This class provides a centralized registry for plugins, allowing
    for plugin discovery, registration, and management.
    """
    
    def __init__(self) -> None:
        """Initialize the plugin registry."""
        # Map of plugin ID to plugin instance
        self.plugins: Dict[str, Plugin] = {}
        
        # Map of plugin type to list of plugin IDs
        self.plugin_types: Dict[str, List[str]] = defaultdict(list)
        
        # Map of hook name to list of hook functions
        self.hooks: Dict[str, List[HookFunc]] = defaultdict(list)
        
        # Map of plugin ID to list of hook names
        self.plugin_hooks: Dict[str, List[str]] = defaultdict(list)
        
        # Set of enabled plugin IDs
        self.enabled_plugins: Set[str] = set()
        
    def register_plugin(self, plugin: Plugin) -> None:
        """
        Register a plugin.
        
        Args:
            plugin: Plugin instance
        """
        plugin_id = plugin.name
        
        # Check if plugin is already registered
        if plugin_id in self.plugins:
            logger.warning(f"Plugin {plugin_id} already registered, ignoring duplicate")
            return
            
        # Register the plugin
        self.plugins[plugin_id] = plugin
        
        # Get plugin type
        plugin_type = self._get_plugin_type(plugin)
        self.plugin_types[plugin_type].append(plugin_id)
        
        # Enable the plugin by default
        if plugin.enabled:
            self.enabled_plugins.add(plugin_id)
            
        logger.info(f"Registered plugin {plugin_id} (type: {plugin_type})")
        
    def register_hook(self, 
                     hook_name: str, 
                     hook_func: HookFunc, 
                     plugin_id: Optional[str] = None) -> None:
        """
        Register a hook function.
        
        Args:
            hook_name: Name of the hook
            hook_func: Hook function
            plugin_id: Optional ID of the plugin that owns the hook
        """
        self.hooks[hook_name].append(hook_func)
        
        if plugin_id:
            self.plugin_hooks[plugin_id].append(hook_name)
            
        logger.debug(f"Registered hook {hook_name}")
        
    def get_plugin(self, plugin_id: str) -> Optional[Plugin]:
        """
        Get a plugin by ID.
        
        Args:
            plugin_id: Plugin ID
            
        Returns:
            Plugin instance or None if not found
        """
        return self.plugins.get(plugin_id)
        
    def get_plugins_by_type(self, plugin_type: str) -> List[Plugin]:
        """
        Get all plugins of a given type.
        
        Args:
            plugin_type: Plugin type
            
        Returns:
            List of plugin instances
        """
        plugin_ids = self.plugin_types.get(plugin_type, [])
        return [self.plugins[pid] for pid in plugin_ids if pid in self.plugins]
        
    def get_typed_plugins(self, plugin_class: Type[T]) -> List[T]:
        """
        Get all plugins of a given class type.
        
        Args:
            plugin_class: Plugin class
            
        Returns:
            List of plugin instances
        """
        return [
            plugin for plugin in self.plugins.values()
            if isinstance(plugin, plugin_class) and plugin.name in self.enabled_plugins
        ]
        
    def get_hooks(self, hook_name: str) -> List[HookFunc]:
        """
        Get all hook functions for a given hook name.
        
        Args:
            hook_name: Hook name
            
        Returns:
            List of hook functions
        """
        return self.hooks.get(hook_name, [])
        
    def execute_hook(self, hook_name: str, *args: Any, **kwargs: Any) -> List[Any]:
        """
        Execute all hook functions for a given hook name.
        
        Args:
            hook_name: Hook name
            *args: Positional arguments to pass to hook functions
            **kwargs: Keyword arguments to pass to hook functions
            
        Returns:
            List of hook function results
        """
        results = []
        
        for hook_func in self.get_hooks(hook_name):
            try:
                result = hook_func(*args, **kwargs)
                results.append(result)
            except Exception as e:
                logger.error(f"Error executing hook {hook_name}: {e}")
                
        return results
        
    def enable_plugin(self, plugin_id: str) -> bool:
        """
        Enable a plugin.
        
        Args:
            plugin_id: Plugin ID
            
        Returns:
            True if the plugin was enabled, False otherwise
        """
        plugin = self.get_plugin(plugin_id)
        if not plugin:
            logger.warning(f"Cannot enable plugin {plugin_id}: not found")
            return False
            
        plugin.enabled = True
        self.enabled_plugins.add(plugin_id)
        logger.info(f"Enabled plugin {plugin_id}")
        return True
        
    def disable_plugin(self, plugin_id: str) -> bool:
        """
        Disable a plugin.
        
        Args:
            plugin_id: Plugin ID
            
        Returns:
            True if the plugin was disabled, False otherwise
        """
        plugin = self.get_plugin(plugin_id)
        if not plugin:
            logger.warning(f"Cannot disable plugin {plugin_id}: not found")
            return False
            
        plugin.enabled = False
        if plugin_id in self.enabled_plugins:
            self.enabled_plugins.remove(plugin_id)
        logger.info(f"Disabled plugin {plugin_id}")
        return True
        
    def is_plugin_enabled(self, plugin_id: str) -> bool:
        """
        Check if a plugin is enabled.
        
        Args:
            plugin_id: Plugin ID
            
        Returns:
            True if the plugin is enabled, False otherwise
        """
        return plugin_id in self.enabled_plugins
        
    def _get_plugin_type(self, plugin: Plugin) -> str:
        """
        Get the type of a plugin.
        
        Args:
            plugin: Plugin instance
            
        Returns:
            Plugin type name
        """
        # Check plugin class hierarchy
        for cls in plugin.__class__.__mro__:
            if cls.__name__ == "Plugin":
                continue
            if issubclass(cls, Plugin):
                return cls.__name__.replace("Plugin", "").lower()
                
        return "unknown"


# Global plugin registry instance
_plugin_registry = None


def get_plugin_registry() -> PluginRegistry:
    """
    Get the global plugin registry instance.
    
    Returns:
        PluginRegistry instance
    """
    global _plugin_registry
    if _plugin_registry is None:
        _plugin_registry = PluginRegistry()
    return _plugin_registry
