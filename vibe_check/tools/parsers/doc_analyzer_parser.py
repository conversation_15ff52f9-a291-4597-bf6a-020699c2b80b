"""
Documentation Analyzer Parser
=========================

This module provides a parser for documentation analyzer output.
"""

import logging

from .base_parser import ToolParser
from .parser_registry import register_parser
from typing import Any, Dict, Optional

logger = logging.getLogger("pat_doc_analyzer_parser")


class DocAnalyzerParser(ToolParser):
    """Parser for documentation analyzer output."""
    
    def __init__(self, name: str = "doc_analyzer", config: Optional[Dict[str, Any]] = None):
        """
        Initialize the documentation analyzer parser.
        
        Args:
            name: Tool name
            config: Parser configuration
        """
        super().__init__(name, config)
    
    def parse(self, output: Dict[str, Any], file_path: str) -> Dict[str, Any]:
        """
        Parse the documentation analyzer output into a standardized format.
        
        Args:
            output: Raw documentation analyzer output
            file_path: Path to the file that was analyzed
            
        Returns:
            Parsed documentation analyzer output in a standardized format
        """
        # Check for errors in the output
        if "error" in output:
            logger.warning(f"Error in documentation analyzer output: {output['error']}")
            return {
                "error": output["error"],
                "documentation_metrics": {},
                "doc_coverage": 0,
                "doc_quality": 0,
                "missing_docs": []
            }
        
        # Extract documentation scores
        doc_coverage = output.get("documentation_coverage", 0)
        doc_quality = output.get("documentation_quality", 0) * 100  # Convert to percentage
        
        # Extract summary metrics
        summary = output.get("summary", {})
        
        # Extract missing docstrings
        missing_docstrings = output.get("missing_docstrings", [])
        
        # Process functions data
        functions = output.get("functions", {})
        function_metrics = []
        
        for func_id, func_data in functions.items():
            quality_score = func_data.get("quality_score", 0)
            quality_percent = round(quality_score * 100, 1)
            
            # Assign rating based on quality
            if quality_score >= 0.8:
                rating = "excellent"
                severity = "LOW"
            elif quality_score >= 0.5:
                rating = "good"
                severity = "LOW"
            elif quality_score > 0:
                rating = "basic"
                severity = "MEDIUM"
            else:
                rating = "missing"
                severity = "HIGH"
            
            function_metrics.append({
                "name": func_id,
                "has_docstring": func_data.get("has_docstring", False),
                "quality_score": quality_percent,
                "rating": rating,
                "severity": severity,
                "is_public": func_data.get("is_public", True),
                "line": func_data.get("line", 0)
            })
        
        # Process classes data
        classes = output.get("classes", {})
        class_metrics = []
        
        for class_name, class_data in classes.items():
            quality_score = class_data.get("quality_score", 0)
            quality_percent = round(quality_score * 100, 1)
            
            # Assign rating based on quality
            if quality_score >= 0.8:
                rating = "excellent"
                severity = "LOW"
            elif quality_score >= 0.5:
                rating = "good"
                severity = "LOW"
            elif quality_score > 0:
                rating = "basic"
                severity = "MEDIUM"
            else:
                rating = "missing"
                severity = "HIGH"
            
            class_metrics.append({
                "name": class_name,
                "has_docstring": class_data.get("has_docstring", False),
                "quality_score": quality_percent,
                "rating": rating,
                "severity": severity,
                "is_public": class_data.get("is_public", True),
                "line": class_data.get("line", 0)
            })
        
        # Process module docstring
        module_info = list(output.get("modules", {}).values())[0] if output.get("modules") else {}
        module_quality = module_info.get("quality_score", 0) if module_info else 0
        module_quality_percent = round(module_quality * 100, 1)
        
        # Create issues for missing or poor documentation
        issues = []
        
        # Check module docstring
        if module_info and not module_info.get("has_docstring", False):
            issues.append({
                "tool": "doc_analyzer",
                "code": "missing_module_docstring",
                "name": "Missing Module Docstring",
                "message": "Module is missing a docstring",
                "location": {
                    "file": file_path,
                    "line": 1,
                    "column": 0
                },
                "severity": "MEDIUM",
                "type": "documentation"
            })
        
        # Check class docstrings
        for cls in class_metrics:
            if not cls["has_docstring"] and cls["is_public"]:
                issues.append({
                    "tool": "doc_analyzer",
                    "code": "missing_class_docstring",
                    "name": "Missing Class Docstring",
                    "message": f"Class {cls['name']} is missing a docstring",
                    "location": {
                        "file": file_path,
                        "line": cls["line"],
                        "column": 0
                    },
                    "severity": cls["severity"],
                    "type": "documentation"
                })
        
        # Check function docstrings
        for func in function_metrics:
            if not func["has_docstring"] and func["is_public"]:
                issues.append({
                    "tool": "doc_analyzer",
                    "code": "missing_function_docstring",
                    "name": "Missing Function Docstring",
                    "message": f"Function {func['name']} is missing a docstring",
                    "location": {
                        "file": file_path,
                        "line": func["line"],
                        "column": 0
                    },
                    "severity": func["severity"],
                    "type": "documentation"
                })
        
        # Create a rating for documentation coverage
        if doc_coverage >= 80:
            coverage_rating = "excellent"
            coverage_severity = "LOW"
        elif doc_coverage >= 60:
            coverage_rating = "good"
            coverage_severity = "LOW"
        elif doc_coverage >= 30:
            coverage_rating = "basic"
            coverage_severity = "MEDIUM"
        else:
            coverage_rating = "poor"
            coverage_severity = "HIGH"
        
        # Create a rating for documentation quality
        if doc_quality >= 80:
            quality_rating = "excellent"
            quality_severity = "LOW"
        elif doc_quality >= 60:
            quality_rating = "good"
            quality_severity = "LOW"
        elif doc_quality >= 30:
            quality_rating = "basic"
            quality_severity = "MEDIUM"
        else:
            quality_rating = "poor"
            quality_severity = "HIGH"
        
        # Calculate a combined documentation score (coverage and quality)
        combined_score = (doc_coverage + doc_quality) / 2
        
        # Create detailed documentation metrics
        documentation_metrics = {
            "coverage": doc_coverage,
            "coverage_rating": coverage_rating,
            "coverage_severity": coverage_severity,
            "quality": doc_quality,
            "quality_rating": quality_rating,
            "quality_severity": quality_severity,
            "combined_score": combined_score,
            "module_quality": module_quality_percent,
            "public_items": summary.get("public_items", 0),
            "documented_items": summary.get("documented_items", 0),
            "total_lines": summary.get("total_lines", 0),
            "code_lines": summary.get("code_lines", 0),
            "comment_lines": summary.get("comment_lines", 0),
            "class_count": summary.get("class_count", 0),
            "function_count": summary.get("function_count", 0),
            "missing_docstrings_count": summary.get("missing_docstrings_count", 0),
        }
        
        # Create item distributions
        documentation_metrics["doc_distribution"] = {
            "module": 1 if module_info and module_info.get("has_docstring", False) else 0,
            "classes": sum(1 for c in class_metrics if c["has_docstring"] and c["is_public"]),
            "functions": sum(1 for f in function_metrics if f["has_docstring"] and f["is_public"]),
            "total_public": summary.get("public_items", 0)
        }
        
        return {
            "issues": issues,
            "documentation_metrics": documentation_metrics,
            "doc_coverage": doc_coverage,
            "doc_quality": doc_quality,
            "module_metrics": {
                "has_docstring": module_info.get("has_docstring", False) if module_info else False,
                "quality": module_quality_percent
            },
            "class_metrics": class_metrics,
            "function_metrics": function_metrics,
            "missing_docs": missing_docstrings
        }


# Register this parser
register_parser("doc_analyzer", DocAnalyzerParser)
