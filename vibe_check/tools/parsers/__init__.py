"""
Vibe Check Tool Parsers
=============

This module provides parsers for various analysis tool outputs.
Parsers are responsible for processing the raw output from tools and
converting it into standardized data structures.

Tool parsers follow a common interface, allowing them to be used interchangeably
and dynamically loaded based on configuration.
"""

# Note: MarkdownParser is not available in this version

__all__ = [
    "ToolParser",
    "register_parser",
    "get_parser",
    "RuffParser",
    "MypyParser",
    "BanditParser",
    "ComplexityParser",
    "PylintParser",
    "PyflakesParser",
    "CustomRulesParser",
]
