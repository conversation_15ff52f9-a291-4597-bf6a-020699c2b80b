"""
Custom Rules Runner
===============

This module provides a runner for custom rule checks.
"""

import logging
from pathlib import Path
from typing import Dict, Any, Union, Optional

from .base_runner import Tool<PERSON>unner
from .tool_registry import register_tool
from ..custom_rules.python_rules import PythonRule<PERSON>hecker

logger = logging.getLogger("vibe_check_custom_rules_runner")


class CustomRulesRunner(ToolRunner):
    """Runner for custom rule checks."""
    
    def __init__(self, name: str = "custom_rules", config: Optional[Dict[str, Any]] = None):
        """
        Initialize the custom rules runner.
        
        Args:
            name: Tool name
            config: Tool configuration
        """
        super().__init__(name, config)
        
        # Default config
        if self.config is None:
            self.config = {}
        
        # Initialize rule checkers
        self.python_checker = PythonRuleChecker()
    
    async def run(self, file_path: Union[str, Path], content: str) -> Dict[str, Any]:
        """
        Run custom rules on the file content.
        
        Args:
            file_path: Path to the file being analyzed
            content: File content as a string
            
        Returns:
            Dictionary with analysis results
        """
        try:
            # Determine file type
            path_obj = Path(file_path)
            file_ext = path_obj.suffix.lower()
            
            # Run appropriate checker based on file type
            if file_ext in ['.py', '.pyx', '.pyi']:
                # Python files
                return self.python_checker.check_file(str(file_path), content)
            else:
                # Unsupported file type
                return {
                    "issues": [],
                    "summary": {
                        "total": 0,
                        "by_category": {},
                        "by_severity": {
                            "HIGH": 0,
                            "MEDIUM": 0,
                            "LOW": 0
                        }
                    }
                }
                
        except Exception as e:
            logger.error(f"Error running custom rules: {e}")
            return {
                "issues": [],
                "summary": {
                    "total": 0,
                    "by_category": {},
                    "by_severity": {
                        "HIGH": 0,
                        "MEDIUM": 0,
                        "LOW": 0
                    }
                },
                "error": str(e)
            }
    
    def is_available(self) -> bool:
        """
        Check if custom rules are available.
        
        Returns:
            Always True since custom rules are built-in
        """
        return True


# Register this tool runner
register_tool("custom_rules", CustomRulesRunner)
