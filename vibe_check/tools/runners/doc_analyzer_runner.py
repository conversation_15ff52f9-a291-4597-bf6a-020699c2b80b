"""
Documentation Analyzer Runner
=========================

This module provides a runner for documentation quality analysis.

The documentation analyzer evaluates docstrings and comments to
assess documentation coverage and quality.
"""

import ast
import logging
import re
from pathlib import Path
from typing import Optional, Dict, Any, List, Union

from .base_runner import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .tool_registry import register_tool

logger = logging.getLogger("pat_doc_analyzer_runner")


class DocStringVisitor(ast.NodeVisitor):
    """AST visitor that analyzes docstrings."""
    
    def __init__(self):
        """Initialize the visitor."""
        self.docs = {}
        self.functions = {}
        self.classes = {}
        self.modules = {}
        self.current_class = None
        self.public_items = 0
        self.documented_items = 0
    
    def visit_Module(self, node):
        """
        Visit a module.
        
        Args:
            node: Module node
        """
        # Check for module docstring
        docstring = ast.get_docstring(node)
        self.modules["module"] = {
            "has_docstring": docstring is not None,
            "docstring": docstring,
            "quality_score": self._assess_docstring_quality(docstring) if docstring else 0,
            "line": 1
        }
        
        # If the module has a docstring, count it as documented
        if docstring:
            self.documented_items += 1
        
        # Count the module itself
        self.public_items += 1
        
        # Visit all children
        self.generic_visit(node)
    
    def visit_ClassDef(self, node):
        """
        Visit a class definition.
        
        Args:
            node: Class definition node
        """
        old_class = self.current_class
        self.current_class = node.name
        
        # Check for class docstring
        docstring = ast.get_docstring(node)
        
        # Check if class is public (not starting with underscore)
        is_public = not node.name.startswith('_')
        if is_public:
            self.public_items += 1
            if docstring:
                self.documented_items += 1
        
        # Record class info
        self.classes[node.name] = {
            "has_docstring": docstring is not None,
            "docstring": docstring,
            "quality_score": self._assess_docstring_quality(docstring) if docstring else 0,
            "line": node.lineno,
            "is_public": is_public
        }
        
        # Visit class body
        for child in node.body:
            if not isinstance(child, (ast.FunctionDef, ast.AsyncFunctionDef, ast.ClassDef)):
                continue
            self.visit(child)
        
        # Restore previous class context
        self.current_class = old_class
    
    def visit_FunctionDef(self, node):
        """
        Visit a function definition.
        
        Args:
            node: Function definition node
        """
        # Check for function docstring
        docstring = ast.get_docstring(node)
        
        # Check if function is a method
        is_method = self.current_class is not None
        
        # Check if function is public (not starting with underscore)
        is_public = not node.name.startswith('_') or node.name == '__init__'
        if is_public:
            self.public_items += 1
            if docstring:
                self.documented_items += 1
        
        # Record function info
        func_id = f"{self.current_class}.{node.name}" if is_method else node.name
        self.functions[func_id] = {
            "has_docstring": docstring is not None,
            "docstring": docstring,
            "quality_score": self._assess_docstring_quality(docstring) if docstring else 0,
            "line": node.lineno,
            "is_method": is_method,
            "is_public": is_public,
            "class_name": self.current_class,
            "arg_count": len(node.args.args) - 1 if is_method else len(node.args.args)
        }
    
    def visit_AsyncFunctionDef(self, node):
        """
        Visit an async function definition.
        
        Args:
            node: Async function definition node
        """
        self.visit_FunctionDef(node)
    
    def _assess_docstring_quality(self, docstring: Optional[str]) -> float:
        """
        Assess the quality of a docstring.
        
        Args:
            docstring: The docstring to assess
            
        Returns:
            Quality score between 0.0 and 1.0
        """
        if not docstring:
            return 0.0
        
        # Remove indentation
        docstring = '\n'.join(line.strip() for line in docstring.split('\n'))
        
        # Basic metrics
        score = 0.0
        
        # Length contributes to score (up to a point)
        length = len(docstring)
        if length > 10:
            score += 0.2
        if length > 40:
            score += 0.2
        
        # Check for parameter documentation
        has_params = bool(re.search(r'(Args|Parameters):', docstring))
        if has_params:
            score += 0.2
        
        # Check for return value documentation
        has_returns = bool(re.search(r'(Returns|Yields):', docstring))
        if has_returns:
            score += 0.2
        
        # Check for examples
        has_examples = bool(re.search(r'Example', docstring))
        if has_examples:
            score += 0.1
        
        # Check for proper sentences (starts with capital, ends with period)
        has_proper_sentences = bool(re.search(r'[A-Z].*\.', docstring))
        if has_proper_sentences:
            score += 0.1
        
        return min(1.0, score)


class DocAnalyzerRunner(ToolRunner):
    """Runner for documentation analysis."""
    
    def __init__(self, name: str = "doc_analyzer", config: Dict[str, Any] = None):
        """
        Initialize the documentation analyzer runner.
        
        Args:
            name: Tool name
            config: Tool configuration
        """
        super().__init__(name, config)
    
    async def run(self, file_path: Union[str, Path], content: str) -> Dict[str, Any]:
        """
        Analyze documentation quality.
        
        Args:
            file_path: Path to the file being analyzed
            content: File content as a string
            
        Returns:
            Dictionary with analysis results
        """
        try:
            # Parse the code into an AST
            tree = ast.parse(content)
            
            # Visit the tree with our docstring visitor
            visitor = DocStringVisitor()
            visitor.visit(tree)
            
            # Extract line counts
            total_lines = len(content.splitlines())
            code_lines = sum(1 for line in content.splitlines() if line.strip() and not line.strip().startswith('#'))
            comment_lines = sum(1 for line in content.splitlines() if line.strip().startswith('#'))
            
            # Calculate documentation coverage for public items
            doc_coverage = round((visitor.documented_items / max(1, visitor.public_items)) * 100, 2)
            
            # Calculate docstring quality average
            quality_scores = []
            
            # Check module docstring quality
            for module_info in visitor.modules.values():
                if module_info["has_docstring"]:
                    quality_scores.append(module_info["quality_score"])
            
            # Check class docstring quality
            for class_info in visitor.classes.values():
                if class_info["has_docstring"]:
                    quality_scores.append(class_info["quality_score"])
            
            # Check function docstring quality
            for func_info in visitor.functions.values():
                if func_info["has_docstring"]:
                    quality_scores.append(func_info["quality_score"])
            
            # Calculate average quality score
            avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0.0
            
            # Generate missing docstrings list
            missing_docstrings = []
            
            # Check module docstring
            module_info = visitor.modules.get("module", {})
            if not module_info.get("has_docstring", False):
                missing_docstrings.append({
                    "type": "module",
                    "name": str(file_path),
                    "line": 1
                })
            
            # Check class docstrings
            for class_name, class_info in visitor.classes.items():
                if class_info["is_public"] and not class_info["has_docstring"]:
                    missing_docstrings.append({
                        "type": "class",
                        "name": class_name,
                        "line": class_info["line"]
                    })
            
            # Check function docstrings
            for func_id, func_info in visitor.functions.items():
                if func_info["is_public"] and not func_info["has_docstring"]:
                    missing_docstrings.append({
                        "type": "function" if not func_info["is_method"] else "method",
                        "name": func_id,
                        "line": func_info["line"]
                    })
            
            # Generate documentation summary
            summary = {
                "file_path": str(file_path),
                "total_lines": total_lines,
                "code_lines": code_lines,
                "comment_lines": comment_lines,
                "public_items": visitor.public_items,
                "documented_items": visitor.documented_items,
                "documentation_coverage": doc_coverage,
                "average_quality": avg_quality,
                "class_count": len(visitor.classes),
                "function_count": len(visitor.functions),
                "missing_docstrings_count": len(missing_docstrings)
            }
            
            return {
                "summary": summary,
                "modules": visitor.modules,
                "classes": visitor.classes,
                "functions": visitor.functions,
                "missing_docstrings": missing_docstrings,
                "documentation_coverage": doc_coverage,
                "documentation_quality": avg_quality
            }
            
        except SyntaxError as e:
            logger.error(f"Syntax error in {file_path}: {e}")
            return {
                "error": f"Syntax error: {e}",
                "summary": {
                    "file_path": str(file_path),
                    "error": f"Syntax error: {e}",
                    "documentation_coverage": 0.0,
                    "documentation_quality": 0.0
                },
                "documentation_coverage": 0.0,
                "documentation_quality": 0.0
            }
        except Exception as e:
            logger.error(f"Error analyzing documentation of {file_path}: {e}")
            return {
                "error": str(e),
                "summary": {
                    "file_path": str(file_path),
                    "error": str(e),
                    "documentation_coverage": 0.0,
                    "documentation_quality": 0.0
                },
                "documentation_coverage": 0.0,
                "documentation_quality": 0.0
            }
    
    def is_available(self) -> bool:
        """
        Check if documentation analysis is available.
        
        Returns:
            True (documentation analysis is always available as it uses built-in modules)
        """
        return True


# Register this tool runner
register_tool("doc_analyzer", DocAnalyzerRunner)
