"""
Vibe Check Tool Runners
=============

This module provides runner implementations for various analysis tools.
Runners are responsible for executing tools and capturing their output.

Tool runners follow a common interface, allowing them to be used interchangeably
and dynamically loaded based on configuration.
"""

# Note: MarkdownRunner is not available in this version

from .tool_registry import get_runner_for_tool, register_tool
from .base_runner import ToolRunner

# Import specific runners
try:
    from .ruff_runner import Ruff<PERSON>unner
except ImportError:
    RuffRunner = None

try:
    from .mypy_runner import Mypy<PERSON>unner
except ImportError:
    MypyRunner = None

try:
    from .bandit_runner import BanditRunner
except ImportError:
    BanditRunner = None

try:
    from .complexity_runner import ComplexityRunner
except ImportError:
    ComplexityRunner = None

try:
    from .pylint_runner import PylintRunner
except ImportError:
    PylintRunner = None

try:
    from .pyflakes_runner import PyflakesRunner
except ImportError:
    PyflakesRunner = None

try:
    from .custom_rules_runner import CustomRulesRunner
except ImportError:
    CustomRulesRunner = None

# Alias for backward compatibility
get_tool_runner = get_runner_for_tool

__all__ = [
    "ToolRunner",
    "register_tool",
    "get_runner_for_tool",
    "get_tool_runner",  # Alias for backward compatibility
    "RuffRunner",
    "MypyRunner",
    "BanditRunner",
    "ComplexityRunner",
    "PylintRunner",
    "PyflakesRunner",
    "CustomRulesRunner",
]
