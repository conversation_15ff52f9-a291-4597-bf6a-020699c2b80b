"""
Bandit Tool Runner
==============

This module provides a runner for the Bandit security analyzer.

Bandit is used to find common security issues in Python code.
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Union

from .base_runner import Tool<PERSON>unner
from .tool_registry import register_tool, check_command_availability

logger = logging.getLogger("pat_bandit_runner")


class BanditRunner(ToolRunner):
    """Runner for the Bandit security analyzer."""
    
    def __init__(self, name: str = "bandit", config: Dict[str, Any] = None):
        """
        Initialize the Bandit runner.
        
        Args:
            name: Tool name
            config: Tool configuration
        """
        super().__init__(name, config)
    
    async def run(self, file_path: Union[str, Path], content: str) -> Dict[str, Any]:
        """
        Run Bandit on the file content.
        
        Args:
            file_path: Path to the file being analyzed
            content: File content as a string
            
        Returns:
            Dictionary with analysis results
        """
        try:
            # Create a temporary file with the content
            temp_path = self.create_temp_file(content, suffix='.py')
            
            try:
                # Build the command
                cmd = ["bandit", "-f", "json", temp_path]
                
                # Add any additional arguments from config
                cmd.extend(self.get_config_args())
                
                # Run the command
                process_result = await self.run_process(cmd)
                
                                # Parse the output
                # Note: Bandit returns non-zero when issues are found, which is expected
                if process_result["returncode"] == 0 or process_result["stdout"].strip() == "":
                    # No issues found
                    return {
                        "issues": [],
                        "summary": {
                            "high": 0,
                            "medium": 0,
                            "low": 0,
                            "total": 0
                        }
                    }
                else:
                    try:
                        # Parse JSON output
                        data = json.loads(process_result["stdout"])
                        issues = []
                        
                        # Extract issues
                        if "results" in data:
                            for result in data["results"]:
                                issues.append({
                                    "code": result.get("test_id", ""),
                                    "name": result.get("test_name", ""),
                                    "line": result.get("line_number", 0),
                                    "message": result.get("issue_text", ""),
                                    "severity": result.get("issue_severity", "MEDIUM"),
                                    "confidence": result.get("issue_confidence", "MEDIUM"),
                                    "more_info": result.get("more_info", "")
                                })
                        
                        # Extract summary
                        metrics = data.get("metrics", {})
                        summary = {
                            "high": metrics.get("SEVERITY.HIGH", 0),
                            "medium": metrics.get("SEVERITY.MEDIUM", 0),
                            "low": metrics.get("SEVERITY.LOW", 0),
                            "total": len(issues)
                        }
                        
                        return {
                            "issues": issues,
                            "summary": summary
                        }
                    except json.JSONDecodeError:
                        # Fall back to text parsing if JSON parsing fails
                        issues = []
                        lines = process_result["stdout"].splitlines()
                        
                        for line in lines:
                            if "] [" in line and ":" in line:
                                # Sample line: "[B301:blacklist_calls] [CWE-78] [MEDIUM] Use of shell=True..."
                                parts = line.strip().split("] [")
                                if len(parts) >= 3:
                                    code = parts[0].lstrip("[")
                                    severity = parts[2].split("]")[0]
                                    message = line.split("] ", 3)[-1]
                                    
                                    issues.append({
                                        "code": code,
                                        "message": message,
                                        "severity": severity,
                                        "line": 0,  # Line number not easily extractable
                                        "confidence": "MEDIUM"
                                    })
                        
                        # Create basic summary
                        summary = {
                            "high": sum(1 for i in issues if i["severity"] == "HIGH"),
                            "medium": sum(1 for i in issues if i["severity"] == "MEDIUM"),
                            "low": sum(1 for i in issues if i["severity"] == "LOW"),
                            "total": len(issues)
                        }
                        
                        return {
                            "issues": issues,
                            "summary": summary
                        }
            finally:
                # Clean up the temporary file
                self.cleanup_temp_file(temp_path)
                
        except Exception as e:
            logger.error(f"Error running Bandit: {e}")
            return {
                "issues": [],
                "summary": {
                    "high": 0,
                    "medium": 0,
                    "low": 0,
                    "total": 0
                },
                "error": str(e)
            }
    
    def is_available(self) -> bool:
        """
        Check if Bandit is available on the system.
        
        Returns:
            True if Bandit is available, False otherwise
        """
        return check_command_availability("bandit")


# Register this tool runner
register_tool("bandit", BanditRunner)
