"""
File: vibe_check/enterprise/collaboration.py
Purpose: Team collaboration features for enterprise
Related Files: vibe_check/enterprise/
Dependencies: typing, datetime, pathlib
"""

import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field
from enum import Enum
import uuid

from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class UserRole(Enum):
    """User roles in team collaboration."""
    ADMIN = "admin"
    LEAD = "lead"
    DEVELOPER = "developer"
    VIEWER = "viewer"


class TeamPermission(Enum):
    """Team permissions."""
    READ_REPORTS = "read_reports"
    WRITE_REPORTS = "write_reports"
    MANAGE_CONFIG = "manage_config"
    MANAGE_MEMBERS = "manage_members"
    DELETE_TEAM = "delete_team"


@dataclass
class TeamMember:
    """Team member information."""
    user_id: str
    username: str
    email: str
    role: UserR<PERSON>
    joined_at: datetime
    permissions: Set[TeamPermission] = field(default_factory=set)
    active: bool = True

    def __post_init__(self):
        # Set default permissions based on role
        if not self.permissions:
            self.permissions = self._get_default_permissions()

    def _get_default_permissions(self) -> Set[TeamPermission]:
        """Get default permissions for role."""
        if self.role == UserRole.ADMIN:
            return set(TeamPermission)
        elif self.role == UserRole.LEAD:
            return {
                TeamPermission.READ_REPORTS,
                TeamPermission.WRITE_REPORTS,
                TeamPermission.MANAGE_CONFIG,
                TeamPermission.MANAGE_MEMBERS
            }
        elif self.role == UserRole.DEVELOPER:
            return {
                TeamPermission.READ_REPORTS,
                TeamPermission.WRITE_REPORTS
            }
        else:  # VIEWER
            return {TeamPermission.READ_REPORTS}

    def has_permission(self, permission: TeamPermission) -> bool:
        """Check if member has specific permission."""
        return permission in self.permissions

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "user_id": self.user_id,
            "username": self.username,
            "email": self.email,
            "role": self.role.value,
            "joined_at": self.joined_at.isoformat(),
            "permissions": [p.value for p in self.permissions],
            "active": self.active
        }


@dataclass
class Team:
    """Team information and configuration."""
    team_id: str
    name: str
    description: str
    created_at: datetime
    created_by: str
    members: List[TeamMember] = field(default_factory=list)
    shared_configs: Dict[str, str] = field(default_factory=dict)
    team_settings: Dict[str, Any] = field(default_factory=dict)
    active: bool = True

    def add_member(self, member: TeamMember) -> bool:
        """Add a member to the team."""
        # Check if user already exists
        for existing_member in self.members:
            if existing_member.user_id == member.user_id:
                logger.warning(f"User {member.user_id} already in team {self.name}")
                return False

        self.members.append(member)
        logger.info(f"Added {member.username} to team {self.name}")
        return True

    def remove_member(self, user_id: str) -> bool:
        """Remove a member from the team."""
        for i, member in enumerate(self.members):
            if member.user_id == user_id:
                removed_member = self.members.pop(i)
                logger.info(f"Removed {removed_member.username} from team {self.name}")
                return True
        return False

    def get_member(self, user_id: str) -> Optional[TeamMember]:
        """Get a team member by user ID."""
        for member in self.members:
            if member.user_id == user_id:
                return member
        return None

    def update_member_role(self, user_id: str, new_role: UserRole) -> bool:
        """Update a member's role."""
        member = self.get_member(user_id)
        if member:
            old_role = member.role
            member.role = new_role
            member.permissions = member._get_default_permissions()
            logger.info(f"Updated {member.username} role from {old_role.value} to {new_role.value}")
            return True
        return False

    def get_members_by_role(self, role: UserRole) -> List[TeamMember]:
        """Get all members with a specific role."""
        return [member for member in self.members if member.role == role]

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "team_id": self.team_id,
            "name": self.name,
            "description": self.description,
            "created_at": self.created_at.isoformat(),
            "created_by": self.created_by,
            "members": [member.to_dict() for member in self.members],
            "shared_configs": self.shared_configs,
            "team_settings": self.team_settings,
            "active": self.active
        }


class TeamManager:
    """Manages team collaboration features."""

    def __init__(self, data_dir: Optional[Path] = None):
        """
        Initialize team manager.

        Args:
            data_dir: Directory for storing team data
        """
        self.data_dir = data_dir or Path.cwd() / "team_data"
        self.data_dir.mkdir(parents=True, exist_ok=True)

        self.teams: Dict[str, Team] = {}
        self.users: Dict[str, Dict[str, Any]] = {}
        self._initialized = False

    async def initialize(self) -> None:
        """Initialize team manager."""
        if self._initialized:
            return

        # Load existing teams and users
        await self._load_teams()
        await self._load_users()

        self._initialized = True
        logger.info(f"Team manager initialized with {len(self.teams)} teams and {len(self.users)} users")

    async def _load_teams(self) -> None:
        """Load teams from storage."""
        teams_file = self.data_dir / "teams.json"
        if teams_file.exists():
            try:
                with open(teams_file, 'r', encoding='utf-8') as f:
                    teams_data = json.load(f)

                for team_data in teams_data:
                    team = self._team_from_dict(team_data)
                    self.teams[team.team_id] = team

                logger.debug(f"Loaded {len(self.teams)} teams from storage")

            except Exception as e:
                logger.error(f"Failed to load teams: {e}")

    async def _load_users(self) -> None:
        """Load users from storage."""
        users_file = self.data_dir / "users.json"
        if users_file.exists():
            try:
                with open(users_file, 'r', encoding='utf-8') as f:
                    self.users = json.load(f)

                logger.debug(f"Loaded {len(self.users)} users from storage")

            except Exception as e:
                logger.error(f"Failed to load users: {e}")

    async def _save_teams(self) -> None:
        """Save teams to storage."""
        teams_file = self.data_dir / "teams.json"
        try:
            teams_data = [team.to_dict() for team in self.teams.values()]
            with open(teams_file, 'w', encoding='utf-8') as f:
                json.dump(teams_data, f, indent=2)

            logger.debug("Saved teams to storage")

        except Exception as e:
            logger.error(f"Failed to save teams: {e}")

    async def _save_users(self) -> None:
        """Save users to storage."""
        users_file = self.data_dir / "users.json"
        try:
            with open(users_file, 'w', encoding='utf-8') as f:
                json.dump(self.users, f, indent=2)

            logger.debug("Saved users to storage")

        except Exception as e:
            logger.error(f"Failed to save users: {e}")

    def _team_from_dict(self, data: Dict[str, Any]) -> Team:
        """Create Team object from dictionary."""
        members = []
        for member_data in data.get("members", []):
            member = TeamMember(
                user_id=member_data["user_id"],
                username=member_data["username"],
                email=member_data["email"],
                role=UserRole(member_data["role"]),
                joined_at=datetime.fromisoformat(member_data["joined_at"]),
                permissions={TeamPermission(p) for p in member_data.get("permissions", [])},
                active=member_data.get("active", True)
            )
            members.append(member)

        return Team(
            team_id=data["team_id"],
            name=data["name"],
            description=data["description"],
            created_at=datetime.fromisoformat(data["created_at"]),
            created_by=data["created_by"],
            members=members,
            shared_configs=data.get("shared_configs", {}),
            team_settings=data.get("team_settings", {}),
            active=data.get("active", True)
        )

    async def create_team(
        self,
        name: str,
        description: str,
        created_by: str,
        initial_members: Optional[List[Dict[str, Any]]] = None
    ) -> Team:
        """
        Create a new team.

        Args:
            name: Team name
            description: Team description
            created_by: User ID of team creator
            initial_members: Optional list of initial members

        Returns:
            Created team
        """
        # Check if team name already exists
        for team in self.teams.values():
            if team.name.lower() == name.lower():
                raise ValueError(f"Team with name '{name}' already exists")

        team_id = str(uuid.uuid4())
        team = Team(
            team_id=team_id,
            name=name,
            description=description,
            created_at=datetime.now(),
            created_by=created_by
        )

        # Add creator as admin
        creator_member = TeamMember(
            user_id=created_by,
            username=self.users.get(created_by, {}).get("username", created_by),
            email=self.users.get(created_by, {}).get("email", f"{created_by}@example.com"),
            role=UserRole.ADMIN,
            joined_at=datetime.now()
        )
        team.add_member(creator_member)

        # Add initial members if provided
        if initial_members:
            for member_data in initial_members:
                member = TeamMember(
                    user_id=member_data["user_id"],
                    username=member_data.get("username", member_data["user_id"]),
                    email=member_data.get("email", f"{member_data['user_id']}@example.com"),
                    role=UserRole(member_data.get("role", "developer")),
                    joined_at=datetime.now()
                )
                team.add_member(member)

        self.teams[team_id] = team
        await self._save_teams()

        logger.info(f"Created team '{name}' with ID {team_id}")
        return team

    async def get_team(self, team_id: str) -> Optional[Team]:
        """Get a team by ID."""
        return self.teams.get(team_id)

    async def get_team_by_name(self, name: str) -> Optional[Team]:
        """Get a team by name."""
        for team in self.teams.values():
            if team.name.lower() == name.lower():
                return team
        return None

    async def list_teams(self, user_id: Optional[str] = None) -> List[Team]:
        """
        List teams, optionally filtered by user membership.

        Args:
            user_id: Optional user ID to filter teams

        Returns:
            List of teams
        """
        if user_id:
            return [
                team for team in self.teams.values()
                if any(member.user_id == user_id for member in team.members)
            ]
        return list(self.teams.values())

    async def add_team_member(
        self,
        team_id: str,
        user_id: str,
        username: str,
        email: str,
        role: UserRole = UserRole.DEVELOPER,
        added_by: Optional[str] = None
    ) -> bool:
        """
        Add a member to a team.

        Args:
            team_id: Team ID
            user_id: User ID to add
            username: Username
            email: User email
            role: User role in team
            added_by: User ID who is adding the member

        Returns:
            True if successful, False otherwise
        """
        team = self.teams.get(team_id)
        if not team:
            logger.error(f"Team {team_id} not found")
            return False

        # Check permissions if added_by is provided
        if added_by:
            adding_member = team.get_member(added_by)
            if not adding_member or not adding_member.has_permission(TeamPermission.MANAGE_MEMBERS):
                logger.error(f"User {added_by} does not have permission to add members")
                return False

        member = TeamMember(
            user_id=user_id,
            username=username,
            email=email,
            role=role,
            joined_at=datetime.now()
        )

        success = team.add_member(member)
        if success:
            await self._save_teams()

        return success

    async def remove_team_member(
        self,
        team_id: str,
        user_id: str,
        removed_by: Optional[str] = None
    ) -> bool:
        """
        Remove a member from a team.

        Args:
            team_id: Team ID
            user_id: User ID to remove
            removed_by: User ID who is removing the member

        Returns:
            True if successful, False otherwise
        """
        team = self.teams.get(team_id)
        if not team:
            logger.error(f"Team {team_id} not found")
            return False

        # Check permissions if removed_by is provided
        if removed_by:
            removing_member = team.get_member(removed_by)
            if not removing_member or not removing_member.has_permission(TeamPermission.MANAGE_MEMBERS):
                logger.error(f"User {removed_by} does not have permission to remove members")
                return False

        success = team.remove_member(user_id)
        if success:
            await self._save_teams()

        return success

    async def update_member_role(
        self,
        team_id: str,
        user_id: str,
        new_role: UserRole,
        updated_by: Optional[str] = None
    ) -> bool:
        """
        Update a team member's role.

        Args:
            team_id: Team ID
            user_id: User ID to update
            new_role: New role
            updated_by: User ID who is updating the role

        Returns:
            True if successful, False otherwise
        """
        team = self.teams.get(team_id)
        if not team:
            logger.error(f"Team {team_id} not found")
            return False

        # Check permissions if updated_by is provided
        if updated_by:
            updating_member = team.get_member(updated_by)
            if not updating_member or not updating_member.has_permission(TeamPermission.MANAGE_MEMBERS):
                logger.error(f"User {updated_by} does not have permission to update roles")
                return False

        success = team.update_member_role(user_id, new_role)
        if success:
            await self._save_teams()

        return success

    async def delete_team(self, team_id: str, deleted_by: Optional[str] = None) -> bool:
        """
        Delete a team.

        Args:
            team_id: Team ID to delete
            deleted_by: User ID who is deleting the team

        Returns:
            True if successful, False otherwise
        """
        team = self.teams.get(team_id)
        if not team:
            logger.error(f"Team {team_id} not found")
            return False

        # Check permissions if deleted_by is provided
        if deleted_by:
            deleting_member = team.get_member(deleted_by)
            if not deleting_member or not deleting_member.has_permission(TeamPermission.DELETE_TEAM):
                logger.error(f"User {deleted_by} does not have permission to delete team")
                return False

        del self.teams[team_id]
        await self._save_teams()

        logger.info(f"Deleted team '{team.name}' (ID: {team_id})")
        return True

    def get_team_statistics(self) -> Dict[str, Any]:
        """Get team statistics."""
        total_members = sum(len(team.members) for team in self.teams.values())
        active_teams = sum(1 for team in self.teams.values() if team.active)

        role_distribution = {}
        for team in self.teams.values():
            for member in team.members:
                role = member.role.value
                role_distribution[role] = role_distribution.get(role, 0) + 1

        return {
            "total_teams": len(self.teams),
            "active_teams": active_teams,
            "total_members": total_members,
            "average_team_size": total_members / len(self.teams) if self.teams else 0,
            "role_distribution": role_distribution,
            "teams": [
                {
                    "team_id": team.team_id,
                    "name": team.name,
                    "member_count": len(team.members),
                    "active": team.active
                }
                for team in self.teams.values()
            ]
        }

    async def cleanup(self) -> None:
        """Cleanup team manager resources."""
        await self._save_teams()
        await self._save_users()
        logger.info("Team manager cleaned up")


class SharedConfiguration:
    """Manages shared configurations across teams."""

    def __init__(self, data_dir: Optional[Path] = None):
        """
        Initialize shared configuration manager.

        Args:
            data_dir: Directory for storing configuration data
        """
        self.data_dir = data_dir or Path.cwd() / "shared_configs"
        self.data_dir.mkdir(parents=True, exist_ok=True)

        self.configurations: Dict[str, Dict[str, Any]] = {}
        self.config_permissions: Dict[str, Dict[str, Set[str]]] = {}
        self._initialized = False

    async def initialize(self) -> None:
        """Initialize shared configuration manager."""
        if self._initialized:
            return

        await self._load_configurations()

        self._initialized = True
        logger.info(f"Shared configuration manager initialized with {len(self.configurations)} configs")

    async def _load_configurations(self) -> None:
        """Load configurations from storage."""
        configs_file = self.data_dir / "configurations.json"
        permissions_file = self.data_dir / "permissions.json"

        if configs_file.exists():
            try:
                with open(configs_file, 'r', encoding='utf-8') as f:
                    self.configurations = json.load(f)
                logger.debug(f"Loaded {len(self.configurations)} configurations")
            except Exception as e:
                logger.error(f"Failed to load configurations: {e}")

        if permissions_file.exists():
            try:
                with open(permissions_file, 'r', encoding='utf-8') as f:
                    permissions_data = json.load(f)
                    # Convert lists back to sets
                    self.config_permissions = {
                        config_name: {
                            perm_type: set(users)
                            for perm_type, users in perms.items()
                        }
                        for config_name, perms in permissions_data.items()
                    }
                logger.debug(f"Loaded permissions for {len(self.config_permissions)} configurations")
            except Exception as e:
                logger.error(f"Failed to load configuration permissions: {e}")

    async def _save_configurations(self) -> None:
        """Save configurations to storage."""
        configs_file = self.data_dir / "configurations.json"
        permissions_file = self.data_dir / "permissions.json"

        try:
            with open(configs_file, 'w', encoding='utf-8') as f:
                json.dump(self.configurations, f, indent=2)

            # Convert sets to lists for JSON serialization
            permissions_data = {
                config_name: {
                    perm_type: list(users)
                    for perm_type, users in perms.items()
                }
                for config_name, perms in self.config_permissions.items()
            }

            with open(permissions_file, 'w', encoding='utf-8') as f:
                json.dump(permissions_data, f, indent=2)

            logger.debug("Saved configurations and permissions")

        except Exception as e:
            logger.error(f"Failed to save configurations: {e}")

    async def save_configuration(
        self,
        config_name: str,
        config: Dict[str, Any],
        created_by: str,
        team_id: Optional[str] = None,
        description: Optional[str] = None
    ) -> bool:
        """
        Save a shared configuration.

        Args:
            config_name: Configuration name
            config: Configuration data
            created_by: User ID who created the configuration
            team_id: Optional team ID for team-specific configs
            description: Optional description

        Returns:
            True if successful, False otherwise
        """
        config_data = {
            "name": config_name,
            "config": config,
            "created_by": created_by,
            "created_at": datetime.now().isoformat(),
            "team_id": team_id,
            "description": description or "",
            "version": 1,
            "active": True
        }

        self.configurations[config_name] = config_data

        # Set default permissions - creator has full access
        self.config_permissions[config_name] = {
            "read": {created_by},
            "write": {created_by},
            "admin": {created_by}
        }

        await self._save_configurations()

        logger.info(f"Saved configuration '{config_name}' by {created_by}")
        return True

    async def get_configuration(
        self,
        config_name: str,
        requested_by: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Get a shared configuration.

        Args:
            config_name: Configuration name
            requested_by: User ID requesting the configuration

        Returns:
            Configuration data if accessible, None otherwise
        """
        if config_name not in self.configurations:
            return None

        # Check read permissions if requested_by is provided
        if requested_by:
            permissions = self.config_permissions.get(config_name, {})
            read_users = permissions.get("read", set())
            if requested_by not in read_users:
                logger.warning(f"User {requested_by} does not have read access to config '{config_name}'")
                return None

        return self.configurations[config_name]

    async def update_configuration(
        self,
        config_name: str,
        config: Dict[str, Any],
        updated_by: str
    ) -> bool:
        """
        Update a shared configuration.

        Args:
            config_name: Configuration name
            config: Updated configuration data
            updated_by: User ID updating the configuration

        Returns:
            True if successful, False otherwise
        """
        if config_name not in self.configurations:
            logger.error(f"Configuration '{config_name}' not found")
            return False

        # Check write permissions
        permissions = self.config_permissions.get(config_name, {})
        write_users = permissions.get("write", set())
        if updated_by not in write_users:
            logger.error(f"User {updated_by} does not have write access to config '{config_name}'")
            return False

        config_data = self.configurations[config_name]
        config_data["config"] = config
        config_data["updated_by"] = updated_by
        config_data["updated_at"] = datetime.now().isoformat()
        config_data["version"] += 1

        await self._save_configurations()

        logger.info(f"Updated configuration '{config_name}' by {updated_by}")
        return True

    async def delete_configuration(
        self,
        config_name: str,
        deleted_by: str
    ) -> bool:
        """
        Delete a shared configuration.

        Args:
            config_name: Configuration name
            deleted_by: User ID deleting the configuration

        Returns:
            True if successful, False otherwise
        """
        if config_name not in self.configurations:
            logger.error(f"Configuration '{config_name}' not found")
            return False

        # Check admin permissions
        permissions = self.config_permissions.get(config_name, {})
        admin_users = permissions.get("admin", set())
        if deleted_by not in admin_users:
            logger.error(f"User {deleted_by} does not have admin access to config '{config_name}'")
            return False

        del self.configurations[config_name]
        del self.config_permissions[config_name]

        await self._save_configurations()

        logger.info(f"Deleted configuration '{config_name}' by {deleted_by}")
        return True

    async def grant_access(
        self,
        config_name: str,
        user_id: str,
        permission_type: str,
        granted_by: str
    ) -> bool:
        """
        Grant access to a configuration.

        Args:
            config_name: Configuration name
            user_id: User ID to grant access to
            permission_type: Permission type (read, write, admin)
            granted_by: User ID granting the access

        Returns:
            True if successful, False otherwise
        """
        if config_name not in self.configurations:
            logger.error(f"Configuration '{config_name}' not found")
            return False

        # Check admin permissions
        permissions = self.config_permissions.get(config_name, {})
        admin_users = permissions.get("admin", set())
        if granted_by not in admin_users:
            logger.error(f"User {granted_by} does not have admin access to config '{config_name}'")
            return False

        if permission_type not in ["read", "write", "admin"]:
            logger.error(f"Invalid permission type: {permission_type}")
            return False

        permissions.setdefault(permission_type, set()).add(user_id)

        await self._save_configurations()

        logger.info(f"Granted {permission_type} access to '{config_name}' for user {user_id}")
        return True

    def list_configurations(
        self,
        user_id: Optional[str] = None,
        team_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        List configurations accessible to a user or team.

        Args:
            user_id: Optional user ID to filter by access
            team_id: Optional team ID to filter by

        Returns:
            List of accessible configurations
        """
        configs = []

        for config_name, config_data in self.configurations.items():
            # Filter by team if specified
            if team_id and config_data.get("team_id") != team_id:
                continue

            # Check user access if specified
            if user_id:
                permissions = self.config_permissions.get(config_name, {})
                has_access = any(
                    user_id in users
                    for users in permissions.values()
                )
                if not has_access:
                    continue

            configs.append({
                "name": config_name,
                "description": config_data.get("description", ""),
                "created_by": config_data.get("created_by"),
                "created_at": config_data.get("created_at"),
                "team_id": config_data.get("team_id"),
                "version": config_data.get("version", 1),
                "active": config_data.get("active", True)
            })

        return configs

    def get_configuration_statistics(self) -> Dict[str, Any]:
        """Get configuration statistics."""
        team_configs = sum(1 for config in self.configurations.values() if config.get("team_id"))
        global_configs = len(self.configurations) - team_configs

        return {
            "total_configurations": len(self.configurations),
            "team_configurations": team_configs,
            "global_configurations": global_configs,
            "total_permissions": len(self.config_permissions)
        }

    async def cleanup(self) -> None:
        """Cleanup shared configuration manager."""
        await self._save_configurations()
        logger.info("Shared configuration manager cleaned up")
