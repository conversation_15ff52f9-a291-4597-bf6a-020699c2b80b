"""
File: vibe_check/enterprise/cicd/jenkins.py
Purpose: Jenkins integration for CI/CD workflows
Related Files: vibe_check/enterprise/cicd/
Dependencies: typing, xml, pathlib
"""

import xml.etree.ElementTree as ET
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime

from .models import (
    CICDPlatform, PipelineConfiguration, PipelineExecution,
    PipelineStep, QualityGate, WebhookPayload, PipelineStatus
)
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class JenkinsIntegration:
    """Jenkins integration for Vibe Check."""
    
    def __init__(self):
        self.platform = CICDPlatform.JENKINS
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize Jenkins integration."""
        if self._initialized:
            return
        
        self._initialized = True
        logger.info("Jenkins integration initialized")
    
    def generate_jenkinsfile(
        self,
        config: PipelineConfiguration,
        output_path: Optional[Path] = None
    ) -> str:
        """
        Generate Jenkins pipeline (Jenkinsfile).
        
        Args:
            config: Pipeline configuration
            output_path: Optional path to save the Jenkinsfile
            
        Returns:
            Generated Jenkinsfile content
        """
        jenkinsfile = self._generate_pipeline_script(config)
        
        # Save to file if path provided
        if output_path:
            output_path.parent.mkdir(parents=True, exist_ok=True)
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(jenkinsfile)
            logger.info(f"Generated Jenkinsfile: {output_path}")
        
        return jenkinsfile
    
    def _generate_pipeline_script(self, config: PipelineConfiguration) -> str:
        """Generate Jenkins pipeline script."""
        triggers = self._generate_triggers(config.trigger_events)
        environment = self._generate_environment(config.environment_variables)
        stages = self._generate_stages(config)
        post_actions = self._generate_post_actions(config)
        
        jenkinsfile = f"""
pipeline {{
    agent any
    
    {triggers}
    
    {environment}
    
    options {{
        buildDiscarder(logRotator(numToKeepStr: '10'))
        timeout(time: 30, unit: 'MINUTES')
        skipStagesAfterUnstable()
        parallelsAlwaysFailFast()
    }}
    
    stages {{
{stages}
    }}
    
    {post_actions}
}}
        """.strip()
        
        return jenkinsfile
    
    def _generate_triggers(self, trigger_events: List[str]) -> str:
        """Generate pipeline triggers."""
        triggers = []
        
        for event in trigger_events:
            if event == "push":
                triggers.append("pollSCM('H/5 * * * *')")
            elif event == "schedule":
                triggers.append("cron('H 2 * * 1')")  # Weekly on Monday 2 AM
        
        if triggers:
            return f"triggers {{\n        {chr(10).join(triggers)}\n    }}"
        return ""
    
    def _generate_environment(self, env_vars: Dict[str, str]) -> str:
        """Generate environment variables."""
        env_lines = [
            "VIBE_CHECK_OUTPUT_DIR = 'vibe-check-reports'",
            "PYTHONPATH = '${WORKSPACE}'"
        ]
        
        for key, value in env_vars.items():
            env_lines.append(f"{key} = '{value}'")
        
        return f"environment {{\n        {chr(10).join(env_lines)}\n    }}"
    
    def _generate_stages(self, config: PipelineConfiguration) -> str:
        """Generate pipeline stages."""
        stages = []
        
        # Preparation stage
        stages.append(self._generate_prepare_stage(config))
        
        # Custom stages from configuration
        for step in config.steps:
            stages.append(self._generate_custom_stage(step))
        
        # Analysis stage
        stages.append(self._generate_analysis_stage(config))
        
        # Quality gates stage
        if config.quality_gates:
            stages.append(self._generate_quality_gates_stage(config))
        
        # Report stage
        if config.analysis_config.get("generate_report", True):
            stages.append(self._generate_report_stage(config))
        
        return "\n".join(stages)
    
    def _generate_prepare_stage(self, config: PipelineConfiguration) -> str:
        """Generate preparation stage."""
        return """
        stage('Prepare') {
            steps {
                echo 'Preparing Vibe Check analysis environment...'
                sh '''
                    python3 --version
                    pip3 install --upgrade pip
                    pip3 install vibe-check
                    mkdir -p ${VIBE_CHECK_OUTPUT_DIR}
                '''
            }
        }"""
    
    def _generate_custom_stage(self, step: PipelineStep) -> str:
        """Generate custom stage from configuration."""
        return f"""
        stage('{step.name}') {{
            steps {{
                echo 'Running {step.name}...'
                sh '{step.command}'
            }}
        }}"""
    
    def _generate_analysis_stage(self, config: PipelineConfiguration) -> str:
        """Generate analysis stage."""
        analysis_cmd = self._generate_analysis_command(config.analysis_config)
        
        return f"""
        stage('Vibe Check Analysis') {{
            steps {{
                echo 'Running comprehensive Vibe Check analysis...'
                sh '''
                    {analysis_cmd}
                '''
            }}
            post {{
                always {{
                    publishHTML([
                        allowMissing: false,
                        alwaysLinkToLastBuild: true,
                        keepAll: true,
                        reportDir: '${{VIBE_CHECK_OUTPUT_DIR}}',
                        reportFiles: 'report.html',
                        reportName: 'Vibe Check Report'
                    ])
                    
                    junit testResults: '${{VIBE_CHECK_OUTPUT_DIR}}/junit.xml', allowEmptyResults: true
                    
                    archiveArtifacts artifacts: '${{VIBE_CHECK_OUTPUT_DIR}}/**/*', allowEmptyArchive: true
                }}
            }}
        }}"""
    
    def _generate_quality_gates_stage(self, config: PipelineConfiguration) -> str:
        """Generate quality gates stage."""
        return """
        stage('Quality Gates') {
            steps {
                echo 'Evaluating quality gates...'
                sh '''
                    vibe-check-quality-gates \\
                        --config quality-gates.json \\
                        --input ${VIBE_CHECK_OUTPUT_DIR} \\
                        --fail-on-error
                '''
            }
        }"""
    
    def _generate_report_stage(self, config: PipelineConfiguration) -> str:
        """Generate report generation stage."""
        return """
        stage('Generate Reports') {
            steps {
                echo 'Generating comprehensive analysis reports...'
                sh '''
                    vibe-check-report \\
                        --input ${VIBE_CHECK_OUTPUT_DIR} \\
                        --format html,json,pdf \\
                        --include-trends \\
                        --include-recommendations
                '''
            }
            post {
                always {
                    publishHTML([
                        allowMissing: false,
                        alwaysLinkToLastBuild: true,
                        keepAll: true,
                        reportDir: '${VIBE_CHECK_OUTPUT_DIR}',
                        reportFiles: '*.html',
                        reportName: 'Comprehensive Analysis Report'
                    ])
                }
            }
        }"""
    
    def _generate_post_actions(self, config: PipelineConfiguration) -> str:
        """Generate post-build actions."""
        return """
    post {
        always {
            echo 'Cleaning up workspace...'
            cleanWs()
        }
        success {
            echo 'Pipeline completed successfully!'
            emailext (
                subject: "✅ Vibe Check Analysis - SUCCESS: ${env.JOB_NAME} - ${env.BUILD_NUMBER}",
                body: "The Vibe Check analysis completed successfully. View the report at: ${env.BUILD_URL}Vibe_Check_Report/",
                to: "${env.CHANGE_AUTHOR_EMAIL ?: env.DEFAULT_RECIPIENTS}"
            )
        }
        failure {
            echo 'Pipeline failed!'
            emailext (
                subject: "❌ Vibe Check Analysis - FAILURE: ${env.JOB_NAME} - ${env.BUILD_NUMBER}",
                body: "The Vibe Check analysis failed. Check the console output at: ${env.BUILD_URL}console",
                to: "${env.CHANGE_AUTHOR_EMAIL ?: env.DEFAULT_RECIPIENTS}"
            )
        }
        unstable {
            echo 'Pipeline is unstable!'
            emailext (
                subject: "⚠️ Vibe Check Analysis - UNSTABLE: ${env.JOB_NAME} - ${env.BUILD_NUMBER}",
                body: "The Vibe Check analysis completed with warnings. Review the report at: ${env.BUILD_URL}Vibe_Check_Report/",
                to: "${env.CHANGE_AUTHOR_EMAIL ?: env.DEFAULT_RECIPIENTS}"
            )
        }
    }"""
    
    def _generate_analysis_command(self, analysis_config: Dict[str, Any]) -> str:
        """Generate Vibe Check analysis command."""
        cmd_parts = ["vibe-check-standalone", "."]
        
        # Add configuration options
        if "enabled_categories" in analysis_config:
            categories = ",".join(analysis_config["enabled_categories"])
            cmd_parts.extend(["--categories", categories])
        
        if "severity_threshold" in analysis_config:
            cmd_parts.extend(["--severity", analysis_config["severity_threshold"]])
        
        if analysis_config.get("generate_report", True):
            cmd_parts.extend([
                "--format", "html",
                "--output", "${VIBE_CHECK_OUTPUT_DIR}/report.html",
                "--junit-output", "${VIBE_CHECK_OUTPUT_DIR}/junit.xml",
                "--json-output", "${VIBE_CHECK_OUTPUT_DIR}/results.json"
            ])
        
        if analysis_config.get("fail_on_error", True):
            cmd_parts.append("--fail-on-error")
        
        return " \\\n                    ".join(cmd_parts)
    
    def generate_job_config_xml(
        self,
        config: PipelineConfiguration,
        repository_url: str
    ) -> str:
        """
        Generate Jenkins job configuration XML.
        
        Args:
            config: Pipeline configuration
            repository_url: Git repository URL
            
        Returns:
            Jenkins job configuration XML
        """
        root = ET.Element("flow-definition", plugin="workflow-job@2.40")
        
        # Add description
        description = ET.SubElement(root, "description")
        description.text = f"Vibe Check analysis pipeline for {config.name}"
        
        # Add properties
        properties = ET.SubElement(root, "properties")
        
        # Add SCM configuration
        definition = ET.SubElement(root, "definition", {"class": "org.jenkinsci.plugins.workflow.cps.CpsScmFlowDefinition", "plugin": "workflow-cps@2.80"})
        
        scm = ET.SubElement(definition, "scm", {"class": "hudson.plugins.git.GitSCM", "plugin": "git@4.4.4"})
        config_version = ET.SubElement(scm, "configVersion")
        config_version.text = "2"
        
        user_remote_configs = ET.SubElement(scm, "userRemoteConfigs")
        user_remote_config = ET.SubElement(user_remote_configs, "hudson.plugins.git.UserRemoteConfig")
        url = ET.SubElement(user_remote_config, "url")
        url.text = repository_url
        
        branches = ET.SubElement(scm, "branches")
        branch_spec = ET.SubElement(branches, "hudson.plugins.git.BranchSpec")
        name = ET.SubElement(branch_spec, "name")
        name.text = "*/main"
        
        script_path = ET.SubElement(definition, "scriptPath")
        script_path.text = "Jenkinsfile"
        
        lightweight = ET.SubElement(definition, "lightweight")
        lightweight.text = "true"
        
        return ET.tostring(root, encoding='unicode')
    
    async def parse_webhook(self, payload: Dict[str, Any]) -> Optional[WebhookPayload]:
        """
        Parse Jenkins webhook payload.
        
        Args:
            payload: Raw webhook payload
            
        Returns:
            Parsed webhook payload or None if invalid
        """
        try:
            # Jenkins webhook format varies, this is a basic implementation
            if "build" in payload:
                build = payload["build"]
                return WebhookPayload(
                    platform=self.platform,
                    event_type="build",
                    repository_url=build.get("scm", {}).get("url", ""),
                    branch=build.get("scm", {}).get("branch", "main"),
                    commit_sha=build.get("scm", {}).get("commit", ""),
                    commit_message=build.get("displayName", ""),
                    author=build.get("culprits", [{}])[0].get("fullName", "unknown"),
                    timestamp=datetime.fromtimestamp(build.get("timestamp", 0) / 1000),
                    raw_payload=payload
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to parse Jenkins webhook: {e}")
            return None
    
    async def get_build_status(
        self,
        job_name: str,
        build_number: str
    ) -> Optional[PipelineExecution]:
        """
        Get build execution status.
        
        Args:
            job_name: Jenkins job name
            build_number: Build number
            
        Returns:
            Pipeline execution status or None if not found
        """
        # This would integrate with Jenkins API
        # For now, return a mock execution
        return PipelineExecution(
            execution_id=build_number,
            config_id=job_name,
            platform=self.platform,
            repository_url=f"https://jenkins.example.com/job/{job_name}",
            branch="main",
            commit_sha="abc123",
            trigger_event="push",
            status=PipelineStatus.SUCCESS
        )
    
    async def cleanup(self) -> None:
        """Cleanup Jenkins integration."""
        logger.info("Jenkins integration cleaned up")
