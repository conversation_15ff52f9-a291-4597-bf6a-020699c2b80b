"""
File: vibe_check/enterprise/cicd/gitlab_ci.py
Purpose: GitLab CI integration for CI/CD workflows
Related Files: vibe_check/enterprise/cicd/
Dependencies: typing, yaml, pathlib
"""

import yaml
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime

    CICDPlatform, PipelineConfiguration, PipelineExecution, 
    PipelineStep, QualityGate, WebhookPayload, PipelineStatus
)
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class GitLabCIIntegration:
    """GitLab CI integration for Vibe Check."""
    
    def __init__(self):
        self.platform = CICDPlatform.GITLAB_CI
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize GitLab CI integration."""
        if self._initialized:
            return
        
        self._initialized = True
        logger.info("GitLab CI integration initialized")
    
    def generate_pipeline_file(
        self,
        config: PipelineConfiguration,
        output_path: Optional[Path] = None
    ) -> str:
        """
        Generate GitLab CI pipeline YAML file.
        
        Args:
            config: Pipeline configuration
            output_path: Optional path to save the pipeline file
            
        Returns:
            Generated pipeline YAML content
        """
        pipeline = {
            "stages": ["prepare", "analyze", "quality-gates", "report"],
            "variables": self._generate_variables(config),
            "before_script": [
                "python --version",
                "pip install --upgrade pip",
                "pip install vibe-check"
            ]
        }
        
        # Add jobs
        pipeline.update(self._generate_jobs(config))
        
        yaml_content = yaml.dump(pipeline, default_flow_style=False, sort_keys=False)
        
        # Save to file if path provided
        if output_path:
            output_path.parent.mkdir(parents=True, exist_ok=True)
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(yaml_content)
            logger.info(f"Generated GitLab CI pipeline: {output_path}")
        
        return yaml_content
    
    def _generate_variables(self, config: PipelineConfiguration) -> Dict[str, str]:
        """Generate pipeline variables."""
        variables = {
            "PIP_CACHE_DIR": "$CI_PROJECT_DIR/.cache/pip",
            "VIBE_CHECK_OUTPUT_DIR": "vibe-check-reports"
        }
        
        # Add custom environment variables
        variables.update(config.environment_variables)
        
        return variables
    
    def _generate_jobs(self, config: PipelineConfiguration) -> Dict[str, Any]:
        """Generate pipeline jobs."""
        jobs = {}
        
        # Prepare job
        jobs["prepare"] = {
            "stage": "prepare",
            "script": [
                "echo 'Preparing Vibe Check analysis...'",
                "mkdir -p $VIBE_CHECK_OUTPUT_DIR"
            ],
            "cache": {
                "paths": [".cache/pip/"]
            }
        }
        
        # Custom preparation steps
        for step in config.steps:
            if step.name.lower().startswith("prepare"):
                jobs["prepare"]["script"].append(step.command)
        
        # Analysis job
        analysis_script = [
            "echo 'Running Vibe Check analysis...'",
            self._generate_analysis_command(config.analysis_config)
        ]
        
        jobs["vibe-check-analysis"] = {
            "stage": "analyze",
            "script": analysis_script,
            "artifacts": {
                "reports": {
                    "junit": "$VIBE_CHECK_OUTPUT_DIR/junit.xml"
                },
                "paths": ["$VIBE_CHECK_OUTPUT_DIR/"],
                "expire_in": "1 week",
                "when": "always"
            },
            "rules": self._generate_rules(config.trigger_events)
        }
        
        # Quality gates job
        if config.quality_gates:
            jobs["quality-gates"] = {
                "stage": "quality-gates",
                "script": [
                    "echo 'Evaluating quality gates...'",
                    self._generate_quality_gate_command(config.quality_gates)
                ],
                "dependencies": ["vibe-check-analysis"],
                "rules": self._generate_rules(config.trigger_events)
            }
        
        # Report generation job
        if config.analysis_config.get("generate_report", True):
            jobs["generate-report"] = {
                "stage": "report",
                "script": [
                    "echo 'Generating comprehensive report...'",
                    "vibe-check-report --input $VIBE_CHECK_OUTPUT_DIR --format html",
                    "vibe-check-report --input $VIBE_CHECK_OUTPUT_DIR --format json"
                ],
                "artifacts": {
                    "paths": ["$VIBE_CHECK_OUTPUT_DIR/"],
                    "reports": {
                        "coverage_report": {
                            "coverage_format": "cobertura",
                            "path": "$VIBE_CHECK_OUTPUT_DIR/coverage.xml"
                        }
                    },
                    "expire_in": "30 days"
                },
                "dependencies": ["vibe-check-analysis"],
                "rules": self._generate_rules(config.trigger_events)
            }
        
        return jobs
    
    def _generate_rules(self, trigger_events: List[str]) -> List[Dict[str, Any]]:
        """Generate job rules based on trigger events."""
        rules = []
        
        for event in trigger_events:
            if event == "push":
                rules.append({
                    "if": "$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH",
                    "when": "always"
                })
            elif event == "pull_request":
                rules.append({
                    "if": "$CI_PIPELINE_SOURCE == 'merge_request_event'",
                    "when": "always"
                })
            elif event == "schedule":
                rules.append({
                    "if": "$CI_PIPELINE_SOURCE == 'schedule'",
                    "when": "always"
                })
        
        # Default rule
        if not rules:
            rules.append({"when": "manual"})
        
        return rules
    
    def _generate_analysis_command(self, analysis_config: Dict[str, Any]) -> str:
        """Generate Vibe Check analysis command."""
        cmd_parts = ["vibe-check-standalone", "."]
        
        # Add configuration options
        if "enabled_categories" in analysis_config:
            categories = ",".join(analysis_config["enabled_categories"])
            cmd_parts.extend(["--categories", categories])
        
        if "severity_threshold" in analysis_config:
            cmd_parts.extend(["--severity", analysis_config["severity_threshold"]])
        
        if analysis_config.get("generate_report", True):
            cmd_parts.extend([
                "--format", "html",
                "--output", "$VIBE_CHECK_OUTPUT_DIR/report.html",
                "--junit-output", "$VIBE_CHECK_OUTPUT_DIR/junit.xml"
            ])
        
        if analysis_config.get("fail_on_error", True):
            cmd_parts.append("--fail-on-error")
        
        return " ".join(cmd_parts)
    
    def _generate_quality_gate_command(self, quality_gates: List[QualityGate]) -> str:
        """Generate quality gate evaluation command."""
        return "vibe-check-quality-gates --config quality-gates.json --input $VIBE_CHECK_OUTPUT_DIR"
    
    async def parse_webhook(self, payload: Dict[str, Any]) -> Optional[WebhookPayload]:
        """
        Parse GitLab webhook payload.
        
        Args:
            payload: Raw webhook payload
            
        Returns:
            Parsed webhook payload or None if invalid
        """
        try:
            # Handle push events
            if payload.get("object_kind") == "push":
                return WebhookPayload(
                    platform=self.platform,
                    event_type="push",
                    repository_url=payload["project"]["git_http_url"],
                    branch=payload["ref"].replace("refs/heads/", ""),
                    commit_sha=payload["after"],
                    commit_message=payload["commits"][0]["message"] if payload["commits"] else "",
                    author=payload["user_name"],
                    timestamp=datetime.fromisoformat(payload["commits"][0]["timestamp"]) if payload["commits"] else datetime.now(),
                    raw_payload=payload
                )
            
            # Handle merge request events
            elif payload.get("object_kind") == "merge_request":
                mr = payload["object_attributes"]
                return WebhookPayload(
                    platform=self.platform,
                    event_type="merge_request",
                    repository_url=payload["project"]["git_http_url"],
                    branch=mr["source_branch"],
                    commit_sha=mr["last_commit"]["id"],
                    commit_message=mr["title"],
                    author=mr["author"]["name"],
                    timestamp=datetime.fromisoformat(mr["updated_at"]),
                    raw_payload=payload
                )
            
            # Handle pipeline events
            elif payload.get("object_kind") == "pipeline":
                pipeline = payload["object_attributes"]
                return WebhookPayload(
                    platform=self.platform,
                    event_type="pipeline",
                    repository_url=payload["project"]["git_http_url"],
                    branch=pipeline["ref"],
                    commit_sha=pipeline["sha"],
                    commit_message="Pipeline execution",
                    author=payload["user"]["name"],
                    timestamp=datetime.fromisoformat(pipeline["created_at"]),
                    raw_payload=payload
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to parse GitLab webhook: {e}")
            return None
    
    async def get_pipeline_status(
        self,
        project_id: str,
        pipeline_id: str
    ) -> Optional[PipelineExecution]:
        """
        Get pipeline execution status.
        
        Args:
            project_id: GitLab project ID
            pipeline_id: Pipeline ID
            
        Returns:
            Pipeline execution status or None if not found
        """
        # This would integrate with GitLab API
        # For now, return a mock execution
        return PipelineExecution(
            execution_id=pipeline_id,
            config_id=project_id,
            platform=self.platform,
            repository_url=f"https://gitlab.com/project/{project_id}",
            branch="main",
            commit_sha="abc123",
            trigger_event="push",
            status=PipelineStatus.SUCCESS
        )
    
    def generate_gitlab_ci_template(self) -> str:
        """Generate a comprehensive GitLab CI template."""
        template = """
# Vibe Check GitLab CI Template
# This template provides comprehensive code analysis using Vibe Check

stages:
  - prepare
  - analyze
  - quality-gates
  - report
  - deploy

variables:
  PIP_CACHE_DIR: "$CI_PROJECT_DIR/.cache/pip"
  VIBE_CHECK_OUTPUT_DIR: "vibe-check-reports"
  VIBE_CHECK_VERSION: "latest"

cache:
  paths:
    - .cache/pip/
    - venv/

before_script:
  - python --version
  - pip install --upgrade pip
  - pip install vibe-check==$VIBE_CHECK_VERSION

# Prepare environment
prepare:
  stage: prepare
  script:
    - echo "Preparing Vibe Check analysis environment..."
    - mkdir -p $VIBE_CHECK_OUTPUT_DIR
    - python -m venv venv
    - source venv/bin/activate
  artifacts:
    paths:
      - venv/
    expire_in: 1 hour

# Main analysis job
vibe-check-analysis:
  stage: analyze
  script:
    - echo "Running comprehensive Vibe Check analysis..."
    - source venv/bin/activate
    - vibe-check-standalone . 
        --categories style,security,complexity,docs,imports,types
        --severity warning
        --format html
        --output $VIBE_CHECK_OUTPUT_DIR/report.html
        --junit-output $VIBE_CHECK_OUTPUT_DIR/junit.xml
        --json-output $VIBE_CHECK_OUTPUT_DIR/results.json
  artifacts:
    reports:
      junit: $VIBE_CHECK_OUTPUT_DIR/junit.xml
    paths:
      - $VIBE_CHECK_OUTPUT_DIR/
    expire_in: 1 week
    when: always
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"

# Quality gate evaluation
quality-gates:
  stage: quality-gates
  script:
    - echo "Evaluating quality gates..."
    - source venv/bin/activate
    - vibe-check-quality-gates 
        --config quality-gates.json 
        --input $VIBE_CHECK_OUTPUT_DIR
        --fail-on-error
  dependencies:
    - vibe-check-analysis
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"

# Generate comprehensive report
generate-report:
  stage: report
  script:
    - echo "Generating comprehensive analysis report..."
    - source venv/bin/activate
    - vibe-check-report 
        --input $VIBE_CHECK_OUTPUT_DIR 
        --format html,json,pdf
        --include-trends
        --include-recommendations
  artifacts:
    paths:
      - $VIBE_CHECK_OUTPUT_DIR/
    reports:
      coverage_report:
        coverage_format: cobertura
        path: $VIBE_CHECK_OUTPUT_DIR/coverage.xml
    expire_in: 30 days
  dependencies:
    - vibe-check-analysis
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"

# Deploy reports to GitLab Pages (optional)
pages:
  stage: deploy
  script:
    - mkdir public
    - cp -r $VIBE_CHECK_OUTPUT_DIR/* public/
  artifacts:
    paths:
      - public
  dependencies:
    - generate-report
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  when: manual
        """.strip()
        
        return template
    
    async def cleanup(self) -> None:
        """Cleanup GitLab CI integration."""
        logger.info("GitLab CI integration cleaned up")
