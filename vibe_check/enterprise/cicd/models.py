"""
File: vibe_check/enterprise/cicd/models.py
Purpose: Data models for CI/CD integration
Related Files: vibe_check/enterprise/cicd/
Dependencies: typing, datetime, enum, dataclasses
"""

from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum


class CICDPlatform(Enum):
    """Supported CI/CD platforms."""
    GITHUB_ACTIONS = "github_actions"
    GITLAB_CI = "gitlab_ci"
    JENKINS = "jenkins"
    AZURE_DEVOPS = "azure_devops"
    BITBUCKET_PIPELINES = "bitbucket_pipelines"
    CIRCLECI = "circleci"


class PipelineStatus(Enum):
    """Pipeline execution status."""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILURE = "failure"
    CANCELLED = "cancelled"
    SKIPPED = "skipped"


class BuildResult(Enum):
    """Build result status."""
    PASSED = "passed"
    FAILED = "failed"
    UNSTABLE = "unstable"
    ABORTED = "aborted"


class QualityGateStatus(Enum):
    """Quality gate status."""
    PASSED = "passed"
    FAILED = "failed"
    WARNING = "warning"
    ERROR = "error"


@dataclass
class QualityGate:
    """Quality gate configuration and results."""
    gate_id: str
    name: str
    description: str
    conditions: Dict[str, Any]
    status: QualityGateStatus = QualityGateStatus.PASSED
    results: Dict[str, Any] = field(default_factory=dict)
    evaluated_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "gate_id": self.gate_id,
            "name": self.name,
            "description": self.description,
            "conditions": self.conditions,
            "status": self.status.value,
            "results": self.results,
            "evaluated_at": self.evaluated_at.isoformat() if self.evaluated_at else None
        }


@dataclass
class IntegrationConfig:
    """CI/CD integration configuration."""
    platform: CICDPlatform
    config_id: str
    name: str
    repository_url: str
    branch: str = "main"
    webhook_url: Optional[str] = None
    api_token: Optional[str] = None
    credentials: Dict[str, str] = field(default_factory=dict)
    settings: Dict[str, Any] = field(default_factory=dict)
    active: bool = True
    created_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "platform": self.platform.value,
            "config_id": self.config_id,
            "name": self.name,
            "repository_url": self.repository_url,
            "branch": self.branch,
            "webhook_url": self.webhook_url,
            "api_token": "***" if self.api_token else None,
            "credentials": {k: "***" for k in self.credentials.keys()},
            "settings": self.settings,
            "active": self.active,
            "created_at": self.created_at.isoformat()
        }


@dataclass
class PipelineStep:
    """Individual pipeline step."""
    step_id: str
    name: str
    command: str
    status: PipelineStatus = PipelineStatus.PENDING
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    duration_seconds: float = 0.0
    output: str = ""
    error_output: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "step_id": self.step_id,
            "name": self.name,
            "command": self.command,
            "status": self.status.value,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "duration_seconds": self.duration_seconds,
            "output": self.output,
            "error_output": self.error_output
        }


@dataclass
class PipelineConfiguration:
    """Pipeline configuration for Vibe Check analysis."""
    config_id: str
    name: str
    platform: CICDPlatform
    trigger_events: List[str] = field(default_factory=list)
    analysis_config: Dict[str, Any] = field(default_factory=dict)
    quality_gates: List[QualityGate] = field(default_factory=list)
    notification_settings: Dict[str, Any] = field(default_factory=dict)
    environment_variables: Dict[str, str] = field(default_factory=dict)
    steps: List[PipelineStep] = field(default_factory=list)
    
    def __post_init__(self):
        """Set default configuration."""
        if not self.trigger_events:
            self.trigger_events = ["push", "pull_request"]
        
        if not self.analysis_config:
            self.analysis_config = {
                "enabled_categories": ["style", "security", "complexity"],
                "severity_threshold": "warning",
                "fail_on_error": True,
                "generate_report": True
            }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "config_id": self.config_id,
            "name": self.name,
            "platform": self.platform.value,
            "trigger_events": self.trigger_events,
            "analysis_config": self.analysis_config,
            "quality_gates": [gate.to_dict() for gate in self.quality_gates],
            "notification_settings": self.notification_settings,
            "environment_variables": self.environment_variables,
            "steps": [step.to_dict() for step in self.steps]
        }


@dataclass
class PipelineExecution:
    """Pipeline execution record."""
    execution_id: str
    config_id: str
    platform: CICDPlatform
    repository_url: str
    branch: str
    commit_sha: str
    trigger_event: str
    status: PipelineStatus = PipelineStatus.PENDING
    build_result: Optional[BuildResult] = None
    started_at: datetime = field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None
    duration_seconds: float = 0.0
    steps: List[PipelineStep] = field(default_factory=list)
    quality_gate_results: List[QualityGate] = field(default_factory=list)
    analysis_results: Dict[str, Any] = field(default_factory=dict)
    artifacts: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "execution_id": self.execution_id,
            "config_id": self.config_id,
            "platform": self.platform.value,
            "repository_url": self.repository_url,
            "branch": self.branch,
            "commit_sha": self.commit_sha,
            "trigger_event": self.trigger_event,
            "status": self.status.value,
            "build_result": self.build_result.value if self.build_result else None,
            "started_at": self.started_at.isoformat(),
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "duration_seconds": self.duration_seconds,
            "steps": [step.to_dict() for step in self.steps],
            "quality_gate_results": [gate.to_dict() for gate in self.quality_gate_results],
            "analysis_results": self.analysis_results,
            "artifacts": self.artifacts
        }


@dataclass
class WebhookPayload:
    """Webhook payload from CI/CD platform."""
    platform: CICDPlatform
    event_type: str
    repository_url: str
    branch: str
    commit_sha: str
    commit_message: str
    author: str
    timestamp: datetime
    raw_payload: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "platform": self.platform.value,
            "event_type": self.event_type,
            "repository_url": self.repository_url,
            "branch": self.branch,
            "commit_sha": self.commit_sha,
            "commit_message": self.commit_message,
            "author": self.author,
            "timestamp": self.timestamp.isoformat(),
            "raw_payload": self.raw_payload
        }


@dataclass
class CICDMetrics:
    """CI/CD pipeline metrics."""
    total_executions: int = 0
    successful_executions: int = 0
    failed_executions: int = 0
    average_duration_seconds: float = 0.0
    success_rate: float = 0.0
    quality_gate_pass_rate: float = 0.0
    platform_distribution: Dict[str, int] = field(default_factory=dict)
    recent_executions: List[Dict[str, Any]] = field(default_factory=list)
    
    def calculate_success_rate(self) -> None:
        """Calculate success rate."""
        if self.total_executions > 0:
            self.success_rate = (self.successful_executions / self.total_executions) * 100
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "total_executions": self.total_executions,
            "successful_executions": self.successful_executions,
            "failed_executions": self.failed_executions,
            "average_duration_seconds": self.average_duration_seconds,
            "success_rate": self.success_rate,
            "quality_gate_pass_rate": self.quality_gate_pass_rate,
            "platform_distribution": self.platform_distribution,
            "recent_executions": self.recent_executions
        }
