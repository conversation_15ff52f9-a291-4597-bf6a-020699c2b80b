"""
File: vibe_check/enterprise/cicd/github_actions.py
Purpose: GitHub Actions integration for CI/CD workflows
Related Files: vibe_check/enterprise/cicd/
Dependencies: typing, yaml, pathlib
"""

import yaml
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime

from .models import (
    CICDPlatform, PipelineConfiguration, PipelineExecution,
    PipelineStep, QualityGate, WebhookPayload, PipelineStatus
)
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class GitHubActionsIntegration:
    """GitHub Actions integration for Vibe Check."""
    
    def __init__(self):
        self.platform = CICDPlatform.GITHUB_ACTIONS
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize GitHub Actions integration."""
        if self._initialized:
            return
        
        self._initialized = True
        logger.info("GitHub Actions integration initialized")
    
    def generate_workflow_file(
        self,
        config: PipelineConfiguration,
        output_path: Optional[Path] = None
    ) -> str:
        """
        Generate GitHub Actions workflow YAML file.
        
        Args:
            config: Pipeline configuration
            output_path: Optional path to save the workflow file
            
        Returns:
            Generated workflow YAML content
        """
        workflow = {
            "name": f"Vibe Check - {config.name}",
            "on": self._generate_triggers(config.trigger_events),
            "jobs": {
                "vibe-check": {
                    "runs-on": "ubuntu-latest",
                    "steps": self._generate_steps(config)
                }
            }
        }
        
        # Add environment variables if specified
        if config.environment_variables:
            workflow["env"] = config.environment_variables
        
        yaml_content = yaml.dump(workflow, default_flow_style=False, sort_keys=False)
        
        # Save to file if path provided
        if output_path:
            output_path.parent.mkdir(parents=True, exist_ok=True)
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(yaml_content)
            logger.info(f"Generated GitHub Actions workflow: {output_path}")
        
        return yaml_content
    
    def _generate_triggers(self, trigger_events: List[str]) -> Dict[str, Any]:
        """Generate workflow triggers."""
        triggers = {}
        
        for event in trigger_events:
            if event == "push":
                triggers["push"] = {
                    "branches": ["main", "develop"],
                    "paths-ignore": ["docs/**", "*.md"]
                }
            elif event == "pull_request":
                triggers["pull_request"] = {
                    "branches": ["main", "develop"],
                    "types": ["opened", "synchronize", "reopened"]
                }
            elif event == "schedule":
                triggers["schedule"] = [{"cron": "0 2 * * 1"}]  # Weekly on Monday 2 AM
            elif event == "workflow_dispatch":
                triggers["workflow_dispatch"] = {}
        
        return triggers
    
    def _generate_steps(self, config: PipelineConfiguration) -> List[Dict[str, Any]]:
        """Generate workflow steps."""
        steps = [
            {
                "name": "Checkout code",
                "uses": "actions/checkout@v4",
                "with": {"fetch-depth": 0}
            },
            {
                "name": "Set up Python",
                "uses": "actions/setup-python@v4",
                "with": {"python-version": "3.11"}
            },
            {
                "name": "Install Vibe Check",
                "run": "pip install vibe-check"
            }
        ]
        
        # Add custom steps from configuration
        for step in config.steps:
            steps.append({
                "name": step.name,
                "run": step.command
            })
        
        # Add main Vibe Check analysis step
        analysis_cmd = self._generate_analysis_command(config.analysis_config)
        steps.append({
            "name": "Run Vibe Check Analysis",
            "run": analysis_cmd
        })
        
        # Add quality gate evaluation
        if config.quality_gates:
            steps.append({
                "name": "Evaluate Quality Gates",
                "run": self._generate_quality_gate_command(config.quality_gates)
            })
        
        # Add report upload
        if config.analysis_config.get("generate_report", True):
            steps.extend([
                {
                    "name": "Upload Analysis Report",
                    "uses": "actions/upload-artifact@v3",
                    "with": {
                        "name": "vibe-check-report",
                        "path": "vibe-check-report.*",
                        "retention-days": 30
                    },
                    "if": "always()"
                },
                {
                    "name": "Comment PR with Results",
                    "uses": "actions/github-script@v6",
                    "if": "github.event_name == 'pull_request'",
                    "with": {
                        "script": self._generate_pr_comment_script()
                    }
                }
            ])
        
        return steps
    
    def _generate_analysis_command(self, analysis_config: Dict[str, Any]) -> str:
        """Generate Vibe Check analysis command."""
        cmd_parts = ["vibe-check-standalone", "."]
        
        # Add configuration options
        if "enabled_categories" in analysis_config:
            categories = ",".join(analysis_config["enabled_categories"])
            cmd_parts.extend(["--categories", categories])
        
        if "severity_threshold" in analysis_config:
            cmd_parts.extend(["--severity", analysis_config["severity_threshold"]])
        
        if analysis_config.get("generate_report", True):
            cmd_parts.extend(["--format", "html", "--output", "vibe-check-report.html"])
        
        if analysis_config.get("fail_on_error", True):
            cmd_parts.append("--fail-on-error")
        
        return " ".join(cmd_parts)
    
    def _generate_quality_gate_command(self, quality_gates: List[QualityGate]) -> str:
        """Generate quality gate evaluation command."""
        # This would integrate with a quality gate evaluation tool
        return "vibe-check-quality-gates --config quality-gates.json"
    
    def _generate_pr_comment_script(self) -> str:
        """Generate JavaScript for PR commenting."""
        return """
const fs = require('fs');
const path = require('path');

try {
  const reportPath = 'vibe-check-report.html';
  if (fs.existsSync(reportPath)) {
    const reportContent = fs.readFileSync(reportPath, 'utf8');
    const summary = extractSummary(reportContent);
    
    const comment = `## 🔍 Vibe Check Analysis Results
    
${summary}

📊 [View Full Report](${process.env.GITHUB_SERVER_URL}/${process.env.GITHUB_REPOSITORY}/actions/runs/${process.env.GITHUB_RUN_ID})
    `;
    
    github.rest.issues.createComment({
      issue_number: context.issue.number,
      owner: context.repo.owner,
      repo: context.repo.repo,
      body: comment
    });
  }
} catch (error) {
  console.error('Failed to post PR comment:', error);
}

function extractSummary(html) {
  // Extract key metrics from HTML report
  const metrics = html.match(/total_issues_found.*?(\d+)/);
  const files = html.match(/total_files_analyzed.*?(\d+)/);
  
  return `- **Files Analyzed:** ${files ? files[1] : 'N/A'}
- **Issues Found:** ${metrics ? metrics[1] : 'N/A'}`;
}
        """.strip()
    
    async def parse_webhook(self, payload: Dict[str, Any]) -> Optional[WebhookPayload]:
        """
        Parse GitHub webhook payload.
        
        Args:
            payload: Raw webhook payload
            
        Returns:
            Parsed webhook payload or None if invalid
        """
        try:
            # Handle push events
            if "push" in payload:
                return WebhookPayload(
                    platform=self.platform,
                    event_type="push",
                    repository_url=payload["repository"]["clone_url"],
                    branch=payload["ref"].replace("refs/heads/", ""),
                    commit_sha=payload["head_commit"]["id"],
                    commit_message=payload["head_commit"]["message"],
                    author=payload["head_commit"]["author"]["name"],
                    timestamp=datetime.fromisoformat(payload["head_commit"]["timestamp"].replace("Z", "+00:00")),
                    raw_payload=payload
                )
            
            # Handle pull request events
            elif "pull_request" in payload:
                pr = payload["pull_request"]
                return WebhookPayload(
                    platform=self.platform,
                    event_type="pull_request",
                    repository_url=pr["head"]["repo"]["clone_url"],
                    branch=pr["head"]["ref"],
                    commit_sha=pr["head"]["sha"],
                    commit_message=pr["title"],
                    author=pr["user"]["login"],
                    timestamp=datetime.fromisoformat(pr["updated_at"].replace("Z", "+00:00")),
                    raw_payload=payload
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to parse GitHub webhook: {e}")
            return None
    
    async def get_workflow_status(
        self,
        repository_url: str,
        workflow_id: str,
        run_id: str
    ) -> Optional[PipelineExecution]:
        """
        Get workflow execution status.
        
        Args:
            repository_url: Repository URL
            workflow_id: Workflow ID
            run_id: Workflow run ID
            
        Returns:
            Pipeline execution status or None if not found
        """
        # This would integrate with GitHub API
        # For now, return a mock execution
        return PipelineExecution(
            execution_id=run_id,
            config_id=workflow_id,
            platform=self.platform,
            repository_url=repository_url,
            branch="main",
            commit_sha="abc123",
            trigger_event="push",
            status=PipelineStatus.SUCCESS
        )
    
    def generate_action_metadata(self) -> Dict[str, Any]:
        """Generate GitHub Action metadata for marketplace."""
        return {
            "name": "Vibe Check Code Analysis",
            "description": "Comprehensive code analysis with Vibe Check",
            "author": "Vibe Check Team",
            "inputs": {
                "path": {
                    "description": "Path to analyze",
                    "required": False,
                    "default": "."
                },
                "categories": {
                    "description": "Analysis categories (comma-separated)",
                    "required": False,
                    "default": "style,security,complexity"
                },
                "severity": {
                    "description": "Minimum severity level",
                    "required": False,
                    "default": "warning"
                },
                "fail-on-error": {
                    "description": "Fail build on errors",
                    "required": False,
                    "default": "true"
                }
            },
            "outputs": {
                "report-path": {
                    "description": "Path to generated report"
                },
                "issues-count": {
                    "description": "Number of issues found"
                },
                "quality-score": {
                    "description": "Overall quality score"
                }
            },
            "runs": {
                "using": "composite",
                "steps": [
                    {
                        "name": "Install Vibe Check",
                        "run": "pip install vibe-check",
                        "shell": "bash"
                    },
                    {
                        "name": "Run Analysis",
                        "run": "vibe-check-standalone ${{ inputs.path }} --categories ${{ inputs.categories }} --severity ${{ inputs.severity }}",
                        "shell": "bash"
                    }
                ]
            },
            "branding": {
                "icon": "check-circle",
                "color": "blue"
            }
        }
    
    async def cleanup(self) -> None:
        """Cleanup GitHub Actions integration."""
        logger.info("GitHub Actions integration cleaned up")
