"""
File: vibe_check/enterprise/cicd/manager.py
Purpose: CI/CD integration manager for coordinating all platforms
Related Files: vibe_check/enterprise/cicd/
Dependencies: typing, pathlib, json, asyncio
"""

import json
import asyncio
from pathlib import Path

from .models import (
    CICDPlatform, PipelineConfiguration, PipelineExecution, 
    IntegrationConfig, WebhookPayload, CICDMetrics, PipelineStatus
)
from .github_actions import GitHubActionsIntegration
from .gitlab_ci import GitLabCIIntegration
from .jen<PERSON> import JenkinsIntegration
from .azure_devops import AzureDevOpsIntegration
from vibe_check.core.logging import get_logger
from typing import Any, Dict, List, Optional

logger = get_logger(__name__)


class CICDIntegrationManager:
    """Manages CI/CD integrations across multiple platforms."""
    
    def __init__(self, data_dir: Optional[Path] = None):
        """
        Initialize CI/CD integration manager.
        
        Args:
            data_dir: Directory for storing integration data
        """
        self.data_dir = data_dir or Path.cwd() / "cicd_data"
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Platform integrations
        self.integrations: Dict[CICDPlatform, Any] = {
            CICDPlatform.GITHUB_ACTIONS: GitHubActionsIntegration(),
            CICDPlatform.GITLAB_CI: GitLabCIIntegration(),
            CICDPlatform.JENKINS: JenkinsIntegration(),
            CICDPlatform.AZURE_DEVOPS: AzureDevOpsIntegration()
        }
        
        # Configuration and execution tracking
        self.configurations: Dict[str, PipelineConfiguration] = {}
        self.integration_configs: Dict[str, IntegrationConfig] = {}
        self.executions: Dict[str, PipelineExecution] = {}
        self.webhook_handlers: Dict[CICDPlatform, Any] = {}
        
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize CI/CD integration manager."""
        if self._initialized:
            return
        
        # Initialize all platform integrations
        for platform, integration in self.integrations.items():
            try:
                await integration.initialize()
                logger.debug(f"Initialized {platform.value} integration")
            except Exception as e:
                logger.error(f"Failed to initialize {platform.value} integration: {e}")
        
        # Load existing configurations and executions
        await self._load_data()
        
        self._initialized = True
        logger.info(f"CI/CD integration manager initialized with {len(self.integrations)} platforms")
    
    async def _load_data(self) -> None:
        """Load configurations and execution data."""
        # Load pipeline configurations
        configs_file = self.data_dir / "pipeline_configurations.json"
        if configs_file.exists():
            try:
                with open(configs_file, 'r', encoding='utf-8') as f:
                    configs_data = json.load(f)
                
                for config_data in configs_data:
                    config = self._pipeline_config_from_dict(config_data)
                    self.configurations[config.config_id] = config
                
                logger.debug(f"Loaded {len(self.configurations)} pipeline configurations")
                
            except Exception as e:
                logger.error(f"Failed to load pipeline configurations: {e}")
        
        # Load integration configurations
        integrations_file = self.data_dir / "integration_configurations.json"
        if integrations_file.exists():
            try:
                with open(integrations_file, 'r', encoding='utf-8') as f:
                    integrations_data = json.load(f)
                
                for integration_data in integrations_data:
                    config = self._integration_config_from_dict(integration_data)
                    self.integration_configs[config.config_id] = config
                
                logger.debug(f"Loaded {len(self.integration_configs)} integration configurations")
                
            except Exception as e:
                logger.error(f"Failed to load integration configurations: {e}")
    
    async def _save_data(self) -> None:
        """Save configurations and execution data."""
        try:
            # Save pipeline configurations
            configs_file = self.data_dir / "pipeline_configurations.json"
            configs_data = [config.to_dict() for config in self.configurations.values()]
            with open(configs_file, 'w', encoding='utf-8') as f:
                json.dump(configs_data, f, indent=2)
            
            # Save integration configurations
            integrations_file = self.data_dir / "integration_configurations.json"
            integrations_data = [config.to_dict() for config in self.integration_configs.values()]
            with open(integrations_file, 'w', encoding='utf-8') as f:
                json.dump(integrations_data, f, indent=2)
            
            logger.debug("Saved CI/CD configuration data")
            
        except Exception as e:
            logger.error(f"Failed to save CI/CD data: {e}")
    
    def _pipeline_config_from_dict(self, data: Dict[str, Any]) -> PipelineConfiguration:
        """Create PipelineConfiguration from dictionary."""
        from .models import QualityGate, PipelineStep, QualityGateStatus
        
        # Convert quality gates
        quality_gates = []
        for gate_data in data.get("quality_gates", []):
            gate = QualityGate(
                gate_id=gate_data["gate_id"],
                name=gate_data["name"],
                description=gate_data["description"],
                conditions=gate_data["conditions"],
                status=QualityGateStatus(gate_data.get("status", "passed")),
                results=gate_data.get("results", {}),
                evaluated_at=datetime.fromisoformat(gate_data["evaluated_at"]) if gate_data.get("evaluated_at") else None
            )
            quality_gates.append(gate)
        
        # Convert steps
        steps = []
        for step_data in data.get("steps", []):
            step = PipelineStep(
                step_id=step_data["step_id"],
                name=step_data["name"],
                command=step_data["command"]
            )
            steps.append(step)
        
        return PipelineConfiguration(
            config_id=data["config_id"],
            name=data["name"],
            platform=CICDPlatform(data["platform"]),
            trigger_events=data.get("trigger_events", []),
            analysis_config=data.get("analysis_config", {}),
            quality_gates=quality_gates,
            notification_settings=data.get("notification_settings", {}),
            environment_variables=data.get("environment_variables", {}),
            steps=steps
        )
    
    def _integration_config_from_dict(self, data: Dict[str, Any]) -> IntegrationConfig:
        """Create IntegrationConfig from dictionary."""
        return IntegrationConfig(
            platform=CICDPlatform(data["platform"]),
            config_id=data["config_id"],
            name=data["name"],
            repository_url=data["repository_url"],
            branch=data.get("branch", "main"),
            webhook_url=data.get("webhook_url"),
            api_token=data.get("api_token"),
            credentials=data.get("credentials", {}),
            settings=data.get("settings", {}),
            active=data.get("active", True),
            created_at=datetime.fromisoformat(data["created_at"])
        )
    
    async def create_pipeline_configuration(
        self,
        name: str,
        platform: CICDPlatform,
        trigger_events: Optional[List[str]] = None,
        analysis_config: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> PipelineConfiguration:
        """
        Create a new pipeline configuration.
        
        Args:
            name: Pipeline name
            platform: CI/CD platform
            trigger_events: Events that trigger the pipeline
            analysis_config: Analysis configuration
            **kwargs: Additional configuration options
            
        Returns:
            Created pipeline configuration
        """
        import uuid
        
        config_id = str(uuid.uuid4())
        
        config = PipelineConfiguration(
            config_id=config_id,
            name=name,
            platform=platform,
            trigger_events=trigger_events or ["push", "pull_request"],
            analysis_config=analysis_config or {},
            **kwargs
        )
        
        self.configurations[config_id] = config
        await self._save_data()
        
        logger.info(f"Created pipeline configuration '{name}' for {platform.value}")
        return config
    
    async def generate_pipeline_file(
        self,
        config_id: str,
        output_path: Optional[Path] = None
    ) -> Optional[str]:
        """
        Generate pipeline file for a configuration.
        
        Args:
            config_id: Pipeline configuration ID
            output_path: Optional output path
            
        Returns:
            Generated pipeline content or None if config not found
        """
        config = self.configurations.get(config_id)
        if not config:
            logger.error(f"Pipeline configuration {config_id} not found")
            return None
        
        integration = self.integrations.get(config.platform)
        if not integration:
            logger.error(f"Integration for {config.platform.value} not available")
            return None
        
        try:
            if hasattr(integration, 'generate_workflow_file'):
                return integration.generate_workflow_file(config, output_path)
            elif hasattr(integration, 'generate_pipeline_file'):
                return integration.generate_pipeline_file(config, output_path)
            elif hasattr(integration, 'generate_jenkinsfile'):
                return integration.generate_jenkinsfile(config, output_path)
            else:
                logger.error(f"No pipeline generation method for {config.platform.value}")
                return None
                
        except Exception as e:
            logger.error(f"Failed to generate pipeline file: {e}")
            return None
    
    async def handle_webhook(
        self,
        platform: CICDPlatform,
        payload: Dict[str, Any]
    ) -> Optional[WebhookPayload]:
        """
        Handle webhook from CI/CD platform.
        
        Args:
            platform: CI/CD platform
            payload: Webhook payload
            
        Returns:
            Parsed webhook payload or None if invalid
        """
        integration = self.integrations.get(platform)
        if not integration:
            logger.error(f"Integration for {platform.value} not available")
            return None
        
        try:
            webhook_payload = await integration.parse_webhook(payload)
            if webhook_payload:
                logger.info(f"Processed {platform.value} webhook: {webhook_payload.event_type}")
                
                # Trigger analysis if configured
                await self._trigger_analysis_from_webhook(webhook_payload)
            
            return webhook_payload
            
        except Exception as e:
            logger.error(f"Failed to handle {platform.value} webhook: {e}")
            return None
    
    async def _trigger_analysis_from_webhook(self, webhook: WebhookPayload) -> None:
        """Trigger analysis based on webhook event."""
        # Find matching configurations
        matching_configs = [
            config for config in self.configurations.values()
            if (config.platform == webhook.platform and
                webhook.event_type in config.trigger_events)
        ]
        
        for config in matching_configs:
            logger.info(f"Triggering analysis for config {config.name} from {webhook.event_type} event")
            # Here you would trigger the actual analysis
            # This could integrate with the VCS engine or queue analysis jobs
    
    async def get_pipeline_executions(
        self,
        platform: Optional[CICDPlatform] = None,
        status: Optional[PipelineStatus] = None,
        limit: int = 50
    ) -> List[PipelineExecution]:
        """
        Get pipeline executions with optional filtering.
        
        Args:
            platform: Optional platform filter
            status: Optional status filter
            limit: Maximum number of executions to return
            
        Returns:
            List of pipeline executions
        """
        executions = list(self.executions.values())
        
        # Apply filters
        if platform:
            executions = [e for e in executions if e.platform == platform]
        
        if status:
            executions = [e for e in executions if e.status == status]
        
        # Sort by start time (most recent first) and limit
        executions.sort(key=lambda e: e.started_at, reverse=True)
        return executions[:limit]
    
    def get_cicd_metrics(self) -> CICDMetrics:
        """Get CI/CD pipeline metrics."""
        executions = list(self.executions.values())
        
        metrics = CICDMetrics()
        metrics.total_executions = len(executions)
        
        # Calculate success metrics
        successful = [e for e in executions if e.status == PipelineStatus.SUCCESS]
        failed = [e for e in executions if e.status == PipelineStatus.FAILURE]
        
        metrics.successful_executions = len(successful)
        metrics.failed_executions = len(failed)
        metrics.calculate_success_rate()
        
        # Calculate average duration
        completed_executions = [e for e in executions if e.completed_at]
        if completed_executions:
            total_duration = sum(e.duration_seconds for e in completed_executions)
            metrics.average_duration_seconds = total_duration / len(completed_executions)
        
        # Platform distribution
        for execution in executions:
            platform = execution.platform.value
            metrics.platform_distribution[platform] = metrics.platform_distribution.get(platform, 0) + 1
        
        # Recent executions (last 10)
        recent = sorted(executions, key=lambda e: e.started_at, reverse=True)[:10]
        metrics.recent_executions = [
            {
                "execution_id": e.execution_id,
                "platform": e.platform.value,
                "status": e.status.value,
                "started_at": e.started_at.isoformat(),
                "duration_seconds": e.duration_seconds
            }
            for e in recent
        ]
        
        return metrics
    
    def get_integration_statistics(self) -> Dict[str, Any]:
        """Get integration statistics."""
        return {
            "total_platforms": len(self.integrations),
            "active_platforms": [platform.value for platform in self.integrations.keys()],
            "total_configurations": len(self.configurations),
            "total_integrations": len(self.integration_configs),
            "total_executions": len(self.executions),
            "metrics": self.get_cicd_metrics().to_dict()
        }
    
    async def cleanup(self) -> None:
        """Cleanup CI/CD integration manager."""
        # Save data
        await self._save_data()
        
        # Cleanup integrations
        cleanup_tasks = []
        for integration in self.integrations.values():
            if hasattr(integration, 'cleanup'):
                cleanup_tasks.append(integration.cleanup())
        
        if cleanup_tasks:
            await asyncio.gather(*cleanup_tasks, return_exceptions=True)
        
        logger.info("CI/CD integration manager cleaned up")
