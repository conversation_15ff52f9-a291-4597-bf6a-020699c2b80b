"""
File: vibe_check/enterprise/collaboration/team_manager.py
Purpose: Team management for enterprise collaboration
Related Files: vibe_check/enterprise/collaboration/
Dependencies: typing, datetime, pathlib, json, uuid
"""

import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field
from enum import Enum
import uuid

from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class UserRole(Enum):
    """User roles in team collaboration."""
    ADMIN = "admin"
    LEAD = "lead"
    DEVELOPER = "developer"
    VIEWER = "viewer"


class TeamPermission(Enum):
    """Team permissions."""
    READ_REPORTS = "read_reports"
    WRITE_REPORTS = "write_reports"
    MANAGE_CONFIG = "manage_config"
    MANAGE_MEMBERS = "manage_members"
    DELETE_TEAM = "delete_team"


@dataclass
class TeamMember:
    """Team member information."""
    user_id: str
    username: str
    email: str
    role: User<PERSON><PERSON>
    joined_at: datetime
    permissions: Set[TeamPermission] = field(default_factory=set)
    active: bool = True
    
    def __post_init__(self):
        # Set default permissions based on role
        if not self.permissions:
            self.permissions = self._get_default_permissions()
    
    def _get_default_permissions(self) -> Set[TeamPermission]:
        """Get default permissions for role."""
        if self.role == UserRole.ADMIN:
            return set(TeamPermission)
        elif self.role == UserRole.LEAD:
            return {
                TeamPermission.READ_REPORTS,
                TeamPermission.WRITE_REPORTS,
                TeamPermission.MANAGE_CONFIG,
                TeamPermission.MANAGE_MEMBERS
            }
        elif self.role == UserRole.DEVELOPER:
            return {
                TeamPermission.READ_REPORTS,
                TeamPermission.WRITE_REPORTS
            }
        else:  # VIEWER
            return {TeamPermission.READ_REPORTS}
    
    def has_permission(self, permission: TeamPermission) -> bool:
        """Check if member has specific permission."""
        return permission in self.permissions
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "user_id": self.user_id,
            "username": self.username,
            "email": self.email,
            "role": self.role.value,
            "joined_at": self.joined_at.isoformat(),
            "permissions": [p.value for p in self.permissions],
            "active": self.active
        }


@dataclass
class Team:
    """Team information and configuration."""
    team_id: str
    name: str
    description: str
    created_at: datetime
    created_by: str
    members: List[TeamMember] = field(default_factory=list)
    shared_configs: Dict[str, str] = field(default_factory=dict)
    team_settings: Dict[str, Any] = field(default_factory=dict)
    active: bool = True
    
    def add_member(self, member: TeamMember) -> bool:
        """Add a member to the team."""
        # Check if user already exists
        for existing_member in self.members:
            if existing_member.user_id == member.user_id:
                logger.warning(f"User {member.user_id} already in team {self.name}")
                return False
        
        self.members.append(member)
        logger.info(f"Added {member.username} to team {self.name}")
        return True
    
    def remove_member(self, user_id: str) -> bool:
        """Remove a member from the team."""
        for i, member in enumerate(self.members):
            if member.user_id == user_id:
                removed_member = self.members.pop(i)
                logger.info(f"Removed {removed_member.username} from team {self.name}")
                return True
        return False
    
    def get_member(self, user_id: str) -> Optional[TeamMember]:
        """Get a team member by user ID."""
        for member in self.members:
            if member.user_id == user_id:
                return member
        return None
    
    def update_member_role(self, user_id: str, new_role: UserRole) -> bool:
        """Update a member's role."""
        member = self.get_member(user_id)
        if member:
            old_role = member.role
            member.role = new_role
            member.permissions = member._get_default_permissions()
            logger.info(f"Updated {member.username} role from {old_role.value} to {new_role.value}")
            return True
        return False
    
    def get_members_by_role(self, role: UserRole) -> List[TeamMember]:
        """Get all members with a specific role."""
        return [member for member in self.members if member.role == role]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "team_id": self.team_id,
            "name": self.name,
            "description": self.description,
            "created_at": self.created_at.isoformat(),
            "created_by": self.created_by,
            "members": [member.to_dict() for member in self.members],
            "shared_configs": self.shared_configs,
            "team_settings": self.team_settings,
            "active": self.active
        }


class TeamManager:
    """Manages team collaboration features."""
    
    def __init__(self, data_dir: Optional[Path] = None):
        """
        Initialize team manager.
        
        Args:
            data_dir: Directory for storing team data
        """
        self.data_dir = data_dir or Path.cwd() / "team_data"
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        self.teams: Dict[str, Team] = {}
        self.users: Dict[str, Dict[str, Any]] = {}
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize team manager."""
        if self._initialized:
            return
        
        # Load existing teams and users
        await self._load_teams()
        await self._load_users()
        
        self._initialized = True
        logger.info(f"Team manager initialized with {len(self.teams)} teams and {len(self.users)} users")
    
    async def _load_teams(self) -> None:
        """Load teams from storage."""
        teams_file = self.data_dir / "teams.json"
        if teams_file.exists():
            try:
                with open(teams_file, 'r', encoding='utf-8') as f:
                    teams_data = json.load(f)
                
                for team_data in teams_data:
                    team = self._team_from_dict(team_data)
                    self.teams[team.team_id] = team
                
                logger.debug(f"Loaded {len(self.teams)} teams from storage")
                
            except Exception as e:
                logger.error(f"Failed to load teams: {e}")
    
    async def _load_users(self) -> None:
        """Load users from storage."""
        users_file = self.data_dir / "users.json"
        if users_file.exists():
            try:
                with open(users_file, 'r', encoding='utf-8') as f:
                    self.users = json.load(f)
                
                logger.debug(f"Loaded {len(self.users)} users from storage")
                
            except Exception as e:
                logger.error(f"Failed to load users: {e}")
    
    async def _save_teams(self) -> None:
        """Save teams to storage."""
        teams_file = self.data_dir / "teams.json"
        try:
            teams_data = [team.to_dict() for team in self.teams.values()]
            with open(teams_file, 'w', encoding='utf-8') as f:
                json.dump(teams_data, f, indent=2)
            
            logger.debug("Saved teams to storage")
            
        except Exception as e:
            logger.error(f"Failed to save teams: {e}")
    
    async def _save_users(self) -> None:
        """Save users to storage."""
        users_file = self.data_dir / "users.json"
        try:
            with open(users_file, 'w', encoding='utf-8') as f:
                json.dump(self.users, f, indent=2)
            
            logger.debug("Saved users to storage")
            
        except Exception as e:
            logger.error(f"Failed to save users: {e}")
    
    def _team_from_dict(self, data: Dict[str, Any]) -> Team:
        """Create Team object from dictionary."""
        members = []
        for member_data in data.get("members", []):
            member = TeamMember(
                user_id=member_data["user_id"],
                username=member_data["username"],
                email=member_data["email"],
                role=UserRole(member_data["role"]),
                joined_at=datetime.fromisoformat(member_data["joined_at"]),
                permissions={TeamPermission(p) for p in member_data.get("permissions", [])},
                active=member_data.get("active", True)
            )
            members.append(member)
        
        return Team(
            team_id=data["team_id"],
            name=data["name"],
            description=data["description"],
            created_at=datetime.fromisoformat(data["created_at"]),
            created_by=data["created_by"],
            members=members,
            shared_configs=data.get("shared_configs", {}),
            team_settings=data.get("team_settings", {}),
            active=data.get("active", True)
        )
    
    async def create_team(
        self,
        name: str,
        description: str,
        created_by: str,
        initial_members: Optional[List[Dict[str, Any]]] = None
    ) -> Team:
        """
        Create a new team.
        
        Args:
            name: Team name
            description: Team description
            created_by: User ID of team creator
            initial_members: Optional list of initial members
            
        Returns:
            Created team
        """
        # Check if team name already exists
        for team in self.teams.values():
            if team.name.lower() == name.lower():
                raise ValueError(f"Team with name '{name}' already exists")
        
        team_id = str(uuid.uuid4())
        team = Team(
            team_id=team_id,
            name=name,
            description=description,
            created_at=datetime.now(),
            created_by=created_by
        )
        
        # Add creator as admin
        creator_member = TeamMember(
            user_id=created_by,
            username=self.users.get(created_by, {}).get("username", created_by),
            email=self.users.get(created_by, {}).get("email", f"{created_by}@example.com"),
            role=UserRole.ADMIN,
            joined_at=datetime.now()
        )
        team.add_member(creator_member)
        
        # Add initial members if provided
        if initial_members:
            for member_data in initial_members:
                member = TeamMember(
                    user_id=member_data["user_id"],
                    username=member_data.get("username", member_data["user_id"]),
                    email=member_data.get("email", f"{member_data['user_id']}@example.com"),
                    role=UserRole(member_data.get("role", "developer")),
                    joined_at=datetime.now()
                )
                team.add_member(member)
        
        self.teams[team_id] = team
        await self._save_teams()
        
        logger.info(f"Created team '{name}' with ID {team_id}")
        return team
    
    def get_team_statistics(self) -> Dict[str, Any]:
        """Get team statistics."""
        total_members = sum(len(team.members) for team in self.teams.values())
        active_teams = sum(1 for team in self.teams.values() if team.active)
        
        role_distribution = {}
        for team in self.teams.values():
            for member in team.members:
                role = member.role.value
                role_distribution[role] = role_distribution.get(role, 0) + 1
        
        return {
            "total_teams": len(self.teams),
            "active_teams": active_teams,
            "total_members": total_members,
            "average_team_size": total_members / len(self.teams) if self.teams else 0,
            "role_distribution": role_distribution,
            "teams": [
                {
                    "team_id": team.team_id,
                    "name": team.name,
                    "member_count": len(team.members),
                    "active": team.active
                }
                for team in self.teams.values()
            ]
        }
    
    async def cleanup(self) -> None:
        """Cleanup team manager resources."""
        await self._save_teams()
        await self._save_users()
        logger.info("Team manager cleaned up")
