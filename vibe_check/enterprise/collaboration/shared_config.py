"""
File: vibe_check/enterprise/collaboration/shared_config.py
Purpose: Shared configuration management for enterprise collaboration
Related Files: vibe_check/enterprise/collaboration/
Dependencies: typing, datetime, pathlib, json
"""

import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Set

from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class SharedConfiguration:
    """Manages shared configurations across teams."""
    
    def __init__(self, data_dir: Optional[Path] = None):
        """
        Initialize shared configuration manager.
        
        Args:
            data_dir: Directory for storing configuration data
        """
        self.data_dir = data_dir or Path.cwd() / "shared_configs"
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        self.configurations: Dict[str, Dict[str, Any]] = {}
        self.config_permissions: Dict[str, Dict[str, Set[str]]] = {}
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize shared configuration manager."""
        if self._initialized:
            return
        
        await self._load_configurations()
        
        self._initialized = True
        logger.info(f"Shared configuration manager initialized with {len(self.configurations)} configs")
    
    async def _load_configurations(self) -> None:
        """Load configurations from storage."""
        configs_file = self.data_dir / "configurations.json"
        permissions_file = self.data_dir / "permissions.json"
        
        if configs_file.exists():
            try:
                with open(configs_file, 'r', encoding='utf-8') as f:
                    self.configurations = json.load(f)
                logger.debug(f"Loaded {len(self.configurations)} configurations")
            except Exception as e:
                logger.error(f"Failed to load configurations: {e}")
        
        if permissions_file.exists():
            try:
                with open(permissions_file, 'r', encoding='utf-8') as f:
                    permissions_data = json.load(f)
                    # Convert lists back to sets
                    self.config_permissions = {
                        config_name: {
                            perm_type: set(users)
                            for perm_type, users in perms.items()
                        }
                        for config_name, perms in permissions_data.items()
                    }
                logger.debug(f"Loaded permissions for {len(self.config_permissions)} configurations")
            except Exception as e:
                logger.error(f"Failed to load configuration permissions: {e}")
    
    async def _save_configurations(self) -> None:
        """Save configurations to storage."""
        configs_file = self.data_dir / "configurations.json"
        permissions_file = self.data_dir / "permissions.json"
        
        try:
            with open(configs_file, 'w', encoding='utf-8') as f:
                json.dump(self.configurations, f, indent=2)
            
            # Convert sets to lists for JSON serialization
            permissions_data = {
                config_name: {
                    perm_type: list(users)
                    for perm_type, users in perms.items()
                }
                for config_name, perms in self.config_permissions.items()
            }
            
            with open(permissions_file, 'w', encoding='utf-8') as f:
                json.dump(permissions_data, f, indent=2)
            
            logger.debug("Saved configurations and permissions")
            
        except Exception as e:
            logger.error(f"Failed to save configurations: {e}")
    
    async def save_configuration(
        self,
        config_name: str,
        config: Dict[str, Any],
        created_by: str,
        team_id: Optional[str] = None,
        description: Optional[str] = None
    ) -> bool:
        """
        Save a shared configuration.
        
        Args:
            config_name: Configuration name
            config: Configuration data
            created_by: User ID who created the configuration
            team_id: Optional team ID for team-specific configs
            description: Optional description
            
        Returns:
            True if successful, False otherwise
        """
        config_data = {
            "name": config_name,
            "config": config,
            "created_by": created_by,
            "created_at": datetime.now().isoformat(),
            "team_id": team_id,
            "description": description or "",
            "version": 1,
            "active": True
        }
        
        self.configurations[config_name] = config_data
        
        # Set default permissions - creator has full access
        self.config_permissions[config_name] = {
            "read": {created_by},
            "write": {created_by},
            "admin": {created_by}
        }
        
        await self._save_configurations()
        
        logger.info(f"Saved configuration '{config_name}' by {created_by}")
        return True
    
    async def get_configuration(
        self,
        config_name: str,
        requested_by: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Get a shared configuration.
        
        Args:
            config_name: Configuration name
            requested_by: User ID requesting the configuration
            
        Returns:
            Configuration data if accessible, None otherwise
        """
        if config_name not in self.configurations:
            return None
        
        # Check read permissions if requested_by is provided
        if requested_by:
            permissions = self.config_permissions.get(config_name, {})
            read_users = permissions.get("read", set())
            if requested_by not in read_users:
                logger.warning(f"User {requested_by} does not have read access to config '{config_name}'")
                return None
        
        return self.configurations[config_name]
    
    async def update_configuration(
        self,
        config_name: str,
        config: Dict[str, Any],
        updated_by: str
    ) -> bool:
        """
        Update a shared configuration.
        
        Args:
            config_name: Configuration name
            config: Updated configuration data
            updated_by: User ID updating the configuration
            
        Returns:
            True if successful, False otherwise
        """
        if config_name not in self.configurations:
            logger.error(f"Configuration '{config_name}' not found")
            return False
        
        # Check write permissions
        permissions = self.config_permissions.get(config_name, {})
        write_users = permissions.get("write", set())
        if updated_by not in write_users:
            logger.error(f"User {updated_by} does not have write access to config '{config_name}'")
            return False
        
        config_data = self.configurations[config_name]
        config_data["config"] = config
        config_data["updated_by"] = updated_by
        config_data["updated_at"] = datetime.now().isoformat()
        config_data["version"] += 1
        
        await self._save_configurations()
        
        logger.info(f"Updated configuration '{config_name}' by {updated_by}")
        return True
    
    async def delete_configuration(
        self,
        config_name: str,
        deleted_by: str
    ) -> bool:
        """
        Delete a shared configuration.
        
        Args:
            config_name: Configuration name
            deleted_by: User ID deleting the configuration
            
        Returns:
            True if successful, False otherwise
        """
        if config_name not in self.configurations:
            logger.error(f"Configuration '{config_name}' not found")
            return False
        
        # Check admin permissions
        permissions = self.config_permissions.get(config_name, {})
        admin_users = permissions.get("admin", set())
        if deleted_by not in admin_users:
            logger.error(f"User {deleted_by} does not have admin access to config '{config_name}'")
            return False
        
        del self.configurations[config_name]
        del self.config_permissions[config_name]
        
        await self._save_configurations()
        
        logger.info(f"Deleted configuration '{config_name}' by {deleted_by}")
        return True
    
    async def grant_access(
        self,
        config_name: str,
        user_id: str,
        permission_type: str,
        granted_by: str
    ) -> bool:
        """
        Grant access to a configuration.
        
        Args:
            config_name: Configuration name
            user_id: User ID to grant access to
            permission_type: Permission type (read, write, admin)
            granted_by: User ID granting the access
            
        Returns:
            True if successful, False otherwise
        """
        if config_name not in self.configurations:
            logger.error(f"Configuration '{config_name}' not found")
            return False
        
        # Check admin permissions
        permissions = self.config_permissions.get(config_name, {})
        admin_users = permissions.get("admin", set())
        if granted_by not in admin_users:
            logger.error(f"User {granted_by} does not have admin access to config '{config_name}'")
            return False
        
        if permission_type not in ["read", "write", "admin"]:
            logger.error(f"Invalid permission type: {permission_type}")
            return False
        
        permissions.setdefault(permission_type, set()).add(user_id)
        
        await self._save_configurations()
        
        logger.info(f"Granted {permission_type} access to '{config_name}' for user {user_id}")
        return True
    
    def list_configurations(
        self,
        user_id: Optional[str] = None,
        team_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        List configurations accessible to a user or team.
        
        Args:
            user_id: Optional user ID to filter by access
            team_id: Optional team ID to filter by
            
        Returns:
            List of accessible configurations
        """
        configs = []
        
        for config_name, config_data in self.configurations.items():
            # Filter by team if specified
            if team_id and config_data.get("team_id") != team_id:
                continue
            
            # Check user access if specified
            if user_id:
                permissions = self.config_permissions.get(config_name, {})
                has_access = any(
                    user_id in users
                    for users in permissions.values()
                )
                if not has_access:
                    continue
            
            configs.append({
                "name": config_name,
                "description": config_data.get("description", ""),
                "created_by": config_data.get("created_by"),
                "created_at": config_data.get("created_at"),
                "team_id": config_data.get("team_id"),
                "version": config_data.get("version", 1),
                "active": config_data.get("active", True)
            })
        
        return configs
    
    def get_configuration_statistics(self) -> Dict[str, Any]:
        """Get configuration statistics."""
        team_configs = sum(1 for config in self.configurations.values() if config.get("team_id"))
        global_configs = len(self.configurations) - team_configs
        
        return {
            "total_configurations": len(self.configurations),
            "team_configurations": team_configs,
            "global_configurations": global_configs,
            "total_permissions": len(self.config_permissions)
        }
    
    async def cleanup(self) -> None:
        """Cleanup shared configuration manager."""
        await self._save_configurations()
        logger.info("Shared configuration manager cleaned up")
