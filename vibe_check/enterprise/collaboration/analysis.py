"""
File: vibe_check/enterprise/collaboration/analysis.py
Purpose: Collaborative analysis features for teams
Related Files: vibe_check/enterprise/collaboration.py
Dependencies: typing, datetime, pathlib
"""

import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import uuid

from vibe_check.core.vcs.models import AnalysisResult
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class AnalysisStatus(Enum):
    """Status of collaborative analysis."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AnalysisPriority(Enum):
    """Priority levels for analysis."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class CollaborativeAnalysisRequest:
    """Request for collaborative analysis."""
    request_id: str
    team_id: str
    project_name: str
    project_path: str
    requested_by: str
    assigned_to: Optional[str]
    priority: AnalysisPriority
    status: AnalysisStatus
    created_at: datetime
    due_date: Optional[datetime]
    description: str
    analysis_config: Dict[str, Any]
    tags: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "request_id": self.request_id,
            "team_id": self.team_id,
            "project_name": self.project_name,
            "project_path": self.project_path,
            "requested_by": self.requested_by,
            "assigned_to": self.assigned_to,
            "priority": self.priority.value,
            "status": self.status.value,
            "created_at": self.created_at.isoformat(),
            "due_date": self.due_date.isoformat() if self.due_date else None,
            "description": self.description,
            "analysis_config": self.analysis_config,
            "tags": self.tags
        }


@dataclass
class AnalysisComment:
    """Comment on analysis results."""
    comment_id: str
    analysis_id: str
    user_id: str
    username: str
    content: str
    created_at: datetime
    file_path: Optional[str] = None
    line_number: Optional[int] = None
    issue_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "comment_id": self.comment_id,
            "analysis_id": self.analysis_id,
            "user_id": self.user_id,
            "username": self.username,
            "content": self.content,
            "created_at": self.created_at.isoformat(),
            "file_path": self.file_path,
            "line_number": self.line_number,
            "issue_id": self.issue_id
        }


@dataclass
class CollaborativeAnalysisResult:
    """Result of collaborative analysis with team context."""
    analysis_id: str
    request_id: str
    team_id: str
    analysis_results: List[AnalysisResult]
    completed_by: str
    completed_at: datetime
    summary: Dict[str, Any]
    comments: List[AnalysisComment]
    approved_by: Optional[str] = None
    approved_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "analysis_id": self.analysis_id,
            "request_id": self.request_id,
            "team_id": self.team_id,
            "completed_by": self.completed_by,
            "completed_at": self.completed_at.isoformat(),
            "summary": self.summary,
            "comments": [comment.to_dict() for comment in self.comments],
            "approved_by": self.approved_by,
            "approved_at": self.approved_at.isoformat() if self.approved_at else None
        }


class CollaborativeAnalysisManager:
    """Manages collaborative analysis workflows."""
    
    def __init__(self, data_dir: Optional[Path] = None):
        """
        Initialize collaborative analysis manager.
        
        Args:
            data_dir: Directory for storing analysis data
        """
        self.data_dir = data_dir or Path.cwd() / "collaborative_analysis"
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        self.analysis_requests: Dict[str, CollaborativeAnalysisRequest] = {}
        self.analysis_results: Dict[str, CollaborativeAnalysisResult] = {}
        self.comments: Dict[str, List[AnalysisComment]] = {}
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize collaborative analysis manager."""
        if self._initialized:
            return
        
        await self._load_data()
        
        self._initialized = True
        logger.info(f"Collaborative analysis manager initialized with {len(self.analysis_requests)} requests")
    
    async def _load_data(self) -> None:
        """Load analysis data from storage."""
        requests_file = self.data_dir / "requests.json"
        results_file = self.data_dir / "results.json"
        comments_file = self.data_dir / "comments.json"
        
        # Load requests
        if requests_file.exists():
            try:
                with open(requests_file, 'r', encoding='utf-8') as f:
                    requests_data = json.load(f)
                
                for req_data in requests_data:
                    request = CollaborativeAnalysisRequest(
                        request_id=req_data["request_id"],
                        team_id=req_data["team_id"],
                        project_name=req_data["project_name"],
                        project_path=req_data["project_path"],
                        requested_by=req_data["requested_by"],
                        assigned_to=req_data.get("assigned_to"),
                        priority=AnalysisPriority(req_data["priority"]),
                        status=AnalysisStatus(req_data["status"]),
                        created_at=datetime.fromisoformat(req_data["created_at"]),
                        due_date=datetime.fromisoformat(req_data["due_date"]) if req_data.get("due_date") else None,
                        description=req_data["description"],
                        analysis_config=req_data["analysis_config"],
                        tags=req_data.get("tags", [])
                    )
                    self.analysis_requests[request.request_id] = request
                
                logger.debug(f"Loaded {len(self.analysis_requests)} analysis requests")
                
            except Exception as e:
                logger.error(f"Failed to load analysis requests: {e}")
        
        # Load comments
        if comments_file.exists():
            try:
                with open(comments_file, 'r', encoding='utf-8') as f:
                    comments_data = json.load(f)
                
                for analysis_id, comment_list in comments_data.items():
                    comments = []
                    for comment_data in comment_list:
                        comment = AnalysisComment(
                            comment_id=comment_data["comment_id"],
                            analysis_id=comment_data["analysis_id"],
                            user_id=comment_data["user_id"],
                            username=comment_data["username"],
                            content=comment_data["content"],
                            created_at=datetime.fromisoformat(comment_data["created_at"]),
                            file_path=comment_data.get("file_path"),
                            line_number=comment_data.get("line_number"),
                            issue_id=comment_data.get("issue_id")
                        )
                        comments.append(comment)
                    self.comments[analysis_id] = comments
                
                logger.debug(f"Loaded comments for {len(self.comments)} analyses")
                
            except Exception as e:
                logger.error(f"Failed to load comments: {e}")
    
    async def _save_data(self) -> None:
        """Save analysis data to storage."""
        requests_file = self.data_dir / "requests.json"
        comments_file = self.data_dir / "comments.json"
        
        try:
            # Save requests
            requests_data = [req.to_dict() for req in self.analysis_requests.values()]
            with open(requests_file, 'w', encoding='utf-8') as f:
                json.dump(requests_data, f, indent=2)
            
            # Save comments
            comments_data = {
                analysis_id: [comment.to_dict() for comment in comment_list]
                for analysis_id, comment_list in self.comments.items()
            }
            with open(comments_file, 'w', encoding='utf-8') as f:
                json.dump(comments_data, f, indent=2)
            
            logger.debug("Saved collaborative analysis data")
            
        except Exception as e:
            logger.error(f"Failed to save analysis data: {e}")
    
    async def create_analysis_request(
        self,
        team_id: str,
        project_name: str,
        project_path: str,
        requested_by: str,
        description: str,
        priority: AnalysisPriority = AnalysisPriority.MEDIUM,
        analysis_config: Optional[Dict[str, Any]] = None,
        assigned_to: Optional[str] = None,
        due_date: Optional[datetime] = None,
        tags: Optional[List[str]] = None
    ) -> CollaborativeAnalysisRequest:
        """
        Create a new collaborative analysis request.
        
        Args:
            team_id: Team ID
            project_name: Project name
            project_path: Path to project
            requested_by: User ID who requested the analysis
            description: Analysis description
            priority: Analysis priority
            analysis_config: Analysis configuration
            assigned_to: Optional user ID to assign to
            due_date: Optional due date
            tags: Optional tags
            
        Returns:
            Created analysis request
        """
        request_id = str(uuid.uuid4())
        
        request = CollaborativeAnalysisRequest(
            request_id=request_id,
            team_id=team_id,
            project_name=project_name,
            project_path=project_path,
            requested_by=requested_by,
            assigned_to=assigned_to,
            priority=priority,
            status=AnalysisStatus.PENDING,
            created_at=datetime.now(),
            due_date=due_date,
            description=description,
            analysis_config=analysis_config or {},
            tags=tags or []
        )
        
        self.analysis_requests[request_id] = request
        await self._save_data()
        
        logger.info(f"Created analysis request {request_id} for team {team_id}")
        return request
    
    async def assign_analysis(
        self,
        request_id: str,
        assigned_to: str,
        assigned_by: str
    ) -> bool:
        """
        Assign an analysis request to a team member.
        
        Args:
            request_id: Request ID
            assigned_to: User ID to assign to
            assigned_by: User ID who is assigning
            
        Returns:
            True if successful, False otherwise
        """
        request = self.analysis_requests.get(request_id)
        if not request:
            logger.error(f"Analysis request {request_id} not found")
            return False
        
        request.assigned_to = assigned_to
        request.status = AnalysisStatus.IN_PROGRESS
        
        await self._save_data()
        
        logger.info(f"Assigned analysis {request_id} to {assigned_to} by {assigned_by}")
        return True
    
    async def add_comment(
        self,
        analysis_id: str,
        user_id: str,
        username: str,
        content: str,
        file_path: Optional[str] = None,
        line_number: Optional[int] = None,
        issue_id: Optional[str] = None
    ) -> AnalysisComment:
        """
        Add a comment to an analysis.
        
        Args:
            analysis_id: Analysis ID
            user_id: User ID adding comment
            username: Username
            content: Comment content
            file_path: Optional file path for file-specific comments
            line_number: Optional line number for line-specific comments
            issue_id: Optional issue ID for issue-specific comments
            
        Returns:
            Created comment
        """
        comment_id = str(uuid.uuid4())
        
        comment = AnalysisComment(
            comment_id=comment_id,
            analysis_id=analysis_id,
            user_id=user_id,
            username=username,
            content=content,
            created_at=datetime.now(),
            file_path=file_path,
            line_number=line_number,
            issue_id=issue_id
        )
        
        if analysis_id not in self.comments:
            self.comments[analysis_id] = []
        
        self.comments[analysis_id].append(comment)
        await self._save_data()
        
        logger.info(f"Added comment to analysis {analysis_id} by {username}")
        return comment
    
    def get_team_analysis_requests(
        self,
        team_id: str,
        status: Optional[AnalysisStatus] = None
    ) -> List[CollaborativeAnalysisRequest]:
        """
        Get analysis requests for a team.
        
        Args:
            team_id: Team ID
            status: Optional status filter
            
        Returns:
            List of analysis requests
        """
        requests = [
            req for req in self.analysis_requests.values()
            if req.team_id == team_id
        ]
        
        if status:
            requests = [req for req in requests if req.status == status]
        
        return requests
    
    def get_user_analysis_requests(
        self,
        user_id: str,
        assigned_only: bool = False
    ) -> List[CollaborativeAnalysisRequest]:
        """
        Get analysis requests for a user.
        
        Args:
            user_id: User ID
            assigned_only: If True, only return assigned requests
            
        Returns:
            List of analysis requests
        """
        if assigned_only:
            return [
                req for req in self.analysis_requests.values()
                if req.assigned_to == user_id
            ]
        else:
            return [
                req for req in self.analysis_requests.values()
                if req.requested_by == user_id or req.assigned_to == user_id
            ]
    
    def get_collaboration_statistics(self) -> Dict[str, Any]:
        """Get collaboration statistics."""
        total_requests = len(self.analysis_requests)
        status_counts = {}
        priority_counts = {}
        
        for request in self.analysis_requests.values():
            status = request.status.value
            priority = request.priority.value
            
            status_counts[status] = status_counts.get(status, 0) + 1
            priority_counts[priority] = priority_counts.get(priority, 0) + 1
        
        total_comments = sum(len(comments) for comments in self.comments.values())
        
        return {
            "total_requests": total_requests,
            "status_distribution": status_counts,
            "priority_distribution": priority_counts,
            "total_comments": total_comments,
            "analyses_with_comments": len(self.comments)
        }
    
    async def cleanup(self) -> None:
        """Cleanup collaborative analysis manager."""
        await self._save_data()
        logger.info("Collaborative analysis manager cleaned up")
