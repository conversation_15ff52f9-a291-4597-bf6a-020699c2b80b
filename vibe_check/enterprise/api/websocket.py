"""
File: vibe_check/enterprise/api/websocket.py
Purpose: WebSocket server for real-time enterprise Vibe Check
Related Files: vibe_check/enterprise/api/
Dependencies: typing, asyncio, json
"""

import asyncio
from datetime import datetime
import uuid

from .models import WebSocketMessage, WebSocketMessageType, APIConfiguration
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class WebSocketConnection:
    """WebSocket connection wrapper."""
    
    def __init__(self, connection_id: str, user_id: Optional[str] = None):
        self.connection_id = connection_id
        self.user_id = user_id
        self.subscriptions: Set[str] = set()
        self.connected_at = datetime.now()
        self.last_ping = datetime.now()
    
    async def send_message(self, message: WebSocketMessage) -> None:
        """Send message to connection."""
        # In a real implementation, this would send to actual WebSocket
        logger.debug(f"Sending message to {self.connection_id}: {message.message_type.value}")
    
    def subscribe(self, channel: str) -> None:
        """Subscribe to channel."""
        self.subscriptions.add(channel)
    
    def unsubscribe(self, channel: str) -> None:
        """Unsubscribe from channel."""
        self.subscriptions.discard(channel)


class WebSocketServer:
    """WebSocket server for real-time Vibe Check updates."""
    
    def __init__(self, vcs_engine: Any, config: Optional[APIConfiguration] = None):
        """
        Initialize WebSocket server.
        
        Args:
            vcs_engine: VCS engine instance
            config: API configuration
        """
        self.vcs_engine = vcs_engine
        self.config = config or APIConfiguration(
            config_id="default_websocket_api",
            name="Vibe Check WebSocket API",
            port=8002
        )
        
        self.connections: Dict[str, WebSocketConnection] = {}
        self.channels: Dict[str, Set[str]] = {}  # channel -> connection_ids
        self.message_handlers: Dict[WebSocketMessageType, Callable] = {}
        self._running = False
        self._initialized = False
        self._broadcast_task: Optional[asyncio.Task] = None
    
    async def initialize(self) -> None:
        """Initialize WebSocket server."""
        if self._initialized:
            return
        
        await self._register_message_handlers()
        await self._setup_channels()
        
        self._initialized = True
        logger.info("WebSocket server initialized")
    
    async def _register_message_handlers(self) -> None:
        """Register WebSocket message handlers."""
        self.message_handlers = {
            WebSocketMessageType.SUBSCRIBE: self._handle_subscribe,
            WebSocketMessageType.UNSUBSCRIBE: self._handle_unsubscribe,
            WebSocketMessageType.PING: self._handle_ping,
        }
    
    async def _setup_channels(self) -> None:
        """Setup WebSocket channels."""
        channels = [
            "analysis_progress",
            "monitoring_updates",
            "alert_notifications",
            "dashboard_updates",
            "quality_gate_results",
            "team_notifications",
            "pipeline_status",
            "system_health"
        ]
        
        for channel in channels:
            self.channels[channel] = set()
    
    async def start(self) -> None:
        """Start WebSocket server."""
        if self._running:
            return
        
        self._running = True
        
        # Start broadcast task for real-time updates
        self._broadcast_task = asyncio.create_task(self._broadcast_loop())
        
        logger.info(f"WebSocket server started on {self.config.host}:{self.config.port}")
    
    async def stop(self) -> None:
        """Stop WebSocket server."""
        self._running = False
        
        if self._broadcast_task:
            self._broadcast_task.cancel()
            try:
                await self._broadcast_task
            except asyncio.CancelledError:
                pass
        
        # Close all connections
        for connection in self.connections.values():
            await self._disconnect_client(connection.connection_id)
        
        logger.info("WebSocket server stopped")
    
    async def connect_client(self, user_id: Optional[str] = None) -> str:
        """
        Connect a new client.
        
        Args:
            user_id: Optional user ID
            
        Returns:
            Connection ID
        """
        connection_id = str(uuid.uuid4())
        connection = WebSocketConnection(connection_id, user_id)
        
        self.connections[connection_id] = connection
        
        # Send welcome message
        welcome_message = WebSocketMessage(
            message_id=str(uuid.uuid4()),
            message_type=WebSocketMessageType.DATA,
            channel="system",
            data={
                "type": "welcome",
                "connection_id": connection_id,
                "available_channels": list(self.channels.keys())
            }
        )
        
        await connection.send_message(welcome_message)
        
        logger.info(f"Client connected: {connection_id}")
        return connection_id
    
    async def _disconnect_client(self, connection_id: str) -> None:
        """Disconnect a client."""
        if connection_id in self.connections:
            connection = self.connections[connection_id]
            
            # Remove from all channels
            for channel in connection.subscriptions.copy():
                await self._unsubscribe_from_channel(connection_id, channel)
            
            del self.connections[connection_id]
            logger.info(f"Client disconnected: {connection_id}")
    
    async def handle_message(self, connection_id: str, message: WebSocketMessage) -> None:
        """
        Handle incoming WebSocket message.
        
        Args:
            connection_id: Connection ID
            message: WebSocket message
        """
        if connection_id not in self.connections:
            logger.warning(f"Message from unknown connection: {connection_id}")
            return
        
        connection = self.connections[connection_id]
        connection.last_ping = datetime.now()
        
        handler = self.message_handlers.get(message.message_type)
        if handler:
            try:
                await handler(connection_id, message)
            except Exception as e:
                logger.error(f"Error handling message from {connection_id}: {e}")
                
                error_message = WebSocketMessage(
                    message_id=str(uuid.uuid4()),
                    message_type=WebSocketMessageType.ERROR,
                    channel="system",
                    error=str(e)
                )
                await connection.send_message(error_message)
        else:
            logger.warning(f"Unknown message type: {message.message_type}")
    
    async def _handle_subscribe(self, connection_id: str, message: WebSocketMessage) -> None:
        """Handle subscribe message."""
        channel = message.channel
        
        if channel not in self.channels:
            raise ValueError(f"Unknown channel: {channel}")
        
        await self._subscribe_to_channel(connection_id, channel)
        
        # Send confirmation
        response = WebSocketMessage(
            message_id=str(uuid.uuid4()),
            message_type=WebSocketMessageType.DATA,
            channel="system",
            data={
                "type": "subscribed",
                "channel": channel,
                "connection_id": connection_id
            }
        )
        
        await self.connections[connection_id].send_message(response)
    
    async def _handle_unsubscribe(self, connection_id: str, message: WebSocketMessage) -> None:
        """Handle unsubscribe message."""
        channel = message.channel
        await self._unsubscribe_from_channel(connection_id, channel)
        
        # Send confirmation
        response = WebSocketMessage(
            message_id=str(uuid.uuid4()),
            message_type=WebSocketMessageType.DATA,
            channel="system",
            data={
                "type": "unsubscribed",
                "channel": channel,
                "connection_id": connection_id
            }
        )
        
        await self.connections[connection_id].send_message(response)
    
    async def _handle_ping(self, connection_id: str, message: WebSocketMessage) -> None:
        """Handle ping message."""
        pong_message = WebSocketMessage(
            message_id=str(uuid.uuid4()),
            message_type=WebSocketMessageType.PONG,
            channel="system",
            data={"timestamp": datetime.now().isoformat()}
        )
        
        await self.connections[connection_id].send_message(pong_message)
    
    async def _subscribe_to_channel(self, connection_id: str, channel: str) -> None:
        """Subscribe connection to channel."""
        if channel in self.channels:
            self.channels[channel].add(connection_id)
            self.connections[connection_id].subscribe(channel)
            logger.debug(f"Subscribed {connection_id} to {channel}")
    
    async def _unsubscribe_from_channel(self, connection_id: str, channel: str) -> None:
        """Unsubscribe connection from channel."""
        if channel in self.channels:
            self.channels[channel].discard(connection_id)
            self.connections[connection_id].unsubscribe(channel)
            logger.debug(f"Unsubscribed {connection_id} from {channel}")
    
    async def broadcast_to_channel(self, channel: str, data: Dict[str, Any]) -> None:
        """
        Broadcast data to all subscribers of a channel.
        
        Args:
            channel: Channel name
            data: Data to broadcast
        """
        if channel not in self.channels:
            logger.warning(f"Unknown channel: {channel}")
            return
        
        message = WebSocketMessage(
            message_id=str(uuid.uuid4()),
            message_type=WebSocketMessageType.DATA,
            channel=channel,
            data=data
        )
        
        subscribers = self.channels[channel].copy()
        
        for connection_id in subscribers:
            if connection_id in self.connections:
                try:
                    await self.connections[connection_id].send_message(message)
                except Exception as e:
                    logger.error(f"Failed to send message to {connection_id}: {e}")
                    # Remove failed connection
                    await self._disconnect_client(connection_id)
    
    async def _broadcast_loop(self) -> None:
        """Main broadcast loop for real-time updates."""
        while self._running:
            try:
                # Broadcast monitoring updates
                if self.vcs_engine.monitoring_system:
                    monitoring_data = {
                        "type": "monitoring_update",
                        "timestamp": datetime.now().isoformat(),
                        "metrics": {
                            "total_metrics": len(self.vcs_engine.monitoring_system.metrics_collector.metrics),
                            "active_alerts": len(self.vcs_engine.monitoring_system.active_alerts),
                            "system_health": 95.0  # Mock value
                        }
                    }
                    await self.broadcast_to_channel("monitoring_updates", monitoring_data)
                
                # Broadcast system health
                health_data = {
                    "type": "system_health",
                    "timestamp": datetime.now().isoformat(),
                    "status": "healthy",
                    "components": {
                        "vcs_engine": self.vcs_engine.is_enabled(),
                        "monitoring": self.vcs_engine.monitoring_system is not None,
                        "quality_gates": self.vcs_engine.quality_gate_manager is not None,
                        "cicd": self.vcs_engine.cicd_manager is not None,
                        "collaboration": self.vcs_engine.team_manager is not None
                    }
                }
                await self.broadcast_to_channel("system_health", health_data)
                
                # Wait before next broadcast
                await asyncio.sleep(30)  # Broadcast every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in broadcast loop: {e}")
                await asyncio.sleep(60)  # Wait longer on error
    
    def get_connection_statistics(self) -> Dict[str, Any]:
        """Get WebSocket connection statistics."""
        total_connections = len(self.connections)
        
        # Count subscriptions per channel
        channel_stats = {}
        for channel, subscribers in self.channels.items():
            channel_stats[channel] = len(subscribers)
        
        # Calculate average subscriptions per connection
        total_subscriptions = sum(len(conn.subscriptions) for conn in self.connections.values())
        avg_subscriptions = total_subscriptions / max(total_connections, 1)
        
        return {
            "total_connections": total_connections,
            "total_channels": len(self.channels),
            "channel_statistics": channel_stats,
            "total_subscriptions": total_subscriptions,
            "average_subscriptions_per_connection": round(avg_subscriptions, 2)
        }
    
    async def cleanup(self) -> None:
        """Cleanup WebSocket server."""
        await self.stop()
        logger.info("WebSocket server cleaned up")
