"""
File: vibe_check/enterprise/api/models.py
Purpose: Data models for unified API layer
Related Files: vibe_check/enterprise/api/
Dependencies: typing, datetime, enum, dataclasses
"""

from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, List, Optional


class APIMethod(Enum):
    """HTTP API methods."""
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"
    PATCH = "PATCH"


class APIStatus(Enum):
    """API response status."""
    SUCCESS = "success"
    ERROR = "error"
    PARTIAL = "partial"


class WebSocketMessageType(Enum):
    """WebSocket message types."""
    SUBSCRIBE = "subscribe"
    UNSUBSCRIBE = "unsubscribe"
    DATA = "data"
    ERROR = "error"
    PING = "ping"
    PONG = "pong"


@dataclass
class APIRequest:
    """API request model."""
    request_id: str
    method: APIMethod
    endpoint: str
    headers: Dict[str, str] = field(default_factory=dict)
    query_params: Dict[str, str] = field(default_factory=dict)
    body: Optional[Dict[str, Any]] = None
    user_id: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "request_id": self.request_id,
            "method": self.method.value,
            "endpoint": self.endpoint,
            "headers": self.headers,
            "query_params": self.query_params,
            "body": self.body,
            "user_id": self.user_id,
            "timestamp": self.timestamp.isoformat()
        }


@dataclass
class APIResponse:
    """API response model."""
    request_id: str
    status: APIStatus
    status_code: int
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "request_id": self.request_id,
            "status": self.status.value,
            "status_code": self.status_code,
            "data": self.data,
            "error": self.error,
            "metadata": self.metadata,
            "timestamp": self.timestamp.isoformat()
        }


@dataclass
class APIError:
    """API error model."""
    error_id: str
    code: str
    message: str
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "error_id": self.error_id,
            "code": self.code,
            "message": self.message,
            "details": self.details,
            "timestamp": self.timestamp.isoformat()
        }


@dataclass
class WebSocketMessage:
    """WebSocket message model."""
    message_id: str
    message_type: WebSocketMessageType
    channel: str
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "message_id": self.message_id,
            "type": self.message_type.value,
            "channel": self.channel,
            "data": self.data,
            "error": self.error,
            "timestamp": self.timestamp.isoformat()
        }


@dataclass
class GraphQLQuery:
    """GraphQL query model."""
    query_id: str
    query: str
    variables: Optional[Dict[str, Any]] = None
    operation_name: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "query_id": self.query_id,
            "query": self.query,
            "variables": self.variables,
            "operation_name": self.operation_name,
            "timestamp": self.timestamp.isoformat()
        }


@dataclass
class GraphQLMutation:
    """GraphQL mutation model."""
    mutation_id: str
    mutation: str
    variables: Optional[Dict[str, Any]] = None
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "mutation_id": self.mutation_id,
            "mutation": self.mutation,
            "variables": self.variables,
            "timestamp": self.timestamp.isoformat()
        }


@dataclass
class GraphQLSubscription:
    """GraphQL subscription model."""
    subscription_id: str
    subscription: str
    variables: Optional[Dict[str, Any]] = None
    active: bool = True
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "subscription_id": self.subscription_id,
            "subscription": self.subscription,
            "variables": self.variables,
            "active": self.active,
            "timestamp": self.timestamp.isoformat()
        }


@dataclass
class APIEndpoint:
    """API endpoint configuration."""
    endpoint_id: str
    path: str
    method: APIMethod
    handler: str
    description: str
    parameters: List[Dict[str, Any]] = field(default_factory=list)
    responses: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    authentication_required: bool = True
    rate_limit: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "endpoint_id": self.endpoint_id,
            "path": self.path,
            "method": self.method.value,
            "handler": self.handler,
            "description": self.description,
            "parameters": self.parameters,
            "responses": self.responses,
            "authentication_required": self.authentication_required,
            "rate_limit": self.rate_limit
        }


@dataclass
class APIMetrics:
    """API metrics model."""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    average_response_time: float = 0.0
    requests_per_minute: float = 0.0
    active_connections: int = 0
    endpoint_metrics: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    error_rates: Dict[str, float] = field(default_factory=dict)
    
    def calculate_success_rate(self) -> float:
        """Calculate success rate percentage."""
        if self.total_requests > 0:
            return (self.successful_requests / self.total_requests) * 100
        return 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "total_requests": self.total_requests,
            "successful_requests": self.successful_requests,
            "failed_requests": self.failed_requests,
            "success_rate": self.calculate_success_rate(),
            "average_response_time": self.average_response_time,
            "requests_per_minute": self.requests_per_minute,
            "active_connections": self.active_connections,
            "endpoint_metrics": self.endpoint_metrics,
            "error_rates": self.error_rates
        }


@dataclass
class APIConfiguration:
    """API server configuration."""
    config_id: str
    name: str
    host: str = "localhost"
    port: int = 8000
    ssl_enabled: bool = False
    ssl_cert_path: Optional[str] = None
    ssl_key_path: Optional[str] = None
    cors_enabled: bool = True
    cors_origins: List[str] = field(default_factory=lambda: ["*"])
    rate_limiting_enabled: bool = True
    rate_limit_requests: int = 100
    rate_limit_window: int = 60  # seconds
    authentication_enabled: bool = True
    jwt_secret: Optional[str] = None
    api_key_header: str = "X-API-Key"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "config_id": self.config_id,
            "name": self.name,
            "host": self.host,
            "port": self.port,
            "ssl_enabled": self.ssl_enabled,
            "ssl_cert_path": self.ssl_cert_path,
            "ssl_key_path": self.ssl_key_path,
            "cors_enabled": self.cors_enabled,
            "cors_origins": self.cors_origins,
            "rate_limiting_enabled": self.rate_limiting_enabled,
            "rate_limit_requests": self.rate_limit_requests,
            "rate_limit_window": self.rate_limit_window,
            "authentication_enabled": self.authentication_enabled,
            "jwt_secret": "***" if self.jwt_secret else None,
            "api_key_header": self.api_key_header
        }
