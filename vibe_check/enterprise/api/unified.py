"""
File: vibe_check/enterprise/api/unified.py
Purpose: Unified API manager for REST, GraphQL, and WebSocket
Related Files: vibe_check/enterprise/api/
Dependencies: typing, asyncio, pathlib, json
"""

import json
import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime

from .rest import RestAPIServer
from .graphql import GraphQLServer
from .websocket import WebSocketServer
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class UnifiedAPIManager:
    """Unified API manager for all interface types."""
    
    def __init__(self, vcs_engine: Any, data_dir: Optional[Path] = None):
        """
        Initialize unified API manager.
        
        Args:
            vcs_engine: VCS engine instance
            data_dir: Directory for storing API data
        """
        self.vcs_engine = vcs_engine
        self.data_dir = data_dir or Path.cwd() / "api_data"
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # API servers
        self.rest_server: Optional[RestAPIServer] = None
        self.graphql_server: Optional[GraphQLServer] = None
        self.websocket_server: Optional[WebSocketServer] = None
        
        # Configurations
        self.configurations: Dict[str, APIConfiguration] = {}
        self.enabled_interfaces: List[str] = ["rest", "graphql", "websocket"]
        
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize unified API manager."""
        if self._initialized:
            return
        
        await self._load_configurations()
        await self._create_default_configurations()
        await self._initialize_servers()
        
        self._initialized = True
        logger.info(f"Unified API manager initialized with {len(self.enabled_interfaces)} interfaces")
    
    async def _load_configurations(self) -> None:
        """Load API configurations."""
        config_file = self.data_dir / "api_configurations.json"
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    configs_data = json.load(f)
                
                for config_data in configs_data:
                    config = APIConfiguration(**config_data)
                    self.configurations[config.config_id] = config
                
                logger.debug(f"Loaded {len(self.configurations)} API configurations")
                
            except Exception as e:
                logger.error(f"Failed to load API configurations: {e}")
    
    async def _save_configurations(self) -> None:
        """Save API configurations."""
        config_file = self.data_dir / "api_configurations.json"
        try:
            configs_data = [config.to_dict() for config in self.configurations.values()]
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(configs_data, f, indent=2)
            
            logger.debug("Saved API configurations")
            
        except Exception as e:
            logger.error(f"Failed to save API configurations: {e}")
    
    async def _create_default_configurations(self) -> None:
        """Create default API configurations if none exist."""
        if self.configurations:
            return
        
        # REST API configuration
        rest_config = APIConfiguration(
            config_id="default_rest_api",
            name="Vibe Check REST API",
            host="localhost",
            port=8000,
            cors_enabled=True,
            rate_limiting_enabled=True,
            authentication_enabled=False  # Simplified for demo
        )
        
        # GraphQL API configuration
        graphql_config = APIConfiguration(
            config_id="default_graphql_api",
            name="Vibe Check GraphQL API",
            host="localhost",
            port=8001,
            cors_enabled=True,
            rate_limiting_enabled=True,
            authentication_enabled=False  # Simplified for demo
        )
        
        # WebSocket API configuration
        websocket_config = APIConfiguration(
            config_id="default_websocket_api",
            name="Vibe Check WebSocket API",
            host="localhost",
            port=8002,
            cors_enabled=True,
            rate_limiting_enabled=False,  # Different rate limiting for WebSocket
            authentication_enabled=False  # Simplified for demo
        )
        
        self.configurations[rest_config.config_id] = rest_config
        self.configurations[graphql_config.config_id] = graphql_config
        self.configurations[websocket_config.config_id] = websocket_config
        
        await self._save_configurations()
        logger.info("Created default API configurations")
    
    async def _initialize_servers(self) -> None:
        """Initialize API servers."""
        # Initialize REST server
        if "rest" in self.enabled_interfaces:
            rest_config = self.configurations.get("default_rest_api")
            if rest_config:
                self.rest_server = RestAPIServer(self.vcs_engine, rest_config)
                await self.rest_server.initialize()
        
        # Initialize GraphQL server
        if "graphql" in self.enabled_interfaces:
            graphql_config = self.configurations.get("default_graphql_api")
            if graphql_config:
                self.graphql_server = GraphQLServer(self.vcs_engine, graphql_config)
                await self.graphql_server.initialize()
        
        # Initialize WebSocket server
        if "websocket" in self.enabled_interfaces:
            websocket_config = self.configurations.get("default_websocket_api")
            if websocket_config:
                self.websocket_server = WebSocketServer(self.vcs_engine, websocket_config)
                await self.websocket_server.initialize()
    
    async def start_all_servers(self) -> None:
        """Start all enabled API servers."""
        start_tasks = []
        
        if self.rest_server:
            start_tasks.append(self.rest_server.start())
        
        if self.graphql_server:
            start_tasks.append(self.graphql_server.start())
        
        if self.websocket_server:
            start_tasks.append(self.websocket_server.start())
        
        if start_tasks:
            await asyncio.gather(*start_tasks)
        
        logger.info(f"Started {len(start_tasks)} API servers")
    
    async def stop_all_servers(self) -> None:
        """Stop all API servers."""
        stop_tasks = []
        
        if self.rest_server:
            stop_tasks.append(self.rest_server.stop())
        
        if self.graphql_server:
            stop_tasks.append(self.graphql_server.stop())
        
        if self.websocket_server:
            stop_tasks.append(self.websocket_server.stop())
        
        if stop_tasks:
            await asyncio.gather(*stop_tasks, return_exceptions=True)
        
        logger.info("Stopped all API servers")
    
    def get_api_endpoints(self) -> Dict[str, Any]:
        """Get all available API endpoints."""
        endpoints = {
            "rest": {},
            "graphql": {},
            "websocket": {}
        }
        
        # REST endpoints
        if self.rest_server:
            rest_config = self.configurations.get("default_rest_api")
            if rest_config:
                base_url = f"http://{rest_config.host}:{rest_config.port}"
                endpoints["rest"] = {
                    "base_url": base_url,
                    "documentation": f"{base_url}/docs",
                    "endpoints": [endpoint.to_dict() for endpoint in self.rest_server.endpoints.values()]
                }
        
        # GraphQL endpoints
        if self.graphql_server:
            graphql_config = self.configurations.get("default_graphql_api")
            if graphql_config:
                base_url = f"http://{graphql_config.host}:{graphql_config.port}"
                endpoints["graphql"] = {
                    "base_url": base_url,
                    "endpoint": f"{base_url}/graphql",
                    "playground": f"{base_url}/playground",
                    "schema": self.graphql_server.get_schema()
                }
        
        # WebSocket endpoints
        if self.websocket_server:
            websocket_config = self.configurations.get("default_websocket_api")
            if websocket_config:
                base_url = f"ws://{websocket_config.host}:{websocket_config.port}"
                endpoints["websocket"] = {
                    "base_url": base_url,
                    "endpoint": f"{base_url}/ws",
                    "channels": list(self.websocket_server.channels.keys())
                }
        
        return endpoints
    
    def get_unified_statistics(self) -> Dict[str, Any]:
        """Get unified API statistics."""
        stats = {
            "enabled_interfaces": self.enabled_interfaces,
            "total_configurations": len(self.configurations),
            "servers_running": 0,
            "rest": {},
            "graphql": {},
            "websocket": {}
        }
        
        # REST statistics
        if self.rest_server:
            stats["servers_running"] += 1
            stats["rest"] = {
                "available": True,
                "metrics": self.rest_server.metrics.to_dict(),
                "endpoints_count": len(self.rest_server.endpoints)
            }
        else:
            stats["rest"] = {"available": False}
        
        # GraphQL statistics
        if self.graphql_server:
            stats["servers_running"] += 1
            stats["graphql"] = {
                "available": True,
                "resolvers_count": len(self.graphql_server.resolvers),
                "subscriptions_count": len(self.graphql_server.subscriptions)
            }
        else:
            stats["graphql"] = {"available": False}
        
        # WebSocket statistics
        if self.websocket_server:
            stats["servers_running"] += 1
            stats["websocket"] = {
                "available": True,
                **self.websocket_server.get_connection_statistics()
            }
        else:
            stats["websocket"] = {"available": False}
        
        return stats
    
    async def broadcast_notification(self, channel: str, data: Dict[str, Any]) -> None:
        """
        Broadcast notification to WebSocket subscribers.
        
        Args:
            channel: Channel name
            data: Data to broadcast
        """
        if self.websocket_server:
            await self.websocket_server.broadcast_to_channel(channel, data)
    
    async def notify_analysis_progress(self, analysis_id: str, progress: float, status: str) -> None:
        """Notify analysis progress via WebSocket."""
        await self.broadcast_notification("analysis_progress", {
            "type": "analysis_progress",
            "analysis_id": analysis_id,
            "progress": progress,
            "status": status,
            "timestamp": datetime.now().isoformat()
        })
    
    async def notify_alert_created(self, alert: Any) -> None:
        """Notify alert creation via WebSocket."""
        await self.broadcast_notification("alert_notifications", {
            "type": "alert_created",
            "alert": alert.to_dict() if hasattr(alert, 'to_dict') else str(alert),
            "timestamp": datetime.now().isoformat()
        })
    
    async def notify_quality_gate_result(self, gate_result: Any) -> None:
        """Notify quality gate result via WebSocket."""
        await self.broadcast_notification("quality_gate_results", {
            "type": "quality_gate_evaluated",
            "result": gate_result.to_dict() if hasattr(gate_result, 'to_dict') else str(gate_result),
            "timestamp": datetime.now().isoformat()
        })
    
    async def notify_dashboard_update(self, dashboard_id: str, widget_data: Dict[str, Any]) -> None:
        """Notify dashboard update via WebSocket."""
        await self.broadcast_notification("dashboard_updates", {
            "type": "dashboard_updated",
            "dashboard_id": dashboard_id,
            "widget_data": widget_data,
            "timestamp": datetime.now().isoformat()
        })
    
    def get_api_documentation(self) -> Dict[str, Any]:
        """Get comprehensive API documentation."""
        documentation = {
            "title": "Vibe Check Enterprise API Suite",
            "version": "1.0.0",
            "description": "Unified API suite providing REST, GraphQL, and WebSocket interfaces",
            "interfaces": {},
            "authentication": {
                "type": "API Key",
                "description": "Use X-API-Key header for authentication"
            },
            "rate_limiting": {
                "enabled": True,
                "default_limit": "100 requests per minute"
            }
        }
        
        # Add interface-specific documentation
        if self.rest_server:
            documentation["interfaces"]["rest"] = self.rest_server.get_api_documentation()
        
        if self.graphql_server:
            documentation["interfaces"]["graphql"] = {
                "endpoint": "/graphql",
                "schema": self.graphql_server.get_schema(),
                "playground": "/playground"
            }
        
        if self.websocket_server:
            documentation["interfaces"]["websocket"] = {
                "endpoint": "/ws",
                "channels": list(self.websocket_server.channels.keys()),
                "message_types": [msg_type.value for msg_type in self.websocket_server.message_handlers.keys()]
            }
        
        return documentation
    
    async def cleanup(self) -> None:
        """Cleanup unified API manager."""
        # Stop all servers
        await self.stop_all_servers()
        
        # Cleanup individual servers
        cleanup_tasks = []
        
        if self.rest_server:
            cleanup_tasks.append(self.rest_server.cleanup())
        
        if self.graphql_server:
            cleanup_tasks.append(self.graphql_server.cleanup())
        
        if self.websocket_server:
            cleanup_tasks.append(self.websocket_server.cleanup())
        
        if cleanup_tasks:
            await asyncio.gather(*cleanup_tasks, return_exceptions=True)
        
        # Save configurations
        await self._save_configurations()
        
        logger.info("Unified API manager cleaned up")
