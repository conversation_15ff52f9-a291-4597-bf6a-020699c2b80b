"""
File: vibe_check/enterprise/dashboard/components.py
Purpose: Dashboard component system for interactive widgets
Related Files: vibe_check/enterprise/dashboard/
Dependencies: typing, json, datetime
"""

import json
from datetime import datetime
from abc import ABC, abstractmethod

from vibe_check.core.logging import get_logger
from typing import Any, Dict, List

logger = get_logger(__name__)


class DashboardComponent(ABC):
    """Base class for dashboard components."""
    
    def __init__(self, component_id: str, title: str):
        self.component_id = component_id
        self.title = title
        self.created_at = datetime.now()
        self.last_updated = datetime.now()
    
    @abstractmethod
    async def render(self, context: Dict[str, Any]) -> str:
        """Render component HTML."""
        pass
    
    @abstractmethod
    async def get_data(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Get component data."""
        pass
    
    def update_timestamp(self) -> None:
        """Update last updated timestamp."""
        self.last_updated = datetime.now()


class MetricsComponent(DashboardComponent):
    """Metrics display component."""
    
    def __init__(self, component_id: str, title: str, metrics: List[str]):
        super().__init__(component_id, title)
        self.metrics = metrics
    
    async def render(self, context: Dict[str, Any]) -> str:
        """Render metrics component."""
        data = await self.get_data(context)
        
        metrics_html = []
        for metric_name, metric_data in data.get("metrics", {}).items():
            value = metric_data.get("value", 0)
            unit = metric_data.get("unit", "")
            trend = metric_data.get("trend", "")
            trend_class = "positive" if trend.startswith("+") else "negative" if trend.startswith("-") else ""
            trend_icon = "↗" if trend.startswith("+") else "↘" if trend.startswith("-") else "→"
            
            metric_html = f"""
            <div class="metric-item" data-metric="{metric_name}">
                <div class="metric-label">{metric_name.replace('_', ' ').title()}</div>
                <div class="metric-value">
                    <span class="value">{value}</span>
                    <span class="unit">{unit}</span>
                </div>
                <div class="metric-trend {trend_class}">
                    <span class="trend-icon">{trend_icon}</span>
                    <span class="trend-text">{trend}</span>
                </div>
            </div>
            """
            metrics_html.append(metric_html)
        
        return f"""
        <div class="metrics-card">
            {''.join(metrics_html)}
        </div>
        """
    
    async def get_data(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Get metrics data."""
        vcs_engine = context.get("vcs_engine")
        metrics_data = {}
        
        for metric in self.metrics:
            if metric == "cpu" and vcs_engine and vcs_engine.monitoring_system:
                value = vcs_engine.monitoring_system.metrics_collector.get_latest_value("system.cpu_percent") or 0
                metrics_data[metric] = {
                    "value": f"{value:.1f}",
                    "unit": "%",
                    "trend": "+2.3%" if value > 50 else "-1.2%"
                }
            elif metric == "memory" and vcs_engine and vcs_engine.monitoring_system:
                value = vcs_engine.monitoring_system.metrics_collector.get_latest_value("system.memory_percent") or 0
                metrics_data[metric] = {
                    "value": f"{value:.1f}",
                    "unit": "%",
                    "trend": "+0.8%" if value > 60 else "-0.5%"
                }
            elif metric == "disk" and vcs_engine and vcs_engine.monitoring_system:
                value = vcs_engine.monitoring_system.metrics_collector.get_latest_value("system.disk_percent") or 0
                metrics_data[metric] = {
                    "value": f"{value:.1f}",
                    "unit": "%",
                    "trend": "+0.1%"
                }
            elif metric == "network":
                metrics_data[metric] = {
                    "value": "125",
                    "unit": "Mbps",
                    "trend": "+5.2%"
                }
            elif metric == "quality_score" and vcs_engine:
                # Calculate quality score from analysis results
                score = 85.5  # Mock value
                metrics_data[metric] = {
                    "value": f"{score:.1f}",
                    "unit": "/100",
                    "trend": "+2.1%"
                }
            elif metric == "issues_resolved":
                metrics_data[metric] = {
                    "value": "47",
                    "unit": "today",
                    "trend": "+12%"
                }
            else:
                # Default metric
                metrics_data[metric] = {
                    "value": "0",
                    "unit": "",
                    "trend": "0%"
                }
        
        self.update_timestamp()
        return {
            "metrics": metrics_data,
            "timestamp": self.last_updated.isoformat()
        }


class ChartComponent(DashboardComponent):
    """Chart display component."""
    
    def __init__(self, component_id: str, title: str, chart_config: ChartConfig):
        super().__init__(component_id, title)
        self.chart_config = chart_config
    
    async def render(self, context: Dict[str, Any]) -> str:
        """Render chart component."""
        data = await self.get_data(context)
        chart_data = data.get("chart_data", {})
        chart_options = data.get("chart_options", {})
        
        return f"""
        <div class="chart-container">
            <canvas id="chart-{self.component_id}" class="chart-canvas"></canvas>
        </div>
        <script>
            (function() {{
                const ctx = document.getElementById('chart-{self.component_id}').getContext('2d');
                const chartData = {json.dumps(chart_data)};
                const chartOptions = {json.dumps(chart_options)};
                
                const chart = new Chart(ctx, {{
                    type: '{self.chart_config.chart_type.value}',
                    data: chartData,
                    options: chartOptions
                }});
                
                // Store chart instance for updates
                ctx.canvas.chart = chart;
            }})();
        </script>
        """
    
    async def get_data(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Get chart data."""
        vcs_engine = context.get("vcs_engine")
        data_source = self.chart_config.data_source
        
        # Generate chart data based on data source
        if data_source == "analysis_metrics":
            chart_data = {
                "labels": ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
                "datasets": [{
                    "label": "Files Analyzed",
                    "data": [120, 190, 300, 500, 200, 300, 450],
                    "borderColor": self.chart_config.color_scheme[0],
                    "backgroundColor": self.chart_config.color_scheme[0] + "20",
                    "tension": 0.4
                }]
            }
        elif data_source == "quality_gate_results":
            chart_data = {
                "labels": ["Passed", "Failed", "Warning"],
                "datasets": [{
                    "data": [75, 15, 10],
                    "backgroundColor": self.chart_config.color_scheme[:3],
                    "borderWidth": 2
                }]
            }
        elif data_source == "team_metrics":
            chart_data = {
                "labels": ["Team Alpha", "Team Beta", "Team Gamma", "Team Delta"],
                "datasets": [{
                    "label": "Activity Score",
                    "data": [85, 92, 78, 88],
                    "backgroundColor": self.chart_config.color_scheme[0],
                    "borderColor": self.chart_config.color_scheme[0],
                    "borderWidth": 1
                }]
            }
        elif data_source == "performance_metrics":
            chart_data = {
                "labels": ["Performance Score"],
                "datasets": [{
                    "data": [87],
                    "backgroundColor": [
                        self.chart_config.color_scheme[0] if 87 >= 80 else 
                        self.chart_config.color_scheme[1] if 87 >= 60 else 
                        self.chart_config.color_scheme[2]
                    ],
                    "borderWidth": 0
                }]
            }
        else:
            chart_data = {"labels": [], "datasets": []}
        
        # Chart options
        chart_options = {
            "responsive": True,
            "maintainAspectRatio": False,
            "plugins": {
                "legend": {
                    "display": self.chart_config.show_legend,
                    "position": "top"
                },
                "title": {
                    "display": True,
                    "text": self.chart_config.title,
                    "font": {"size": 14, "weight": "bold"}
                }
            },
            "animation": {
                "duration": 1000 if self.chart_config.animation else 0
            }
        }
        
        # Add scales for non-pie charts
        if self.chart_config.chart_type != ChartType.PIE:
            chart_options["scales"] = {
                "x": {
                    "grid": {"display": self.chart_config.show_grid},
                    "title": {"display": bool(self.chart_config.x_axis), "text": self.chart_config.x_axis}
                },
                "y": {
                    "grid": {"display": self.chart_config.show_grid},
                    "title": {"display": bool(self.chart_config.y_axis), "text": self.chart_config.y_axis}
                }
            }
        
        # Gauge chart specific options
        if self.chart_config.chart_type == ChartType.GAUGE:
            chart_options.update({
                "circumference": 180,
                "rotation": 270,
                "cutout": "80%",
                "plugins": {
                    **chart_options["plugins"],
                    "tooltip": {"enabled": False}
                }
            })
        
        self.update_timestamp()
        return {
            "chart_data": chart_data,
            "chart_options": chart_options,
            "timestamp": self.last_updated.isoformat()
        }


class AlertListComponent(DashboardComponent):
    """Alert list display component."""
    
    def __init__(self, component_id: str, title: str, max_items: int = 10):
        super().__init__(component_id, title)
        self.max_items = max_items
    
    async def render(self, context: Dict[str, Any]) -> str:
        """Render alert list component."""
        data = await self.get_data(context)
        alerts = data.get("alerts", [])
        
        if not alerts:
            return """
            <div class="alert-list-empty">
                <div class="empty-state">
                    <span class="empty-icon">✅</span>
                    <p>No active alerts</p>
                </div>
            </div>
            """
        
        alert_items = []
        for alert in alerts:
            severity_class = f"alert-{alert['severity']}"
            severity_icon = {
                "critical": "🔴",
                "high": "🟠", 
                "medium": "🟡",
                "low": "🟢"
            }.get(alert['severity'], "ℹ️")
            
            alert_html = f"""
            <div class="alert-item {severity_class}">
                <div class="alert-header">
                    <span class="alert-icon">{severity_icon}</span>
                    <span class="alert-title">{alert['title']}</span>
                    <span class="alert-time">{alert['created_at']}</span>
                </div>
                <div class="alert-description">{alert['description']}</div>
            </div>
            """
            alert_items.append(alert_html)
        
        return f"""
        <div class="alert-list">
            {''.join(alert_items)}
        </div>
        """
    
    async def get_data(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Get alert list data."""
        vcs_engine = context.get("vcs_engine")
        alerts = []
        
        if vcs_engine and vcs_engine.monitoring_system:
            active_alerts = vcs_engine.monitoring_system.get_active_alerts()
            for alert in active_alerts[:self.max_items]:
                alerts.append({
                    "id": alert.alert_id,
                    "title": alert.title,
                    "description": alert.description[:100] + "..." if len(alert.description) > 100 else alert.description,
                    "severity": alert.severity.value,
                    "created_at": alert.created_at.strftime("%H:%M")
                })
        else:
            # Mock alerts for demo
            alerts = [
                {
                    "id": "alert_1",
                    "title": "High CPU Usage Detected",
                    "description": "CPU usage has exceeded 85% for the last 5 minutes",
                    "severity": "high",
                    "created_at": "14:30"
                },
                {
                    "id": "alert_2",
                    "title": "Quality Gate Failed",
                    "description": "Security quality gate failed due to 3 critical vulnerabilities",
                    "severity": "critical",
                    "created_at": "14:15"
                }
            ]
        
        self.update_timestamp()
        return {
            "alerts": alerts,
            "total_count": len(alerts),
            "timestamp": self.last_updated.isoformat()
        }


class PipelineStatusComponent(DashboardComponent):
    """CI/CD pipeline status component."""
    
    def __init__(self, component_id: str, title: str, platforms: List[str]):
        super().__init__(component_id, title)
        self.platforms = platforms
    
    async def render(self, context: Dict[str, Any]) -> str:
        """Render pipeline status component."""
        data = await self.get_data(context)
        pipelines = data.get("pipelines", [])
        summary = data.get("summary", {})
        
        if not pipelines:
            return """
            <div class="pipeline-status-empty">
                <div class="empty-state">
                    <span class="empty-icon">🔧</span>
                    <p>No pipelines configured</p>
                </div>
            </div>
            """
        
        # Summary section
        summary_html = f"""
        <div class="pipeline-summary">
            <div class="summary-item success">
                <span class="count">{summary.get('success', 0)}</span>
                <span class="label">Success</span>
            </div>
            <div class="summary-item failed">
                <span class="count">{summary.get('failed', 0)}</span>
                <span class="label">Failed</span>
            </div>
            <div class="summary-item running">
                <span class="count">{summary.get('running', 0)}</span>
                <span class="label">Running</span>
            </div>
        </div>
        """
        
        # Pipeline items
        pipeline_items = []
        for pipeline in pipelines:
            status_class = f"status-{pipeline['status']}"
            status_icon = {
                "success": "✅",
                "failed": "❌",
                "running": "🔄",
                "pending": "⏳"
            }.get(pipeline['status'], "❓")
            
            pipeline_html = f"""
            <div class="pipeline-item {status_class}">
                <div class="pipeline-header">
                    <span class="pipeline-icon">{status_icon}</span>
                    <span class="pipeline-name">{pipeline['name']}</span>
                    <span class="pipeline-platform">{pipeline['platform']}</span>
                </div>
                <div class="pipeline-details">
                    <span class="pipeline-duration">{pipeline['duration']}</span>
                    <span class="pipeline-time">{pipeline['last_run']}</span>
                </div>
            </div>
            """
            pipeline_items.append(pipeline_html)
        
        return f"""
        <div class="pipeline-status">
            {summary_html}
            <div class="pipeline-list">
                {''.join(pipeline_items)}
            </div>
        </div>
        """
    
    async def get_data(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Get pipeline status data."""
        vcs_engine = context.get("vcs_engine")
        pipelines = []
        
        if vcs_engine and vcs_engine.cicd_manager:
            for config in vcs_engine.cicd_manager.configurations.values():
                if not self.platforms or config.platform.value in self.platforms:
                    pipelines.append({
                        "id": config.config_id,
                        "name": config.name,
                        "platform": config.platform.value,
                        "status": "success",  # Mock status
                        "last_run": "2 hours ago",
                        "duration": "2m 34s"
                    })
        else:
            # Mock pipelines for demo
            pipelines = [
                {
                    "id": "pipeline_1",
                    "name": "Main Analysis Pipeline",
                    "platform": "github",
                    "status": "success",
                    "last_run": "1 hour ago",
                    "duration": "3m 12s"
                },
                {
                    "id": "pipeline_2",
                    "name": "Security Scan Pipeline",
                    "platform": "gitlab",
                    "status": "running",
                    "last_run": "5 minutes ago",
                    "duration": "1m 45s"
                }
            ]
        
        # Calculate summary
        summary = {
            "total": len(pipelines),
            "success": len([p for p in pipelines if p["status"] == "success"]),
            "failed": len([p for p in pipelines if p["status"] == "failed"]),
            "running": len([p for p in pipelines if p["status"] == "running"])
        }
        
        self.update_timestamp()
        return {
            "pipelines": pipelines,
            "summary": summary,
            "timestamp": self.last_updated.isoformat()
        }
