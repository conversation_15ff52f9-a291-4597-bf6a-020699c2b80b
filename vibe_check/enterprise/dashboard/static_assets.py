"""
File: vibe_check/enterprise/dashboard/static_assets.py
Purpose: Static asset management for web dashboard
Related Files: vibe_check/enterprise/dashboard/
Dependencies: typing, pathlib
"""

from pathlib import Path
from typing import Dict, List
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class StaticAssetManager:
    """Manages static assets for dashboard."""
    
    def __init__(self):
        self.assets_dir = Path(__file__).parent / "static"
        self.css_files: Dict[str, str] = {}
        self.js_files: Dict[str, str] = {}
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize static asset manager."""
        if self._initialized:
            return
        
        self.assets_dir.mkdir(exist_ok=True)
        (self.assets_dir / "css").mkdir(exist_ok=True)
        (self.assets_dir / "js").mkdir(exist_ok=True)
        (self.assets_dir / "images").mkdir(exist_ok=True)
        
        await self._create_default_assets()
        
        self._initialized = True
        logger.info("Static asset manager initialized")
    
    async def _create_default_assets(self) -> None:
        """Create default static assets."""
        # Create CSS files
        self.css_files["dashboard.css"] = self._get_dashboard_css()
        self.css_files["themes.css"] = self._get_themes_css()
        
        # Create JS files
        self.js_files["dashboard.js"] = self._get_dashboard_js()
        
        # Write files to disk
        for filename, content in self.css_files.items():
            css_file = self.assets_dir / "css" / filename
            with open(css_file, 'w', encoding='utf-8') as f:
                f.write(content)
        
        for filename, content in self.js_files.items():
            js_file = self.assets_dir / "js" / filename
            with open(js_file, 'w', encoding='utf-8') as f:
                f.write(content)
        
        logger.info("Created default static assets")
    
    def _get_dashboard_css(self) -> str:
        """Get dashboard CSS."""
        return """
/* Dashboard Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
}

#app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.dashboard-header {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 2rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.dashboard-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.header-controls {
    display: flex;
    gap: 0.5rem;
}

/* Main Content */
.dashboard-main {
    flex: 1;
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    grid-template-rows: repeat(8, 120px);
    gap: 1rem;
    min-height: 600px;
}

/* Widgets */
.widget-container {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: box-shadow 0.2s ease;
}

.widget-container:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.widget-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.widget-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.widget-controls {
    display: flex;
    gap: 0.25rem;
}

.widget-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.widget-footer {
    margin-top: auto;
    padding-top: 0.5rem;
    border-top: 1px solid var(--border-color);
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* Buttons */
.btn {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.btn:hover {
    background: var(--bg-hover);
}

.btn-icon {
    padding: 0.5rem;
    font-size: 1rem;
}

.widget-refresh, .widget-settings {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    font-size: 0.875rem;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.widget-refresh:hover, .widget-settings:hover {
    opacity: 1;
    background: var(--bg-hover);
}

/* Charts */
.chart-container {
    width: 100%;
    height: 100%;
    position: relative;
}

.chart-canvas {
    max-width: 100%;
    max-height: 100%;
}

/* Metrics Cards */
.metrics-card {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    width: 100%;
}

.metric-item {
    text-align: center;
    padding: 1rem;
    background: var(--bg-tertiary);
    border-radius: 6px;
}

.metric-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.metric-value .unit {
    font-size: 0.875rem;
    font-weight: 400;
    color: var(--text-secondary);
}

.metric-trend {
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
}

.metric-trend.positive {
    color: var(--success-color);
}

.metric-trend.negative {
    color: var(--error-color);
}

/* Footer */
.dashboard-footer {
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    padding: 1rem 2rem;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* Responsive */
@media (max-width: 768px) {
    .dashboard-main {
        padding: 1rem;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
        grid-template-rows: auto;
    }
    
    .widget-container {
        min-height: 200px;
    }
}
        """.strip()
    
    def _get_themes_css(self) -> str:
        """Get theme CSS variables."""
        return """
/* Light Theme */
[data-theme="light"] {
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-hover: #e2e8f0;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --success-color: #10b981;
    --error-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #3b82f6;
}

/* Dark Theme */
[data-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-hover: #475569;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --border-color: #334155;
    --success-color: #10b981;
    --error-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #3b82f6;
}
        """.strip()
    
    def _get_dashboard_js(self) -> str:
        """Get dashboard JavaScript."""
        return """
// Dashboard JavaScript
window.Dashboard = (function() {
    let config = {
        autoRefresh: true,
        refreshInterval: 30000,
        theme: 'light'
    };
    
    let refreshTimer = null;
    
    function init(options = {}) {
        config = { ...config, ...options };
        
        setupEventListeners();
        setupTheme();
        
        if (config.autoRefresh) {
            startAutoRefresh();
        }
        
        console.log('Dashboard initialized');
    }
    
    function setupEventListeners() {
        // Theme toggle
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', toggleTheme);
        }
        
        // Refresh button
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', refreshDashboard);
        }
        
        // Widget refresh buttons
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('widget-refresh')) {
                const widgetId = e.target.closest('.widget-container').dataset.widgetId;
                refreshWidget(widgetId);
            }
        });
    }
    
    function setupTheme() {
        const savedTheme = localStorage.getItem('dashboard-theme') || config.theme;
        setTheme(savedTheme);
    }
    
    function toggleTheme() {
        const currentTheme = document.documentElement.dataset.theme;
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        setTheme(newTheme);
    }
    
    function setTheme(theme) {
        document.documentElement.dataset.theme = theme;
        localStorage.setItem('dashboard-theme', theme);
        config.theme = theme;
    }
    
    function startAutoRefresh() {
        if (refreshTimer) {
            clearInterval(refreshTimer);
        }
        
        refreshTimer = setInterval(refreshDashboard, config.refreshInterval);
    }
    
    function stopAutoRefresh() {
        if (refreshTimer) {
            clearInterval(refreshTimer);
            refreshTimer = null;
        }
    }
    
    function refreshDashboard() {
        console.log('Refreshing dashboard...');
        
        // Update timestamp
        const timestampEl = document.getElementById('last-updated');
        if (timestampEl) {
            timestampEl.textContent = 'Last updated: ' + new Date().toLocaleTimeString();
        }
        
        // Refresh all widgets
        const widgets = document.querySelectorAll('.widget-container');
        widgets.forEach(widget => {
            const widgetId = widget.dataset.widgetId;
            refreshWidget(widgetId);
        });
    }
    
    function refreshWidget(widgetId) {
        console.log('Refreshing widget:', widgetId);
        
        const widget = document.querySelector(`[data-widget-id="${widgetId}"]`);
        if (!widget) return;
        
        // Add loading state
        widget.classList.add('loading');
        
        // Simulate API call
        setTimeout(() => {
            widget.classList.remove('loading');
            
            // Update widget timestamp
            const timestamp = widget.querySelector('.widget-timestamp');
            if (timestamp) {
                timestamp.textContent = new Date().toLocaleTimeString();
            }
        }, 1000);
    }
    
    // Public API
    return {
        init: init,
        refresh: refreshDashboard,
        refreshWidget: refreshWidget,
        setTheme: setTheme,
        startAutoRefresh: startAutoRefresh,
        stopAutoRefresh: stopAutoRefresh
    };
})();
        """.strip()
    
    def get_asset_path(self, asset_type: str, filename: str) -> Path:
        """Get path to static asset."""
        return self.assets_dir / asset_type / filename
    
    def list_assets(self) -> Dict[str, List[str]]:
        """List all available assets."""
        return {
            "css": list(self.css_files.keys()),
            "js": list(self.js_files.keys()),
            "images": []  # Would list image files
        }
