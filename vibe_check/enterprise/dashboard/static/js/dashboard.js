// Dashboard JavaScript
window.Dashboard = (function() {
    let config = {
        autoRefresh: true,
        refreshInterval: 30000,
        theme: 'light'
    };
    
    let refreshTimer = null;
    
    function init(options = {}) {
        config = { ...config, ...options };
        
        setupEventListeners();
        setupTheme();
        
        if (config.autoRefresh) {
            startAutoRefresh();
        }
        
        console.log('Dashboard initialized');
    }
    
    function setupEventListeners() {
        // Theme toggle
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', toggleTheme);
        }
        
        // Refresh button
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', refreshDashboard);
        }
        
        // Widget refresh buttons
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('widget-refresh')) {
                const widgetId = e.target.closest('.widget-container').dataset.widgetId;
                refreshWidget(widgetId);
            }
        });
    }
    
    function setupTheme() {
        const savedTheme = localStorage.getItem('dashboard-theme') || config.theme;
        setTheme(savedTheme);
    }
    
    function toggleTheme() {
        const currentTheme = document.documentElement.dataset.theme;
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        setTheme(newTheme);
    }
    
    function setTheme(theme) {
        document.documentElement.dataset.theme = theme;
        localStorage.setItem('dashboard-theme', theme);
        config.theme = theme;
    }
    
    function startAutoRefresh() {
        if (refreshTimer) {
            clearInterval(refreshTimer);
        }
        
        refreshTimer = setInterval(refreshDashboard, config.refreshInterval);
    }
    
    function stopAutoRefresh() {
        if (refreshTimer) {
            clearInterval(refreshTimer);
            refreshTimer = null;
        }
    }
    
    function refreshDashboard() {
        console.log('Refreshing dashboard...');
        
        // Update timestamp
        const timestampEl = document.getElementById('last-updated');
        if (timestampEl) {
            timestampEl.textContent = 'Last updated: ' + new Date().toLocaleTimeString();
        }
        
        // Refresh all widgets
        const widgets = document.querySelectorAll('.widget-container');
        widgets.forEach(widget => {
            const widgetId = widget.dataset.widgetId;
            refreshWidget(widgetId);
        });
    }
    
    function refreshWidget(widgetId) {
        console.log('Refreshing widget:', widgetId);
        
        const widget = document.querySelector(`[data-widget-id="${widgetId}"]`);
        if (!widget) return;
        
        // Add loading state
        widget.classList.add('loading');
        
        // Simulate API call
        setTimeout(() => {
            widget.classList.remove('loading');
            
            // Update widget timestamp
            const timestamp = widget.querySelector('.widget-timestamp');
            if (timestamp) {
                timestamp.textContent = new Date().toLocaleTimeString();
            }
        }, 1000);
    }
    
    // Public API
    return {
        init: init,
        refresh: refreshDashboard,
        refreshWidget: refreshWidget,
        setTheme: setTheme,
        startAutoRefresh: startAutoRefresh,
        stopAutoRefresh: stopAutoRefresh
    };
})();