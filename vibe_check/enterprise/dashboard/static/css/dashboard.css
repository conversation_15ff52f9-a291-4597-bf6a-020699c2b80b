/* Dashboard Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
}

#app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.dashboard-header {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 2rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.dashboard-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.header-controls {
    display: flex;
    gap: 0.5rem;
}

/* Main Content */
.dashboard-main {
    flex: 1;
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    grid-template-rows: repeat(8, 120px);
    gap: 1rem;
    min-height: 600px;
}

/* Widgets */
.widget-container {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: box-shadow 0.2s ease;
}

.widget-container:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.widget-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.widget-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.widget-controls {
    display: flex;
    gap: 0.25rem;
}

.widget-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.widget-footer {
    margin-top: auto;
    padding-top: 0.5rem;
    border-top: 1px solid var(--border-color);
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* Buttons */
.btn {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.btn:hover {
    background: var(--bg-hover);
}

.btn-icon {
    padding: 0.5rem;
    font-size: 1rem;
}

.widget-refresh, .widget-settings {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    font-size: 0.875rem;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.widget-refresh:hover, .widget-settings:hover {
    opacity: 1;
    background: var(--bg-hover);
}

/* Charts */
.chart-container {
    width: 100%;
    height: 100%;
    position: relative;
}

.chart-canvas {
    max-width: 100%;
    max-height: 100%;
}

/* Metrics Cards */
.metrics-card {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    width: 100%;
}

.metric-item {
    text-align: center;
    padding: 1rem;
    background: var(--bg-tertiary);
    border-radius: 6px;
}

.metric-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.metric-value .unit {
    font-size: 0.875rem;
    font-weight: 400;
    color: var(--text-secondary);
}

.metric-trend {
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
}

.metric-trend.positive {
    color: var(--success-color);
}

.metric-trend.negative {
    color: var(--error-color);
}

/* Footer */
.dashboard-footer {
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    padding: 1rem 2rem;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* Responsive */
@media (max-width: 768px) {
    .dashboard-main {
        padding: 1rem;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
        grid-template-rows: auto;
    }
    
    .widget-container {
        min-height: 200px;
    }
}