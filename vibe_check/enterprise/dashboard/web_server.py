"""
File: vibe_check/enterprise/dashboard/web_server.py
Purpose: Web dashboard server with interactive visualizations
Related Files: vibe_check/enterprise/dashboard/
Dependencies: typing, asyncio, pathlib, json
"""

import json
from pathlib import Path
from datetime import datetime
import uuid

from .models import (
    DashboardConfig, WidgetConfig, ChartConfig, UserSession,
    DashboardTheme, LayoutConfig, ChartType, WidgetSize, DashboardMetrics
)
from .templates import TemplateManager
from .static_assets import StaticAssetManager
from vibe_check.core.logging import get_logger
from typing import Any, Dict

logger = get_logger(__name__)


class WebDashboardServer:
    """Web dashboard server for Vibe Check enterprise."""
    
    def __init__(self, vcs_engine: Any, host: str = "localhost", port: int = 8080):
        """
        Initialize web dashboard server.
        
        Args:
            vcs_engine: VCS engine instance
            host: Server host
            port: Server port
        """
        self.vcs_engine = vcs_engine
        self.host = host
        self.port = port
        
        # Dashboard components
        self.dashboards: Dict[str, DashboardConfig] = {}
        self.user_sessions: Dict[str, UserSession] = {}
        self.template_manager = TemplateManager()
        self.static_assets = StaticAssetManager()
        self.metrics = DashboardMetrics()
        
        # Server state
        self._running = False
        self._initialized = False
        self.data_dir = Path.cwd() / "dashboard_data"
        self.data_dir.mkdir(parents=True, exist_ok=True)
    
    async def initialize(self) -> None:
        """Initialize web dashboard server."""
        if self._initialized:
            return
        
        await self.template_manager.initialize()
        await self.static_assets.initialize()
        await self._load_dashboards()
        await self._create_default_dashboard()
        
        self._initialized = True
        logger.info("Web dashboard server initialized")
    
    async def _load_dashboards(self) -> None:
        """Load dashboard configurations."""
        dashboards_file = self.data_dir / "dashboards.json"
        if dashboards_file.exists():
            try:
                with open(dashboards_file, 'r', encoding='utf-8') as f:
                    dashboards_data = json.load(f)
                
                for dashboard_data in dashboards_data:
                    dashboard = self._dashboard_from_dict(dashboard_data)
                    self.dashboards[dashboard.dashboard_id] = dashboard
                
                logger.debug(f"Loaded {len(self.dashboards)} dashboards")
                
            except Exception as e:
                logger.error(f"Failed to load dashboards: {e}")
    
    async def _save_dashboards(self) -> None:
        """Save dashboard configurations."""
        dashboards_file = self.data_dir / "dashboards.json"
        try:
            dashboards_data = [dashboard.to_dict() for dashboard in self.dashboards.values()]
            with open(dashboards_file, 'w', encoding='utf-8') as f:
                json.dump(dashboards_data, f, indent=2)
            
            logger.debug("Saved dashboard configurations")
            
        except Exception as e:
            logger.error(f"Failed to save dashboards: {e}")
    
    def _dashboard_from_dict(self, data: Dict[str, Any]) -> DashboardConfig:
        """Create DashboardConfig from dictionary."""
        # Parse layout
        layout_data = data.get("layout", {})
        layout = LayoutConfig(
            grid_columns=layout_data.get("grid_columns", 12),
            grid_rows=layout_data.get("grid_rows", 8),
            widget_margin=layout_data.get("widget_margin", 10),
            responsive=layout_data.get("responsive", True),
            breakpoints=layout_data.get("breakpoints", {})
        )
        
        # Parse widgets
        widgets = []
        for widget_data in data.get("widgets", []):
            # Parse chart config if present
            chart_config = None
            if widget_data.get("chart_config"):
                chart_data = widget_data["chart_config"]
                chart_config = ChartConfig(
                    chart_type=ChartType(chart_data["chart_type"]),
                    title=chart_data["title"],
                    data_source=chart_data["data_source"],
                    x_axis=chart_data.get("x_axis", ""),
                    y_axis=chart_data.get("y_axis", ""),
                    color_scheme=chart_data.get("color_scheme", []),
                    show_legend=chart_data.get("show_legend", True),
                    show_grid=chart_data.get("show_grid", True),
                    animation=chart_data.get("animation", True),
                    refresh_interval=chart_data.get("refresh_interval", 30)
                )
            
            widget = WidgetConfig(
                widget_id=widget_data["widget_id"],
                title=widget_data["title"],
                widget_type=widget_data["widget_type"],
                size=WidgetSize(widget_data["size"]),
                position=widget_data["position"],
                chart_config=chart_config,
                data_config=widget_data.get("data_config", {}),
                style_config=widget_data.get("style_config", {}),
                enabled=widget_data.get("enabled", True)
            )
            widgets.append(widget)
        
        return DashboardConfig(
            dashboard_id=data["dashboard_id"],
            name=data["name"],
            description=data["description"],
            theme=DashboardTheme(data.get("theme", "light")),
            layout=layout,
            widgets=widgets,
            permissions=data.get("permissions", {}),
            auto_refresh=data.get("auto_refresh", True),
            refresh_interval=data.get("refresh_interval", 30),
            created_at=datetime.fromisoformat(data["created_at"]),
            updated_at=datetime.fromisoformat(data["updated_at"])
        )
    
    async def _create_default_dashboard(self) -> None:
        """Create default dashboard if none exist."""
        if self.dashboards:
            return
        
        # Create default widgets
        widgets = [
            WidgetConfig(
                widget_id="system_overview",
                title="System Overview",
                widget_type="metrics_card",
                size=WidgetSize.LARGE,
                position={"x": 0, "y": 0, "width": 4, "height": 2},
                data_config={"metrics": ["cpu", "memory", "disk", "network"]}
            ),
            WidgetConfig(
                widget_id="analysis_trends",
                title="Analysis Trends",
                widget_type="chart",
                size=WidgetSize.WIDE,
                position={"x": 4, "y": 0, "width": 8, "height": 2},
                chart_config=ChartConfig(
                    chart_type=ChartType.LINE,
                    title="Analysis Trends",
                    data_source="analysis_metrics",
                    x_axis="timestamp",
                    y_axis="count"
                )
            ),
            WidgetConfig(
                widget_id="quality_gates",
                title="Quality Gates Status",
                widget_type="chart",
                size=WidgetSize.MEDIUM,
                position={"x": 0, "y": 2, "width": 6, "height": 2},
                chart_config=ChartConfig(
                    chart_type=ChartType.PIE,
                    title="Quality Gates",
                    data_source="quality_gate_results"
                )
            ),
            WidgetConfig(
                widget_id="active_alerts",
                title="Active Alerts",
                widget_type="alert_list",
                size=WidgetSize.MEDIUM,
                position={"x": 6, "y": 2, "width": 6, "height": 2},
                data_config={"max_items": 10, "severity_filter": ["high", "critical"]}
            ),
            WidgetConfig(
                widget_id="team_activity",
                title="Team Activity",
                widget_type="chart",
                size=WidgetSize.WIDE,
                position={"x": 0, "y": 4, "width": 12, "height": 2},
                chart_config=ChartConfig(
                    chart_type=ChartType.BAR,
                    title="Team Activity",
                    data_source="team_metrics",
                    x_axis="team",
                    y_axis="activity"
                )
            ),
            WidgetConfig(
                widget_id="cicd_pipelines",
                title="CI/CD Pipeline Status",
                widget_type="pipeline_status",
                size=WidgetSize.LARGE,
                position={"x": 0, "y": 6, "width": 6, "height": 2},
                data_config={"platforms": ["github", "gitlab", "jenkins", "azure"]}
            ),
            WidgetConfig(
                widget_id="performance_metrics",
                title="Performance Metrics",
                widget_type="chart",
                size=WidgetSize.LARGE,
                position={"x": 6, "y": 6, "width": 6, "height": 2},
                chart_config=ChartConfig(
                    chart_type=ChartType.GAUGE,
                    title="Performance",
                    data_source="performance_metrics"
                )
            )
        ]
        
        default_dashboard = DashboardConfig(
            dashboard_id="enterprise_overview",
            name="Enterprise Overview",
            description="Comprehensive overview of Vibe Check enterprise features and metrics",
            theme=DashboardTheme.LIGHT,
            widgets=widgets,
            permissions={"admin": ["read", "write"], "user": ["read"]}
        )
        
        self.dashboards[default_dashboard.dashboard_id] = default_dashboard
        await self._save_dashboards()
        logger.info("Created default enterprise dashboard")
    
    async def start(self) -> None:
        """Start web dashboard server."""
        if self._running:
            return
        
        self._running = True
        # In a real implementation, this would start an actual web server
        # For now, we'll simulate the server running
        logger.info(f"Web dashboard server started on http://{self.host}:{self.port}")
    
    async def stop(self) -> None:
        """Stop web dashboard server."""
        self._running = False
        logger.info("Web dashboard server stopped")
    
    async def create_user_session(self, user_id: str, username: str, role: str) -> UserSession:
        """
        Create a new user session.
        
        Args:
            user_id: User ID
            username: Username
            role: User role
            
        Returns:
            Created user session
        """
        session_id = str(uuid.uuid4())
        
        # Define role-based permissions
        permissions = []
        if role == "admin":
            permissions = ["read", "write", "delete", "manage_users", "manage_dashboards"]
        elif role == "lead":
            permissions = ["read", "write", "manage_dashboards"]
        elif role == "developer":
            permissions = ["read", "write"]
        else:  # viewer
            permissions = ["read"]
        
        session = UserSession(
            session_id=session_id,
            user_id=user_id,
            username=username,
            role=role,
            permissions=permissions
        )
        
        self.user_sessions[session_id] = session
        self.metrics.unique_users += 1
        
        logger.info(f"Created user session for {username} ({role})")
        return session
    
    async def get_dashboard_data(self, dashboard_id: str, session_id: str) -> Dict[str, Any]:
        """
        Get dashboard data for rendering.
        
        Args:
            dashboard_id: Dashboard ID
            session_id: User session ID
            
        Returns:
            Dashboard data with widget content
        """
        # Verify session
        session = self.user_sessions.get(session_id)
        if not session or not session.active:
            raise ValueError("Invalid or expired session")
        
        # Get dashboard
        dashboard = self.dashboards.get(dashboard_id)
        if not dashboard:
            raise ValueError(f"Dashboard {dashboard_id} not found")
        
        # Check permissions
        user_permissions = dashboard.permissions.get(session.role, [])
        if "read" not in user_permissions:
            raise ValueError("Insufficient permissions")
        
        # Update session activity
        session.last_activity = datetime.now()
        self.metrics.total_views += 1
        
        # Generate widget data
        widget_data = {}
        for widget in dashboard.widgets:
            if widget.enabled:
                widget_data[widget.widget_id] = await self._get_widget_data(widget)
        
        return {
            "dashboard": dashboard.to_dict(),
            "widgets": widget_data,
            "session": session.to_dict(),
            "timestamp": datetime.now().isoformat()
        }
    
    async def _get_widget_data(self, widget: WidgetConfig) -> Dict[str, Any]:
        """Get data for a specific widget."""
        widget_type = widget.widget_type
        
        if widget_type == "metrics_card":
            return await self._get_metrics_card_data(widget)
        elif widget_type == "chart":
            return await self._get_chart_data(widget)
        elif widget_type == "alert_list":
            return await self._get_alert_list_data(widget)
        elif widget_type == "pipeline_status":
            return await self._get_pipeline_status_data(widget)
        else:
            return {"error": f"Unknown widget type: {widget_type}"}
    
    async def _get_metrics_card_data(self, widget: WidgetConfig) -> Dict[str, Any]:
        """Get metrics card data."""
        metrics = widget.data_config.get("metrics", [])
        data = {}
        
        for metric in metrics:
            if metric == "cpu" and self.vcs_engine.monitoring_system:
                data["cpu"] = self.vcs_engine.monitoring_system.metrics_collector.get_latest_value("system.cpu_percent") or 0
            elif metric == "memory" and self.vcs_engine.monitoring_system:
                data["memory"] = self.vcs_engine.monitoring_system.metrics_collector.get_latest_value("system.memory_percent") or 0
            elif metric == "disk" and self.vcs_engine.monitoring_system:
                data["disk"] = self.vcs_engine.monitoring_system.metrics_collector.get_latest_value("system.disk_percent") or 0
            else:
                data[metric] = 0  # Default value
        
        return {
            "type": "metrics_card",
            "data": data,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _get_chart_data(self, widget: WidgetConfig) -> Dict[str, Any]:
        """Get chart data."""
        if not widget.chart_config:
            return {"error": "No chart configuration"}
        
        chart_config = widget.chart_config
        data_source = chart_config.data_source
        
        # Generate mock data based on data source
        if data_source == "analysis_metrics":
            data = {
                "labels": ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
                "datasets": [{
                    "label": "Files Analyzed",
                    "data": [120, 190, 300, 500, 200, 300, 450],
                    "borderColor": chart_config.color_scheme[0],
                    "backgroundColor": chart_config.color_scheme[0] + "20"
                }]
            }
        elif data_source == "quality_gate_results":
            data = {
                "labels": ["Passed", "Failed", "Warning"],
                "datasets": [{
                    "data": [75, 15, 10],
                    "backgroundColor": chart_config.color_scheme[:3]
                }]
            }
        elif data_source == "team_metrics":
            data = {
                "labels": ["Team A", "Team B", "Team C", "Team D"],
                "datasets": [{
                    "label": "Activity Score",
                    "data": [85, 92, 78, 88],
                    "backgroundColor": chart_config.color_scheme[0]
                }]
            }
        else:
            data = {"labels": [], "datasets": []}
        
        return {
            "type": "chart",
            "chart_type": chart_config.chart_type.value,
            "data": data,
            "options": {
                "responsive": True,
                "plugins": {
                    "legend": {"display": chart_config.show_legend},
                    "title": {"display": True, "text": chart_config.title}
                },
                "scales": {
                    "x": {"grid": {"display": chart_config.show_grid}},
                    "y": {"grid": {"display": chart_config.show_grid}}
                },
                "animation": {"duration": 1000 if chart_config.animation else 0}
            },
            "timestamp": datetime.now().isoformat()
        }
    
    async def _get_alert_list_data(self, widget: WidgetConfig) -> Dict[str, Any]:
        """Get alert list data."""
        max_items = widget.data_config.get("max_items", 10)
        severity_filter = widget.data_config.get("severity_filter", [])
        
        # Get alerts from monitoring system
        alerts = []
        if self.vcs_engine.monitoring_system:
            active_alerts = self.vcs_engine.monitoring_system.get_active_alerts()
            for alert in active_alerts[:max_items]:
                if not severity_filter or alert.severity.value in severity_filter:
                    alerts.append({
                        "id": alert.alert_id,
                        "title": alert.title,
                        "severity": alert.severity.value,
                        "created_at": alert.created_at.isoformat(),
                        "description": alert.description[:100] + "..." if len(alert.description) > 100 else alert.description
                    })
        
        return {
            "type": "alert_list",
            "alerts": alerts,
            "total_count": len(alerts),
            "timestamp": datetime.now().isoformat()
        }
    
    async def _get_pipeline_status_data(self, widget: WidgetConfig) -> Dict[str, Any]:
        """Get CI/CD pipeline status data."""
        platforms = widget.data_config.get("platforms", [])
        
        pipelines = []
        if self.vcs_engine.cicd_manager:
            for config in self.vcs_engine.cicd_manager.configurations.values():
                if not platforms or config.platform.value in platforms:
                    pipelines.append({
                        "id": config.config_id,
                        "name": config.name,
                        "platform": config.platform.value,
                        "status": "success",  # Mock status
                        "last_run": datetime.now().isoformat(),
                        "duration": "2m 34s"
                    })
        
        return {
            "type": "pipeline_status",
            "pipelines": pipelines,
            "summary": {
                "total": len(pipelines),
                "success": len([p for p in pipelines if p["status"] == "success"]),
                "failed": len([p for p in pipelines if p["status"] == "failed"]),
                "running": len([p for p in pipelines if p["status"] == "running"])
            },
            "timestamp": datetime.now().isoformat()
        }
    
    def get_dashboard_statistics(self) -> Dict[str, Any]:
        """Get dashboard usage statistics."""
        active_sessions = sum(1 for session in self.user_sessions.values() if session.active)
        
        return {
            "total_dashboards": len(self.dashboards),
            "active_sessions": active_sessions,
            "total_users": len(self.user_sessions),
            "server_running": self._running,
            "metrics": self.metrics.to_dict(),
            "uptime": "24h 15m",  # Mock uptime
            "version": "1.0.0"
        }
    
    async def cleanup(self) -> None:
        """Cleanup web dashboard server."""
        await self.stop()
        await self._save_dashboards()
        logger.info("Web dashboard server cleaned up")
