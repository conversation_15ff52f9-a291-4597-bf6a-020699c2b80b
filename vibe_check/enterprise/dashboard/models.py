"""
File: vibe_check/enterprise/dashboard/models.py
Purpose: Data models for web dashboard system
Related Files: vibe_check/enterprise/dashboard/
Dependencies: typing, datetime, enum, dataclasses
"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum


class DashboardTheme(Enum):
    """Dashboard theme options."""
    LIGHT = "light"
    DARK = "dark"
    AUTO = "auto"


class ChartType(Enum):
    """Chart type options."""
    LINE = "line"
    BAR = "bar"
    PIE = "pie"
    AREA = "area"
    SCATTER = "scatter"
    GAUGE = "gauge"
    HEATMAP = "heatmap"
    TABLE = "table"


class WidgetSize(Enum):
    """Widget size options."""
    SMALL = "small"      # 1x1
    MEDIUM = "medium"    # 2x1
    LARGE = "large"      # 2x2
    WIDE = "wide"        # 3x1
    TALL = "tall"        # 1x3
    EXTRA_LARGE = "xl"   # 3x2


@dataclass
class LayoutConfig:
    """Dashboard layout configuration."""
    grid_columns: int = 12
    grid_rows: int = 8
    widget_margin: int = 10
    responsive: bool = True
    breakpoints: Dict[str, int] = field(default_factory=lambda: {
        "xs": 480,
        "sm": 768,
        "md": 1024,
        "lg": 1280,
        "xl": 1920
    })
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "grid_columns": self.grid_columns,
            "grid_rows": self.grid_rows,
            "widget_margin": self.widget_margin,
            "responsive": self.responsive,
            "breakpoints": self.breakpoints
        }


@dataclass
class ChartConfig:
    """Chart configuration."""
    chart_type: ChartType
    title: str
    data_source: str
    x_axis: str = ""
    y_axis: str = ""
    color_scheme: List[str] = field(default_factory=lambda: [
        "#3B82F6", "#EF4444", "#10B981", "#F59E0B", "#8B5CF6"
    ])
    show_legend: bool = True
    show_grid: bool = True
    animation: bool = True
    refresh_interval: int = 30  # seconds
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "chart_type": self.chart_type.value,
            "title": self.title,
            "data_source": self.data_source,
            "x_axis": self.x_axis,
            "y_axis": self.y_axis,
            "color_scheme": self.color_scheme,
            "show_legend": self.show_legend,
            "show_grid": self.show_grid,
            "animation": self.animation,
            "refresh_interval": self.refresh_interval
        }


@dataclass
class WidgetConfig:
    """Widget configuration."""
    widget_id: str
    title: str
    widget_type: str
    size: WidgetSize
    position: Dict[str, int]  # x, y, width, height
    chart_config: Optional[ChartConfig] = None
    data_config: Dict[str, Any] = field(default_factory=dict)
    style_config: Dict[str, Any] = field(default_factory=dict)
    enabled: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "widget_id": self.widget_id,
            "title": self.title,
            "widget_type": self.widget_type,
            "size": self.size.value,
            "position": self.position,
            "chart_config": self.chart_config.to_dict() if self.chart_config else None,
            "data_config": self.data_config,
            "style_config": self.style_config,
            "enabled": self.enabled
        }


@dataclass
class DashboardConfig:
    """Dashboard configuration."""
    dashboard_id: str
    name: str
    description: str
    theme: DashboardTheme = DashboardTheme.LIGHT
    layout: LayoutConfig = field(default_factory=LayoutConfig)
    widgets: List[WidgetConfig] = field(default_factory=list)
    permissions: Dict[str, List[str]] = field(default_factory=dict)
    auto_refresh: bool = True
    refresh_interval: int = 30  # seconds
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "dashboard_id": self.dashboard_id,
            "name": self.name,
            "description": self.description,
            "theme": self.theme.value,
            "layout": self.layout.to_dict(),
            "widgets": [widget.to_dict() for widget in self.widgets],
            "permissions": self.permissions,
            "auto_refresh": self.auto_refresh,
            "refresh_interval": self.refresh_interval,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }


@dataclass
class UserSession:
    """User session for dashboard access."""
    session_id: str
    user_id: str
    username: str
    role: str
    permissions: List[str] = field(default_factory=list)
    dashboard_preferences: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    last_activity: datetime = field(default_factory=datetime.now)
    active: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "session_id": self.session_id,
            "user_id": self.user_id,
            "username": self.username,
            "role": self.role,
            "permissions": self.permissions,
            "dashboard_preferences": self.dashboard_preferences,
            "created_at": self.created_at.isoformat(),
            "last_activity": self.last_activity.isoformat(),
            "active": self.active
        }


@dataclass
class DashboardMetrics:
    """Dashboard usage metrics."""
    total_views: int = 0
    unique_users: int = 0
    average_session_duration: float = 0.0
    most_viewed_widgets: List[str] = field(default_factory=list)
    peak_concurrent_users: int = 0
    error_rate: float = 0.0
    load_time_avg: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "total_views": self.total_views,
            "unique_users": self.unique_users,
            "average_session_duration": self.average_session_duration,
            "most_viewed_widgets": self.most_viewed_widgets,
            "peak_concurrent_users": self.peak_concurrent_users,
            "error_rate": self.error_rate,
            "load_time_avg": self.load_time_avg
        }


@dataclass
class DashboardAlert:
    """Dashboard alert configuration."""
    alert_id: str
    widget_id: str
    condition: str
    threshold: Union[int, float]
    message: str
    severity: str = "medium"
    enabled: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "alert_id": self.alert_id,
            "widget_id": self.widget_id,
            "condition": self.condition,
            "threshold": self.threshold,
            "message": self.message,
            "severity": self.severity,
            "enabled": self.enabled
        }


@dataclass
class DashboardExport:
    """Dashboard export configuration."""
    export_id: str
    dashboard_id: str
    format: str  # pdf, png, html, json
    options: Dict[str, Any] = field(default_factory=dict)
    scheduled: bool = False
    schedule_cron: Optional[str] = None
    recipients: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "export_id": self.export_id,
            "dashboard_id": self.dashboard_id,
            "format": self.format,
            "options": self.options,
            "scheduled": self.scheduled,
            "schedule_cron": self.schedule_cron,
            "recipients": self.recipients
        }
