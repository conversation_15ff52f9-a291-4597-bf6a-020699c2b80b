"""
File: vibe_check/enterprise/performance/performance_optimizer.py
Purpose: Enterprise performance optimization coordinator
Related Files: vibe_check/enterprise/performance/
Dependencies: typing, asyncio, dataclasses, enum
"""

import asyncio
from typing import Dict, List, Optional, Any, Callable
from enum import Enum

from .cache_manager import AdvancedCacheManager, CacheStrategy
from .distributed_processor import DistributedProcessor
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class OptimizationProfile(Enum):
    """Performance optimization profiles."""
    BALANCED = "balanced"
    THROUGHPUT = "throughput"
    LATENCY = "latency"
    MEMORY = "memory"
    CUSTOM = "custom"


@dataclass
class PerformanceMetrics:
    """Performance metrics tracking."""
    requests_per_second: float = 0.0
    avg_response_time: float = 0.0  # milliseconds
    memory_usage: float = 0.0  # MB
    cpu_usage: float = 0.0  # percentage
    cache_hit_rate: float = 0.0  # percentage
    error_rate: float = 0.0  # percentage
    throughput: float = 0.0  # operations per second
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "requests_per_second": self.requests_per_second,
            "avg_response_time": self.avg_response_time,
            "memory_usage": self.memory_usage,
            "cpu_usage": self.cpu_usage,
            "cache_hit_rate": self.cache_hit_rate,
            "error_rate": self.error_rate,
            "throughput": self.throughput
        }


@dataclass
class OptimizationConfig:
    """Performance optimization configuration."""
    profile: OptimizationProfile = OptimizationProfile.BALANCED
    cache_strategy: CacheStrategy = CacheStrategy.ADAPTIVE
    max_cache_size: int = 10000
    max_cache_memory: int = 500 * 1024 * 1024  # 500MB
    distributed_processing: bool = True
    max_concurrent_tasks: int = 10
    auto_scaling: bool = True
    monitoring_interval: int = 30  # seconds
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "profile": self.profile.value,
            "cache_strategy": self.cache_strategy.value,
            "max_cache_size": self.max_cache_size,
            "max_cache_memory": self.max_cache_memory,
            "distributed_processing": self.distributed_processing,
            "max_concurrent_tasks": self.max_concurrent_tasks,
            "auto_scaling": self.auto_scaling,
            "monitoring_interval": self.monitoring_interval
        }


class PerformanceOptimizer:
    """Enterprise performance optimization coordinator."""
    
    def __init__(self, config: Optional[OptimizationConfig] = None):
        """
        Initialize performance optimizer.
        
        Args:
            config: Optimization configuration
        """
        self.config = config or OptimizationConfig()
        
        # Performance components
        self.cache_manager: Optional[AdvancedCacheManager] = None
        self.distributed_processor: Optional[DistributedProcessor] = None
        
        # Metrics tracking
        self.metrics = PerformanceMetrics()
        self.metrics_history: List[PerformanceMetrics] = []
        
        # Optimization state
        self._monitoring_task: Optional[asyncio.Task] = None
        self._optimization_task: Optional[asyncio.Task] = None
        self._initialized = False
        self._running = False
    
    async def initialize(self) -> None:
        """Initialize performance optimizer."""
        if self._initialized:
            return
        
        # Initialize cache manager
        self.cache_manager = AdvancedCacheManager(
            max_size=self.config.max_cache_size,
            max_memory=self.config.max_cache_memory,
            strategy=self.config.cache_strategy
        )
        await self.cache_manager.initialize()
        
        # Initialize distributed processor if enabled
        if self.config.distributed_processing:
            self.distributed_processor = DistributedProcessor(
                node_name="optimizer",
                node_capacity=self.config.max_concurrent_tasks
            )
            await self.distributed_processor.initialize()
        
        # Apply optimization profile
        await self._apply_optimization_profile()
        
        # Start monitoring
        self._running = True
        self._monitoring_task = asyncio.create_task(self._monitoring_loop())
        self._optimization_task = asyncio.create_task(self._optimization_loop())
        
        self._initialized = True
        logger.info(f"Performance optimizer initialized (profile: {self.config.profile.value})")
    
    async def _apply_optimization_profile(self) -> None:
        """Apply optimization settings based on profile."""
        if self.config.profile == OptimizationProfile.THROUGHPUT:
            # Optimize for maximum throughput
            if self.cache_manager:
                self.cache_manager.strategy = CacheStrategy.LFU
                self.cache_manager.max_size = self.config.max_cache_size * 2
            
            if self.distributed_processor:
                self.distributed_processor.local_node.capacity = self.config.max_concurrent_tasks * 2
        
        elif self.config.profile == OptimizationProfile.LATENCY:
            # Optimize for low latency
            if self.cache_manager:
                self.cache_manager.strategy = CacheStrategy.LRU
                self.cache_manager.default_ttl = 1800  # 30 minutes
            
            if self.distributed_processor:
                self.distributed_processor.local_node.capacity = max(1, self.config.max_concurrent_tasks // 2)
        
        elif self.config.profile == OptimizationProfile.MEMORY:
            # Optimize for memory usage
            if self.cache_manager:
                self.cache_manager.strategy = CacheStrategy.TTL
                self.cache_manager.max_memory = self.config.max_cache_memory // 2
                self.cache_manager.default_ttl = 900  # 15 minutes
        
        # BALANCED and CUSTOM use default settings
        logger.debug(f"Applied optimization profile: {self.config.profile.value}")
    
    async def _monitoring_loop(self) -> None:
        """Performance monitoring loop."""
        while self._running:
            try:
                await self._collect_metrics()
                await asyncio.sleep(self.config.monitoring_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Monitoring loop error: {e}")
                await asyncio.sleep(self.config.monitoring_interval)
    
    async def _optimization_loop(self) -> None:
        """Automatic optimization loop."""
        while self._running:
            try:
                await self._auto_optimize()
                await asyncio.sleep(self.config.monitoring_interval * 2)  # Less frequent than monitoring
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Optimization loop error: {e}")
                await asyncio.sleep(self.config.monitoring_interval * 2)
    
    async def _collect_metrics(self) -> None:
        """Collect performance metrics."""
        try:
            # Cache metrics
            if self.cache_manager:
                cache_stats = self.cache_manager.get_statistics()
                self.metrics.cache_hit_rate = cache_stats["metrics"]["hit_rate"]
                self.metrics.memory_usage = cache_stats["memory_usage_mb"]
            
            # Distributed processing metrics
            if self.distributed_processor:
                proc_stats = self.distributed_processor.get_statistics()
                node_stats = proc_stats["local_node"]
                self.metrics.cpu_usage = node_stats["utilization"]
                
                # Calculate throughput
                if node_stats["total_tasks_processed"] > 0:
                    self.metrics.throughput = 1.0 / node_stats["avg_processing_time"] if node_stats["avg_processing_time"] > 0 else 0.0
            
            # Store metrics history
            self.metrics_history.append(self.metrics)
            
            # Keep only last 100 metrics
            if len(self.metrics_history) > 100:
                self.metrics_history = self.metrics_history[-100:]
            
        except Exception as e:
            logger.error(f"Failed to collect metrics: {e}")
    
    async def _auto_optimize(self) -> None:
        """Automatic performance optimization."""
        if not self.config.auto_scaling or len(self.metrics_history) < 5:
            return
        
        try:
            # Analyze recent metrics
            recent_metrics = self.metrics_history[-5:]
            avg_cache_hit_rate = sum(m.cache_hit_rate for m in recent_metrics) / len(recent_metrics)
            avg_cpu_usage = sum(m.cpu_usage for m in recent_metrics) / len(recent_metrics)
            avg_memory_usage = sum(m.memory_usage for m in recent_metrics) / len(recent_metrics)
            
            # Cache optimization
            if self.cache_manager and avg_cache_hit_rate < 70:
                # Low cache hit rate - increase cache size
                new_size = min(self.cache_manager.max_size * 1.2, self.config.max_cache_size * 2)
                self.cache_manager.max_size = int(new_size)
                logger.info(f"Increased cache size to {self.cache_manager.max_size} due to low hit rate")
            
            # CPU optimization
            if self.distributed_processor and avg_cpu_usage > 80:
                # High CPU usage - reduce concurrent tasks
                current_capacity = self.distributed_processor.local_node.capacity
                new_capacity = max(1, int(current_capacity * 0.8))
                self.distributed_processor.local_node.capacity = new_capacity
                logger.info(f"Reduced node capacity to {new_capacity} due to high CPU usage")
            
            elif self.distributed_processor and avg_cpu_usage < 30:
                # Low CPU usage - increase concurrent tasks
                current_capacity = self.distributed_processor.local_node.capacity
                new_capacity = min(current_capacity + 1, self.config.max_concurrent_tasks * 2)
                self.distributed_processor.local_node.capacity = new_capacity
                logger.info(f"Increased node capacity to {new_capacity} due to low CPU usage")
            
            # Memory optimization
            if self.cache_manager and avg_memory_usage > 400:  # > 400MB
                # High memory usage - reduce cache memory
                new_memory = max(self.cache_manager.max_memory * 0.8, self.config.max_cache_memory // 2)
                self.cache_manager.max_memory = int(new_memory)
                logger.info(f"Reduced cache memory to {new_memory / (1024*1024):.1f}MB due to high usage")
        
        except Exception as e:
            logger.error(f"Auto-optimization failed: {e}")
    
    async def optimize_function(self, func: Callable, cache_ttl: Optional[int] = None) -> Callable:
        """
        Optimize a function with caching and distributed processing.
        
        Args:
            func: Function to optimize
            cache_ttl: Cache TTL in seconds
            
        Returns:
            Optimized function
        """
        if not self.cache_manager:
            return func
        
        # Apply caching
        cached_func = self.cache_manager.cache_decorator(ttl=cache_ttl)(func)
        
        # Register for distributed processing if available
        if self.distributed_processor:
            func_name = f"{func.__module__}.{func.__name__}"
            self.distributed_processor.register_function(func_name, cached_func)
        
        return cached_func
    
    async def submit_task(self, func_name: str, *args, **kwargs) -> Optional[str]:
        """
        Submit task for distributed processing.
        
        Args:
            func_name: Function name
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Task ID if distributed processing available, None otherwise
        """
        if not self.distributed_processor:
            return None
        
        return await self.distributed_processor.submit_task(func_name, *args, **kwargs)
    
    async def get_task_result(self, task_id: str, timeout: Optional[float] = None) -> Any:
        """Get distributed task result."""
        if not self.distributed_processor:
            raise RuntimeError("Distributed processing not available")
        
        return await self.distributed_processor.get_task_result(task_id, timeout)
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report."""
        report = {
            "config": self.config.to_dict(),
            "current_metrics": self.metrics.to_dict(),
            "optimization_profile": self.config.profile.value,
            "components": {}
        }
        
        # Cache manager stats
        if self.cache_manager:
            report["components"]["cache"] = self.cache_manager.get_statistics()
        
        # Distributed processor stats
        if self.distributed_processor:
            report["components"]["distributed_processing"] = self.distributed_processor.get_statistics()
        
        # Performance trends
        if len(self.metrics_history) >= 2:
            recent = self.metrics_history[-1]
            previous = self.metrics_history[-2]
            
            report["trends"] = {
                "cache_hit_rate_change": recent.cache_hit_rate - previous.cache_hit_rate,
                "response_time_change": recent.avg_response_time - previous.avg_response_time,
                "memory_usage_change": recent.memory_usage - previous.memory_usage,
                "throughput_change": recent.throughput - previous.throughput
            }
        
        return report
    
    async def update_config(self, new_config: OptimizationConfig) -> None:
        """Update optimization configuration."""
        self.config = new_config
        await self._apply_optimization_profile()
        logger.info(f"Updated optimization configuration (profile: {self.config.profile.value})")
    
    async def cleanup(self) -> None:
        """Cleanup performance optimizer."""
        self._running = False
        
        # Cancel monitoring tasks
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
        
        if self._optimization_task:
            self._optimization_task.cancel()
            try:
                await self._optimization_task
            except asyncio.CancelledError:
                pass
        
        # Cleanup components
        if self.cache_manager:
            await self.cache_manager.cleanup()
        
        if self.distributed_processor:
            await self.distributed_processor.cleanup()
        
        logger.info("Performance optimizer cleaned up")
