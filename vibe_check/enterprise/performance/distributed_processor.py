"""
File: vibe_check/enterprise/performance/distributed_processor.py
Purpose: Distributed processing system for enterprise scalability
Related Files: vibe_check/enterprise/performance/
Dependencies: typing, asyncio, uuid, time, dataclasses
"""

import asyncio
import uuid
import time
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime

from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class TaskStatus(Enum):
    """Task execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class NodeStatus(Enum):
    """Processing node status."""
    IDLE = "idle"
    BUSY = "busy"
    OFFLINE = "offline"
    MAINTENANCE = "maintenance"


@dataclass
class ProcessingTask:
    """Distributed processing task."""
    task_id: str
    function_name: str
    args: tuple
    kwargs: dict
    priority: int = 0
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    status: TaskStatus = TaskStatus.PENDING
    result: Any = None
    error: Optional[str] = None
    node_id: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "task_id": self.task_id,
            "function_name": self.function_name,
            "priority": self.priority,
            "created_at": self.created_at.isoformat(),
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "status": self.status.value,
            "node_id": self.node_id,
            "retry_count": self.retry_count,
            "max_retries": self.max_retries,
            "error": self.error
        }


@dataclass
class ProcessingNode:
    """Distributed processing node."""
    node_id: str
    name: str
    capacity: int = 4  # Max concurrent tasks
    current_load: int = 0
    status: NodeStatus = NodeStatus.IDLE
    last_heartbeat: datetime = field(default_factory=datetime.now)
    total_tasks_processed: int = 0
    total_processing_time: float = 0.0
    error_count: int = 0
    
    @property
    def utilization(self) -> float:
        """Get node utilization percentage."""
        return (self.current_load / self.capacity) * 100 if self.capacity > 0 else 0.0
    
    @property
    def avg_processing_time(self) -> float:
        """Get average processing time."""
        return (self.total_processing_time / self.total_tasks_processed 
                if self.total_tasks_processed > 0 else 0.0)
    
    def is_available(self) -> bool:
        """Check if node is available for tasks."""
        return (self.status in [NodeStatus.IDLE, NodeStatus.BUSY] and 
                self.current_load < self.capacity)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "node_id": self.node_id,
            "name": self.name,
            "capacity": self.capacity,
            "current_load": self.current_load,
            "status": self.status.value,
            "utilization": self.utilization,
            "last_heartbeat": self.last_heartbeat.isoformat(),
            "total_tasks_processed": self.total_tasks_processed,
            "avg_processing_time": self.avg_processing_time,
            "error_count": self.error_count
        }


class TaskDistributor:
    """Distributes tasks across processing nodes."""
    
    def __init__(self):
        self.nodes: Dict[str, ProcessingNode] = {}
        self.task_queue: List[ProcessingTask] = []
        self.active_tasks: Dict[str, ProcessingTask] = {}
        self.completed_tasks: Dict[str, ProcessingTask] = {}
        
    def register_node(self, node: ProcessingNode) -> None:
        """Register a processing node."""
        self.nodes[node.node_id] = node
        logger.info(f"Registered processing node: {node.name} ({node.node_id})")
    
    def unregister_node(self, node_id: str) -> None:
        """Unregister a processing node."""
        if node_id in self.nodes:
            del self.nodes[node_id]
            logger.info(f"Unregistered processing node: {node_id}")
    
    def select_node(self, task: ProcessingTask) -> Optional[ProcessingNode]:
        """Select best node for task execution."""
        available_nodes = [node for node in self.nodes.values() if node.is_available()]
        
        if not available_nodes:
            return None
        
        # Select node with lowest utilization
        return min(available_nodes, key=lambda n: n.utilization)
    
    def add_task(self, task: ProcessingTask) -> None:
        """Add task to queue."""
        # Insert task in priority order (higher priority first)
        inserted = False
        for i, queued_task in enumerate(self.task_queue):
            if task.priority > queued_task.priority:
                self.task_queue.insert(i, task)
                inserted = True
                break
        
        if not inserted:
            self.task_queue.append(task)
        
        logger.debug(f"Added task {task.task_id} to queue (priority: {task.priority})")
    
    def get_next_task(self) -> Optional[ProcessingTask]:
        """Get next task from queue."""
        return self.task_queue.pop(0) if self.task_queue else None
    
    def assign_task(self, task: ProcessingTask, node: ProcessingNode) -> None:
        """Assign task to node."""
        task.node_id = node.node_id
        task.status = TaskStatus.RUNNING
        task.started_at = datetime.now()
        
        node.current_load += 1
        node.status = NodeStatus.BUSY if node.current_load >= node.capacity else NodeStatus.IDLE
        
        self.active_tasks[task.task_id] = task
        logger.debug(f"Assigned task {task.task_id} to node {node.node_id}")
    
    def complete_task(self, task_id: str, result: Any = None, error: Optional[str] = None) -> None:
        """Mark task as completed."""
        if task_id not in self.active_tasks:
            return
        
        task = self.active_tasks[task_id]
        node = self.nodes.get(task.node_id)
        
        if node:
            node.current_load = max(0, node.current_load - 1)
            node.status = NodeStatus.IDLE if node.current_load == 0 else NodeStatus.BUSY
            node.total_tasks_processed += 1
            
            if task.started_at:
                processing_time = (datetime.now() - task.started_at).total_seconds()
                node.total_processing_time += processing_time
        
        task.completed_at = datetime.now()
        task.result = result
        task.error = error
        task.status = TaskStatus.COMPLETED if error is None else TaskStatus.FAILED
        
        # Move to completed tasks
        del self.active_tasks[task_id]
        self.completed_tasks[task_id] = task
        
        if error:
            if node:
                node.error_count += 1
            logger.warning(f"Task {task_id} failed: {error}")
        else:
            logger.debug(f"Task {task_id} completed successfully")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get distributor statistics."""
        total_nodes = len(self.nodes)
        active_nodes = len([n for n in self.nodes.values() if n.status != NodeStatus.OFFLINE])
        total_capacity = sum(n.capacity for n in self.nodes.values())
        current_load = sum(n.current_load for n in self.nodes.values())
        
        return {
            "total_nodes": total_nodes,
            "active_nodes": active_nodes,
            "total_capacity": total_capacity,
            "current_load": current_load,
            "utilization": (current_load / total_capacity * 100) if total_capacity > 0 else 0.0,
            "queued_tasks": len(self.task_queue),
            "active_tasks": len(self.active_tasks),
            "completed_tasks": len(self.completed_tasks)
        }


class DistributedProcessor:
    """Main distributed processing coordinator."""
    
    def __init__(self, node_name: str = "main", node_capacity: int = 4):
        """
        Initialize distributed processor.
        
        Args:
            node_name: Name of this processing node
            node_capacity: Maximum concurrent tasks for this node
        """
        self.node_name = node_name
        self.node_capacity = node_capacity
        
        # Create local node
        self.local_node = ProcessingNode(
            node_id=str(uuid.uuid4()),
            name=node_name,
            capacity=node_capacity
        )
        
        # Task distributor
        self.distributor = TaskDistributor()
        self.distributor.register_node(self.local_node)
        
        # Function registry
        self.functions: Dict[str, Callable] = {}
        
        # Processing loop
        self._processing_task: Optional[asyncio.Task] = None
        self._running = False
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize distributed processor."""
        if self._initialized:
            return
        
        self._running = True
        self._processing_task = asyncio.create_task(self._processing_loop())
        
        self._initialized = True
        logger.info(f"Distributed processor initialized (node: {self.node_name})")
    
    async def _processing_loop(self) -> None:
        """Main processing loop."""
        while self._running:
            try:
                # Get next task
                task = self.distributor.get_next_task()
                if not task:
                    await asyncio.sleep(0.1)
                    continue
                
                # Select node for execution
                node = self.distributor.select_node(task)
                if not node:
                    # No available nodes, put task back in queue
                    self.distributor.add_task(task)
                    await asyncio.sleep(1.0)
                    continue
                
                # Assign and execute task
                self.distributor.assign_task(task, node)
                
                # Execute task (only on local node for now)
                if node.node_id == self.local_node.node_id:
                    asyncio.create_task(self._execute_task(task))
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Processing loop error: {e}")
                await asyncio.sleep(1.0)
    
    async def _execute_task(self, task: ProcessingTask) -> None:
        """Execute a task."""
        try:
            # Get function
            func = self.functions.get(task.function_name)
            if not func:
                raise ValueError(f"Function {task.function_name} not registered")
            
            # Execute function
            if asyncio.iscoroutinefunction(func):
                result = await func(*task.args, **task.kwargs)
            else:
                result = func(*task.args, **task.kwargs)
            
            # Mark as completed
            self.distributor.complete_task(task.task_id, result=result)
            
        except Exception as e:
            error_msg = str(e)
            
            # Handle retries
            if task.retry_count < task.max_retries:
                task.retry_count += 1
                task.status = TaskStatus.PENDING
                task.node_id = None
                task.started_at = None
                
                # Move back to queue
                del self.distributor.active_tasks[task.task_id]
                self.distributor.add_task(task)
                
                logger.warning(f"Task {task.task_id} failed, retrying ({task.retry_count}/{task.max_retries}): {error_msg}")
            else:
                # Mark as failed
                self.distributor.complete_task(task.task_id, error=error_msg)
    
    def register_function(self, name: str, func: Callable) -> None:
        """Register a function for distributed execution."""
        self.functions[name] = func
        logger.debug(f"Registered function: {name}")
    
    async def submit_task(
        self,
        function_name: str,
        *args,
        priority: int = 0,
        max_retries: int = 3,
        **kwargs
    ) -> str:
        """
        Submit a task for distributed execution.
        
        Args:
            function_name: Name of registered function
            *args: Function arguments
            priority: Task priority (higher = more important)
            max_retries: Maximum retry attempts
            **kwargs: Function keyword arguments
            
        Returns:
            Task ID
        """
        task = ProcessingTask(
            task_id=str(uuid.uuid4()),
            function_name=function_name,
            args=args,
            kwargs=kwargs,
            priority=priority,
            max_retries=max_retries
        )
        
        self.distributor.add_task(task)
        return task.task_id
    
    async def get_task_result(self, task_id: str, timeout: Optional[float] = None) -> Any:
        """
        Get task result (blocks until completion).
        
        Args:
            task_id: Task ID
            timeout: Maximum wait time in seconds
            
        Returns:
            Task result
            
        Raises:
            TimeoutError: If timeout exceeded
            RuntimeError: If task failed
        """
        start_time = time.time()
        
        while True:
            # Check completed tasks
            if task_id in self.distributor.completed_tasks:
                task = self.distributor.completed_tasks[task_id]
                if task.status == TaskStatus.FAILED:
                    raise RuntimeError(f"Task failed: {task.error}")
                return task.result
            
            # Check timeout
            if timeout and (time.time() - start_time) > timeout:
                raise TimeoutError(f"Task {task_id} timed out")
            
            await asyncio.sleep(0.1)
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get task status information."""
        # Check active tasks
        if task_id in self.distributor.active_tasks:
            return self.distributor.active_tasks[task_id].to_dict()
        
        # Check completed tasks
        if task_id in self.distributor.completed_tasks:
            return self.distributor.completed_tasks[task_id].to_dict()
        
        # Check queued tasks
        for task in self.distributor.task_queue:
            if task.task_id == task_id:
                return task.to_dict()
        
        return None
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get processing statistics."""
        distributor_stats = self.distributor.get_statistics()
        
        return {
            "local_node": self.local_node.to_dict(),
            "distributor": distributor_stats,
            "registered_functions": list(self.functions.keys()),
            "running": self._running
        }
    
    async def cleanup(self) -> None:
        """Cleanup distributed processor."""
        self._running = False
        
        if self._processing_task:
            self._processing_task.cancel()
            try:
                await self._processing_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Distributed processor cleaned up")
