"""
File: vibe_check/enterprise/performance/cache_manager.py
Purpose: Advanced caching system for enterprise performance optimization
Related Files: vibe_check/enterprise/performance/
Dependencies: typing, asyncio, hashlib, time, enum
"""

import asyncio
import hashlib
import time
from enum import Enum
from datetime import datetime, timedelta

from vibe_check.core.logging import get_logger
from typing import Any, Dict, List, Optional

logger = get_logger(__name__)


class CacheStrategy(Enum):
    """Cache strategy options."""
    LRU = "lru"  # Least Recently Used
    LFU = "lfu"  # Least Frequently Used
    TTL = "ttl"  # Time To Live
    ADAPTIVE = "adaptive"  # Adaptive based on usage patterns


@dataclass
class CacheMetrics:
    """Cache performance metrics."""
    hits: int = 0
    misses: int = 0
    evictions: int = 0
    memory_usage: int = 0  # bytes
    avg_access_time: float = 0.0  # milliseconds
    hit_rate: float = 0.0
    
    def update_hit_rate(self) -> None:
        """Update hit rate calculation."""
        total = self.hits + self.misses
        self.hit_rate = (self.hits / total * 100) if total > 0 else 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "hits": self.hits,
            "misses": self.misses,
            "evictions": self.evictions,
            "memory_usage": self.memory_usage,
            "avg_access_time": self.avg_access_time,
            "hit_rate": self.hit_rate
        }


@dataclass
class CacheEntry:
    """Cache entry with metadata."""
    key: str
    value: Any
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0
    ttl: Optional[int] = None  # seconds
    size: int = 0  # bytes
    
    def is_expired(self) -> bool:
        """Check if entry is expired."""
        if self.ttl is None:
            return False
        return datetime.now() > self.created_at + timedelta(seconds=self.ttl)
    
    def touch(self) -> None:
        """Update access metadata."""
        self.last_accessed = datetime.now()
        self.access_count += 1


class AdvancedCacheManager:
    """Advanced multi-level cache manager with intelligent strategies."""
    
    def __init__(
        self,
        max_size: int = 1000,
        max_memory: int = 100 * 1024 * 1024,  # 100MB
        strategy: CacheStrategy = CacheStrategy.ADAPTIVE,
        default_ttl: Optional[int] = 3600  # 1 hour
    ):
        """
        Initialize advanced cache manager.
        
        Args:
            max_size: Maximum number of entries
            max_memory: Maximum memory usage in bytes
            strategy: Cache eviction strategy
            default_ttl: Default TTL in seconds
        """
        self.max_size = max_size
        self.max_memory = max_memory
        self.strategy = strategy
        self.default_ttl = default_ttl
        
        # Cache storage
        self._cache: Dict[str, CacheEntry] = {}
        self._access_order: List[str] = []  # For LRU
        self._frequency: Dict[str, int] = {}  # For LFU
        
        # Metrics
        self.metrics = CacheMetrics()
        
        # Background tasks
        self._cleanup_task: Optional[asyncio.Task] = None
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize cache manager."""
        if self._initialized:
            return
        
        # Start background cleanup task
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        self._initialized = True
        logger.info(f"Advanced cache manager initialized (strategy: {self.strategy.value}, max_size: {self.max_size})")
    
    async def _cleanup_loop(self) -> None:
        """Background cleanup loop."""
        while True:
            try:
                await asyncio.sleep(60)  # Run every minute
                await self._cleanup_expired()
                await self._enforce_limits()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Cache cleanup error: {e}")
    
    async def _cleanup_expired(self) -> None:
        """Remove expired entries."""
        expired_keys = []
        for key, entry in self._cache.items():
            if entry.is_expired():
                expired_keys.append(key)
        
        for key in expired_keys:
            await self._remove_entry(key)
            self.metrics.evictions += 1
        
        if expired_keys:
            logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")
    
    async def _enforce_limits(self) -> None:
        """Enforce size and memory limits."""
        # Check size limit
        while len(self._cache) > self.max_size:
            key_to_remove = await self._select_eviction_candidate()
            if key_to_remove:
                await self._remove_entry(key_to_remove)
                self.metrics.evictions += 1
        
        # Check memory limit
        while self.metrics.memory_usage > self.max_memory:
            key_to_remove = await self._select_eviction_candidate()
            if key_to_remove:
                await self._remove_entry(key_to_remove)
                self.metrics.evictions += 1
            else:
                break  # No more entries to remove
    
    async def _select_eviction_candidate(self) -> Optional[str]:
        """Select entry for eviction based on strategy."""
        if not self._cache:
            return None
        
        if self.strategy == CacheStrategy.LRU:
            return self._access_order[0] if self._access_order else None
        
        elif self.strategy == CacheStrategy.LFU:
            if self._frequency:
                return min(self._frequency.keys(), key=lambda k: self._frequency[k])
            return None
        
        elif self.strategy == CacheStrategy.TTL:
            # Remove entry with earliest expiration
            candidates = [(k, v) for k, v in self._cache.items() if v.ttl is not None]
            if candidates:
                return min(candidates, key=lambda x: x[1].created_at + timedelta(seconds=x[1].ttl))[0]
            return list(self._cache.keys())[0] if self._cache else None
        
        elif self.strategy == CacheStrategy.ADAPTIVE:
            # Adaptive strategy: consider access frequency, recency, and size
            scores = {}
            now = datetime.now()
            
            for key, entry in self._cache.items():
                # Calculate adaptive score (lower = more likely to evict)
                recency_score = (now - entry.last_accessed).total_seconds()
                frequency_score = 1.0 / (entry.access_count + 1)
                size_score = entry.size / 1024  # KB
                
                scores[key] = recency_score * frequency_score * size_score
            
            return max(scores.keys(), key=lambda k: scores[k]) if scores else None
        
        return None
    
    async def _remove_entry(self, key: str) -> None:
        """Remove entry from cache."""
        if key in self._cache:
            entry = self._cache[key]
            self.metrics.memory_usage -= entry.size
            del self._cache[key]
        
        if key in self._access_order:
            self._access_order.remove(key)
        
        if key in self._frequency:
            del self._frequency[key]
    
    def _calculate_size(self, value: Any) -> int:
        """Estimate memory size of value."""
        try:
            import sys
            return sys.getsizeof(value)
        except Exception:
            # Fallback estimation
            if isinstance(value, str):
                return len(value.encode('utf-8'))
            elif isinstance(value, (list, tuple)):
                return sum(self._calculate_size(item) for item in value)
            elif isinstance(value, dict):
                return sum(self._calculate_size(k) + self._calculate_size(v) for k, v in value.items())
            else:
                return 64  # Default estimate
    
    def _generate_key(self, *args, **kwargs) -> str:
        """Generate cache key from arguments."""
        key_data = str(args) + str(sorted(kwargs.items()))
        return hashlib.md5(key_data.encode()).hexdigest()
    
    async def get(self, key: str) -> Optional[Any]:
        """
        Get value from cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cached value if found and not expired, None otherwise
        """
        start_time = time.time()
        
        try:
            if key not in self._cache:
                self.metrics.misses += 1
                return None
            
            entry = self._cache[key]
            
            # Check expiration
            if entry.is_expired():
                await self._remove_entry(key)
                self.metrics.misses += 1
                return None
            
            # Update access metadata
            entry.touch()
            
            # Update access tracking
            if key in self._access_order:
                self._access_order.remove(key)
            self._access_order.append(key)
            
            self._frequency[key] = self._frequency.get(key, 0) + 1
            
            self.metrics.hits += 1
            return entry.value
        
        finally:
            # Update metrics
            access_time = (time.time() - start_time) * 1000  # ms
            self.metrics.avg_access_time = (
                (self.metrics.avg_access_time * (self.metrics.hits + self.metrics.misses - 1) + access_time) /
                (self.metrics.hits + self.metrics.misses)
            )
            self.metrics.update_hit_rate()
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """
        Set value in cache.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds (uses default if None)
        """
        if ttl is None:
            ttl = self.default_ttl
        
        # Calculate size
        size = self._calculate_size(value)
        
        # Remove existing entry if present
        if key in self._cache:
            await self._remove_entry(key)
        
        # Create new entry
        entry = CacheEntry(
            key=key,
            value=value,
            created_at=datetime.now(),
            last_accessed=datetime.now(),
            ttl=ttl,
            size=size
        )
        
        # Add to cache
        self._cache[key] = entry
        self._access_order.append(key)
        self._frequency[key] = 1
        self.metrics.memory_usage += size
        
        # Enforce limits
        await self._enforce_limits()
    
    async def delete(self, key: str) -> bool:
        """
        Delete entry from cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if entry was deleted, False if not found
        """
        if key in self._cache:
            await self._remove_entry(key)
            return True
        return False
    
    async def clear(self) -> None:
        """Clear all cache entries."""
        self._cache.clear()
        self._access_order.clear()
        self._frequency.clear()
        self.metrics = CacheMetrics()
        logger.info("Cache cleared")
    
    def cache_decorator(self, ttl: Optional[int] = None, key_func: Optional[Callable] = None):
        """
        Decorator for caching function results.
        
        Args:
            ttl: Time to live in seconds
            key_func: Function to generate cache key
        """
        def decorator(func):
            async def wrapper(*args, **kwargs):
                # Generate cache key
                if key_func:
                    cache_key = key_func(*args, **kwargs)
                else:
                    cache_key = f"{func.__name__}:{self._generate_key(*args, **kwargs)}"
                
                # Try to get from cache
                cached_result = await self.get(cache_key)
                if cached_result is not None:
                    return cached_result
                
                # Execute function and cache result
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                await self.set(cache_key, result, ttl)
                return result
            
            return wrapper
        return decorator
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            "strategy": self.strategy.value,
            "max_size": self.max_size,
            "max_memory": self.max_memory,
            "current_size": len(self._cache),
            "metrics": self.metrics.to_dict(),
            "memory_usage_mb": self.metrics.memory_usage / (1024 * 1024),
            "memory_utilization": (self.metrics.memory_usage / self.max_memory) * 100
        }
    
    async def cleanup(self) -> None:
        """Cleanup cache manager."""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        await self.clear()
        logger.info("Advanced cache manager cleaned up")
