"""
File: vibe_check/enterprise/monitoring/models.py
Purpose: Data models for monitoring and quality gates
Related Files: vibe_check/enterprise/monitoring/
Dependencies: typing, datetime, enum, dataclasses
"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum


class AlertSeverity(Enum):
    """Alert severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AlertStatus(Enum):
    """Alert status."""
    ACTIVE = "active"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"
    SUPPRESSED = "suppressed"


class QualityGateStatus(Enum):
    """Quality gate evaluation status."""
    PASSED = "passed"
    FAILED = "failed"
    WARNING = "warning"
    ERROR = "error"
    PENDING = "pending"


class MetricType(Enum):
    """Types of monitoring metrics."""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    SUMMARY = "summary"


class ThresholdOperator(Enum):
    """Threshold comparison operators."""
    GREATER_THAN = "gt"
    GREATER_EQUAL = "gte"
    LESS_THAN = "lt"
    LESS_EQUAL = "lte"
    EQUAL = "eq"
    NOT_EQUAL = "ne"


@dataclass
class MetricThreshold:
    """Metric threshold configuration."""
    metric_name: str
    operator: ThresholdOperator
    value: Union[int, float]
    severity: AlertSeverity = AlertSeverity.MEDIUM
    description: str = ""
    
    def evaluate(self, metric_value: Union[int, float]) -> bool:
        """Evaluate if metric value breaches threshold."""
        if self.operator == ThresholdOperator.GREATER_THAN:
            return metric_value > self.value
        elif self.operator == ThresholdOperator.GREATER_EQUAL:
            return metric_value >= self.value
        elif self.operator == ThresholdOperator.LESS_THAN:
            return metric_value < self.value
        elif self.operator == ThresholdOperator.LESS_EQUAL:
            return metric_value <= self.value
        elif self.operator == ThresholdOperator.EQUAL:
            return metric_value == self.value
        elif self.operator == ThresholdOperator.NOT_EQUAL:
            return metric_value != self.value
        return False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "metric_name": self.metric_name,
            "operator": self.operator.value,
            "value": self.value,
            "severity": self.severity.value,
            "description": self.description
        }


@dataclass
class QualityGate:
    """Quality gate configuration."""
    gate_id: str
    name: str
    description: str
    enabled: bool = True
    thresholds: List[MetricThreshold] = field(default_factory=list)
    conditions: Dict[str, Any] = field(default_factory=dict)
    tags: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "gate_id": self.gate_id,
            "name": self.name,
            "description": self.description,
            "enabled": self.enabled,
            "thresholds": [t.to_dict() for t in self.thresholds],
            "conditions": self.conditions,
            "tags": self.tags,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }


@dataclass
class QualityGateResult:
    """Quality gate evaluation result."""
    result_id: str
    gate_id: str
    status: QualityGateStatus
    evaluated_at: datetime
    metrics: Dict[str, Union[int, float]] = field(default_factory=dict)
    threshold_results: List[Dict[str, Any]] = field(default_factory=list)
    details: Dict[str, Any] = field(default_factory=dict)
    duration_seconds: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "result_id": self.result_id,
            "gate_id": self.gate_id,
            "status": self.status.value,
            "evaluated_at": self.evaluated_at.isoformat(),
            "metrics": self.metrics,
            "threshold_results": self.threshold_results,
            "details": self.details,
            "duration_seconds": self.duration_seconds
        }


@dataclass
class MonitoringMetric:
    """Monitoring metric data point."""
    metric_id: str
    name: str
    value: Union[int, float]
    metric_type: MetricType
    timestamp: datetime
    labels: Dict[str, str] = field(default_factory=dict)
    tags: List[str] = field(default_factory=list)
    source: str = "vibe_check"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "metric_id": self.metric_id,
            "name": self.name,
            "value": self.value,
            "metric_type": self.metric_type.value,
            "timestamp": self.timestamp.isoformat(),
            "labels": self.labels,
            "tags": self.tags,
            "source": self.source
        }


@dataclass
class Alert:
    """Alert notification."""
    alert_id: str
    title: str
    description: str
    severity: AlertSeverity
    status: AlertStatus = AlertStatus.ACTIVE
    source: str = "vibe_check"
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    resolved_at: Optional[datetime] = None
    acknowledged_by: Optional[str] = None
    acknowledged_at: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    tags: List[str] = field(default_factory=list)
    
    def acknowledge(self, user_id: str) -> None:
        """Acknowledge the alert."""
        self.status = AlertStatus.ACKNOWLEDGED
        self.acknowledged_by = user_id
        self.acknowledged_at = datetime.now()
        self.updated_at = datetime.now()
    
    def resolve(self) -> None:
        """Resolve the alert."""
        self.status = AlertStatus.RESOLVED
        self.resolved_at = datetime.now()
        self.updated_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "alert_id": self.alert_id,
            "title": self.title,
            "description": self.description,
            "severity": self.severity.value,
            "status": self.status.value,
            "source": self.source,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "resolved_at": self.resolved_at.isoformat() if self.resolved_at else None,
            "acknowledged_by": self.acknowledged_by,
            "acknowledged_at": self.acknowledged_at.isoformat() if self.acknowledged_at else None,
            "metadata": self.metadata,
            "tags": self.tags
        }


@dataclass
class DashboardWidget:
    """Dashboard widget configuration."""
    widget_id: str
    title: str
    widget_type: str
    position: Dict[str, int]  # x, y, width, height
    config: Dict[str, Any] = field(default_factory=dict)
    data_source: str = ""
    refresh_interval: int = 30  # seconds
    enabled: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "widget_id": self.widget_id,
            "title": self.title,
            "widget_type": self.widget_type,
            "position": self.position,
            "config": self.config,
            "data_source": self.data_source,
            "refresh_interval": self.refresh_interval,
            "enabled": self.enabled
        }


@dataclass
class Dashboard:
    """Dashboard configuration."""
    dashboard_id: str
    name: str
    description: str
    widgets: List[DashboardWidget] = field(default_factory=list)
    layout: Dict[str, Any] = field(default_factory=dict)
    permissions: Dict[str, List[str]] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "dashboard_id": self.dashboard_id,
            "name": self.name,
            "description": self.description,
            "widgets": [w.to_dict() for w in self.widgets],
            "layout": self.layout,
            "permissions": self.permissions,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }


@dataclass
class MonitoringConfiguration:
    """Monitoring system configuration."""
    config_id: str
    name: str
    enabled: bool = True
    collection_interval: int = 60  # seconds
    retention_days: int = 30
    quality_gates: List[str] = field(default_factory=list)  # gate IDs
    alert_rules: List[str] = field(default_factory=list)  # alert rule IDs
    notification_channels: List[str] = field(default_factory=list)
    settings: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "config_id": self.config_id,
            "name": self.name,
            "enabled": self.enabled,
            "collection_interval": self.collection_interval,
            "retention_days": self.retention_days,
            "quality_gates": self.quality_gates,
            "alert_rules": self.alert_rules,
            "notification_channels": self.notification_channels,
            "settings": self.settings
        }
