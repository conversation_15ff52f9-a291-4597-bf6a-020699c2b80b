"""
File: vibe_check/enterprise/monitoring/quality_gates.py
Purpose: Quality gate management and evaluation system
Related Files: vibe_check/enterprise/monitoring/
Dependencies: typing, pathlib, json, asyncio
"""

import json
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime
import uuid

from .models import (
    QualityGate, QualityGateResult, QualityGateStatus, 
    MetricThreshold, ThresholdOperator, AlertSeverity
)
from vibe_check.core.vcs.models import AnalysisResult
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class QualityGateEngine:
    """Engine for evaluating quality gates."""
    
    def __init__(self):
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize quality gate engine."""
        if self._initialized:
            return
        
        self._initialized = True
        logger.info("Quality gate engine initialized")
    
    async def evaluate_gate(
        self,
        gate: QualityGate,
        analysis_results: List[AnalysisResult],
        additional_metrics: Optional[Dict[str, Any]] = None
    ) -> QualityGateResult:
        """
        Evaluate a quality gate against analysis results.
        
        Args:
            gate: Quality gate to evaluate
            analysis_results: Analysis results to evaluate against
            additional_metrics: Additional metrics to include
            
        Returns:
            Quality gate evaluation result
        """
        start_time = datetime.now()
        
        # Extract metrics from analysis results
        metrics = self._extract_metrics(analysis_results)
        
        # Add additional metrics if provided
        if additional_metrics:
            metrics.update(additional_metrics)
        
        # Evaluate thresholds
        threshold_results = []
        overall_status = QualityGateStatus.PASSED
        
        for threshold in gate.thresholds:
            if threshold.metric_name in metrics:
                metric_value = metrics[threshold.metric_name]
                breached = threshold.evaluate(metric_value)
                
                threshold_result = {
                    "metric_name": threshold.metric_name,
                    "threshold_value": threshold.value,
                    "actual_value": metric_value,
                    "operator": threshold.operator.value,
                    "breached": breached,
                    "severity": threshold.severity.value,
                    "description": threshold.description
                }
                
                threshold_results.append(threshold_result)
                
                # Update overall status based on breaches
                if breached:
                    if threshold.severity in [AlertSeverity.HIGH, AlertSeverity.CRITICAL]:
                        overall_status = QualityGateStatus.FAILED
                    elif overall_status == QualityGateStatus.PASSED:
                        overall_status = QualityGateStatus.WARNING
            else:
                # Metric not found
                threshold_result = {
                    "metric_name": threshold.metric_name,
                    "threshold_value": threshold.value,
                    "actual_value": None,
                    "operator": threshold.operator.value,
                    "breached": False,
                    "severity": threshold.severity.value,
                    "description": f"Metric '{threshold.metric_name}' not found",
                    "error": "metric_not_found"
                }
                threshold_results.append(threshold_result)
        
        # Evaluate custom conditions
        if gate.conditions:
            condition_result = await self._evaluate_conditions(gate.conditions, metrics)
            if condition_result and condition_result.get("failed"):
                overall_status = QualityGateStatus.FAILED
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        result = QualityGateResult(
            result_id=str(uuid.uuid4()),
            gate_id=gate.gate_id,
            status=overall_status,
            evaluated_at=start_time,
            metrics=metrics,
            threshold_results=threshold_results,
            details={
                "gate_name": gate.name,
                "total_thresholds": len(gate.thresholds),
                "breached_thresholds": sum(1 for r in threshold_results if r["breached"]),
                "conditions_evaluated": bool(gate.conditions)
            },
            duration_seconds=duration
        )
        
        logger.info(f"Evaluated quality gate '{gate.name}': {overall_status.value}")
        return result
    
    def _extract_metrics(self, analysis_results: List[AnalysisResult]) -> Dict[str, Any]:
        """Extract metrics from analysis results."""
        metrics = {}
        
        # Basic metrics
        total_files = len(analysis_results)
        total_issues = sum(len(result.issues) for result in analysis_results)
        successful_analyses = sum(1 for result in analysis_results if result.success)
        
        metrics.update({
            "total_files": total_files,
            "total_issues": total_issues,
            "successful_analyses": successful_analyses,
            "success_rate": (successful_analyses / total_files * 100) if total_files > 0 else 0,
            "average_issues_per_file": total_issues / total_files if total_files > 0 else 0
        })
        
        # Issue severity breakdown
        error_count = 0
        warning_count = 0
        info_count = 0
        
        for result in analysis_results:
            for issue in result.issues:
                if issue.severity.value == "error":
                    error_count += 1
                elif issue.severity.value == "warning":
                    warning_count += 1
                else:
                    info_count += 1
        
        metrics.update({
            "error_count": error_count,
            "warning_count": warning_count,
            "info_count": info_count,
            "critical_issues": error_count,  # Alias for compatibility
            "high_priority_issues": warning_count  # Alias for compatibility
        })
        
        # Category breakdown
        category_counts = {}
        for result in analysis_results:
            for issue in result.issues:
                category = issue.category.value
                category_counts[f"{category}_issues"] = category_counts.get(f"{category}_issues", 0) + 1
        
        metrics.update(category_counts)
        
        # Quality score calculation (0-100)
        if total_files > 0:
            # Simple quality score based on issue density and severity
            issue_density = total_issues / total_files
            severity_weight = (error_count * 3 + warning_count * 2 + info_count * 1) / max(total_issues, 1)
            
            # Quality score decreases with issue density and severity
            quality_score = max(0, 100 - (issue_density * 10) - (severity_weight * 5))
            metrics["quality_score"] = round(quality_score, 2)
        else:
            metrics["quality_score"] = 100
        
        # Code coverage (placeholder - would be integrated with coverage tools)
        metrics["code_coverage"] = 85.0  # Mock value
        
        # Technical debt estimation (hours)
        debt_hours = error_count * 2 + warning_count * 0.5 + info_count * 0.1
        metrics["technical_debt_hours"] = round(debt_hours, 2)
        
        return metrics
    
    async def _evaluate_conditions(
        self,
        conditions: Dict[str, Any],
        metrics: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Evaluate custom conditions."""
        try:
            # Simple condition evaluation
            # This could be extended to support complex expressions
            
            if "expression" in conditions:
                expression = conditions["expression"]
                # For safety, only allow basic mathematical operations
                # In a real implementation, you'd use a safe expression evaluator
                
                # Replace metric names with values
                for metric_name, value in metrics.items():
                    expression = expression.replace(f"${metric_name}", str(value))
                
                # Evaluate simple expressions (this is a simplified implementation)
                try:
                    result = eval(expression, {"__builtins__": {}}, {})
                    return {"result": result, "failed": not bool(result)}
                except Exception as e:
                    logger.error(f"Failed to evaluate condition expression: {e}")
                    return {"error": str(e), "failed": True}
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to evaluate conditions: {e}")
            return {"error": str(e), "failed": True}
    
    async def cleanup(self) -> None:
        """Cleanup quality gate engine."""
        logger.info("Quality gate engine cleaned up")


class QualityGateManager:
    """Manages quality gates and their evaluation."""
    
    def __init__(self, data_dir: Optional[Path] = None):
        """
        Initialize quality gate manager.
        
        Args:
            data_dir: Directory for storing quality gate data
        """
        self.data_dir = data_dir or Path.cwd() / "quality_gates"
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        self.gates: Dict[str, QualityGate] = {}
        self.results: Dict[str, QualityGateResult] = {}
        self.engine = QualityGateEngine()
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize quality gate manager."""
        if self._initialized:
            return
        
        await self.engine.initialize()
        await self._load_gates()
        await self._create_default_gates()
        
        self._initialized = True
        logger.info(f"Quality gate manager initialized with {len(self.gates)} gates")
    
    async def _load_gates(self) -> None:
        """Load quality gates from storage."""
        gates_file = self.data_dir / "quality_gates.json"
        if gates_file.exists():
            try:
                with open(gates_file, 'r', encoding='utf-8') as f:
                    gates_data = json.load(f)
                
                for gate_data in gates_data:
                    gate = self._gate_from_dict(gate_data)
                    self.gates[gate.gate_id] = gate
                
                logger.debug(f"Loaded {len(self.gates)} quality gates")
                
            except Exception as e:
                logger.error(f"Failed to load quality gates: {e}")
    
    async def _save_gates(self) -> None:
        """Save quality gates to storage."""
        gates_file = self.data_dir / "quality_gates.json"
        try:
            gates_data = [gate.to_dict() for gate in self.gates.values()]
            with open(gates_file, 'w', encoding='utf-8') as f:
                json.dump(gates_data, f, indent=2)
            
            logger.debug("Saved quality gates to storage")
            
        except Exception as e:
            logger.error(f"Failed to save quality gates: {e}")
    
    def _gate_from_dict(self, data: Dict[str, Any]) -> QualityGate:
        """Create QualityGate from dictionary."""
        thresholds = []
        for threshold_data in data.get("thresholds", []):
            threshold = MetricThreshold(
                metric_name=threshold_data["metric_name"],
                operator=ThresholdOperator(threshold_data["operator"]),
                value=threshold_data["value"],
                severity=AlertSeverity(threshold_data.get("severity", "medium")),
                description=threshold_data.get("description", "")
            )
            thresholds.append(threshold)
        
        return QualityGate(
            gate_id=data["gate_id"],
            name=data["name"],
            description=data["description"],
            enabled=data.get("enabled", True),
            thresholds=thresholds,
            conditions=data.get("conditions", {}),
            tags=data.get("tags", []),
            created_at=datetime.fromisoformat(data["created_at"]),
            updated_at=datetime.fromisoformat(data["updated_at"])
        )
    
    async def _create_default_gates(self) -> None:
        """Create default quality gates if none exist."""
        if self.gates:
            return  # Gates already exist
        
        # Basic Quality Gate
        basic_gate = QualityGate(
            gate_id="basic_quality",
            name="Basic Quality Gate",
            description="Basic quality checks for code analysis",
            thresholds=[
                MetricThreshold("error_count", ThresholdOperator.LESS_EQUAL, 0, AlertSeverity.HIGH, "No critical errors allowed"),
                MetricThreshold("warning_count", ThresholdOperator.LESS_EQUAL, 10, AlertSeverity.MEDIUM, "Maximum 10 warnings allowed"),
                MetricThreshold("quality_score", ThresholdOperator.GREATER_EQUAL, 70, AlertSeverity.MEDIUM, "Minimum quality score of 70")
            ],
            tags=["default", "basic"]
        )
        
        # Strict Quality Gate
        strict_gate = QualityGate(
            gate_id="strict_quality",
            name="Strict Quality Gate",
            description="Strict quality checks for production code",
            thresholds=[
                MetricThreshold("error_count", ThresholdOperator.EQUAL, 0, AlertSeverity.CRITICAL, "Zero errors required"),
                MetricThreshold("warning_count", ThresholdOperator.LESS_EQUAL, 5, AlertSeverity.HIGH, "Maximum 5 warnings allowed"),
                MetricThreshold("quality_score", ThresholdOperator.GREATER_EQUAL, 85, AlertSeverity.HIGH, "Minimum quality score of 85"),
                MetricThreshold("code_coverage", ThresholdOperator.GREATER_EQUAL, 80, AlertSeverity.MEDIUM, "Minimum 80% code coverage")
            ],
            tags=["strict", "production"]
        )
        
        # Security Gate
        security_gate = QualityGate(
            gate_id="security_gate",
            name="Security Quality Gate",
            description="Security-focused quality checks",
            thresholds=[
                MetricThreshold("security_issues", ThresholdOperator.EQUAL, 0, AlertSeverity.CRITICAL, "No security issues allowed"),
                MetricThreshold("error_count", ThresholdOperator.LESS_EQUAL, 2, AlertSeverity.HIGH, "Maximum 2 critical errors"),
                MetricThreshold("quality_score", ThresholdOperator.GREATER_EQUAL, 75, AlertSeverity.MEDIUM, "Minimum quality score of 75")
            ],
            tags=["security", "compliance"]
        )
        
        self.gates[basic_gate.gate_id] = basic_gate
        self.gates[strict_gate.gate_id] = strict_gate
        self.gates[security_gate.gate_id] = security_gate
        
        await self._save_gates()
        logger.info("Created default quality gates")
    
    async def create_gate(
        self,
        name: str,
        description: str,
        thresholds: List[MetricThreshold],
        conditions: Optional[Dict[str, Any]] = None,
        tags: Optional[List[str]] = None
    ) -> QualityGate:
        """
        Create a new quality gate.
        
        Args:
            name: Gate name
            description: Gate description
            thresholds: Metric thresholds
            conditions: Custom conditions
            tags: Tags for categorization
            
        Returns:
            Created quality gate
        """
        gate_id = str(uuid.uuid4())
        
        gate = QualityGate(
            gate_id=gate_id,
            name=name,
            description=description,
            thresholds=thresholds,
            conditions=conditions or {},
            tags=tags or []
        )
        
        self.gates[gate_id] = gate
        await self._save_gates()
        
        logger.info(f"Created quality gate '{name}' with {len(thresholds)} thresholds")
        return gate
    
    async def evaluate_gate(
        self,
        gate_id: str,
        analysis_results: List[AnalysisResult],
        additional_metrics: Optional[Dict[str, Any]] = None
    ) -> Optional[QualityGateResult]:
        """
        Evaluate a quality gate.
        
        Args:
            gate_id: Quality gate ID
            analysis_results: Analysis results to evaluate
            additional_metrics: Additional metrics
            
        Returns:
            Quality gate result or None if gate not found
        """
        gate = self.gates.get(gate_id)
        if not gate:
            logger.error(f"Quality gate {gate_id} not found")
            return None
        
        if not gate.enabled:
            logger.warning(f"Quality gate {gate_id} is disabled")
            return None
        
        result = await self.engine.evaluate_gate(gate, analysis_results, additional_metrics)
        self.results[result.result_id] = result
        
        return result
    
    async def evaluate_all_gates(
        self,
        analysis_results: List[AnalysisResult],
        additional_metrics: Optional[Dict[str, Any]] = None,
        tags: Optional[List[str]] = None
    ) -> List[QualityGateResult]:
        """
        Evaluate all enabled quality gates.
        
        Args:
            analysis_results: Analysis results to evaluate
            additional_metrics: Additional metrics
            tags: Optional tags to filter gates
            
        Returns:
            List of quality gate results
        """
        results = []
        
        for gate in self.gates.values():
            if not gate.enabled:
                continue
            
            # Filter by tags if specified
            if tags and not any(tag in gate.tags for tag in tags):
                continue
            
            result = await self.engine.evaluate_gate(gate, analysis_results, additional_metrics)
            self.results[result.result_id] = result
            results.append(result)
        
        logger.info(f"Evaluated {len(results)} quality gates")
        return results
    
    def get_gate_statistics(self) -> Dict[str, Any]:
        """Get quality gate statistics."""
        total_gates = len(self.gates)
        enabled_gates = sum(1 for gate in self.gates.values() if gate.enabled)
        total_results = len(self.results)
        
        # Recent results (last 10)
        recent_results = sorted(self.results.values(), key=lambda r: r.evaluated_at, reverse=True)[:10]
        
        status_counts = {}
        for result in self.results.values():
            status = result.status.value
            status_counts[status] = status_counts.get(status, 0) + 1
        
        return {
            "total_gates": total_gates,
            "enabled_gates": enabled_gates,
            "total_evaluations": total_results,
            "status_distribution": status_counts,
            "recent_results": [
                {
                    "result_id": r.result_id,
                    "gate_id": r.gate_id,
                    "status": r.status.value,
                    "evaluated_at": r.evaluated_at.isoformat()
                }
                for r in recent_results
            ]
        }
    
    async def cleanup(self) -> None:
        """Cleanup quality gate manager."""
        await self._save_gates()
        await self.engine.cleanup()
        logger.info("Quality gate manager cleaned up")
