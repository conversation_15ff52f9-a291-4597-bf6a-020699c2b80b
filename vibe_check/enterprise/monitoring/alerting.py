"""
File: vibe_check/enterprise/monitoring/alerting.py
Purpose: Alerting and notification system for enterprise monitoring
Related Files: vibe_check/enterprise/monitoring/
Dependencies: typing, pathlib, json, asyncio, smtplib
"""

import json
import asyncio
import smtplib
from pathlib import Path
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
import uuid

from vibe_check.core.logging import get_logger
from typing import Any, Dict, List, Optional

logger = get_logger(__name__)


class NotificationChannel:
    """Base class for notification channels."""
    
    def __init__(self, channel_id: str, name: str, config: Dict[str, Any]):
        self.channel_id = channel_id
        self.name = name
        self.config = config
        self.enabled = config.get("enabled", True)
    
    async def send_notification(self, alert: Alert) -> bool:
        """Send notification for an alert."""
        raise NotImplementedError
    
    def supports_severity(self, severity: AlertSeverity) -> bool:
        """Check if channel supports the given severity level."""
        min_severity = self.config.get("min_severity", "low")
        severity_levels = ["low", "medium", "high", "critical"]
        
        min_index = severity_levels.index(min_severity)
        alert_index = severity_levels.index(severity.value)
        
        return alert_index >= min_index


class EmailNotificationChannel(NotificationChannel):
    """Email notification channel."""
    
    async def send_notification(self, alert: Alert) -> bool:
        """Send email notification."""
        try:
            smtp_server = self.config.get("smtp_server", "localhost")
            smtp_port = self.config.get("smtp_port", 587)
            username = self.config.get("username")
            password = self.config.get("password")
            from_email = self.config.get("from_email", "<EMAIL>")
            to_emails = self.config.get("to_emails", [])
            
            if not to_emails:
                logger.warning("No email recipients configured")
                return False
            
            # Create email message
            msg = MIMEMultipart()
            msg['From'] = from_email
            msg['To'] = ", ".join(to_emails)
            msg['Subject'] = f"[{alert.severity.value.upper()}] {alert.title}"
            
            # Email body
            body = f"""
Alert Details:
--------------
Title: {alert.title}
Severity: {alert.severity.value.upper()}
Status: {alert.status.value}
Created: {alert.created_at.strftime('%Y-%m-%d %H:%M:%S')}
Source: {alert.source}

Description:
{alert.description}

Metadata:
{json.dumps(alert.metadata, indent=2)}

Alert ID: {alert.alert_id}
            """.strip()
            
            msg.attach(MIMEText(body, 'plain'))
            
            # Send email
            with smtplib.SMTP(smtp_server, smtp_port) as server:
                if username and password:
                    server.starttls()
                    server.login(username, password)
                
                server.send_message(msg)
            
            logger.info(f"Sent email notification for alert {alert.alert_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email notification: {e}")
            return False


class SlackNotificationChannel(NotificationChannel):
    """Slack notification channel."""
    
    async def send_notification(self, alert: Alert) -> bool:
        """Send Slack notification."""
        try:
            webhook_url = self.config.get("webhook_url")
            if not webhook_url:
                logger.warning("No Slack webhook URL configured")
                return False
            
            # Create Slack message
            color_map = {
                AlertSeverity.LOW: "good",
                AlertSeverity.MEDIUM: "warning", 
                AlertSeverity.HIGH: "danger",
                AlertSeverity.CRITICAL: "danger"
            }
            
            message = {
                "text": f"Vibe Check Alert: {alert.title}",
                "attachments": [
                    {
                        "color": color_map.get(alert.severity, "warning"),
                        "fields": [
                            {
                                "title": "Severity",
                                "value": alert.severity.value.upper(),
                                "short": True
                            },
                            {
                                "title": "Status", 
                                "value": alert.status.value,
                                "short": True
                            },
                            {
                                "title": "Source",
                                "value": alert.source,
                                "short": True
                            },
                            {
                                "title": "Created",
                                "value": alert.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                                "short": True
                            },
                            {
                                "title": "Description",
                                "value": alert.description,
                                "short": False
                            }
                        ],
                        "footer": f"Alert ID: {alert.alert_id}"
                    }
                ]
            }
            
            # Send to Slack (would use requests in real implementation)
            logger.info(f"Would send Slack notification for alert {alert.alert_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send Slack notification: {e}")
            return False


class WebhookNotificationChannel(NotificationChannel):
    """Generic webhook notification channel."""
    
    async def send_notification(self, alert: Alert) -> bool:
        """Send webhook notification."""
        try:
            webhook_url = self.config.get("webhook_url")
            if not webhook_url:
                logger.warning("No webhook URL configured")
                return False
            
            # Create webhook payload
            payload = {
                "alert": alert.to_dict(),
                "timestamp": datetime.now().isoformat(),
                "source": "vibe_check_enterprise"
            }
            
            # Send webhook (would use aiohttp in real implementation)
            logger.info(f"Would send webhook notification for alert {alert.alert_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send webhook notification: {e}")
            return False


class AlertingSystem:
    """Enterprise alerting system."""
    
    def __init__(self, data_dir: Optional[Path] = None):
        """
        Initialize alerting system.
        
        Args:
            data_dir: Directory for storing alerting data
        """
        self.data_dir = data_dir or Path.cwd() / "alerting_data"
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        self.channels: Dict[str, NotificationChannel] = {}
        self.alert_rules: Dict[str, Dict[str, Any]] = {}
        self.alert_history: List[Alert] = []
        self.suppression_rules: List[Dict[str, Any]] = []
        
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize alerting system."""
        if self._initialized:
            return
        
        await self._load_configuration()
        await self._setup_default_channels()
        
        self._initialized = True
        logger.info(f"Alerting system initialized with {len(self.channels)} channels")
    
    async def _load_configuration(self) -> None:
        """Load alerting configuration."""
        config_file = self.data_dir / "alerting_config.json"
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # Load channels
                for channel_data in config_data.get("channels", []):
                    await self._create_channel_from_config(channel_data)
                
                # Load alert rules
                self.alert_rules = config_data.get("alert_rules", {})
                
                # Load suppression rules
                self.suppression_rules = config_data.get("suppression_rules", [])
                
                logger.debug("Loaded alerting configuration")
                
            except Exception as e:
                logger.error(f"Failed to load alerting configuration: {e}")
    
    async def _save_configuration(self) -> None:
        """Save alerting configuration."""
        config_file = self.data_dir / "alerting_config.json"
        try:
            config_data = {
                "channels": [
                    {
                        "channel_id": channel.channel_id,
                        "name": channel.name,
                        "type": channel.__class__.__name__,
                        "config": channel.config
                    }
                    for channel in self.channels.values()
                ],
                "alert_rules": self.alert_rules,
                "suppression_rules": self.suppression_rules
            }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2)
            
            logger.debug("Saved alerting configuration")
            
        except Exception as e:
            logger.error(f"Failed to save alerting configuration: {e}")
    
    async def _create_channel_from_config(self, channel_data: Dict[str, Any]) -> None:
        """Create notification channel from configuration."""
        channel_type = channel_data.get("type", "EmailNotificationChannel")
        channel_id = channel_data["channel_id"]
        name = channel_data["name"]
        config = channel_data["config"]
        
        if channel_type == "EmailNotificationChannel":
            channel = EmailNotificationChannel(channel_id, name, config)
        elif channel_type == "SlackNotificationChannel":
            channel = SlackNotificationChannel(channel_id, name, config)
        elif channel_type == "WebhookNotificationChannel":
            channel = WebhookNotificationChannel(channel_id, name, config)
        else:
            logger.warning(f"Unknown channel type: {channel_type}")
            return
        
        self.channels[channel_id] = channel
    
    async def _setup_default_channels(self) -> None:
        """Setup default notification channels if none exist."""
        if self.channels:
            return
        
        # Default email channel
        email_channel = EmailNotificationChannel(
            channel_id="default_email",
            name="Default Email Notifications",
            config={
                "enabled": True,
                "min_severity": "medium",
                "smtp_server": "localhost",
                "smtp_port": 587,
                "from_email": "<EMAIL>",
                "to_emails": ["<EMAIL>"]
            }
        )
        
        self.channels[email_channel.channel_id] = email_channel
        
        await self._save_configuration()
        logger.info("Created default notification channels")
    
    async def send_alert(self, alert: Alert) -> Dict[str, bool]:
        """
        Send alert through all appropriate channels.
        
        Args:
            alert: Alert to send
            
        Returns:
            Dictionary mapping channel IDs to success status
        """
        # Check suppression rules
        if self._is_alert_suppressed(alert):
            logger.info(f"Alert {alert.alert_id} suppressed by rules")
            return {}
        
        results = {}
        
        for channel_id, channel in self.channels.items():
            if not channel.enabled:
                continue
            
            if not channel.supports_severity(alert.severity):
                continue
            
            try:
                success = await channel.send_notification(alert)
                results[channel_id] = success
                
                if success:
                    logger.info(f"Alert {alert.alert_id} sent via {channel.name}")
                else:
                    logger.warning(f"Failed to send alert {alert.alert_id} via {channel.name}")
                    
            except Exception as e:
                logger.error(f"Error sending alert via {channel.name}: {e}")
                results[channel_id] = False
        
        # Add to history
        self.alert_history.append(alert)
        
        return results
    
    def _is_alert_suppressed(self, alert: Alert) -> bool:
        """Check if alert should be suppressed."""
        for rule in self.suppression_rules:
            if self._matches_suppression_rule(alert, rule):
                return True
        return False
    
    def _matches_suppression_rule(self, alert: Alert, rule: Dict[str, Any]) -> bool:
        """Check if alert matches a suppression rule."""
        # Simple rule matching - could be extended
        if "title_contains" in rule:
            if rule["title_contains"].lower() not in alert.title.lower():
                return False
        
        if "severity" in rule:
            if alert.severity.value != rule["severity"]:
                return False
        
        if "source" in rule:
            if alert.source != rule["source"]:
                return False
        
        return True
    
    def add_notification_channel(
        self,
        channel_type: str,
        name: str,
        config: Dict[str, Any]
    ) -> str:
        """
        Add a new notification channel.
        
        Args:
            channel_type: Type of channel (email, slack, webhook)
            name: Channel name
            config: Channel configuration
            
        Returns:
            Channel ID
        """
        channel_id = str(uuid.uuid4())
        
        if channel_type.lower() == "email":
            channel = EmailNotificationChannel(channel_id, name, config)
        elif channel_type.lower() == "slack":
            channel = SlackNotificationChannel(channel_id, name, config)
        elif channel_type.lower() == "webhook":
            channel = WebhookNotificationChannel(channel_id, name, config)
        else:
            raise ValueError(f"Unsupported channel type: {channel_type}")
        
        self.channels[channel_id] = channel
        logger.info(f"Added {channel_type} notification channel: {name}")
        
        return channel_id
    
    def add_suppression_rule(
        self,
        rule_name: str,
        conditions: Dict[str, Any],
        duration_minutes: Optional[int] = None
    ) -> None:
        """
        Add an alert suppression rule.
        
        Args:
            rule_name: Name of the rule
            conditions: Conditions for suppression
            duration_minutes: Optional duration for temporary suppression
        """
        rule = {
            "name": rule_name,
            "conditions": conditions,
            "created_at": datetime.now().isoformat()
        }
        
        if duration_minutes:
            rule["expires_at"] = (datetime.now() + timedelta(minutes=duration_minutes)).isoformat()
        
        self.suppression_rules.append(rule)
        logger.info(f"Added suppression rule: {rule_name}")
    
    def get_alert_statistics(self) -> Dict[str, Any]:
        """Get alerting system statistics."""
        total_alerts = len(self.alert_history)
        
        # Count by severity
        severity_counts = {}
        for alert in self.alert_history:
            severity = alert.severity.value
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
        
        # Recent alerts (last 24 hours)
        cutoff_time = datetime.now() - timedelta(hours=24)
        recent_alerts = [
            alert for alert in self.alert_history
            if alert.created_at >= cutoff_time
        ]
        
        return {
            "total_alerts": total_alerts,
            "recent_alerts_24h": len(recent_alerts),
            "severity_distribution": severity_counts,
            "active_channels": len([c for c in self.channels.values() if c.enabled]),
            "total_channels": len(self.channels),
            "suppression_rules": len(self.suppression_rules)
        }
    
    async def cleanup(self) -> None:
        """Cleanup alerting system."""
        await self._save_configuration()
        logger.info("Alerting system cleaned up")


class NotificationManager:
    """Manages notifications and alert routing."""
    
    def __init__(self, alerting_system: AlertingSystem):
        self.alerting_system = alerting_system
        self.notification_queue: asyncio.Queue = asyncio.Queue()
        self._processor_task: Optional[asyncio.Task] = None
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize notification manager."""
        if self._initialized:
            return
        
        # Start notification processor
        self._processor_task = asyncio.create_task(self._process_notifications())
        
        self._initialized = True
        logger.info("Notification manager initialized")
    
    async def queue_notification(self, alert: Alert) -> None:
        """Queue an alert for notification."""
        await self.notification_queue.put(alert)
    
    async def _process_notifications(self) -> None:
        """Process queued notifications."""
        while True:
            try:
                # Get alert from queue
                alert = await self.notification_queue.get()
                
                # Send through alerting system
                results = await self.alerting_system.send_alert(alert)
                
                # Log results
                successful_channels = sum(1 for success in results.values() if success)
                total_channels = len(results)
                
                logger.info(f"Alert {alert.alert_id} sent to {successful_channels}/{total_channels} channels")
                
                # Mark task as done
                self.notification_queue.task_done()
                
            except Exception as e:
                logger.error(f"Error processing notification: {e}")
                await asyncio.sleep(1)
    
    async def cleanup(self) -> None:
        """Cleanup notification manager."""
        if self._processor_task:
            self._processor_task.cancel()
            try:
                await self._processor_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Notification manager cleaned up")
