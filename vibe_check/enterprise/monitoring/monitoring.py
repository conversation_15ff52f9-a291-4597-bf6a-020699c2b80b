"""
File: vibe_check/enterprise/monitoring/monitoring.py
Purpose: Real-time monitoring system for enterprise metrics
Related Files: vibe_check/enterprise/monitoring/
Dependencies: typing, pathlib, json, asyncio, time
"""

import json
import asyncio
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from collections import deque
import uuid

from .models import (
    MonitoringMetric, MetricType, MonitoringConfiguration,
    Alert, AlertSeverity, AlertStatus
)
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class MetricsCollector:
    """Collects and stores monitoring metrics."""
    
    def __init__(self, retention_days: int = 30):
        """
        Initialize metrics collector.
        
        Args:
            retention_days: Number of days to retain metrics
        """
        self.retention_days = retention_days
        self.metrics: Dict[str, deque] = {}  # metric_name -> deque of values
        self.metric_metadata: Dict[str, Dict[str, Any]] = {}
        self._collection_tasks: Dict[str, asyncio.Task] = {}
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize metrics collector."""
        if self._initialized:
            return
        
        # Start cleanup task
        self._cleanup_task = asyncio.create_task(self._cleanup_old_metrics())
        
        self._initialized = True
        logger.info("Metrics collector initialized")
    
    def record_metric(
        self,
        name: str,
        value: float,
        metric_type: MetricType = MetricType.GAUGE,
        labels: Optional[Dict[str, str]] = None,
        tags: Optional[List[str]] = None
    ) -> MonitoringMetric:
        """
        Record a metric value.
        
        Args:
            name: Metric name
            value: Metric value
            metric_type: Type of metric
            labels: Metric labels
            tags: Metric tags
            
        Returns:
            Created monitoring metric
        """
        metric = MonitoringMetric(
            metric_id=str(uuid.uuid4()),
            name=name,
            value=value,
            metric_type=metric_type,
            timestamp=datetime.now(),
            labels=labels or {},
            tags=tags or []
        )
        
        # Store metric
        if name not in self.metrics:
            self.metrics[name] = deque(maxlen=10000)  # Limit memory usage
            self.metric_metadata[name] = {
                "type": metric_type.value,
                "first_seen": metric.timestamp,
                "last_updated": metric.timestamp,
                "count": 0
            }
        
        self.metrics[name].append(metric)
        self.metric_metadata[name]["last_updated"] = metric.timestamp
        self.metric_metadata[name]["count"] += 1
        
        return metric
    
    def get_metric_values(
        self,
        name: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 1000
    ) -> List[MonitoringMetric]:
        """
        Get metric values within time range.
        
        Args:
            name: Metric name
            start_time: Start time filter
            end_time: End time filter
            limit: Maximum number of values to return
            
        Returns:
            List of metric values
        """
        if name not in self.metrics:
            return []
        
        metrics = list(self.metrics[name])
        
        # Apply time filters
        if start_time:
            metrics = [m for m in metrics if m.timestamp >= start_time]
        
        if end_time:
            metrics = [m for m in metrics if m.timestamp <= end_time]
        
        # Sort by timestamp and limit
        metrics.sort(key=lambda m: m.timestamp, reverse=True)
        return metrics[:limit]
    
    def get_latest_value(self, name: str) -> Optional[float]:
        """Get the latest value for a metric."""
        if name not in self.metrics or not self.metrics[name]:
            return None
        
        return self.metrics[name][-1].value
    
    def get_metric_summary(self, name: str, duration_minutes: int = 60) -> Dict[str, Any]:
        """
        Get summary statistics for a metric over a time period.
        
        Args:
            name: Metric name
            duration_minutes: Duration in minutes to analyze
            
        Returns:
            Summary statistics
        """
        if name not in self.metrics:
            return {}
        
        cutoff_time = datetime.now() - timedelta(minutes=duration_minutes)
        recent_metrics = [
            m for m in self.metrics[name]
            if m.timestamp >= cutoff_time
        ]
        
        if not recent_metrics:
            return {}
        
        values = [m.value for m in recent_metrics]
        
        return {
            "count": len(values),
            "min": min(values),
            "max": max(values),
            "avg": sum(values) / len(values),
            "latest": values[-1] if values else None,
            "duration_minutes": duration_minutes
        }
    
    def list_metrics(self) -> List[Dict[str, Any]]:
        """List all available metrics with metadata."""
        return [
            {
                "name": name,
                "metadata": metadata,
                "current_count": len(self.metrics.get(name, []))
            }
            for name, metadata in self.metric_metadata.items()
        ]
    
    async def _cleanup_old_metrics(self) -> None:
        """Cleanup old metrics based on retention policy."""
        while True:
            try:
                cutoff_time = datetime.now() - timedelta(days=self.retention_days)
                
                for name, metric_deque in self.metrics.items():
                    # Remove old metrics
                    while metric_deque and metric_deque[0].timestamp < cutoff_time:
                        metric_deque.popleft()
                
                # Sleep for 1 hour before next cleanup
                await asyncio.sleep(3600)
                
            except Exception as e:
                logger.error(f"Error during metrics cleanup: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error
    
    async def cleanup(self) -> None:
        """Cleanup metrics collector."""
        if hasattr(self, '_cleanup_task'):
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Metrics collector cleaned up")


class MonitoringSystem:
    """Real-time monitoring system for Vibe Check enterprise."""
    
    def __init__(self, data_dir: Optional[Path] = None):
        """
        Initialize monitoring system.
        
        Args:
            data_dir: Directory for storing monitoring data
        """
        self.data_dir = data_dir or Path.cwd() / "monitoring_data"
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        self.metrics_collector = MetricsCollector()
        self.configurations: Dict[str, MonitoringConfiguration] = {}
        self.alert_handlers: List[Callable] = []
        self.active_alerts: Dict[str, Alert] = {}
        
        # Monitoring tasks
        self._monitoring_tasks: Dict[str, asyncio.Task] = {}
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize monitoring system."""
        if self._initialized:
            return
        
        await self.metrics_collector.initialize()
        await self._load_configurations()
        await self._create_default_configuration()
        
        # Start monitoring tasks
        await self._start_monitoring_tasks()
        
        self._initialized = True
        logger.info("Monitoring system initialized")
    
    async def _load_configurations(self) -> None:
        """Load monitoring configurations."""
        config_file = self.data_dir / "monitoring_configurations.json"
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    configs_data = json.load(f)
                
                for config_data in configs_data:
                    config = MonitoringConfiguration(**config_data)
                    self.configurations[config.config_id] = config
                
                logger.debug(f"Loaded {len(self.configurations)} monitoring configurations")
                
            except Exception as e:
                logger.error(f"Failed to load monitoring configurations: {e}")
    
    async def _save_configurations(self) -> None:
        """Save monitoring configurations."""
        config_file = self.data_dir / "monitoring_configurations.json"
        try:
            configs_data = [config.to_dict() for config in self.configurations.values()]
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(configs_data, f, indent=2)
            
            logger.debug("Saved monitoring configurations")
            
        except Exception as e:
            logger.error(f"Failed to save monitoring configurations: {e}")
    
    async def _create_default_configuration(self) -> None:
        """Create default monitoring configuration if none exist."""
        if self.configurations:
            return
        
        default_config = MonitoringConfiguration(
            config_id="default_monitoring",
            name="Default Monitoring Configuration",
            enabled=True,
            collection_interval=60,
            retention_days=30,
            settings={
                "collect_system_metrics": True,
                "collect_analysis_metrics": True,
                "alert_on_failures": True
            }
        )
        
        self.configurations[default_config.config_id] = default_config
        await self._save_configurations()
        logger.info("Created default monitoring configuration")
    
    async def _start_monitoring_tasks(self) -> None:
        """Start monitoring tasks for enabled configurations."""
        for config in self.configurations.values():
            if config.enabled:
                task_name = f"monitoring_{config.config_id}"
                self._monitoring_tasks[task_name] = asyncio.create_task(
                    self._monitoring_loop(config)
                )
        
        logger.info(f"Started {len(self._monitoring_tasks)} monitoring tasks")
    
    async def _monitoring_loop(self, config: MonitoringConfiguration) -> None:
        """Main monitoring loop for a configuration."""
        while True:
            try:
                # Collect system metrics
                if config.settings.get("collect_system_metrics", True):
                    await self._collect_system_metrics()
                
                # Collect application metrics
                if config.settings.get("collect_analysis_metrics", True):
                    await self._collect_analysis_metrics()
                
                # Sleep until next collection
                await asyncio.sleep(config.collection_interval)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop for {config.name}: {e}")
                await asyncio.sleep(60)  # Wait 1 minute on error
    
    async def _collect_system_metrics(self) -> None:
        """Collect system-level metrics."""
        try:
            import psutil
            
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            self.metrics_collector.record_metric("system.cpu_percent", cpu_percent)
            
            # Memory usage
            memory = psutil.virtual_memory()
            self.metrics_collector.record_metric("system.memory_percent", memory.percent)
            self.metrics_collector.record_metric("system.memory_used_gb", memory.used / (1024**3))
            
            # Disk usage
            disk = psutil.disk_usage('/')
            self.metrics_collector.record_metric("system.disk_percent", disk.percent)
            
        except ImportError:
            # psutil not available, record basic metrics
            self.metrics_collector.record_metric("system.timestamp", time.time())
        except Exception as e:
            logger.error(f"Failed to collect system metrics: {e}")
    
    async def _collect_analysis_metrics(self) -> None:
        """Collect analysis-related metrics."""
        # These would be updated by the VCS engine during analysis
        # For now, record timestamp to show the system is active
        self.metrics_collector.record_metric("analysis.heartbeat", 1.0)
    
    def record_analysis_metrics(self, analysis_results: List[Any]) -> None:
        """
        Record metrics from analysis results.
        
        Args:
            analysis_results: Analysis results to extract metrics from
        """
        if not analysis_results:
            return
        
        # Basic analysis metrics
        total_files = len(analysis_results)
        total_issues = sum(len(getattr(result, 'issues', [])) for result in analysis_results)
        successful_analyses = sum(1 for result in analysis_results if getattr(result, 'success', False))
        
        self.metrics_collector.record_metric("analysis.files_analyzed", total_files, MetricType.COUNTER)
        self.metrics_collector.record_metric("analysis.total_issues", total_issues, MetricType.COUNTER)
        self.metrics_collector.record_metric("analysis.successful_analyses", successful_analyses, MetricType.COUNTER)
        
        if total_files > 0:
            success_rate = (successful_analyses / total_files) * 100
            self.metrics_collector.record_metric("analysis.success_rate", success_rate)
            
            avg_issues = total_issues / total_files
            self.metrics_collector.record_metric("analysis.avg_issues_per_file", avg_issues)
    
    def create_alert(
        self,
        title: str,
        description: str,
        severity: AlertSeverity,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Alert:
        """
        Create a new alert.
        
        Args:
            title: Alert title
            description: Alert description
            severity: Alert severity
            metadata: Additional metadata
            
        Returns:
            Created alert
        """
        alert = Alert(
            alert_id=str(uuid.uuid4()),
            title=title,
            description=description,
            severity=severity,
            metadata=metadata or {}
        )
        
        self.active_alerts[alert.alert_id] = alert
        
        # Notify alert handlers
        for handler in self.alert_handlers:
            try:
                handler(alert)
            except Exception as e:
                logger.error(f"Error in alert handler: {e}")
        
        logger.warning(f"Alert created: {title} ({severity.value})")
        return alert
    
    def add_alert_handler(self, handler: Callable[[Alert], None]) -> None:
        """Add an alert handler function."""
        self.alert_handlers.append(handler)
    
    def get_active_alerts(self, severity: Optional[AlertSeverity] = None) -> List[Alert]:
        """Get active alerts, optionally filtered by severity."""
        alerts = [
            alert for alert in self.active_alerts.values()
            if alert.status == AlertStatus.ACTIVE
        ]
        
        if severity:
            alerts = [alert for alert in alerts if alert.severity == severity]
        
        return sorted(alerts, key=lambda a: a.created_at, reverse=True)
    
    def get_monitoring_statistics(self) -> Dict[str, Any]:
        """Get monitoring system statistics."""
        total_metrics = len(self.metrics_collector.metrics)
        total_alerts = len(self.active_alerts)
        active_alerts = len(self.get_active_alerts())
        
        # Recent metrics summary
        recent_metrics = {}
        for name in list(self.metrics_collector.metrics.keys())[:10]:  # Top 10 metrics
            summary = self.metrics_collector.get_metric_summary(name, 60)
            if summary:
                recent_metrics[name] = summary
        
        return {
            "total_metrics": total_metrics,
            "total_alerts": total_alerts,
            "active_alerts": active_alerts,
            "monitoring_tasks": len(self._monitoring_tasks),
            "configurations": len(self.configurations),
            "recent_metrics": recent_metrics,
            "available_metrics": self.metrics_collector.list_metrics()
        }
    
    async def cleanup(self) -> None:
        """Cleanup monitoring system."""
        # Cancel monitoring tasks
        for task in self._monitoring_tasks.values():
            task.cancel()
        
        # Wait for tasks to complete
        if self._monitoring_tasks:
            await asyncio.gather(*self._monitoring_tasks.values(), return_exceptions=True)
        
        # Save configurations
        await self._save_configurations()
        
        # Cleanup metrics collector
        await self.metrics_collector.cleanup()
        
        logger.info("Monitoring system cleaned up")
