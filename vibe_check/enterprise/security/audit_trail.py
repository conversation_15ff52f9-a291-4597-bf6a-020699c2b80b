"""
File: vibe_check/enterprise/security/audit_trail.py
Purpose: Audit trail and logging system for enterprise security
Related Files: vibe_check/enterprise/security/
Dependencies: typing, datetime, enum, dataclasses, json
"""

import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import uuid

from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class AuditEventType(Enum):
    """Types of audit events."""
    USER_LOGIN = "user_login"
    USER_LOGOUT = "user_logout"
    USER_CREATED = "user_created"
    USER_DELETED = "user_deleted"
    USER_MODIFIED = "user_modified"
    ACCESS_GRANTED = "access_granted"
    ACCESS_DENIED = "access_denied"
    DATA_ACCESS = "data_access"
    DATA_MODIFIED = "data_modified"
    DATA_DELETED = "data_deleted"
    SYSTEM_CONFIG_CHANGED = "system_config_changed"
    SECURITY_ALERT = "security_alert"
    COMPLIANCE_CHECK = "compliance_check"
    BACKUP_CREATED = "backup_created"
    BACKUP_RESTORED = "backup_restored"
    API_CALL = "api_call"
    ANALYSIS_STARTED = "analysis_started"
    ANALYSIS_COMPLETED = "analysis_completed"
    REPORT_GENERATED = "report_generated"


class AuditSeverity(Enum):
    """Audit event severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class AuditEvent:
    """Individual audit event."""
    event_id: str
    event_type: AuditEventType
    timestamp: datetime
    user_id: Optional[str] = None
    username: Optional[str] = None
    source_ip: Optional[str] = None
    user_agent: Optional[str] = None
    resource: Optional[str] = None
    action: Optional[str] = None
    result: Optional[str] = None
    severity: AuditSeverity = AuditSeverity.LOW
    details: Dict[str, Any] = field(default_factory=dict)
    session_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "event_id": self.event_id,
            "event_type": self.event_type.value,
            "timestamp": self.timestamp.isoformat(),
            "user_id": self.user_id,
            "username": self.username,
            "source_ip": self.source_ip,
            "user_agent": self.user_agent,
            "resource": self.resource,
            "action": self.action,
            "result": self.result,
            "severity": self.severity.value,
            "details": self.details,
            "session_id": self.session_id
        }
    
    def to_json(self) -> str:
        """Convert to JSON string."""
        return json.dumps(self.to_dict(), default=str)


class AuditLogger:
    """Audit event logger with filtering and search capabilities."""
    
    def __init__(self, max_events: int = 100000):
        """
        Initialize audit logger.
        
        Args:
            max_events: Maximum number of events to store in memory
        """
        self.max_events = max_events
        self.events: List[AuditEvent] = []
        self._event_index: Dict[str, AuditEvent] = {}
    
    def log_event(self, event: AuditEvent) -> None:
        """
        Log an audit event.
        
        Args:
            event: Audit event to log
        """
        # Add to events list
        self.events.append(event)
        self._event_index[event.event_id] = event
        
        # Enforce size limit
        if len(self.events) > self.max_events:
            # Remove oldest event
            oldest_event = self.events.pop(0)
            del self._event_index[oldest_event.event_id]
        
        # Log to system logger based on severity
        log_message = f"Audit: {event.event_type.value} - {event.username or 'Unknown'} - {event.action or 'N/A'}"
        
        if event.severity == AuditSeverity.CRITICAL:
            logger.critical(log_message)
        elif event.severity == AuditSeverity.HIGH:
            logger.error(log_message)
        elif event.severity == AuditSeverity.MEDIUM:
            logger.warning(log_message)
        else:
            logger.info(log_message)
    
    def get_event(self, event_id: str) -> Optional[AuditEvent]:
        """Get event by ID."""
        return self._event_index.get(event_id)
    
    def search_events(
        self,
        event_type: Optional[AuditEventType] = None,
        user_id: Optional[str] = None,
        username: Optional[str] = None,
        severity: Optional[AuditSeverity] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        resource: Optional[str] = None,
        limit: int = 100
    ) -> List[AuditEvent]:
        """
        Search audit events with filters.
        
        Args:
            event_type: Filter by event type
            user_id: Filter by user ID
            username: Filter by username
            severity: Filter by severity
            start_time: Filter by start time
            end_time: Filter by end time
            resource: Filter by resource
            limit: Maximum number of results
            
        Returns:
            List of matching audit events
        """
        results = []
        
        for event in reversed(self.events):  # Most recent first
            # Apply filters
            if event_type and event.event_type != event_type:
                continue
            
            if user_id and event.user_id != user_id:
                continue
            
            if username and event.username != username:
                continue
            
            if severity and event.severity != severity:
                continue
            
            if start_time and event.timestamp < start_time:
                continue
            
            if end_time and event.timestamp > end_time:
                continue
            
            if resource and event.resource != resource:
                continue
            
            results.append(event)
            
            if len(results) >= limit:
                break
        
        return results
    
    def get_recent_events(self, hours: int = 24, limit: int = 100) -> List[AuditEvent]:
        """Get recent events within specified hours."""
        start_time = datetime.now() - timedelta(hours=hours)
        return self.search_events(start_time=start_time, limit=limit)
    
    def get_user_activity(self, user_id: str, hours: int = 24) -> List[AuditEvent]:
        """Get recent activity for a specific user."""
        start_time = datetime.now() - timedelta(hours=hours)
        return self.search_events(user_id=user_id, start_time=start_time)
    
    def get_security_events(self, hours: int = 24) -> List[AuditEvent]:
        """Get recent security-related events."""
        start_time = datetime.now() - timedelta(hours=hours)
        security_types = [
            AuditEventType.USER_LOGIN,
            AuditEventType.ACCESS_DENIED,
            AuditEventType.SECURITY_ALERT,
            AuditEventType.USER_CREATED,
            AuditEventType.USER_DELETED
        ]
        
        results = []
        for event_type in security_types:
            results.extend(self.search_events(
                event_type=event_type,
                start_time=start_time,
                limit=50
            ))
        
        # Sort by timestamp (most recent first)
        results.sort(key=lambda e: e.timestamp, reverse=True)
        return results[:100]  # Limit to 100 events
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get audit logger statistics."""
        if not self.events:
            return {
                "total_events": 0,
                "events_by_type": {},
                "events_by_severity": {},
                "unique_users": 0,
                "date_range": None
            }
        
        # Count events by type
        events_by_type = {}
        for event in self.events:
            event_type = event.event_type.value
            events_by_type[event_type] = events_by_type.get(event_type, 0) + 1
        
        # Count events by severity
        events_by_severity = {}
        for event in self.events:
            severity = event.severity.value
            events_by_severity[severity] = events_by_severity.get(severity, 0) + 1
        
        # Count unique users
        unique_users = len(set(e.user_id for e in self.events if e.user_id))
        
        # Date range
        timestamps = [e.timestamp for e in self.events]
        date_range = {
            "earliest": min(timestamps).isoformat(),
            "latest": max(timestamps).isoformat()
        }
        
        return {
            "total_events": len(self.events),
            "events_by_type": events_by_type,
            "events_by_severity": events_by_severity,
            "unique_users": unique_users,
            "date_range": date_range
        }


class AuditTrail:
    """Main audit trail system."""
    
    def __init__(self):
        """Initialize audit trail system."""
        self.logger = AuditLogger()
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize audit trail system."""
        if self._initialized:
            return
        
        # Log system startup
        self.log_system_event(
            event_type=AuditEventType.SYSTEM_CONFIG_CHANGED,
            action="audit_trail_initialized",
            details={"component": "audit_trail"}
        )
        
        self._initialized = True
        logger.info("Audit trail system initialized")
    
    def log_user_event(
        self,
        event_type: AuditEventType,
        user_id: str,
        username: str,
        action: str,
        resource: Optional[str] = None,
        result: Optional[str] = None,
        severity: AuditSeverity = AuditSeverity.LOW,
        details: Optional[Dict[str, Any]] = None,
        source_ip: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> str:
        """
        Log a user-related audit event.
        
        Args:
            event_type: Type of audit event
            user_id: User ID
            username: Username
            action: Action performed
            resource: Resource accessed
            result: Result of action
            severity: Event severity
            details: Additional details
            source_ip: Source IP address
            user_agent: User agent string
            session_id: Session ID
            
        Returns:
            Event ID
        """
        event = AuditEvent(
            event_id=str(uuid.uuid4()),
            event_type=event_type,
            timestamp=datetime.now(),
            user_id=user_id,
            username=username,
            source_ip=source_ip,
            user_agent=user_agent,
            resource=resource,
            action=action,
            result=result,
            severity=severity,
            details=details or {},
            session_id=session_id
        )
        
        self.logger.log_event(event)
        return event.event_id
    
    def log_system_event(
        self,
        event_type: AuditEventType,
        action: str,
        resource: Optional[str] = None,
        result: Optional[str] = None,
        severity: AuditSeverity = AuditSeverity.LOW,
        details: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Log a system-related audit event.
        
        Args:
            event_type: Type of audit event
            action: Action performed
            resource: Resource affected
            result: Result of action
            severity: Event severity
            details: Additional details
            
        Returns:
            Event ID
        """
        event = AuditEvent(
            event_id=str(uuid.uuid4()),
            event_type=event_type,
            timestamp=datetime.now(),
            username="system",
            resource=resource,
            action=action,
            result=result,
            severity=severity,
            details=details or {}
        )
        
        self.logger.log_event(event)
        return event.event_id
    
    def log_api_call(
        self,
        endpoint: str,
        method: str,
        user_id: Optional[str] = None,
        username: Optional[str] = None,
        status_code: Optional[int] = None,
        source_ip: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> str:
        """Log API call audit event."""
        severity = AuditSeverity.LOW
        if status_code and status_code >= 400:
            severity = AuditSeverity.MEDIUM if status_code < 500 else AuditSeverity.HIGH
        
        return self.log_user_event(
            event_type=AuditEventType.API_CALL,
            user_id=user_id or "anonymous",
            username=username or "anonymous",
            action=f"{method} {endpoint}",
            result=f"HTTP {status_code}" if status_code else None,
            severity=severity,
            details=details,
            source_ip=source_ip,
            user_agent=user_agent,
            session_id=session_id
        )
    
    def log_analysis_event(
        self,
        analysis_type: str,
        project_path: str,
        user_id: str,
        username: str,
        result: str,
        details: Optional[Dict[str, Any]] = None
    ) -> str:
        """Log analysis-related audit event."""
        event_type = AuditEventType.ANALYSIS_STARTED if result == "started" else AuditEventType.ANALYSIS_COMPLETED
        
        return self.log_user_event(
            event_type=event_type,
            user_id=user_id,
            username=username,
            action=f"analysis_{analysis_type}",
            resource=project_path,
            result=result,
            severity=AuditSeverity.LOW,
            details=details
        )
    
    def search_events(self, **kwargs) -> List[Dict[str, Any]]:
        """Search audit events and return as dictionaries."""
        events = self.logger.search_events(**kwargs)
        return [event.to_dict() for event in events]
    
    def get_recent_activity(self, hours: int = 24) -> List[Dict[str, Any]]:
        """Get recent audit activity."""
        events = self.logger.get_recent_events(hours=hours)
        return [event.to_dict() for event in events]
    
    def get_user_activity(self, user_id: str, hours: int = 24) -> List[Dict[str, Any]]:
        """Get user activity."""
        events = self.logger.get_user_activity(user_id, hours)
        return [event.to_dict() for event in events]
    
    def get_security_events(self, hours: int = 24) -> List[Dict[str, Any]]:
        """Get security events."""
        events = self.logger.get_security_events(hours)
        return [event.to_dict() for event in events]
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get audit trail statistics."""
        return self.logger.get_statistics()
    
    async def cleanup(self) -> None:
        """Cleanup audit trail system."""
        self.log_system_event(
            event_type=AuditEventType.SYSTEM_CONFIG_CHANGED,
            action="audit_trail_shutdown",
            details={"component": "audit_trail"}
        )
        
        logger.info("Audit trail system cleaned up")
