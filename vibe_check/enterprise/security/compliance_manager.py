"""
File: vibe_check/enterprise/security/compliance_manager.py
Purpose: Compliance management and reporting for enterprise security
Related Files: vibe_check/enterprise/security/
Dependencies: typing, datetime, enum, dataclasses
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import uuid

from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class ComplianceFramework(Enum):
    """Supported compliance frameworks."""
    SOC2 = "soc2"
    GDPR = "gdpr"
    HIPAA = "hipaa"
    PCI_DSS = "pci_dss"
    ISO27001 = "iso27001"
    NIST = "nist"


class ComplianceStatus(Enum):
    """Compliance status levels."""
    COMPLIANT = "compliant"
    NON_COMPLIANT = "non_compliant"
    PARTIALLY_COMPLIANT = "partially_compliant"
    UNDER_REVIEW = "under_review"
    NOT_APPLICABLE = "not_applicable"


@dataclass
class ComplianceControl:
    """Individual compliance control."""
    control_id: str
    framework: ComplianceFramework
    title: str
    description: str
    requirement: str
    status: ComplianceStatus = ComplianceStatus.UNDER_REVIEW
    evidence: List[str] = field(default_factory=list)
    last_assessed: Optional[datetime] = None
    next_assessment: Optional[datetime] = None
    responsible_party: Optional[str] = None
    remediation_notes: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "control_id": self.control_id,
            "framework": self.framework.value,
            "title": self.title,
            "description": self.description,
            "requirement": self.requirement,
            "status": self.status.value,
            "evidence": self.evidence,
            "last_assessed": self.last_assessed.isoformat() if self.last_assessed else None,
            "next_assessment": self.next_assessment.isoformat() if self.next_assessment else None,
            "responsible_party": self.responsible_party,
            "remediation_notes": self.remediation_notes
        }


@dataclass
class ComplianceReport:
    """Compliance assessment report."""
    report_id: str
    framework: ComplianceFramework
    generated_at: datetime
    assessment_period_start: datetime
    assessment_period_end: datetime
    overall_status: ComplianceStatus
    controls_assessed: int
    controls_compliant: int
    controls_non_compliant: int
    controls_partial: int
    compliance_score: float  # 0-100
    findings: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "report_id": self.report_id,
            "framework": self.framework.value,
            "generated_at": self.generated_at.isoformat(),
            "assessment_period_start": self.assessment_period_start.isoformat(),
            "assessment_period_end": self.assessment_period_end.isoformat(),
            "overall_status": self.overall_status.value,
            "controls_assessed": self.controls_assessed,
            "controls_compliant": self.controls_compliant,
            "controls_non_compliant": self.controls_non_compliant,
            "controls_partial": self.controls_partial,
            "compliance_score": self.compliance_score,
            "findings": self.findings,
            "recommendations": self.recommendations
        }


class ComplianceManager:
    """Enterprise compliance management system."""
    
    def __init__(self):
        """Initialize compliance manager."""
        self.controls: Dict[str, ComplianceControl] = {}
        self.reports: Dict[str, ComplianceReport] = {}
        self._initialized = False
        
        # Initialize default controls
        self._initialize_default_controls()
    
    async def initialize(self) -> None:
        """Initialize compliance manager."""
        if self._initialized:
            return
        
        self._initialized = True
        logger.info(f"Compliance manager initialized with {len(self.controls)} controls")
    
    def _initialize_default_controls(self) -> None:
        """Initialize default compliance controls."""
        # SOC 2 Controls
        soc2_controls = [
            ComplianceControl(
                control_id="CC6.1",
                framework=ComplianceFramework.SOC2,
                title="Logical and Physical Access Controls",
                description="The entity implements logical and physical access controls to protect against threats from sources outside its system boundaries.",
                requirement="Implement access controls including authentication, authorization, and physical security measures."
            ),
            ComplianceControl(
                control_id="CC6.2",
                framework=ComplianceFramework.SOC2,
                title="Access Control Management",
                description="Prior to issuing system credentials and granting system access, the entity registers and authorizes new internal and external users.",
                requirement="Establish user registration and authorization processes for system access."
            ),
            ComplianceControl(
                control_id="CC7.1",
                framework=ComplianceFramework.SOC2,
                title="System Monitoring",
                description="To meet its objectives, the entity uses detection and monitoring procedures to identify anomalies.",
                requirement="Implement monitoring and detection systems for security anomalies."
            )
        ]
        
        # GDPR Controls
        gdpr_controls = [
            ComplianceControl(
                control_id="GDPR.7",
                framework=ComplianceFramework.GDPR,
                title="Consent Management",
                description="Processing is lawful only if and to the extent that at least one of the legal bases applies.",
                requirement="Implement consent management for personal data processing."
            ),
            ComplianceControl(
                control_id="GDPR.25",
                framework=ComplianceFramework.GDPR,
                title="Data Protection by Design",
                description="The controller shall implement appropriate technical and organisational measures.",
                requirement="Implement data protection by design and by default."
            ),
            ComplianceControl(
                control_id="GDPR.32",
                framework=ComplianceFramework.GDPR,
                title="Security of Processing",
                description="The controller and processor shall implement appropriate technical and organisational measures.",
                requirement="Implement security measures for personal data processing."
            )
        ]
        
        # HIPAA Controls
        hipaa_controls = [
            ComplianceControl(
                control_id="HIPAA.164.308",
                framework=ComplianceFramework.HIPAA,
                title="Administrative Safeguards",
                description="A covered entity must implement administrative safeguards.",
                requirement="Implement administrative safeguards for PHI protection."
            ),
            ComplianceControl(
                control_id="HIPAA.164.310",
                framework=ComplianceFramework.HIPAA,
                title="Physical Safeguards",
                description="A covered entity must implement physical safeguards.",
                requirement="Implement physical safeguards for PHI protection."
            )
        ]
        
        # Add all controls
        all_controls = soc2_controls + gdpr_controls + hipaa_controls
        for control in all_controls:
            self.controls[control.control_id] = control
    
    def get_controls_by_framework(self, framework: ComplianceFramework) -> List[ComplianceControl]:
        """Get all controls for a specific framework."""
        return [control for control in self.controls.values() if control.framework == framework]
    
    def update_control_status(
        self,
        control_id: str,
        status: ComplianceStatus,
        evidence: Optional[List[str]] = None,
        responsible_party: Optional[str] = None,
        remediation_notes: Optional[str] = None
    ) -> bool:
        """
        Update compliance control status.
        
        Args:
            control_id: Control identifier
            status: New compliance status
            evidence: Supporting evidence
            responsible_party: Responsible party
            remediation_notes: Remediation notes
            
        Returns:
            True if updated successfully, False otherwise
        """
        if control_id not in self.controls:
            logger.warning(f"Control {control_id} not found")
            return False
        
        control = self.controls[control_id]
        control.status = status
        control.last_assessed = datetime.now()
        control.next_assessment = datetime.now() + timedelta(days=90)  # Quarterly assessment
        
        if evidence:
            control.evidence.extend(evidence)
        
        if responsible_party:
            control.responsible_party = responsible_party
        
        if remediation_notes:
            control.remediation_notes = remediation_notes
        
        logger.info(f"Updated control {control_id} status to {status.value}")
        return True
    
    def assess_framework_compliance(self, framework: ComplianceFramework) -> ComplianceReport:
        """
        Assess compliance for a specific framework.
        
        Args:
            framework: Compliance framework to assess
            
        Returns:
            Compliance assessment report
        """
        controls = self.get_controls_by_framework(framework)
        
        if not controls:
            logger.warning(f"No controls found for framework {framework.value}")
            return ComplianceReport(
                report_id=str(uuid.uuid4()),
                framework=framework,
                generated_at=datetime.now(),
                assessment_period_start=datetime.now() - timedelta(days=90),
                assessment_period_end=datetime.now(),
                overall_status=ComplianceStatus.NOT_APPLICABLE,
                controls_assessed=0,
                controls_compliant=0,
                controls_non_compliant=0,
                controls_partial=0,
                compliance_score=0.0
            )
        
        # Count control statuses
        controls_assessed = len(controls)
        controls_compliant = len([c for c in controls if c.status == ComplianceStatus.COMPLIANT])
        controls_non_compliant = len([c for c in controls if c.status == ComplianceStatus.NON_COMPLIANT])
        controls_partial = len([c for c in controls if c.status == ComplianceStatus.PARTIALLY_COMPLIANT])
        
        # Calculate compliance score
        compliance_score = (controls_compliant / controls_assessed) * 100 if controls_assessed > 0 else 0.0
        
        # Determine overall status
        if compliance_score >= 95:
            overall_status = ComplianceStatus.COMPLIANT
        elif compliance_score >= 70:
            overall_status = ComplianceStatus.PARTIALLY_COMPLIANT
        else:
            overall_status = ComplianceStatus.NON_COMPLIANT
        
        # Generate findings and recommendations
        findings = []
        recommendations = []
        
        non_compliant_controls = [c for c in controls if c.status == ComplianceStatus.NON_COMPLIANT]
        if non_compliant_controls:
            findings.append(f"{len(non_compliant_controls)} controls are non-compliant")
            recommendations.append("Address non-compliant controls with immediate remediation plans")
        
        partial_controls = [c for c in controls if c.status == ComplianceStatus.PARTIALLY_COMPLIANT]
        if partial_controls:
            findings.append(f"{len(partial_controls)} controls are partially compliant")
            recommendations.append("Complete implementation of partially compliant controls")
        
        under_review_controls = [c for c in controls if c.status == ComplianceStatus.UNDER_REVIEW]
        if under_review_controls:
            findings.append(f"{len(under_review_controls)} controls are under review")
            recommendations.append("Complete assessment of controls under review")
        
        # Create report
        report = ComplianceReport(
            report_id=str(uuid.uuid4()),
            framework=framework,
            generated_at=datetime.now(),
            assessment_period_start=datetime.now() - timedelta(days=90),
            assessment_period_end=datetime.now(),
            overall_status=overall_status,
            controls_assessed=controls_assessed,
            controls_compliant=controls_compliant,
            controls_non_compliant=controls_non_compliant,
            controls_partial=controls_partial,
            compliance_score=compliance_score,
            findings=findings,
            recommendations=recommendations
        )
        
        # Store report
        self.reports[report.report_id] = report
        
        logger.info(f"Generated compliance report for {framework.value}: {compliance_score:.1f}% compliant")
        return report
    
    def get_compliance_dashboard(self) -> Dict[str, Any]:
        """Get compliance dashboard data."""
        dashboard_data = {
            "frameworks": {},
            "overall_summary": {
                "total_controls": len(self.controls),
                "total_reports": len(self.reports),
                "frameworks_tracked": len(set(c.framework for c in self.controls.values()))
            }
        }
        
        # Framework-specific data
        for framework in ComplianceFramework:
            controls = self.get_controls_by_framework(framework)
            if controls:
                compliant = len([c for c in controls if c.status == ComplianceStatus.COMPLIANT])
                total = len(controls)
                compliance_rate = (compliant / total * 100) if total > 0 else 0.0
                
                dashboard_data["frameworks"][framework.value] = {
                    "total_controls": total,
                    "compliant_controls": compliant,
                    "compliance_rate": compliance_rate,
                    "last_assessment": max(
                        (c.last_assessed for c in controls if c.last_assessed),
                        default=None
                    )
                }
        
        return dashboard_data
    
    def get_control_details(self, control_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific control."""
        if control_id not in self.controls:
            return None
        
        return self.controls[control_id].to_dict()
    
    def get_report(self, report_id: str) -> Optional[Dict[str, Any]]:
        """Get compliance report by ID."""
        if report_id not in self.reports:
            return None
        
        return self.reports[report_id].to_dict()
    
    def list_reports(self, framework: Optional[ComplianceFramework] = None) -> List[Dict[str, Any]]:
        """List compliance reports, optionally filtered by framework."""
        reports = list(self.reports.values())
        
        if framework:
            reports = [r for r in reports if r.framework == framework]
        
        # Sort by generation date (newest first)
        reports.sort(key=lambda r: r.generated_at, reverse=True)
        
        return [r.to_dict() for r in reports]
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get compliance manager statistics."""
        total_controls = len(self.controls)
        compliant_controls = len([c for c in self.controls.values() if c.status == ComplianceStatus.COMPLIANT])
        
        return {
            "total_controls": total_controls,
            "compliant_controls": compliant_controls,
            "overall_compliance_rate": (compliant_controls / total_controls * 100) if total_controls > 0 else 0.0,
            "frameworks_supported": [f.value for f in ComplianceFramework],
            "total_reports": len(self.reports),
            "controls_by_framework": {
                f.value: len(self.get_controls_by_framework(f)) for f in ComplianceFramework
            }
        }
    
    async def cleanup(self) -> None:
        """Cleanup compliance manager."""
        logger.info("Compliance manager cleaned up")
