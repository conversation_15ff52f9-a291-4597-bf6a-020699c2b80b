"""
File: vibe_check/enterprise/security/access_control.py
Purpose: Access control and authorization system for enterprise security
Related Files: vibe_check/enterprise/security/
Dependencies: typing, datetime, enum, dataclasses
"""

from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field
from enum import Enum
import uuid

from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class Permission(Enum):
    """System permissions."""
    READ = "read"
    WRITE = "write"
    DELETE = "delete"
    EXECUTE = "execute"
    ADMIN = "admin"
    MANAGE_USERS = "manage_users"
    MANAGE_ROLES = "manage_roles"
    VIEW_AUDIT = "view_audit"
    MANAGE_COMPLIANCE = "manage_compliance"
    GENERATE_REPORTS = "generate_reports"
    MANAGE_INTEGRATIONS = "manage_integrations"
    VIEW_DASHBOARDS = "view_dashboards"
    MANAGE_DASHBOARDS = "manage_dashboards"


@dataclass
class Role:
    """User role with permissions."""
    role_id: str
    name: str
    description: str
    permissions: Set[Permission] = field(default_factory=set)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def has_permission(self, permission: Permission) -> bool:
        """Check if role has specific permission."""
        return permission in self.permissions or Permission.ADMIN in self.permissions
    
    def add_permission(self, permission: Permission) -> None:
        """Add permission to role."""
        self.permissions.add(permission)
        self.updated_at = datetime.now()
    
    def remove_permission(self, permission: Permission) -> None:
        """Remove permission from role."""
        self.permissions.discard(permission)
        self.updated_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "role_id": self.role_id,
            "name": self.name,
            "description": self.description,
            "permissions": [p.value for p in self.permissions],
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }


@dataclass
class SecurityPolicy:
    """Security policy configuration."""
    policy_id: str
    name: str
    description: str
    password_min_length: int = 8
    password_require_uppercase: bool = True
    password_require_lowercase: bool = True
    password_require_numbers: bool = True
    password_require_symbols: bool = True
    session_timeout_minutes: int = 480  # 8 hours
    max_login_attempts: int = 5
    lockout_duration_minutes: int = 30
    require_mfa: bool = False
    allowed_ip_ranges: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "policy_id": self.policy_id,
            "name": self.name,
            "description": self.description,
            "password_min_length": self.password_min_length,
            "password_require_uppercase": self.password_require_uppercase,
            "password_require_lowercase": self.password_require_lowercase,
            "password_require_numbers": self.password_require_numbers,
            "password_require_symbols": self.password_require_symbols,
            "session_timeout_minutes": self.session_timeout_minutes,
            "max_login_attempts": self.max_login_attempts,
            "lockout_duration_minutes": self.lockout_duration_minutes,
            "require_mfa": self.require_mfa,
            "allowed_ip_ranges": self.allowed_ip_ranges
        }


class AccessController:
    """Enterprise access control system."""
    
    def __init__(self):
        """Initialize access controller."""
        self.roles: Dict[str, Role] = {}
        self.user_roles: Dict[str, Set[str]] = {}  # user_id -> role_ids
        self.security_policy = SecurityPolicy(
            policy_id="default",
            name="Default Security Policy",
            description="Default enterprise security policy"
        )
        self._initialized = False
        
        # Initialize default roles
        self._initialize_default_roles()
    
    async def initialize(self) -> None:
        """Initialize access controller."""
        if self._initialized:
            return
        
        self._initialized = True
        logger.info(f"Access controller initialized with {len(self.roles)} roles")
    
    def _initialize_default_roles(self) -> None:
        """Initialize default system roles."""
        # Super Admin Role
        super_admin = Role(
            role_id="super_admin",
            name="Super Administrator",
            description="Full system access with all permissions",
            permissions={Permission.ADMIN}  # ADMIN implies all permissions
        )
        
        # Admin Role
        admin = Role(
            role_id="admin",
            name="Administrator",
            description="Administrative access with user and system management",
            permissions={
                Permission.READ, Permission.WRITE, Permission.DELETE,
                Permission.MANAGE_USERS, Permission.MANAGE_ROLES,
                Permission.VIEW_AUDIT, Permission.MANAGE_COMPLIANCE,
                Permission.GENERATE_REPORTS, Permission.MANAGE_INTEGRATIONS,
                Permission.VIEW_DASHBOARDS, Permission.MANAGE_DASHBOARDS
            }
        )
        
        # Team Lead Role
        team_lead = Role(
            role_id="team_lead",
            name="Team Lead",
            description="Team leadership with project management capabilities",
            permissions={
                Permission.READ, Permission.WRITE,
                Permission.GENERATE_REPORTS, Permission.VIEW_DASHBOARDS,
                Permission.MANAGE_DASHBOARDS
            }
        )
        
        # Developer Role
        developer = Role(
            role_id="developer",
            name="Developer",
            description="Development access with read/write permissions",
            permissions={
                Permission.READ, Permission.WRITE, Permission.EXECUTE,
                Permission.VIEW_DASHBOARDS
            }
        )
        
        # Analyst Role
        analyst = Role(
            role_id="analyst",
            name="Analyst",
            description="Analysis and reporting access",
            permissions={
                Permission.READ, Permission.GENERATE_REPORTS,
                Permission.VIEW_DASHBOARDS
            }
        )
        
        # Viewer Role
        viewer = Role(
            role_id="viewer",
            name="Viewer",
            description="Read-only access to system resources",
            permissions={Permission.READ, Permission.VIEW_DASHBOARDS}
        )
        
        # Add all roles
        for role in [super_admin, admin, team_lead, developer, analyst, viewer]:
            self.roles[role.role_id] = role
    
    def create_role(
        self,
        name: str,
        description: str,
        permissions: Optional[List[Permission]] = None
    ) -> str:
        """
        Create a new role.
        
        Args:
            name: Role name
            description: Role description
            permissions: List of permissions
            
        Returns:
            Role ID
        """
        role_id = str(uuid.uuid4())
        role = Role(
            role_id=role_id,
            name=name,
            description=description,
            permissions=set(permissions or [])
        )
        
        self.roles[role_id] = role
        logger.info(f"Created role: {name} ({role_id})")
        return role_id
    
    def assign_role_to_user(self, user_id: str, role_id: str) -> bool:
        """
        Assign role to user.
        
        Args:
            user_id: User ID
            role_id: Role ID
            
        Returns:
            True if assigned successfully, False otherwise
        """
        if role_id not in self.roles:
            logger.warning(f"Role {role_id} not found")
            return False
        
        if user_id not in self.user_roles:
            self.user_roles[user_id] = set()
        
        self.user_roles[user_id].add(role_id)
        logger.info(f"Assigned role {role_id} to user {user_id}")
        return True
    
    def remove_role_from_user(self, user_id: str, role_id: str) -> bool:
        """
        Remove role from user.
        
        Args:
            user_id: User ID
            role_id: Role ID
            
        Returns:
            True if removed successfully, False otherwise
        """
        if user_id not in self.user_roles:
            return False
        
        self.user_roles[user_id].discard(role_id)
        logger.info(f"Removed role {role_id} from user {user_id}")
        return True
    
    def check_permission(self, user_id: str, permission: Permission) -> bool:
        """
        Check if user has specific permission.
        
        Args:
            user_id: User ID
            permission: Permission to check
            
        Returns:
            True if user has permission, False otherwise
        """
        if user_id not in self.user_roles:
            return False
        
        user_role_ids = self.user_roles[user_id]
        
        for role_id in user_role_ids:
            role = self.roles.get(role_id)
            if role and role.has_permission(permission):
                return True
        
        return False
    
    def get_user_permissions(self, user_id: str) -> Set[Permission]:
        """
        Get all permissions for a user.
        
        Args:
            user_id: User ID
            
        Returns:
            Set of permissions
        """
        permissions = set()
        
        if user_id not in self.user_roles:
            return permissions
        
        user_role_ids = self.user_roles[user_id]
        
        for role_id in user_role_ids:
            role = self.roles.get(role_id)
            if role:
                if Permission.ADMIN in role.permissions:
                    # Admin has all permissions
                    return set(Permission)
                permissions.update(role.permissions)
        
        return permissions
    
    def get_user_roles(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Get roles assigned to user.
        
        Args:
            user_id: User ID
            
        Returns:
            List of role dictionaries
        """
        if user_id not in self.user_roles:
            return []
        
        user_role_ids = self.user_roles[user_id]
        roles = []
        
        for role_id in user_role_ids:
            role = self.roles.get(role_id)
            if role:
                roles.append(role.to_dict())
        
        return roles
    
    def list_roles(self) -> List[Dict[str, Any]]:
        """List all available roles."""
        return [role.to_dict() for role in self.roles.values()]
    
    def get_role(self, role_id: str) -> Optional[Dict[str, Any]]:
        """Get role by ID."""
        role = self.roles.get(role_id)
        return role.to_dict() if role else None
    
    def update_role_permissions(self, role_id: str, permissions: List[Permission]) -> bool:
        """
        Update role permissions.
        
        Args:
            role_id: Role ID
            permissions: New list of permissions
            
        Returns:
            True if updated successfully, False otherwise
        """
        if role_id not in self.roles:
            logger.warning(f"Role {role_id} not found")
            return False
        
        role = self.roles[role_id]
        role.permissions = set(permissions)
        role.updated_at = datetime.now()
        
        logger.info(f"Updated permissions for role {role_id}")
        return True
    
    def validate_password(self, password: str) -> Dict[str, Any]:
        """
        Validate password against security policy.
        
        Args:
            password: Password to validate
            
        Returns:
            Validation result with details
        """
        policy = self.security_policy
        errors = []
        
        if len(password) < policy.password_min_length:
            errors.append(f"Password must be at least {policy.password_min_length} characters long")
        
        if policy.password_require_uppercase and not any(c.isupper() for c in password):
            errors.append("Password must contain at least one uppercase letter")
        
        if policy.password_require_lowercase and not any(c.islower() for c in password):
            errors.append("Password must contain at least one lowercase letter")
        
        if policy.password_require_numbers and not any(c.isdigit() for c in password):
            errors.append("Password must contain at least one number")
        
        if policy.password_require_symbols and not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            errors.append("Password must contain at least one special character")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "policy": policy.to_dict()
        }
    
    def is_ip_allowed(self, ip_address: str) -> bool:
        """
        Check if IP address is allowed by security policy.
        
        Args:
            ip_address: IP address to check
            
        Returns:
            True if allowed, False otherwise
        """
        if not self.security_policy.allowed_ip_ranges:
            return True  # No restrictions
        
        # Simple IP range check (in production, use proper CIDR matching)
        for allowed_range in self.security_policy.allowed_ip_ranges:
            if ip_address.startswith(allowed_range.split('/')[0].rsplit('.', 1)[0]):
                return True
        
        return False
    
    def update_security_policy(self, policy_updates: Dict[str, Any]) -> bool:
        """
        Update security policy.
        
        Args:
            policy_updates: Dictionary of policy updates
            
        Returns:
            True if updated successfully, False otherwise
        """
        try:
            for key, value in policy_updates.items():
                if hasattr(self.security_policy, key):
                    setattr(self.security_policy, key, value)
            
            logger.info("Security policy updated")
            return True
        
        except Exception as e:
            logger.error(f"Failed to update security policy: {e}")
            return False
    
    def get_access_summary(self) -> Dict[str, Any]:
        """Get access control summary."""
        total_users = len(self.user_roles)
        total_roles = len(self.roles)
        
        # Count users by role
        role_usage = {}
        for user_id, role_ids in self.user_roles.items():
            for role_id in role_ids:
                role = self.roles.get(role_id)
                if role:
                    role_usage[role.name] = role_usage.get(role.name, 0) + 1
        
        return {
            "total_users": total_users,
            "total_roles": total_roles,
            "role_usage": role_usage,
            "security_policy": self.security_policy.to_dict(),
            "available_permissions": [p.value for p in Permission]
        }
    
    async def cleanup(self) -> None:
        """Cleanup access controller."""
        logger.info("Access controller cleaned up")
