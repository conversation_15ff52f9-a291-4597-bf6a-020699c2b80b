"""
File: vibe_check/enterprise/reporting/engine.py
Purpose: Enterprise reporting engine for multi-format report generation
Related Files: vibe_check/enterprise/reporting/
Dependencies: typing, pathlib, datetime
"""

from datetime import datetime
from pathlib import Path
from dataclasses import dataclass
from enum import Enum

from vibe_check.core.vcs.models import AnalysisResult
from vibe_check.core.vcs.integration.meta_analyzer import MetaAnalysisResult
from vibe_check.core.logging import get_logger
from typing import Any, Dict, List, Optional

logger = get_logger(__name__)


class ReportFormat(Enum):
    """Supported report formats."""
    PDF = "pdf"
    EXCEL = "excel"
    HTML = "html"
    JSON = "json"
    MARKDOWN = "markdown"
    CSV = "csv"


class ReportType(Enum):
    """Types of enterprise reports."""
    EXECUTIVE_SUMMARY = "executive_summary"
    DETAILED_ANALYSIS = "detailed_analysis"
    COMPLIANCE_REPORT = "compliance_report"
    TREND_ANALYSIS = "trend_analysis"
    TEAM_PERFORMANCE = "team_performance"
    SECURITY_AUDIT = "security_audit"


@dataclass
class ReportConfiguration:
    """Configuration for enterprise report generation."""
    report_type: ReportType
    format: ReportFormat
    include_executive_summary: bool = True
    include_detailed_findings: bool = True
    include_recommendations: bool = True
    include_trends: bool = False
    include_team_metrics: bool = False
    custom_branding: bool = False
    confidentiality_level: str = "internal"
    distribution_list: List[str] = None
    
    def __post_init__(self):
        if self.distribution_list is None:
            self.distribution_list = []


@dataclass
class EnterpriseReport:
    """Enterprise report with metadata and content."""
    report_id: str
    title: str
    generated_at: datetime
    configuration: ReportConfiguration
    content: Dict[str, Any]
    file_path: Optional[Path] = None
    size_bytes: int = 0
    generation_time_seconds: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "report_id": self.report_id,
            "title": self.title,
            "generated_at": self.generated_at.isoformat(),
            "configuration": {
                "report_type": self.configuration.report_type.value,
                "format": self.configuration.format.value,
                "include_executive_summary": self.configuration.include_executive_summary,
                "include_detailed_findings": self.configuration.include_detailed_findings,
                "include_recommendations": self.configuration.include_recommendations,
                "include_trends": self.configuration.include_trends,
                "include_team_metrics": self.configuration.include_team_metrics,
                "confidentiality_level": self.configuration.confidentiality_level
            },
            "file_path": str(self.file_path) if self.file_path else None,
            "size_bytes": self.size_bytes,
            "generation_time_seconds": self.generation_time_seconds
        }


class EnterpriseReportingEngine:
    """Enterprise-grade reporting engine with multi-format support."""
    
    def __init__(self, output_dir: Optional[Path] = None):
        """
        Initialize enterprise reporting engine.
        
        Args:
            output_dir: Directory for report output
        """
        self.output_dir = output_dir or Path.cwd() / "enterprise_reports"
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Report generators by format
        self._generators = {}
        self._templates = {}
        self._initialized = False
        
        # Report history and tracking
        self.report_history: List[EnterpriseReport] = []
        self.active_reports: Dict[str, EnterpriseReport] = {}
    
    async def initialize(self) -> None:
        """Initialize the reporting engine."""
        if self._initialized:
            return
        
        # Initialize report generators
        await self._initialize_generators()
        
        # Load report templates
        await self._load_templates()
        
        self._initialized = True
        logger.info("Enterprise reporting engine initialized")
    
    async def _initialize_generators(self) -> None:
        """Initialize format-specific report generators."""
        try:
            from .formats import (
                PDFReportGenerator, ExcelReportGenerator,
                HTMLReportGenerator, JSONReportGenerator
            )
            
            self._generators = {
                ReportFormat.PDF: PDFReportGenerator(),
                ReportFormat.EXCEL: ExcelReportGenerator(),
                ReportFormat.HTML: HTMLReportGenerator(),
                ReportFormat.JSON: JSONReportGenerator()
            }
            
            # Initialize each generator
            for generator in self._generators.values():
                await generator.initialize()
            
            logger.debug(f"Initialized {len(self._generators)} report generators")
            
        except ImportError as e:
            logger.warning(f"Some report generators not available: {e}")
            # Fallback to basic generators
            self._generators = {
                ReportFormat.JSON: JSONReportGenerator(),
                ReportFormat.HTML: HTMLReportGenerator()
            }
    
    async def _load_templates(self) -> None:
        """Load report templates."""
        try:
            from .templates import TemplateManager
            
            template_manager = TemplateManager()
            await template_manager.initialize()
            
            self._templates = await template_manager.load_all_templates()
            
            logger.debug(f"Loaded {len(self._templates)} report templates")
            
        except Exception as e:
            logger.warning(f"Failed to load templates: {e}")
            self._templates = {}
    
    async def generate_report(
        self,
        analysis_results: List[AnalysisResult],
        meta_analysis: Optional[MetaAnalysisResult] = None,
        configuration: Optional[ReportConfiguration] = None,
        project_name: str = "Unknown Project"
    ) -> EnterpriseReport:
        """
        Generate enterprise report from analysis results.
        
        Args:
            analysis_results: Analysis results to include in report
            meta_analysis: Optional meta-analysis results
            configuration: Report configuration
            project_name: Name of the analyzed project
            
        Returns:
            Generated enterprise report
        """
        if not self._initialized:
            await self.initialize()
        
        start_time = datetime.now()
        
        # Use default configuration if none provided
        if configuration is None:
            configuration = ReportConfiguration(
                report_type=ReportType.DETAILED_ANALYSIS,
                format=ReportFormat.HTML
            )
        
        # Generate unique report ID
        report_id = f"ent_{int(start_time.timestamp())}_{configuration.format.value}"
        
        # Prepare report content
        content = await self._prepare_report_content(
            analysis_results, meta_analysis, configuration, project_name
        )
        
        # Generate report using appropriate generator
        generator = self._generators.get(configuration.format)
        if not generator:
            raise ValueError(f"No generator available for format: {configuration.format}")
        
        # Generate the actual report file
        file_path = await generator.generate(
            content, self.output_dir, report_id, configuration
        )
        
        # Calculate generation time and file size
        generation_time = (datetime.now() - start_time).total_seconds()
        file_size = file_path.stat().st_size if file_path.exists() else 0
        
        # Create enterprise report object
        report = EnterpriseReport(
            report_id=report_id,
            title=f"{project_name} - {configuration.report_type.value.replace('_', ' ').title()}",
            generated_at=start_time,
            configuration=configuration,
            content=content,
            file_path=file_path,
            size_bytes=file_size,
            generation_time_seconds=generation_time
        )
        
        # Track report
        self.report_history.append(report)
        self.active_reports[report_id] = report
        
        logger.info(f"Generated enterprise report: {report_id} ({configuration.format.value})")
        return report
    
    async def _prepare_report_content(
        self,
        analysis_results: List[AnalysisResult],
        meta_analysis: Optional[MetaAnalysisResult],
        configuration: ReportConfiguration,
        project_name: str
    ) -> Dict[str, Any]:
        """Prepare structured content for report generation."""
        content = {
            "metadata": {
                "project_name": project_name,
                "generated_at": datetime.now().isoformat(),
                "report_type": configuration.report_type.value,
                "total_files": len(analysis_results),
                "confidentiality": configuration.confidentiality_level
            },
            "summary": {},
            "detailed_findings": [],
            "recommendations": [],
            "appendices": {}
        }
        
        # Generate executive summary if requested
        if configuration.include_executive_summary:
            content["summary"] = await self._generate_executive_summary(
                analysis_results, meta_analysis
            )
        
        # Include detailed findings if requested
        if configuration.include_detailed_findings:
            content["detailed_findings"] = await self._generate_detailed_findings(
                analysis_results
            )
        
        # Include recommendations if requested
        if configuration.include_recommendations:
            content["recommendations"] = await self._generate_recommendations(
                analysis_results, meta_analysis
            )
        
        # Include trend analysis if requested and available
        if configuration.include_trends and meta_analysis:
            content["trends"] = meta_analysis.trends
        
        return content
    
    async def _generate_executive_summary(
        self,
        analysis_results: List[AnalysisResult],
        meta_analysis: Optional[MetaAnalysisResult]
    ) -> Dict[str, Any]:
        """Generate executive summary section."""
        total_issues = sum(len(result.issues) for result in analysis_results)
        successful_analyses = sum(1 for result in analysis_results if result.success)
        
        summary = {
            "overview": {
                "total_files_analyzed": len(analysis_results),
                "successful_analyses": successful_analyses,
                "total_issues_found": total_issues,
                "analysis_success_rate": (successful_analyses / len(analysis_results)) * 100 if analysis_results else 0
            },
            "key_metrics": {
                "average_issues_per_file": total_issues / len(analysis_results) if analysis_results else 0,
                "quality_score": meta_analysis.quality_score if meta_analysis else None
            },
            "critical_findings": [],
            "business_impact": {
                "risk_level": "Low",
                "maintenance_burden": "Moderate",
                "technical_debt": "Manageable"
            }
        }
        
        # Determine risk level based on issues
        if total_issues > 100:
            summary["business_impact"]["risk_level"] = "High"
        elif total_issues > 50:
            summary["business_impact"]["risk_level"] = "Medium"
        
        return summary
    
    async def _generate_detailed_findings(
        self,
        analysis_results: List[AnalysisResult]
    ) -> List[Dict[str, Any]]:
        """Generate detailed findings section."""
        findings = []
        
        for result in analysis_results:
            if result.issues:
                finding = {
                    "file_path": str(result.target.path) if result.target.path else "unknown",
                    "issue_count": len(result.issues),
                    "issues": [
                        {
                            "rule_id": issue.rule_id,
                            "severity": issue.severity.value,
                            "category": issue.category.value,
                            "message": issue.message,
                            "line": issue.line,
                            "column": issue.column,
                            "fix_suggestion": issue.fix_suggestion
                        }
                        for issue in result.issues
                    ]
                }
                findings.append(finding)
        
        return findings
    
    async def _generate_recommendations(
        self,
        analysis_results: List[AnalysisResult],
        meta_analysis: Optional[MetaAnalysisResult]
    ) -> List[Dict[str, Any]]:
        """Generate recommendations section."""
        recommendations = []
        
        # Add meta-analysis recommendations if available
        if meta_analysis and meta_analysis.recommendations:
            for rec in meta_analysis.recommendations:
                recommendations.append({
                    "type": "strategic",
                    "priority": "high",
                    "description": rec,
                    "impact": "project-wide"
                })
        
        # Add tactical recommendations based on common issues
        issue_counts = {}
        for result in analysis_results:
            for issue in result.issues:
                issue_counts[issue.rule_id] = issue_counts.get(issue.rule_id, 0) + 1
        
        # Top 3 most common issues
        top_issues = sorted(issue_counts.items(), key=lambda x: x[1], reverse=True)[:3]
        
        for rule_id, count in top_issues:
            recommendations.append({
                "type": "tactical",
                "priority": "medium",
                "description": f"Address {count} instances of {rule_id}",
                "impact": "code quality"
            })
        
        return recommendations
    
    def get_report_history(self) -> List[Dict[str, Any]]:
        """Get history of generated reports."""
        return [report.to_dict() for report in self.report_history]
    
    def get_report_statistics(self) -> Dict[str, Any]:
        """Get reporting engine statistics."""
        if not self.report_history:
            return {"total_reports": 0}
        
        total_reports = len(self.report_history)
        total_size = sum(report.size_bytes for report in self.report_history)
        avg_generation_time = sum(report.generation_time_seconds for report in self.report_history) / total_reports
        
        format_counts = {}
        for report in self.report_history:
            format_name = report.configuration.format.value
            format_counts[format_name] = format_counts.get(format_name, 0) + 1
        
        return {
            "total_reports": total_reports,
            "total_size_bytes": total_size,
            "average_generation_time_seconds": avg_generation_time,
            "format_distribution": format_counts,
            "available_formats": [fmt.value for fmt in ReportFormat],
            "available_generators": list(self._generators.keys())
        }
    
    async def cleanup(self) -> None:
        """Cleanup reporting engine resources."""
        # Cleanup generators
        for generator in self._generators.values():
            if hasattr(generator, 'cleanup'):
                await generator.cleanup()
        
        logger.info("Enterprise reporting engine cleaned up")


# Placeholder generators - will be implemented in formats.py
class BaseReportGenerator:
    """Base class for report generators."""

    async def initialize(self) -> None:
        """Initialize the generator."""
        pass

    async def generate(self, content: Dict[str, Any], output_dir: Path,
                      report_id: str, configuration) -> Path:
        """Generate report file."""
        raise NotImplementedError

    async def cleanup(self) -> None:
        """Cleanup generator resources."""
        pass
