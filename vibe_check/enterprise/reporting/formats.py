"""
File: vibe_check/enterprise/reporting/formats.py
Purpose: Format-specific report generators for enterprise reporting
Related Files: vibe_check/enterprise/reporting/engine.py
Dependencies: typing, pathlib, json
"""

import json
from pathlib import Path
from datetime import datetime

from vibe_check.core.logging import get_logger
from typing import Any, Dict

logger = get_logger(__name__)


class BaseReportGenerator:
    """Base class for report generators."""
    
    def __init__(self):
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize the generator."""
        self._initialized = True
    
    async def generate(self, content: Dict[str, Any], output_dir: Path, 
                      report_id: str, configuration: Any) -> Path:
        """Generate report file."""
        raise NotImplementedError
    
    async def cleanup(self) -> None:
        """Cleanup generator resources."""
        pass


class JSONReportGenerator(BaseReportGenerator):
    """JSON format report generator."""
    
    async def generate(self, content: Dict[str, Any], output_dir: Path, 
                      report_id: str, configuration: Any) -> Path:
        """Generate JSON report."""
        filename = f"{report_id}.json"
        file_path = output_dir / filename
        
        # Add generation metadata
        report_data = {
            "report_metadata": {
                "id": report_id,
                "format": "json",
                "generated_at": datetime.now().isoformat(),
                "generator_version": "1.0.0"
            },
            "content": content
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, default=str)
        
        logger.debug(f"Generated JSON report: {file_path}")
        return file_path


class HTMLReportGenerator(BaseReportGenerator):
    """HTML format report generator."""
    
    async def generate(self, content: Dict[str, Any], output_dir: Path, 
                      report_id: str, configuration: Any) -> Path:
        """Generate HTML report."""
        filename = f"{report_id}.html"
        file_path = output_dir / filename
        
        html_content = self._generate_html_content(content, report_id)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.debug(f"Generated HTML report: {file_path}")
        return file_path
    
    def _generate_html_content(self, content: Dict[str, Any], report_id: str) -> str:
        """Generate HTML content from report data."""
        metadata = content.get("metadata", {})
        summary = content.get("summary", {})
        findings = content.get("detailed_findings", [])
        recommendations = content.get("recommendations", [])
        
        html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enterprise Analysis Report - {metadata.get('project_name', 'Unknown')}</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .header {{
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }}
        .header h1 {{
            color: #333;
            margin: 0;
            font-size: 2.5em;
        }}
        .header .subtitle {{
            color: #666;
            font-size: 1.1em;
            margin-top: 10px;
        }}
        .section {{
            margin-bottom: 40px;
        }}
        .section h2 {{
            color: #007bff;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }}
        .metric-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        .metric-card {{
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }}
        .metric-value {{
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }}
        .metric-label {{
            color: #666;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }}
        .finding {{
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
        }}
        .finding-header {{
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
        }}
        .issue {{
            background: #f8f9fa;
            padding: 10px;
            margin: 5px 0;
            border-left: 3px solid #dc3545;
            border-radius: 3px;
        }}
        .issue.warning {{
            border-left-color: #ffc107;
        }}
        .issue.info {{
            border-left-color: #17a2b8;
        }}
        .recommendation {{
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
        }}
        .recommendation.high {{
            border-left: 4px solid #28a745;
        }}
        .recommendation.medium {{
            border-left: 4px solid #ffc107;
        }}
        .footer {{
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #666;
            font-size: 0.9em;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Enterprise Analysis Report</h1>
            <div class="subtitle">
                Project: {metadata.get('project_name', 'Unknown')} | 
                Generated: {metadata.get('generated_at', 'Unknown')} |
                Report ID: {report_id}
            </div>
        </div>
        
        <div class="section">
            <h2>Executive Summary</h2>
            <div class="metric-grid">
                <div class="metric-card">
                    <div class="metric-value">{summary.get('overview', {}).get('total_files_analyzed', 0)}</div>
                    <div class="metric-label">Files Analyzed</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{summary.get('overview', {}).get('total_issues_found', 0)}</div>
                    <div class="metric-label">Issues Found</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{summary.get('overview', {}).get('analysis_success_rate', 0):.1f}%</div>
                    <div class="metric-label">Success Rate</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{summary.get('key_metrics', {}).get('quality_score', 'N/A')}</div>
                    <div class="metric-label">Quality Score</div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>Detailed Findings</h2>
            {self._generate_findings_html(findings)}
        </div>
        
        <div class="section">
            <h2>Recommendations</h2>
            {self._generate_recommendations_html(recommendations)}
        </div>
        
        <div class="footer">
            <p>Generated by Vibe Check Enterprise Reporting Engine</p>
            <p>Confidentiality Level: {metadata.get('confidentiality', 'Internal')}</p>
        </div>
    </div>
</body>
</html>
        """
        
        return html
    
    def _generate_findings_html(self, findings: list) -> str:
        """Generate HTML for findings section."""
        if not findings:
            return "<p>No issues found in the analyzed files.</p>"
        
        html_parts = []
        for finding in findings[:10]:  # Limit to first 10 files
            file_path = finding.get('file_path', 'Unknown')
            issue_count = finding.get('issue_count', 0)
            issues = finding.get('issues', [])
            
            html_parts.append(f"""
            <div class="finding">
                <div class="finding-header">{file_path} ({issue_count} issues)</div>
                {self._generate_issues_html(issues[:5])}  <!-- Show first 5 issues -->
            </div>
            """)
        
        return "".join(html_parts)
    
    def _generate_issues_html(self, issues: list) -> str:
        """Generate HTML for individual issues."""
        html_parts = []
        for issue in issues:
            severity = issue.get('severity', 'info').lower()
            css_class = 'issue'
            if severity in ['warning', 'info']:
                css_class += f' {severity}'
            
            html_parts.append(f"""
            <div class="{css_class}">
                <strong>{issue.get('rule_id', 'Unknown')}</strong>: {issue.get('message', 'No message')}
                <br><small>Line {issue.get('line', 0)}, Column {issue.get('column', 0)}</small>
            </div>
            """)
        
        return "".join(html_parts)
    
    def _generate_recommendations_html(self, recommendations: list) -> str:
        """Generate HTML for recommendations section."""
        if not recommendations:
            return "<p>No specific recommendations at this time.</p>"
        
        html_parts = []
        for rec in recommendations:
            priority = rec.get('priority', 'medium').lower()
            css_class = f'recommendation {priority}'
            
            html_parts.append(f"""
            <div class="{css_class}">
                <strong>{rec.get('type', 'General').title()} Recommendation</strong>
                <p>{rec.get('description', 'No description available')}</p>
                <small>Priority: {rec.get('priority', 'Medium').title()} | Impact: {rec.get('impact', 'Unknown')}</small>
            </div>
            """)
        
        return "".join(html_parts)


class PDFReportGenerator(BaseReportGenerator):
    """PDF format report generator."""
    
    async def initialize(self) -> None:
        """Initialize PDF generator."""
        try:
            # Try to import PDF generation libraries
            import weasyprint  # or reportlab
            self.pdf_available = True
        except ImportError:
            logger.warning("PDF generation libraries not available")
            self.pdf_available = False
        
        await super().initialize()
    
    async def generate(self, content: Dict[str, Any], output_dir: Path, 
                      report_id: str, configuration: Any) -> Path:
        """Generate PDF report."""
        if not self.pdf_available:
            # Fallback to HTML
            logger.warning("PDF generation not available, falling back to HTML")
            html_generator = HTMLReportGenerator()
            return await html_generator.generate(content, output_dir, report_id, configuration)
        
        filename = f"{report_id}.pdf"
        file_path = output_dir / filename
        
        # Generate HTML first, then convert to PDF
        html_generator = HTMLReportGenerator()
        html_content = html_generator._generate_html_content(content, report_id)
        
        try:
            import weasyprint
            weasyprint.HTML(string=html_content).write_pdf(str(file_path))
        except Exception as e:
            logger.error(f"PDF generation failed: {e}")
            # Fallback to HTML
            return await html_generator.generate(content, output_dir, report_id, configuration)
        
        logger.debug(f"Generated PDF report: {file_path}")
        return file_path


class ExcelReportGenerator(BaseReportGenerator):
    """Excel format report generator."""
    
    async def initialize(self) -> None:
        """Initialize Excel generator."""
        try:
            import openpyxl
            self.excel_available = True
        except ImportError:
            logger.warning("Excel generation libraries not available")
            self.excel_available = False
        
        await super().initialize()
    
    async def generate(self, content: Dict[str, Any], output_dir: Path, 
                      report_id: str, configuration: Any) -> Path:
        """Generate Excel report."""
        if not self.excel_available:
            # Fallback to JSON
            logger.warning("Excel generation not available, falling back to JSON")
            json_generator = JSONReportGenerator()
            return await json_generator.generate(content, output_dir, report_id, configuration)
        
        filename = f"{report_id}.xlsx"
        file_path = output_dir / filename
        
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill
            
            workbook = openpyxl.Workbook()
            
            # Summary sheet
            summary_sheet = workbook.active
            summary_sheet.title = "Summary"
            
            # Add summary data
            summary = content.get("summary", {})
            overview = summary.get("overview", {})
            
            summary_sheet['A1'] = "Metric"
            summary_sheet['B1'] = "Value"
            summary_sheet['A1'].font = Font(bold=True)
            summary_sheet['B1'].font = Font(bold=True)
            
            row = 2
            for key, value in overview.items():
                summary_sheet[f'A{row}'] = key.replace('_', ' ').title()
                summary_sheet[f'B{row}'] = value
                row += 1
            
            # Findings sheet
            findings_sheet = workbook.create_sheet("Findings")
            findings_sheet['A1'] = "File"
            findings_sheet['B1'] = "Rule ID"
            findings_sheet['C1'] = "Severity"
            findings_sheet['D1'] = "Message"
            findings_sheet['E1'] = "Line"
            
            # Style headers
            for col in ['A1', 'B1', 'C1', 'D1', 'E1']:
                findings_sheet[col].font = Font(bold=True)
                findings_sheet[col].fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
            
            # Add findings data
            row = 2
            findings = content.get("detailed_findings", [])
            for finding in findings:
                file_path = finding.get('file_path', 'Unknown')
                for issue in finding.get('issues', []):
                    findings_sheet[f'A{row}'] = file_path
                    findings_sheet[f'B{row}'] = issue.get('rule_id', '')
                    findings_sheet[f'C{row}'] = issue.get('severity', '')
                    findings_sheet[f'D{row}'] = issue.get('message', '')
                    findings_sheet[f'E{row}'] = issue.get('line', 0)
                    row += 1
            
            workbook.save(str(file_path))
            
        except Exception as e:
            logger.error(f"Excel generation failed: {e}")
            # Fallback to JSON
            json_generator = JSONReportGenerator()
            return await json_generator.generate(content, output_dir, report_id, configuration)
        
        logger.debug(f"Generated Excel report: {file_path}")
        return file_path
