"""
File: vibe_check/enterprise/reporting/executive.py
Purpose: Executive summary generation for enterprise reports
Related Files: vibe_check/enterprise/reporting/engine.py
Dependencies: typing, dataclasses
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime
from enum import Enum

from vibe_check.core.vcs.models import AnalysisResult, RuleCategory, IssueSeverity
from vibe_check.core.vcs.integration.meta_analyzer import MetaAnalysisResult
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class RiskLevel(Enum):
    """Risk assessment levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class BusinessImpact(Enum):
    """Business impact categories."""
    MINIMAL = "minimal"
    MODERATE = "moderate"
    SIGNIFICANT = "significant"
    SEVERE = "severe"


@dataclass
class ExecutiveMetrics:
    """Key metrics for executive summary."""
    total_files_analyzed: int
    total_issues_found: int
    critical_issues: int
    high_priority_issues: int
    quality_score: Optional[float]
    technical_debt_hours: float
    maintenance_burden_score: float
    security_risk_score: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "total_files_analyzed": self.total_files_analyzed,
            "total_issues_found": self.total_issues_found,
            "critical_issues": self.critical_issues,
            "high_priority_issues": self.high_priority_issues,
            "quality_score": self.quality_score,
            "technical_debt_hours": self.technical_debt_hours,
            "maintenance_burden_score": self.maintenance_burden_score,
            "security_risk_score": self.security_risk_score
        }


@dataclass
class RiskAssessment:
    """Risk assessment for executive summary."""
    overall_risk_level: RiskLevel
    security_risk: RiskLevel
    maintenance_risk: RiskLevel
    performance_risk: RiskLevel
    compliance_risk: RiskLevel
    business_impact: BusinessImpact
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "overall_risk_level": self.overall_risk_level.value,
            "security_risk": self.security_risk.value,
            "maintenance_risk": self.maintenance_risk.value,
            "performance_risk": self.performance_risk.value,
            "compliance_risk": self.compliance_risk.value,
            "business_impact": self.business_impact.value
        }


@dataclass
class StrategicRecommendation:
    """Strategic recommendation for executives."""
    title: str
    description: str
    business_justification: str
    estimated_effort_days: int
    expected_roi: str
    priority: str
    timeline: str
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "title": self.title,
            "description": self.description,
            "business_justification": self.business_justification,
            "estimated_effort_days": self.estimated_effort_days,
            "expected_roi": self.expected_roi,
            "priority": self.priority,
            "timeline": self.timeline
        }


@dataclass
class ExecutiveSummary:
    """Complete executive summary."""
    project_name: str
    analysis_date: datetime
    metrics: ExecutiveMetrics
    risk_assessment: RiskAssessment
    key_findings: List[str]
    strategic_recommendations: List[StrategicRecommendation]
    next_steps: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "project_name": self.project_name,
            "analysis_date": self.analysis_date.isoformat(),
            "metrics": self.metrics.to_dict(),
            "risk_assessment": self.risk_assessment.to_dict(),
            "key_findings": self.key_findings,
            "strategic_recommendations": [rec.to_dict() for rec in self.strategic_recommendations],
            "next_steps": self.next_steps
        }


class ExecutiveSummaryGenerator:
    """Generates executive summaries from analysis results."""
    
    def __init__(self):
        self.severity_weights = {
            IssueSeverity.ERROR: 10,
            IssueSeverity.WARNING: 5,
            IssueSeverity.INFO: 1
        }
        
        self.category_risk_multipliers = {
            RuleCategory.SECURITY: 3.0,
            RuleCategory.COMPLEXITY: 2.0,
            RuleCategory.STYLE: 1.0,
            RuleCategory.DOCS: 1.0,
            RuleCategory.IMPORTS: 1.5,
            RuleCategory.TYPES: 1.5
        }
    
    async def generate_executive_summary(
        self,
        project_name: str,
        analysis_results: List[AnalysisResult],
        meta_analysis: Optional[MetaAnalysisResult] = None
    ) -> ExecutiveSummary:
        """
        Generate comprehensive executive summary.
        
        Args:
            project_name: Name of the analyzed project
            analysis_results: Analysis results from all files
            meta_analysis: Optional meta-analysis results
            
        Returns:
            Complete executive summary
        """
        logger.info(f"Generating executive summary for {project_name}")
        
        # Calculate metrics
        metrics = self._calculate_executive_metrics(analysis_results, meta_analysis)
        
        # Assess risks
        risk_assessment = self._assess_risks(analysis_results, metrics)
        
        # Generate key findings
        key_findings = self._generate_key_findings(analysis_results, metrics, risk_assessment)
        
        # Generate strategic recommendations
        strategic_recommendations = self._generate_strategic_recommendations(
            analysis_results, metrics, risk_assessment
        )
        
        # Generate next steps
        next_steps = self._generate_next_steps(risk_assessment, strategic_recommendations)
        
        return ExecutiveSummary(
            project_name=project_name,
            analysis_date=datetime.now(),
            metrics=metrics,
            risk_assessment=risk_assessment,
            key_findings=key_findings,
            strategic_recommendations=strategic_recommendations,
            next_steps=next_steps
        )
    
    def _calculate_executive_metrics(
        self,
        analysis_results: List[AnalysisResult],
        meta_analysis: Optional[MetaAnalysisResult]
    ) -> ExecutiveMetrics:
        """Calculate key metrics for executives."""
        total_files = len(analysis_results)
        all_issues = []
        
        for result in analysis_results:
            all_issues.extend(result.issues)
        
        total_issues = len(all_issues)
        
        # Count critical and high priority issues
        critical_issues = sum(1 for issue in all_issues if issue.severity == IssueSeverity.ERROR)
        high_priority_issues = sum(1 for issue in all_issues if issue.severity == IssueSeverity.WARNING)
        
        # Calculate technical debt (rough estimate)
        technical_debt_hours = self._estimate_technical_debt(all_issues)
        
        # Calculate maintenance burden score (0-100)
        maintenance_burden_score = min(100, (total_issues / max(total_files, 1)) * 10)
        
        # Calculate security risk score (0-100)
        security_issues = [issue for issue in all_issues if issue.category == RuleCategory.SECURITY]
        security_risk_score = min(100, len(security_issues) * 5)
        
        return ExecutiveMetrics(
            total_files_analyzed=total_files,
            total_issues_found=total_issues,
            critical_issues=critical_issues,
            high_priority_issues=high_priority_issues,
            quality_score=meta_analysis.quality_score if meta_analysis else None,
            technical_debt_hours=technical_debt_hours,
            maintenance_burden_score=maintenance_burden_score,
            security_risk_score=security_risk_score
        )
    
    def _estimate_technical_debt(self, issues: List) -> float:
        """Estimate technical debt in hours."""
        # Rough estimates for fixing different types of issues
        debt_hours = 0.0
        
        for issue in issues:
            if issue.severity == IssueSeverity.ERROR:
                debt_hours += 2.0  # 2 hours per error
            elif issue.severity == IssueSeverity.WARNING:
                debt_hours += 0.5  # 30 minutes per warning
            else:
                debt_hours += 0.1  # 6 minutes per info
            
            # Multiply by category complexity
            multiplier = self.category_risk_multipliers.get(issue.category, 1.0)
            debt_hours *= multiplier
        
        return debt_hours
    
    def _assess_risks(self, analysis_results: List[AnalysisResult], metrics: ExecutiveMetrics) -> RiskAssessment:
        """Assess various risk levels."""
        # Overall risk based on quality score and issue count
        if metrics.quality_score is not None:
            if metrics.quality_score < 50:
                overall_risk = RiskLevel.HIGH
            elif metrics.quality_score < 70:
                overall_risk = RiskLevel.MEDIUM
            else:
                overall_risk = RiskLevel.LOW
        else:
            # Fallback based on issue density
            issue_density = metrics.total_issues_found / max(metrics.total_files_analyzed, 1)
            if issue_density > 10:
                overall_risk = RiskLevel.HIGH
            elif issue_density > 5:
                overall_risk = RiskLevel.MEDIUM
            else:
                overall_risk = RiskLevel.LOW
        
        # Security risk
        if metrics.security_risk_score > 50:
            security_risk = RiskLevel.HIGH
        elif metrics.security_risk_score > 20:
            security_risk = RiskLevel.MEDIUM
        else:
            security_risk = RiskLevel.LOW
        
        # Maintenance risk
        if metrics.maintenance_burden_score > 70:
            maintenance_risk = RiskLevel.HIGH
        elif metrics.maintenance_burden_score > 40:
            maintenance_risk = RiskLevel.MEDIUM
        else:
            maintenance_risk = RiskLevel.LOW
        
        # Performance risk (based on complexity issues)
        complexity_issues = 0
        for result in analysis_results:
            complexity_issues += sum(1 for issue in result.issues if issue.category == RuleCategory.COMPLEXITY)
        
        if complexity_issues > 20:
            performance_risk = RiskLevel.HIGH
        elif complexity_issues > 10:
            performance_risk = RiskLevel.MEDIUM
        else:
            performance_risk = RiskLevel.LOW
        
        # Compliance risk (placeholder)
        compliance_risk = RiskLevel.LOW
        
        # Business impact
        if overall_risk == RiskLevel.HIGH:
            business_impact = BusinessImpact.SIGNIFICANT
        elif overall_risk == RiskLevel.MEDIUM:
            business_impact = BusinessImpact.MODERATE
        else:
            business_impact = BusinessImpact.MINIMAL
        
        return RiskAssessment(
            overall_risk_level=overall_risk,
            security_risk=security_risk,
            maintenance_risk=maintenance_risk,
            performance_risk=performance_risk,
            compliance_risk=compliance_risk,
            business_impact=business_impact
        )
    
    def _generate_key_findings(
        self,
        analysis_results: List[AnalysisResult],
        metrics: ExecutiveMetrics,
        risk_assessment: RiskAssessment
    ) -> List[str]:
        """Generate key findings for executives."""
        findings = []
        
        # Quality score finding
        if metrics.quality_score is not None:
            if metrics.quality_score >= 90:
                findings.append(f"Excellent code quality with a score of {metrics.quality_score:.1f}/100")
            elif metrics.quality_score >= 70:
                findings.append(f"Good code quality with a score of {metrics.quality_score:.1f}/100")
            else:
                findings.append(f"Code quality needs improvement with a score of {metrics.quality_score:.1f}/100")
        
        # Issue density finding
        issue_density = metrics.total_issues_found / max(metrics.total_files_analyzed, 1)
        if issue_density > 10:
            findings.append(f"High issue density detected: {issue_density:.1f} issues per file")
        elif issue_density > 5:
            findings.append(f"Moderate issue density: {issue_density:.1f} issues per file")
        
        # Security finding
        if metrics.critical_issues > 0:
            findings.append(f"{metrics.critical_issues} critical security issues require immediate attention")
        
        # Technical debt finding
        if metrics.technical_debt_hours > 100:
            findings.append(f"Estimated {metrics.technical_debt_hours:.0f} hours of technical debt")
        
        # Risk assessment finding
        if risk_assessment.overall_risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL]:
            findings.append("Project poses significant risk to business objectives")
        
        return findings
    
    def _generate_strategic_recommendations(
        self,
        analysis_results: List[AnalysisResult],
        metrics: ExecutiveMetrics,
        risk_assessment: RiskAssessment
    ) -> List[StrategicRecommendation]:
        """Generate strategic recommendations for executives."""
        recommendations = []
        
        # High-level recommendations based on risk assessment
        if risk_assessment.security_risk in [RiskLevel.HIGH, RiskLevel.CRITICAL]:
            recommendations.append(StrategicRecommendation(
                title="Immediate Security Review",
                description="Conduct comprehensive security audit and remediation",
                business_justification="Prevent potential security breaches and compliance violations",
                estimated_effort_days=10,
                expected_roi="Risk mitigation worth 10x investment",
                priority="Critical",
                timeline="1-2 weeks"
            ))
        
        if metrics.technical_debt_hours > 200:
            recommendations.append(StrategicRecommendation(
                title="Technical Debt Reduction Initiative",
                description="Systematic approach to reduce technical debt",
                business_justification="Improve development velocity and reduce maintenance costs",
                estimated_effort_days=30,
                expected_roi="20% improvement in development speed",
                priority="High",
                timeline="1-2 months"
            ))
        
        if risk_assessment.maintenance_risk == RiskLevel.HIGH:
            recommendations.append(StrategicRecommendation(
                title="Code Quality Improvement Program",
                description="Implement automated quality gates and developer training",
                business_justification="Reduce long-term maintenance costs and improve reliability",
                estimated_effort_days=15,
                expected_roi="30% reduction in bug reports",
                priority="Medium",
                timeline="1 month"
            ))
        
        return recommendations
    
    def _generate_next_steps(
        self,
        risk_assessment: RiskAssessment,
        strategic_recommendations: List[StrategicRecommendation]
    ) -> List[str]:
        """Generate actionable next steps."""
        next_steps = []
        
        # Immediate actions based on risk level
        if risk_assessment.overall_risk_level == RiskLevel.HIGH:
            next_steps.append("Schedule emergency code review meeting within 48 hours")
            next_steps.append("Assign dedicated team to address critical issues")
        
        # Actions based on recommendations
        for rec in strategic_recommendations:
            if rec.priority == "Critical":
                next_steps.append(f"Initiate {rec.title} immediately")
            elif rec.priority == "High":
                next_steps.append(f"Plan {rec.title} for next sprint")
        
        # Standard next steps
        next_steps.extend([
            "Review detailed findings with development team",
            "Establish regular code quality monitoring",
            "Schedule follow-up analysis in 30 days"
        ])
        
        return next_steps
