"""
File: vibe_check/enterprise/reporting/templates.py
Purpose: Report template management for enterprise reporting
Related Files: vibe_check/enterprise/reporting/engine.py
Dependencies: typing, pathlib, json
"""

import json
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class TemplateType(Enum):
    """Types of report templates."""
    EXECUTIVE_SUMMARY = "executive_summary"
    DETAILED_TECHNICAL = "detailed_technical"
    COMPLIANCE_AUDIT = "compliance_audit"
    SECURITY_ASSESSMENT = "security_assessment"
    PERFORMANCE_REVIEW = "performance_review"
    TEAM_DASHBOARD = "team_dashboard"


@dataclass
class TemplateSection:
    """A section within a report template."""
    name: str
    title: str
    description: str
    required: bool = True
    order: int = 0
    content_type: str = "text"
    formatting: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.formatting is None:
            self.formatting = {}


@dataclass
class ReportTemplate:
    """Report template definition."""
    template_id: str
    name: str
    description: str
    template_type: TemplateType
    sections: List[TemplateSection]
    metadata: Dict[str, Any] = None
    version: str = "1.0.0"
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "template_id": self.template_id,
            "name": self.name,
            "description": self.description,
            "template_type": self.template_type.value,
            "version": self.version,
            "metadata": self.metadata,
            "sections": [
                {
                    "name": section.name,
                    "title": section.title,
                    "description": section.description,
                    "required": section.required,
                    "order": section.order,
                    "content_type": section.content_type,
                    "formatting": section.formatting
                }
                for section in self.sections
            ]
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ReportTemplate':
        """Create from dictionary."""
        sections = []
        for section_data in data.get("sections", []):
            sections.append(TemplateSection(
                name=section_data["name"],
                title=section_data["title"],
                description=section_data["description"],
                required=section_data.get("required", True),
                order=section_data.get("order", 0),
                content_type=section_data.get("content_type", "text"),
                formatting=section_data.get("formatting", {})
            ))
        
        return cls(
            template_id=data["template_id"],
            name=data["name"],
            description=data["description"],
            template_type=TemplateType(data["template_type"]),
            sections=sections,
            metadata=data.get("metadata", {}),
            version=data.get("version", "1.0.0")
        )


class TemplateManager:
    """Manages report templates."""
    
    def __init__(self, templates_dir: Optional[Path] = None):
        """
        Initialize template manager.
        
        Args:
            templates_dir: Directory containing template files
        """
        self.templates_dir = templates_dir or Path(__file__).parent / "templates"
        self.templates: Dict[str, ReportTemplate] = {}
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize template manager."""
        if self._initialized:
            return
        
        # Create templates directory if it doesn't exist
        self.templates_dir.mkdir(parents=True, exist_ok=True)
        
        # Load built-in templates
        await self._create_built_in_templates()
        
        # Load custom templates from files
        await self._load_custom_templates()
        
        self._initialized = True
        logger.info(f"Template manager initialized with {len(self.templates)} templates")
    
    async def _create_built_in_templates(self) -> None:
        """Create built-in report templates."""
        # Executive Summary Template
        executive_template = ReportTemplate(
            template_id="executive_summary_v1",
            name="Executive Summary Report",
            description="High-level summary for executive stakeholders",
            template_type=TemplateType.EXECUTIVE_SUMMARY,
            sections=[
                TemplateSection(
                    name="overview",
                    title="Executive Overview",
                    description="High-level project assessment",
                    order=1,
                    content_type="summary"
                ),
                TemplateSection(
                    name="key_metrics",
                    title="Key Performance Indicators",
                    description="Critical metrics and scores",
                    order=2,
                    content_type="metrics"
                ),
                TemplateSection(
                    name="risk_assessment",
                    title="Risk Assessment",
                    description="Business and technical risks",
                    order=3,
                    content_type="risk_matrix"
                ),
                TemplateSection(
                    name="recommendations",
                    title="Strategic Recommendations",
                    description="High-level action items",
                    order=4,
                    content_type="recommendations"
                ),
                TemplateSection(
                    name="next_steps",
                    title="Next Steps",
                    description="Immediate action items",
                    order=5,
                    content_type="action_items"
                )
            ]
        )
        
        # Detailed Technical Template
        technical_template = ReportTemplate(
            template_id="detailed_technical_v1",
            name="Detailed Technical Report",
            description="Comprehensive technical analysis for development teams",
            template_type=TemplateType.DETAILED_TECHNICAL,
            sections=[
                TemplateSection(
                    name="summary",
                    title="Technical Summary",
                    description="Overview of technical findings",
                    order=1,
                    content_type="summary"
                ),
                TemplateSection(
                    name="code_quality",
                    title="Code Quality Analysis",
                    description="Detailed code quality metrics",
                    order=2,
                    content_type="quality_metrics"
                ),
                TemplateSection(
                    name="issues_by_category",
                    title="Issues by Category",
                    description="Breakdown of issues by type",
                    order=3,
                    content_type="category_breakdown"
                ),
                TemplateSection(
                    name="detailed_findings",
                    title="Detailed Findings",
                    description="File-by-file analysis results",
                    order=4,
                    content_type="detailed_issues"
                ),
                TemplateSection(
                    name="recommendations",
                    title="Technical Recommendations",
                    description="Specific technical improvements",
                    order=5,
                    content_type="technical_recommendations"
                ),
                TemplateSection(
                    name="appendices",
                    title="Appendices",
                    description="Supporting data and references",
                    order=6,
                    content_type="appendices",
                    required=False
                )
            ]
        )
        
        # Security Assessment Template
        security_template = ReportTemplate(
            template_id="security_assessment_v1",
            name="Security Assessment Report",
            description="Security-focused analysis and recommendations",
            template_type=TemplateType.SECURITY_ASSESSMENT,
            sections=[
                TemplateSection(
                    name="security_overview",
                    title="Security Overview",
                    description="High-level security assessment",
                    order=1,
                    content_type="security_summary"
                ),
                TemplateSection(
                    name="vulnerability_analysis",
                    title="Vulnerability Analysis",
                    description="Identified security vulnerabilities",
                    order=2,
                    content_type="vulnerabilities"
                ),
                TemplateSection(
                    name="risk_matrix",
                    title="Security Risk Matrix",
                    description="Risk assessment and prioritization",
                    order=3,
                    content_type="risk_matrix"
                ),
                TemplateSection(
                    name="compliance_check",
                    title="Compliance Assessment",
                    description="Compliance with security standards",
                    order=4,
                    content_type="compliance"
                ),
                TemplateSection(
                    name="remediation_plan",
                    title="Remediation Plan",
                    description="Security improvement recommendations",
                    order=5,
                    content_type="remediation"
                )
            ]
        )
        
        # Store built-in templates
        self.templates[executive_template.template_id] = executive_template
        self.templates[technical_template.template_id] = technical_template
        self.templates[security_template.template_id] = security_template
        
        logger.debug("Created built-in report templates")
    
    async def _load_custom_templates(self) -> None:
        """Load custom templates from files."""
        if not self.templates_dir.exists():
            return
        
        for template_file in self.templates_dir.glob("*.json"):
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    template_data = json.load(f)
                
                template = ReportTemplate.from_dict(template_data)
                self.templates[template.template_id] = template
                
                logger.debug(f"Loaded custom template: {template.template_id}")
                
            except Exception as e:
                logger.warning(f"Failed to load template {template_file}: {e}")
    
    async def load_all_templates(self) -> Dict[str, ReportTemplate]:
        """Load and return all available templates."""
        if not self._initialized:
            await self.initialize()
        
        return self.templates.copy()
    
    def get_template(self, template_id: str) -> Optional[ReportTemplate]:
        """Get a specific template by ID."""
        return self.templates.get(template_id)
    
    def get_templates_by_type(self, template_type: TemplateType) -> List[ReportTemplate]:
        """Get all templates of a specific type."""
        return [
            template for template in self.templates.values()
            if template.template_type == template_type
        ]
    
    async def save_template(self, template: ReportTemplate) -> None:
        """Save a template to file."""
        template_file = self.templates_dir / f"{template.template_id}.json"
        
        try:
            with open(template_file, 'w', encoding='utf-8') as f:
                json.dump(template.to_dict(), f, indent=2)
            
            # Update in-memory templates
            self.templates[template.template_id] = template
            
            logger.info(f"Saved template: {template.template_id}")
            
        except Exception as e:
            logger.error(f"Failed to save template {template.template_id}: {e}")
            raise
    
    async def delete_template(self, template_id: str) -> bool:
        """Delete a template."""
        if template_id not in self.templates:
            return False
        
        # Remove from memory
        del self.templates[template_id]
        
        # Remove file if it exists
        template_file = self.templates_dir / f"{template_id}.json"
        if template_file.exists():
            try:
                template_file.unlink()
                logger.info(f"Deleted template file: {template_file}")
            except Exception as e:
                logger.warning(f"Failed to delete template file {template_file}: {e}")
        
        logger.info(f"Deleted template: {template_id}")
        return True
    
    def list_templates(self) -> List[Dict[str, Any]]:
        """List all available templates with metadata."""
        return [
            {
                "template_id": template.template_id,
                "name": template.name,
                "description": template.description,
                "type": template.template_type.value,
                "version": template.version,
                "sections_count": len(template.sections)
            }
            for template in self.templates.values()
        ]
    
    def get_template_statistics(self) -> Dict[str, Any]:
        """Get template system statistics."""
        type_counts = {}
        for template in self.templates.values():
            template_type = template.template_type.value
            type_counts[template_type] = type_counts.get(template_type, 0) + 1
        
        return {
            "total_templates": len(self.templates),
            "templates_by_type": type_counts,
            "templates_directory": str(self.templates_dir),
            "available_types": [t.value for t in TemplateType]
        }
