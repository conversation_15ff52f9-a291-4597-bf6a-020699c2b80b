"""
File: vibe_check/enterprise/reporting/customization.py
Purpose: Report customization and branding for enterprise reporting
Related Files: vibe_check/enterprise/reporting/engine.py
Dependencies: typing, pathlib
"""

from pathlib import Path
from dataclasses import dataclass

from vibe_check.core.logging import get_logger
from typing import Any, Dict, Optional

logger = get_logger(__name__)


@dataclass
class BrandingConfiguration:
    """Branding configuration for enterprise reports."""
    company_name: str
    logo_path: Optional[Path] = None
    primary_color: str = "#007bff"
    secondary_color: str = "#6c757d"
    font_family: str = "Arial, sans-serif"
    header_template: Optional[str] = None
    footer_template: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "company_name": self.company_name,
            "logo_path": str(self.logo_path) if self.logo_path else None,
            "primary_color": self.primary_color,
            "secondary_color": self.secondary_color,
            "font_family": self.font_family,
            "header_template": self.header_template,
            "footer_template": self.footer_template
        }


class ReportCustomizer:
    """Customizes report appearance and content."""
    
    def __init__(self):
        self.customizations: Dict[str, Any] = {}
    
    def apply_branding(self, content: Dict[str, Any], branding: BrandingConfiguration) -> Dict[str, Any]:
        """Apply branding to report content."""
        # Add branding information to metadata
        if "metadata" not in content:
            content["metadata"] = {}
        
        content["metadata"]["branding"] = branding.to_dict()
        
        # Apply custom styling if needed
        if "styling" not in content:
            content["styling"] = {}
        
        content["styling"].update({
            "primary_color": branding.primary_color,
            "secondary_color": branding.secondary_color,
            "font_family": branding.font_family
        })
        
        return content
    
    def customize_sections(self, content: Dict[str, Any], customizations: Dict[str, Any]) -> Dict[str, Any]:
        """Apply section-level customizations."""
        for section_name, section_config in customizations.items():
            if section_name in content:
                # Apply section-specific customizations
                if "title" in section_config:
                    content[section_name]["custom_title"] = section_config["title"]
                
                if "visibility" in section_config:
                    content[section_name]["visible"] = section_config["visibility"]
        
        return content


class BrandingManager:
    """Manages branding configurations for different organizations."""
    
    def __init__(self, branding_dir: Optional[Path] = None):
        """
        Initialize branding manager.
        
        Args:
            branding_dir: Directory containing branding configurations
        """
        self.branding_dir = branding_dir or Path.cwd() / "enterprise_branding"
        self.branding_configs: Dict[str, BrandingConfiguration] = {}
    
    def load_branding_config(self, config_name: str) -> Optional[BrandingConfiguration]:
        """Load a branding configuration."""
        return self.branding_configs.get(config_name)
    
    def save_branding_config(self, config_name: str, branding: BrandingConfiguration) -> None:
        """Save a branding configuration."""
        self.branding_configs[config_name] = branding
        logger.info(f"Saved branding configuration: {config_name}")
    
    def get_default_branding(self) -> BrandingConfiguration:
        """Get default branding configuration."""
        return BrandingConfiguration(
            company_name="Your Organization",
            primary_color="#007bff",
            secondary_color="#6c757d"
        )
