"""
File: vibe_check/ai/temporal/debt_predictor.py
Purpose: Technical debt prediction system
Related Files: vibe_check/ai/temporal/
Dependencies: typing, asyncio, datetime, enum, dataclasses
"""

from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import math

from vibe_check.core.logging import get_logger
from typing import Any, Dict, List

logger = get_logger(__name__)


class DebtCategory(Enum):
    """Categories of technical debt."""
    CODE_COMPLEXITY = "code_complexity"
    DOCUMENTATION = "documentation"
    TESTING = "testing"
    ARCHITECTURE = "architecture"
    PERFORMANCE = "performance"
    SECURITY = "security"
    DEPENDENCIES = "dependencies"
    MAINTAINABILITY = "maintainability"


class DebtSeverity(Enum):
    """Severity levels of technical debt."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class DebtItem:
    """Individual technical debt item."""
    debt_id: str
    category: DebtCategory
    severity: DebtSeverity
    title: str
    description: str
    estimated_cost_hours: float
    interest_rate: float  # How much the debt grows over time
    location: str
    introduced_date: datetime
    last_updated: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "debt_id": self.debt_id,
            "category": self.category.value,
            "severity": self.severity.value,
            "title": self.title,
            "description": self.description,
            "estimated_cost_hours": self.estimated_cost_hours,
            "interest_rate": self.interest_rate,
            "location": self.location,
            "introduced_date": self.introduced_date.isoformat(),
            "last_updated": self.last_updated.isoformat()
        }


@dataclass
class DebtPrediction:
    """Technical debt prediction result."""
    prediction_id: str
    category: DebtCategory
    current_debt_hours: float
    predicted_debt_hours: float
    prediction_timeframe: timedelta
    confidence: float
    growth_rate: float
    risk_factors: List[str] = field(default_factory=list)
    mitigation_strategies: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "prediction_id": self.prediction_id,
            "category": self.category.value,
            "current_debt_hours": self.current_debt_hours,
            "predicted_debt_hours": self.predicted_debt_hours,
            "prediction_timeframe_days": self.prediction_timeframe.days,
            "confidence": self.confidence,
            "growth_rate": self.growth_rate,
            "risk_factors": self.risk_factors,
            "mitigation_strategies": self.mitigation_strategies
        }


@dataclass
class DebtReport:
    """Comprehensive technical debt analysis report."""
    report_id: str
    analysis_date: datetime
    total_debt_hours: float
    debt_by_category: Dict[str, float] = field(default_factory=dict)
    debt_by_severity: Dict[str, float] = field(default_factory=dict)
    debt_items: List[DebtItem] = field(default_factory=list)
    predictions: List[DebtPrediction] = field(default_factory=list)
    priority_recommendations: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "report_id": self.report_id,
            "analysis_date": self.analysis_date.isoformat(),
            "total_debt_hours": self.total_debt_hours,
            "debt_by_category": self.debt_by_category,
            "debt_by_severity": self.debt_by_severity,
            "debt_items": [item.to_dict() for item in self.debt_items],
            "predictions": [pred.to_dict() for pred in self.predictions],
            "priority_recommendations": self.priority_recommendations
        }


class DebtDetectionRules:
    """Rules for detecting technical debt."""
    
    def __init__(self):
        self.debt_indicators = {
            DebtCategory.CODE_COMPLEXITY: {
                "cyclomatic_complexity": 10,
                "function_length": 50,
                "parameter_count": 6,
                "nesting_depth": 4
            },
            DebtCategory.DOCUMENTATION: {
                "missing_docstrings": 0.3,  # 30% missing
                "comment_ratio": 0.1,       # Less than 10% comments
                "outdated_docs": 0.2        # 20% outdated
            },
            DebtCategory.TESTING: {
                "test_coverage": 0.8,       # Less than 80%
                "test_quality": 0.7,        # Test quality score
                "missing_tests": 0.2        # 20% missing tests
            },
            DebtCategory.ARCHITECTURE: {
                "coupling": 0.7,            # High coupling
                "cohesion": 0.6,            # Low cohesion
                "circular_dependencies": 1  # Any circular deps
            }
        }
    
    def detect_debt_items(self, code_metrics: Dict[str, Any]) -> List[DebtItem]:
        """Detect technical debt items from code metrics."""
        debt_items = []
        current_time = datetime.now()
        
        # Code complexity debt
        complexity = code_metrics.get("cyclomatic_complexity", 0)
        if complexity > self.debt_indicators[DebtCategory.CODE_COMPLEXITY]["cyclomatic_complexity"]:
            severity = DebtSeverity.HIGH if complexity > 20 else DebtSeverity.MEDIUM
            debt_items.append(DebtItem(
                debt_id=f"complexity_{current_time.timestamp()}",
                category=DebtCategory.CODE_COMPLEXITY,
                severity=severity,
                title="High cyclomatic complexity",
                description=f"Code has cyclomatic complexity of {complexity}, exceeding recommended threshold",
                estimated_cost_hours=complexity * 0.5,  # Rough estimate
                interest_rate=0.1,  # 10% growth per month
                location="Multiple functions",
                introduced_date=current_time - timedelta(days=30),  # Estimate
                last_updated=current_time
            ))
        
        # Documentation debt
        doc_ratio = code_metrics.get("documentation_ratio", 0)
        if doc_ratio < self.debt_indicators[DebtCategory.DOCUMENTATION]["missing_docstrings"]:
            debt_items.append(DebtItem(
                debt_id=f"documentation_{current_time.timestamp()}",
                category=DebtCategory.DOCUMENTATION,
                severity=DebtSeverity.MEDIUM,
                title="Missing documentation",
                description=f"Only {doc_ratio:.1%} of functions have documentation",
                estimated_cost_hours=(1 - doc_ratio) * 20,  # Estimate based on missing docs
                interest_rate=0.05,  # 5% growth per month
                location="Multiple functions and classes",
                introduced_date=current_time - timedelta(days=60),
                last_updated=current_time
            ))
        
        # Testing debt
        test_coverage = code_metrics.get("test_coverage", 0)
        if test_coverage < self.debt_indicators[DebtCategory.TESTING]["test_coverage"]:
            severity = DebtSeverity.HIGH if test_coverage < 0.5 else DebtSeverity.MEDIUM
            debt_items.append(DebtItem(
                debt_id=f"testing_{current_time.timestamp()}",
                category=DebtCategory.TESTING,
                severity=severity,
                title="Insufficient test coverage",
                description=f"Test coverage is {test_coverage:.1%}, below recommended 80%",
                estimated_cost_hours=(0.8 - test_coverage) * 40,  # Estimate based on missing coverage
                interest_rate=0.15,  # 15% growth per month (testing debt grows fast)
                location="Multiple modules",
                introduced_date=current_time - timedelta(days=45),
                last_updated=current_time
            ))
        
        # Performance debt (if metrics available)
        performance_score = code_metrics.get("performance_score", 1.0)
        if performance_score < 0.7:
            debt_items.append(DebtItem(
                debt_id=f"performance_{current_time.timestamp()}",
                category=DebtCategory.PERFORMANCE,
                severity=DebtSeverity.MEDIUM,
                title="Performance issues",
                description=f"Performance score is {performance_score:.2f}, indicating potential bottlenecks",
                estimated_cost_hours=(0.8 - performance_score) * 30,
                interest_rate=0.08,  # 8% growth per month
                location="Performance-critical sections",
                introduced_date=current_time - timedelta(days=20),
                last_updated=current_time
            ))
        
        return debt_items
    
    def calculate_debt_interest(self, debt_item: DebtItem, time_elapsed: timedelta) -> float:
        """Calculate accumulated interest on technical debt."""
        months_elapsed = time_elapsed.days / 30.0
        compound_factor = (1 + debt_item.interest_rate) ** months_elapsed
        return debt_item.estimated_cost_hours * (compound_factor - 1)


class DebtPredictionModel:
    """Predictive model for technical debt growth."""
    
    def __init__(self):
        self.growth_models = {
            "linear": self._linear_growth,
            "exponential": self._exponential_growth,
            "compound": self._compound_growth
        }
    
    def predict_debt_growth(
        self,
        debt_items: List[DebtItem],
        prediction_timeframe: timedelta,
        model_type: str = "compound"
    ) -> List[DebtPrediction]:
        """Predict technical debt growth."""
        if model_type not in self.growth_models:
            model_type = "compound"
        
        predictions = []
        
        # Group debt by category
        debt_by_category = {}
        for item in debt_items:
            category = item.category
            if category not in debt_by_category:
                debt_by_category[category] = []
            debt_by_category[category].append(item)
        
        # Generate predictions for each category
        for category, items in debt_by_category.items():
            current_debt = sum(item.estimated_cost_hours for item in items)
            avg_interest_rate = sum(item.interest_rate for item in items) / len(items)
            
            predicted_debt = self.growth_models[model_type](
                current_debt, avg_interest_rate, prediction_timeframe
            )
            
            # Calculate confidence based on data quality
            confidence = self._calculate_confidence(items, prediction_timeframe)
            
            # Calculate growth rate
            growth_rate = (predicted_debt - current_debt) / max(1, current_debt)
            
            # Generate risk factors and mitigation strategies
            risk_factors = self._identify_risk_factors(category, items, growth_rate)
            mitigation_strategies = self._generate_mitigation_strategies(category, items)
            
            prediction = DebtPrediction(
                prediction_id=f"pred_{category.value}_{datetime.now().timestamp()}",
                category=category,
                current_debt_hours=current_debt,
                predicted_debt_hours=predicted_debt,
                prediction_timeframe=prediction_timeframe,
                confidence=confidence,
                growth_rate=growth_rate,
                risk_factors=risk_factors,
                mitigation_strategies=mitigation_strategies
            )
            
            predictions.append(prediction)
        
        return predictions
    
    def _linear_growth(self, current_debt: float, interest_rate: float, timeframe: timedelta) -> float:
        """Linear growth model."""
        months = timeframe.days / 30.0
        return current_debt + (current_debt * interest_rate * months)
    
    def _exponential_growth(self, current_debt: float, interest_rate: float, timeframe: timedelta) -> float:
        """Exponential growth model."""
        months = timeframe.days / 30.0
        return current_debt * math.exp(interest_rate * months)
    
    def _compound_growth(self, current_debt: float, interest_rate: float, timeframe: timedelta) -> float:
        """Compound growth model."""
        months = timeframe.days / 30.0
        return current_debt * ((1 + interest_rate) ** months)
    
    def _calculate_confidence(self, debt_items: List[DebtItem], timeframe: timedelta) -> float:
        """Calculate confidence in prediction."""
        base_confidence = 0.8
        
        # Reduce confidence for longer timeframes
        months = timeframe.days / 30.0
        time_penalty = min(0.3, months * 0.05)
        
        # Reduce confidence for fewer data points
        data_penalty = max(0, 0.3 - len(debt_items) * 0.1)
        
        # Reduce confidence for high variance in interest rates
        if len(debt_items) > 1:
            rates = [item.interest_rate for item in debt_items]
            variance = sum((r - sum(rates)/len(rates))**2 for r in rates) / len(rates)
            variance_penalty = min(0.2, variance * 2)
        else:
            variance_penalty = 0.1
        
        return max(0.1, base_confidence - time_penalty - data_penalty - variance_penalty)
    
    def _identify_risk_factors(self, category: DebtCategory, items: List[DebtItem], growth_rate: float) -> List[str]:
        """Identify risk factors for debt growth."""
        risk_factors = []
        
        if growth_rate > 0.5:
            risk_factors.append("High predicted growth rate")
        
        high_severity_count = len([item for item in items if item.severity in [DebtSeverity.HIGH, DebtSeverity.CRITICAL]])
        if high_severity_count > len(items) * 0.5:
            risk_factors.append("High proportion of severe debt items")
        
        if category == DebtCategory.TESTING:
            risk_factors.append("Testing debt compounds quickly without intervention")
        elif category == DebtCategory.SECURITY:
            risk_factors.append("Security debt poses increasing risk over time")
        elif category == DebtCategory.ARCHITECTURE:
            risk_factors.append("Architectural debt becomes harder to fix over time")
        
        return risk_factors
    
    def _generate_mitigation_strategies(self, category: DebtCategory, items: List[DebtItem]) -> List[str]:
        """Generate mitigation strategies for debt category."""
        strategies = {
            DebtCategory.CODE_COMPLEXITY: [
                "Refactor complex functions into smaller units",
                "Apply design patterns to reduce complexity",
                "Implement code review processes"
            ],
            DebtCategory.DOCUMENTATION: [
                "Establish documentation standards",
                "Implement automated documentation generation",
                "Schedule regular documentation reviews"
            ],
            DebtCategory.TESTING: [
                "Increase test coverage incrementally",
                "Implement test-driven development",
                "Add automated testing to CI/CD pipeline"
            ],
            DebtCategory.ARCHITECTURE: [
                "Plan architectural refactoring",
                "Implement dependency injection",
                "Apply SOLID principles"
            ],
            DebtCategory.PERFORMANCE: [
                "Profile and optimize critical paths",
                "Implement caching strategies",
                "Optimize database queries"
            ],
            DebtCategory.SECURITY: [
                "Conduct security audits",
                "Update dependencies regularly",
                "Implement security best practices"
            ]
        }
        
        return strategies.get(category, ["Address debt items systematically"])


class TechnicalDebtPredictor:
    """Technical debt prediction system."""
    
    def __init__(self):
        """Initialize technical debt predictor."""
        self.detection_rules = DebtDetectionRules()
        self.prediction_model = DebtPredictionModel()
        self.debt_cache: Dict[str, DebtReport] = {}
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize technical debt predictor."""
        if self._initialized:
            return
        
        self._initialized = True
        logger.info("Technical debt predictor initialized")
    
    async def analyze_and_predict(
        self,
        code_metrics: Dict[str, Any],
        prediction_timeframe: timedelta = timedelta(days=90)
    ) -> DebtReport:
        """
        Analyze current technical debt and predict future growth.
        
        Args:
            code_metrics: Current code metrics
            prediction_timeframe: How far ahead to predict
            
        Returns:
            Comprehensive debt analysis and prediction report
        """
        # Generate cache key
        import hashlib
        cache_key = hashlib.md5(
            f"{str(code_metrics)}{prediction_timeframe.days}".encode()
        ).hexdigest()
        
        if cache_key in self.debt_cache:
            logger.debug("Using cached debt analysis")
            return self.debt_cache[cache_key]
        
        try:
            # Detect current debt items
            debt_items = self.detection_rules.detect_debt_items(code_metrics)
            
            # Calculate total debt
            total_debt_hours = sum(item.estimated_cost_hours for item in debt_items)
            
            # Group debt by category and severity
            debt_by_category = {}
            debt_by_severity = {}
            
            for item in debt_items:
                category = item.category.value
                severity = item.severity.value
                
                debt_by_category[category] = debt_by_category.get(category, 0) + item.estimated_cost_hours
                debt_by_severity[severity] = debt_by_severity.get(severity, 0) + item.estimated_cost_hours
            
            # Generate predictions
            predictions = self.prediction_model.predict_debt_growth(
                debt_items, prediction_timeframe
            )
            
            # Generate priority recommendations
            priority_recommendations = self._generate_priority_recommendations(debt_items, predictions)
            
            # Create report
            report = DebtReport(
                report_id=cache_key,
                analysis_date=datetime.now(),
                total_debt_hours=total_debt_hours,
                debt_by_category=debt_by_category,
                debt_by_severity=debt_by_severity,
                debt_items=debt_items,
                predictions=predictions,
                priority_recommendations=priority_recommendations
            )
            
            # Cache result
            self.debt_cache[cache_key] = report
            
            logger.info(f"Analyzed technical debt: {total_debt_hours:.1f} hours, {len(predictions)} predictions")
            return report
            
        except Exception as e:
            logger.error(f"Failed to analyze technical debt: {e}")
            # Return minimal report on error
            return DebtReport(
                report_id=cache_key,
                analysis_date=datetime.now(),
                total_debt_hours=0.0,
                priority_recommendations=[f"Analysis failed: {str(e)}"]
            )
    
    def _generate_priority_recommendations(
        self,
        debt_items: List[DebtItem],
        predictions: List[DebtPrediction]
    ) -> List[str]:
        """Generate priority recommendations for debt management."""
        recommendations = []
        
        # Sort debt items by severity and cost
        critical_items = [item for item in debt_items if item.severity == DebtSeverity.CRITICAL]
        high_items = [item for item in debt_items if item.severity == DebtSeverity.HIGH]
        
        if critical_items:
            recommendations.append(f"Address {len(critical_items)} critical debt items immediately")
        
        if high_items:
            recommendations.append(f"Plan remediation for {len(high_items)} high-severity debt items")
        
        # Check for high-growth predictions
        high_growth_predictions = [p for p in predictions if p.growth_rate > 0.5]
        if high_growth_predictions:
            categories = [p.category.value for p in high_growth_predictions]
            recommendations.append(f"Focus on {', '.join(categories)} debt - high growth predicted")
        
        # Testing debt special case
        testing_debt = sum(item.estimated_cost_hours for item in debt_items 
                          if item.category == DebtCategory.TESTING)
        if testing_debt > 20:
            recommendations.append("Prioritize test coverage improvements to prevent debt accumulation")
        
        return recommendations
    
    def get_prediction_statistics(self) -> Dict[str, Any]:
        """Get technical debt prediction statistics."""
        total_reports = len(self.debt_cache)
        
        if total_reports == 0:
            return {
                "total_reports": 0,
                "avg_debt_hours": 0.0,
                "avg_predictions": 0.0,
                "debt_categories": {},
                "prediction_accuracy": 0.0
            }
        
        total_debt = sum(report.total_debt_hours for report in self.debt_cache.values())
        total_predictions = sum(len(report.predictions) for report in self.debt_cache.values())
        
        # Count debt categories
        debt_categories = {}
        for report in self.debt_cache.values():
            for category, hours in report.debt_by_category.items():
                debt_categories[category] = debt_categories.get(category, 0) + hours
        
        return {
            "total_reports": total_reports,
            "avg_debt_hours": total_debt / total_reports,
            "avg_predictions": total_predictions / total_reports,
            "debt_categories": debt_categories,
            "cache_size": len(self.debt_cache)
        }
    
    async def cleanup(self) -> None:
        """Cleanup technical debt predictor."""
        self.debt_cache.clear()
        logger.info("Technical debt predictor cleaned up")
