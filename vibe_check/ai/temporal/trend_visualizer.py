"""
File: vibe_check/ai/temporal/trend_visualizer.py
Purpose: Trend visualization and chart generation
Related Files: vibe_check/ai/temporal/
Dependencies: typing, asyncio, datetime, enum, dataclasses
"""

from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import json

from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class VisualizationType(Enum):
    """Types of visualizations."""
    LINE_CHART = "line_chart"
    BAR_CHART = "bar_chart"
    AREA_CHART = "area_chart"
    SCATTER_PLOT = "scatter_plot"
    HEATMAP = "heatmap"
    HISTOGRAM = "histogram"
    BOX_PLOT = "box_plot"
    RADAR_CHART = "radar_chart"
    TREEMAP = "treemap"
    SANKEY_DIAGRAM = "sankey_diagram"


class TrendDirection(Enum):
    """Trend direction indicators."""
    INCREASING = "increasing"
    DECREASING = "decreasing"
    STABLE = "stable"
    VOLATILE = "volatile"
    SEASONAL = "seasonal"


@dataclass
class ChartDataPoint:
    """Single data point for charts."""
    x_value: Any  # Could be datetime, string, or number
    y_value: float
    label: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "x": self.x_value.isoformat() if isinstance(self.x_value, datetime) else self.x_value,
            "y": self.y_value,
            "label": self.label,
            "metadata": self.metadata
        }


@dataclass
class ChartSeries:
    """Data series for charts."""
    series_id: str
    name: str
    data_points: List[ChartDataPoint] = field(default_factory=list)
    color: Optional[str] = None
    style: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "id": self.series_id,
            "name": self.name,
            "data": [point.to_dict() for point in self.data_points],
            "color": self.color,
            "style": self.style
        }


@dataclass
class ChartConfiguration:
    """Chart configuration and styling."""
    chart_id: str
    title: str
    chart_type: VisualizationType
    width: int = 800
    height: int = 400
    x_axis_label: str = ""
    y_axis_label: str = ""
    show_legend: bool = True
    show_grid: bool = True
    theme: str = "default"
    annotations: List[Dict[str, Any]] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "id": self.chart_id,
            "title": self.title,
            "type": self.chart_type.value,
            "width": self.width,
            "height": self.height,
            "xAxisLabel": self.x_axis_label,
            "yAxisLabel": self.y_axis_label,
            "showLegend": self.show_legend,
            "showGrid": self.show_grid,
            "theme": self.theme,
            "annotations": self.annotations
        }


@dataclass
class VisualizationResult:
    """Complete visualization result."""
    visualization_id: str
    configuration: ChartConfiguration
    series: List[ChartSeries] = field(default_factory=list)
    insights: List[str] = field(default_factory=list)
    export_formats: List[str] = field(default_factory=list)
    generated_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "id": self.visualization_id,
            "configuration": self.configuration.to_dict(),
            "series": [series.to_dict() for series in self.series],
            "insights": self.insights,
            "exportFormats": self.export_formats,
            "generatedAt": self.generated_at.isoformat()
        }
    
    def to_json(self) -> str:
        """Convert to JSON string."""
        return json.dumps(self.to_dict(), indent=2)


class ChartDataProcessor:
    """Processes data for chart visualization."""
    
    def __init__(self):
        self.color_palette = [
            "#3498db", "#e74c3c", "#2ecc71", "#f39c12", "#9b59b6",
            "#1abc9c", "#34495e", "#e67e22", "#95a5a6", "#d35400"
        ]
    
    def process_time_series_data(
        self,
        data_points: List[Tuple[datetime, float]],
        series_name: str,
        color_index: int = 0
    ) -> ChartSeries:
        """Process time series data into chart series."""
        chart_points = []
        
        for timestamp, value in data_points:
            point = ChartDataPoint(
                x_value=timestamp,
                y_value=value,
                metadata={"timestamp": timestamp.isoformat()}
            )
            chart_points.append(point)
        
        return ChartSeries(
            series_id=f"series_{series_name.lower().replace(' ', '_')}",
            name=series_name,
            data_points=chart_points,
            color=self.color_palette[color_index % len(self.color_palette)]
        )
    
    def process_categorical_data(
        self,
        categories: List[str],
        values: List[float],
        series_name: str,
        color_index: int = 0
    ) -> ChartSeries:
        """Process categorical data into chart series."""
        chart_points = []
        
        for category, value in zip(categories, values):
            point = ChartDataPoint(
                x_value=category,
                y_value=value,
                label=category
            )
            chart_points.append(point)
        
        return ChartSeries(
            series_id=f"series_{series_name.lower().replace(' ', '_')}",
            name=series_name,
            data_points=chart_points,
            color=self.color_palette[color_index % len(self.color_palette)]
        )
    
    def process_comparison_data(
        self,
        datasets: Dict[str, List[Tuple[Any, float]]],
        base_series_name: str = "Series"
    ) -> List[ChartSeries]:
        """Process multiple datasets for comparison."""
        series_list = []
        
        for i, (dataset_name, data_points) in enumerate(datasets.items()):
            if isinstance(data_points[0][0], datetime):
                # Time series data
                series = self.process_time_series_data(
                    data_points, dataset_name, i
                )
            else:
                # Categorical data
                categories = [point[0] for point in data_points]
                values = [point[1] for point in data_points]
                series = self.process_categorical_data(
                    categories, values, dataset_name, i
                )
            
            series_list.append(series)
        
        return series_list
    
    def add_trend_annotations(
        self,
        configuration: ChartConfiguration,
        trend_direction: TrendDirection,
        trend_data: Dict[str, Any]
    ) -> None:
        """Add trend annotations to chart configuration."""
        if trend_direction == TrendDirection.INCREASING:
            annotation = {
                "type": "trend_line",
                "direction": "up",
                "color": "#2ecc71",
                "label": f"Increasing trend ({trend_data.get('change_rate', 0):.2f})"
            }
        elif trend_direction == TrendDirection.DECREASING:
            annotation = {
                "type": "trend_line",
                "direction": "down",
                "color": "#e74c3c",
                "label": f"Decreasing trend ({trend_data.get('change_rate', 0):.2f})"
            }
        elif trend_direction == TrendDirection.VOLATILE:
            annotation = {
                "type": "volatility_band",
                "color": "#f39c12",
                "label": "High volatility detected"
            }
        else:
            annotation = {
                "type": "stable_line",
                "color": "#95a5a6",
                "label": "Stable trend"
            }
        
        configuration.annotations.append(annotation)


class TrendVisualizer:
    """Trend visualization and chart generation system."""
    
    def __init__(self):
        """Initialize trend visualizer."""
        self.data_processor = ChartDataProcessor()
        self.visualization_cache: Dict[str, VisualizationResult] = {}
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize trend visualizer."""
        if self._initialized:
            return
        
        self._initialized = True
        logger.info("Trend visualizer initialized")
    
    async def create_time_series_chart(
        self,
        title: str,
        time_series_data: Dict[str, List[Tuple[datetime, float]]],
        chart_type: VisualizationType = VisualizationType.LINE_CHART,
        trend_analysis: Optional[Dict[str, Any]] = None
    ) -> VisualizationResult:
        """
        Create a time series visualization.
        
        Args:
            title: Chart title
            time_series_data: Dictionary of series_name -> [(timestamp, value)]
            chart_type: Type of chart to create
            trend_analysis: Optional trend analysis data
            
        Returns:
            Complete visualization result
        """
        # Generate visualization ID
        import hashlib
        viz_id = hashlib.md5(f"{title}{len(time_series_data)}".encode()).hexdigest()[:16]
        
        if viz_id in self.visualization_cache:
            logger.debug("Using cached visualization")
            return self.visualization_cache[viz_id]
        
        try:
            # Create chart configuration
            config = ChartConfiguration(
                chart_id=viz_id,
                title=title,
                chart_type=chart_type,
                x_axis_label="Time",
                y_axis_label="Value",
                width=1000,
                height=500
            )
            
            # Process data into series
            series_list = self.data_processor.process_comparison_data(time_series_data)
            
            # Add trend annotations if provided
            if trend_analysis:
                trend_direction = TrendDirection(trend_analysis.get("direction", "stable"))
                self.data_processor.add_trend_annotations(config, trend_direction, trend_analysis)
            
            # Generate insights
            insights = self._generate_chart_insights(series_list, trend_analysis)
            
            # Create visualization result
            visualization = VisualizationResult(
                visualization_id=viz_id,
                configuration=config,
                series=series_list,
                insights=insights,
                export_formats=["png", "svg", "pdf", "json"]
            )
            
            # Cache result
            self.visualization_cache[viz_id] = visualization
            
            logger.info(f"Created time series chart: {title} with {len(series_list)} series")
            return visualization
            
        except Exception as e:
            logger.error(f"Failed to create time series chart: {e}")
            raise
    
    async def create_comparison_chart(
        self,
        title: str,
        categories: List[str],
        datasets: Dict[str, List[float]],
        chart_type: VisualizationType = VisualizationType.BAR_CHART
    ) -> VisualizationResult:
        """
        Create a comparison chart for categorical data.
        
        Args:
            title: Chart title
            categories: List of category names
            datasets: Dictionary of dataset_name -> [values]
            chart_type: Type of chart to create
            
        Returns:
            Complete visualization result
        """
        # Generate visualization ID
        import hashlib
        viz_id = hashlib.md5(f"{title}{len(datasets)}".encode()).hexdigest()[:16]
        
        try:
            # Create chart configuration
            config = ChartConfiguration(
                chart_id=viz_id,
                title=title,
                chart_type=chart_type,
                x_axis_label="Categories",
                y_axis_label="Values"
            )
            
            # Process data into series
            series_list = []
            for i, (dataset_name, values) in enumerate(datasets.items()):
                series = self.data_processor.process_categorical_data(
                    categories, values, dataset_name, i
                )
                series_list.append(series)
            
            # Generate insights
            insights = self._generate_comparison_insights(categories, datasets)
            
            # Create visualization result
            visualization = VisualizationResult(
                visualization_id=viz_id,
                configuration=config,
                series=series_list,
                insights=insights,
                export_formats=["png", "svg", "pdf", "json"]
            )
            
            logger.info(f"Created comparison chart: {title} with {len(series_list)} series")
            return visualization
            
        except Exception as e:
            logger.error(f"Failed to create comparison chart: {e}")
            raise
    
    async def create_productivity_dashboard(
        self,
        productivity_data: Dict[str, Any],
        time_period: Tuple[datetime, datetime]
    ) -> List[VisualizationResult]:
        """
        Create a comprehensive productivity dashboard.
        
        Args:
            productivity_data: Productivity metrics and trends
            time_period: Time period for the dashboard
            
        Returns:
            List of visualization results for dashboard
        """
        visualizations = []
        
        try:
            # 1. Productivity trends over time
            if "trends" in productivity_data:
                trends_data = {}
                for trend in productivity_data["trends"]:
                    metric_name = trend.get("metric", "Unknown")
                    data_points = [
                        (datetime.fromisoformat(dt), val) 
                        for dt, val in trend.get("data_points", [])
                    ]
                    if data_points:
                        trends_data[metric_name] = data_points
                
                if trends_data:
                    trends_chart = await self.create_time_series_chart(
                        "Productivity Trends Over Time",
                        trends_data,
                        VisualizationType.LINE_CHART
                    )
                    visualizations.append(trends_chart)
            
            # 2. Current metrics comparison
            if "current_metrics" in productivity_data:
                metrics = productivity_data["current_metrics"]
                categories = list(metrics.keys())
                values = list(metrics.values())
                
                metrics_chart = await self.create_comparison_chart(
                    "Current Productivity Metrics",
                    categories,
                    {"Current Values": values},
                    VisualizationType.BAR_CHART
                )
                visualizations.append(metrics_chart)
            
            # 3. Performance distribution
            if "performance_distribution" in productivity_data:
                dist_data = productivity_data["performance_distribution"]
                
                # Create histogram-style data
                bins = list(dist_data.keys())
                frequencies = list(dist_data.values())
                
                dist_chart = await self.create_comparison_chart(
                    "Performance Distribution",
                    bins,
                    {"Frequency": frequencies},
                    VisualizationType.HISTOGRAM
                )
                visualizations.append(dist_chart)
            
            logger.info(f"Created productivity dashboard with {len(visualizations)} charts")
            return visualizations
            
        except Exception as e:
            logger.error(f"Failed to create productivity dashboard: {e}")
            return []
    
    def _generate_chart_insights(
        self,
        series_list: List[ChartSeries],
        trend_analysis: Optional[Dict[str, Any]]
    ) -> List[str]:
        """Generate insights for chart visualization."""
        insights = []
        
        # Data insights
        if series_list:
            total_points = sum(len(series.data_points) for series in series_list)
            insights.append(f"Chart contains {total_points} data points across {len(series_list)} series")
            
            # Value range insights
            all_values = []
            for series in series_list:
                all_values.extend([point.y_value for point in series.data_points])
            
            if all_values:
                min_val, max_val = min(all_values), max(all_values)
                insights.append(f"Value range: {min_val:.2f} to {max_val:.2f}")
        
        # Trend insights
        if trend_analysis:
            direction = trend_analysis.get("direction", "stable")
            change_rate = trend_analysis.get("change_rate", 0)
            
            if direction == "increasing":
                insights.append(f"Positive trend detected with {change_rate:.2f} rate of change")
            elif direction == "decreasing":
                insights.append(f"Negative trend detected with {change_rate:.2f} rate of change")
            else:
                insights.append("Stable trend with minimal variation")
        
        return insights
    
    def _generate_comparison_insights(
        self,
        categories: List[str],
        datasets: Dict[str, List[float]]
    ) -> List[str]:
        """Generate insights for comparison charts."""
        insights = []
        
        # Dataset insights
        insights.append(f"Comparing {len(datasets)} datasets across {len(categories)} categories")
        
        # Find highest and lowest values
        all_values = []
        for values in datasets.values():
            all_values.extend(values)
        
        if all_values:
            max_val = max(all_values)
            min_val = min(all_values)
            
            # Find which dataset/category has max/min
            for dataset_name, values in datasets.items():
                for i, value in enumerate(values):
                    if value == max_val:
                        insights.append(f"Highest value: {max_val:.2f} in {dataset_name} - {categories[i]}")
                    elif value == min_val:
                        insights.append(f"Lowest value: {min_val:.2f} in {dataset_name} - {categories[i]}")
        
        return insights
    
    def export_visualization(
        self,
        visualization: VisualizationResult,
        format_type: str = "json"
    ) -> str:
        """
        Export visualization in specified format.
        
        Args:
            visualization: Visualization to export
            format_type: Export format ("json", "csv", "html")
            
        Returns:
            Exported data as string
        """
        if format_type == "json":
            return visualization.to_json()
        
        elif format_type == "csv":
            # Simple CSV export for data points
            lines = ["Series,X,Y,Label"]
            for series in visualization.series:
                for point in series.data_points:
                    x_val = point.x_value
                    if isinstance(x_val, datetime):
                        x_val = x_val.isoformat()
                    lines.append(f"{series.name},{x_val},{point.y_value},{point.label or ''}")
            return "\n".join(lines)
        
        elif format_type == "html":
            # Simple HTML export
            html = f"""
            <html>
            <head><title>{visualization.configuration.title}</title></head>
            <body>
                <h1>{visualization.configuration.title}</h1>
                <div id="chart-{visualization.visualization_id}">
                    <p>Chart data: {len(visualization.series)} series</p>
                    <script>
                        const chartData = {visualization.to_json()};
                        // Chart rendering would go here
                    </script>
                </div>
                <div>
                    <h3>Insights:</h3>
                    <ul>
                        {''.join(f'<li>{insight}</li>' for insight in visualization.insights)}
                    </ul>
                </div>
            </body>
            </html>
            """
            return html
        
        else:
            return visualization.to_json()
    
    def get_visualizer_statistics(self) -> Dict[str, Any]:
        """Get trend visualizer statistics."""
        total_visualizations = len(self.visualization_cache)
        
        if total_visualizations == 0:
            return {
                "total_visualizations": 0,
                "chart_types": {},
                "avg_series_per_chart": 0.0,
                "total_data_points": 0
            }
        
        # Count chart types
        chart_types = {}
        total_series = 0
        total_points = 0
        
        for viz in self.visualization_cache.values():
            chart_type = viz.configuration.chart_type.value
            chart_types[chart_type] = chart_types.get(chart_type, 0) + 1
            
            total_series += len(viz.series)
            for series in viz.series:
                total_points += len(series.data_points)
        
        return {
            "total_visualizations": total_visualizations,
            "chart_types": chart_types,
            "avg_series_per_chart": total_series / total_visualizations,
            "total_data_points": total_points,
            "cache_size": len(self.visualization_cache)
        }
    
    async def cleanup(self) -> None:
        """Cleanup trend visualizer."""
        self.visualization_cache.clear()
        logger.info("Trend visualizer cleaned up")
