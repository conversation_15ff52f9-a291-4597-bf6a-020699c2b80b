"""
File: vibe_check/ai/temporal/temporal_engine.py
Purpose: Code evolution analysis over time
Related Files: vibe_check/ai/temporal/
Dependencies: typing, asyncio, datetime, enum, dataclasses
"""

from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import hashlib

from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class EvolutionMetric(Enum):
    """Types of evolution metrics."""
    COMPLEXITY_GROWTH = "complexity_growth"
    CODE_CHURN = "code_churn"
    DEFECT_DENSITY = "defect_density"
    TECHNICAL_DEBT = "technical_debt"
    MAINTAINABILITY = "maintainability"
    TEST_COVERAGE = "test_coverage"
    PERFORMANCE = "performance"
    SECURITY_ISSUES = "security_issues"
    DOCUMENTATION_QUALITY = "documentation_quality"
    DEPENDENCY_HEALTH = "dependency_health"


class AnalysisTimeframe(Enum):
    """Analysis timeframe options."""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    YEARLY = "yearly"


@dataclass
class CodeSnapshot:
    """Snapshot of code at a specific point in time."""
    snapshot_id: str
    timestamp: datetime
    commit_hash: Optional[str] = None
    branch: str = "main"
    metrics: Dict[str, float] = field(default_factory=dict)
    file_count: int = 0
    line_count: int = 0
    function_count: int = 0
    class_count: int = 0
    complexity_score: float = 0.0
    quality_score: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "snapshot_id": self.snapshot_id,
            "timestamp": self.timestamp.isoformat(),
            "commit_hash": self.commit_hash,
            "branch": self.branch,
            "metrics": self.metrics,
            "file_count": self.file_count,
            "line_count": self.line_count,
            "function_count": self.function_count,
            "class_count": self.class_count,
            "complexity_score": self.complexity_score,
            "quality_score": self.quality_score
        }


@dataclass
class EvolutionTrend:
    """Evolution trend analysis result."""
    metric: EvolutionMetric
    timeframe: AnalysisTimeframe
    trend_direction: str  # "increasing", "decreasing", "stable"
    change_rate: float  # Rate of change per timeframe unit
    confidence: float  # Confidence in trend analysis
    data_points: List[Tuple[datetime, float]] = field(default_factory=list)
    predictions: List[Tuple[datetime, float]] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "metric": self.metric.value,
            "timeframe": self.timeframe.value,
            "trend_direction": self.trend_direction,
            "change_rate": self.change_rate,
            "confidence": self.confidence,
            "data_points": [(dt.isoformat(), val) for dt, val in self.data_points],
            "predictions": [(dt.isoformat(), val) for dt, val in self.predictions]
        }


@dataclass
class EvolutionReport:
    """Comprehensive code evolution analysis report."""
    report_id: str
    analysis_period: Tuple[datetime, datetime]
    timeframe: AnalysisTimeframe
    snapshots_analyzed: int
    trends: List[EvolutionTrend] = field(default_factory=list)
    key_insights: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    risk_factors: List[str] = field(default_factory=list)
    generated_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "report_id": self.report_id,
            "analysis_period": [self.analysis_period[0].isoformat(), self.analysis_period[1].isoformat()],
            "timeframe": self.timeframe.value,
            "snapshots_analyzed": self.snapshots_analyzed,
            "trends": [trend.to_dict() for trend in self.trends],
            "key_insights": self.key_insights,
            "recommendations": self.recommendations,
            "risk_factors": self.risk_factors,
            "generated_at": self.generated_at.isoformat()
        }


class SnapshotGenerator:
    """Generates code snapshots for temporal analysis."""
    
    def __init__(self):
        self.snapshot_cache: Dict[str, CodeSnapshot] = {}
    
    def generate_snapshot(self, code: str, timestamp: datetime, commit_hash: Optional[str] = None) -> CodeSnapshot:
        """Generate a code snapshot."""
        # Generate snapshot ID
        snapshot_id = hashlib.md5(f"{code}{timestamp.isoformat()}".encode()).hexdigest()[:16]
        
        # Calculate basic metrics
        lines = code.split('\n')
        line_count = len([line for line in lines if line.strip()])
        
        # Count functions and classes (simplified)
        function_count = code.count('def ')
        class_count = code.count('class ')
        
        # Calculate complexity score (simplified)
        complexity_indicators = ['if ', 'for ', 'while ', 'try:', 'except:', 'elif ']
        complexity_score = sum(code.count(indicator) for indicator in complexity_indicators) / max(1, line_count) * 100
        
        # Calculate quality score (simplified)
        quality_indicators = ['"""', "'''", '# ', 'def ', 'class ']
        quality_score = min(100, sum(code.count(indicator) for indicator in quality_indicators) / max(1, line_count) * 100)
        
        # Generate metrics
        metrics = {
            "cyclomatic_complexity": complexity_score,
            "code_quality": quality_score,
            "documentation_ratio": (code.count('"""') + code.count("'''")) / max(1, function_count + class_count),
            "comment_ratio": code.count('# ') / max(1, line_count),
            "function_density": function_count / max(1, line_count) * 100,
            "class_density": class_count / max(1, line_count) * 100
        }
        
        snapshot = CodeSnapshot(
            snapshot_id=snapshot_id,
            timestamp=timestamp,
            commit_hash=commit_hash,
            metrics=metrics,
            file_count=1,  # Simplified for single file analysis
            line_count=line_count,
            function_count=function_count,
            class_count=class_count,
            complexity_score=complexity_score,
            quality_score=quality_score
        )
        
        self.snapshot_cache[snapshot_id] = snapshot
        return snapshot
    
    def get_snapshot(self, snapshot_id: str) -> Optional[CodeSnapshot]:
        """Get cached snapshot."""
        return self.snapshot_cache.get(snapshot_id)


class TrendAnalyzer:
    """Analyzes trends in code evolution."""
    
    def __init__(self):
        self.trend_algorithms = {
            "linear_regression": self._linear_regression_trend,
            "moving_average": self._moving_average_trend,
            "exponential_smoothing": self._exponential_smoothing_trend
        }
    
    def analyze_trend(
        self,
        snapshots: List[CodeSnapshot],
        metric: EvolutionMetric,
        timeframe: AnalysisTimeframe,
        algorithm: str = "linear_regression"
    ) -> EvolutionTrend:
        """Analyze trend for a specific metric."""
        if algorithm not in self.trend_algorithms:
            algorithm = "linear_regression"
        
        # Extract data points
        data_points = []
        for snapshot in sorted(snapshots, key=lambda s: s.timestamp):
            if metric.value in snapshot.metrics:
                data_points.append((snapshot.timestamp, snapshot.metrics[metric.value]))
            elif metric == EvolutionMetric.COMPLEXITY_GROWTH:
                data_points.append((snapshot.timestamp, snapshot.complexity_score))
            elif metric == EvolutionMetric.MAINTAINABILITY:
                data_points.append((snapshot.timestamp, snapshot.quality_score))
        
        if len(data_points) < 2:
            return EvolutionTrend(
                metric=metric,
                timeframe=timeframe,
                trend_direction="stable",
                change_rate=0.0,
                confidence=0.0,
                data_points=data_points
            )
        
        # Apply trend analysis algorithm
        return self.trend_algorithms[algorithm](data_points, metric, timeframe)
    
    def _linear_regression_trend(
        self,
        data_points: List[Tuple[datetime, float]],
        metric: EvolutionMetric,
        timeframe: AnalysisTimeframe
    ) -> EvolutionTrend:
        """Simple linear regression trend analysis."""
        if len(data_points) < 2:
            return EvolutionTrend(metric=metric, timeframe=timeframe, trend_direction="stable", change_rate=0.0, confidence=0.0)
        
        # Convert timestamps to numeric values (days since first point)
        base_time = data_points[0][0]
        x_values = [(dt - base_time).total_seconds() / 86400 for dt, _ in data_points]  # Days
        y_values = [val for _, val in data_points]
        
        # Simple linear regression
        n = len(data_points)
        sum_x = sum(x_values)
        sum_y = sum(y_values)
        sum_xy = sum(x * y for x, y in zip(x_values, y_values))
        sum_x2 = sum(x * x for x in x_values)
        
        # Calculate slope (change rate)
        denominator = n * sum_x2 - sum_x * sum_x
        if denominator == 0:
            change_rate = 0.0
        else:
            change_rate = (n * sum_xy - sum_x * sum_y) / denominator
        
        # Determine trend direction
        if abs(change_rate) < 0.01:
            trend_direction = "stable"
        elif change_rate > 0:
            trend_direction = "increasing"
        else:
            trend_direction = "decreasing"
        
        # Calculate confidence (simplified R-squared)
        if len(set(y_values)) == 1:
            confidence = 1.0 if trend_direction == "stable" else 0.0
        else:
            y_mean = sum_y / n
            ss_tot = sum((y - y_mean) ** 2 for y in y_values)
            ss_res = sum((y_values[i] - (change_rate * x_values[i] + (sum_y - change_rate * sum_x) / n)) ** 2 
                        for i in range(n))
            confidence = max(0.0, 1 - (ss_res / ss_tot)) if ss_tot > 0 else 0.0
        
        # Generate predictions (next 3 time periods)
        predictions = []
        last_time = data_points[-1][0]
        time_delta = self._get_time_delta(timeframe)
        
        for i in range(1, 4):
            future_time = last_time + (time_delta * i)
            future_x = (future_time - base_time).total_seconds() / 86400
            future_y = change_rate * future_x + (sum_y - change_rate * sum_x) / n
            predictions.append((future_time, max(0, future_y)))  # Ensure non-negative
        
        return EvolutionTrend(
            metric=metric,
            timeframe=timeframe,
            trend_direction=trend_direction,
            change_rate=change_rate,
            confidence=confidence,
            data_points=data_points,
            predictions=predictions
        )
    
    def _moving_average_trend(
        self,
        data_points: List[Tuple[datetime, float]],
        metric: EvolutionMetric,
        timeframe: AnalysisTimeframe
    ) -> EvolutionTrend:
        """Moving average trend analysis."""
        window_size = min(5, len(data_points) // 2)
        if window_size < 2:
            return self._linear_regression_trend(data_points, metric, timeframe)
        
        # Calculate moving averages
        smoothed_points = []
        for i in range(window_size, len(data_points)):
            window_values = [data_points[j][1] for j in range(i - window_size, i)]
            avg_value = sum(window_values) / len(window_values)
            smoothed_points.append((data_points[i][0], avg_value))
        
        # Apply linear regression to smoothed data
        return self._linear_regression_trend(smoothed_points, metric, timeframe)
    
    def _exponential_smoothing_trend(
        self,
        data_points: List[Tuple[datetime, float]],
        metric: EvolutionMetric,
        timeframe: AnalysisTimeframe
    ) -> EvolutionTrend:
        """Exponential smoothing trend analysis."""
        alpha = 0.3  # Smoothing factor
        
        if len(data_points) < 2:
            return self._linear_regression_trend(data_points, metric, timeframe)
        
        # Apply exponential smoothing
        smoothed_points = [data_points[0]]
        smoothed_value = data_points[0][1]
        
        for i in range(1, len(data_points)):
            smoothed_value = alpha * data_points[i][1] + (1 - alpha) * smoothed_value
            smoothed_points.append((data_points[i][0], smoothed_value))
        
        # Apply linear regression to smoothed data
        return self._linear_regression_trend(smoothed_points, metric, timeframe)
    
    def _get_time_delta(self, timeframe: AnalysisTimeframe) -> timedelta:
        """Get time delta for timeframe."""
        if timeframe == AnalysisTimeframe.DAILY:
            return timedelta(days=1)
        elif timeframe == AnalysisTimeframe.WEEKLY:
            return timedelta(weeks=1)
        elif timeframe == AnalysisTimeframe.MONTHLY:
            return timedelta(days=30)
        elif timeframe == AnalysisTimeframe.QUARTERLY:
            return timedelta(days=90)
        elif timeframe == AnalysisTimeframe.YEARLY:
            return timedelta(days=365)
        else:
            return timedelta(days=1)


class TemporalAnalysisEngine:
    """Code evolution analysis over time."""
    
    def __init__(self):
        """Initialize temporal analysis engine."""
        self.snapshot_generator = SnapshotGenerator()
        self.trend_analyzer = TrendAnalyzer()
        self.analysis_cache: Dict[str, EvolutionReport] = {}
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize temporal analysis engine."""
        if self._initialized:
            return
        
        self._initialized = True
        logger.info("Temporal analysis engine initialized")
    
    async def analyze_evolution(
        self,
        code_history: List[Tuple[str, datetime, Optional[str]]],  # (code, timestamp, commit_hash)
        timeframe: AnalysisTimeframe = AnalysisTimeframe.WEEKLY,
        metrics: Optional[List[EvolutionMetric]] = None
    ) -> EvolutionReport:
        """
        Analyze code evolution over time.
        
        Args:
            code_history: List of (code, timestamp, commit_hash) tuples
            timeframe: Analysis timeframe
            metrics: Metrics to analyze (default: all)
            
        Returns:
            Evolution analysis report
        """
        if metrics is None:
            metrics = list(EvolutionMetric)
        
        # Generate cache key
        cache_key = hashlib.md5(
            f"{len(code_history)}{timeframe.value}{','.join(m.value for m in metrics)}".encode()
        ).hexdigest()
        
        if cache_key in self.analysis_cache:
            logger.debug("Using cached evolution analysis")
            return self.analysis_cache[cache_key]
        
        try:
            # Generate snapshots
            snapshots = []
            for code, timestamp, commit_hash in code_history:
                snapshot = self.snapshot_generator.generate_snapshot(code, timestamp, commit_hash)
                snapshots.append(snapshot)
            
            if len(snapshots) < 2:
                raise ValueError("Need at least 2 code snapshots for evolution analysis")
            
            # Analyze trends for each metric
            trends = []
            for metric in metrics:
                trend = self.trend_analyzer.analyze_trend(snapshots, metric, timeframe)
                trends.append(trend)
            
            # Generate insights and recommendations
            key_insights = self._generate_insights(trends, snapshots)
            recommendations = self._generate_recommendations(trends)
            risk_factors = self._identify_risk_factors(trends)
            
            # Create report
            analysis_period = (snapshots[0].timestamp, snapshots[-1].timestamp)
            
            report = EvolutionReport(
                report_id=cache_key,
                analysis_period=analysis_period,
                timeframe=timeframe,
                snapshots_analyzed=len(snapshots),
                trends=trends,
                key_insights=key_insights,
                recommendations=recommendations,
                risk_factors=risk_factors
            )
            
            # Cache result
            self.analysis_cache[cache_key] = report
            
            logger.info(f"Analyzed code evolution: {len(snapshots)} snapshots, {len(trends)} trends")
            return report
            
        except Exception as e:
            logger.error(f"Failed to analyze code evolution: {e}")
            # Return minimal report on error
            return EvolutionReport(
                report_id=cache_key,
                analysis_period=(datetime.now() - timedelta(days=30), datetime.now()),
                timeframe=timeframe,
                snapshots_analyzed=0,
                key_insights=[f"Analysis failed: {str(e)}"]
            )
    
    def _generate_insights(self, trends: List[EvolutionTrend], snapshots: List[CodeSnapshot]) -> List[str]:
        """Generate key insights from trend analysis."""
        insights = []
        
        # Complexity insights
        complexity_trends = [t for t in trends if t.metric == EvolutionMetric.COMPLEXITY_GROWTH]
        if complexity_trends:
            trend = complexity_trends[0]
            if trend.trend_direction == "increasing" and trend.confidence > 0.7:
                insights.append(f"Code complexity is increasing at {trend.change_rate:.2f} units per {trend.timeframe.value}")
            elif trend.trend_direction == "decreasing":
                insights.append("Code complexity is decreasing, indicating improved maintainability")
        
        # Quality insights
        quality_trends = [t for t in trends if t.metric == EvolutionMetric.MAINTAINABILITY]
        if quality_trends:
            trend = quality_trends[0]
            if trend.trend_direction == "decreasing" and trend.confidence > 0.6:
                insights.append("Code maintainability is declining, requiring attention")
            elif trend.trend_direction == "increasing":
                insights.append("Code maintainability is improving over time")
        
        # Growth insights
        if len(snapshots) >= 2:
            first_snapshot = snapshots[0]
            last_snapshot = snapshots[-1]
            
            line_growth = ((last_snapshot.line_count - first_snapshot.line_count) / 
                          max(1, first_snapshot.line_count)) * 100
            
            if line_growth > 50:
                insights.append(f"Codebase has grown significantly by {line_growth:.1f}%")
            elif line_growth < -20:
                insights.append(f"Codebase has shrunk by {abs(line_growth):.1f}%")
        
        return insights
    
    def _generate_recommendations(self, trends: List[EvolutionTrend]) -> List[str]:
        """Generate recommendations based on trends."""
        recommendations = []
        
        # Check for concerning trends
        for trend in trends:
            if trend.confidence < 0.5:
                continue
            
            if trend.metric == EvolutionMetric.COMPLEXITY_GROWTH and trend.trend_direction == "increasing":
                recommendations.append("Consider refactoring to reduce complexity growth")
            
            elif trend.metric == EvolutionMetric.TECHNICAL_DEBT and trend.trend_direction == "increasing":
                recommendations.append("Address technical debt before it becomes unmanageable")
            
            elif trend.metric == EvolutionMetric.TEST_COVERAGE and trend.trend_direction == "decreasing":
                recommendations.append("Improve test coverage to maintain code quality")
            
            elif trend.metric == EvolutionMetric.MAINTAINABILITY and trend.trend_direction == "decreasing":
                recommendations.append("Focus on code maintainability improvements")
        
        # General recommendations
        if not recommendations:
            recommendations.append("Continue monitoring code evolution trends")
        
        return recommendations
    
    def _identify_risk_factors(self, trends: List[EvolutionTrend]) -> List[str]:
        """Identify risk factors from trends."""
        risk_factors = []
        
        for trend in trends:
            if trend.confidence < 0.3:
                risk_factors.append(f"Low confidence in {trend.metric.value} trend analysis")
            
            if (trend.metric in [EvolutionMetric.COMPLEXITY_GROWTH, EvolutionMetric.TECHNICAL_DEBT] and
                trend.trend_direction == "increasing" and trend.change_rate > 1.0):
                risk_factors.append(f"Rapid increase in {trend.metric.value}")
            
            if (trend.metric == EvolutionMetric.MAINTAINABILITY and
                trend.trend_direction == "decreasing" and trend.change_rate < -1.0):
                risk_factors.append("Rapid decline in code maintainability")
        
        return risk_factors
    
    def get_analysis_statistics(self) -> Dict[str, Any]:
        """Get temporal analysis statistics."""
        total_reports = len(self.analysis_cache)
        
        if total_reports == 0:
            return {
                "total_reports": 0,
                "total_snapshots": 0,
                "avg_trends_per_report": 0.0,
                "timeframes_used": {},
                "metrics_analyzed": {}
            }
        
        total_snapshots = sum(report.snapshots_analyzed for report in self.analysis_cache.values())
        total_trends = sum(len(report.trends) for report in self.analysis_cache.values())
        
        # Count timeframes and metrics
        timeframes_used = {}
        metrics_analyzed = {}
        
        for report in self.analysis_cache.values():
            timeframe = report.timeframe.value
            timeframes_used[timeframe] = timeframes_used.get(timeframe, 0) + 1
            
            for trend in report.trends:
                metric = trend.metric.value
                metrics_analyzed[metric] = metrics_analyzed.get(metric, 0) + 1
        
        return {
            "total_reports": total_reports,
            "total_snapshots": total_snapshots,
            "avg_trends_per_report": total_trends / total_reports,
            "timeframes_used": timeframes_used,
            "metrics_analyzed": metrics_analyzed,
            "cache_size": len(self.analysis_cache)
        }
    
    async def cleanup(self) -> None:
        """Cleanup temporal analysis engine."""
        self.analysis_cache.clear()
        self.snapshot_generator.snapshot_cache.clear()
        logger.info("Temporal analysis engine cleaned up")
