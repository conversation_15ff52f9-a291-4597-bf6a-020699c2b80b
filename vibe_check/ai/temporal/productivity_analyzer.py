"""
File: vibe_check/ai/temporal/productivity_analyzer.py
Purpose: Developer productivity insights and analysis
Related Files: vibe_check/ai/temporal/
Dependencies: typing, asyncio, datetime, enum, dataclasses
"""

from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import statistics

from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class ProductivityMetric(Enum):
    """Types of productivity metrics."""
    CODE_OUTPUT = "code_output"                    # Lines of code per time period
    COMMIT_FREQUENCY = "commit_frequency"          # Commits per time period
    FEATURE_VELOCITY = "feature_velocity"          # Features completed per sprint
    BUG_RESOLUTION = "bug_resolution"              # Bugs fixed per time period
    CODE_QUALITY = "code_quality"                  # Quality score trends
    REVIEW_EFFICIENCY = "review_efficiency"        # Code review turnaround time
    REFACTORING_RATE = "refactoring_rate"         # Refactoring activities
    TESTING_PRODUCTIVITY = "testing_productivity"  # Test coverage improvements
    DOCUMENTATION_RATE = "documentation_rate"      # Documentation updates
    COLLABORATION_INDEX = "collaboration_index"    # Team collaboration metrics


class DeveloperInsight(Enum):
    """Types of developer insights."""
    PEAK_PERFORMANCE_TIMES = "peak_performance_times"
    PRODUCTIVITY_PATTERNS = "productivity_patterns"
    SKILL_DEVELOPMENT = "skill_development"
    COLLABORATION_STYLE = "collaboration_style"
    FOCUS_AREAS = "focus_areas"
    IMPROVEMENT_OPPORTUNITIES = "improvement_opportunities"


@dataclass
class ProductivitySnapshot:
    """Snapshot of productivity metrics at a point in time."""
    snapshot_id: str
    timestamp: datetime
    developer_id: Optional[str] = None
    team_id: Optional[str] = None
    metrics: Dict[str, float] = field(default_factory=dict)
    context: Dict[str, Any] = field(default_factory=dict)  # Sprint, project, etc.
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "snapshot_id": self.snapshot_id,
            "timestamp": self.timestamp.isoformat(),
            "developer_id": self.developer_id,
            "team_id": self.team_id,
            "metrics": self.metrics,
            "context": self.context
        }


@dataclass
class ProductivityTrend:
    """Productivity trend analysis result."""
    trend_id: str
    metric: ProductivityMetric
    entity_id: str  # Developer or team ID
    entity_type: str  # "developer" or "team"
    trend_direction: str  # "improving", "declining", "stable"
    change_rate: float
    confidence: float
    time_period: Tuple[datetime, datetime]
    baseline_value: float
    current_value: float
    peak_value: float
    insights: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "trend_id": self.trend_id,
            "metric": self.metric.value,
            "entity_id": self.entity_id,
            "entity_type": self.entity_type,
            "trend_direction": self.trend_direction,
            "change_rate": self.change_rate,
            "confidence": self.confidence,
            "time_period": [self.time_period[0].isoformat(), self.time_period[1].isoformat()],
            "baseline_value": self.baseline_value,
            "current_value": self.current_value,
            "peak_value": self.peak_value,
            "insights": self.insights
        }


@dataclass
class ProductivityReport:
    """Comprehensive productivity analysis report."""
    report_id: str
    analysis_period: Tuple[datetime, datetime]
    entity_type: str  # "developer", "team", or "organization"
    entity_id: str
    overall_productivity_score: float
    trends: List[ProductivityTrend] = field(default_factory=list)
    insights: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    benchmarks: Dict[str, float] = field(default_factory=dict)
    generated_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "report_id": self.report_id,
            "analysis_period": [self.analysis_period[0].isoformat(), self.analysis_period[1].isoformat()],
            "entity_type": self.entity_type,
            "entity_id": self.entity_id,
            "overall_productivity_score": self.overall_productivity_score,
            "trends": [trend.to_dict() for trend in self.trends],
            "insights": self.insights,
            "recommendations": self.recommendations,
            "benchmarks": self.benchmarks,
            "generated_at": self.generated_at.isoformat()
        }


class ProductivityMetricsCalculator:
    """Calculates productivity metrics from raw data."""
    
    def __init__(self):
        self.metric_calculators = {
            ProductivityMetric.CODE_OUTPUT: self._calculate_code_output,
            ProductivityMetric.COMMIT_FREQUENCY: self._calculate_commit_frequency,
            ProductivityMetric.FEATURE_VELOCITY: self._calculate_feature_velocity,
            ProductivityMetric.BUG_RESOLUTION: self._calculate_bug_resolution,
            ProductivityMetric.CODE_QUALITY: self._calculate_code_quality,
            ProductivityMetric.REVIEW_EFFICIENCY: self._calculate_review_efficiency,
            ProductivityMetric.REFACTORING_RATE: self._calculate_refactoring_rate,
            ProductivityMetric.TESTING_PRODUCTIVITY: self._calculate_testing_productivity,
            ProductivityMetric.DOCUMENTATION_RATE: self._calculate_documentation_rate,
            ProductivityMetric.COLLABORATION_INDEX: self._calculate_collaboration_index
        }
    
    def calculate_metrics(self, raw_data: Dict[str, Any], time_period: timedelta) -> Dict[str, float]:
        """Calculate all productivity metrics from raw data."""
        metrics = {}
        
        for metric_type in ProductivityMetric:
            try:
                calculator = self.metric_calculators[metric_type]
                value = calculator(raw_data, time_period)
                metrics[metric_type.value] = value
            except Exception as e:
                logger.warning(f"Failed to calculate {metric_type.value}: {e}")
                metrics[metric_type.value] = 0.0
        
        return metrics
    
    def _calculate_code_output(self, data: Dict[str, Any], period: timedelta) -> float:
        """Calculate code output metric (lines per day)."""
        lines_added = data.get("lines_added", 0)
        lines_deleted = data.get("lines_deleted", 0)
        net_lines = lines_added - (lines_deleted * 0.5)  # Weight deletions less
        
        days = max(1, period.days)
        return max(0, net_lines / days)
    
    def _calculate_commit_frequency(self, data: Dict[str, Any], period: timedelta) -> float:
        """Calculate commit frequency (commits per day)."""
        commit_count = data.get("commit_count", 0)
        days = max(1, period.days)
        return commit_count / days
    
    def _calculate_feature_velocity(self, data: Dict[str, Any], period: timedelta) -> float:
        """Calculate feature velocity (features per week)."""
        features_completed = data.get("features_completed", 0)
        weeks = max(1, period.days / 7)
        return features_completed / weeks
    
    def _calculate_bug_resolution(self, data: Dict[str, Any], period: timedelta) -> float:
        """Calculate bug resolution rate (bugs per day)."""
        bugs_fixed = data.get("bugs_fixed", 0)
        days = max(1, period.days)
        return bugs_fixed / days
    
    def _calculate_code_quality(self, data: Dict[str, Any], period: timedelta) -> float:
        """Calculate code quality trend."""
        quality_scores = data.get("quality_scores", [])
        if not quality_scores:
            return 0.0
        
        # Return average quality score
        return statistics.mean(quality_scores)
    
    def _calculate_review_efficiency(self, data: Dict[str, Any], period: timedelta) -> float:
        """Calculate code review efficiency."""
        review_times = data.get("review_turnaround_hours", [])
        if not review_times:
            return 0.0
        
        # Lower average review time = higher efficiency
        avg_review_time = statistics.mean(review_times)
        return max(0, 100 - avg_review_time)  # Efficiency score
    
    def _calculate_refactoring_rate(self, data: Dict[str, Any], period: timedelta) -> float:
        """Calculate refactoring activity rate."""
        refactoring_commits = data.get("refactoring_commits", 0)
        total_commits = data.get("commit_count", 1)
        return (refactoring_commits / total_commits) * 100
    
    def _calculate_testing_productivity(self, data: Dict[str, Any], period: timedelta) -> float:
        """Calculate testing productivity."""
        test_coverage_improvement = data.get("test_coverage_delta", 0)
        tests_added = data.get("tests_added", 0)
        
        # Combine coverage improvement and test additions
        return (test_coverage_improvement * 10) + (tests_added * 0.1)
    
    def _calculate_documentation_rate(self, data: Dict[str, Any], period: timedelta) -> float:
        """Calculate documentation update rate."""
        doc_updates = data.get("documentation_updates", 0)
        days = max(1, period.days)
        return doc_updates / days
    
    def _calculate_collaboration_index(self, data: Dict[str, Any], period: timedelta) -> float:
        """Calculate collaboration index."""
        code_reviews_given = data.get("reviews_given", 0)
        code_reviews_received = data.get("reviews_received", 0)
        pair_programming_hours = data.get("pair_programming_hours", 0)
        
        # Weighted collaboration score
        collaboration_score = (code_reviews_given * 2) + (code_reviews_received * 1) + (pair_programming_hours * 3)
        days = max(1, period.days)
        return collaboration_score / days


class ProductivityTrendAnalyzer:
    """Analyzes productivity trends over time."""
    
    def __init__(self):
        self.trend_algorithms = {
            "linear": self._linear_trend_analysis,
            "moving_average": self._moving_average_analysis,
            "seasonal": self._seasonal_analysis
        }
    
    def analyze_trends(
        self,
        snapshots: List[ProductivitySnapshot],
        entity_id: str,
        entity_type: str,
        algorithm: str = "linear"
    ) -> List[ProductivityTrend]:
        """Analyze productivity trends for an entity."""
        if algorithm not in self.trend_algorithms:
            algorithm = "linear"
        
        trends = []
        
        # Group snapshots by metric
        metrics_data = {}
        for snapshot in snapshots:
            for metric_name, value in snapshot.metrics.items():
                if metric_name not in metrics_data:
                    metrics_data[metric_name] = []
                metrics_data[metric_name].append((snapshot.timestamp, value))
        
        # Analyze trend for each metric
        for metric_name, data_points in metrics_data.items():
            if len(data_points) < 3:  # Need at least 3 points for trend analysis
                continue
            
            try:
                metric_enum = ProductivityMetric(metric_name)
                trend = self.trend_algorithms[algorithm](
                    data_points, metric_enum, entity_id, entity_type
                )
                trends.append(trend)
            except ValueError:
                logger.warning(f"Unknown metric: {metric_name}")
                continue
        
        return trends
    
    def _linear_trend_analysis(
        self,
        data_points: List[Tuple[datetime, float]],
        metric: ProductivityMetric,
        entity_id: str,
        entity_type: str
    ) -> ProductivityTrend:
        """Linear trend analysis."""
        # Sort by timestamp
        data_points.sort(key=lambda x: x[0])
        
        # Convert to numeric for regression
        base_time = data_points[0][0]
        x_values = [(dt - base_time).total_seconds() / 86400 for dt, _ in data_points]  # Days
        y_values = [val for _, val in data_points]
        
        # Simple linear regression
        n = len(data_points)
        sum_x = sum(x_values)
        sum_y = sum(y_values)
        sum_xy = sum(x * y for x, y in zip(x_values, y_values))
        sum_x2 = sum(x * x for x in x_values)
        
        # Calculate slope (change rate)
        denominator = n * sum_x2 - sum_x * sum_x
        if denominator == 0:
            change_rate = 0.0
        else:
            change_rate = (n * sum_xy - sum_x * sum_y) / denominator
        
        # Determine trend direction
        if abs(change_rate) < 0.01:
            trend_direction = "stable"
        elif change_rate > 0:
            trend_direction = "improving"
        else:
            trend_direction = "declining"
        
        # Calculate confidence (R-squared)
        if len(set(y_values)) == 1:
            confidence = 1.0 if trend_direction == "stable" else 0.0
        else:
            y_mean = sum_y / n
            ss_tot = sum((y - y_mean) ** 2 for y in y_values)
            ss_res = sum((y_values[i] - (change_rate * x_values[i] + (sum_y - change_rate * sum_x) / n)) ** 2 
                        for i in range(n))
            confidence = max(0.0, 1 - (ss_res / ss_tot)) if ss_tot > 0 else 0.0
        
        # Calculate values
        baseline_value = y_values[0]
        current_value = y_values[-1]
        peak_value = max(y_values)
        
        # Generate insights
        insights = self._generate_trend_insights(metric, trend_direction, change_rate, y_values)
        
        return ProductivityTrend(
            trend_id=f"trend_{entity_id}_{metric.value}_{datetime.now().timestamp()}",
            metric=metric,
            entity_id=entity_id,
            entity_type=entity_type,
            trend_direction=trend_direction,
            change_rate=change_rate,
            confidence=confidence,
            time_period=(data_points[0][0], data_points[-1][0]),
            baseline_value=baseline_value,
            current_value=current_value,
            peak_value=peak_value,
            insights=insights
        )
    
    def _moving_average_analysis(
        self,
        data_points: List[Tuple[datetime, float]],
        metric: ProductivityMetric,
        entity_id: str,
        entity_type: str
    ) -> ProductivityTrend:
        """Moving average trend analysis."""
        # Apply moving average smoothing first
        window_size = min(5, len(data_points) // 2)
        if window_size < 2:
            return self._linear_trend_analysis(data_points, metric, entity_id, entity_type)
        
        smoothed_points = []
        for i in range(window_size, len(data_points)):
            window_values = [data_points[j][1] for j in range(i - window_size, i)]
            avg_value = sum(window_values) / len(window_values)
            smoothed_points.append((data_points[i][0], avg_value))
        
        return self._linear_trend_analysis(smoothed_points, metric, entity_id, entity_type)
    
    def _seasonal_analysis(
        self,
        data_points: List[Tuple[datetime, float]],
        metric: ProductivityMetric,
        entity_id: str,
        entity_type: str
    ) -> ProductivityTrend:
        """Seasonal trend analysis (simplified)."""
        # For now, just apply linear analysis
        # In a full implementation, this would detect seasonal patterns
        return self._linear_trend_analysis(data_points, metric, entity_id, entity_type)
    
    def _generate_trend_insights(
        self,
        metric: ProductivityMetric,
        direction: str,
        change_rate: float,
        values: List[float]
    ) -> List[str]:
        """Generate insights for a productivity trend."""
        insights = []
        
        if direction == "improving":
            if metric == ProductivityMetric.CODE_OUTPUT:
                insights.append("Code output is increasing - good productivity trend")
            elif metric == ProductivityMetric.CODE_QUALITY:
                insights.append("Code quality is improving over time")
            elif metric == ProductivityMetric.BUG_RESOLUTION:
                insights.append("Bug resolution rate is increasing")
        
        elif direction == "declining":
            if metric == ProductivityMetric.CODE_QUALITY:
                insights.append("Code quality is declining - may need attention")
            elif metric == ProductivityMetric.COMMIT_FREQUENCY:
                insights.append("Commit frequency is decreasing - check for blockers")
            elif metric == ProductivityMetric.COLLABORATION_INDEX:
                insights.append("Collaboration is decreasing - encourage team interaction")
        
        # Check for volatility
        if len(values) > 2:
            volatility = statistics.stdev(values) / statistics.mean(values) if statistics.mean(values) > 0 else 0
            if volatility > 0.5:
                insights.append("High variability in performance - investigate causes")
        
        return insights


class ProductivityAnalyzer:
    """Developer productivity insights and analysis system."""
    
    def __init__(self):
        """Initialize productivity analyzer."""
        self.metrics_calculator = ProductivityMetricsCalculator()
        self.trend_analyzer = ProductivityTrendAnalyzer()
        self.analysis_cache: Dict[str, ProductivityReport] = {}
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize productivity analyzer."""
        if self._initialized:
            return
        
        self._initialized = True
        logger.info("Productivity analyzer initialized")
    
    async def analyze_productivity(
        self,
        entity_id: str,
        entity_type: str,  # "developer", "team", or "organization"
        raw_data_history: List[Tuple[Dict[str, Any], datetime]],  # (data, timestamp)
        analysis_period: Optional[Tuple[datetime, datetime]] = None
    ) -> ProductivityReport:
        """
        Analyze productivity for a developer, team, or organization.
        
        Args:
            entity_id: ID of the entity being analyzed
            entity_type: Type of entity ("developer", "team", "organization")
            raw_data_history: Historical raw data with timestamps
            analysis_period: Optional specific period to analyze
            
        Returns:
            Comprehensive productivity analysis report
        """
        # Generate cache key
        import hashlib
        cache_key = hashlib.md5(
            f"{entity_id}{entity_type}{len(raw_data_history)}".encode()
        ).hexdigest()
        
        if cache_key in self.analysis_cache:
            logger.debug("Using cached productivity analysis")
            return self.analysis_cache[cache_key]
        
        try:
            # Filter data by analysis period if specified
            if analysis_period:
                filtered_data = [
                    (data, timestamp) for data, timestamp in raw_data_history
                    if analysis_period[0] <= timestamp <= analysis_period[1]
                ]
            else:
                filtered_data = raw_data_history
                analysis_period = (
                    min(timestamp for _, timestamp in filtered_data),
                    max(timestamp for _, timestamp in filtered_data)
                )
            
            if len(filtered_data) < 2:
                raise ValueError("Need at least 2 data points for productivity analysis")
            
            # Generate productivity snapshots
            snapshots = []
            for i, (raw_data, timestamp) in enumerate(filtered_data):
                # Calculate time period for this snapshot
                if i == 0:
                    time_period = timedelta(days=1)  # Default for first snapshot
                else:
                    time_period = timestamp - filtered_data[i-1][1]
                
                # Calculate metrics
                metrics = self.metrics_calculator.calculate_metrics(raw_data, time_period)
                
                snapshot = ProductivitySnapshot(
                    snapshot_id=f"snapshot_{entity_id}_{timestamp.timestamp()}",
                    timestamp=timestamp,
                    developer_id=entity_id if entity_type == "developer" else None,
                    team_id=entity_id if entity_type == "team" else None,
                    metrics=metrics,
                    context=raw_data.get("context", {})
                )
                snapshots.append(snapshot)
            
            # Analyze trends
            trends = self.trend_analyzer.analyze_trends(snapshots, entity_id, entity_type)
            
            # Calculate overall productivity score
            overall_score = self._calculate_overall_productivity_score(snapshots)
            
            # Generate insights and recommendations
            insights = self._generate_productivity_insights(trends, snapshots)
            recommendations = self._generate_productivity_recommendations(trends, entity_type)
            benchmarks = self._calculate_benchmarks(snapshots, entity_type)
            
            # Create report
            report = ProductivityReport(
                report_id=cache_key,
                analysis_period=analysis_period,
                entity_type=entity_type,
                entity_id=entity_id,
                overall_productivity_score=overall_score,
                trends=trends,
                insights=insights,
                recommendations=recommendations,
                benchmarks=benchmarks
            )
            
            # Cache result
            self.analysis_cache[cache_key] = report
            
            logger.info(f"Analyzed productivity for {entity_type} {entity_id}: {overall_score:.2f} score")
            return report
            
        except Exception as e:
            logger.error(f"Failed to analyze productivity: {e}")
            # Return minimal report on error
            return ProductivityReport(
                report_id=cache_key,
                analysis_period=analysis_period or (datetime.now() - timedelta(days=30), datetime.now()),
                entity_type=entity_type,
                entity_id=entity_id,
                overall_productivity_score=0.0,
                insights=[f"Analysis failed: {str(e)}"]
            )
    
    def _calculate_overall_productivity_score(self, snapshots: List[ProductivitySnapshot]) -> float:
        """Calculate overall productivity score."""
        if not snapshots:
            return 0.0
        
        # Weight different metrics
        metric_weights = {
            ProductivityMetric.CODE_OUTPUT.value: 0.15,
            ProductivityMetric.COMMIT_FREQUENCY.value: 0.10,
            ProductivityMetric.FEATURE_VELOCITY.value: 0.20,
            ProductivityMetric.BUG_RESOLUTION.value: 0.15,
            ProductivityMetric.CODE_QUALITY.value: 0.20,
            ProductivityMetric.REVIEW_EFFICIENCY.value: 0.10,
            ProductivityMetric.TESTING_PRODUCTIVITY.value: 0.10
        }
        
        # Calculate weighted average across all snapshots
        total_score = 0.0
        total_weight = 0.0
        
        for snapshot in snapshots:
            snapshot_score = 0.0
            snapshot_weight = 0.0
            
            for metric_name, value in snapshot.metrics.items():
                weight = metric_weights.get(metric_name, 0.0)
                if weight > 0:
                    # Normalize value to 0-100 scale (simplified)
                    normalized_value = min(100, max(0, value))
                    snapshot_score += normalized_value * weight
                    snapshot_weight += weight
            
            if snapshot_weight > 0:
                total_score += snapshot_score / snapshot_weight
                total_weight += 1.0
        
        return total_score / total_weight if total_weight > 0 else 0.0
    
    def _generate_productivity_insights(
        self,
        trends: List[ProductivityTrend],
        snapshots: List[ProductivitySnapshot]
    ) -> List[str]:
        """Generate productivity insights."""
        insights = []
        
        # Trend-based insights
        improving_trends = [t for t in trends if t.trend_direction == "improving"]
        declining_trends = [t for t in trends if t.trend_direction == "declining"]
        
        if improving_trends:
            metrics = [t.metric.value for t in improving_trends]
            insights.append(f"Improving trends in: {', '.join(metrics)}")
        
        if declining_trends:
            metrics = [t.metric.value for t in declining_trends]
            insights.append(f"Declining trends in: {', '.join(metrics)}")
        
        # Performance consistency
        if snapshots:
            latest_snapshot = snapshots[-1]
            code_output = latest_snapshot.metrics.get(ProductivityMetric.CODE_OUTPUT.value, 0)
            quality = latest_snapshot.metrics.get(ProductivityMetric.CODE_QUALITY.value, 0)
            
            if code_output > 50 and quality > 80:
                insights.append("High productivity with good code quality")
            elif code_output > 100 and quality < 60:
                insights.append("High output but declining quality - consider balance")
        
        return insights
    
    def _generate_productivity_recommendations(
        self,
        trends: List[ProductivityTrend],
        entity_type: str
    ) -> List[str]:
        """Generate productivity recommendations."""
        recommendations = []
        
        # Check for specific issues
        quality_declining = any(
            t.metric == ProductivityMetric.CODE_QUALITY and t.trend_direction == "declining"
            for t in trends
        )
        
        if quality_declining:
            recommendations.append("Focus on code quality - consider code reviews and refactoring")
        
        collaboration_low = any(
            t.metric == ProductivityMetric.COLLABORATION_INDEX and t.current_value < 2.0
            for t in trends
        )
        
        if collaboration_low:
            recommendations.append("Increase collaboration through pair programming and code reviews")
        
        # Entity-specific recommendations
        if entity_type == "developer":
            recommendations.extend([
                "Set daily productivity goals",
                "Track time spent on different activities",
                "Regular skill development sessions"
            ])
        elif entity_type == "team":
            recommendations.extend([
                "Implement team productivity metrics dashboard",
                "Regular retrospectives to identify blockers",
                "Cross-training to improve team flexibility"
            ])
        
        return recommendations
    
    def _calculate_benchmarks(self, snapshots: List[ProductivitySnapshot], entity_type: str) -> Dict[str, float]:
        """Calculate productivity benchmarks."""
        if not snapshots:
            return {}
        
        # Industry benchmarks (simplified)
        industry_benchmarks = {
            "code_output": 30.0,      # Lines per day
            "commit_frequency": 2.0,   # Commits per day
            "code_quality": 75.0,      # Quality score
            "review_efficiency": 80.0   # Efficiency score
        }
        
        # Calculate current averages
        current_benchmarks = {}
        for metric_name in industry_benchmarks.keys():
            values = [
                snapshot.metrics.get(metric_name, 0)
                for snapshot in snapshots
                if metric_name in snapshot.metrics
            ]
            if values:
                current_benchmarks[f"current_{metric_name}"] = statistics.mean(values)
                current_benchmarks[f"industry_{metric_name}"] = industry_benchmarks[metric_name]
        
        return current_benchmarks
    
    def get_analyzer_statistics(self) -> Dict[str, Any]:
        """Get productivity analyzer statistics."""
        total_reports = len(self.analysis_cache)
        
        if total_reports == 0:
            return {
                "total_reports": 0,
                "avg_productivity_score": 0.0,
                "entity_types": {},
                "trends_analyzed": 0
            }
        
        total_score = sum(report.overall_productivity_score for report in self.analysis_cache.values())
        total_trends = sum(len(report.trends) for report in self.analysis_cache.values())
        
        # Count entity types
        entity_types = {}
        for report in self.analysis_cache.values():
            entity_type = report.entity_type
            entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
        
        return {
            "total_reports": total_reports,
            "avg_productivity_score": total_score / total_reports,
            "entity_types": entity_types,
            "trends_analyzed": total_trends,
            "cache_size": len(self.analysis_cache)
        }
    
    async def cleanup(self) -> None:
        """Cleanup productivity analyzer."""
        self.analysis_cache.clear()
        logger.info("Productivity analyzer cleaned up")
