"""
File: vibe_check/ai/explanation/comment_analyzer.py
Purpose: Code comment quality analysis and improvement suggestions
Related Files: vibe_check/ai/explanation/
Dependencies: typing, re, enum, dataclasses
"""

import re
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime

from vibe_check.core.logging import get_logger
from typing import Any, Dict, List

logger = get_logger(__name__)


class CommentType(Enum):
    """Types of code comments."""
    SINGLE_LINE = "single_line"      # # comment
    MULTI_LINE = "multi_line"        # """ comment """
    DOCSTRING = "docstring"          # Function/class docstrings
    INLINE = "inline"                # code  # comment
    TODO = "todo"                    # TODO: comment
    FIXME = "fixme"                  # FIXME: comment
    NOTE = "note"                    # NOTE: comment


class CommentQuality(Enum):
    """Comment quality levels."""
    EXCELLENT = "excellent"          # Clear, informative, well-written
    GOOD = "good"                   # Helpful and clear
    FAIR = "fair"                   # Basic but adequate
    POOR = "poor"                   # Unclear or unhelpful
    MISSING = "missing"             # No comment where needed


@dataclass
class CommentAnalysis:
    """Analysis result for a single comment."""
    comment_id: str
    comment_text: str
    comment_type: CommentType
    line_number: int
    quality: CommentQuality
    quality_score: float  # 0.0 - 1.0
    issues: List[str] = field(default_factory=list)
    suggestions: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "comment_id": self.comment_id,
            "comment_text": self.comment_text,
            "comment_type": self.comment_type.value,
            "line_number": self.line_number,
            "quality": self.quality.value,
            "quality_score": self.quality_score,
            "issues": self.issues,
            "suggestions": self.suggestions
        }


@dataclass
class CommentReport:
    """Comprehensive comment analysis report."""
    report_id: str
    source_code: str
    total_comments: int
    comments_by_type: Dict[str, int] = field(default_factory=dict)
    comments_by_quality: Dict[str, int] = field(default_factory=dict)
    overall_quality_score: float = 0.0
    comment_coverage: float = 0.0  # Percentage of functions/classes with comments
    analyses: List[CommentAnalysis] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    generated_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "report_id": self.report_id,
            "source_code": self.source_code[:200] + "..." if len(self.source_code) > 200 else self.source_code,
            "total_comments": self.total_comments,
            "comments_by_type": self.comments_by_type,
            "comments_by_quality": self.comments_by_quality,
            "overall_quality_score": self.overall_quality_score,
            "comment_coverage": self.comment_coverage,
            "analyses": [analysis.to_dict() for analysis in self.analyses],
            "recommendations": self.recommendations,
            "generated_at": self.generated_at.isoformat()
        }


class CommentExtractor:
    """Extracts and categorizes comments from source code."""
    
    def __init__(self):
        self.comment_patterns = {
            CommentType.SINGLE_LINE: r'^\s*#\s*(.+)$',
            CommentType.INLINE: r'^(.+?)\s*#\s*(.+)$',
            CommentType.TODO: r'#\s*(TODO|FIXME|NOTE|HACK|XXX):\s*(.+)',
            CommentType.MULTI_LINE: r'"""(.*?)"""',
            CommentType.DOCSTRING: r'^\s*"""(.*?)"""'
        }
    
    def extract_comments(self, code: str) -> List[Dict[str, Any]]:
        """Extract all comments from code."""
        comments = []
        lines = code.split('\n')
        
        # Track if we're in a multi-line string
        in_multiline = False
        multiline_start = 0
        multiline_content = []
        
        for line_num, line in enumerate(lines, 1):
            # Handle multi-line strings/docstrings
            if '"""' in line:
                if not in_multiline:
                    # Starting multi-line
                    in_multiline = True
                    multiline_start = line_num
                    multiline_content = [line.split('"""', 1)[1] if '"""' in line else ""]
                else:
                    # Ending multi-line
                    in_multiline = False
                    multiline_content.append(line.split('"""')[0] if '"""' in line else line)
                    
                    content = '\n'.join(multiline_content).strip()
                    if content:
                        comments.append({
                            "text": content,
                            "type": CommentType.DOCSTRING if self._is_docstring_context(lines, multiline_start - 1) else CommentType.MULTI_LINE,
                            "line_number": multiline_start,
                            "end_line": line_num
                        })
                    multiline_content = []
                continue
            
            if in_multiline:
                multiline_content.append(line)
                continue
            
            # Single line comments
            if '#' in line:
                # Check for TODO/FIXME/NOTE patterns first
                todo_match = re.search(self.comment_patterns[CommentType.TODO], line, re.IGNORECASE)
                if todo_match:
                    comments.append({
                        "text": f"{todo_match.group(1)}: {todo_match.group(2)}",
                        "type": CommentType.TODO,
                        "line_number": line_num
                    })
                    continue
                
                # Check for inline comments
                inline_match = re.match(self.comment_patterns[CommentType.INLINE], line)
                if inline_match and inline_match.group(1).strip():
                    comments.append({
                        "text": inline_match.group(2),
                        "type": CommentType.INLINE,
                        "line_number": line_num,
                        "code_context": inline_match.group(1).strip()
                    })
                    continue
                
                # Regular single-line comment
                single_match = re.match(self.comment_patterns[CommentType.SINGLE_LINE], line)
                if single_match:
                    comments.append({
                        "text": single_match.group(1),
                        "type": CommentType.SINGLE_LINE,
                        "line_number": line_num
                    })
        
        return comments
    
    def _is_docstring_context(self, lines: List[str], line_index: int) -> bool:
        """Check if multi-line string is in docstring context."""
        if line_index < 0:
            return False
        
        # Look backwards for function/class definition
        for i in range(line_index, max(-1, line_index - 5), -1):
            line = lines[i].strip()
            if line.startswith('def ') or line.startswith('class ') or line.startswith('async def '):
                return True
            if line and not line.startswith('#'):
                break
        
        return False


class CommentQualityAnalyzer:
    """Analyzes comment quality and provides improvement suggestions."""
    
    def __init__(self):
        self.quality_indicators = {
            'positive': [
                'explains why',
                'describes purpose',
                'provides context',
                'explains algorithm',
                'documents parameters',
                'explains return value',
                'warns about',
                'notes that',
                'important:'
            ],
            'negative': [
                'obvious statement',
                'redundant',
                'too short',
                'unclear',
                'outdated',
                'wrong',
                'misleading'
            ]
        }
    
    def analyze_comment_quality(self, comment: Dict[str, Any], code_context: str = "") -> CommentAnalysis:
        """Analyze quality of a single comment."""
        comment_text = comment["text"]
        comment_type = comment["type"]
        line_number = comment["line_number"]
        
        # Generate unique ID
        import hashlib
        comment_id = hashlib.md5(f"{comment_text}{line_number}".encode()).hexdigest()[:8]
        
        # Analyze quality
        quality_score = self._calculate_quality_score(comment_text, comment_type, code_context)
        quality = self._determine_quality_level(quality_score)
        
        # Identify issues
        issues = self._identify_issues(comment_text, comment_type, code_context)
        
        # Generate suggestions
        suggestions = self._generate_suggestions(comment_text, comment_type, issues)
        
        return CommentAnalysis(
            comment_id=comment_id,
            comment_text=comment_text,
            comment_type=comment_type,
            line_number=line_number,
            quality=quality,
            quality_score=quality_score,
            issues=issues,
            suggestions=suggestions
        )
    
    def _calculate_quality_score(self, text: str, comment_type: CommentType, code_context: str) -> float:
        """Calculate quality score for comment."""
        score = 0.5  # Base score
        
        # Length considerations
        if len(text) < 5:
            score -= 0.3  # Too short
        elif len(text) > 100:
            score += 0.1  # Detailed
        
        # Content quality
        text_lower = text.lower()
        
        # Positive indicators
        for indicator in self.quality_indicators['positive']:
            if indicator in text_lower:
                score += 0.1
        
        # Negative indicators
        for indicator in self.quality_indicators['negative']:
            if indicator in text_lower:
                score -= 0.2
        
        # Type-specific scoring
        if comment_type == CommentType.DOCSTRING:
            if 'args:' in text_lower or 'parameters:' in text_lower:
                score += 0.2
            if 'returns:' in text_lower:
                score += 0.1
            if 'raises:' in text_lower:
                score += 0.1
        
        elif comment_type == CommentType.TODO:
            if any(word in text_lower for word in ['urgent', 'important', 'critical']):
                score += 0.1
        
        # Grammar and clarity (basic checks)
        if text[0].isupper() and text.endswith('.'):
            score += 0.1  # Proper sentence structure
        
        return max(0.0, min(1.0, score))
    
    def _determine_quality_level(self, score: float) -> CommentQuality:
        """Determine quality level from score."""
        if score >= 0.8:
            return CommentQuality.EXCELLENT
        elif score >= 0.6:
            return CommentQuality.GOOD
        elif score >= 0.4:
            return CommentQuality.FAIR
        else:
            return CommentQuality.POOR
    
    def _identify_issues(self, text: str, comment_type: CommentType, code_context: str) -> List[str]:
        """Identify issues with comment."""
        issues = []
        
        # Length issues
        if len(text) < 5:
            issues.append("Comment is too short to be meaningful")
        
        # Clarity issues
        if not text[0].isupper():
            issues.append("Comment should start with capital letter")
        
        if comment_type == CommentType.DOCSTRING:
            if 'args:' not in text.lower() and 'def ' in code_context:
                issues.append("Function docstring missing parameter documentation")
            
            if 'returns:' not in text.lower() and 'return ' in code_context:
                issues.append("Function docstring missing return value documentation")
        
        # Redundancy check (basic)
        if comment_type == CommentType.INLINE and code_context:
            # Check if comment just repeats variable name
            words_in_comment = set(text.lower().split())
            words_in_code = set(re.findall(r'\b\w+\b', code_context.lower()))
            if words_in_comment.issubset(words_in_code):
                issues.append("Comment appears to be redundant with code")
        
        return issues
    
    def _generate_suggestions(self, text: str, comment_type: CommentType, issues: List[str]) -> List[str]:
        """Generate improvement suggestions."""
        suggestions = []
        
        if "too short" in str(issues):
            suggestions.append("Expand comment to explain the 'why' not just the 'what'")
        
        if "capital letter" in str(issues):
            suggestions.append("Start comment with capital letter for better readability")
        
        if comment_type == CommentType.DOCSTRING:
            if "parameter documentation" in str(issues):
                suggestions.append("Add Args: section to document function parameters")
            
            if "return value documentation" in str(issues):
                suggestions.append("Add Returns: section to document return value")
        
        if "redundant" in str(issues):
            suggestions.append("Focus on explaining why the code exists rather than what it does")
        
        # General suggestions based on type
        if comment_type == CommentType.TODO:
            suggestions.append("Consider adding priority level and target completion date")
        
        return suggestions


class CommentAnalyzer:
    """Main comment analysis system."""
    
    def __init__(self):
        """Initialize comment analyzer."""
        self.extractor = CommentExtractor()
        self.quality_analyzer = CommentQualityAnalyzer()
        self.analysis_cache: Dict[str, CommentReport] = {}
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize comment analyzer."""
        if self._initialized:
            return
        
        self._initialized = True
        logger.info("Comment analyzer initialized")
    
    async def analyze_comments(self, code: str) -> CommentReport:
        """
        Analyze all comments in code.
        
        Args:
            code: Source code to analyze
            
        Returns:
            Comprehensive comment analysis report
        """
        # Generate cache key
        import hashlib
        cache_key = hashlib.md5(code.encode()).hexdigest()
        
        if cache_key in self.analysis_cache:
            logger.debug("Using cached comment analysis")
            return self.analysis_cache[cache_key]
        
        try:
            # Extract comments
            comments = self.extractor.extract_comments(code)
            
            # Analyze each comment
            analyses = []
            for comment in comments:
                analysis = self.quality_analyzer.analyze_comment_quality(comment, code)
                analyses.append(analysis)
            
            # Calculate statistics
            total_comments = len(comments)
            comments_by_type = {}
            comments_by_quality = {}
            total_quality_score = 0.0
            
            for analysis in analyses:
                comment_type = analysis.comment_type.value
                quality = analysis.quality.value
                
                comments_by_type[comment_type] = comments_by_type.get(comment_type, 0) + 1
                comments_by_quality[quality] = comments_by_quality.get(quality, 0) + 1
                total_quality_score += analysis.quality_score
            
            # Calculate overall quality score
            overall_quality_score = total_quality_score / total_comments if total_comments > 0 else 0.0
            
            # Calculate comment coverage
            comment_coverage = self._calculate_comment_coverage(code, comments)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(analyses, comment_coverage)
            
            # Create report
            report = CommentReport(
                report_id=cache_key,
                source_code=code,
                total_comments=total_comments,
                comments_by_type=comments_by_type,
                comments_by_quality=comments_by_quality,
                overall_quality_score=overall_quality_score,
                comment_coverage=comment_coverage,
                analyses=analyses,
                recommendations=recommendations
            )
            
            # Cache result
            self.analysis_cache[cache_key] = report
            
            logger.info(f"Analyzed {total_comments} comments with {overall_quality_score:.2f} average quality")
            return report
            
        except Exception as e:
            logger.error(f"Failed to analyze comments: {e}")
            # Return empty report on error
            return CommentReport(
                report_id=cache_key,
                source_code=code,
                total_comments=0,
                overall_quality_score=0.0,
                comment_coverage=0.0,
                recommendations=[f"Analysis failed: {str(e)}"]
            )
    
    def _calculate_comment_coverage(self, code: str, comments: List[Dict[str, Any]]) -> float:
        """Calculate percentage of functions/classes with comments."""
        # Count functions and classes
        func_class_count = len(re.findall(r'^\s*(def |class |async def )', code, re.MULTILINE))
        
        if func_class_count == 0:
            return 1.0  # No functions/classes to document
        
        # Count docstrings
        docstring_count = len([c for c in comments if c["type"] == CommentType.DOCSTRING])
        
        return min(1.0, docstring_count / func_class_count)
    
    def _generate_recommendations(self, analyses: List[CommentAnalysis], coverage: float) -> List[str]:
        """Generate overall recommendations."""
        recommendations = []
        
        # Coverage recommendations
        if coverage < 0.5:
            recommendations.append("Add docstrings to functions and classes to improve documentation coverage")
        
        # Quality recommendations
        poor_quality_count = len([a for a in analyses if a.quality == CommentQuality.POOR])
        if poor_quality_count > 0:
            recommendations.append(f"Improve {poor_quality_count} low-quality comments")
        
        # Type-specific recommendations
        todo_count = len([a for a in analyses if a.comment_type == CommentType.TODO])
        if todo_count > 5:
            recommendations.append("Consider addressing or prioritizing TODO items")
        
        # General recommendations
        if len(analyses) == 0:
            recommendations.append("Add comments to explain complex logic and design decisions")
        
        return recommendations
    
    def get_comment_statistics(self) -> Dict[str, Any]:
        """Get comment analyzer statistics."""
        total_reports = len(self.analysis_cache)
        
        if total_reports == 0:
            return {
                "total_reports": 0,
                "average_quality_score": 0.0,
                "average_coverage": 0.0,
                "total_comments_analyzed": 0
            }
        
        total_quality = sum(report.overall_quality_score for report in self.analysis_cache.values())
        total_coverage = sum(report.comment_coverage for report in self.analysis_cache.values())
        total_comments = sum(report.total_comments for report in self.analysis_cache.values())
        
        return {
            "total_reports": total_reports,
            "average_quality_score": total_quality / total_reports,
            "average_coverage": total_coverage / total_reports,
            "total_comments_analyzed": total_comments,
            "cache_size": len(self.analysis_cache)
        }
    
    async def cleanup(self) -> None:
        """Cleanup comment analyzer."""
        self.analysis_cache.clear()
        logger.info("Comment analyzer cleaned up")
