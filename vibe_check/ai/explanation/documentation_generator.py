"""
File: vibe_check/ai/explanation/documentation_generator.py
Purpose: Automatic documentation generation using AI
Related Files: vibe_check/ai/explanation/
Dependencies: typing, asyncio, ast, enum, dataclasses
"""

import ast
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime

from vibe_check.ai.infrastructure import AIModelManager
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class DocumentationFormat(Enum):
    """Documentation output formats."""
    MARKDOWN = "markdown"
    RESTRUCTURED_TEXT = "rst"
    HTML = "html"
    DOCSTRING = "docstring"
    SPHINX = "sphinx"


class DocumentationStyle(Enum):
    """Documentation styles."""
    GOOGLE = "google"
    NUMPY = "numpy"
    SPHINX = "sphinx"
    PLAIN = "plain"


@dataclass
class DocumentationSection:
    """Documentation section."""
    title: str
    content: str
    section_type: str
    order: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "title": self.title,
            "content": self.content,
            "section_type": self.section_type,
            "order": self.order
        }


@dataclass
class GeneratedDocumentation:
    """Generated documentation result."""
    doc_id: str
    source_code: str
    documentation_format: DocumentationFormat
    documentation_style: DocumentationStyle
    sections: List[DocumentationSection] = field(default_factory=list)
    generated_at: datetime = field(default_factory=datetime.now)
    completeness_score: float = 0.0
    quality_score: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "doc_id": self.doc_id,
            "source_code": self.source_code[:200] + "..." if len(self.source_code) > 200 else self.source_code,
            "documentation_format": self.documentation_format.value,
            "documentation_style": self.documentation_style.value,
            "sections": [section.to_dict() for section in self.sections],
            "generated_at": self.generated_at.isoformat(),
            "completeness_score": self.completeness_score,
            "quality_score": self.quality_score
        }
    
    def get_full_documentation(self) -> str:
        """Get complete documentation as formatted string."""
        if self.documentation_format == DocumentationFormat.MARKDOWN:
            return self._format_as_markdown()
        elif self.documentation_format == DocumentationFormat.RESTRUCTURED_TEXT:
            return self._format_as_rst()
        elif self.documentation_format == DocumentationFormat.HTML:
            return self._format_as_html()
        else:
            return self._format_as_plain()
    
    def _format_as_markdown(self) -> str:
        """Format documentation as Markdown."""
        lines = []
        for section in sorted(self.sections, key=lambda s: s.order):
            lines.append(f"## {section.title}")
            lines.append("")
            lines.append(section.content)
            lines.append("")
        return "\n".join(lines)
    
    def _format_as_rst(self) -> str:
        """Format documentation as reStructuredText."""
        lines = []
        for section in sorted(self.sections, key=lambda s: s.order):
            lines.append(section.title)
            lines.append("=" * len(section.title))
            lines.append("")
            lines.append(section.content)
            lines.append("")
        return "\n".join(lines)
    
    def _format_as_html(self) -> str:
        """Format documentation as HTML."""
        lines = ["<div class='documentation'>"]
        for section in sorted(self.sections, key=lambda s: s.order):
            lines.append(f"<h2>{section.title}</h2>")
            lines.append(f"<div class='section-content'>{section.content}</div>")
        lines.append("</div>")
        return "\n".join(lines)
    
    def _format_as_plain(self) -> str:
        """Format documentation as plain text."""
        lines = []
        for section in sorted(self.sections, key=lambda s: s.order):
            lines.append(f"{section.title}")
            lines.append("-" * len(section.title))
            lines.append(section.content)
            lines.append("")
        return "\n".join(lines)


class CodeDocumentationAnalyzer:
    """Analyzes code structure for documentation generation."""
    
    def __init__(self):
        self.docstring_patterns = {
            'google': {
                'args': 'Args:',
                'returns': 'Returns:',
                'raises': 'Raises:',
                'example': 'Example:'
            },
            'numpy': {
                'args': 'Parameters',
                'returns': 'Returns',
                'raises': 'Raises',
                'example': 'Examples'
            }
        }
    
    def analyze_code_elements(self, code: str) -> Dict[str, Any]:
        """Analyze code elements for documentation."""
        try:
            tree = ast.parse(code)
            elements = {
                "module_docstring": ast.get_docstring(tree),
                "functions": [],
                "classes": [],
                "constants": [],
                "imports": []
            }
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    func_info = {
                        "name": node.name,
                        "docstring": ast.get_docstring(node),
                        "args": [arg.arg for arg in node.args.args],
                        "returns": self._has_return_statement(node),
                        "is_async": isinstance(node, ast.AsyncFunctionDef),
                        "decorators": [self._get_decorator_name(d) for d in node.decorator_list],
                        "line_number": node.lineno
                    }
                    elements["functions"].append(func_info)
                
                elif isinstance(node, ast.ClassDef):
                    class_info = {
                        "name": node.name,
                        "docstring": ast.get_docstring(node),
                        "methods": [],
                        "bases": [self._get_base_name(base) for base in node.bases],
                        "line_number": node.lineno
                    }
                    
                    # Get methods
                    for item in node.body:
                        if isinstance(item, ast.FunctionDef):
                            method_info = {
                                "name": item.name,
                                "docstring": ast.get_docstring(item),
                                "args": [arg.arg for arg in item.args.args],
                                "is_property": any(
                                    self._get_decorator_name(d) == "property" 
                                    for d in item.decorator_list
                                )
                            }
                            class_info["methods"].append(method_info)
                    
                    elements["classes"].append(class_info)
                
                elif isinstance(node, ast.Assign):
                    # Look for module-level constants
                    for target in node.targets:
                        if isinstance(target, ast.Name) and target.id.isupper():
                            elements["constants"].append({
                                "name": target.id,
                                "line_number": node.lineno
                            })
            
            return elements
            
        except SyntaxError as e:
            logger.warning(f"Failed to parse code for documentation analysis: {e}")
            return {"error": "Invalid Python syntax"}
        except Exception as e:
            logger.error(f"Code documentation analysis failed: {e}")
            return {"error": str(e)}
    
    def _has_return_statement(self, func_node: ast.FunctionDef) -> bool:
        """Check if function has return statement."""
        for node in ast.walk(func_node):
            if isinstance(node, ast.Return) and node.value is not None:
                return True
        return False
    
    def _get_decorator_name(self, decorator: ast.expr) -> str:
        """Get decorator name."""
        if isinstance(decorator, ast.Name):
            return decorator.id
        elif isinstance(decorator, ast.Attribute):
            return decorator.attr
        else:
            return "unknown"
    
    def _get_base_name(self, base: ast.expr) -> str:
        """Get base class name."""
        if isinstance(base, ast.Name):
            return base.id
        elif isinstance(base, ast.Attribute):
            return base.attr
        else:
            return "unknown"
    
    def calculate_documentation_completeness(self, elements: Dict[str, Any]) -> float:
        """Calculate documentation completeness score."""
        total_items = 0
        documented_items = 0
        
        # Module docstring
        total_items += 1
        if elements.get("module_docstring"):
            documented_items += 1
        
        # Functions
        for func in elements.get("functions", []):
            total_items += 1
            if func.get("docstring"):
                documented_items += 1
        
        # Classes
        for cls in elements.get("classes", []):
            total_items += 1
            if cls.get("docstring"):
                documented_items += 1
            
            # Class methods
            for method in cls.get("methods", []):
                total_items += 1
                if method.get("docstring"):
                    documented_items += 1
        
        return (documented_items / total_items) if total_items > 0 else 0.0


class DocumentationGenerator:
    """Automatic documentation generator using AI."""
    
    def __init__(self, model_manager: Optional[AIModelManager] = None):
        """
        Initialize documentation generator.
        
        Args:
            model_manager: AI model manager instance
        """
        self.model_manager = model_manager
        self.code_analyzer = CodeDocumentationAnalyzer()
        self.documentation_cache: Dict[str, GeneratedDocumentation] = {}
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize documentation generator."""
        if self._initialized:
            return
        
        if not self.model_manager:
            self.model_manager = AIModelManager()
            await self.model_manager.initialize()
        
        self._initialized = True
        logger.info("Documentation generator initialized")
    
    async def generate_documentation(
        self,
        code: str,
        doc_format: DocumentationFormat = DocumentationFormat.MARKDOWN,
        doc_style: DocumentationStyle = DocumentationStyle.GOOGLE,
        include_examples: bool = True
    ) -> GeneratedDocumentation:
        """
        Generate comprehensive documentation for code.
        
        Args:
            code: Source code to document
            doc_format: Output documentation format
            doc_style: Documentation style
            include_examples: Whether to include usage examples
            
        Returns:
            Generated documentation
        """
        # Generate cache key
        import hashlib
        cache_key = hashlib.md5(
            f"{code}{doc_format.value}{doc_style.value}{include_examples}".encode()
        ).hexdigest()
        
        if cache_key in self.documentation_cache:
            logger.debug("Using cached documentation")
            return self.documentation_cache[cache_key]
        
        try:
            # Analyze code structure
            elements = self.code_analyzer.analyze_code_elements(code)
            
            if "error" in elements:
                raise ValueError(f"Code analysis failed: {elements['error']}")
            
            # Generate documentation sections
            sections = []
            
            # Overview section
            overview_section = await self._generate_overview_section(code, elements)
            sections.append(overview_section)
            
            # API documentation sections
            if elements.get("functions"):
                functions_section = await self._generate_functions_section(
                    elements["functions"], doc_style, include_examples
                )
                sections.append(functions_section)
            
            if elements.get("classes"):
                classes_section = await self._generate_classes_section(
                    elements["classes"], doc_style, include_examples
                )
                sections.append(classes_section)
            
            # Usage examples section
            if include_examples:
                examples_section = await self._generate_examples_section(code, elements)
                sections.append(examples_section)
            
            # Calculate quality scores
            completeness_score = self.code_analyzer.calculate_documentation_completeness(elements)
            quality_score = self._calculate_documentation_quality(sections)
            
            # Create documentation result
            documentation = GeneratedDocumentation(
                doc_id=cache_key,
                source_code=code,
                documentation_format=doc_format,
                documentation_style=doc_style,
                sections=sections,
                completeness_score=completeness_score,
                quality_score=quality_score
            )
            
            # Cache result
            self.documentation_cache[cache_key] = documentation
            
            logger.info(f"Generated {doc_format.value} documentation ({doc_style.value} style)")
            return documentation
            
        except Exception as e:
            logger.error(f"Failed to generate documentation: {e}")
            # Return minimal documentation on error
            return GeneratedDocumentation(
                doc_id=cache_key,
                source_code=code,
                documentation_format=doc_format,
                documentation_style=doc_style,
                sections=[DocumentationSection(
                    title="Error",
                    content=f"Failed to generate documentation: {str(e)}",
                    section_type="error",
                    order=0
                )],
                completeness_score=0.0,
                quality_score=0.0
            )
    
    async def _generate_overview_section(self, code: str, elements: Dict[str, Any]) -> DocumentationSection:
        """Generate overview section."""
        # Count elements
        num_functions = len(elements.get("functions", []))
        num_classes = len(elements.get("classes", []))
        num_imports = len(elements.get("imports", []))
        
        # Generate AI-powered overview if model available
        if self.model_manager:
            try:
                prompt = f"""
Generate a brief overview for this Python code:

Code structure:
- Functions: {num_functions}
- Classes: {num_classes}
- Imports: {num_imports}

Code:
```python
{code[:500]}...
```

Provide a concise overview of what this code does.
"""
                
                ai_overview = await self.model_manager.generate_response(
                    "code_explainer_v1",
                    prompt,
                    {"temperature": 0.2, "max_tokens": 256}
                )
                
                if ai_overview:
                    return DocumentationSection(
                        title="Overview",
                        content=ai_overview,
                        section_type="overview",
                        order=1
                    )
            
            except Exception as e:
                logger.warning(f"AI overview generation failed: {e}")
        
        # Fallback overview
        overview_parts = []
        
        if num_classes > 0:
            overview_parts.append(f"This module defines {num_classes} class{'es' if num_classes > 1 else ''}")
        
        if num_functions > 0:
            overview_parts.append(f"implements {num_functions} function{'s' if num_functions > 1 else ''}")
        
        if num_imports > 0:
            overview_parts.append(f"and imports {num_imports} external module{'s' if num_imports > 1 else ''}")
        
        overview_text = " ".join(overview_parts) + "." if overview_parts else "This is a Python code module."
        
        return DocumentationSection(
            title="Overview",
            content=overview_text,
            section_type="overview",
            order=1
        )
    
    async def _generate_functions_section(
        self,
        functions: List[Dict[str, Any]],
        doc_style: DocumentationStyle,
        include_examples: bool
    ) -> DocumentationSection:
        """Generate functions documentation section."""
        content_parts = []
        
        for func in functions:
            func_doc = f"### {func['name']}\n\n"
            
            if func.get("docstring"):
                func_doc += f"{func['docstring']}\n\n"
            else:
                func_doc += f"Function: {func['name']}\n\n"
            
            # Parameters
            if func.get("args"):
                func_doc += "**Parameters:**\n"
                for arg in func["args"]:
                    func_doc += f"- `{arg}`: Parameter description\n"
                func_doc += "\n"
            
            # Return value
            if func.get("returns"):
                func_doc += "**Returns:**\n"
                func_doc += "- Return value description\n\n"
            
            content_parts.append(func_doc)
        
        return DocumentationSection(
            title="Functions",
            content="\n".join(content_parts),
            section_type="functions",
            order=2
        )
    
    async def _generate_classes_section(
        self,
        classes: List[Dict[str, Any]],
        doc_style: DocumentationStyle,
        include_examples: bool
    ) -> DocumentationSection:
        """Generate classes documentation section."""
        content_parts = []
        
        for cls in classes:
            class_doc = f"### {cls['name']}\n\n"
            
            if cls.get("docstring"):
                class_doc += f"{cls['docstring']}\n\n"
            else:
                class_doc += f"Class: {cls['name']}\n\n"
            
            # Inheritance
            if cls.get("bases"):
                class_doc += f"**Inherits from:** {', '.join(cls['bases'])}\n\n"
            
            # Methods
            if cls.get("methods"):
                class_doc += "**Methods:**\n"
                for method in cls["methods"]:
                    class_doc += f"- `{method['name']}`: Method description\n"
                class_doc += "\n"
            
            content_parts.append(class_doc)
        
        return DocumentationSection(
            title="Classes",
            content="\n".join(content_parts),
            section_type="classes",
            order=3
        )
    
    async def _generate_examples_section(self, code: str, elements: Dict[str, Any]) -> DocumentationSection:
        """Generate usage examples section."""
        examples = []
        
        # Generate basic usage examples
        if elements.get("functions"):
            func_name = elements["functions"][0]["name"]
            examples.append(f"""
```python
# Example usage
result = {func_name}()
print(result)
```
""")
        
        if elements.get("classes"):
            class_name = elements["classes"][0]["name"]
            examples.append(f"""
```python
# Example instantiation
instance = {class_name}()
```
""")
        
        content = "\n".join(examples) if examples else "No usage examples available."
        
        return DocumentationSection(
            title="Examples",
            content=content,
            section_type="examples",
            order=4
        )
    
    def _calculate_documentation_quality(self, sections: List[DocumentationSection]) -> float:
        """Calculate documentation quality score."""
        if not sections:
            return 0.0
        
        quality_factors = []
        
        # Check for overview
        has_overview = any(s.section_type == "overview" for s in sections)
        quality_factors.append(0.3 if has_overview else 0.0)
        
        # Check for API documentation
        has_api_docs = any(s.section_type in ["functions", "classes"] for s in sections)
        quality_factors.append(0.4 if has_api_docs else 0.0)
        
        # Check for examples
        has_examples = any(s.section_type == "examples" for s in sections)
        quality_factors.append(0.3 if has_examples else 0.0)
        
        return sum(quality_factors)
    
    def get_documentation_statistics(self) -> Dict[str, Any]:
        """Get documentation generator statistics."""
        total_docs = len(self.documentation_cache)
        
        if total_docs == 0:
            return {
                "total_documentation": 0,
                "docs_by_format": {},
                "docs_by_style": {},
                "average_completeness": 0.0,
                "average_quality": 0.0
            }
        
        # Count by format and style
        by_format = {}
        by_style = {}
        total_completeness = 0.0
        total_quality = 0.0
        
        for doc in self.documentation_cache.values():
            doc_format = doc.documentation_format.value
            doc_style = doc.documentation_style.value
            
            by_format[doc_format] = by_format.get(doc_format, 0) + 1
            by_style[doc_style] = by_style.get(doc_style, 0) + 1
            total_completeness += doc.completeness_score
            total_quality += doc.quality_score
        
        return {
            "total_documentation": total_docs,
            "docs_by_format": by_format,
            "docs_by_style": by_style,
            "average_completeness": total_completeness / total_docs,
            "average_quality": total_quality / total_docs,
            "cache_size": len(self.documentation_cache)
        }
    
    async def cleanup(self) -> None:
        """Cleanup documentation generator."""
        self.documentation_cache.clear()
        logger.info("Documentation generator cleaned up")
