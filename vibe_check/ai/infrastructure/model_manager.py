"""
File: vibe_check/ai/infrastructure/model_manager.py
Purpose: AI model management system for local LLM integration
Related Files: vibe_check/ai/infrastructure/
Dependencies: typing, asyncio, pathlib, enum, dataclasses
"""

import asyncio
import json
from pathlib import Path
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime

from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class ModelType(Enum):
    """Types of AI models."""
    CODE_ANALYSIS = "code_analysis"
    CODE_EXPLANATION = "code_explanation"
    REFACTORING = "refactoring"
    DOCUMENTATION = "documentation"
    GENERAL_PURPOSE = "general_purpose"


class ModelStatus(Enum):
    """Model status."""
    AVAILABLE = "available"
    LOADING = "loading"
    LOADED = "loaded"
    ERROR = "error"
    UNLOADED = "unloaded"


@dataclass
class ModelConfig:
    """AI model configuration."""
    model_id: str
    name: str
    model_type: ModelType
    model_path: Optional[str] = None
    model_url: Optional[str] = None
    version: str = "1.0.0"
    description: str = ""
    parameters: Dict[str, Any] = field(default_factory=dict)
    requirements: Dict[str, Any] = field(default_factory=dict)
    privacy_level: str = "high"
    max_context_length: int = 4096
    supports_streaming: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "model_id": self.model_id,
            "name": self.name,
            "model_type": self.model_type.value,
            "model_path": self.model_path,
            "model_url": self.model_url,
            "version": self.version,
            "description": self.description,
            "parameters": self.parameters,
            "requirements": self.requirements,
            "privacy_level": self.privacy_level,
            "max_context_length": self.max_context_length,
            "supports_streaming": self.supports_streaming
        }


@dataclass
class ModelInstance:
    """Running model instance."""
    model_id: str
    config: ModelConfig
    status: ModelStatus = ModelStatus.AVAILABLE
    loaded_at: Optional[datetime] = None
    last_used: Optional[datetime] = None
    usage_count: int = 0
    memory_usage: int = 0  # MB
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "model_id": self.model_id,
            "config": self.config.to_dict(),
            "status": self.status.value,
            "loaded_at": self.loaded_at.isoformat() if self.loaded_at else None,
            "last_used": self.last_used.isoformat() if self.last_used else None,
            "usage_count": self.usage_count,
            "memory_usage": self.memory_usage,
            "error_message": self.error_message
        }


class AIModelManager:
    """AI model management system for local LLM integration."""
    
    def __init__(self, models_dir: Optional[Path] = None):
        """
        Initialize AI model manager.
        
        Args:
            models_dir: Directory for storing model files
        """
        self.models_dir = models_dir or Path.cwd() / "ai_models"
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
        # Model registry
        self.model_configs: Dict[str, ModelConfig] = {}
        self.model_instances: Dict[str, ModelInstance] = {}
        
        # Configuration
        self.max_loaded_models = 3
        self.auto_unload_timeout = 3600  # 1 hour
        
        # State
        self._initialized = False
        self._cleanup_task: Optional[asyncio.Task] = None
    
    async def initialize(self) -> None:
        """Initialize AI model manager."""
        if self._initialized:
            return
        
        # Load model configurations
        await self._load_model_configs()
        
        # Initialize default models
        await self._initialize_default_models()
        
        # Start cleanup task
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        self._initialized = True
        logger.info(f"AI model manager initialized with {len(self.model_configs)} models")
    
    async def _load_model_configs(self) -> None:
        """Load model configurations from disk."""
        config_file = self.models_dir / "models.json"
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    configs_data = json.load(f)
                
                for config_data in configs_data:
                    config = ModelConfig(
                        model_id=config_data["model_id"],
                        name=config_data["name"],
                        model_type=ModelType(config_data["model_type"]),
                        model_path=config_data.get("model_path"),
                        model_url=config_data.get("model_url"),
                        version=config_data.get("version", "1.0.0"),
                        description=config_data.get("description", ""),
                        parameters=config_data.get("parameters", {}),
                        requirements=config_data.get("requirements", {}),
                        privacy_level=config_data.get("privacy_level", "high"),
                        max_context_length=config_data.get("max_context_length", 4096),
                        supports_streaming=config_data.get("supports_streaming", True)
                    )
                    self.model_configs[config.model_id] = config
                
                logger.debug(f"Loaded {len(self.model_configs)} model configurations")
                
            except Exception as e:
                logger.error(f"Failed to load model configurations: {e}")
    
    async def _save_model_configs(self) -> None:
        """Save model configurations to disk."""
        config_file = self.models_dir / "models.json"
        try:
            configs_data = [config.to_dict() for config in self.model_configs.values()]
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(configs_data, f, indent=2)
            
            logger.debug("Saved model configurations")
            
        except Exception as e:
            logger.error(f"Failed to save model configurations: {e}")
    
    async def _initialize_default_models(self) -> None:
        """Initialize default AI models."""
        default_models = [
            ModelConfig(
                model_id="code_analyzer_v1",
                name="Code Analysis Model v1",
                model_type=ModelType.CODE_ANALYSIS,
                description="Local model for code analysis and quality assessment",
                parameters={
                    "temperature": 0.1,
                    "max_tokens": 2048,
                    "top_p": 0.9
                },
                requirements={
                    "min_memory_gb": 4,
                    "gpu_required": False
                },
                max_context_length=8192
            ),
            ModelConfig(
                model_id="code_explainer_v1",
                name="Code Explanation Model v1",
                model_type=ModelType.CODE_EXPLANATION,
                description="Local model for generating code explanations and documentation",
                parameters={
                    "temperature": 0.3,
                    "max_tokens": 1024,
                    "top_p": 0.95
                },
                requirements={
                    "min_memory_gb": 2,
                    "gpu_required": False
                },
                max_context_length=4096
            ),
            ModelConfig(
                model_id="refactoring_assistant_v1",
                name="Refactoring Assistant v1",
                model_type=ModelType.REFACTORING,
                description="Local model for suggesting code refactoring improvements",
                parameters={
                    "temperature": 0.2,
                    "max_tokens": 1536,
                    "top_p": 0.9
                },
                requirements={
                    "min_memory_gb": 3,
                    "gpu_required": False
                },
                max_context_length=6144
            )
        ]
        
        for model_config in default_models:
            if model_config.model_id not in self.model_configs:
                self.model_configs[model_config.model_id] = model_config
        
        await self._save_model_configs()
        logger.info(f"Initialized {len(default_models)} default AI models")
    
    async def _cleanup_loop(self) -> None:
        """Background cleanup loop for model management."""
        while True:
            try:
                await asyncio.sleep(300)  # Run every 5 minutes
                await self._cleanup_unused_models()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Model cleanup error: {e}")
    
    async def _cleanup_unused_models(self) -> None:
        """Cleanup unused models to free memory."""
        now = datetime.now()
        models_to_unload = []
        
        for model_id, instance in self.model_instances.items():
            if (instance.status == ModelStatus.LOADED and 
                instance.last_used and 
                (now - instance.last_used).total_seconds() > self.auto_unload_timeout):
                models_to_unload.append(model_id)
        
        for model_id in models_to_unload:
            await self.unload_model(model_id)
            logger.info(f"Auto-unloaded unused model: {model_id}")
    
    async def register_model(self, config: ModelConfig) -> bool:
        """
        Register a new AI model.
        
        Args:
            config: Model configuration
            
        Returns:
            True if registered successfully, False otherwise
        """
        try:
            self.model_configs[config.model_id] = config
            await self._save_model_configs()
            
            logger.info(f"Registered AI model: {config.name} ({config.model_id})")
            return True
            
        except Exception as e:
            logger.error(f"Failed to register model {config.model_id}: {e}")
            return False
    
    async def load_model(self, model_id: str) -> bool:
        """
        Load an AI model into memory.
        
        Args:
            model_id: Model identifier
            
        Returns:
            True if loaded successfully, False otherwise
        """
        if model_id not in self.model_configs:
            logger.error(f"Model {model_id} not found in registry")
            return False
        
        if model_id in self.model_instances:
            instance = self.model_instances[model_id]
            if instance.status == ModelStatus.LOADED:
                logger.debug(f"Model {model_id} already loaded")
                return True
        
        # Check if we need to unload models to free memory
        if len([i for i in self.model_instances.values() if i.status == ModelStatus.LOADED]) >= self.max_loaded_models:
            await self._unload_least_used_model()
        
        try:
            config = self.model_configs[model_id]
            
            # Create model instance
            instance = ModelInstance(
                model_id=model_id,
                config=config,
                status=ModelStatus.LOADING,
                loaded_at=datetime.now()
            )
            self.model_instances[model_id] = instance
            
            # Simulate model loading (in real implementation, load actual model)
            await asyncio.sleep(1.0)  # Simulate loading time
            
            # Mock memory usage calculation
            instance.memory_usage = config.requirements.get("min_memory_gb", 2) * 1024  # MB
            instance.status = ModelStatus.LOADED
            
            logger.info(f"Loaded AI model: {config.name} ({model_id})")
            return True
            
        except Exception as e:
            if model_id in self.model_instances:
                self.model_instances[model_id].status = ModelStatus.ERROR
                self.model_instances[model_id].error_message = str(e)
            
            logger.error(f"Failed to load model {model_id}: {e}")
            return False
    
    async def unload_model(self, model_id: str) -> bool:
        """
        Unload an AI model from memory.
        
        Args:
            model_id: Model identifier
            
        Returns:
            True if unloaded successfully, False otherwise
        """
        if model_id not in self.model_instances:
            return True  # Already unloaded
        
        try:
            instance = self.model_instances[model_id]
            instance.status = ModelStatus.UNLOADED
            instance.memory_usage = 0
            
            logger.info(f"Unloaded AI model: {model_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to unload model {model_id}: {e}")
            return False
    
    async def _unload_least_used_model(self) -> None:
        """Unload the least recently used model."""
        loaded_models = [
            (model_id, instance) for model_id, instance in self.model_instances.items()
            if instance.status == ModelStatus.LOADED
        ]
        
        if not loaded_models:
            return
        
        # Find least recently used model
        least_used = min(loaded_models, key=lambda x: x[1].last_used or datetime.min)
        await self.unload_model(least_used[0])
    
    async def generate_response(
        self,
        model_id: str,
        prompt: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> Optional[str]:
        """
        Generate response using specified AI model.
        
        Args:
            model_id: Model identifier
            prompt: Input prompt
            parameters: Generation parameters
            
        Returns:
            Generated response or None if failed
        """
        # Ensure model is loaded
        if not await self.load_model(model_id):
            return None
        
        instance = self.model_instances[model_id]
        if instance.status != ModelStatus.LOADED:
            logger.error(f"Model {model_id} not loaded")
            return None
        
        try:
            # Update usage statistics
            instance.last_used = datetime.now()
            instance.usage_count += 1
            
            # Merge parameters
            config = instance.config
            gen_params = {**config.parameters, **(parameters or {})}
            
            # Simulate AI response generation (in real implementation, call actual model)
            await asyncio.sleep(0.5)  # Simulate processing time
            
            # Mock response based on model type
            if config.model_type == ModelType.CODE_ANALYSIS:
                response = f"Code analysis result for prompt: {prompt[:50]}..."
            elif config.model_type == ModelType.CODE_EXPLANATION:
                response = f"Code explanation: {prompt[:50]}..."
            elif config.model_type == ModelType.REFACTORING:
                response = f"Refactoring suggestions for: {prompt[:50]}..."
            else:
                response = f"AI response to: {prompt[:50]}..."
            
            logger.debug(f"Generated response using model {model_id}")
            return response
            
        except Exception as e:
            logger.error(f"Failed to generate response with model {model_id}: {e}")
            return None
    
    def get_model_info(self, model_id: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific model."""
        if model_id not in self.model_configs:
            return None
        
        config = self.model_configs[model_id]
        instance = self.model_instances.get(model_id)
        
        info = config.to_dict()
        if instance:
            info.update({
                "status": instance.status.value,
                "loaded_at": instance.loaded_at.isoformat() if instance.loaded_at else None,
                "last_used": instance.last_used.isoformat() if instance.last_used else None,
                "usage_count": instance.usage_count,
                "memory_usage": instance.memory_usage,
                "error_message": instance.error_message
            })
        else:
            info["status"] = ModelStatus.AVAILABLE.value
        
        return info
    
    def list_models(self, model_type: Optional[ModelType] = None) -> List[Dict[str, Any]]:
        """
        List available AI models.
        
        Args:
            model_type: Filter by model type
            
        Returns:
            List of model information
        """
        models = []
        for model_id, config in self.model_configs.items():
            if model_type is None or config.model_type == model_type:
                models.append(self.get_model_info(model_id))
        
        return models
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get AI model manager statistics."""
        total_models = len(self.model_configs)
        loaded_models = len([i for i in self.model_instances.values() if i.status == ModelStatus.LOADED])
        total_memory = sum(i.memory_usage for i in self.model_instances.values())
        total_usage = sum(i.usage_count for i in self.model_instances.values())
        
        # Models by type
        models_by_type = {}
        for config in self.model_configs.values():
            model_type = config.model_type.value
            models_by_type[model_type] = models_by_type.get(model_type, 0) + 1
        
        return {
            "total_models": total_models,
            "loaded_models": loaded_models,
            "total_memory_usage_mb": total_memory,
            "total_usage_count": total_usage,
            "models_by_type": models_by_type,
            "max_loaded_models": self.max_loaded_models,
            "auto_unload_timeout": self.auto_unload_timeout
        }
    
    async def cleanup(self) -> None:
        """Cleanup AI model manager."""
        # Cancel cleanup task
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        # Unload all models
        for model_id in list(self.model_instances.keys()):
            await self.unload_model(model_id)
        
        logger.info("AI model manager cleaned up")
