"""
File: vibe_check/ai/infrastructure/privacy_processor.py
Purpose: Privacy-preserving AI processing for secure code analysis
Related Files: vibe_check/ai/infrastructure/
Dependencies: typing, re, hashlib, enum
"""

import re
import hashlib
from enum import Enum

from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class PrivacyLevel(Enum):
    """Privacy protection levels."""
    MINIMAL = "minimal"      # Basic sanitization
    STANDARD = "standard"    # Standard privacy protection
    HIGH = "high"           # High privacy protection
    MAXIMUM = "maximum"     # Maximum privacy protection


@dataclass
class SanitizationRule:
    """Data sanitization rule."""
    name: str
    pattern: str
    replacement: str
    description: str
    enabled: bool = True
    
    def apply(self, text: str) -> str:
        """Apply sanitization rule to text."""
        if not self.enabled:
            return text
        
        try:
            return re.sub(self.pattern, self.replacement, text, flags=re.IGNORECASE | re.MULTILINE)
        except Exception as e:
            logger.warning(f"Failed to apply sanitization rule {self.name}: {e}")
            return text


class DataSanitizer:
    """Data sanitization engine for privacy protection."""
    
    def __init__(self, privacy_level: PrivacyLevel = PrivacyLevel.STANDARD):
        """
        Initialize data sanitizer.
        
        Args:
            privacy_level: Privacy protection level
        """
        self.privacy_level = privacy_level
        self.rules: List[SanitizationRule] = []
        self.custom_patterns: Dict[str, str] = {}
        
        # Initialize default rules
        self._initialize_default_rules()
    
    def _initialize_default_rules(self) -> None:
        """Initialize default sanitization rules."""
        # Email addresses
        self.rules.append(SanitizationRule(
            name="email_addresses",
            pattern=r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            replacement="[EMAIL_REDACTED]",
            description="Remove email addresses"
        ))
        
        # IP addresses
        self.rules.append(SanitizationRule(
            name="ip_addresses",
            pattern=r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b',
            replacement="[IP_REDACTED]",
            description="Remove IP addresses"
        ))
        
        # URLs
        self.rules.append(SanitizationRule(
            name="urls",
            pattern=r'https?://[^\s<>"{}|\\^`\[\]]+',
            replacement="[URL_REDACTED]",
            description="Remove URLs"
        ))
        
        # API keys (common patterns)
        self.rules.append(SanitizationRule(
            name="api_keys",
            pattern=r'(?i)(api[_-]?key|token|secret)["\s]*[:=]["\s]*[a-zA-Z0-9_-]{16,}',
            replacement=r'\1="[API_KEY_REDACTED]"',
            description="Remove API keys and tokens"
        ))
        
        # Database connection strings
        self.rules.append(SanitizationRule(
            name="db_connections",
            pattern=r'(?i)(password|pwd)["\s]*[:=]["\s]*[^"\s;]+',
            replacement=r'\1="[PASSWORD_REDACTED]"',
            description="Remove database passwords"
        ))
        
        # File paths (if high privacy)
        if self.privacy_level in [PrivacyLevel.HIGH, PrivacyLevel.MAXIMUM]:
            self.rules.append(SanitizationRule(
                name="file_paths",
                pattern=r'(?:[A-Za-z]:\\|/)[^\s<>"{}|\\^`\[\]]*',
                replacement="[PATH_REDACTED]",
                description="Remove file paths"
            ))
        
        # Personal names (if maximum privacy)
        if self.privacy_level == PrivacyLevel.MAXIMUM:
            self.rules.append(SanitizationRule(
                name="personal_names",
                pattern=r'\b[A-Z][a-z]+ [A-Z][a-z]+\b',
                replacement="[NAME_REDACTED]",
                description="Remove personal names"
            ))
    
    def add_custom_pattern(self, name: str, pattern: str, replacement: str, description: str = "") -> None:
        """
        Add custom sanitization pattern.
        
        Args:
            name: Pattern name
            pattern: Regex pattern
            replacement: Replacement string
            description: Pattern description
        """
        rule = SanitizationRule(
            name=f"custom_{name}",
            pattern=pattern,
            replacement=replacement,
            description=description or f"Custom pattern: {name}"
        )
        self.rules.append(rule)
        logger.info(f"Added custom sanitization pattern: {name}")
    
    def sanitize_text(self, text: str) -> str:
        """
        Sanitize text according to privacy level.
        
        Args:
            text: Text to sanitize
            
        Returns:
            Sanitized text
        """
        sanitized = text
        
        for rule in self.rules:
            sanitized = rule.apply(sanitized)
        
        return sanitized
    
    def sanitize_code(self, code: str, preserve_structure: bool = True) -> str:
        """
        Sanitize code while preserving structure.
        
        Args:
            code: Code to sanitize
            preserve_structure: Whether to preserve code structure
            
        Returns:
            Sanitized code
        """
        if not preserve_structure:
            return self.sanitize_text(code)
        
        # Split code into lines for line-by-line processing
        lines = code.split('\n')
        sanitized_lines = []
        
        for line in lines:
            # Preserve indentation and basic structure
            stripped = line.lstrip()
            indent = line[:len(line) - len(stripped)]
            
            # Sanitize the content while preserving syntax
            sanitized_content = self.sanitize_text(stripped)
            
            # Reconstruct line
            sanitized_lines.append(indent + sanitized_content)
        
        return '\n'.join(sanitized_lines)
    
    def get_sanitization_report(self, original: str, sanitized: str) -> Dict[str, Any]:
        """
        Generate sanitization report.
        
        Args:
            original: Original text
            sanitized: Sanitized text
            
        Returns:
            Sanitization report
        """
        changes = 0
        rules_applied = []
        
        # Check which rules were applied
        temp_text = original
        for rule in self.rules:
            if rule.enabled:
                before = temp_text
                temp_text = rule.apply(temp_text)
                if before != temp_text:
                    changes += 1
                    rules_applied.append(rule.name)
        
        return {
            "privacy_level": self.privacy_level.value,
            "rules_applied": rules_applied,
            "changes_made": changes,
            "original_length": len(original),
            "sanitized_length": len(sanitized),
            "reduction_ratio": (len(original) - len(sanitized)) / len(original) if original else 0
        }


class PrivacyPreservingProcessor:
    """Privacy-preserving AI processing system."""
    
    def __init__(self, privacy_level: PrivacyLevel = PrivacyLevel.STANDARD):
        """
        Initialize privacy-preserving processor.
        
        Args:
            privacy_level: Privacy protection level
        """
        self.privacy_level = privacy_level
        self.sanitizer = DataSanitizer(privacy_level)
        self.processing_cache: Dict[str, str] = {}
        self.anonymization_map: Dict[str, str] = {}
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize privacy-preserving processor."""
        if self._initialized:
            return
        
        self._initialized = True
        logger.info(f"Privacy-preserving processor initialized (level: {self.privacy_level.value})")
    
    def _generate_hash(self, data: str) -> str:
        """Generate hash for data anonymization."""
        return hashlib.sha256(data.encode()).hexdigest()[:16]
    
    def anonymize_identifiers(self, code: str) -> Tuple[str, Dict[str, str]]:
        """
        Anonymize identifiers in code.
        
        Args:
            code: Source code
            
        Returns:
            Tuple of (anonymized_code, mapping)
        """
        # Extract identifiers (simplified approach)
        identifier_pattern = r'\b[a-zA-Z_][a-zA-Z0-9_]*\b'
        identifiers = set(re.findall(identifier_pattern, code))
        
        # Filter out keywords and common terms
        keywords = {
            'if', 'else', 'for', 'while', 'def', 'class', 'import', 'from', 'return',
            'try', 'except', 'finally', 'with', 'as', 'and', 'or', 'not', 'in', 'is',
            'True', 'False', 'None', 'self', 'cls', 'int', 'str', 'list', 'dict',
            'len', 'range', 'print', 'open', 'close', 'read', 'write'
        }
        
        identifiers = identifiers - keywords
        
        # Create anonymization mapping
        mapping = {}
        anonymized_code = code
        
        for i, identifier in enumerate(sorted(identifiers)):
            if len(identifier) > 2:  # Only anonymize meaningful identifiers
                anon_name = f"var_{i:03d}"
                mapping[identifier] = anon_name
                
                # Replace in code (word boundaries to avoid partial matches)
                anonymized_code = re.sub(
                    rf'\b{re.escape(identifier)}\b',
                    anon_name,
                    anonymized_code
                )
        
        return anonymized_code, mapping
    
    async def process_code_safely(
        self,
        code: str,
        preserve_structure: bool = True,
        anonymize_identifiers: bool = False
    ) -> Dict[str, Any]:
        """
        Process code with privacy protection.
        
        Args:
            code: Source code to process
            preserve_structure: Whether to preserve code structure
            anonymize_identifiers: Whether to anonymize variable names
            
        Returns:
            Processing result with privacy protection
        """
        # Generate cache key
        cache_key = self._generate_hash(code + str(preserve_structure) + str(anonymize_identifiers))
        
        if cache_key in self.processing_cache:
            logger.debug("Using cached privacy-processed result")
            return {"processed_code": self.processing_cache[cache_key], "cached": True}
        
        try:
            # Step 1: Sanitize sensitive data
            sanitized_code = self.sanitizer.sanitize_code(code, preserve_structure)
            
            # Step 2: Anonymize identifiers if requested
            anonymized_code = sanitized_code
            identifier_mapping = {}
            
            if anonymize_identifiers and self.privacy_level in [PrivacyLevel.HIGH, PrivacyLevel.MAXIMUM]:
                anonymized_code, identifier_mapping = self.anonymize_identifiers(sanitized_code)
            
            # Step 3: Generate processing report
            sanitization_report = self.sanitizer.get_sanitization_report(code, sanitized_code)
            
            # Cache result
            self.processing_cache[cache_key] = anonymized_code
            
            result = {
                "processed_code": anonymized_code,
                "original_length": len(code),
                "processed_length": len(anonymized_code),
                "sanitization_report": sanitization_report,
                "identifier_mapping": identifier_mapping,
                "privacy_level": self.privacy_level.value,
                "cached": False
            }
            
            logger.debug(f"Processed code with privacy protection (level: {self.privacy_level.value})")
            return result
            
        except Exception as e:
            logger.error(f"Failed to process code safely: {e}")
            return {
                "processed_code": code,  # Return original on error
                "error": str(e),
                "cached": False
            }
    
    def restore_identifiers(self, processed_result: str, identifier_mapping: Dict[str, str]) -> str:
        """
        Restore original identifiers from anonymized code.
        
        Args:
            processed_result: Processed code with anonymized identifiers
            identifier_mapping: Mapping from original to anonymized names
            
        Returns:
            Code with restored identifiers
        """
        restored = processed_result
        
        # Reverse the mapping
        reverse_mapping = {v: k for k, v in identifier_mapping.items()}
        
        for anon_name, original_name in reverse_mapping.items():
            restored = re.sub(
                rf'\b{re.escape(anon_name)}\b',
                original_name,
                restored
            )
        
        return restored
    
    def update_privacy_level(self, new_level: PrivacyLevel) -> None:
        """
        Update privacy protection level.
        
        Args:
            new_level: New privacy level
        """
        if new_level != self.privacy_level:
            self.privacy_level = new_level
            self.sanitizer = DataSanitizer(new_level)
            self.processing_cache.clear()  # Clear cache as rules changed
            
            logger.info(f"Updated privacy level to: {new_level.value}")
    
    def add_custom_sanitization_rule(
        self,
        name: str,
        pattern: str,
        replacement: str,
        description: str = ""
    ) -> None:
        """Add custom sanitization rule."""
        self.sanitizer.add_custom_pattern(name, pattern, replacement, description)
        self.processing_cache.clear()  # Clear cache as rules changed
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get privacy processor statistics."""
        return {
            "privacy_level": self.privacy_level.value,
            "sanitization_rules": len(self.sanitizer.rules),
            "cache_size": len(self.processing_cache),
            "anonymization_mappings": len(self.anonymization_map),
            "enabled_rules": [rule.name for rule in self.sanitizer.rules if rule.enabled]
        }
    
    async def cleanup(self) -> None:
        """Cleanup privacy processor."""
        self.processing_cache.clear()
        self.anonymization_map.clear()
        logger.info("Privacy-preserving processor cleaned up")
