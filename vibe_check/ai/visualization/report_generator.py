"""
File: vibe_check/ai/visualization/report_generator.py
Purpose: Comprehensive report generation system
Related Files: vibe_check/ai/visualization/
Dependencies: typing, asyncio, datetime, enum, dataclasses
"""

from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import json

from vibe_check.core.logging import get_logger
from typing import Any, Dict, List, Optional

logger = get_logger(__name__)


class ReportFormat(Enum):
    """Report output formats."""
    HTML = "html"
    PDF = "pdf"
    MARKDOWN = "markdown"
    JSON = "json"
    CSV = "csv"
    EXCEL = "excel"
    POWERPOINT = "powerpoint"
    WORD = "word"


class ReportStyle(Enum):
    """Report styling options."""
    EXECUTIVE = "executive"
    TECHNICAL = "technical"
    DASHBOARD = "dashboard"
    MINIMAL = "minimal"
    DETAILED = "detailed"
    PRESENTATION = "presentation"


@dataclass
class ReportSection:
    """Individual report section."""
    section_id: str
    title: str
    content_type: str  # "text", "chart", "table", "image", "code"
    content: Any
    order: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "id": self.section_id,
            "title": self.title,
            "contentType": self.content_type,
            "content": self.content,
            "order": self.order,
            "metadata": self.metadata
        }


@dataclass
class ReportTemplate:
    """Report template definition."""
    template_id: str
    name: str
    description: str
    style: ReportStyle
    sections: List[str] = field(default_factory=list)  # Section IDs in order
    header_template: str = ""
    footer_template: str = ""
    css_styles: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "id": self.template_id,
            "name": self.name,
            "description": self.description,
            "style": self.style.value,
            "sections": self.sections,
            "headerTemplate": self.header_template,
            "footerTemplate": self.footer_template,
            "cssStyles": self.css_styles
        }


@dataclass
class GeneratedReport:
    """Generated report result."""
    report_id: str
    title: str
    format_type: ReportFormat
    style: ReportStyle
    content: str
    sections: List[ReportSection] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    generated_at: datetime = field(default_factory=datetime.now)
    file_size: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "id": self.report_id,
            "title": self.title,
            "format": self.format_type.value,
            "style": self.style.value,
            "content": self.content,
            "sections": [section.to_dict() for section in self.sections],
            "metadata": self.metadata,
            "generatedAt": self.generated_at.isoformat(),
            "fileSize": self.file_size
        }


class ReportTemplateLibrary:
    """Library of report templates."""
    
    def __init__(self):
        self.templates = {
            "executive_summary": self._create_executive_template,
            "technical_analysis": self._create_technical_template,
            "dashboard_report": self._create_dashboard_template,
            "code_quality": self._create_quality_template,
            "productivity_report": self._create_productivity_template
        }
    
    def get_template(self, template_name: str) -> ReportTemplate:
        """Get report template by name."""
        if template_name in self.templates:
            return self.templates[template_name]()
        else:
            return self._create_default_template()
    
    def _create_executive_template(self) -> ReportTemplate:
        """Create executive summary template."""
        return ReportTemplate(
            template_id="executive_summary",
            name="Executive Summary",
            description="High-level executive summary report",
            style=ReportStyle.EXECUTIVE,
            sections=[
                "executive_overview",
                "key_metrics",
                "critical_issues",
                "recommendations",
                "next_steps"
            ],
            header_template="<h1>{title}</h1><p>Generated: {date}</p>",
            footer_template="<p>Confidential - {organization}</p>",
            css_styles="""
                body { font-family: 'Segoe UI', sans-serif; }
                .executive-summary { max-width: 800px; margin: 0 auto; }
                .metric-card { background: #f8f9fa; padding: 20px; margin: 10px 0; }
                .critical { color: #dc3545; font-weight: bold; }
            """
        )
    
    def _create_technical_template(self) -> ReportTemplate:
        """Create technical analysis template."""
        return ReportTemplate(
            template_id="technical_analysis",
            name="Technical Analysis",
            description="Detailed technical analysis report",
            style=ReportStyle.TECHNICAL,
            sections=[
                "technical_overview",
                "architecture_analysis",
                "code_metrics",
                "technical_debt",
                "security_analysis",
                "performance_analysis",
                "recommendations"
            ],
            css_styles="""
                body { font-family: 'Consolas', monospace; }
                .code-block { background: #f4f4f4; padding: 15px; border-left: 4px solid #007bff; }
                .metric-table { width: 100%; border-collapse: collapse; }
                .metric-table th, .metric-table td { border: 1px solid #ddd; padding: 8px; }
            """
        )
    
    def _create_dashboard_template(self) -> ReportTemplate:
        """Create dashboard report template."""
        return ReportTemplate(
            template_id="dashboard_report",
            name="Dashboard Report",
            description="Visual dashboard-style report",
            style=ReportStyle.DASHBOARD,
            sections=[
                "dashboard_overview",
                "key_visualizations",
                "trend_analysis",
                "alerts_notifications"
            ],
            css_styles="""
                body { font-family: Arial, sans-serif; }
                .dashboard-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
                .dashboard-card { background: white; border: 1px solid #ddd; border-radius: 8px; padding: 20px; }
                .chart-container { height: 300px; }
            """
        )
    
    def _create_quality_template(self) -> ReportTemplate:
        """Create code quality template."""
        return ReportTemplate(
            template_id="code_quality",
            name="Code Quality Report",
            description="Comprehensive code quality analysis",
            style=ReportStyle.DETAILED,
            sections=[
                "quality_overview",
                "complexity_analysis",
                "code_smells",
                "test_coverage",
                "maintainability",
                "improvement_plan"
            ]
        )
    
    def _create_productivity_template(self) -> ReportTemplate:
        """Create productivity report template."""
        return ReportTemplate(
            template_id="productivity_report",
            name="Productivity Analysis",
            description="Developer and team productivity analysis",
            style=ReportStyle.PRESENTATION,
            sections=[
                "productivity_summary",
                "individual_metrics",
                "team_comparison",
                "trend_analysis",
                "recommendations"
            ]
        )
    
    def _create_default_template(self) -> ReportTemplate:
        """Create default template."""
        return ReportTemplate(
            template_id="default",
            name="Default Report",
            description="Basic report template",
            style=ReportStyle.MINIMAL,
            sections=["overview", "analysis", "conclusions"]
        )


class ReportFormatter:
    """Formats reports in different output formats."""
    
    def __init__(self):
        self.formatters = {
            ReportFormat.HTML: self._format_html,
            ReportFormat.MARKDOWN: self._format_markdown,
            ReportFormat.JSON: self._format_json,
            ReportFormat.CSV: self._format_csv
        }
    
    def format_report(
        self,
        sections: List[ReportSection],
        template: ReportTemplate,
        format_type: ReportFormat,
        metadata: Dict[str, Any]
    ) -> str:
        """Format report in specified format."""
        if format_type in self.formatters:
            return self.formatters[format_type](sections, template, metadata)
        else:
            return self._format_html(sections, template, metadata)
    
    def _format_html(
        self,
        sections: List[ReportSection],
        template: ReportTemplate,
        metadata: Dict[str, Any]
    ) -> str:
        """Format report as HTML."""
        html = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{metadata.get('title', 'Report')}</title>
    <style>
        {template.css_styles}
    </style>
</head>
<body>
    <div class="report-container">
        {template.header_template.format(**metadata)}
        
        <div class="report-content">
"""
        
        # Sort sections by order
        sorted_sections = sorted(sections, key=lambda s: s.order)
        
        for section in sorted_sections:
            html += f"""
        <div class="report-section" id="{section.section_id}">
            <h2>{section.title}</h2>
            <div class="section-content">
"""
            
            if section.content_type == "text":
                html += f"<p>{section.content}</p>"
            elif section.content_type == "chart":
                html += f'<div class="chart-container">{section.content}</div>'
            elif section.content_type == "table":
                html += self._format_table_html(section.content)
            elif section.content_type == "code":
                html += f'<pre class="code-block"><code>{section.content}</code></pre>'
            else:
                html += f"<div>{section.content}</div>"
            
            html += """
            </div>
        </div>
"""
        
        html += f"""
        </div>
        
        {template.footer_template.format(**metadata)}
    </div>
</body>
</html>
"""
        
        return html
    
    def _format_markdown(
        self,
        sections: List[ReportSection],
        template: ReportTemplate,
        metadata: Dict[str, Any]
    ) -> str:
        """Format report as Markdown."""
        md = f"# {metadata.get('title', 'Report')}\\n\\n"
        md += f"Generated: {metadata.get('generated_at', datetime.now().isoformat())}\\n\\n"
        
        # Sort sections by order
        sorted_sections = sorted(sections, key=lambda s: s.order)
        
        for section in sorted_sections:
            md += f"## {section.title}\\n\\n"
            
            if section.content_type == "text":
                md += f"{section.content}\\n\\n"
            elif section.content_type == "table":
                md += self._format_table_markdown(section.content)
            elif section.content_type == "code":
                md += f"```\\n{section.content}\\n```\\n\\n"
            else:
                md += f"{section.content}\\n\\n"
        
        return md
    
    def _format_json(
        self,
        sections: List[ReportSection],
        template: ReportTemplate,
        metadata: Dict[str, Any]
    ) -> str:
        """Format report as JSON."""
        report_data = {
            "metadata": metadata,
            "template": template.to_dict(),
            "sections": [section.to_dict() for section in sections]
        }
        return json.dumps(report_data, indent=2)
    
    def _format_csv(
        self,
        sections: List[ReportSection],
        template: ReportTemplate,
        metadata: Dict[str, Any]
    ) -> str:
        """Format report as CSV (simplified)."""
        csv_lines = ["Section,Title,Content Type,Content"]
        
        for section in sections:
            content = str(section.content).replace('"', '""').replace('\\n', ' ')
            csv_lines.append(f'"{section.section_id}","{section.title}","{section.content_type}","{content}"')
        
        return "\\n".join(csv_lines)
    
    def _format_table_html(self, table_data: Any) -> str:
        """Format table data as HTML."""
        if isinstance(table_data, dict) and "headers" in table_data and "rows" in table_data:
            html = '<table class="data-table">'
            html += "<thead><tr>"
            for header in table_data["headers"]:
                html += f"<th>{header}</th>"
            html += "</tr></thead><tbody>"
            
            for row in table_data["rows"]:
                html += "<tr>"
                for cell in row:
                    html += f"<td>{cell}</td>"
                html += "</tr>"
            
            html += "</tbody></table>"
            return html
        else:
            return f"<div>{table_data}</div>"
    
    def _format_table_markdown(self, table_data: Any) -> str:
        """Format table data as Markdown."""
        if isinstance(table_data, dict) and "headers" in table_data and "rows" in table_data:
            md = "| " + " | ".join(table_data["headers"]) + " |\\n"
            md += "| " + " | ".join(["---"] * len(table_data["headers"])) + " |\\n"
            
            for row in table_data["rows"]:
                md += "| " + " | ".join(str(cell) for cell in row) + " |\\n"
            
            md += "\\n"
            return md
        else:
            return f"{table_data}\\n\\n"


class ReportGenerator:
    """Comprehensive report generation system."""
    
    def __init__(self):
        """Initialize report generator."""
        self.template_library = ReportTemplateLibrary()
        self.formatter = ReportFormatter()
        self.report_cache: Dict[str, GeneratedReport] = {}
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize report generator."""
        if self._initialized:
            return
        
        self._initialized = True
        logger.info("Report generator initialized")
    
    async def generate_report(
        self,
        title: str,
        data: Dict[str, Any],
        template_name: str = "default",
        format_type: ReportFormat = ReportFormat.HTML,
        custom_sections: Optional[List[Dict[str, Any]]] = None
    ) -> GeneratedReport:
        """
        Generate a comprehensive report.
        
        Args:
            title: Report title
            data: Data to include in report
            template_name: Template to use
            format_type: Output format
            custom_sections: Custom sections to include
            
        Returns:
            Generated report
        """
        # Get template
        template = self.template_library.get_template(template_name)
        
        # Generate report ID
        import hashlib
        report_id = hashlib.md5(f"{title}{template_name}{datetime.now().isoformat()}".encode()).hexdigest()[:16]
        
        # Create sections
        sections = []
        
        if custom_sections:
            # Use custom sections
            for i, section_data in enumerate(custom_sections):
                section = ReportSection(
                    section_id=section_data.get("id", f"section_{i}"),
                    title=section_data.get("title", f"Section {i+1}"),
                    content_type=section_data.get("content_type", "text"),
                    content=section_data.get("content", ""),
                    order=section_data.get("order", i),
                    metadata=section_data.get("metadata", {})
                )
                sections.append(section)
        else:
            # Generate sections from template and data
            sections = await self._generate_sections_from_template(template, data)
        
        # Prepare metadata
        metadata = {
            "title": title,
            "generated_at": datetime.now().isoformat(),
            "template": template_name,
            "format": format_type.value,
            "organization": data.get("organization", "Vibe Check"),
            "date": datetime.now().strftime("%Y-%m-%d")
        }
        
        # Format report
        content = self.formatter.format_report(sections, template, format_type, metadata)
        
        # Create report
        report = GeneratedReport(
            report_id=report_id,
            title=title,
            format_type=format_type,
            style=template.style,
            content=content,
            sections=sections,
            metadata=metadata,
            file_size=len(content.encode('utf-8'))
        )
        
        # Cache report
        self.report_cache[report_id] = report
        
        logger.info(f"Generated report: {title} ({format_type.value}, {len(content)} chars)")
        return report
    
    async def _generate_sections_from_template(
        self,
        template: ReportTemplate,
        data: Dict[str, Any]
    ) -> List[ReportSection]:
        """Generate sections based on template and data."""
        sections = []
        
        for i, section_id in enumerate(template.sections):
            if section_id == "executive_overview":
                section = ReportSection(
                    section_id=section_id,
                    title="Executive Overview",
                    content_type="text",
                    content=self._generate_executive_overview(data),
                    order=i
                )
            elif section_id == "key_metrics":
                section = ReportSection(
                    section_id=section_id,
                    title="Key Metrics",
                    content_type="table",
                    content=self._generate_metrics_table(data),
                    order=i
                )
            elif section_id == "technical_overview":
                section = ReportSection(
                    section_id=section_id,
                    title="Technical Overview",
                    content_type="text",
                    content=self._generate_technical_overview(data),
                    order=i
                )
            elif section_id == "recommendations":
                section = ReportSection(
                    section_id=section_id,
                    title="Recommendations",
                    content_type="text",
                    content=self._generate_recommendations(data),
                    order=i
                )
            else:
                # Generic section
                section = ReportSection(
                    section_id=section_id,
                    title=section_id.replace("_", " ").title(),
                    content_type="text",
                    content=f"Analysis for {section_id} would be generated here based on available data.",
                    order=i
                )
            
            sections.append(section)
        
        return sections
    
    def _generate_executive_overview(self, data: Dict[str, Any]) -> str:
        """Generate executive overview content."""
        overview = "This report provides a comprehensive analysis of the codebase quality, technical debt, and development productivity. "
        
        if "quality_score" in data:
            overview += f"The overall code quality score is {data['quality_score']:.1f}/100. "
        
        if "debt_hours" in data:
            overview += f"Technical debt is estimated at {data['debt_hours']:.1f} hours. "
        
        if "productivity_score" in data:
            overview += f"Team productivity score is {data['productivity_score']:.1f}/100. "
        
        overview += "Key findings and recommendations are detailed in the following sections."
        
        return overview
    
    def _generate_metrics_table(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate metrics table."""
        metrics = []
        
        if "quality_score" in data:
            metrics.append(["Code Quality", f"{data['quality_score']:.1f}/100", "Good" if data['quality_score'] > 70 else "Needs Improvement"])
        
        if "debt_hours" in data:
            metrics.append(["Technical Debt", f"{data['debt_hours']:.1f} hours", "High" if data['debt_hours'] > 50 else "Moderate"])
        
        if "test_coverage" in data:
            metrics.append(["Test Coverage", f"{data['test_coverage']:.1%}", "Good" if data['test_coverage'] > 0.8 else "Low"])
        
        if "productivity_score" in data:
            metrics.append(["Productivity", f"{data['productivity_score']:.1f}/100", "High" if data['productivity_score'] > 80 else "Moderate"])
        
        return {
            "headers": ["Metric", "Value", "Status"],
            "rows": metrics
        }
    
    def _generate_technical_overview(self, data: Dict[str, Any]) -> str:
        """Generate technical overview content."""
        overview = "Technical analysis reveals the following key aspects of the codebase: "
        
        if "complexity_score" in data:
            overview += f"Average complexity score is {data['complexity_score']:.2f}. "
        
        if "code_smells" in data:
            overview += f"Detected {data['code_smells']} code smells requiring attention. "
        
        if "security_issues" in data:
            overview += f"Found {data['security_issues']} potential security issues. "
        
        overview += "Detailed analysis and recommendations follow."
        
        return overview
    
    def _generate_recommendations(self, data: Dict[str, Any]) -> str:
        """Generate recommendations content."""
        recommendations = []
        
        if data.get("quality_score", 100) < 70:
            recommendations.append("Improve code quality through refactoring and code reviews.")
        
        if data.get("debt_hours", 0) > 50:
            recommendations.append("Address technical debt systematically to prevent accumulation.")
        
        if data.get("test_coverage", 1.0) < 0.8:
            recommendations.append("Increase test coverage to improve code reliability.")
        
        if data.get("productivity_score", 100) < 80:
            recommendations.append("Implement productivity improvement measures and remove blockers.")
        
        if not recommendations:
            recommendations.append("Continue current practices and monitor key metrics.")
        
        return " ".join(f"{i+1}. {rec}" for i, rec in enumerate(recommendations))
    
    async def get_report(self, report_id: str) -> Optional[GeneratedReport]:
        """Get report by ID."""
        return self.report_cache.get(report_id)
    
    async def list_reports(self) -> List[GeneratedReport]:
        """List all generated reports."""
        return list(self.report_cache.values())
    
    async def export_report(self, report_id: str, file_path: str) -> bool:
        """Export report to file."""
        report = await self.get_report(report_id)
        if not report:
            return False
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(report.content)
            logger.info(f"Exported report to: {file_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to export report: {e}")
            return False
    
    def get_generator_statistics(self) -> Dict[str, Any]:
        """Get report generator statistics."""
        total_reports = len(self.report_cache)
        
        if total_reports == 0:
            return {
                "total_reports": 0,
                "reports_by_format": {},
                "reports_by_style": {},
                "total_file_size": 0
            }
        
        # Count by format and style
        reports_by_format = {}
        reports_by_style = {}
        total_file_size = 0
        
        for report in self.report_cache.values():
            format_type = report.format_type.value
            style = report.style.value
            
            reports_by_format[format_type] = reports_by_format.get(format_type, 0) + 1
            reports_by_style[style] = reports_by_style.get(style, 0) + 1
            total_file_size += report.file_size
        
        return {
            "total_reports": total_reports,
            "reports_by_format": reports_by_format,
            "reports_by_style": reports_by_style,
            "total_file_size": total_file_size,
            "cache_size": len(self.report_cache)
        }
    
    async def cleanup(self) -> None:
        """Cleanup report generator."""
        self.report_cache.clear()
        logger.info("Report generator cleaned up")
