"""
File: vibe_check/ai/visualization/dashboard_engine.py
Purpose: Advanced dashboard creation and management engine
Related Files: vibe_check/ai/visualization/
Dependencies: typing, asyncio, datetime, enum, dataclasses
"""

from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import json

from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class DashboardType(Enum):
    """Types of dashboards."""
    OVERVIEW = "overview"
    TECHNICAL_DEBT = "technical_debt"
    PRODUCTIVITY = "productivity"
    CODE_QUALITY = "code_quality"
    EVOLUTION = "evolution"
    REFACTORING = "refactoring"
    SECURITY = "security"
    PERFORMANCE = "performance"
    CUSTOM = "custom"


class LayoutType(Enum):
    """Dashboard layout types."""
    GRID = "grid"
    MASONRY = "masonry"
    TABS = "tabs"
    SIDEBAR = "sidebar"
    FULLSCREEN = "fullscreen"
    RESPONSIVE = "responsive"


@dataclass
class DashboardWidget:
    """Individual dashboard widget."""
    widget_id: str
    title: str
    widget_type: str  # "chart", "metric", "table", "text"
    data_source: str
    configuration: Dict[str, Any] = field(default_factory=dict)
    position: Dict[str, int] = field(default_factory=dict)  # x, y, width, height
    refresh_interval: int = 0  # seconds, 0 = no auto-refresh
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "id": self.widget_id,
            "title": self.title,
            "type": self.widget_type,
            "dataSource": self.data_source,
            "configuration": self.configuration,
            "position": self.position,
            "refreshInterval": self.refresh_interval
        }


@dataclass
class DashboardLayout:
    """Dashboard layout configuration."""
    layout_id: str
    layout_type: LayoutType
    grid_columns: int = 12
    grid_rows: int = 8
    responsive_breakpoints: Dict[str, int] = field(default_factory=dict)
    theme: str = "default"
    custom_css: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "id": self.layout_id,
            "type": self.layout_type.value,
            "gridColumns": self.grid_columns,
            "gridRows": self.grid_rows,
            "responsiveBreakpoints": self.responsive_breakpoints,
            "theme": self.theme,
            "customCss": self.custom_css
        }


@dataclass
class Dashboard:
    """Complete dashboard definition."""
    dashboard_id: str
    title: str
    description: str
    dashboard_type: DashboardType
    layout: DashboardLayout
    widgets: List[DashboardWidget] = field(default_factory=list)
    filters: Dict[str, Any] = field(default_factory=dict)
    permissions: Dict[str, List[str]] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "id": self.dashboard_id,
            "title": self.title,
            "description": self.description,
            "type": self.dashboard_type.value,
            "layout": self.layout.to_dict(),
            "widgets": [widget.to_dict() for widget in self.widgets],
            "filters": self.filters,
            "permissions": self.permissions,
            "createdAt": self.created_at.isoformat(),
            "updatedAt": self.updated_at.isoformat()
        }
    
    def to_json(self) -> str:
        """Convert to JSON string."""
        return json.dumps(self.to_dict(), indent=2)


class DashboardTemplateLibrary:
    """Library of pre-built dashboard templates."""
    
    def __init__(self):
        self.templates = {
            DashboardType.OVERVIEW: self._create_overview_template,
            DashboardType.TECHNICAL_DEBT: self._create_debt_template,
            DashboardType.PRODUCTIVITY: self._create_productivity_template,
            DashboardType.CODE_QUALITY: self._create_quality_template,
            DashboardType.EVOLUTION: self._create_evolution_template,
            DashboardType.REFACTORING: self._create_refactoring_template
        }
    
    def get_template(self, dashboard_type: DashboardType) -> Dashboard:
        """Get a pre-built dashboard template."""
        if dashboard_type in self.templates:
            return self.templates[dashboard_type]()
        else:
            return self._create_custom_template()
    
    def _create_overview_template(self) -> Dashboard:
        """Create overview dashboard template."""
        layout = DashboardLayout(
            layout_id="overview_layout",
            layout_type=LayoutType.GRID,
            grid_columns=12,
            grid_rows=8,
            responsive_breakpoints={"sm": 576, "md": 768, "lg": 992, "xl": 1200}
        )
        
        widgets = [
            DashboardWidget(
                widget_id="overview_metrics",
                title="Key Metrics",
                widget_type="metric",
                data_source="aggregated_metrics",
                position={"x": 0, "y": 0, "width": 12, "height": 2},
                configuration={
                    "metrics": ["code_quality", "technical_debt", "productivity", "test_coverage"],
                    "display": "cards"
                }
            ),
            DashboardWidget(
                widget_id="quality_trend",
                title="Code Quality Trend",
                widget_type="chart",
                data_source="quality_history",
                position={"x": 0, "y": 2, "width": 6, "height": 3},
                configuration={
                    "chartType": "line",
                    "timeRange": "30d",
                    "showTrendLine": True
                }
            ),
            DashboardWidget(
                widget_id="debt_breakdown",
                title="Technical Debt Breakdown",
                widget_type="chart",
                data_source="debt_categories",
                position={"x": 6, "y": 2, "width": 6, "height": 3},
                configuration={
                    "chartType": "pie",
                    "showLabels": True,
                    "showPercentages": True
                }
            ),
            DashboardWidget(
                widget_id="recent_issues",
                title="Recent Issues",
                widget_type="table",
                data_source="code_issues",
                position={"x": 0, "y": 5, "width": 12, "height": 3},
                configuration={
                    "columns": ["severity", "type", "location", "description", "date"],
                    "sortBy": "date",
                    "pageSize": 10
                }
            )
        ]
        
        return Dashboard(
            dashboard_id="overview_dashboard",
            title="Code Analysis Overview",
            description="Comprehensive overview of code analysis results",
            dashboard_type=DashboardType.OVERVIEW,
            layout=layout,
            widgets=widgets
        )
    
    def _create_debt_template(self) -> Dashboard:
        """Create technical debt dashboard template."""
        layout = DashboardLayout(
            layout_id="debt_layout",
            layout_type=LayoutType.GRID,
            grid_columns=12,
            grid_rows=10
        )
        
        widgets = [
            DashboardWidget(
                widget_id="debt_summary",
                title="Technical Debt Summary",
                widget_type="metric",
                data_source="debt_metrics",
                position={"x": 0, "y": 0, "width": 12, "height": 2},
                configuration={
                    "metrics": ["total_debt_hours", "debt_growth_rate", "priority_items", "estimated_cost"],
                    "display": "large_numbers"
                }
            ),
            DashboardWidget(
                widget_id="debt_by_category",
                title="Debt by Category",
                widget_type="chart",
                data_source="debt_categories",
                position={"x": 0, "y": 2, "width": 6, "height": 4},
                configuration={
                    "chartType": "bar",
                    "orientation": "horizontal",
                    "showValues": True
                }
            ),
            DashboardWidget(
                widget_id="debt_predictions",
                title="Debt Growth Predictions",
                widget_type="chart",
                data_source="debt_predictions",
                position={"x": 6, "y": 2, "width": 6, "height": 4},
                configuration={
                    "chartType": "line",
                    "showPredictions": True,
                    "confidenceInterval": True
                }
            ),
            DashboardWidget(
                widget_id="debt_items",
                title="High Priority Debt Items",
                widget_type="table",
                data_source="debt_items",
                position={"x": 0, "y": 6, "width": 12, "height": 4},
                configuration={
                    "columns": ["severity", "category", "title", "estimated_hours", "interest_rate"],
                    "filter": {"severity": ["high", "critical"]},
                    "sortBy": "estimated_hours"
                }
            )
        ]
        
        return Dashboard(
            dashboard_id="debt_dashboard",
            title="Technical Debt Analysis",
            description="Detailed technical debt tracking and predictions",
            dashboard_type=DashboardType.TECHNICAL_DEBT,
            layout=layout,
            widgets=widgets
        )
    
    def _create_productivity_template(self) -> Dashboard:
        """Create productivity dashboard template."""
        layout = DashboardLayout(
            layout_id="productivity_layout",
            layout_type=LayoutType.TABS,
            grid_columns=12,
            grid_rows=8
        )
        
        widgets = [
            DashboardWidget(
                widget_id="productivity_score",
                title="Overall Productivity Score",
                widget_type="metric",
                data_source="productivity_metrics",
                position={"x": 0, "y": 0, "width": 3, "height": 2},
                configuration={
                    "display": "gauge",
                    "min": 0,
                    "max": 100,
                    "thresholds": [30, 70, 90]
                }
            ),
            DashboardWidget(
                widget_id="productivity_trends",
                title="Productivity Trends",
                widget_type="chart",
                data_source="productivity_history",
                position={"x": 3, "y": 0, "width": 9, "height": 4},
                configuration={
                    "chartType": "multi_line",
                    "metrics": ["code_output", "quality", "velocity"],
                    "timeRange": "90d"
                }
            ),
            DashboardWidget(
                widget_id="team_comparison",
                title="Team Productivity Comparison",
                widget_type="chart",
                data_source="team_metrics",
                position={"x": 0, "y": 4, "width": 12, "height": 4},
                configuration={
                    "chartType": "radar",
                    "metrics": ["output", "quality", "collaboration", "efficiency"],
                    "entities": ["team_a", "team_b", "team_c"]
                }
            )
        ]
        
        return Dashboard(
            dashboard_id="productivity_dashboard",
            title="Developer Productivity",
            description="Comprehensive productivity analysis and team comparisons",
            dashboard_type=DashboardType.PRODUCTIVITY,
            layout=layout,
            widgets=widgets
        )
    
    def _create_quality_template(self) -> Dashboard:
        """Create code quality dashboard template."""
        layout = DashboardLayout(
            layout_id="quality_layout",
            layout_type=LayoutType.GRID
        )
        
        widgets = [
            DashboardWidget(
                widget_id="quality_metrics",
                title="Quality Metrics",
                widget_type="metric",
                data_source="quality_scores",
                position={"x": 0, "y": 0, "width": 12, "height": 2}
            ),
            DashboardWidget(
                widget_id="complexity_heatmap",
                title="Code Complexity Heatmap",
                widget_type="chart",
                data_source="complexity_data",
                position={"x": 0, "y": 2, "width": 8, "height": 4},
                configuration={"chartType": "heatmap"}
            ),
            DashboardWidget(
                widget_id="smell_distribution",
                title="Code Smell Distribution",
                widget_type="chart",
                data_source="code_smells",
                position={"x": 8, "y": 2, "width": 4, "height": 4},
                configuration={"chartType": "donut"}
            )
        ]
        
        return Dashboard(
            dashboard_id="quality_dashboard",
            title="Code Quality Analysis",
            description="Detailed code quality metrics and analysis",
            dashboard_type=DashboardType.CODE_QUALITY,
            layout=layout,
            widgets=widgets
        )
    
    def _create_evolution_template(self) -> Dashboard:
        """Create code evolution dashboard template."""
        layout = DashboardLayout(
            layout_id="evolution_layout",
            layout_type=LayoutType.FULLSCREEN
        )
        
        widgets = [
            DashboardWidget(
                widget_id="evolution_timeline",
                title="Code Evolution Timeline",
                widget_type="chart",
                data_source="evolution_data",
                position={"x": 0, "y": 0, "width": 12, "height": 6},
                configuration={
                    "chartType": "timeline",
                    "showEvents": True,
                    "interactive": True
                }
            ),
            DashboardWidget(
                widget_id="growth_metrics",
                title="Growth Metrics",
                widget_type="chart",
                data_source="growth_data",
                position={"x": 0, "y": 6, "width": 12, "height": 2},
                configuration={"chartType": "area_stack"}
            )
        ]
        
        return Dashboard(
            dashboard_id="evolution_dashboard",
            title="Code Evolution Analysis",
            description="Historical code evolution and growth patterns",
            dashboard_type=DashboardType.EVOLUTION,
            layout=layout,
            widgets=widgets
        )
    
    def _create_refactoring_template(self) -> Dashboard:
        """Create refactoring dashboard template."""
        layout = DashboardLayout(
            layout_id="refactoring_layout",
            layout_type=LayoutType.SIDEBAR
        )
        
        widgets = [
            DashboardWidget(
                widget_id="refactoring_opportunities",
                title="Refactoring Opportunities",
                widget_type="table",
                data_source="refactoring_suggestions",
                position={"x": 0, "y": 0, "width": 8, "height": 4}
            ),
            DashboardWidget(
                widget_id="impact_analysis",
                title="Impact Analysis",
                widget_type="chart",
                data_source="impact_data",
                position={"x": 8, "y": 0, "width": 4, "height": 4},
                configuration={"chartType": "bubble"}
            ),
            DashboardWidget(
                widget_id="pattern_recommendations",
                title="Design Pattern Recommendations",
                widget_type="table",
                data_source="pattern_data",
                position={"x": 0, "y": 4, "width": 12, "height": 4}
            )
        ]
        
        return Dashboard(
            dashboard_id="refactoring_dashboard",
            title="Refactoring Analysis",
            description="Refactoring opportunities and impact analysis",
            dashboard_type=DashboardType.REFACTORING,
            layout=layout,
            widgets=widgets
        )
    
    def _create_custom_template(self) -> Dashboard:
        """Create custom dashboard template."""
        layout = DashboardLayout(
            layout_id="custom_layout",
            layout_type=LayoutType.RESPONSIVE
        )
        
        return Dashboard(
            dashboard_id="custom_dashboard",
            title="Custom Dashboard",
            description="Customizable dashboard template",
            dashboard_type=DashboardType.CUSTOM,
            layout=layout
        )


class DashboardEngine:
    """Advanced dashboard creation and management engine."""
    
    def __init__(self):
        """Initialize dashboard engine."""
        self.template_library = DashboardTemplateLibrary()
        self.dashboard_cache: Dict[str, Dashboard] = {}
        self.data_sources: Dict[str, Any] = {}
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize dashboard engine."""
        if self._initialized:
            return
        
        # Initialize default data sources
        self.data_sources = {
            "aggregated_metrics": {},
            "quality_history": [],
            "debt_categories": {},
            "code_issues": [],
            "debt_metrics": {},
            "debt_predictions": [],
            "debt_items": [],
            "productivity_metrics": {},
            "productivity_history": [],
            "team_metrics": {},
            "quality_scores": {},
            "complexity_data": {},
            "code_smells": {},
            "evolution_data": [],
            "growth_data": [],
            "refactoring_suggestions": [],
            "impact_data": [],
            "pattern_data": []
        }
        
        self._initialized = True
        logger.info("Dashboard engine initialized")
    
    async def create_dashboard(
        self,
        dashboard_type: DashboardType,
        title: Optional[str] = None,
        description: Optional[str] = None,
        custom_config: Optional[Dict[str, Any]] = None
    ) -> Dashboard:
        """
        Create a new dashboard from template.
        
        Args:
            dashboard_type: Type of dashboard to create
            title: Custom title (optional)
            description: Custom description (optional)
            custom_config: Custom configuration overrides
            
        Returns:
            Created dashboard
        """
        # Get template
        dashboard = self.template_library.get_template(dashboard_type)
        
        # Apply customizations
        if title:
            dashboard.title = title
        if description:
            dashboard.description = description
        
        if custom_config:
            # Apply custom configuration
            if "layout" in custom_config:
                for key, value in custom_config["layout"].items():
                    setattr(dashboard.layout, key, value)
            
            if "widgets" in custom_config:
                # Modify widget configurations
                for widget_config in custom_config["widgets"]:
                    widget_id = widget_config.get("id")
                    for widget in dashboard.widgets:
                        if widget.widget_id == widget_id:
                            widget.configuration.update(widget_config.get("configuration", {}))
        
        # Update timestamps
        dashboard.created_at = datetime.now()
        dashboard.updated_at = datetime.now()
        
        # Cache dashboard
        self.dashboard_cache[dashboard.dashboard_id] = dashboard
        
        logger.info(f"Created dashboard: {dashboard.title} ({dashboard_type.value})")
        return dashboard
    
    async def update_dashboard(
        self,
        dashboard_id: str,
        updates: Dict[str, Any]
    ) -> Optional[Dashboard]:
        """
        Update an existing dashboard.
        
        Args:
            dashboard_id: ID of dashboard to update
            updates: Dictionary of updates to apply
            
        Returns:
            Updated dashboard or None if not found
        """
        if dashboard_id not in self.dashboard_cache:
            logger.warning(f"Dashboard not found: {dashboard_id}")
            return None
        
        dashboard = self.dashboard_cache[dashboard_id]
        
        # Apply updates
        if "title" in updates:
            dashboard.title = updates["title"]
        if "description" in updates:
            dashboard.description = updates["description"]
        if "widgets" in updates:
            # Update widgets
            for widget_update in updates["widgets"]:
                widget_id = widget_update.get("id")
                for widget in dashboard.widgets:
                    if widget.widget_id == widget_id:
                        for key, value in widget_update.items():
                            if key != "id":
                                setattr(widget, key, value)
        
        dashboard.updated_at = datetime.now()
        
        logger.info(f"Updated dashboard: {dashboard_id}")
        return dashboard
    
    async def get_dashboard(self, dashboard_id: str) -> Optional[Dashboard]:
        """Get dashboard by ID."""
        return self.dashboard_cache.get(dashboard_id)
    
    async def list_dashboards(self) -> List[Dashboard]:
        """List all dashboards."""
        return list(self.dashboard_cache.values())
    
    async def delete_dashboard(self, dashboard_id: str) -> bool:
        """Delete dashboard by ID."""
        if dashboard_id in self.dashboard_cache:
            del self.dashboard_cache[dashboard_id]
            logger.info(f"Deleted dashboard: {dashboard_id}")
            return True
        return False
    
    def register_data_source(self, source_name: str, data: Any) -> None:
        """Register a data source for dashboards."""
        self.data_sources[source_name] = data
        logger.debug(f"Registered data source: {source_name}")
    
    def get_data_source(self, source_name: str) -> Any:
        """Get data from a registered source."""
        return self.data_sources.get(source_name)
    
    async def render_dashboard(self, dashboard_id: str, format_type: str = "json") -> str:
        """
        Render dashboard in specified format.
        
        Args:
            dashboard_id: ID of dashboard to render
            format_type: Output format ("json", "html", "react")
            
        Returns:
            Rendered dashboard as string
        """
        dashboard = await self.get_dashboard(dashboard_id)
        if not dashboard:
            raise ValueError(f"Dashboard not found: {dashboard_id}")
        
        if format_type == "json":
            return dashboard.to_json()
        
        elif format_type == "html":
            return self._render_html_dashboard(dashboard)
        
        elif format_type == "react":
            return self._render_react_dashboard(dashboard)
        
        else:
            return dashboard.to_json()
    
    def _render_html_dashboard(self, dashboard: Dashboard) -> str:
        """Render dashboard as HTML."""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>{dashboard.title}</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; }}
                .dashboard {{ max-width: 1200px; margin: 0 auto; }}
                .dashboard-header {{ margin-bottom: 30px; }}
                .dashboard-grid {{ display: grid; grid-template-columns: repeat({dashboard.layout.grid_columns}, 1fr); gap: 20px; }}
                .widget {{ background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; }}
                .widget-title {{ font-size: 18px; font-weight: bold; margin-bottom: 15px; }}
                .metric {{ text-align: center; font-size: 24px; color: #007bff; }}
                {dashboard.layout.custom_css}
            </style>
        </head>
        <body>
            <div class="dashboard">
                <div class="dashboard-header">
                    <h1>{dashboard.title}</h1>
                    <p>{dashboard.description}</p>
                </div>
                <div class="dashboard-grid">
        """
        
        for widget in dashboard.widgets:
            widget_style = f"grid-column: span {widget.position.get('width', 6)}; grid-row: span {widget.position.get('height', 2)};"
            html += f"""
                    <div class="widget" style="{widget_style}">
                        <div class="widget-title">{widget.title}</div>
                        <div class="widget-content">
                            <div class="metric">[{widget.widget_type.upper()} - {widget.data_source}]</div>
                        </div>
                    </div>
            """
        
        html += """
                </div>
            </div>
            <script>
                // Dashboard interactivity would go here
                console.log('Dashboard loaded:', """ + dashboard.to_json() + """);
            </script>
        </body>
        </html>
        """
        
        return html
    
    def _render_react_dashboard(self, dashboard: Dashboard) -> str:
        """Render dashboard as React component."""
        return f"""
import React from 'react';

const Dashboard = () => {{
    const dashboardData = {dashboard.to_json()};
    
    return (
        <div className="dashboard">
            <div className="dashboard-header">
                <h1>{{dashboardData.title}}</h1>
                <p>{{dashboardData.description}}</p>
            </div>
            <div className="dashboard-grid">
                {{dashboardData.widgets.map(widget => (
                    <div key={{widget.id}} className="widget">
                        <h3>{{widget.title}}</h3>
                        <div>[{{widget.type.toUpperCase()}} - {{widget.dataSource}}]</div>
                    </div>
                ))}}
            </div>
        </div>
    );
}};

export default Dashboard;
"""
    
    def get_engine_statistics(self) -> Dict[str, Any]:
        """Get dashboard engine statistics."""
        total_dashboards = len(self.dashboard_cache)
        
        if total_dashboards == 0:
            return {
                "total_dashboards": 0,
                "dashboards_by_type": {},
                "total_widgets": 0,
                "data_sources": 0
            }
        
        # Count by type
        dashboards_by_type = {}
        total_widgets = 0
        
        for dashboard in self.dashboard_cache.values():
            dash_type = dashboard.dashboard_type.value
            dashboards_by_type[dash_type] = dashboards_by_type.get(dash_type, 0) + 1
            total_widgets += len(dashboard.widgets)
        
        return {
            "total_dashboards": total_dashboards,
            "dashboards_by_type": dashboards_by_type,
            "total_widgets": total_widgets,
            "data_sources": len(self.data_sources),
            "cache_size": len(self.dashboard_cache)
        }
    
    async def cleanup(self) -> None:
        """Cleanup dashboard engine."""
        self.dashboard_cache.clear()
        self.data_sources.clear()
        logger.info("Dashboard engine cleaned up")
