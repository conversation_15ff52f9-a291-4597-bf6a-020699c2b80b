"""
File: vibe_check/ai/visualization/data_aggregator.py
Purpose: Data aggregation and filtering for visualizations
Related Files: vibe_check/ai/visualization/
Dependencies: typing, asyncio, datetime, enum, dataclasses
"""

from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import statistics

from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class AggregationType(Enum):
    """Types of data aggregation."""
    SUM = "sum"
    AVERAGE = "average"
    MEDIAN = "median"
    MIN = "min"
    MAX = "max"
    COUNT = "count"
    DISTINCT_COUNT = "distinct_count"
    PERCENTILE = "percentile"
    STANDARD_DEVIATION = "std_dev"
    VARIANCE = "variance"
    FIRST = "first"
    LAST = "last"
    CONCAT = "concat"
    GROUP_BY = "group_by"


@dataclass
class FilterCriteria:
    """Data filtering criteria."""
    field: str
    operator: str  # "eq", "ne", "gt", "gte", "lt", "lte", "in", "not_in", "contains", "regex"
    value: Any
    case_sensitive: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "field": self.field,
            "operator": self.operator,
            "value": self.value,
            "caseSensitive": self.case_sensitive
        }


@dataclass
class AggregationRule:
    """Data aggregation rule."""
    rule_id: str
    source_field: str
    aggregation_type: AggregationType
    target_field: str
    group_by_fields: List[str] = field(default_factory=list)
    filters: List[FilterCriteria] = field(default_factory=list)
    parameters: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "id": self.rule_id,
            "sourceField": self.source_field,
            "aggregationType": self.aggregation_type.value,
            "targetField": self.target_field,
            "groupByFields": self.group_by_fields,
            "filters": [f.to_dict() for f in self.filters],
            "parameters": self.parameters
        }


@dataclass
class AggregationResult:
    """Result of data aggregation."""
    result_id: str
    source_data_count: int
    aggregated_data: Dict[str, Any]
    metadata: Dict[str, Any] = field(default_factory=dict)
    processing_time_ms: float = 0.0
    generated_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "id": self.result_id,
            "sourceDataCount": self.source_data_count,
            "aggregatedData": self.aggregated_data,
            "metadata": self.metadata,
            "processingTimeMs": self.processing_time_ms,
            "generatedAt": self.generated_at.isoformat()
        }


class DataFilter:
    """Data filtering utilities."""
    
    def __init__(self):
        self.operators = {
            "eq": lambda x, y: x == y,
            "ne": lambda x, y: x != y,
            "gt": lambda x, y: x > y,
            "gte": lambda x, y: x >= y,
            "lt": lambda x, y: x < y,
            "lte": lambda x, y: x <= y,
            "in": lambda x, y: x in y,
            "not_in": lambda x, y: x not in y,
            "contains": lambda x, y: str(y).lower() in str(x).lower(),
            "regex": self._regex_match
        }
    
    def apply_filters(self, data: List[Dict[str, Any]], filters: List[FilterCriteria]) -> List[Dict[str, Any]]:
        """Apply filters to data."""
        filtered_data = data
        
        for filter_criteria in filters:
            filtered_data = self._apply_single_filter(filtered_data, filter_criteria)
        
        return filtered_data
    
    def _apply_single_filter(self, data: List[Dict[str, Any]], criteria: FilterCriteria) -> List[Dict[str, Any]]:
        """Apply a single filter criteria."""
        if criteria.operator not in self.operators:
            logger.warning(f"Unknown filter operator: {criteria.operator}")
            return data
        
        operator_func = self.operators[criteria.operator]
        filtered = []
        
        for item in data:
            if criteria.field not in item:
                continue
            
            field_value = item[criteria.field]
            
            # Handle case sensitivity for string operations
            if not criteria.case_sensitive and isinstance(field_value, str) and isinstance(criteria.value, str):
                field_value = field_value.lower()
                test_value = criteria.value.lower()
            else:
                test_value = criteria.value
            
            try:
                if operator_func(field_value, test_value):
                    filtered.append(item)
            except Exception as e:
                logger.debug(f"Filter evaluation error: {e}")
                continue
        
        return filtered
    
    def _regex_match(self, field_value: Any, pattern: str) -> bool:
        """Regex matching for filter."""
        import re
        try:
            return bool(re.search(pattern, str(field_value)))
        except re.error:
            return False


class DataAggregator:
    """Data aggregation and filtering for visualizations."""
    
    def __init__(self):
        """Initialize data aggregator."""
        self.data_filter = DataFilter()
        self.aggregation_cache: Dict[str, AggregationResult] = {}
        self.custom_aggregators: Dict[str, Callable] = {}
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize data aggregator."""
        if self._initialized:
            return
        
        # Register default custom aggregators
        self.custom_aggregators = {
            "weighted_average": self._weighted_average,
            "moving_average": self._moving_average,
            "growth_rate": self._growth_rate,
            "trend_direction": self._trend_direction
        }
        
        self._initialized = True
        logger.info("Data aggregator initialized")
    
    async def aggregate_data(
        self,
        data: List[Dict[str, Any]],
        aggregation_rules: List[AggregationRule],
        cache_key: Optional[str] = None
    ) -> AggregationResult:
        """
        Aggregate data according to specified rules.
        
        Args:
            data: Source data to aggregate
            aggregation_rules: List of aggregation rules to apply
            cache_key: Optional cache key for result
            
        Returns:
            Aggregation result
        """
        start_time = datetime.now()
        
        # Generate cache key if not provided
        if not cache_key:
            import hashlib
            cache_key = hashlib.md5(
                f"{len(data)}{len(aggregation_rules)}{start_time.isoformat()}".encode()
            ).hexdigest()[:16]
        
        # Check cache
        if cache_key in self.aggregation_cache:
            logger.debug("Using cached aggregation result")
            return self.aggregation_cache[cache_key]
        
        try:
            aggregated_data = {}
            
            for rule in aggregation_rules:
                # Apply filters first
                filtered_data = self.data_filter.apply_filters(data, rule.filters)
                
                # Apply aggregation
                if rule.group_by_fields:
                    # Group by aggregation
                    grouped_result = await self._aggregate_with_grouping(filtered_data, rule)
                    aggregated_data[rule.target_field] = grouped_result
                else:
                    # Simple aggregation
                    aggregated_value = await self._aggregate_simple(filtered_data, rule)
                    aggregated_data[rule.target_field] = aggregated_value
            
            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            
            # Create result
            result = AggregationResult(
                result_id=cache_key,
                source_data_count=len(data),
                aggregated_data=aggregated_data,
                metadata={
                    "rules_applied": len(aggregation_rules),
                    "filters_applied": sum(len(rule.filters) for rule in aggregation_rules)
                },
                processing_time_ms=processing_time
            )
            
            # Cache result
            self.aggregation_cache[cache_key] = result
            
            logger.info(f"Aggregated {len(data)} records in {processing_time:.2f}ms")
            return result
            
        except Exception as e:
            logger.error(f"Data aggregation failed: {e}")
            # Return empty result on error
            return AggregationResult(
                result_id=cache_key,
                source_data_count=len(data),
                aggregated_data={},
                metadata={"error": str(e)}
            )
    
    async def _aggregate_simple(self, data: List[Dict[str, Any]], rule: AggregationRule) -> Any:
        """Apply simple aggregation without grouping."""
        if not data:
            return None
        
        # Extract values for the source field
        values = []
        for item in data:
            if rule.source_field in item:
                value = item[rule.source_field]
                if value is not None:
                    values.append(value)
        
        if not values:
            return None
        
        # Apply aggregation
        if rule.aggregation_type == AggregationType.SUM:
            return sum(values)
        elif rule.aggregation_type == AggregationType.AVERAGE:
            return statistics.mean(values)
        elif rule.aggregation_type == AggregationType.MEDIAN:
            return statistics.median(values)
        elif rule.aggregation_type == AggregationType.MIN:
            return min(values)
        elif rule.aggregation_type == AggregationType.MAX:
            return max(values)
        elif rule.aggregation_type == AggregationType.COUNT:
            return len(values)
        elif rule.aggregation_type == AggregationType.DISTINCT_COUNT:
            return len(set(values))
        elif rule.aggregation_type == AggregationType.PERCENTILE:
            percentile = rule.parameters.get("percentile", 50)
            return self._calculate_percentile(values, percentile)
        elif rule.aggregation_type == AggregationType.STANDARD_DEVIATION:
            return statistics.stdev(values) if len(values) > 1 else 0
        elif rule.aggregation_type == AggregationType.VARIANCE:
            return statistics.variance(values) if len(values) > 1 else 0
        elif rule.aggregation_type == AggregationType.FIRST:
            return values[0]
        elif rule.aggregation_type == AggregationType.LAST:
            return values[-1]
        elif rule.aggregation_type == AggregationType.CONCAT:
            separator = rule.parameters.get("separator", ", ")
            return separator.join(str(v) for v in values)
        else:
            # Check for custom aggregators
            custom_name = rule.aggregation_type.value
            if custom_name in self.custom_aggregators:
                return self.custom_aggregators[custom_name](values, rule.parameters)
            else:
                logger.warning(f"Unknown aggregation type: {rule.aggregation_type}")
                return None
    
    async def _aggregate_with_grouping(self, data: List[Dict[str, Any]], rule: AggregationRule) -> Dict[str, Any]:
        """Apply aggregation with grouping."""
        # Group data by specified fields
        groups = {}
        
        for item in data:
            # Create group key
            group_key_parts = []
            for field in rule.group_by_fields:
                if field in item:
                    group_key_parts.append(str(item[field]))
                else:
                    group_key_parts.append("null")
            
            group_key = "|".join(group_key_parts)
            
            if group_key not in groups:
                groups[group_key] = []
            groups[group_key].append(item)
        
        # Apply aggregation to each group
        grouped_results = {}
        for group_key, group_data in groups.items():
            # Create a temporary rule for this group
            temp_rule = AggregationRule(
                rule_id=f"{rule.rule_id}_group",
                source_field=rule.source_field,
                aggregation_type=rule.aggregation_type,
                target_field=rule.target_field,
                parameters=rule.parameters
            )
            
            group_result = await self._aggregate_simple(group_data, temp_rule)
            grouped_results[group_key] = group_result
        
        return grouped_results
    
    def _calculate_percentile(self, values: List[float], percentile: float) -> float:
        """Calculate percentile of values."""
        if not values:
            return 0.0
        
        sorted_values = sorted(values)
        k = (len(sorted_values) - 1) * (percentile / 100.0)
        f = int(k)
        c = k - f
        
        if f + 1 < len(sorted_values):
            return sorted_values[f] + c * (sorted_values[f + 1] - sorted_values[f])
        else:
            return sorted_values[f]
    
    def _weighted_average(self, values: List[Any], parameters: Dict[str, Any]) -> float:
        """Calculate weighted average."""
        weights = parameters.get("weights", [1] * len(values))
        
        if len(values) != len(weights):
            logger.warning("Values and weights length mismatch")
            return statistics.mean(values) if values else 0.0
        
        weighted_sum = sum(v * w for v, w in zip(values, weights))
        total_weight = sum(weights)
        
        return weighted_sum / total_weight if total_weight > 0 else 0.0
    
    def _moving_average(self, values: List[float], parameters: Dict[str, Any]) -> List[float]:
        """Calculate moving average."""
        window_size = parameters.get("window_size", 3)
        
        if len(values) < window_size:
            return values
        
        moving_averages = []
        for i in range(len(values) - window_size + 1):
            window = values[i:i + window_size]
            moving_averages.append(statistics.mean(window))
        
        return moving_averages
    
    def _growth_rate(self, values: List[float], parameters: Dict[str, Any]) -> float:
        """Calculate growth rate."""
        if len(values) < 2:
            return 0.0
        
        start_value = values[0]
        end_value = values[-1]
        
        if start_value == 0:
            return float('inf') if end_value > 0 else 0.0
        
        return ((end_value - start_value) / start_value) * 100
    
    def _trend_direction(self, values: List[float], parameters: Dict[str, Any]) -> str:
        """Determine trend direction."""
        if len(values) < 2:
            return "stable"
        
        # Simple linear regression slope
        n = len(values)
        x_values = list(range(n))
        
        sum_x = sum(x_values)
        sum_y = sum(values)
        sum_xy = sum(x * y for x, y in zip(x_values, values))
        sum_x2 = sum(x * x for x in x_values)
        
        denominator = n * sum_x2 - sum_x * sum_x
        if denominator == 0:
            return "stable"
        
        slope = (n * sum_xy - sum_x * sum_y) / denominator
        
        threshold = parameters.get("threshold", 0.01)
        
        if slope > threshold:
            return "increasing"
        elif slope < -threshold:
            return "decreasing"
        else:
            return "stable"
    
    async def create_time_series_aggregation(
        self,
        data: List[Dict[str, Any]],
        time_field: str,
        value_field: str,
        interval: str = "daily",
        aggregation_type: AggregationType = AggregationType.AVERAGE
    ) -> Dict[str, Any]:
        """
        Create time series aggregation.
        
        Args:
            data: Source data with time and value fields
            time_field: Field containing timestamp
            value_field: Field containing values to aggregate
            interval: Time interval (daily, weekly, monthly)
            aggregation_type: Type of aggregation to apply
            
        Returns:
            Time series aggregation result
        """
        # Group data by time intervals
        time_groups = {}
        
        for item in data:
            if time_field not in item or value_field not in item:
                continue
            
            timestamp = item[time_field]
            if isinstance(timestamp, str):
                timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            
            # Create time bucket based on interval
            if interval == "daily":
                bucket = timestamp.date().isoformat()
            elif interval == "weekly":
                # Start of week (Monday)
                start_of_week = timestamp - timedelta(days=timestamp.weekday())
                bucket = start_of_week.date().isoformat()
            elif interval == "monthly":
                bucket = f"{timestamp.year}-{timestamp.month:02d}"
            else:
                bucket = timestamp.date().isoformat()
            
            if bucket not in time_groups:
                time_groups[bucket] = []
            time_groups[bucket].append(item[value_field])
        
        # Aggregate each time bucket
        time_series = {}
        for bucket, values in time_groups.items():
            if aggregation_type == AggregationType.SUM:
                time_series[bucket] = sum(values)
            elif aggregation_type == AggregationType.AVERAGE:
                time_series[bucket] = statistics.mean(values)
            elif aggregation_type == AggregationType.COUNT:
                time_series[bucket] = len(values)
            else:
                time_series[bucket] = values[0] if values else 0
        
        return {
            "time_series": time_series,
            "interval": interval,
            "aggregation_type": aggregation_type.value,
            "total_buckets": len(time_series)
        }
    
    def register_custom_aggregator(self, name: str, aggregator_func: Callable) -> None:
        """Register a custom aggregation function."""
        self.custom_aggregators[name] = aggregator_func
        logger.debug(f"Registered custom aggregator: {name}")
    
    async def clear_cache(self) -> None:
        """Clear aggregation cache."""
        self.aggregation_cache.clear()
        logger.debug("Aggregation cache cleared")
    
    def get_aggregator_statistics(self) -> Dict[str, Any]:
        """Get data aggregator statistics."""
        total_results = len(self.aggregation_cache)
        
        if total_results == 0:
            return {
                "total_results": 0,
                "avg_processing_time": 0.0,
                "total_records_processed": 0,
                "custom_aggregators": 0
            }
        
        total_processing_time = sum(result.processing_time_ms for result in self.aggregation_cache.values())
        total_records = sum(result.source_data_count for result in self.aggregation_cache.values())
        
        return {
            "total_results": total_results,
            "avg_processing_time": total_processing_time / total_results,
            "total_records_processed": total_records,
            "custom_aggregators": len(self.custom_aggregators),
            "cache_size": len(self.aggregation_cache)
        }
    
    async def cleanup(self) -> None:
        """Cleanup data aggregator."""
        self.aggregation_cache.clear()
        self.custom_aggregators.clear()
        logger.info("Data aggregator cleaned up")
