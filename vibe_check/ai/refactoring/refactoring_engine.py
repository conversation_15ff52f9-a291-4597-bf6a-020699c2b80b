"""
File: vibe_check/ai/refactoring/refactoring_engine.py
Purpose: AI-powered refactoring suggestion engine
Related Files: vibe_check/ai/refactoring/
Dependencies: typing, asyncio, ast, enum, dataclasses
"""

import ast
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime

from vibe_check.ai.infrastructure import AIModelManager
from vibe_check.core.logging import get_logger
from typing import Any, Dict, List, Optional

logger = get_logger(__name__)


class RefactoringType(Enum):
    """Types of refactoring suggestions."""
    EXTRACT_METHOD = "extract_method"
    EXTRACT_CLASS = "extract_class"
    RENAME_VARIABLE = "rename_variable"
    RENAME_METHOD = "rename_method"
    SIMPLIFY_CONDITIONAL = "simplify_conditional"
    REMOVE_DUPLICATION = "remove_duplication"
    IMPROVE_NAMING = "improve_naming"
    REDUCE_COMPLEXITY = "reduce_complexity"
    OPTIMIZE_IMPORTS = "optimize_imports"
    ADD_TYPE_HINTS = "add_type_hints"
    SPLIT_LARGE_FUNCTION = "split_large_function"
    CONSOLIDATE_PARAMETERS = "consolidate_parameters"


class RefactoringSeverity(Enum):
    """Severity levels for refactoring suggestions."""
    LOW = "low"           # Nice to have improvements
    MEDIUM = "medium"     # Recommended improvements
    HIGH = "high"         # Important improvements
    CRITICAL = "critical" # Critical improvements needed


@dataclass
class RefactoringSuggestion:
    """A single refactoring suggestion."""
    suggestion_id: str
    refactoring_type: RefactoringType
    severity: RefactoringSeverity
    title: str
    description: str
    original_code: str
    suggested_code: str
    line_start: int
    line_end: int
    confidence_score: float
    benefits: List[str] = field(default_factory=list)
    risks: List[str] = field(default_factory=list)
    effort_estimate: str = "medium"  # low, medium, high
    automated: bool = False  # Can be applied automatically
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "suggestion_id": self.suggestion_id,
            "refactoring_type": self.refactoring_type.value,
            "severity": self.severity.value,
            "title": self.title,
            "description": self.description,
            "original_code": self.original_code,
            "suggested_code": self.suggested_code,
            "line_start": self.line_start,
            "line_end": self.line_end,
            "confidence_score": self.confidence_score,
            "benefits": self.benefits,
            "risks": self.risks,
            "effort_estimate": self.effort_estimate,
            "automated": self.automated
        }


@dataclass
class RefactoringReport:
    """Comprehensive refactoring analysis report."""
    report_id: str
    source_code: str
    total_suggestions: int
    suggestions_by_type: Dict[str, int] = field(default_factory=dict)
    suggestions_by_severity: Dict[str, int] = field(default_factory=dict)
    overall_complexity_score: float = 0.0
    maintainability_score: float = 0.0
    suggestions: List[RefactoringSuggestion] = field(default_factory=list)
    summary: str = ""
    generated_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "report_id": self.report_id,
            "source_code": self.source_code[:200] + "..." if len(self.source_code) > 200 else self.source_code,
            "total_suggestions": self.total_suggestions,
            "suggestions_by_type": self.suggestions_by_type,
            "suggestions_by_severity": self.suggestions_by_severity,
            "overall_complexity_score": self.overall_complexity_score,
            "maintainability_score": self.maintainability_score,
            "suggestions": [suggestion.to_dict() for suggestion in self.suggestions],
            "summary": self.summary,
            "generated_at": self.generated_at.isoformat()
        }


class CodeAnalyzer:
    """Analyzes code structure for refactoring opportunities."""
    
    def __init__(self):
        self.complexity_thresholds = {
            "function_length": 20,
            "function_parameters": 5,
            "cyclomatic_complexity": 10,
            "nesting_depth": 4
        }
    
    def analyze_code_structure(self, code: str) -> Dict[str, Any]:
        """Analyze code structure for refactoring opportunities."""
        try:
            tree = ast.parse(code)
            analysis = {
                "functions": [],
                "classes": [],
                "complexity_issues": [],
                "naming_issues": [],
                "structure_issues": []
            }
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    func_analysis = self._analyze_function(node, code)
                    analysis["functions"].append(func_analysis)
                    
                    # Check for complexity issues
                    if func_analysis["line_count"] > self.complexity_thresholds["function_length"]:
                        analysis["complexity_issues"].append({
                            "type": "long_function",
                            "function": func_analysis["name"],
                            "line_count": func_analysis["line_count"],
                            "line_number": node.lineno
                        })
                    
                    if func_analysis["parameter_count"] > self.complexity_thresholds["function_parameters"]:
                        analysis["complexity_issues"].append({
                            "type": "too_many_parameters",
                            "function": func_analysis["name"],
                            "parameter_count": func_analysis["parameter_count"],
                            "line_number": node.lineno
                        })
                    
                    # Check naming conventions
                    if not self._is_snake_case(func_analysis["name"]):
                        analysis["naming_issues"].append({
                            "type": "function_naming",
                            "name": func_analysis["name"],
                            "line_number": node.lineno
                        })
                
                elif isinstance(node, ast.ClassDef):
                    class_analysis = self._analyze_class(node, code)
                    analysis["classes"].append(class_analysis)
                    
                    # Check naming conventions
                    if not self._is_pascal_case(class_analysis["name"]):
                        analysis["naming_issues"].append({
                            "type": "class_naming",
                            "name": class_analysis["name"],
                            "line_number": node.lineno
                        })
            
            return analysis
            
        except SyntaxError as e:
            logger.warning(f"Failed to parse code for refactoring analysis: {e}")
            return {"error": "Invalid Python syntax"}
        except Exception as e:
            logger.error(f"Code refactoring analysis failed: {e}")
            return {"error": str(e)}
    
    def _analyze_function(self, func_node: ast.FunctionDef, code: str) -> Dict[str, Any]:
        """Analyze a single function."""
        lines = code.split('\n')
        func_lines = lines[func_node.lineno - 1:func_node.end_lineno] if hasattr(func_node, 'end_lineno') else []
        
        return {
            "name": func_node.name,
            "line_number": func_node.lineno,
            "line_count": len(func_lines),
            "parameter_count": len(func_node.args.args),
            "has_docstring": ast.get_docstring(func_node) is not None,
            "has_type_hints": any(arg.annotation for arg in func_node.args.args),
            "has_return_annotation": func_node.returns is not None,
            "is_async": isinstance(func_node, ast.AsyncFunctionDef),
            "decorators": len(func_node.decorator_list),
            "complexity_score": self._calculate_cyclomatic_complexity(func_node)
        }
    
    def _analyze_class(self, class_node: ast.ClassDef, code: str) -> Dict[str, Any]:
        """Analyze a single class."""
        methods = [node for node in class_node.body if isinstance(node, ast.FunctionDef)]
        
        return {
            "name": class_node.name,
            "line_number": class_node.lineno,
            "method_count": len(methods),
            "has_docstring": ast.get_docstring(class_node) is not None,
            "has_init": any(method.name == "__init__" for method in methods),
            "inheritance": len(class_node.bases),
            "decorators": len(class_node.decorator_list)
        }
    
    def _calculate_cyclomatic_complexity(self, func_node: ast.FunctionDef) -> int:
        """Calculate cyclomatic complexity of a function."""
        complexity = 1  # Base complexity
        
        for node in ast.walk(func_node):
            if isinstance(node, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(node, ast.ExceptHandler):
                complexity += 1
            elif isinstance(node, ast.BoolOp):
                complexity += len(node.values) - 1
        
        return complexity
    
    def _is_snake_case(self, name: str) -> bool:
        """Check if name follows snake_case convention."""
        return name.islower() and '_' in name or name.islower()
    
    def _is_pascal_case(self, name: str) -> bool:
        """Check if name follows PascalCase convention."""
        return name[0].isupper() and not '_' in name


class RefactoringSuggestionEngine:
    """AI-powered refactoring suggestion engine."""
    
    def __init__(self, model_manager: Optional[AIModelManager] = None):
        """
        Initialize refactoring suggestion engine.
        
        Args:
            model_manager: AI model manager instance
        """
        self.model_manager = model_manager
        self.code_analyzer = CodeAnalyzer()
        self.suggestion_cache: Dict[str, RefactoringReport] = {}
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize refactoring suggestion engine."""
        if self._initialized:
            return
        
        if not self.model_manager:
            self.model_manager = AIModelManager()
            await self.model_manager.initialize()
        
        self._initialized = True
        logger.info("Refactoring suggestion engine initialized")
    
    async def analyze_and_suggest(self, code: str) -> RefactoringReport:
        """
        Analyze code and generate refactoring suggestions.
        
        Args:
            code: Source code to analyze
            
        Returns:
            Comprehensive refactoring report
        """
        # Generate cache key
        import hashlib
        cache_key = hashlib.md5(code.encode()).hexdigest()
        
        if cache_key in self.suggestion_cache:
            logger.debug("Using cached refactoring suggestions")
            return self.suggestion_cache[cache_key]
        
        try:
            # Analyze code structure
            analysis = self.code_analyzer.analyze_code_structure(code)
            
            if "error" in analysis:
                raise ValueError(f"Code analysis failed: {analysis['error']}")
            
            # Generate refactoring suggestions
            suggestions = []
            
            # Function-level suggestions
            for func in analysis.get("functions", []):
                func_suggestions = await self._generate_function_suggestions(func, code)
                suggestions.extend(func_suggestions)
            
            # Class-level suggestions
            for cls in analysis.get("classes", []):
                class_suggestions = await self._generate_class_suggestions(cls, code)
                suggestions.extend(class_suggestions)
            
            # Complexity-based suggestions
            complexity_suggestions = await self._generate_complexity_suggestions(
                analysis.get("complexity_issues", []), code
            )
            suggestions.extend(complexity_suggestions)
            
            # Naming-based suggestions
            naming_suggestions = await self._generate_naming_suggestions(
                analysis.get("naming_issues", []), code
            )
            suggestions.extend(naming_suggestions)
            
            # Calculate statistics
            total_suggestions = len(suggestions)
            suggestions_by_type = {}
            suggestions_by_severity = {}
            
            for suggestion in suggestions:
                sug_type = suggestion.refactoring_type.value
                severity = suggestion.severity.value
                
                suggestions_by_type[sug_type] = suggestions_by_type.get(sug_type, 0) + 1
                suggestions_by_severity[severity] = suggestions_by_severity.get(severity, 0) + 1
            
            # Calculate scores
            complexity_score = self._calculate_complexity_score(analysis)
            maintainability_score = self._calculate_maintainability_score(suggestions)
            
            # Generate summary
            summary = await self._generate_summary(suggestions, analysis)
            
            # Create report
            report = RefactoringReport(
                report_id=cache_key,
                source_code=code,
                total_suggestions=total_suggestions,
                suggestions_by_type=suggestions_by_type,
                suggestions_by_severity=suggestions_by_severity,
                overall_complexity_score=complexity_score,
                maintainability_score=maintainability_score,
                suggestions=suggestions,
                summary=summary
            )
            
            # Cache result
            self.suggestion_cache[cache_key] = report
            
            logger.info(f"Generated {total_suggestions} refactoring suggestions")
            return report
            
        except Exception as e:
            logger.error(f"Failed to generate refactoring suggestions: {e}")
            # Return empty report on error
            return RefactoringReport(
                report_id=cache_key,
                source_code=code,
                total_suggestions=0,
                summary=f"Analysis failed: {str(e)}"
            )
    
    async def _generate_function_suggestions(self, func_analysis: Dict[str, Any], code: str) -> List[RefactoringSuggestion]:
        """Generate function-level refactoring suggestions."""
        suggestions = []
        
        # Long function suggestion
        if func_analysis["line_count"] > 20:
            suggestions.append(RefactoringSuggestion(
                suggestion_id=f"func_{func_analysis['name']}_split",
                refactoring_type=RefactoringType.SPLIT_LARGE_FUNCTION,
                severity=RefactoringSeverity.MEDIUM,
                title=f"Split large function '{func_analysis['name']}'",
                description=f"Function has {func_analysis['line_count']} lines. Consider breaking it into smaller functions.",
                original_code=f"def {func_analysis['name']}(...):",
                suggested_code="# Split into multiple smaller functions",
                line_start=func_analysis["line_number"],
                line_end=func_analysis["line_number"] + func_analysis["line_count"],
                confidence_score=0.8,
                benefits=["Improved readability", "Better testability", "Easier maintenance"],
                risks=["May increase complexity if over-split"],
                effort_estimate="medium"
            ))
        
        # Too many parameters suggestion
        if func_analysis["parameter_count"] > 5:
            suggestions.append(RefactoringSuggestion(
                suggestion_id=f"func_{func_analysis['name']}_params",
                refactoring_type=RefactoringType.CONSOLIDATE_PARAMETERS,
                severity=RefactoringSeverity.MEDIUM,
                title=f"Consolidate parameters in '{func_analysis['name']}'",
                description=f"Function has {func_analysis['parameter_count']} parameters. Consider using a data class or dictionary.",
                original_code=f"def {func_analysis['name']}(param1, param2, ...):",
                suggested_code="def function_name(config: ConfigClass):",
                line_start=func_analysis["line_number"],
                line_end=func_analysis["line_number"],
                confidence_score=0.7,
                benefits=["Cleaner interface", "Better parameter validation", "Easier to extend"],
                risks=["May require changes to calling code"],
                effort_estimate="medium"
            ))
        
        # Missing type hints suggestion
        if not func_analysis["has_type_hints"]:
            suggestions.append(RefactoringSuggestion(
                suggestion_id=f"func_{func_analysis['name']}_types",
                refactoring_type=RefactoringType.ADD_TYPE_HINTS,
                severity=RefactoringSeverity.LOW,
                title=f"Add type hints to '{func_analysis['name']}'",
                description="Function lacks type hints. Adding them improves code clarity and IDE support.",
                original_code=f"def {func_analysis['name']}(param):",
                suggested_code=f"def {func_analysis['name']}(param: Type) -> ReturnType:",
                line_start=func_analysis["line_number"],
                line_end=func_analysis["line_number"],
                confidence_score=0.9,
                benefits=["Better IDE support", "Improved documentation", "Catch type errors early"],
                risks=["None"],
                effort_estimate="low",
                automated=True
            ))
        
        return suggestions
    
    async def _generate_class_suggestions(self, class_analysis: Dict[str, Any], code: str) -> List[RefactoringSuggestion]:
        """Generate class-level refactoring suggestions."""
        suggestions = []
        
        # Missing docstring suggestion
        if not class_analysis["has_docstring"]:
            suggestions.append(RefactoringSuggestion(
                suggestion_id=f"class_{class_analysis['name']}_docstring",
                refactoring_type=RefactoringType.IMPROVE_NAMING,
                severity=RefactoringSeverity.LOW,
                title=f"Add docstring to class '{class_analysis['name']}'",
                description="Class lacks documentation. Adding a docstring improves code understanding.",
                original_code=f"class {class_analysis['name']}:",
                suggested_code=f'class {class_analysis["name"]}:\n    """Class description."""',
                line_start=class_analysis["line_number"],
                line_end=class_analysis["line_number"],
                confidence_score=0.9,
                benefits=["Better documentation", "Improved code understanding"],
                risks=["None"],
                effort_estimate="low",
                automated=True
            ))
        
        return suggestions
    
    async def _generate_complexity_suggestions(self, complexity_issues: List[Dict[str, Any]], code: str) -> List[RefactoringSuggestion]:
        """Generate complexity-based suggestions."""
        suggestions = []
        
        for issue in complexity_issues:
            if issue["type"] == "long_function":
                suggestions.append(RefactoringSuggestion(
                    suggestion_id=f"complexity_{issue['function']}_length",
                    refactoring_type=RefactoringType.REDUCE_COMPLEXITY,
                    severity=RefactoringSeverity.HIGH,
                    title=f"Reduce complexity in '{issue['function']}'",
                    description=f"Function is too long ({issue['line_count']} lines). Consider extracting methods.",
                    original_code="# Long function",
                    suggested_code="# Extract methods to reduce complexity",
                    line_start=issue["line_number"],
                    line_end=issue["line_number"] + issue["line_count"],
                    confidence_score=0.8,
                    benefits=["Improved maintainability", "Better testability"],
                    risks=["May increase number of methods"],
                    effort_estimate="high"
                ))
        
        return suggestions
    
    async def _generate_naming_suggestions(self, naming_issues: List[Dict[str, Any]], code: str) -> List[RefactoringSuggestion]:
        """Generate naming-based suggestions."""
        suggestions = []
        
        for issue in naming_issues:
            if issue["type"] == "function_naming":
                suggestions.append(RefactoringSuggestion(
                    suggestion_id=f"naming_{issue['name']}_function",
                    refactoring_type=RefactoringType.RENAME_METHOD,
                    severity=RefactoringSeverity.LOW,
                    title=f"Rename function '{issue['name']}' to follow snake_case",
                    description="Function name should follow Python naming conventions (snake_case).",
                    original_code=f"def {issue['name']}():",
                    suggested_code=f"def {self._to_snake_case(issue['name'])}():",
                    line_start=issue["line_number"],
                    line_end=issue["line_number"],
                    confidence_score=0.9,
                    benefits=["Follows Python conventions", "Improved consistency"],
                    risks=["Requires updating all references"],
                    effort_estimate="low",
                    automated=True
                ))
        
        return suggestions
    
    def _to_snake_case(self, name: str) -> str:
        """Convert name to snake_case."""
        import re
        # Simple conversion - in practice would be more sophisticated
        return re.sub(r'(?<!^)(?=[A-Z])', '_', name).lower()
    
    def _calculate_complexity_score(self, analysis: Dict[str, Any]) -> float:
        """Calculate overall complexity score."""
        complexity_issues = len(analysis.get("complexity_issues", []))
        total_functions = len(analysis.get("functions", []))
        
        if total_functions == 0:
            return 0.0
        
        # Higher score means more complex (worse)
        return min(1.0, complexity_issues / total_functions)
    
    def _calculate_maintainability_score(self, suggestions: List[RefactoringSuggestion]) -> float:
        """Calculate maintainability score based on suggestions."""
        if not suggestions:
            return 1.0  # Perfect if no suggestions needed
        
        # Weight by severity
        severity_weights = {
            RefactoringSeverity.LOW: 0.1,
            RefactoringSeverity.MEDIUM: 0.3,
            RefactoringSeverity.HIGH: 0.6,
            RefactoringSeverity.CRITICAL: 1.0
        }
        
        total_weight = sum(severity_weights[s.severity] for s in suggestions)
        max_possible_weight = len(suggestions) * 1.0  # If all were critical
        
        # Return inverted score (1.0 = perfect maintainability)
        return max(0.0, 1.0 - (total_weight / max_possible_weight))
    
    async def _generate_summary(self, suggestions: List[RefactoringSuggestion], analysis: Dict[str, Any]) -> str:
        """Generate summary of refactoring analysis."""
        if not suggestions:
            return "Code analysis complete. No refactoring suggestions needed."
        
        critical_count = len([s for s in suggestions if s.severity == RefactoringSeverity.CRITICAL])
        high_count = len([s for s in suggestions if s.severity == RefactoringSeverity.HIGH])
        medium_count = len([s for s in suggestions if s.severity == RefactoringSeverity.MEDIUM])
        low_count = len([s for s in suggestions if s.severity == RefactoringSeverity.LOW])
        
        summary_parts = [f"Found {len(suggestions)} refactoring opportunities:"]
        
        if critical_count > 0:
            summary_parts.append(f"{critical_count} critical issues requiring immediate attention")
        if high_count > 0:
            summary_parts.append(f"{high_count} high-priority improvements")
        if medium_count > 0:
            summary_parts.append(f"{medium_count} medium-priority suggestions")
        if low_count > 0:
            summary_parts.append(f"{low_count} minor improvements")
        
        return ". ".join(summary_parts) + "."
    
    def get_refactoring_statistics(self) -> Dict[str, Any]:
        """Get refactoring engine statistics."""
        total_reports = len(self.suggestion_cache)
        
        if total_reports == 0:
            return {
                "total_reports": 0,
                "total_suggestions": 0,
                "avg_complexity_score": 0.0,
                "avg_maintainability_score": 0.0,
                "suggestions_by_type": {},
                "suggestions_by_severity": {}
            }
        
        total_suggestions = sum(report.total_suggestions for report in self.suggestion_cache.values())
        total_complexity = sum(report.overall_complexity_score for report in self.suggestion_cache.values())
        total_maintainability = sum(report.maintainability_score for report in self.suggestion_cache.values())
        
        # Aggregate suggestions by type and severity
        all_suggestions_by_type = {}
        all_suggestions_by_severity = {}
        
        for report in self.suggestion_cache.values():
            for sug_type, count in report.suggestions_by_type.items():
                all_suggestions_by_type[sug_type] = all_suggestions_by_type.get(sug_type, 0) + count
            
            for severity, count in report.suggestions_by_severity.items():
                all_suggestions_by_severity[severity] = all_suggestions_by_severity.get(severity, 0) + count
        
        return {
            "total_reports": total_reports,
            "total_suggestions": total_suggestions,
            "avg_complexity_score": total_complexity / total_reports,
            "avg_maintainability_score": total_maintainability / total_reports,
            "suggestions_by_type": all_suggestions_by_type,
            "suggestions_by_severity": all_suggestions_by_severity,
            "cache_size": len(self.suggestion_cache)
        }
    
    async def cleanup(self) -> None:
        """Cleanup refactoring suggestion engine."""
        self.suggestion_cache.clear()
        logger.info("Refactoring suggestion engine cleaned up")
