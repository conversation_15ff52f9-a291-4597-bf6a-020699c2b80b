"""
File: vibe_check/ai/refactoring/pattern_recommender.py
Purpose: Design pattern recommendation system
Related Files: vibe_check/ai/refactoring/
Dependencies: typing, ast, enum, dataclasses
"""

import ast
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime

from vibe_check.core.logging import get_logger
from typing import Any, Dict, List, Optional

logger = get_logger(__name__)


class DesignPattern(Enum):
    """Types of design patterns."""
    SINGLETON = "singleton"
    FACTORY = "factory"
    OBSERVER = "observer"
    STRATEGY = "strategy"
    DECORATOR = "decorator"
    ADAPTER = "adapter"
    FACADE = "facade"
    TEMPLATE_METHOD = "template_method"
    COMMAND = "command"
    STATE = "state"
    BUILDER = "builder"
    PROTOTYPE = "prototype"
    PROXY = "proxy"
    COMPOSITE = "composite"
    BRIDGE = "bridge"
    FLYWEIGHT = "flyweight"
    CHAIN_OF_RESPONSIBILITY = "chain_of_responsibility"
    MEDIATOR = "mediator"
    MEMENTO = "memento"
    VISITOR = "visitor"
    ITERATOR = "iterator"


class PatternCategory(Enum):
    """Categories of design patterns."""
    CREATIONAL = "creational"
    STRUCTURAL = "structural"
    BEHAVIORAL = "behavioral"


@dataclass
class PatternRecommendation:
    """A design pattern recommendation."""
    recommendation_id: str
    pattern: DesignPattern
    category: PatternCategory
    title: str
    description: str
    problem_addressed: str
    benefits: List[str] = field(default_factory=list)
    implementation_hints: List[str] = field(default_factory=list)
    example_code: str = ""
    confidence_score: float = 0.0
    applicable_location: str = ""
    line_number: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "recommendation_id": self.recommendation_id,
            "pattern": self.pattern.value,
            "category": self.category.value,
            "title": self.title,
            "description": self.description,
            "problem_addressed": self.problem_addressed,
            "benefits": self.benefits,
            "implementation_hints": self.implementation_hints,
            "example_code": self.example_code,
            "confidence_score": self.confidence_score,
            "applicable_location": self.applicable_location,
            "line_number": self.line_number
        }


@dataclass
class PatternAnalysisReport:
    """Design pattern analysis report."""
    report_id: str
    source_code: str
    total_recommendations: int
    recommendations_by_category: Dict[str, int] = field(default_factory=dict)
    recommendations_by_pattern: Dict[str, int] = field(default_factory=dict)
    design_quality_score: float = 0.0
    pattern_opportunities_score: float = 0.0
    recommendations: List[PatternRecommendation] = field(default_factory=list)
    summary: str = ""
    generated_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "report_id": self.report_id,
            "source_code": self.source_code[:200] + "..." if len(self.source_code) > 200 else self.source_code,
            "total_recommendations": self.total_recommendations,
            "recommendations_by_category": self.recommendations_by_category,
            "recommendations_by_pattern": self.recommendations_by_pattern,
            "design_quality_score": self.design_quality_score,
            "pattern_opportunities_score": self.pattern_opportunities_score,
            "recommendations": [rec.to_dict() for rec in self.recommendations],
            "summary": self.summary,
            "generated_at": self.generated_at.isoformat()
        }


class PatternDetectionRules:
    """Rules for detecting design pattern opportunities."""
    
    def __init__(self):
        self.pattern_definitions = {
            DesignPattern.SINGLETON: {
                "category": PatternCategory.CREATIONAL,
                "indicators": ["global_state", "single_instance", "class_variable"],
                "anti_patterns": ["multiple_instances", "inheritance"]
            },
            DesignPattern.FACTORY: {
                "category": PatternCategory.CREATIONAL,
                "indicators": ["object_creation", "conditional_creation", "type_switching"],
                "anti_patterns": ["direct_instantiation"]
            },
            DesignPattern.STRATEGY: {
                "category": PatternCategory.BEHAVIORAL,
                "indicators": ["algorithm_switching", "conditional_behavior", "if_elif_chains"],
                "anti_patterns": ["hardcoded_behavior"]
            },
            DesignPattern.OBSERVER: {
                "category": PatternCategory.BEHAVIORAL,
                "indicators": ["event_handling", "notification", "callback_lists"],
                "anti_patterns": ["tight_coupling"]
            },
            DesignPattern.DECORATOR: {
                "category": PatternCategory.STRUCTURAL,
                "indicators": ["behavior_extension", "wrapper_classes", "composition"],
                "anti_patterns": ["inheritance_chains"]
            }
        }
    
    def detect_singleton_opportunity(self, class_node: ast.ClassDef, code: str) -> Optional[PatternRecommendation]:
        """Detect singleton pattern opportunity."""
        # Look for global variables or class-level state management
        has_class_variables = any(
            isinstance(node, ast.Assign) and 
            any(isinstance(target, ast.Name) for target in node.targets)
            for node in class_node.body
        )
        
        # Check for methods that suggest single instance need
        method_names = [node.name for node in class_node.body if isinstance(node, ast.FunctionDef)]
        singleton_indicators = ['get_instance', 'instance', 'create_instance']
        
        has_singleton_methods = any(indicator in method_names for indicator in singleton_indicators)
        
        if has_class_variables or has_singleton_methods:
            return PatternRecommendation(
                recommendation_id=f"singleton_{class_node.name}_{class_node.lineno}",
                pattern=DesignPattern.SINGLETON,
                category=PatternCategory.CREATIONAL,
                title=f"Consider Singleton pattern for {class_node.name}",
                description="Class appears to manage global state or single instance access.",
                problem_addressed="Ensures only one instance exists and provides global access point",
                benefits=[
                    "Controlled access to single instance",
                    "Reduced memory footprint",
                    "Global access point"
                ],
                implementation_hints=[
                    "Implement __new__ method to control instantiation",
                    "Use class variable to store single instance",
                    "Consider thread safety for concurrent access"
                ],
                example_code="""
class Singleton:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
""",
                confidence_score=0.7,
                applicable_location=class_node.name,
                line_number=class_node.lineno
            )
        
        return None
    
    def detect_factory_opportunity(self, func_node: ast.FunctionDef, code: str) -> Optional[PatternRecommendation]:
        """Detect factory pattern opportunity."""
        # Look for conditional object creation
        has_conditional_creation = False
        
        for node in ast.walk(func_node):
            if isinstance(node, ast.If):
                # Check if the if statement contains object instantiation
                for child in ast.walk(node):
                    if isinstance(child, ast.Call) and isinstance(child.func, ast.Name):
                        has_conditional_creation = True
                        break
        
        if has_conditional_creation:
            return PatternRecommendation(
                recommendation_id=f"factory_{func_node.name}_{func_node.lineno}",
                pattern=DesignPattern.FACTORY,
                category=PatternCategory.CREATIONAL,
                title=f"Consider Factory pattern for {func_node.name}",
                description="Function contains conditional object creation logic.",
                problem_addressed="Encapsulates object creation logic and reduces coupling",
                benefits=[
                    "Centralized object creation",
                    "Easier to extend with new types",
                    "Reduced coupling between client and concrete classes"
                ],
                implementation_hints=[
                    "Create a factory class or method",
                    "Use a registry pattern for extensibility",
                    "Consider abstract factory for families of objects"
                ],
                example_code="""
class ShapeFactory:
    @staticmethod
    def create_shape(shape_type):
        if shape_type == "circle":
            return Circle()
        elif shape_type == "square":
            return Square()
        else:
            raise ValueError("Unknown shape type")
""",
                confidence_score=0.8,
                applicable_location=func_node.name,
                line_number=func_node.lineno
            )
        
        return None
    
    def detect_strategy_opportunity(self, func_node: ast.FunctionDef, code: str) -> Optional[PatternRecommendation]:
        """Detect strategy pattern opportunity."""
        # Look for long if-elif chains or switch-like behavior
        if_elif_count = 0
        
        for node in ast.walk(func_node):
            if isinstance(node, ast.If):
                current = node
                while current and hasattr(current, 'orelse') and current.orelse:
                    if_elif_count += 1
                    if isinstance(current.orelse[0], ast.If):
                        current = current.orelse[0]
                    else:
                        break
        
        if if_elif_count > 3:  # Threshold for strategy pattern recommendation
            return PatternRecommendation(
                recommendation_id=f"strategy_{func_node.name}_{func_node.lineno}",
                pattern=DesignPattern.STRATEGY,
                category=PatternCategory.BEHAVIORAL,
                title=f"Consider Strategy pattern for {func_node.name}",
                description=f"Function contains {if_elif_count} conditional branches that could be strategies.",
                problem_addressed="Eliminates conditional complexity and makes algorithms interchangeable",
                benefits=[
                    "Eliminates conditional statements",
                    "Makes algorithms interchangeable",
                    "Easier to add new strategies"
                ],
                implementation_hints=[
                    "Create a strategy interface",
                    "Implement concrete strategies for each condition",
                    "Use composition to select strategy at runtime"
                ],
                example_code="""
class PaymentStrategy:
    def pay(self, amount): pass

class CreditCardPayment(PaymentStrategy):
    def pay(self, amount):
        # Credit card payment logic
        pass

class PayPalPayment(PaymentStrategy):
    def pay(self, amount):
        # PayPal payment logic
        pass
""",
                confidence_score=0.9,
                applicable_location=func_node.name,
                line_number=func_node.lineno
            )
        
        return None
    
    def detect_observer_opportunity(self, class_node: ast.ClassDef, code: str) -> Optional[PatternRecommendation]:
        """Detect observer pattern opportunity."""
        # Look for callback lists, event handling, or notification methods
        method_names = [node.name for node in class_node.body if isinstance(node, ast.FunctionDef)]
        
        observer_indicators = [
            'notify', 'update', 'on_', 'handle_', 'callback', 'listener', 'subscribe', 'unsubscribe'
        ]
        
        has_observer_methods = any(
            any(indicator in method_name.lower() for indicator in observer_indicators)
            for method_name in method_names
        )
        
        # Look for list attributes that might store observers
        has_observer_lists = any(
            isinstance(node, ast.Assign) and
            any(isinstance(target, ast.Name) and 'listener' in target.id.lower() or 'observer' in target.id.lower()
                for target in node.targets if isinstance(target, ast.Name))
            for node in class_node.body
        )
        
        if has_observer_methods or has_observer_lists:
            return PatternRecommendation(
                recommendation_id=f"observer_{class_node.name}_{class_node.lineno}",
                pattern=DesignPattern.OBSERVER,
                category=PatternCategory.BEHAVIORAL,
                title=f"Consider Observer pattern for {class_node.name}",
                description="Class appears to handle events or notifications.",
                problem_addressed="Defines one-to-many dependency between objects",
                benefits=[
                    "Loose coupling between subject and observers",
                    "Dynamic relationships at runtime",
                    "Broadcast communication"
                ],
                implementation_hints=[
                    "Define observer interface",
                    "Maintain list of observers in subject",
                    "Implement subscribe/unsubscribe methods"
                ],
                example_code="""
class Subject:
    def __init__(self):
        self._observers = []
    
    def subscribe(self, observer):
        self._observers.append(observer)
    
    def notify(self, event):
        for observer in self._observers:
            observer.update(event)
""",
                confidence_score=0.8,
                applicable_location=class_node.name,
                line_number=class_node.lineno
            )
        
        return None
    
    def detect_decorator_opportunity(self, func_node: ast.FunctionDef, code: str) -> Optional[PatternRecommendation]:
        """Detect decorator pattern opportunity."""
        # Look for wrapper-like behavior or behavior extension
        has_decorators = len(func_node.decorator_list) > 0
        
        # Look for wrapper patterns in function body
        wrapper_indicators = ['wrapper', 'wrap', 'before', 'after', 'around']
        func_body_text = ast.get_source_segment(code, func_node) if hasattr(ast, 'get_source_segment') else ""
        
        has_wrapper_logic = any(indicator in func_body_text.lower() for indicator in wrapper_indicators)
        
        if has_decorators or has_wrapper_logic:
            return PatternRecommendation(
                recommendation_id=f"decorator_{func_node.name}_{func_node.lineno}",
                pattern=DesignPattern.DECORATOR,
                category=PatternCategory.STRUCTURAL,
                title=f"Consider Decorator pattern for {func_node.name}",
                description="Function shows decorator-like behavior or could benefit from decoration.",
                problem_addressed="Adds behavior to objects dynamically without altering structure",
                benefits=[
                    "Dynamic behavior addition",
                    "Alternative to subclassing",
                    "Composable behavior modifications"
                ],
                implementation_hints=[
                    "Create decorator base class",
                    "Implement concrete decorators",
                    "Use composition to wrap original object"
                ],
                example_code="""
class Component:
    def operation(self): pass

class Decorator(Component):
    def __init__(self, component):
        self._component = component
    
    def operation(self):
        return self._component.operation()

class ConcreteDecorator(Decorator):
    def operation(self):
        result = super().operation()
        # Add additional behavior
        return result
""",
                confidence_score=0.7,
                applicable_location=func_node.name,
                line_number=func_node.lineno
            )
        
        return None


class DesignPatternRecommender:
    """Design pattern recommendation system."""
    
    def __init__(self):
        """Initialize design pattern recommender."""
        self.detection_rules = PatternDetectionRules()
        self.recommendation_cache: Dict[str, PatternAnalysisReport] = {}
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize design pattern recommender."""
        if self._initialized:
            return
        
        self._initialized = True
        logger.info("Design pattern recommender initialized")
    
    async def analyze_patterns(self, code: str) -> PatternAnalysisReport:
        """
        Analyze code and recommend design patterns.
        
        Args:
            code: Source code to analyze
            
        Returns:
            Pattern analysis report with recommendations
        """
        # Generate cache key
        import hashlib
        cache_key = hashlib.md5(code.encode()).hexdigest()
        
        if cache_key in self.recommendation_cache:
            logger.debug("Using cached pattern recommendations")
            return self.recommendation_cache[cache_key]
        
        try:
            # Parse code
            tree = ast.parse(code)
            recommendations = []
            
            # Analyze functions and classes for pattern opportunities
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    # Check for various pattern opportunities
                    singleton_rec = self.detection_rules.detect_singleton_opportunity(node, code)
                    if singleton_rec:
                        recommendations.append(singleton_rec)
                    
                    observer_rec = self.detection_rules.detect_observer_opportunity(node, code)
                    if observer_rec:
                        recommendations.append(observer_rec)
                
                elif isinstance(node, ast.FunctionDef):
                    factory_rec = self.detection_rules.detect_factory_opportunity(node, code)
                    if factory_rec:
                        recommendations.append(factory_rec)
                    
                    strategy_rec = self.detection_rules.detect_strategy_opportunity(node, code)
                    if strategy_rec:
                        recommendations.append(strategy_rec)
                    
                    decorator_rec = self.detection_rules.detect_decorator_opportunity(node, code)
                    if decorator_rec:
                        recommendations.append(decorator_rec)
            
            # Calculate statistics
            total_recommendations = len(recommendations)
            recommendations_by_category = {}
            recommendations_by_pattern = {}
            
            for rec in recommendations:
                category = rec.category.value
                pattern = rec.pattern.value
                
                recommendations_by_category[category] = recommendations_by_category.get(category, 0) + 1
                recommendations_by_pattern[pattern] = recommendations_by_pattern.get(pattern, 0) + 1
            
            # Calculate scores
            design_quality_score = self._calculate_design_quality_score(recommendations, code)
            pattern_opportunities_score = self._calculate_pattern_opportunities_score(recommendations)
            
            # Generate summary
            summary = self._generate_summary(recommendations)
            
            # Create report
            report = PatternAnalysisReport(
                report_id=cache_key,
                source_code=code,
                total_recommendations=total_recommendations,
                recommendations_by_category=recommendations_by_category,
                recommendations_by_pattern=recommendations_by_pattern,
                design_quality_score=design_quality_score,
                pattern_opportunities_score=pattern_opportunities_score,
                recommendations=recommendations,
                summary=summary
            )
            
            # Cache result
            self.recommendation_cache[cache_key] = report
            
            logger.info(f"Generated {total_recommendations} design pattern recommendations")
            return report
            
        except SyntaxError as e:
            logger.warning(f"Failed to parse code for pattern analysis: {e}")
            return PatternAnalysisReport(
                report_id=cache_key,
                source_code=code,
                total_recommendations=0,
                summary=f"Code parsing failed: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Pattern analysis failed: {e}")
            return PatternAnalysisReport(
                report_id=cache_key,
                source_code=code,
                total_recommendations=0,
                summary=f"Analysis failed: {str(e)}"
            )
    
    def _calculate_design_quality_score(self, recommendations: List[PatternRecommendation], code: str) -> float:
        """Calculate design quality score based on pattern opportunities."""
        if not recommendations:
            return 1.0  # Perfect if no patterns needed
        
        # Consider code size and complexity
        lines_of_code = len([line for line in code.split('\n') if line.strip()])
        
        # More patterns needed relative to code size indicates lower design quality
        pattern_density = len(recommendations) / max(1, lines_of_code / 50)  # Per 50 lines
        
        # Return inverted score (1.0 = excellent design)
        return max(0.0, 1.0 - min(1.0, pattern_density))
    
    def _calculate_pattern_opportunities_score(self, recommendations: List[PatternRecommendation]) -> float:
        """Calculate pattern opportunities score."""
        if not recommendations:
            return 0.0
        
        # Weight by confidence scores
        total_confidence = sum(rec.confidence_score for rec in recommendations)
        avg_confidence = total_confidence / len(recommendations)
        
        # Normalize to 0-1 scale
        return min(1.0, avg_confidence * len(recommendations) / 5)  # Assuming 5 high-confidence patterns = 1.0
    
    def _generate_summary(self, recommendations: List[PatternRecommendation]) -> str:
        """Generate summary of pattern analysis."""
        if not recommendations:
            return "Code analysis complete. No design pattern opportunities identified."
        
        # Count by category
        creational_count = len([r for r in recommendations if r.category == PatternCategory.CREATIONAL])
        structural_count = len([r for r in recommendations if r.category == PatternCategory.STRUCTURAL])
        behavioral_count = len([r for r in recommendations if r.category == PatternCategory.BEHAVIORAL])
        
        summary_parts = [f"Identified {len(recommendations)} design pattern opportunities:"]
        
        if creational_count > 0:
            summary_parts.append(f"{creational_count} creational patterns")
        if structural_count > 0:
            summary_parts.append(f"{structural_count} structural patterns")
        if behavioral_count > 0:
            summary_parts.append(f"{behavioral_count} behavioral patterns")
        
        return " ".join(summary_parts) + "."
    
    def get_recommendation_statistics(self) -> Dict[str, Any]:
        """Get pattern recommendation statistics."""
        total_reports = len(self.recommendation_cache)
        
        if total_reports == 0:
            return {
                "total_reports": 0,
                "total_recommendations": 0,
                "avg_design_quality": 0.0,
                "avg_pattern_opportunities": 0.0,
                "recommendations_by_category": {},
                "recommendations_by_pattern": {}
            }
        
        total_recommendations = sum(report.total_recommendations for report in self.recommendation_cache.values())
        total_design_quality = sum(report.design_quality_score for report in self.recommendation_cache.values())
        total_pattern_opportunities = sum(report.pattern_opportunities_score for report in self.recommendation_cache.values())
        
        # Aggregate recommendations
        all_by_category = {}
        all_by_pattern = {}
        
        for report in self.recommendation_cache.values():
            for category, count in report.recommendations_by_category.items():
                all_by_category[category] = all_by_category.get(category, 0) + count
            
            for pattern, count in report.recommendations_by_pattern.items():
                all_by_pattern[pattern] = all_by_pattern.get(pattern, 0) + count
        
        return {
            "total_reports": total_reports,
            "total_recommendations": total_recommendations,
            "avg_design_quality": total_design_quality / total_reports,
            "avg_pattern_opportunities": total_pattern_opportunities / total_reports,
            "recommendations_by_category": all_by_category,
            "recommendations_by_pattern": all_by_pattern,
            "cache_size": len(self.recommendation_cache)
        }
    
    async def cleanup(self) -> None:
        """Cleanup design pattern recommender."""
        self.recommendation_cache.clear()
        logger.info("Design pattern recommender cleaned up")
