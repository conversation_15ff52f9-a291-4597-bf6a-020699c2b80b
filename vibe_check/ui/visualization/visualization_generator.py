"""
Visualization Generator
==================

This module provides the VisualizationGenerator class for generating visualizations.
"""

import os
from pathlib import Path
from typing import Dict, Any, Union, Optional

from ...core.models.project_metrics import ProjectMetrics
from ...core.fs_utils import ensure_directory


class VisualizationGenerator:
    """Base class for visualization generators."""
    
    def __init__(self, output_dir: Union[str, Path], format_type: str):
        """
        Initialize the visualization generator.
        
        Args:
            output_dir: Output directory
            format_type: Format type (png, svg, html, etc.)
        """
        self.output_dir = str(output_dir)
        self.format = format_type
    
    def ensure_output_dir(self) -> None:
        """Ensure the output directory exists."""
        ensure_directory(self.output_dir)
    
    def generate_dependency_graph(self, metrics: ProjectMetrics) -> str:
        """
        Generate a dependency graph.
        
        Args:
            metrics: Project metrics
            
        Returns:
            Path to the generated visualization
            
        Raises:
            NotImplementedError: This method must be implemented by subclasses
        """
        raise NotImplementedError("Subclasses must implement generate_dependency_graph")
    
    def generate_complexity_heatmap(self, metrics: ProjectMetrics) -> str:
        """
        Generate a complexity heatmap.
        
        Args:
            metrics: Project metrics
            
        Returns:
            Path to the generated visualization
            
        Raises:
            NotImplementedError: This method must be implemented by subclasses
        """
        raise NotImplementedError("Subclasses must implement generate_complexity_heatmap")
    
    def generate_issue_distribution(self, metrics: ProjectMetrics) -> str:
        """
        Generate an issue distribution visualization.
        
        Args:
            metrics: Project metrics
            
        Returns:
            Path to the generated visualization
            
        Raises:
            NotImplementedError: This method must be implemented by subclasses
        """
        raise NotImplementedError("Subclasses must implement generate_issue_distribution")
    
    def generate_all_visualizations(self, metrics: ProjectMetrics) -> Dict[str, str]:
        """
        Generate all visualizations.
        
        Args:
            metrics: Project metrics
            
        Returns:
            Dictionary mapping visualization types to file paths
        """
        # Ensure the output directory exists
        self.ensure_output_dir()
        
        # Generate all visualizations
        visualizations = {
            "dependency_graph": self.generate_dependency_graph(metrics),
            "complexity_heatmap": self.generate_complexity_heatmap(metrics),
            "issue_distribution": self.generate_issue_distribution(metrics)
        }
        
        return visualizations


class DependencyGraphGenerator(VisualizationGenerator):
    """Visualization generator for dependency graphs."""
    
    def __init__(self, output_dir: Union[str, Path]):
        """
        Initialize the dependency graph generator.
        
        Args:
            output_dir: Output directory
        """
        super().__init__(output_dir, "png")
    
    def generate_dependency_graph(self, metrics: ProjectMetrics) -> str:
        """
        Generate a dependency graph.
        
        Args:
            metrics: Project metrics
            
        Returns:
            Path to the generated visualization
        """
        # Ensure the output directory exists
        self.ensure_output_dir()
        
        try:
            import graphviz
        except ImportError:
            raise ImportError("Graphviz is not installed. Install it with 'pip install graphviz'.")
        
        # Create a directed graph
        graph = graphviz.Digraph(
            name="dependency_graph",
            comment="Project Dependency Graph",
            format=self.format
        )
        
        # Add nodes for each file
        for file_path in metrics.file_metrics:
            file_name = os.path.basename(file_path)
            graph.node(file_path, file_name)
        
        # Add edges for dependencies
        for file_path, file_metrics in metrics.file_metrics.items():
            for dependency in file_metrics.dependencies:
                if dependency in metrics.file_metrics:
                    graph.edge(file_path, dependency)
        
        # Render the graph
        output_path = os.path.join(self.output_dir, "dependency_graph")
        graph.render(output_path, cleanup=True)
        
        return f"{output_path}.{self.format}"


class ComplexityHeatmapGenerator(VisualizationGenerator):
    """Visualization generator for complexity heatmaps."""
    
    def __init__(self, output_dir: Union[str, Path]):
        """
        Initialize the complexity heatmap generator.
        
        Args:
            output_dir: Output directory
        """
        super().__init__(output_dir, "png")
    
    def generate_complexity_heatmap(self, metrics: ProjectMetrics) -> str:
        """
        Generate a complexity heatmap.
        
        Args:
            metrics: Project metrics
            
        Returns:
            Path to the generated visualization
        """
        # Ensure the output directory exists
        self.ensure_output_dir()
        
        try:
            import matplotlib.pyplot as plt
            import numpy as np
        except ImportError:
            raise ImportError("Matplotlib is not installed. Install it with 'pip install matplotlib'.")
        
        # Extract complexity data
        file_names = []
        complexities = []
        
        for file_path, file_metrics in metrics.file_metrics.items():
            file_names.append(os.path.basename(file_path))
            complexities.append(file_metrics.complexity)
        
        # Sort by complexity
        sorted_indices = np.argsort(complexities)[::-1]  # Descending order
        file_names = [file_names[i] for i in sorted_indices]
        complexities = [complexities[i] for i in sorted_indices]
        
        # Create the heatmap
        plt.figure(figsize=(10, 8))
        plt.barh(file_names, complexities, color="skyblue")
        plt.xlabel("Complexity")
        plt.title("File Complexity Heatmap")
        plt.tight_layout()
        
        # Save the heatmap
        output_path = os.path.join(self.output_dir, "complexity_heatmap.png")
        plt.savefig(output_path)
        plt.close()
        
        return output_path


class IssueDistributionGenerator(VisualizationGenerator):
    """Visualization generator for issue distributions."""
    
    def __init__(self, output_dir: Union[str, Path]):
        """
        Initialize the issue distribution generator.
        
        Args:
            output_dir: Output directory
        """
        super().__init__(output_dir, "png")
    
    def generate_issue_distribution(self, metrics: ProjectMetrics) -> str:
        """
        Generate an issue distribution visualization.
        
        Args:
            metrics: Project metrics
            
        Returns:
            Path to the generated visualization
        """
        # Ensure the output directory exists
        self.ensure_output_dir()
        
        try:
            import matplotlib.pyplot as plt
            import numpy as np
        except ImportError:
            raise ImportError("Matplotlib is not installed. Install it with 'pip install matplotlib'.")
        
        # Count issues by severity
        severity_counts = {}
        for file_path, file_metrics in metrics.file_metrics.items():
            for issue in file_metrics.issues:
                severity = issue.get("severity", "unknown")
                severity_counts[severity] = severity_counts.get(severity, 0) + 1
        
        # Create the pie chart
        plt.figure(figsize=(8, 8))
        
        labels = list(severity_counts.keys())
        sizes = list(severity_counts.values())
        
        # Define colors for each severity
        colors = {
            "critical": "red",
            "high": "orange",
            "medium": "yellow",
            "low": "green",
            "info": "blue",
            "unknown": "gray"
        }
        
        # Get colors for each label
        chart_colors = [colors.get(label, "gray") for label in labels]
        
        plt.pie(sizes, labels=labels, colors=chart_colors, autopct="%1.1f%%", startangle=90)
        plt.axis("equal")  # Equal aspect ratio ensures that pie is drawn as a circle
        plt.title("Issue Distribution by Severity")
        
        # Save the chart
        output_path = os.path.join(self.output_dir, "issue_distribution.png")
        plt.savefig(output_path)
        plt.close()
        
        return output_path
