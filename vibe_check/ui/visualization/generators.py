"""
Visualization Generators
===================

This module provides functions for generating visualizations.
"""

import os
from pathlib import Path

from ...core.models.project_metrics import ProjectMetrics
from .charts import (
    create_complexity_chart,
    create_issues_chart,
    create_issues_by_tool_chart,
    create_lines_of_code_chart
)
from .exporters import export_chart
from .interactive_charts import (
    create_interactive_complexity_chart,
    create_interactive_issues_chart,
    create_interactive_issues_by_tool_chart,
    create_interactive_coverage_chart,
    create_interactive_dependency_graph,
    create_interactive_complexity_heatmap,
    export_interactive_chart,
    export_interactive_dependency_graph,
    export_interactive_complexity_heatmap
)


def generate_visualization(metrics: ProjectMetrics,
                          output_dir: Union[str, Path],
                          format_type: str = "html",
                          charts: Optional[List[str]] = None,
                          interactive: bool = True) -> Dict[str, str]:
    """
    Generate visualizations for project metrics.

    Args:
        metrics: Project metrics
        output_dir: Output directory
        format_type: Output format (json, html)
        charts: List of charts to generate (complexity, issues, issues_by_tool, lines_of_code,
                coverage, dependency_graph, complexity_heatmap)
        interactive: Whether to generate interactive charts

    Returns:
        Dictionary mapping chart names to file paths
    """
    # Create the output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Default charts
    if charts is None:
        charts = ["complexity", "issues", "issues_by_tool", "lines_of_code",
                 "coverage", "dependency_graph", "complexity_heatmap"]

    # Chart generators
    if interactive and format_type == "html":
        # Interactive chart generators
        chart_generators = {
            "complexity": create_interactive_complexity_chart,
            "issues": create_interactive_issues_chart,
            "issues_by_tool": create_interactive_issues_by_tool_chart,
            "coverage": create_interactive_coverage_chart,
            "dependency_graph": create_interactive_dependency_graph,
            "complexity_heatmap": create_interactive_complexity_heatmap,
            "lines_of_code": create_lines_of_code_chart  # Fallback to non-interactive
        }

        # Chart exporters
        chart_exporters = {
            "complexity": export_interactive_chart,
            "issues": export_interactive_chart,
            "issues_by_tool": export_interactive_chart,
            "coverage": export_interactive_chart,
            "dependency_graph": export_interactive_dependency_graph,
            "complexity_heatmap": export_interactive_complexity_heatmap,
            "lines_of_code": export_chart  # Fallback to non-interactive
        }
    else:
        # Non-interactive chart generators
        chart_generators = {
            "complexity": create_complexity_chart,
            "issues": create_issues_chart,
            "issues_by_tool": create_issues_by_tool_chart,
            "lines_of_code": create_lines_of_code_chart
        }

        # Chart exporters
        chart_exporters = {
            "complexity": export_chart,
            "issues": export_chart,
            "issues_by_tool": export_chart,
            "lines_of_code": export_chart
        }

    # Generate and export charts
    result = {}

    for chart_name in charts:
        if chart_name not in chart_generators:
            continue

        # Generate chart specification
        chart_spec = chart_generators[chart_name](metrics)

        # Export chart
        output_path = os.path.join(output_dir, f"{chart_name}.{format_type}")

        if chart_name in chart_exporters:
            if chart_name in ["dependency_graph", "complexity_heatmap"]:
                # Special exporters for these chart types
                chart_exporters[chart_name](chart_spec, output_path)
            elif chart_name in ["complexity", "issues", "issues_by_tool", "coverage"]:
                # Interactive chart exporters
                chart_exporters[chart_name](chart_spec, output_path)
            else:
                # Standard exporters
                chart_exporters[chart_name](chart_spec, output_path, format_type)
        else:
            # Fallback to standard exporter
            export_chart(chart_spec, output_path, format_type)

        result[chart_name] = output_path

    return result


def generate_dashboard(metrics: ProjectMetrics, output_path: Union[str, Path], interactive: bool = True) -> str:
    """
    Generate a dashboard with multiple visualizations.

    Args:
        metrics: Project metrics
        output_path: Output file path
        interactive: Whether to generate interactive charts

    Returns:
        Path to the generated dashboard
    """
    # Create the output directory if it doesn't exist
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    # Generate chart specifications
    if interactive:
        charts = {
            "complexity": create_interactive_complexity_chart(metrics),
            "issues": create_interactive_issues_chart(metrics),
            "issues_by_tool": create_interactive_issues_by_tool_chart(metrics),
            "coverage": create_interactive_coverage_chart(metrics)
        }

        # Generate dependency graph and heatmap
        dependency_graph = create_interactive_dependency_graph(metrics)
        complexity_heatmap = create_interactive_complexity_heatmap(metrics)
    else:
        charts = {
            "complexity": create_complexity_chart(metrics),
            "issues": create_issues_chart(metrics),
            "issues_by_tool": create_issues_by_tool_chart(metrics),
            "lines_of_code": create_lines_of_code_chart(metrics)
        }

        # No dependency graph or heatmap for non-interactive mode
        dependency_graph = None
        complexity_heatmap = None

    # Convert chart specifications to JSON
    import json
    charts_json = json.dumps(charts)
    dependency_graph_json = json.dumps(dependency_graph) if dependency_graph else "null"
    complexity_heatmap_json = json.dumps(complexity_heatmap) if complexity_heatmap else "null"

    # Generate HTML
    html = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Vibe Check Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vis-network@9.1.2/dist/vis-network.min.js"></script>
    <script src="https://code.highcharts.com/highcharts.js"></script>
    <script src="https://code.highcharts.com/modules/heatmap.js"></script>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .dashboard {{ display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px; }}
        .chart-container {{ border: 1px solid #ddd; padding: 10px; height: 400px; }}
        .full-width {{ grid-column: 1 / span 2; height: 500px; }}
        h1, h2 {{ text-align: center; }}
        .tabs {{ display: flex; margin-bottom: 10px; }}
        .tab {{ padding: 10px; cursor: pointer; border: 1px solid #ddd; margin-right: 5px; }}
        .tab.active {{ background-color: #f0f0f0; }}
        .tab-content {{ display: none; }}
        .tab-content.active {{ display: block; }}
    </style>
</head>
<body>
    <h1>Vibe Check Dashboard</h1>
    <h2>{Path(metrics.project_path).name}</h2>

    <div class="tabs">
        <div class="tab active" onclick="showTab('charts')">Charts</div>
        <div class="tab" onclick="showTab('dependency')">Dependency Graph</div>
        <div class="tab" onclick="showTab('heatmap')">Complexity Heatmap</div>
    </div>

    <div id="charts" class="tab-content active">
        <div class="dashboard">
            <div class="chart-container">
                <h3>Complexity</h3>
                <canvas id="complexity-chart"></canvas>
            </div>
            <div class="chart-container">
                <h3>Issues by Severity</h3>
                <canvas id="issues-chart"></canvas>
            </div>
            <div class="chart-container">
                <h3>Issues by Tool</h3>
                <canvas id="issues-by-tool-chart"></canvas>
            </div>
            <div class="chart-container">
                <h3>Type and Docstring Coverage</h3>
                <canvas id="coverage-chart"></canvas>
            </div>
        </div>
    </div>

    <div id="dependency" class="tab-content">
        <div class="chart-container full-width">
            <h3>Dependency Graph</h3>
            <div id="dependency-graph" style="height: 100%;"></div>
        </div>
    </div>

    <div id="heatmap" class="tab-content">
        <div class="chart-container full-width">
            <h3>Complexity Heatmap</h3>
            <div id="complexity-heatmap" style="height: 100%;"></div>
        </div>
    </div>

    <script>
        // Tab functionality
        function showTab(tabId) {{
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {{
                content.classList.remove('active');
            }});

            // Deactivate all tabs
            document.querySelectorAll('.tab').forEach(tab => {{
                tab.classList.remove('active');
            }});

            // Show the selected tab content
            document.getElementById(tabId).classList.add('active');

            // Activate the clicked tab
            document.querySelector(`.tab[onclick="showTab('${{tabId}}')"]`).classList.add('active');
        }}

        // Chart.js charts
        const charts = {charts_json};

        // Create complexity chart
        const complexityCtx = document.getElementById('complexity-chart').getContext('2d');
        new Chart(complexityCtx, charts.complexity);

        // Create issues chart
        const issuesCtx = document.getElementById('issues-chart').getContext('2d');
        new Chart(issuesCtx, charts.issues);

        // Create issues by tool chart
        const issuesByToolCtx = document.getElementById('issues-by-tool-chart').getContext('2d');
        new Chart(issuesByToolCtx, charts.issues_by_tool);

        // Create coverage chart
        if (charts.coverage) {{
            const coverageCtx = document.getElementById('coverage-chart').getContext('2d');
            new Chart(coverageCtx, charts.coverage);
        }} else if (charts.lines_of_code) {{
            // Fallback to lines of code chart if coverage is not available
            const coverageCtx = document.getElementById('coverage-chart').getContext('2d');
            new Chart(coverageCtx, charts.lines_of_code);
            // Update the title
            document.querySelector('.chart-container:nth-child(4) h3').textContent = 'Lines of Code';
        }}

        // Dependency graph
        const dependencyGraph = {dependency_graph_json};
        if (dependencyGraph) {{
            const container = document.getElementById('dependency-graph');
            const network = new vis.Network(container, {{
                nodes: new vis.DataSet(dependencyGraph.nodes),
                edges: new vis.DataSet(dependencyGraph.edges)
            }}, dependencyGraph.options);
        }}

        // Complexity heatmap
        const complexityHeatmap = {complexity_heatmap_json};
        if (complexityHeatmap) {{
            Highcharts.chart('complexity-heatmap', {{
                chart: {{
                    type: 'heatmap'
                }},
                title: {{
                    text: 'File Complexity Heatmap'
                }},
                xAxis: complexityHeatmap.options.xAxis,
                yAxis: complexityHeatmap.options.yAxis,
                colorAxis: complexityHeatmap.options.colorAxis,
                legend: complexityHeatmap.options.legend,
                tooltip: {{
                    formatter: new Function('return ' + complexityHeatmap.options.tooltip.formatter)()
                }},
                series: [{{
                    name: 'Complexity',
                    data: complexityHeatmap.data,
                    dataLabels: {{
                        enabled: true,
                        color: '#000000'
                    }}
                }}]
            }});
        }}
    </script>
</body>
</html>
"""

    # Write HTML to file
    with open(output_path, "w", encoding="utf-8") as f:
        f.write(html)

    return str(output_path)
