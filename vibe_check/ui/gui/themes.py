"""
Modern Theme System for Vibe Check GUI
======================================

Provides modern, professional styling for the Tkinter-based GUI.
Includes color schemes, fonts, and widget styling for a contemporary look.
"""

import tkinter as tk
from tkinter import ttk
import platform

# Modern color palette
COLORS = {
    # Primary colors
    "primary": "#2563eb",      # Blue
    "primary_dark": "#1d4ed8", # Darker blue
    "primary_light": "#3b82f6", # Lighter blue
    
    # Secondary colors
    "secondary": "#64748b",     # Slate
    "secondary_dark": "#475569", # Dark slate
    "secondary_light": "#94a3b8", # Light slate
    
    # Background colors
    "bg_primary": "#ffffff",    # White
    "bg_secondary": "#f8fafc",  # Very light gray
    "bg_tertiary": "#f1f5f9",   # Light gray
    "bg_dark": "#0f172a",       # Very dark blue
    
    # Text colors
    "text_primary": "#0f172a",  # Very dark blue
    "text_secondary": "#475569", # Dark slate
    "text_muted": "#64748b",    # Slate
    "text_light": "#ffffff",    # White
    
    # Status colors
    "success": "#10b981",       # Green
    "warning": "#f59e0b",       # Amber
    "error": "#ef4444",         # Red
    "info": "#3b82f6",          # Blue
    
    # Border colors
    "border": "#e2e8f0",        # Light gray
    "border_focus": "#2563eb",  # Blue
    "border_error": "#ef4444",  # Red
}

# Font configuration
FONTS = {
    "default": ("Segoe UI", 9),
    "heading": ("Segoe UI", 12, "bold"),
    "subheading": ("Segoe UI", 10, "bold"),
    "small": ("Segoe UI", 8),
    "monospace": ("Consolas", 9),
}

# Platform-specific font adjustments
if platform.system() == "Darwin":  # macOS
    FONTS.update({
        "default": ("SF Pro Text", 13),
        "heading": ("SF Pro Text", 16, "bold"),
        "subheading": ("SF Pro Text", 14, "bold"),
        "small": ("SF Pro Text", 11),
        "monospace": ("SF Mono", 12),
    })
elif platform.system() == "Linux":
    FONTS.update({
        "default": ("Ubuntu", 10),
        "heading": ("Ubuntu", 13, "bold"),
        "subheading": ("Ubuntu", 11, "bold"),
        "small": ("Ubuntu", 9),
        "monospace": ("Ubuntu Mono", 10),
    })


def setup_modern_theme(root: tk.Tk) -> None:
    """
    Set up modern theme for the application.
    
    Args:
        root: The root Tkinter window
    """
    # Configure the style
    style = ttk.Style()
    
    # Use a modern theme as base
    available_themes = style.theme_names()
    if "clam" in available_themes:
        style.theme_use("clam")
    elif "alt" in available_themes:
        style.theme_use("alt")
    else:
        style.theme_use("default")
    
    # Configure root window
    root.configure(bg=COLORS["bg_primary"])
    
    # Configure ttk styles
    configure_button_styles(style)
    configure_frame_styles(style)
    configure_label_styles(style)
    configure_entry_styles(style)
    configure_treeview_styles(style)
    configure_progressbar_styles(style)
    configure_notebook_styles(style)


def configure_button_styles(style: ttk.Style) -> None:
    """Configure button styles."""
    # Primary button
    style.configure(
        "Primary.TButton",
        background=COLORS["primary"],
        foreground=COLORS["text_light"],
        font=FONTS["default"],
        padding=(12, 8),
        relief="flat"
    )
    style.map(
        "Primary.TButton",
        background=[("active", COLORS["primary_dark"]),
                   ("pressed", COLORS["primary_dark"])]
    )
    
    # Secondary button
    style.configure(
        "Secondary.TButton",
        background=COLORS["bg_tertiary"],
        foreground=COLORS["text_primary"],
        font=FONTS["default"],
        padding=(12, 8),
        relief="flat"
    )
    style.map(
        "Secondary.TButton",
        background=[("active", COLORS["border"]),
                   ("pressed", COLORS["border"])]
    )
    
    # Success button
    style.configure(
        "Success.TButton",
        background=COLORS["success"],
        foreground=COLORS["text_light"],
        font=FONTS["default"],
        padding=(12, 8),
        relief="flat"
    )
    
    # Warning button
    style.configure(
        "Warning.TButton",
        background=COLORS["warning"],
        foreground=COLORS["text_light"],
        font=FONTS["default"],
        padding=(12, 8),
        relief="flat"
    )
    
    # Error button
    style.configure(
        "Error.TButton",
        background=COLORS["error"],
        foreground=COLORS["text_light"],
        font=FONTS["default"],
        padding=(12, 8),
        relief="flat"
    )


def configure_frame_styles(style: ttk.Style) -> None:
    """Configure frame styles."""
    style.configure(
        "Card.TFrame",
        background=COLORS["bg_primary"],
        relief="solid",
        borderwidth=1
    )
    
    style.configure(
        "Sidebar.TFrame",
        background=COLORS["bg_secondary"],
        relief="flat"
    )


def configure_label_styles(style: ttk.Style) -> None:
    """Configure label styles."""
    style.configure(
        "Heading.TLabel",
        background=COLORS["bg_primary"],
        foreground=COLORS["text_primary"],
        font=FONTS["heading"]
    )
    
    style.configure(
        "Subheading.TLabel",
        background=COLORS["bg_primary"],
        foreground=COLORS["text_primary"],
        font=FONTS["subheading"]
    )
    
    style.configure(
        "Muted.TLabel",
        background=COLORS["bg_primary"],
        foreground=COLORS["text_muted"],
        font=FONTS["small"]
    )
    
    style.configure(
        "Success.TLabel",
        background=COLORS["bg_primary"],
        foreground=COLORS["success"],
        font=FONTS["default"]
    )
    
    style.configure(
        "Warning.TLabel",
        background=COLORS["bg_primary"],
        foreground=COLORS["warning"],
        font=FONTS["default"]
    )
    
    style.configure(
        "Error.TLabel",
        background=COLORS["bg_primary"],
        foreground=COLORS["error"],
        font=FONTS["default"]
    )


def configure_entry_styles(style: ttk.Style) -> None:
    """Configure entry styles."""
    style.configure(
        "Modern.TEntry",
        fieldbackground=COLORS["bg_primary"],
        bordercolor=COLORS["border"],
        focuscolor=COLORS["border_focus"],
        font=FONTS["default"],
        padding=8
    )


def configure_treeview_styles(style: ttk.Style) -> None:
    """Configure treeview styles."""
    style.configure(
        "Modern.Treeview",
        background=COLORS["bg_primary"],
        foreground=COLORS["text_primary"],
        fieldbackground=COLORS["bg_primary"],
        font=FONTS["default"],
        rowheight=25
    )
    
    style.configure(
        "Modern.Treeview.Heading",
        background=COLORS["bg_tertiary"],
        foreground=COLORS["text_primary"],
        font=FONTS["subheading"],
        relief="flat"
    )


def configure_progressbar_styles(style: ttk.Style) -> None:
    """Configure progressbar styles."""
    style.configure(
        "Modern.Horizontal.TProgressbar",
        background=COLORS["primary"],
        troughcolor=COLORS["bg_tertiary"],
        borderwidth=0,
        lightcolor=COLORS["primary"],
        darkcolor=COLORS["primary"]
    )


def configure_notebook_styles(style: ttk.Style) -> None:
    """Configure notebook (tab) styles."""
    style.configure(
        "Modern.TNotebook",
        background=COLORS["bg_primary"],
        borderwidth=0
    )
    
    style.configure(
        "Modern.TNotebook.Tab",
        background=COLORS["bg_tertiary"],
        foreground=COLORS["text_secondary"],
        font=FONTS["default"],
        padding=(12, 8)
    )
    
    style.map(
        "Modern.TNotebook.Tab",
        background=[("selected", COLORS["bg_primary"]),
                   ("active", COLORS["border"])],
        foreground=[("selected", COLORS["text_primary"])]
    )


def get_color(color_name: str) -> str:
    """
    Get a color from the theme palette.
    
    Args:
        color_name: Name of the color
        
    Returns:
        Hex color code
    """
    return COLORS.get(color_name, "#000000")


def get_font(font_name: str) -> tuple:
    """
    Get a font from the theme fonts.
    
    Args:
        font_name: Name of the font
        
    Returns:
        Font tuple (family, size, style)
    """
    return FONTS.get(font_name, FONTS["default"])
