"""
Simple GUI Demo for Vibe Check
==============================

A simplified GUI demonstration showing the core functionality
and modern design principles for the Vibe Check interface.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import logging
from pathlib import Path
import subprocess
import sys

from .themes import setup_modern_theme, get_color, get_font

logger = logging.getLogger(__name__)


class SimpleVibeCheckGUI:
    """
    Simplified GUI for Vibe Check demonstration.
    
    Shows the core functionality and design principles without
    complex dependencies.
    """
    
    def __init__(self, root: tk.Tk, initial_project: Optional[str] = None):
        """
        Initialize the simple GUI.
        
        Args:
            root: The root Tkinter window
            initial_project: Optional initial project path
        """
        self.root = root
        self.current_project: Optional[str] = initial_project
        self.analysis_thread: Optional[threading.Thread] = None
        
        # Create the UI
        self.create_ui()
        
        # Load initial project if provided
        if initial_project:
            self.load_project(initial_project)
    
    def create_ui(self) -> None:
        """Create the user interface."""
        # Main container
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Header
        self.create_header()
        
        # Project selection
        self.create_project_section()
        
        # Analysis options
        self.create_options_section()
        
        # Progress section
        self.create_progress_section()
        
        # Results section
        self.create_results_section()
        
        # Status bar
        self.create_status_bar()
    
    def create_header(self) -> None:
        """Create the application header."""
        header_frame = ttk.Frame(self.main_frame)
        header_frame.pack(fill=tk.X, pady=(0, 30))
        
        # Title
        title_label = ttk.Label(
            header_frame,
            text="🔍 Vibe Check",
            style="Heading.TLabel"
        )
        title_label.pack(anchor=tk.W)
        
        # Subtitle
        subtitle_label = ttk.Label(
            header_frame,
            text="Professional Python Code Analysis Tool",
            style="Muted.TLabel"
        )
        subtitle_label.pack(anchor=tk.W)
    
    def create_project_section(self) -> None:
        """Create the project selection section."""
        # Project frame
        project_frame = ttk.LabelFrame(self.main_frame, text="📁 Project Selection", padding=15)
        project_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Project path display
        path_frame = ttk.Frame(project_frame)
        path_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(path_frame, text="Project Path:", style="Subheading.TLabel").pack(anchor=tk.W)
        
        self.project_path_var = tk.StringVar(value="No project selected")
        self.project_path_label = ttk.Label(
            path_frame,
            textvariable=self.project_path_var,
            style="Muted.TLabel",
            wraplength=600
        )
        self.project_path_label.pack(anchor=tk.W, pady=(5, 0))
        
        # Buttons
        button_frame = ttk.Frame(project_frame)
        button_frame.pack(fill=tk.X)
        
        self.browse_btn = ttk.Button(
            button_frame,
            text="📁 Browse...",
            style="Secondary.TButton",
            command=self.browse_project
        )
        self.browse_btn.pack(side=tk.LEFT)
        
        self.analyze_btn = ttk.Button(
            button_frame,
            text="🔍 Analyze Project",
            style="Primary.TButton",
            command=self.start_analysis,
            state=tk.DISABLED
        )
        self.analyze_btn.pack(side=tk.RIGHT)
    
    def create_options_section(self) -> None:
        """Create the analysis options section."""
        options_frame = ttk.LabelFrame(self.main_frame, text="⚙️ Analysis Options", padding=15)
        options_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Profile selection
        profile_frame = ttk.Frame(options_frame)
        profile_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(profile_frame, text="Analysis Profile:", style="Subheading.TLabel").pack(anchor=tk.W)
        
        self.profile_var = tk.StringVar(value="standard")
        profile_combo = ttk.Combobox(
            profile_frame,
            textvariable=self.profile_var,
            values=["minimal", "standard", "comprehensive"],
            state="readonly",
            width=20
        )
        profile_combo.pack(anchor=tk.W, pady=(5, 0))
        
        # VCS mode option
        self.vcs_mode_var = tk.BooleanVar(value=False)
        vcs_check = ttk.Checkbutton(
            options_frame,
            text="Use VCS Mode (Built-in Analysis Rules)",
            variable=self.vcs_mode_var
        )
        vcs_check.pack(anchor=tk.W, pady=(10, 0))
        
        # Verbose output option
        self.verbose_var = tk.BooleanVar(value=False)
        verbose_check = ttk.Checkbutton(
            options_frame,
            text="Verbose Output",
            variable=self.verbose_var
        )
        verbose_check.pack(anchor=tk.W, pady=(5, 0))
    
    def create_progress_section(self) -> None:
        """Create the progress section."""
        self.progress_frame = ttk.LabelFrame(self.main_frame, text="📊 Analysis Progress", padding=15)
        self.progress_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            self.progress_frame,
            variable=self.progress_var,
            maximum=100,
            style="Modern.Horizontal.TProgressbar"
        )
        self.progress_bar.pack(fill=tk.X, pady=(0, 10))
        
        # Status text
        self.status_var = tk.StringVar(value="Ready to analyze")
        self.status_label = ttk.Label(
            self.progress_frame,
            textvariable=self.status_var,
            style="Muted.TLabel"
        )
        self.status_label.pack(anchor=tk.W)
        
        # Cancel button
        self.cancel_btn = ttk.Button(
            self.progress_frame,
            text="❌ Cancel",
            style="Error.TButton",
            command=self.cancel_analysis,
            state=tk.DISABLED
        )
        self.cancel_btn.pack(anchor=tk.E, pady=(10, 0))
    
    def create_results_section(self) -> None:
        """Create the results section."""
        results_frame = ttk.LabelFrame(self.main_frame, text="📈 Analysis Results", padding=15)
        results_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # Results text area with scrollbar
        text_frame = ttk.Frame(results_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        self.results_text = tk.Text(
            text_frame,
            wrap=tk.WORD,
            font=get_font("monospace"),
            bg=get_color("bg_secondary"),
            fg=get_color("text_primary"),
            relief=tk.FLAT,
            padx=10,
            pady=10
        )
        
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)
        
        self.results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Initial message
        self.results_text.insert(tk.END, "Analysis results will appear here...\n\n")
        self.results_text.insert(tk.END, "Select a project and click 'Analyze Project' to begin.")
        self.results_text.config(state=tk.DISABLED)
    
    def create_status_bar(self) -> None:
        """Create the status bar."""
        status_frame = ttk.Frame(self.main_frame)
        status_frame.pack(fill=tk.X)
        
        self.status_bar_var = tk.StringVar(value="Ready")
        status_label = ttk.Label(
            status_frame,
            textvariable=self.status_bar_var,
            style="Muted.TLabel"
        )
        status_label.pack(side=tk.LEFT)
        
        # Version info
        version_label = ttk.Label(
            status_frame,
            text="Vibe Check v1.0.0",
            style="Muted.TLabel"
        )
        version_label.pack(side=tk.RIGHT)
    
    def browse_project(self) -> None:
        """Open project selection dialog."""
        project_path = filedialog.askdirectory(
            title="Select Python Project Directory",
            initialdir=str(Path.home())
        )
        
        if project_path:
            self.load_project(project_path)
    
    def load_project(self, project_path: str) -> None:
        """Load a project for analysis."""
        try:
            self.current_project = project_path
            
            # Update UI
            self.project_path_var.set(project_path)
            self.analyze_btn.config(state=tk.NORMAL)
            self.status_bar_var.set(f"Project loaded: {Path(project_path).name}")
            
            # Clear previous results
            self.results_text.config(state=tk.NORMAL)
            self.results_text.delete(1.0, tk.END)
            self.results_text.insert(tk.END, f"Project loaded: {project_path}\n\n")
            self.results_text.insert(tk.END, "Ready for analysis. Click 'Analyze Project' to begin.")
            self.results_text.config(state=tk.DISABLED)
            
            logger.info(f"Loaded project: {project_path}")
            
        except Exception as e:
            logger.error(f"Error loading project: {e}")
            messagebox.showerror("Error", f"Failed to load project: {e}")
    
    def start_analysis(self) -> None:
        """Start project analysis."""
        if not self.current_project:
            messagebox.showwarning("Warning", "Please select a project first.")
            return
        
        if self.analysis_thread and self.analysis_thread.is_alive():
            messagebox.showwarning("Warning", "Analysis is already running.")
            return
        
        # Start analysis in background thread
        self.analysis_thread = threading.Thread(
            target=self._run_analysis,
            daemon=True
        )
        self.analysis_thread.start()
    
    def _run_analysis(self) -> None:
        """Run analysis in background thread."""
        try:
            # Update UI
            self.root.after(0, self._analysis_started)
            
            # Build command
            cmd = [sys.executable, "-m", "vibe_check", "analyze", self.current_project]
            cmd.extend(["--profile", self.profile_var.get()])
            
            if self.vcs_mode_var.get():
                cmd.append("--vcs-mode")
            
            if self.verbose_var.get():
                cmd.append("--verbose")
            
            # Run analysis
            self.root.after(0, lambda: self.status_var.set("Running analysis..."))
            self.root.after(0, lambda: self.progress_var.set(50))
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            # Update results
            self.root.after(0, lambda: self._analysis_completed(result))
            
        except subprocess.TimeoutExpired:
            self.root.after(0, lambda: self._analysis_failed("Analysis timed out"))
        except Exception as e:
            self.root.after(0, lambda: self._analysis_failed(str(e)))
    
    def _analysis_started(self) -> None:
        """Handle analysis start."""
        self.analyze_btn.config(state=tk.DISABLED)
        self.cancel_btn.config(state=tk.NORMAL)
        self.progress_var.set(10)
        self.status_var.set("Starting analysis...")
        self.status_bar_var.set("Analysis in progress...")
        
        # Clear results
        self.results_text.config(state=tk.NORMAL)
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, "🔍 Analysis in progress...\n\n")
        self.results_text.config(state=tk.DISABLED)
    
    def _analysis_completed(self, result: subprocess.CompletedProcess) -> None:
        """Handle analysis completion."""
        # Update UI
        self.analyze_btn.config(state=tk.NORMAL)
        self.cancel_btn.config(state=tk.DISABLED)
        self.progress_var.set(100)
        
        if result.returncode == 0:
            self.status_var.set("Analysis completed successfully")
            self.status_bar_var.set("Analysis completed")
            
            # Show results
            self.results_text.config(state=tk.NORMAL)
            self.results_text.delete(1.0, tk.END)
            self.results_text.insert(tk.END, "✅ Analysis completed successfully!\n\n")
            self.results_text.insert(tk.END, "=== OUTPUT ===\n")
            self.results_text.insert(tk.END, result.stdout)
            if result.stderr:
                self.results_text.insert(tk.END, "\n=== ERRORS ===\n")
                self.results_text.insert(tk.END, result.stderr)
            self.results_text.config(state=tk.DISABLED)
        else:
            self.status_var.set("Analysis failed")
            self.status_bar_var.set("Analysis failed")
            
            # Show error
            self.results_text.config(state=tk.NORMAL)
            self.results_text.delete(1.0, tk.END)
            self.results_text.insert(tk.END, "❌ Analysis failed!\n\n")
            self.results_text.insert(tk.END, "=== ERROR OUTPUT ===\n")
            self.results_text.insert(tk.END, result.stderr or result.stdout)
            self.results_text.config(state=tk.DISABLED)
    
    def _analysis_failed(self, error_message: str) -> None:
        """Handle analysis failure."""
        self.analyze_btn.config(state=tk.NORMAL)
        self.cancel_btn.config(state=tk.DISABLED)
        self.progress_var.set(0)
        self.status_var.set("Analysis failed")
        self.status_bar_var.set("Analysis failed")
        
        # Show error
        self.results_text.config(state=tk.NORMAL)
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, f"❌ Analysis failed: {error_message}\n\n")
        self.results_text.config(state=tk.DISABLED)
        
        messagebox.showerror("Analysis Error", f"Analysis failed: {error_message}")
    
    def cancel_analysis(self) -> None:
        """Cancel running analysis."""
        # Note: This is a simplified implementation
        # In a real application, you'd need proper process management
        self.analyze_btn.config(state=tk.NORMAL)
        self.cancel_btn.config(state=tk.DISABLED)
        self.progress_var.set(0)
        self.status_var.set("Analysis cancelled")
        self.status_bar_var.set("Ready")
    
    def cleanup(self) -> None:
        """Clean up resources."""
        if self.analysis_thread and self.analysis_thread.is_alive():
            self.cancel_analysis()


def run_simple_gui(project_path: Optional[str] = None) -> None:
    """
    Run the simple GUI application.
    
    Args:
        project_path: Optional initial project path
    """
    # Create root window
    root = tk.Tk()
    
    # Set up theme
    setup_modern_theme(root)
    
    # Configure window
    root.title("Vibe Check - Project Analysis Tool")
    root.geometry("900x700")
    root.minsize(700, 500)
    
    # Create application
    app = SimpleVibeCheckGUI(root, project_path)
    
    # Handle closing
    def on_closing():
        app.cleanup()
        root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    
    # Center window
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() - width) // 2
    y = (root.winfo_screenheight() - height) // 2
    root.geometry(f"{width}x{height}+{x}+{y}")
    
    # Start GUI
    root.mainloop()
