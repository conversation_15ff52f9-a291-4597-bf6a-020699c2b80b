"""
GUI Application Entry Point
===========================

Main entry point for the Vibe Check GUI application.
Handles application initialization, theme setup, and window management.
"""

import tkinter as tk
import sys
import logging
from typing import Optional

from .simple_gui import run_simple_gui

logger = logging.getLogger(__name__)


def run_gui(project_path: Optional[str] = None, config_path: Optional[str] = None) -> None:
    """
    Launch the Vibe Check GUI application.

    Args:
        project_path: Optional initial project path
        config_path: Optional configuration file path
    """
    try:
        logger.info("Starting Vibe Check GUI")
        run_simple_gui(project_path)

    except Exception as e:
        logger.error(f"Error starting GUI: {e}")
        print(f"❌ Error starting GUI: {e}")
        sys.exit(1)


def center_window(window: tk.Tk) -> None:
    """
    Center a window on the screen.
    
    Args:
        window: The window to center
    """
    window.update_idletasks()
    
    # Get window dimensions
    width = window.winfo_width()
    height = window.winfo_height()
    
    # Get screen dimensions
    screen_width = window.winfo_screenwidth()
    screen_height = window.winfo_screenheight()
    
    # Calculate position
    x = (screen_width - width) // 2
    y = (screen_height - height) // 2
    
    # Set window position
    window.geometry(f"{width}x{height}+{x}+{y}")


if __name__ == "__main__":
    # Allow running the GUI directly
    run_gui()
