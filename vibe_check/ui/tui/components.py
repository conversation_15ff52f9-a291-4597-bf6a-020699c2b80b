"""
TUI Components
===========

This module provides components for rendering the Vibe Check TUI.
This is now a facade that imports from the specialized component modules.
"""

# Import from specialized component modules
try:
except ImportError:
    # Fallback implementations if the specialized modules are not available
    import logging
    from typing import List, <PERSON><PERSON>
    from typing import Any
    logger = logging.getLogger(__name__)

    def render_header(title: str = "Vibe Check - Project Analysis Tool") -> None:
        logger.info(f"TUI Header: {title}")

    def render_footer(state: Any) -> None:
        logger.debug("TUI Footer rendered")

    def render_menu(state: Any, options: List[Tuple[str, str]], title: str = "Main Menu") -> None:
        logger.info(f"TUI Menu: {title} with {len(options)} options")

    def render_main_menu(state: Any) -> None:
        logger.info("TUI Main menu rendered")

    def render_project_selection(state: Any) -> None:
        logger.info("TUI Project selection rendered")

    def render_config(state: Any) -> None:
        logger.info("TUI Configuration rendered")

    def render_progress(state: Any) -> None:
        logger.info("TUI Progress rendered")

    def render_results(state: Any) -> None:
        logger.info("TUI Results rendered")

    def render_summary_view(state: Any, metrics: Any) -> None:
        logger.info("TUI Summary view rendered")

    def render_issues_view(state, metrics) -> None:
        render_results(state)

    def render_recommendations_view(state, metrics) -> None:
        render_results(state)


# Re-export all functions for backward compatibility
__all__ = [
    'render_header',
    'render_footer',
    'render_menu',
    'render_main_menu',
    'render_project_selection',
    'render_config',
    'render_progress',
    'render_results',
    'render_summary_view',
    'render_issues_view',
    'render_recommendations_view'
]
