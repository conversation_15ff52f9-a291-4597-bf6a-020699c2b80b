"""
TUI Configuration Components
===========================

This module provides configuration rendering functions for the TUI.
"""

# Import TUI framework
try:
    from rich.console import Console
    from rich.table import Table
except ImportError:
    # Provide fallback if rich is not available
    class FallbackConsole:
        """Fallback console class if rich is not available."""
        def print(self, *args, **kwargs):
            import logging
            logger = logging.getLogger(__name__)
            logger.info(" ".join(str(arg) for arg in args))

    class FallbackTable:
        """Fallback table class if rich is not available."""
        def __init__(self, *args, **kwargs):
            self.rows = []
        
        def add_column(self, *args, **kwargs):
            pass
        
        def add_row(self, *args):
            self.rows.append(args)

    Console = FallbackConsole
    Table = FallbackTable

from .state_manager import TUIState
from .header_footer import render_header, render_footer

# Create console
console = Console()


def render_config(state: TUIState) -> None:
    """
    Render the configuration screen.

    Args:
        state: Application state
    """
    render_header("Configuration")

    # Create sections for different config options
    config = state.config

    # Create a table for general config
    table = Table(title="General Configuration", show_lines=True)
    table.add_column("Option", style="bold")
    table.add_column("Value")

    # Add general config options
    table.add_row("Project Path", state.project_path or "Not set")
    table.add_row("Output Directory",
                config.get("output_dir") or "Default")

    extensions = ", ".join(config.get("file_extensions", [".py"]))
    table.add_row("File Extensions", extensions)

    analyze_docs = "Yes" if config.get("analyze_docs", False) else "No"
    table.add_row("Analyze Documentation", analyze_docs)

    console.print(table)
    console.print()

    # Create a table for tool config
    tools_table = Table(title="Tools Configuration")
    tools_table.add_column("Tool", style="bold")
    tools_table.add_column("Enabled")

    tool_config = config.get("tools", {})
    for tool_name, tool_opts in {
        "ruff": {"name": "Ruff Linter"},
        "mypy": {"name": "MyPy Type Checker"},
        "bandit": {"name": "Bandit Security Scanner"},
        "complexity": {"name": "Complexity Analyzer"},
        "markdown": {"name": "Markdown Analyzer"}
    }.items():
        enabled = tool_config.get(tool_name, {}).get("enabled", True)
        enabled_str = "[green]✓[/green]" if enabled else "[red]✗[/red]"
        tools_table.add_row(tool_opts["name"], enabled_str)

    console.print(tools_table)
    console.print()

    # Create a table for analysis options
    analysis_table = Table(title="Analysis Options")
    analysis_table.add_column("Option", style="bold")
    analysis_table.add_column("Value")

    analysis_table.add_row("Simple Analysis",
                    "Yes" if config.get("use_simple_analyzer", True) else "No")
    analysis_table.add_row("Import Analysis",
                    "Yes" if config.get("import_analysis", {}).get("enabled", True) else "No")
    analysis_table.add_row("Visualizations",
                    "Yes" if config.get("visualization", {}).get("enabled", False) else "No")

    console.print(analysis_table)

    render_footer(state)
