"""
Text User Interface for Vibe Check
========================

This module provides a text-based user interface for Vibe Check, allowing users to
interact with the tool through a terminal-based UI.

The TUI provides interactive menus, progress indicators, and result visualization
in the terminal, making it suitable for remote or headless environments.
"""


__all__ = [
    "run_tui",
    "render_menu",
    "render_progress",
    "render_results",
    "TUIState"
]
