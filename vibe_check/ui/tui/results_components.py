"""
TUI Results Components
=====================

This module provides results rendering functions for the TUI.
"""

import os

# Import TUI framework
try:
    from rich.console import Console
    from rich.table import Table
    from rich.panel import Panel
except ImportError:
    # Provide fallback if rich is not available
    class FallbackConsole:
        """Fallback console class if rich is not available."""
        def print(self, *args, **kwargs):
            import logging
            logger = logging.getLogger(__name__)
            logger.info(" ".join(str(arg) for arg in args))

    class FallbackTable:
        """Fallback table class if rich is not available."""
        def __init__(self, *args, **kwargs):
            self.rows = []
        
        def add_column(self, *args, **kwargs):
            pass
        
        def add_row(self, *args):
            self.rows.append(args)

    class FallbackPanel:
        """Fallback panel class if rich is not available."""
        def __init__(self, content, **kwargs):
            self.content = content

    Console = FallbackConsole
    Table = FallbackTable
    Panel = FallbackPanel

from vibe_check.core.models import ProjectMetrics
from .state_manager import TUIState
from .header_footer import render_header, render_footer
from typing import Protocol

# Create console
console = Console()


def render_results(state: TUIState) -> None:
    """
    Render the results screen.

    Args:
        state: Application state
    """
    view = state.selected_view or "summary"
    metrics = state.metrics

    if metrics is None:
        render_header("Results")
        console.print("[yellow]No analysis results available[/yellow]")
        render_footer(state)
        return

    if view == "summary":
        render_summary_view(state, metrics)
    elif view == "issues":
        render_issues_view(state, metrics)
    elif view == "recommendations":
        render_recommendations_view(state, metrics)
    else:
        # Default to summary
        render_summary_view(state, metrics)


def render_summary_view(state: TUIState, metrics: ProjectMetrics) -> None:
    """
    Render the summary view.

    Args:
        state: Application state
        metrics: Analysis metrics
    """
    render_header("Analysis Summary")

    # Create a table for key metrics
    table = Table(title="Project Metrics", show_footer=True)
    table.add_column("Metric", style="bold", footer="Total")
    table.add_column("Value", justify="right", footer=str(metrics.total_file_count))

    table.add_row("Python Files", str(metrics.python_file_count))
    table.add_row("Markdown Files", str(metrics.markdown_file_count))
    table.add_row("Directories", str(metrics.total_directory_count))
    table.add_row("Avg. Complexity", f"{metrics.avg_complexity:.2f}")
    table.add_row("Avg. Documentation Coverage", f"{metrics.avg_doc_coverage:.2f}%")
    table.add_row("Avg. Type Coverage", f"{metrics.avg_type_coverage:.2f}%")

    console.print(table)
    console.print()

    # Show complex files if available
    if metrics.complexity_scores:
        complex_files = sorted(
            metrics.complexity_scores.items(),
            key=lambda x: x[1],
            reverse=True
        )[:5]

        complex_table = Table(title="Most Complex Files")
        complex_table.add_column("File", style="blue")
        complex_table.add_column("Complexity", justify="right", style="cyan")

        for file_path, score in complex_files:
            complex_table.add_row(os.path.basename(file_path), str(score))

        console.print(complex_table)

    # Show circular dependencies if available
    circular_deps = metrics.get_circular_dependencies()
    if circular_deps:
        console.print()
        console.print(Panel(
            f"[yellow]Warning: Found {len(circular_deps)} circular dependencies![/yellow]",
            title="Circular Dependencies"
        ))

        for i, cycle in enumerate(circular_deps[:3], 1):
            cycle_str = " -> ".join(os.path.basename(p) for p in cycle)
            console.print(f"  {i}. {cycle_str} -> {os.path.basename(cycle[0])}")

        if len(circular_deps) > 3:
            console.print(f"  ... and {len(circular_deps) - 3} more")

    render_footer(state)


def render_issues_view(state: TUIState, metrics: ProjectMetrics) -> None:
    """
    Render the issues view.

    Args:
        state: Application state
        metrics: Analysis metrics
    """
    render_header("Analysis Issues")

    # Collect all issues
    all_issues = []
    for file_path, file_metric in metrics.files.items():
        for tool_name, tool_result in file_metric.tool_results.items():
            if "issues" in tool_result:
                for issue in tool_result["issues"]:
                    all_issues.append({
                        "file": file_path,
                        "tool": tool_name,
                        "code": issue.get("code", "unknown"),
                        "message": issue.get("message", "Unknown issue"),
                        "line": issue.get("line", 0),
                        "severity": issue.get("severity", "MEDIUM")
                    })

    if not all_issues:
        console.print("[green]No issues found! 🎉[/green]")
        render_footer(state)
        return

    # Group issues by severity
    severity_groups = {
        "HIGH": [],
        "MEDIUM": [],
        "LOW": []
    }

    for issue in all_issues:
        severity = issue["severity"]
        if severity in severity_groups:
            severity_groups[severity].append(issue)
        else:
            severity_groups["MEDIUM"].append(issue)

    # Display counts
    console.print(f"Found [bold]{len(all_issues)} issues[/bold]:")
    console.print(f"  [red]High severity: {len(severity_groups['HIGH'])}[/red]")
    console.print(f"  [yellow]Medium severity: {len(severity_groups['MEDIUM'])}[/yellow]")
    console.print(f"  [blue]Low severity: {len(severity_groups['LOW'])}[/blue]")
    console.print()

    # Show high severity issues first
    if severity_groups["HIGH"]:
        console.print("[bold red]HIGH SEVERITY ISSUES[/bold red]")
        for issue in severity_groups["HIGH"]:
            file_name = os.path.basename(issue["file"])
            message = issue["message"]
            code = issue["code"]
            line = issue["line"]

            console.print(f"[red]{file_name}:{line}[/red] - {code}: {message}")
            console.print(f"  Tool: {issue['tool']}")
            console.print()

    # Show medium severity issues
    if severity_groups["MEDIUM"]:
        console.print("[bold yellow]MEDIUM SEVERITY ISSUES[/bold yellow]")
        for issue in severity_groups["MEDIUM"][:5]:  # Show only first 5
            file_name = os.path.basename(issue["file"])
            message = issue["message"]
            code = issue["code"]
            line = issue["line"]

            console.print(f"[yellow]{file_name}:{line}[/yellow] - {code}: {message}")
            console.print(f"  Tool: {issue['tool']}")
            console.print()

        if len(severity_groups["MEDIUM"]) > 5:
            console.print(f"  ... and {len(severity_groups['MEDIUM']) - 5} more medium severity issues")
            console.print()

    render_footer(state)


def render_recommendations_view(state: TUIState, metrics: ProjectMetrics) -> None:
    """
    Render the recommendations view.

    Args:
        state: Application state
        metrics: Analysis metrics
    """
    render_header("Recommendations")

    # Generate recommendations based on metrics
    recommendations = []

    # Check for circular dependencies
    circular_deps = metrics.get_circular_dependencies()
    if circular_deps:
        recommendations.append({
            "title": "Resolve Circular Dependencies",
            "description": f"Found {len(circular_deps)} circular dependencies. These can make the code harder to maintain.",
            "actions": [
                "Identify the root cause of the circular dependencies",
                "Consider using dependency injection or other design patterns",
                "Refactor the code to break the cycles"
            ],
            "severity": "high"
        })

    # Check for high complexity files
    high_complexity_files = [(p, c) for p, c in metrics.complexity_scores.items() if c > 10]
    if high_complexity_files:
        file_list = ", ".join([os.path.basename(p) for p, _ in high_complexity_files[:3]])
        if len(high_complexity_files) > 3:
            file_list += f" and {len(high_complexity_files) - 3} more"

        recommendations.append({
            "title": "Reduce Code Complexity",
            "description": f"Found {len(high_complexity_files)} files with high complexity. High complexity makes code harder to maintain and test.",
            "actions": [
                "Break down complex functions into smaller, more focused functions",
                "Refactor complex conditional logic",
                "Consider using design patterns to simplify the code"
            ],
            "examples": file_list,
            "severity": "medium"
        })

    # Check for low type coverage
    low_type_coverage_files = [(p, c) for p, c in metrics.type_coverage.items() if c < 50]
    if low_type_coverage_files:
        recommendations.append({
            "title": "Improve Type Coverage",
            "description": f"Found {len(low_type_coverage_files)} files with low type annotation coverage. Type annotations help catch errors early.",
            "actions": [
                "Add type annotations to function parameters and return values",
                "Use mypy to check for type errors",
                "Consider using typing.Protocol for interface definitions"
            ],
            "severity": "low"
        })

    # Display recommendations
    if not recommendations:
        console.print("[green]No specific recommendations. The project looks good! 🎉[/green]")
    else:
        for rec in recommendations:
            severity_style = {
                "high": "red",
                "medium": "yellow",
                "low": "blue"
            }.get(rec["severity"], "white")

            console.print(f"[{severity_style}]{rec['title']}[/{severity_style}]")
            console.print(rec["description"])
            console.print()

            console.print("[bold]Recommended Actions:[/bold]")
            for action in rec["actions"]:
                console.print(f"  • {action}")

            if "examples" in rec:
                console.print(f"\n[bold]Examples:[/bold] {rec['examples']}")

            console.print("\n" + "-" * 50 + "\n")

    render_footer(state)
