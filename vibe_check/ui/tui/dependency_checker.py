"""
TUI Dependency Checker and Graceful Fallback System
===================================================

This module provides intelligent dependency management for the TUI interface,
including automatic installation, graceful degradation, and fallback options.

Features:
- Automatic dependency detection and installation
- Graceful fallback to simplified TUI when dependencies are missing
- User-friendly error messages and installation guidance
- Performance monitoring and optimization suggestions
"""

import sys
import subprocess
import importlib
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum

from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class TUIMode(Enum):
    """TUI operation modes based on available dependencies."""
    
    FULL = "full"           # All dependencies available
    BASIC = "basic"         # Basic dependencies only (rich)
    MINIMAL = "minimal"     # No dependencies, text-only fallback
    DISABLED = "disabled"   # TUI completely disabled


@dataclass
class TUIDependency:
    """Information about a TUI dependency."""
    
    name: str
    package: str
    import_name: str
    description: str
    required_for: str
    install_command: str
    available: bool = False
    version: Optional[str] = None


class TUIDependencyChecker:
    """Intelligent dependency checker for TUI interface."""
    
    def __init__(self):
        self.dependencies = self._define_dependencies()
        self._check_all_dependencies()
    
    def _define_dependencies(self) -> Dict[str, TUIDependency]:
        """Define all TUI dependencies."""
        return {
            "rich": TUIDependency(
                name="Rich Console",
                package="rich>=12.0.0",
                import_name="rich",
                description="Enhanced terminal output with colors and formatting",
                required_for="Basic TUI functionality",
                install_command="pip install rich>=12.0.0"
            ),
            "textual": TUIDependency(
                name="Textual TUI Framework",
                package="textual>=0.9.0",
                import_name="textual",
                description="Advanced terminal user interface framework",
                required_for="Full interactive TUI",
                install_command="pip install textual>=0.9.0"
            ),
            "keyboard": TUIDependency(
                name="Keyboard Input Handler",
                package="keyboard>=0.13.5",
                import_name="keyboard",
                description="Global keyboard input capture",
                required_for="Keyboard navigation and shortcuts",
                install_command="pip install keyboard>=0.13.5"
            )
        }
    
    def _check_all_dependencies(self) -> None:
        """Check availability of all dependencies."""
        for dep_key, dep in self.dependencies.items():
            try:
                module = importlib.import_module(dep.import_name)
                dep.available = True
                dep.version = getattr(module, '__version__', 'unknown')
                logger.debug(f"TUI dependency {dep.name} available: v{dep.version}")
            except ImportError:
                dep.available = False
                logger.debug(f"TUI dependency {dep.name} not available")
    
    def get_available_mode(self) -> TUIMode:
        """Determine the best available TUI mode."""
        
        # Check for full mode (all dependencies)
        if all(dep.available for dep in self.dependencies.values()):
            return TUIMode.FULL
        
        # Check for basic mode (rich only)
        if self.dependencies["rich"].available:
            return TUIMode.BASIC
        
        # Fallback to minimal mode
        return TUIMode.MINIMAL
    
    def get_missing_dependencies(self, mode: TUIMode = TUIMode.FULL) -> List[TUIDependency]:
        """Get list of missing dependencies for specified mode."""
        
        required_deps = {
            TUIMode.FULL: ["rich", "textual", "keyboard"],
            TUIMode.BASIC: ["rich"],
            TUIMode.MINIMAL: [],
            TUIMode.DISABLED: []
        }
        
        missing = []
        for dep_key in required_deps.get(mode, []):
            if not self.dependencies[dep_key].available:
                missing.append(self.dependencies[dep_key])
        
        return missing
    
    def can_auto_install(self) -> bool:
        """Check if automatic installation is possible."""
        try:
            # Check if pip is available
            result = subprocess.run(
                [sys.executable, "-m", "pip", "--version"],
                capture_output=True,
                timeout=5
            )
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False
    
    def install_dependencies(self, mode: TUIMode = TUIMode.BASIC, prompt_user: bool = True) -> bool:
        """
        Install missing dependencies for specified mode.
        
        Args:
            mode: Target TUI mode
            prompt_user: Whether to prompt user for confirmation
            
        Returns:
            True if installation successful, False otherwise
        """
        missing = self.get_missing_dependencies(mode)
        
        if not missing:
            logger.info("All required dependencies already available")
            return True
        
        if not self.can_auto_install():
            logger.error("Automatic installation not available")
            return False
        
        # Prompt user if requested
        if prompt_user:
            print(f"\n🔧 TUI Dependencies Required for {mode.value.title()} Mode")
            print("=" * 60)
            print("Missing dependencies:")
            for dep in missing:
                print(f"  • {dep.name}: {dep.description}")
            
            response = input("\nInstall missing dependencies? [y/N]: ").strip().lower()
            if response not in ['y', 'yes']:
                return False
        
        # Install dependencies
        success = True
        for dep in missing:
            try:
                print(f"Installing {dep.name}...")
                result = subprocess.run(
                    [sys.executable, "-m", "pip", "install", dep.package],
                    capture_output=True,
                    text=True,
                    timeout=60
                )
                
                if result.returncode == 0:
                    print(f"✅ {dep.name} installed successfully")
                    dep.available = True
                else:
                    print(f"❌ Failed to install {dep.name}: {result.stderr}")
                    success = False
                    
            except subprocess.TimeoutExpired:
                print(f"❌ Installation of {dep.name} timed out")
                success = False
            except Exception as e:
                print(f"❌ Error installing {dep.name}: {e}")
                success = False
        
        return success
    
    def get_fallback_suggestions(self, mode: TUIMode) -> List[str]:
        """Get suggestions for fallback options."""
        
        suggestions = []
        
        if mode == TUIMode.MINIMAL:
            suggestions.extend([
                "Use CLI mode: vibe-check analyze [project_path]",
                "Install basic TUI: pip install rich>=12.0.0",
                "Use web interface: vibe-check web [project_path]"
            ])
        elif mode == TUIMode.BASIC:
            suggestions.extend([
                "Limited keyboard navigation available",
                "Install full TUI: pip install textual>=0.9.0 keyboard>=0.13.5",
                "Use CLI for full functionality: vibe-check analyze --help"
            ])
        
        return suggestions
    
    def print_dependency_status(self) -> None:
        """Print comprehensive dependency status report."""
        
        print("\n🔧 TUI DEPENDENCY STATUS")
        print("=" * 50)
        
        available_mode = self.get_available_mode()
        print(f"Available Mode: {available_mode.value.title()}")
        
        print("\nDependency Details:")
        for dep_key, dep in self.dependencies.items():
            status = "✅ Available" if dep.available else "❌ Missing"
            version_info = f" (v{dep.version})" if dep.available and dep.version else ""
            print(f"  • {dep.name}: {status}{version_info}")
            print(f"    {dep.description}")
            if not dep.available:
                print(f"    Install: {dep.install_command}")
        
        # Show mode-specific information
        if available_mode == TUIMode.MINIMAL:
            print("\n⚠️  LIMITED FUNCTIONALITY")
            print("TUI will run in text-only mode with basic features.")
            
        elif available_mode == TUIMode.BASIC:
            print("\n📊 BASIC FUNCTIONALITY")
            print("TUI will run with enhanced display but limited interactivity.")
            
        elif available_mode == TUIMode.FULL:
            print("\n🚀 FULL FUNCTIONALITY")
            print("All TUI features available including interactive navigation.")
        
        # Show suggestions
        suggestions = self.get_fallback_suggestions(available_mode)
        if suggestions:
            print("\n💡 Suggestions:")
            for suggestion in suggestions:
                print(f"  • {suggestion}")


def check_tui_dependencies() -> Tuple[TUIMode, TUIDependencyChecker]:
    """
    Check TUI dependencies and return available mode.
    
    Returns:
        Tuple of (available_mode, dependency_checker)
    """
    checker = TUIDependencyChecker()
    mode = checker.get_available_mode()
    
    logger.info(f"TUI mode determined: {mode.value}")
    
    return mode, checker


def ensure_tui_dependencies(target_mode: TUIMode = TUIMode.BASIC) -> bool:
    """
    Ensure TUI dependencies are available for target mode.
    
    Args:
        target_mode: Desired TUI mode
        
    Returns:
        True if dependencies are satisfied, False otherwise
    """
    mode, checker = check_tui_dependencies()
    
    if mode.value >= target_mode.value:  # Mode hierarchy check
        return True
    
    # Try to install missing dependencies
    print(f"\n🎯 Target TUI Mode: {target_mode.value.title()}")
    print(f"Current Mode: {mode.value.title()}")
    
    return checker.install_dependencies(target_mode, prompt_user=True)


def get_tui_startup_message(mode: TUIMode) -> str:
    """Get startup message for TUI mode."""
    
    messages = {
        TUIMode.FULL: "🚀 Starting Vibe Check TUI (Full Mode)",
        TUIMode.BASIC: "📊 Starting Vibe Check TUI (Basic Mode)",
        TUIMode.MINIMAL: "📝 Starting Vibe Check TUI (Minimal Mode)",
        TUIMode.DISABLED: "❌ TUI Disabled"
    }
    
    return messages.get(mode, "🔧 Starting Vibe Check TUI")


if __name__ == "__main__":
    # CLI for dependency checking
    checker = TUIDependencyChecker()
    checker.print_dependency_status()
