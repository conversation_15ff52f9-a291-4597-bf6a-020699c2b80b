"""
TUI Application
===========

This module provides the main application for the Vibe Check TUI.
It handles the event loop, keyboard input, and screen rendering.
"""

import logging
import os
import sys
import threading
import time
from pathlib import Path

try:
    from rich.console import Console
    import keyboard
except ImportError:
    import logging
    logger = logging.getLogger(__name__)
    logger.error("Required packages not found. Please install with: pip install rich keyboard")
    sys.exit(1)

from .components import (
    render_header, render_footer,
    render_main_menu, render_project_selection,
    render_config, render_progress,
    render_results, render_visualization
)

# Configure logging
logger = logging.getLogger("pat_tui")

# Create console
console = Console()


def handle_input(key: str, state: TUIState) -> None:
    """
    Handle keyboard input.

    Args:
        key: Key pressed
        state: Application state
    """
    screen = state.current_screen

    # Global keys
    if key == 'q':
        state.request_exit()
        return

    if key == 'b' and screen != TUIScreen.MAIN_MENU:
        state.go_back()
        return

    # Screen-specific keys
    if screen == TUIScreen.MAIN_MENU:
        if key == '1':
            state.set_screen(TUIScreen.PROJECT_SELECT)
        elif key == '2':
            state.set_screen(TUIScreen.CONFIG)
        elif key == '3':
            # Recent projects - not implemented yet
            pass
        elif key == '4':
            # Help - not implemented yet
            pass

    elif screen == TUIScreen.PROJECT_SELECT:
        # This would be handled by text input in a real TUI
        pass

    elif screen == TUIScreen.CONFIG:
        # Config would use arrow keys + input in a real TUI
        pass

    elif screen == TUIScreen.RESULTS:
        if key == 's':
            state.set_selected_view("summary")
        elif key == 'i':
            state.set_selected_view("issues")
        elif key == 'r':
            state.set_selected_view("recommendations")
        elif key == 'v':
            state.set_screen(TUIScreen.VISUALIZATION)


def render_screen(state: TUIState) -> None:
    """
    Render the current screen.

    Args:
        state: Application state
    """
    # Clear the console
    os.system('cls' if os.name == 'nt' else 'clear')

    # Render the current screen
    screen = state.current_screen

    if screen == TUIScreen.MAIN_MENU:
        render_main_menu(state)

    elif screen == TUIScreen.PROJECT_SELECT:
        render_project_selection(state)

    elif screen == TUIScreen.CONFIG:
        render_config(state)

    elif screen == TUIScreen.RUNNING:
        render_progress(state)

    elif screen == TUIScreen.RESULTS:
        render_results(state)

    elif screen == TUIScreen.VISUALIZATION:
        render_visualization(state)

    elif screen == TUIScreen.ERROR:
        render_header("Error")
        console.print(f"[red]Error: {state.error}[/red]")
        render_footer(state)


class InputHandler:
    """Handler for keyboard input."""

    def __init__(self, state: TUIState):
        """Initialize the input handler."""
        self.state = state
        self.running = False
        self.thread = None

    def start(self):
        """Start the input handler."""
        self.running = True
        self.thread = threading.Thread(target=self._run)
        self.thread.daemon = True
        self.thread.start()

    def stop(self):
        """Stop the input handler."""
        self.running = False
        if self.thread:
            self.thread.join(timeout=1.0)

    def _run(self):
        """Run the input loop."""
        while self.running:
            # Use keyboard library to get single keypresses
            event = keyboard.read_event(suppress=True)
            if event.event_type == 'down':
                handle_input(event.name, self.state)

                # Small sleep to avoid thread overload
                time.sleep(0.05)


def run_tui():
    """
    Run the TUI application.

    This function initializes the UI, starts the input handler,
    and runs the main event loop.
    """
    try:
        # Initialize the state
        state = TUIState()

        # Set up screen change listener
        def on_state_change(state):
            render_screen(state)

        state.add_state_listener(on_state_change)

        # Render the initial screen
        render_screen(state)

        # Start input handler
        input_handler = InputHandler(state)
        input_handler.start()

        # Main loop
        try:
            while not state.exit_requested:
                # Check if we need to update the screen
                # In a real TUI app, this would be event-driven
                if state.current_screen == TUIScreen.RUNNING:
                    # Update progress in running screen
                    render_screen(state)

                time.sleep(0.1)

        except KeyboardInterrupt:
            state.request_exit()

        finally:
            # Clean up
            input_handler.stop()

        # Final cleanup
        os.system('cls' if os.name == 'nt' else 'clear')
        console.print("[green]Thank you for using Vibe Check![/green]")

    except Exception as e:
        logger.error(f"Error running TUI: {e}", exc_info=True)
        console.print(f"[red]Error running TUI: {e}[/red]")

        return 1

    return 0


async def run_app(project_path: Union[str, Path],
                 config_path: Optional[Union[str, Path]] = None,
                 context: Optional[Dict[str, Any]] = None) -> None:
    """
    Run the TUI application with a specific project.

    Args:
        project_path: Path to the project to analyze
        config_path: Optional path to a configuration file
        context: Optional context metadata
    """
    # Initialize the state
    state = TUIState()

    # Set the project path
    state.project_path = str(project_path)

    # Set the config path if provided
    if config_path:
        state.config_path = str(config_path)

    # Set the context if provided
    if context:
        state.context = context

    # Start the TUI
    run_tui()


if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

    sys.exit(run_tui())
