"""
TUI Menu Components
==================

This module provides menu rendering functions for the TUI.
"""

from typing import List, Tuple

# Import TUI framework
try:
    from rich.console import Console
    from rich.table import Table
except ImportError:
    # Provide fallback if rich is not available
    class FallbackConsole:
        """Fallback console class if rich is not available."""
        def print(self, *args, **kwargs):
            import logging
            logger = logging.getLogger(__name__)
            logger.info(" ".join(str(arg) for arg in args))

    class FallbackTable:
        """Fallback table class if rich is not available."""
        def __init__(self, *args, **kwargs):
            self.rows = []
        
        def add_column(self, *args, **kwargs):
            pass
        
        def add_row(self, *args):
            self.rows.append(args)

    Console = FallbackConsole
    Table = FallbackTable

from .state_manager import TUIState
from .header_footer import render_header, render_footer

# Create console
console = Console()


def render_menu(state: TUIState, options: List[Tuple[str, str]],
              title: str = "Main Menu") -> None:
    """
    Render a menu with options.

    Args:
        state: Application state
        options: List of (option_key, option_description) tuples
        title: Menu title
    """
    render_header(title)

    # Create a table for the menu
    table = Table(show_header=False, box=None, padding=(0, 2))
    table.add_column("Key", style="bold cyan")
    table.add_column("Description")

    for key, description in options:
        table.add_row(key, description)

    console.print(table)

    render_footer(state)


def render_main_menu(state: TUIState) -> None:
    """
    Render the main menu.

    Args:
        state: Application state
    """
    options = [
        ("1", "Analyze Project"),
        ("2", "Configuration"),
        ("3", "Recent Projects"),
        ("4", "Help"),
        ("q", "Quit")
    ]

    render_menu(state, options)


def render_project_selection(state: TUIState) -> None:
    """
    Render the project selection screen.

    Args:
        state: Application state
    """
    render_header("Project Selection")

    console.print("\nEnter the path to the project to analyze:")
    console.print("[dim]Example: /path/to/your/project[/dim]\n")

    if state.project_path:
        console.print(f"Current path: [green]{state.project_path}[/green]")

    render_footer(state)
