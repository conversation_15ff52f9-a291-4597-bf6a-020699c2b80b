"""
TUI Header and Footer Components
==============================

This module provides header and footer rendering functions for the TUI.
"""


# Import TUI framework
try:
    from rich.console import Console
except ImportError:
    # Provide fallback if rich is not available
    class FallbackConsole:
        """Fallback console class if rich is not available."""
        def print(self, *args, **kwargs):
            import logging
            logger = logging.getLogger(__name__)
            logger.info(" ".join(str(arg) for arg in args))
        
        @property
        def width(self):
            return 80

    Console = FallbackConsole

from .state_manager import TUIState, TUIScreen

# Create console
console = Console()


def render_header(title: str = "Vibe Check - Project Analysis Tool") -> None:
    """
    Render the application header.

    Args:
        title: Application title
    """
    console.print()
    console.print(f"[bold blue]{title}[/bold blue]")
    console.print("[blue]" + "=" * len(title) + "[/blue]")
    console.print()


def render_footer(state: TUIState) -> None:
    """
    Render the application footer.

    Args:
        state: Application state
    """
    console.print()
    console.print("[dim]" + "-" * console.width + "[/dim]")

    # Show navigation hints
    hints = []

    if state.current_screen != TUIScreen.MAIN_MENU:
        hints.append("[b]b[/b]:back")

    hints.append("[b]q[/b]:quit")

    if state.current_screen == TUIScreen.PROJECT_SELECT:
        hints.append("[b]enter[/b]:select")

    if state.current_screen == TUIScreen.CONFIG:
        hints.append("[b]enter[/b]:confirm")
        hints.append("[b]tab[/b]:next field")

    if state.current_screen == TUIScreen.RESULTS:
        hints.append("[b]v[/b]:visualizations")
        hints.append("[b]s[/b]:summary")
        hints.append("[b]i[/b]:issues")
        hints.append("[b]r[/b]:recommendations")

    console.print(" | ".join(hints))
