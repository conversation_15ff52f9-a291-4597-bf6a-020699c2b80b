"""
Web UI Components
==============

This module provides reusable components for the Vibe Check web UI.
These components are used to render different parts of the UI.
"""

import os

import altair as alt
import pandas as pd
import streamlit as st
from typing import Any, Dict



def render_header() -> None:
    """Render the header of the application."""
    st.title("Vibe Check - Project Analysis Tool")
    st.markdown("""
    Analyze Python projects for code quality, dependencies, complexity,
    type coverage, documentation quality, and more.

    This tool provides fast and efficient analysis with comprehensive reporting.
    """)

    # Add container for visually separating the header
    st.markdown("---")


def render_project_selector(on_select: callable) -> None:
    """
    Render a project directory selector.

    Args:
        on_select: Function to call when a project dir is selected
    """
    st.markdown("## Project Selection")

    col1, col2 = st.columns([3, 1])

    with col1:
        project_path = st.text_input("Project Path",
                                    placeholder="/path/to/your/project")

    with col2:
        if st.button("Analyze", key="analyze_button"):
            if project_path:
                on_select(project_path)
            else:
                st.error("Please enter a project path")

    # Allow configuration options
    with st.expander("Advanced Configuration"):
        output_dir = st.text_input("Output Directory",
                                 placeholder="Leave blank for default")

        file_extensions = st.multiselect(
            "File Extensions",
            options=[".py", ".pyx", ".pyw", ".pyi", ".md", ".rst"],
            default=[".py"]
        )

        analyze_docs = st.checkbox("Analyze Documentation Files", value=False)

        # Tool selection
        st.markdown("### Tools")
        col1, col2 = st.columns(2)

        with col1:
            use_ruff = st.checkbox("Use Ruff Linter", value=True)
            use_mypy = st.checkbox("Use MyPy Type Checker", value=True)
            use_bandit = st.checkbox("Use Bandit Security Analysis", value=True)

        with col2:
            use_complexity = st.checkbox("Use Complexity Analysis", value=True)
            use_markdown = st.checkbox("Use Markdown Analysis", value=False)

        # Analysis options
        st.markdown("### Analysis Options")
        use_import_analysis = st.checkbox("Enable Import Analysis", value=True)
        use_visualizations = st.checkbox("Enable Visualizations", value=False)
        use_simple_analyzer = st.checkbox("Use Simple Analyzer", value=True)

        # Create configuration dictionary
        config = {
            "output_dir": output_dir if output_dir else None,
            "file_extensions": file_extensions,
            "analyze_docs": analyze_docs,
            "tools": {
                "ruff": {"enabled": use_ruff},
                "mypy": {"enabled": use_mypy},
                "bandit": {"enabled": use_bandit},
                "complexity": {"enabled": use_complexity},
                "markdown": {"enabled": use_markdown}
            },
            "use_simple_analyzer": use_simple_analyzer,
            "import_analysis": {"enabled": use_import_analysis},
            "visualization": {"enabled": use_visualizations}
        }

        # Return configuration when user clicks the button
        if st.button("Apply Configuration"):
            return config

    return None


def render_progress(progress: Dict[str, Any]) -> None:
    """
    Render the progress information.

    Args:
        progress: Progress information
    """
    # Extract progress data
    total_files = progress.get("total_files", 0)
    processed_files = progress.get("processed_files", 0)
    current_phase = progress.get("current_phase", "")
    phase_progress = progress.get("phase_progress", 0)

    # Show overall progress
    if total_files > 0:
        st.progress(min(1.0, processed_files / total_files))
        st.text(f"Processing files: {processed_files}/{total_files}")

    # Show phase progress
    if current_phase:
        st.text(f"Current phase: {current_phase}")
        st.progress(phase_progress)


def render_results(metrics: ProjectMetrics) -> None:
    """
    Render the analysis results.

    Args:
        metrics: ProjectMetrics object
    """
    st.markdown("## Analysis Results")

    # Create tabs for different views
    tab_summary, tab_issues, tab_metrics, tab_recommendations = st.tabs([
        "Summary", "Issues", "Metrics", "Recommendations"
    ])

    with tab_summary:
        render_summary(metrics)

    with tab_issues:
        render_issues(metrics)

    with tab_metrics:
        render_metrics(metrics)

    with tab_recommendations:
        render_recommendations(metrics)


def render_summary(metrics: ProjectMetrics) -> None:
    """
    Render the summary tab.

    Args:
        metrics: ProjectMetrics object
    """
    st.markdown("### Project Summary")

    # Key metrics in a grid
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("Total Files", metrics.total_file_count)
        st.metric("Python Files", metrics.python_file_count)

    with col2:
        st.metric("Documentation Files", metrics.markdown_file_count)
        st.metric("Total Directories", metrics.total_directory_count)

    with col3:
        st.metric("Avg. Complexity", f"{metrics.avg_complexity:.2f}")
        st.metric("Avg. Doc Coverage", f"{metrics.avg_doc_coverage:.2f}%")

    # Top complexity files
    st.markdown("### Most Complex Files")
    if metrics.complexity_scores:
        complex_files = sorted(
            metrics.complexity_scores.items(),
            key=lambda x: x[1],
            reverse=True
        )[:5]

        for file_path, score in complex_files:
            st.text(f"{file_path} (Complexity: {score})")
    else:
        st.info("No complexity data available")


def render_issues(metrics: ProjectMetrics) -> None:
    """
    Render the issues tab.

    Args:
        metrics: ProjectMetrics object
    """
    st.markdown("### Issues Found")

    # Collect issues from all files
    all_issues = []
    for file_path, file_metric in metrics.files.items():
        for tool_name, tool_result in file_metric.tool_results.items():
            if "issues" in tool_result:
                for issue in tool_result["issues"]:
                    all_issues.append({
                        "file": file_path,
                        "tool": tool_name,
                        "code": issue.get("code", "unknown"),
                        "message": issue.get("message", "Unknown issue"),
                        "line": issue.get("line", 0),
                        "severity": issue.get("severity", "MEDIUM")
                    })

    if not all_issues:
        st.success("No issues found! 🎉")
        return

    # Convert to DataFrame for easier filtering
    issues_df = pd.DataFrame(all_issues)

    # Issue filtering UI
    col1, col2, col3 = st.columns(3)

    with col1:
        severity_filter = st.multiselect(
            "Filter by Severity",
            options=sorted(issues_df["severity"].unique()),
            default=sorted(issues_df["severity"].unique())
        )

    with col2:
        tool_filter = st.multiselect(
            "Filter by Tool",
            options=sorted(issues_df["tool"].unique()),
            default=sorted(issues_df["tool"].unique())
        )

    with col3:
        # Extract first letter of code for issue type
        if "code" in issues_df.columns and not issues_df["code"].isna().all():
            issues_df["type"] = issues_df["code"].astype(str).str[0]
            type_filter = st.multiselect(
                "Filter by Type",
                options=sorted(issues_df["type"].unique()),
                default=sorted(issues_df["type"].unique())
            )
        else:
            type_filter = []

    # Apply filters
    filtered_df = issues_df
    if severity_filter:
        filtered_df = filtered_df[filtered_df["severity"].isin(severity_filter)]
    if tool_filter:
        filtered_df = filtered_df[filtered_df["tool"].isin(tool_filter)]
    if type_filter and "type" in filtered_df.columns:
        filtered_df = filtered_df[filtered_df["type"].isin(type_filter)]

    # Show filtered issues
    st.markdown(f"### Showing {len(filtered_df)} issues")

    for _, issue in filtered_df.iterrows():
        severity_color = {
            "HIGH": "red",
            "MEDIUM": "orange",
            "LOW": "blue"
        }.get(issue["severity"], "gray")

        st.markdown(f"""
        <div style="border-left: 4px solid {severity_color}; padding-left: 10px; margin-bottom: 10px;">
            <strong>{issue['file']}:{issue['line']}</strong> -
            <span style="color: {severity_color};">{issue['severity']}</span>
            <br/>
            <code>{issue['code']}</code>: {issue['message']}
            <br/>
            <small>Reported by: {issue['tool']}</small>
        </div>
        """, unsafe_allow_html=True)


def render_metrics(metrics: ProjectMetrics) -> None:
    """
    Render the metrics tab.

    Args:
        metrics: ProjectMetrics object
    """
    st.markdown("### Project Metrics")

    # Create metric visualizations
    if metrics.complexity_scores:
        st.markdown("#### File Complexity")

        # Convert to DataFrame
        complexity_data = pd.DataFrame({
            "file": list(metrics.complexity_scores.keys()),
            "complexity": list(metrics.complexity_scores.values())
        })

        # Create bar chart
        chart = alt.Chart(complexity_data).mark_bar().encode(
            x=alt.X("complexity:Q", title="Complexity Score"),
            y=alt.Y("file:N", title="File", sort="-x")
        ).properties(
            title="File Complexity",
            height=min(500, len(complexity_data) * 25)
        )

        st.altair_chart(chart, use_container_width=True)

    # Type coverage
    if metrics.type_coverage:
        st.markdown("#### Type Coverage")

        # Convert to DataFrame
        type_data = pd.DataFrame({
            "file": list(metrics.type_coverage.keys()),
            "coverage": list(metrics.type_coverage.values())
        })

        # Create bar chart
        chart = alt.Chart(type_data).mark_bar().encode(
            x=alt.X("coverage:Q", title="Type Coverage (%)", scale=alt.Scale(domain=[0, 100])),
            y=alt.Y("file:N", title="File", sort="-x")
        ).properties(
            title="Type Coverage",
            height=min(500, len(type_data) * 25)
        )

        st.altair_chart(chart, use_container_width=True)

    # Documentation coverage
    if metrics.doc_coverage:
        st.markdown("#### Documentation Coverage")

        # Convert to DataFrame
        doc_data = pd.DataFrame({
            "file": list(metrics.doc_coverage.keys()),
            "coverage": list(metrics.doc_coverage.values())
        })

        # Create bar chart
        chart = alt.Chart(doc_data).mark_bar().encode(
            x=alt.X("coverage:Q", title="Documentation Coverage (%)", scale=alt.Scale(domain=[0, 100])),
            y=alt.Y("file:N", title="File", sort="-x")
        ).properties(
            title="Documentation Coverage",
            height=min(500, len(doc_data) * 25)
        )

        st.altair_chart(chart, use_container_width=True)


def render_recommendations(metrics: ProjectMetrics) -> None:
    """
    Render the recommendations tab.

    Args:
        metrics: ProjectMetrics object
    """
    st.markdown("### Recommendations")

    # Generate recommendations based on metrics
    recommendations = []

    # Check for circular dependencies
    circular_deps = metrics.get_circular_dependencies()
    if circular_deps:
        recommendations.append({
            "title": "Resolve Circular Dependencies",
            "description": f"Found {len(circular_deps)} circular dependencies. These can make the code harder to maintain.",
            "actions": [
                "Identify the root cause of the circular dependencies",
                "Consider using dependency injection or other design patterns",
                "Refactor the code to break the cycles"
            ],
            "severity": "high"
        })

    # Check for high complexity files
    high_complexity_files = [(p, c) for p, c in metrics.complexity_scores.items() if c > 10]
    if high_complexity_files:
        file_list = ", ".join([p for p, _ in high_complexity_files[:3]])
        if len(high_complexity_files) > 3:
            file_list += f" and {len(high_complexity_files) - 3} more"

        recommendations.append({
            "title": "Reduce Code Complexity",
            "description": f"Found {len(high_complexity_files)} files with high complexity. High complexity makes code harder to maintain and test.",
            "actions": [
                "Break down complex functions into smaller, more focused functions",
                "Refactor complex conditional logic",
                "Consider using design patterns to simplify the code"
            ],
            "examples": file_list,
            "severity": "medium"
        })

    # Check for low type coverage
    low_type_coverage_files = [(p, c) for p, c in metrics.type_coverage.items() if c < 50]
    if low_type_coverage_files:
        recommendations.append({
            "title": "Improve Type Coverage",
            "description": f"Found {len(low_type_coverage_files)} files with low type annotation coverage. Type annotations help catch errors early.",
            "actions": [
                "Add type annotations to function parameters and return values",
                "Use a type checker like mypy in your development workflow",
                "Consider using a tool to automatically add annotations"
            ],
            "severity": "medium"
        })

    # Render recommendations
    if not recommendations:
        st.success("No specific recommendations. The project looks good! 🎉")
    else:
        for rec in recommendations:
            severity_color = {
                "high": "red",
                "medium": "orange",
                "low": "blue"
            }.get(rec["severity"], "gray")

            st.markdown(f"""
            <div style="border-left: 4px solid {severity_color}; padding-left: 10px; margin-bottom: 20px;">
                <h4>{rec['title']}</h4>
                <p>{rec['description']}</p>
            </div>
            """, unsafe_allow_html=True)

            st.markdown("**Recommended Actions:**")
            for action in rec["actions"]:
                st.markdown(f"- {action}")

            if "examples" in rec:
                st.markdown(f"**Examples**: {rec['examples']}")

            st.markdown("---")


def render_visualization(metrics: ProjectMetrics, view_type: str = "dependency") -> None:
    """
    Render a specific visualization.

    Args:
        metrics: ProjectMetrics object
        view_type: Type of visualization to render
    """
    st.markdown("## Visualizations")

    # Select visualization type
    view_options = {
        "dependency": "Dependency Graph",
        "complexity": "Complexity Heatmap",
        "coverage": "Coverage Chart"
    }

    selected_view = st.selectbox(
        "Select Visualization",
        options=list(view_options.keys()),
        format_func=lambda x: view_options[x],
        index=list(view_options.keys()).index(view_type) if view_type in view_options else 0
    )

    # Render the selected visualization
    if selected_view == "dependency":
        render_dependency_graph(metrics)
    elif selected_view == "complexity":
        render_complexity_heatmap(metrics)
    elif selected_view == "coverage":
        render_coverage_chart(metrics)


def render_dependency_graph(metrics: ProjectMetrics) -> None:
    """
    Render a dependency graph visualization.

    Args:
        metrics: ProjectMetrics object
    """
    st.markdown("### Module Dependency Graph")

    if not hasattr(metrics, "dependency_graph") or len(metrics.dependency_graph) == 0:
        st.info("No dependency data available. Run the analysis with a tool that generates dependency information.")
        return

    # Generate a dot file to render the dependency graph
    import networkx as nx

    # Create a simplified graph for visualization
    graph = metrics.dependency_graph
    simplified_graph = nx.DiGraph()

    # Convert paths to file names
    for node in graph.nodes():
        simplified_graph.add_node(os.path.basename(node))

    for source, target in graph.edges():
        simplified_graph.add_edge(os.path.basename(source), os.path.basename(target))

    # Find circular dependencies
    try:
        cycles = list(nx.simple_cycles(simplified_graph))
    except:
        cycles = []

    # Display circular dependencies if found
    if cycles:
        st.warning(f"Found {len(cycles)} circular dependencies")
        with st.expander("Show circular dependencies"):
            for i, cycle in enumerate(cycles):
                st.text(f"{i+1}. {' -> '.join(cycle)} -> {cycle[0]}")

    # Generate the graph visualization using graphviz
    try:
        import graphviz

        dot = graphviz.Digraph()
        dot.attr(rankdir="LR")

        # Add nodes
        for node in simplified_graph.nodes():
            dot.node(node)

        # Add edges
        for source, target in simplified_graph.edges():
            dot.edge(source, target)

        # Display the graph
        st.graphviz_chart(dot)
    except Exception as e:
        st.error(f"Error generating dependency graph: {e}")
        st.info("Make sure graphviz is installed on your system.")


def render_complexity_heatmap(metrics: ProjectMetrics) -> None:
    """
    Render a complexity heatmap visualization.

    Args:
        metrics: ProjectMetrics object
    """
    st.markdown("### Code Complexity Heatmap")

    if not metrics.complexity_scores:
        st.info("No complexity data available. Run the analysis with the complexity tool enabled.")
        return

    # Convert to DataFrame
    data = []
    for file_path, complexity in metrics.complexity_scores.items():
        file_name = os.path.basename(file_path)
        data.append({"file": file_name, "complexity": complexity})

    df = pd.DataFrame(data)

    # Create a treemap chart
    chart = alt.Chart(df).mark_rect().encode(
        x=alt.X('file:N', title=None),
        y=alt.Y('complexity:Q', title="Complexity"),
        color=alt.Color('complexity:Q', scale=alt.Scale(scheme='viridis')),
        tooltip=['file', 'complexity']
    ).properties(
        title="Code Complexity Heatmap"
    )

    st.altair_chart(chart, use_container_width=True)


def render_coverage_chart(metrics: ProjectMetrics) -> None:
    """
    Render a coverage chart visualization.

    Args:
        metrics: ProjectMetrics object
    """
    st.markdown("### Coverage Analysis")

    # Prepare data
    data = []

    for file_path in metrics.files.keys():
        file_name = os.path.basename(file_path)
        row = {
            "file": file_name,
            "type_coverage": metrics.type_coverage.get(file_path, 0),
            "doc_coverage": metrics.doc_coverage.get(file_path, 0)
        }
        data.append(row)

    if not data:
        st.info("No coverage data available. Run the analysis with type checking and documentation tools enabled.")
        return

    # Convert to DataFrame
    df = pd.DataFrame(data)

    # Melt the DataFrame for easier plotting
    melted_df = pd.melt(
        df,
        id_vars=["file"],
        value_vars=["type_coverage", "doc_coverage"],
        var_name="coverage_type",
        value_name="coverage"
    )

    # Create a grouped bar chart
    chart = alt.Chart(melted_df).mark_bar().encode(
        x=alt.X('file:N', title="File"),
        y=alt.Y('coverage:Q', title="Coverage (%)"),
        color=alt.Color('coverage_type:N', title="Coverage Type"),
        tooltip=['file', 'coverage_type', 'coverage']
    ).properties(
        title="Type and Documentation Coverage"
    )

    st.altair_chart(chart, use_container_width=True)
