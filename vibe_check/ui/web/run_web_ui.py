#!/usr/bin/env python3
"""
Run Vibe Check Web UI
============

This script launches the Vibe Check Web UI application using Streamlit.

Usage:
    python -m vibe_check.ui.web.run_web_ui
    # or
    python path/to/run_web_ui.py
"""

import logging
import sys
from pathlib import Path

# Add the project root to the path
# This is needed when running this script directly
project_root = Path(__file__).parent.parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# Import streamlit
try:
    import streamlit as st
except ImportError:
    logger = logging.getLogger(__name__)
    logger.error("Streamlit not found. Please install it with: pip install streamlit")
    sys.exit(1)

# Import the app module
from vibe_check.ui.web.app import run_app


def main():
    """
    Main entry point for running the web UI.

    This function configures logging and launches the web UI.
    """
    # Configure logging
    log_dir = Path.home() / ".vibe_check" / "logs"
    log_dir.mkdir(parents=True, exist_ok=True)

    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(log_dir / "vibe_check_web_ui.log")
        ]
    )

    logger = logging.getLogger("vibe_check_web_ui")
    logger.info("Starting Vibe Check Web UI")

    # Prepare Streamlit command
    current_file = Path(__file__).absolute()
    module_path = ".".join(current_file.with_suffix("").parts[-4:])

    # Construct streamlit command
    streamlit_cmd = [
        "streamlit",
        "run",
        str(current_file),
        "--",  # Pass remaining args to the script
    ]

    # Add any command line arguments
    streamlit_cmd.extend(sys.argv[1:])

    # Log instructions
    logger.info("Launching Vibe Check Web UI...")
    logger.info("Open your web browser to view the UI (if not opened automatically)")
    logger.info("Use Ctrl+C to stop the server")

    # Run the app
    run_app()


if __name__ == "__main__":
    main()
