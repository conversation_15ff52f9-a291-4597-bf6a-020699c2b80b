"""
CLI Commands
=========

This module provides the command functions for the CLI.
"""

import os
import sys
from pathlib import Path
from typing import Optional, Dict, Any, List

import click

from ..core.config import load_config
from ..plugins.manager import list_plugins
from ..core.error_handling.error_aggregator import (
    reset_error_aggregator,
    collect_errors_from_analysis_results
)


def analyze_command(project_path: str,
                   config_path: Optional[str] = None,
                   output_dir: Optional[str] = None,
                   verbose: bool = False,
                   quiet: bool = False,
                   config_override: Optional[Dict[str, Any]] = None,
                   analyze_trends: bool = False,
                   report_progress: bool = False,
                   use_simple_analyzer: bool = True,  # Always use simple analyzer now
                   profile: str = "standard",  # Analysis profile
                   **kwargs: Any) -> Dict[str, Any]:
    """
    Run the analyze command.

    Args:
        project_path: Path to the project to analyze
        config_path: Optional path to a configuration file
        output_dir: Optional directory to write output files to
        verbose: Whether to enable verbose output
        quiet: Whether to suppress output
        config_override: Optional dictionary to override configuration values
        analyze_trends: Whether to analyze trends compared to previous runs
        report_progress: Whether to report progress between analyses with the same output directory
        use_simple_analyzer: Whether to use the simple analyzer (always True now)
        profile: Analysis profile to use (minimal, standard, comprehensive, security, performance, maintainability)
        **kwargs: Additional keyword arguments

    Returns:
        Analysis results
    """
    import logging
    import traceback
    import time
    from pathlib import Path

    # Set up a logger for this function
    logger = logging.getLogger("vibe_check_cli.analyze_command")

    try:
        start_time = time.time()
        logger.debug("Starting analyze_command")
        logger.debug(f"Project path: {project_path}")
        logger.debug(f"Config path: {config_path}")
        logger.debug(f"Output directory: {output_dir}")
        logger.debug(f"Verbose: {verbose}, Quiet: {quiet}")
        logger.debug(f"Config override: {config_override}")
        logger.debug(f"Analyze trends: {analyze_trends}, Report progress: {report_progress}")
        logger.debug(f"Use simple analyzer: {use_simple_analyzer}")
        logger.debug(f"Additional kwargs: {kwargs}")

        # Reset error aggregator for new analysis
        reset_error_aggregator()

        # Set up logging
        if verbose:
            from ..core.logging import setup_logging
            logger.debug("Setting up logging with debug=True")
            setup_logging(debug=True)
        elif quiet:
            from ..core.logging import setup_logging
            logger.debug("Setting up logging with quiet=True")
            setup_logging(quiet=True)

        # Validate project path
        import os
        if not os.path.exists(project_path):
            logger.error(f"Project path does not exist: {project_path}")
            return {
                "error": f"Project path does not exist: {project_path}",
                "error_type": "path_not_found",
                "suggestions": [
                    "Check the path spelling and try again",
                    "Ensure the path is accessible from your current location",
                    "Use an absolute path instead of a relative path"
                ]
            }

        if not os.path.isdir(project_path):
            logger.error(f"Project path is not a directory: {project_path}")
            return {
                "error": f"Project path is not a directory: {project_path}",
                "error_type": "invalid_directory",
                "suggestions": [
                    "Ensure you're pointing to a directory, not a file",
                    "Check that the path contains a Python project"
                ]
            }

        # Create output directory if it doesn't exist
        if output_dir and not os.path.exists(output_dir):
            logger.debug(f"Creating output directory: {output_dir}")
            os.makedirs(output_dir, exist_ok=True)

        # Load configuration if specified
        config = None
        if config_path:
            from ..core.config import load_config
            logger.debug(f"Loading configuration from {config_path}")
            config = load_config(config_path)
            logger.debug(f"Loaded configuration: {config}")

        # Apply preset if specified
        if config_override and "preset" in config_override:
            preset_name = config_override["preset"]
            logger.debug(f"Applying preset: {preset_name}")

            try:
                from ..core.config import load_preset
                preset_config = load_preset(preset_name)
                logger.debug(f"Loaded preset configuration: {preset_config}")

                # Merge preset with existing config
                if config is None:
                    config = preset_config
                else:
                    # Deep merge the configurations
                    from ..core.utils.dict_utils import deep_merge
                    config = deep_merge(config, preset_config)
                    logger.debug(f"Merged configuration: {config}")
            except Exception as e:
                logger.error(f"Error loading preset {preset_name}: {e}")
                logger.error(traceback.format_exc())
                return {
                    "error": f"Error loading preset {preset_name}: {e}",
                    "error_type": "preset_load_error",
                    "error_details": traceback.format_exc(),
                    "suggestions": [
                        f"Check if preset '{preset_name}' exists",
                        "Try using a different preset (minimal, standard, comprehensive)",
                        "Run without a preset to use default settings"
                    ]
                }

        # Apply other config overrides
        if config_override:
            if config is None:
                config = {}

            # Remove preset key as it's already been processed
            config_override_copy = config_override.copy()
            if "preset" in config_override_copy:
                del config_override_copy["preset"]

            # Apply remaining overrides
            if config_override_copy:
                from ..core.utils.dict_utils import deep_merge
                config = deep_merge(config, config_override_copy)
                logger.debug(f"Applied config overrides: {config}")

        # Check if VCS mode is enabled
        vcs_mode = kwargs.get('vcs_mode', False)

        if vcs_mode:
            logger.info("Using VCS (Vibe Check Standalone) mode with built-in analysis rules")

            try:
                from ..core.vcs.engine import VibeCheckEngine
                from ..core.vcs.models import EngineMode, AnalysisTarget
                from ..core.vcs.config import VCSConfig
                import asyncio

                # Create VCS configuration
                vcs_config = VCSConfig(
                    mode=EngineMode.STANDALONE,
                    cache_enabled=True,
                    performance_mode=profile in ["minimal", "performance"]
                )

                # Create and start VCS engine
                engine = VibeCheckEngine(mode=EngineMode.STANDALONE, config=vcs_config)

                async def run_vcs_analysis():
                    await engine.start()
                    try:
                        # Analyze all Python files in the project with filtering
                        from pathlib import Path
                        import os
                        import fnmatch

                        project_path_obj = Path(project_path)
                        results = []

                        # Default ignore patterns (like .gitignore)
                        ignore_patterns = [
                            "venv/*", "env/*", ".venv/*", ".env/*",
                            "__pycache__/*", "*.pyc", "*.pyo", "*.pyd",
                            ".git/*", ".svn/*", ".hg/*",
                            "node_modules/*", ".tox/*", ".pytest_cache/*",
                            "build/*", "dist/*", "*.egg-info/*",
                            ".coverage", ".mypy_cache/*", ".ruff_cache/*",
                            "site-packages/*", "lib/python*/site-packages/*"
                        ]

                        def should_ignore(file_path: Path) -> bool:
                            """Check if file should be ignored based on patterns."""
                            relative_path = str(file_path.relative_to(project_path_obj))
                            for pattern in ignore_patterns:
                                if fnmatch.fnmatch(relative_path, pattern) or fnmatch.fnmatch(relative_path, f"*/{pattern}"):
                                    return True
                            return False

                        total_files = 0
                        analyzed_files = 0

                        # Count total files first for progress
                        all_py_files = [f for f in project_path_obj.rglob("*.py") if f.is_file() and not should_ignore(f)]
                        total_files = len(all_py_files)

                        if not quiet:
                            print(f"Found {total_files} Python files to analyze...")

                        for py_file in all_py_files:
                            try:
                                target = AnalysisTarget.from_file(py_file)
                                result = await engine.analyze(target)
                                analyzed_files += 1

                                # Get analysis time from performance monitor
                                try:
                                    perf_stats = engine.performance_monitor.get_performance_report()
                                    analysis_time = perf_stats.get('summary', {}).get('average_execution_time', 0.0)
                                except:
                                    analysis_time = 0.0

                                results.append({
                                    "file": str(py_file.relative_to(project_path_obj)),
                                    "issues": len(result.issues),
                                    "rules_executed": len(result.rules_executed),
                                    "analysis_time": analysis_time,
                                    "success": result.success,
                                    "issues_detail": [
                                        {
                                            "rule_id": issue.rule_id,
                                            "category": issue.category.value,
                                            "severity": issue.severity.value,
                                            "line": issue.line,
                                            "column": issue.column,
                                            "message": issue.message,
                                            "auto_fixable": issue.auto_fixable,
                                            "fix_suggestion": issue.fix_suggestion
                                        }
                                        for issue in result.issues
                                    ]
                                })

                                # Progress indication
                                if not quiet and analyzed_files % 10 == 0:
                                    print(f"Analyzed {analyzed_files}/{total_files} files...")

                            except Exception as e:
                                logger.warning(f"Failed to analyze {py_file}: {e}")
                                results.append({
                                    "file": str(py_file.relative_to(project_path_obj)),
                                    "issues": 0,
                                    "rules_executed": 0,
                                    "analysis_time": 0.0,
                                    "success": False,
                                    "error": str(e)
                                })

                        if not quiet:
                            print(f"Analysis complete: {analyzed_files}/{total_files} files analyzed successfully")

                        return results
                    finally:
                        await engine.stop()

                # Run the async analysis
                vcs_results = asyncio.run(run_vcs_analysis())

                # Convert to format expected by CLI and add summary
                total_issues = sum(r["issues"] for r in vcs_results)
                total_files = len(vcs_results)
                successful_files = len([r for r in vcs_results if r.get("success", True)])

                # Categorize issues by severity and category
                issues_by_severity = {"ERROR": 0, "WARNING": 0, "INFO": 0, "HINT": 0}
                issues_by_category = {"style": 0, "security": 0, "complexity": 0, "documentation": 0, "imports": 0, "types": 0}
                auto_fixable_count = 0

                for file_result in vcs_results:
                    for issue in file_result.get("issues_detail", []):
                        severity = issue.get("severity", "INFO")
                        category = issue.get("category", "unknown")
                        if severity in issues_by_severity:
                            issues_by_severity[severity] += 1
                        if category in issues_by_category:
                            issues_by_category[category] += 1
                        if issue.get("auto_fixable", False):
                            auto_fixable_count += 1

                # Display summary if not quiet
                if not quiet:
                    print("\n" + "="*60)
                    print("VCS ANALYSIS SUMMARY")
                    print("="*60)
                    print(f"Files analyzed: {successful_files}/{total_files}")
                    print(f"Total issues found: {total_issues}")
                    print(f"Auto-fixable issues: {auto_fixable_count}")

                    if total_issues > 0:
                        print(f"\nIssues by severity:")
                        for severity, count in issues_by_severity.items():
                            if count > 0:
                                print(f"  {severity}: {count}")

                        print(f"\nIssues by category:")
                        for category, count in issues_by_category.items():
                            if count > 0:
                                print(f"  {category}: {count}")

                    # Get final performance stats
                    try:
                        final_perf = engine.performance_monitor.get_performance_report()
                        avg_time = final_perf.get('summary', {}).get('average_execution_time', 0)
                        total_time = final_perf.get('summary', {}).get('total_execution_time', 0)
                        print(f"\nPerformance:")
                        print(f"  Total analysis time: {total_time:.2f}s")
                        print(f"  Average per file: {avg_time:.3f}s")
                    except:
                        pass

                    print("="*60)

                metrics = {
                    "vcs_mode": True,
                    "total_files_analyzed": total_files,
                    "successful_files": successful_files,
                    "total_issues_found": total_issues,
                    "auto_fixable_issues": auto_fixable_count,
                    "issues_by_severity": issues_by_severity,
                    "issues_by_category": issues_by_category,
                    "files": vcs_results,
                    "analysis_engine": "VCS (Vibe Check Standalone)",
                    "rules_available": 32,
                    "profile": profile
                }

            except Exception as e:
                logger.error(f"Error in VCS analysis: {e}")
                logger.error(traceback.format_exc())
                return {
                    "error": f"Error in VCS analysis: {e}",
                    "error_type": "vcs_analysis_error",
                    "error_details": traceback.format_exc(),
                    "suggestions": [
                        "Try running without --vcs-mode flag",
                        "Check that the project contains valid Python files",
                        "Run with --verbose for more detailed logs"
                    ]
                }
        else:
            # Use simple analyzer (original behavior)
            logger.info("Using simplified analysis engine")

            try:
                from ..core.simple_analyzer import simple_analyze_project

                # Add progress indicators
                print("🔍 Starting project analysis...")
                print(f"📁 Project: {project_path}")
                print(f"⚙️  Profile: {profile}")
                print("⏳ Analysis in progress...")

                start_time = time.time()

                # Run the simple analyzer with profile
                metrics = simple_analyze_project(
                    project_path=project_path,
                    output_dir=output_dir,
                    config=config,
                    profile=profile
                )

                # Show completion with timing
                elapsed_time = time.time() - start_time
                print(f"✅ Analysis completed in {elapsed_time:.2f} seconds")

            except Exception as e:
                logger.error(f"Error in simple_analyze_project: {e}")
                logger.error(traceback.format_exc())

                # Categorize the error for better user guidance
                error_msg = str(e).lower()
                if "memory" in error_msg or "out of memory" in error_msg:
                    error_type = "memory_error"
                    suggestions = [
                        "Try analyzing a smaller project or subset of files",
                        "Use --profile minimal to reduce memory usage",
                        "Close other applications to free up memory"
                    ]
                elif "permission" in error_msg or "access" in error_msg:
                    error_type = "permission_error"
                    suggestions = [
                        "Check file and directory permissions",
                        "Run with appropriate permissions",
                        "Try a different output directory"
                    ]
                elif "timeout" in error_msg:
                    error_type = "timeout_error"
                    suggestions = [
                        "Try analyzing a smaller project",
                        "Use --profile minimal for faster analysis",
                        "Check system resources"
                    ]
                else:
                    error_type = "analysis_error"
                    suggestions = [
                        "Run with --verbose for more detailed logs",
                        "Try analyzing a smaller project first",
                        "Check that the project contains valid Python files"
                    ]

                return {
                    "error": f"Error in analysis: {e}",
                    "error_type": error_type,
                    "error_details": traceback.format_exc(),
                    "suggestions": suggestions
                }

        end_time = time.time()
        logger.info(f"Analysis completed in {end_time - start_time:.2f} seconds")

        # Collect errors from analysis results
        collect_errors_from_analysis_results(metrics)

        # Convert metrics to dictionary if needed
        if not isinstance(metrics, dict):
            # Try to convert to dictionary using to_dict() method if available
            try:
                if hasattr(metrics, 'to_dict') and callable(getattr(metrics, 'to_dict')):
                    metrics_dict = metrics.to_dict()
                    return metrics_dict
                else:
                    # Fallback to __dict__ if no to_dict() method
                    metrics_dict = metrics.__dict__ if hasattr(metrics, '__dict__') else {"metrics": metrics}
                    return metrics_dict
            except Exception as e:
                logger.warning(f"Could not convert metrics to dictionary: {e}")
                # Return as is and let the formatter handle it
                return {"metrics": metrics}
        return metrics

    except Exception as e:
        logger.error(f"Error in analyze_command: {e}")
        logger.error(traceback.format_exc())
        return {"error": str(e), "error_details": traceback.format_exc()}


def tui_command(project_path: str,
               config_path: Optional[str] = None) -> None:
    """
    Run the TUI command with intelligent dependency management.

    Args:
        project_path: Path to the project to analyze
        config_path: Optional path to a configuration file
    """
    from ..core.dependency_manager import dependency_manager

    # Check if TUI dependencies are available
    available, missing = dependency_manager.check_interface_dependencies("tui")

    if not available:
        print(f"\n🎨 TUI Interface - Missing Dependencies")
        print("=" * 50)
        print("The Terminal User Interface requires additional packages.")
        print(f"Missing: {', '.join(missing)}")

        # Offer automatic installation
        if dependency_manager.auto_install_dependencies("tui", prompt_user=True):
            print("🔄 Retrying TUI launch...")
        else:
            print("\n💡 Manual installation:")
            print("   pip install vibe-check[tui]")
            print("   # or")
            print("   pip install textual>=0.9.0 keyboard>=0.13.5")
            sys.exit(1)

    # Try to launch TUI
    try:
        from ..ui.tui import run_tui
        print("🚀 Launching TUI interface...")
        run_tui(project_path=project_path, config_path=config_path)
    except ImportError as e:
        import logging
        logger = logging.getLogger("vibe_check_cli.tui")
        logger.error(f"TUI launch failed: {e}")
        print(f"❌ TUI launch failed: {e}")
        print("💡 Try reinstalling: pip install --force-reinstall vibe-check[tui]")
        sys.exit(1)


def web_command(project_path: str,
               config_path: Optional[str] = None,
               host: str = "localhost",
               port: int = 8000) -> None:
    """
    Run the web command with intelligent dependency management.

    Args:
        project_path: Path to the project to analyze
        config_path: Optional path to a configuration file
        host: Host to bind to
        port: Port to bind to
    """
    from ..core.dependency_manager import dependency_manager

    # Check if Web UI dependencies are available
    available, missing = dependency_manager.check_interface_dependencies("web")

    if not available:
        print(f"\n🌐 Web Interface - Missing Dependencies")
        print("=" * 50)
        print("The Web User Interface requires additional packages.")
        print(f"Missing: {', '.join(missing)}")

        # Offer automatic installation
        if dependency_manager.auto_install_dependencies("web", prompt_user=True):
            print("🔄 Retrying Web UI launch...")
        else:
            print("\n💡 Manual installation:")
            print("   pip install vibe-check[web]")
            print("   # or")
            print("   pip install streamlit>=1.10.0 altair>=4.2.0 pandas>=1.4.0")
            sys.exit(1)

    # Try to launch Web UI
    try:
        from ..ui.web import run_web_server
        print(f"🚀 Launching Web interface at http://{host}:{port}")
        run_web_server(project_path, config_path, host, port)
    except ImportError as e:
        import logging
        logger = logging.getLogger("vibe_check_cli.web")
        logger.error(f"Web UI launch failed: {e}")
        print(f"❌ Web UI launch failed: {e}")
        print("💡 Try reinstalling: pip install --force-reinstall vibe-check[web]")
        sys.exit(1)


def debug_command(project_path: str,
               output_dir: Optional[str] = None,
               verbose: bool = False,
               timeout: float = 120.0) -> Dict[str, Any]:
    """
    Run the debug command (simplified - actor system removed).

    This command now provides basic debugging information for the analysis process.

    Args:
        project_path: Path to the project to analyze
        output_dir: Optional directory to write output files to
        verbose: Whether to enable verbose output
        timeout: Maximum time in seconds to wait for analysis

    Returns:
        Dictionary with debugging results
    """
    import logging
    import traceback
    import time
    from pathlib import Path

    # Set up a logger for this function
    logger = logging.getLogger("vibe_check_cli.debug_command")

    try:
        start_time = time.time()
        logger.info("Starting debug_command")
        logger.info(f"Project path: {project_path}")
        logger.info(f"Output directory: {output_dir}")
        logger.info(f"Verbose: {verbose}")
        logger.info(f"Timeout: {timeout}")

        # Create output directory if it doesn't exist
        if output_dir:
            output_dir_path = Path(output_dir)
            output_dir_path.mkdir(parents=True, exist_ok=True)
        else:
            # Use default output directory
            output_dir_path = Path("vibe_check_debug")
            output_dir_path.mkdir(parents=True, exist_ok=True)
            output_dir = str(output_dir_path)

        # Run analysis with debugging enabled
        logger.info("Running analysis with debugging enabled")

        try:
            from ..core.simple_analyzer import simple_analyze_project

            # Run analysis to get project information
            logger.info("Running analysis with simple analyzer")
            result = simple_analyze_project(
                project_path=project_path,
                output_dir=output_dir,
                config={"debug": True, "verbose": verbose}
            )

            logger.info(f"Analysis completed: {len(result.files)} files analyzed")

            end_time = time.time()
            debug_info = {
                "status": "success",
                "analysis_time": end_time - start_time,
                "files_analyzed": len(result.files),
                "total_lines": result.total_line_count,
                "issues_found": result.issue_count,
                "output_directory": output_dir
            }

            logger.info(f"Debug analysis completed successfully in {end_time - start_time:.2f} seconds")
            return debug_info

        except Exception as e:
            logger.error(f"Error in debug analysis: {e}")
            logger.error(traceback.format_exc())
            return {
                "error": f"Error in debug analysis: {e}",
                "error_details": traceback.format_exc()
            }

            # Debug analysis is now complete (actor system removed)
            return debug_info

        except Exception as e:
            logger.error(f"Error in debug analysis: {e}")
            logger.error(traceback.format_exc())
            return {
                "error": f"Error in debug analysis: {e}",
                "error_details": traceback.format_exc()
            }

    except Exception as e:
        logger.error(f"Error in debug_command: {e}")
        logger.error(traceback.format_exc())
        return {"error": str(e), "error_details": traceback.format_exc()}


def plugin_command(action: str, plugin_name: Optional[str] = None) -> None:
    """
    Run the plugin command.

    Args:
        action: Action to perform (list, install, uninstall)
        plugin_name: Name of the plugin
    """
    import logging
    logger = logging.getLogger("vibe_check_cli.plugin")

    if action == "list":
        plugins = list_plugins()
        if not plugins:
            logger.info("No plugins installed.")
            click.echo("No plugins installed.")
            return

        logger.info(f"Found {len(plugins)} installed plugins")
        click.echo("Installed plugins:")
        for plugin in plugins:
            click.echo(f"  {plugin}")
    elif action == "install":
        if not plugin_name:
            logger.error("Plugin name not specified")
            click.echo("Please specify a plugin name.", err=True)
            sys.exit(1)

        try:
            # Plugin system is not implemented yet
            logger.warning(f"Plugin installation not implemented: {plugin_name}")
            click.echo(f"Plugin installation is not yet implemented: {plugin_name}", err=True)
            sys.exit(1)
        except Exception as e:
            logger.error(f"Failed to install plugin {plugin_name}: {e}")
            click.echo(f"Failed to install plugin {plugin_name}: {e}", err=True)
            sys.exit(1)
    elif action == "uninstall":
        if not plugin_name:
            logger.error("Plugin name not specified")
            click.echo("Please specify a plugin name.", err=True)
            sys.exit(1)

        try:
            # Plugin system is not implemented yet
            logger.warning(f"Plugin uninstallation not implemented: {plugin_name}")
            click.echo(f"Plugin uninstallation is not yet implemented: {plugin_name}", err=True)
            sys.exit(1)
        except Exception as e:
            logger.error(f"Failed to uninstall plugin {plugin_name}: {e}")
            click.echo(f"Failed to uninstall plugin {plugin_name}: {e}", err=True)
            sys.exit(1)
    else:
        logger.error(f"Unknown plugin action: {action}")
        click.echo(f"Unknown action: {action}", err=True)
        click.echo("Available actions: list, install, uninstall")
        sys.exit(1)


def install_hooks_command(
    project_path: str = ".",
    validation_level: str = "standard",
    merge_existing: bool = True,
    dry_run: bool = False,
    custom_args: Optional[List[str]] = None
) -> None:
    """
    Install Vibe Check as a pre-commit hook in the specified project.

    This command provides one-step setup for integrating Vibe Check into
    pre-commit workflows with automatic conflict detection and resolution.

    Args:
        project_path: Path to the project directory (default: current directory)
        validation_level: Validation level (minimal/standard/strict, default: standard)
        merge_existing: Whether to merge with existing pre-commit config (default: True)
        dry_run: Show what would be done without making changes (default: False)
        custom_args: Additional arguments to pass to Vibe Check hooks

    Performance:
        - Minimal level: <10 seconds execution time
        - Standard level: <20 seconds execution time
        - Strict level: <30 seconds execution time

    Implementation:
        Uses PreCommitInstaller for safe installation with backup and rollback
        capabilities. Detects conflicts and provides resolution strategies.
    """
    from pathlib import Path
    from vibe_check.core.precommit.installer import PreCommitInstaller, InstallMode
    from vibe_check.core.precommit.generator import ValidationLevel
    from vibe_check.core.logging import get_logger

    logger = get_logger(__name__)

    try:
        # Validate inputs
        project_dir = Path(project_path).resolve()
        if not project_dir.exists():
            print(f"❌ Error: Project directory does not exist: {project_dir}")
            return

        # Parse validation level
        try:
            level = ValidationLevel(validation_level.lower())
        except ValueError:
            print(f"❌ Error: Invalid validation level '{validation_level}'")
            print("   Available levels: minimal, standard, strict")
            return

        # Determine installation mode
        mode = InstallMode.MERGE if merge_existing else InstallMode.FRESH
        if dry_run:
            mode = InstallMode.VALIDATE

        print(f"🔧 Installing Vibe Check pre-commit hooks...")
        print(f"   📁 Project: {project_dir}")
        print(f"   📊 Level: {level.value}")
        print(f"   🔄 Mode: {mode.value}")

        if dry_run:
            print("   🧪 Dry run: No changes will be made")

        # Create installer and run installation
        installer = PreCommitInstaller(project_dir)
        result = installer.install(
            validation_level=level,
            mode=mode,
            custom_args=custom_args,
            dry_run=dry_run
        )

        # Display results
        print(f"\n{result}")

        if result.success:
            if not dry_run:
                print(f"\n🎯 Next steps:")
                print(f"   1. Run: cd {project_dir}")
                print(f"   2. Test: pre-commit run --all-files")
                print(f"   3. Commit changes to activate hooks")

            # Show performance expectations
            from vibe_check.core.precommit.generator import PreCommitHookGenerator
            generator = PreCommitHookGenerator(project_dir)
            estimated_time = generator.estimate_execution_time(level, 10)
            target_met = generator.validate_performance_target(level, estimated_time)

            print(f"\n📈 Performance expectations:")
            print(f"   ⏱️  Estimated execution time: {estimated_time:.1f}s")
            print(f"   🎯 Target met: {'✅' if target_met else '❌'}")

            if result.conflicts_resolved:
                print(f"\n⚠️  Conflicts resolved: {len(result.conflicts_resolved)}")
                for conflict in result.conflicts_resolved:
                    print(f"   • {conflict.hook_id}: {conflict.resolution}")

        else:
            print(f"\n💡 Troubleshooting:")
            print(f"   • Ensure you're in a git repository")
            print(f"   • Check that pre-commit is installed: pip install pre-commit")
            print(f"   • Verify project permissions")

    except Exception as e:
        logger.error(f"Install hooks command failed: {e}")
        print(f"❌ Installation failed: {e}")
        print(f"💡 Try running with --dry-run to diagnose issues")
