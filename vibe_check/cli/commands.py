"""
CLI Commands
=========

This module provides the command functions for the CLI.
"""

import os
import sys
from pathlib import Path
from typing import Optional, Dict, Any, List

import click

from ..core.config import load_config
from ..plugins.manager import list_plugins
from ..core.error_handling.error_aggregator import (
    reset_error_aggregator,
    collect_errors_from_analysis_results
)
from ..core.error_handling.failure_integration import CLIFailureIntegration


def analyze_command(project_path: str,
                   config_path: Optional[str] = None,
                   output_dir: Optional[str] = None,
                   verbose: bool = False,
                   quiet: bool = False,
                   config_override: Optional[Dict[str, Any]] = None,
                   analyze_trends: bool = False,
                   report_progress: bool = False,
                   use_simple_analyzer: bool = True,  # Always use simple analyzer now
                   profile: str = "standard",  # Analysis profile
                   rule_id: Optional[str] = None,  # Single rule testing
                   rule_ids: Optional[str] = None,  # Multiple rule testing
                   exclude_rules: Optional[str] = None,  # Rule exclusion
                   detailed: bool = False,  # Detailed issue reporting
                   categories: Optional[str] = None,  # Category filtering
                   exclude_categories: Optional[str] = None,  # Category exclusion
                   tools: Optional[str] = None,  # External tool integration
                   exclude_tools: Optional[str] = None,  # External tool exclusion
                   no_save: bool = False,  # Skip database persistence
                   **kwargs: Any) -> Dict[str, Any]:
    """
    Run the analyze command.

    Args:
        project_path: Path to the project to analyze
        config_path: Optional path to a configuration file
        output_dir: Optional directory to write output files to
        verbose: Whether to enable verbose output
        quiet: Whether to suppress output
        config_override: Optional dictionary to override configuration values
        analyze_trends: Whether to analyze trends compared to previous runs
        report_progress: Whether to report progress between analyses with the same output directory
        use_simple_analyzer: Whether to use the simple analyzer (always True now)
        profile: Analysis profile to use (minimal, standard, comprehensive, security, performance, maintainability)
        rule_id: Optional specific rule ID to test (e.g., 'S001', 'ADV003') for single-rule debugging
        rule_ids: Optional comma-separated list of rule IDs to include (e.g., 'S001,S002,C001')
        exclude_rules: Optional comma-separated list of rule IDs to exclude (e.g., 'S001,C002')
        detailed: Whether to show detailed issue information including file locations and fix recommendations
        categories: Optional comma-separated list of categories to include (e.g., 'style,security')
        exclude_categories: Optional comma-separated list of categories to exclude (e.g., 'documentation,imports')
        tools: Optional comma-separated list of external tools to run (e.g., 'ruff,mypy,bandit')
        exclude_tools: Optional comma-separated list of external tools to exclude (e.g., 'pylint,flake8')
        no_save: Whether to skip saving results to database (useful for quick checks and CI/CD)
        **kwargs: Additional keyword arguments

    Returns:
        Analysis results
    """
    import logging
    import traceback
    import time
    from pathlib import Path

    # Set up a logger for this function
    logger = logging.getLogger("vibe_check_cli.analyze_command")

    try:
        start_time = time.time()
        logger.debug("Starting analyze_command")
        logger.debug(f"Project path: {project_path}")
        logger.debug(f"Config path: {config_path}")
        logger.debug(f"Output directory: {output_dir}")
        logger.debug(f"Verbose: {verbose}, Quiet: {quiet}")
        logger.debug(f"Config override: {config_override}")
        logger.debug(f"Analyze trends: {analyze_trends}, Report progress: {report_progress}")
        logger.debug(f"Use simple analyzer: {use_simple_analyzer}")
        logger.debug(f"Additional kwargs: {kwargs}")

        # Reset error aggregator for new analysis
        reset_error_aggregator()

        # Set up logging
        if verbose:
            from ..core.logging import setup_logging
            logger.debug("Setting up logging with debug=True")
            setup_logging(debug=True)
        elif quiet:
            from ..core.logging import setup_logging
            logger.debug("Setting up logging with quiet=True")
            setup_logging(quiet=True)

        # Validate project path
        import os
        if not os.path.exists(project_path):
            logger.error(f"Project path does not exist: {project_path}")
            return {
                "error": f"Project path does not exist: {project_path}",
                "error_type": "path_not_found",
                "suggestions": [
                    "Check the path spelling and try again",
                    "Ensure the path is accessible from your current location",
                    "Use an absolute path instead of a relative path"
                ]
            }

        if not os.path.isdir(project_path):
            logger.error(f"Project path is not a directory: {project_path}")
            return {
                "error": f"Project path is not a directory: {project_path}",
                "error_type": "invalid_directory",
                "suggestions": [
                    "Ensure you're pointing to a directory, not a file",
                    "Check that the path contains a Python project"
                ]
            }

        # Create output directory if it doesn't exist
        if output_dir and not os.path.exists(output_dir):
            logger.debug(f"Creating output directory: {output_dir}")
            os.makedirs(output_dir, exist_ok=True)

        # Load configuration if specified
        config = None
        if config_path:
            from ..core.config import load_config
            logger.debug(f"Loading configuration from {config_path}")
            config = load_config(config_path)
            logger.debug(f"Loaded configuration: {config}")

        # Apply preset if specified
        if config_override and "preset" in config_override:
            preset_name = config_override["preset"]
            logger.debug(f"Applying preset: {preset_name}")

            try:
                from ..core.config import load_preset
                preset_config = load_preset(preset_name)
                logger.debug(f"Loaded preset configuration: {preset_config}")

                # Merge preset with existing config
                if config is None:
                    config = preset_config
                else:
                    # Deep merge the configurations
                    from ..core.utils.dict_utils import deep_merge
                    config = deep_merge(config, preset_config)
                    logger.debug(f"Merged configuration: {config}")
            except Exception as e:
                logger.error(f"Error loading preset {preset_name}: {e}")
                logger.error(traceback.format_exc())
                return {
                    "error": f"Error loading preset {preset_name}: {e}",
                    "error_type": "preset_load_error",
                    "error_details": traceback.format_exc(),
                    "suggestions": [
                        f"Check if preset '{preset_name}' exists",
                        "Try using a different preset (minimal, standard, comprehensive)",
                        "Run without a preset to use default settings"
                    ]
                }

        # Apply other config overrides
        if config_override:
            if config is None:
                config = {}

            # Remove preset key as it's already been processed
            config_override_copy = config_override.copy()
            if "preset" in config_override_copy:
                del config_override_copy["preset"]

            # Apply remaining overrides
            if config_override_copy:
                from ..core.utils.dict_utils import deep_merge
                config = deep_merge(config, config_override_copy)
                logger.debug(f"Applied config overrides: {config}")

        # Check if VCS mode is enabled
        vcs_mode = kwargs.get('vcs_mode', False)

        if vcs_mode:
            logger.info("Using VCS (Vibe Check Standalone) mode with built-in analysis rules")

            try:
                from ..core.vcs.engine import VibeCheckEngine
                from ..core.vcs.models import EngineMode, AnalysisTarget, RuleCategory
                from ..core.vcs.config import VCSConfig
                import asyncio

                # Create VCS configuration
                vcs_config = VCSConfig(
                    mode=EngineMode.STANDALONE,
                    cache_enabled=True,
                    performance_mode=profile in ["minimal", "performance"]
                )

                # Configure rule/category filtering at the config level
                if rule_id:
                    if not quiet:
                        logger.info(f"Running single-rule test for rule: {rule_id}")
                    vcs_config.enabled_rules = {rule_id}
                    vcs_config.enabled_categories = set(RuleCategory)  # Enable all categories for single rule
                elif rule_ids:
                    # Enable multiple specific rules
                    rule_list = [rule.strip() for rule in rule_ids.split(",")]
                    vcs_config.enabled_rules = set(rule_list)
                    vcs_config.enabled_categories = set(RuleCategory)  # Enable all categories for rule filtering
                    if not quiet:
                        logger.info(f"Running multi-rule test for rules: {rule_list}")
                elif exclude_rules:
                    # Disable specific rules
                    exclude_list = [rule.strip() for rule in exclude_rules.split(",")]
                    vcs_config.disabled_rules = set(exclude_list)
                    if not quiet:
                        logger.info(f"Excluding rules: {exclude_list}")
                elif categories:
                    # Enable only specified categories
                    category_list = [cat.strip().lower() for cat in categories.split(",")]
                    enabled_categories = set()
                    for cat_name in category_list:
                        for rule_cat in RuleCategory:
                            if rule_cat.value.lower() == cat_name:
                                enabled_categories.add(rule_cat)
                                break
                    vcs_config.enabled_categories = enabled_categories
                    if not quiet:
                        logger.info(f"Category filter: {[cat.value for cat in enabled_categories]}")
                elif exclude_categories:
                    # Enable all categories except excluded ones
                    exclude_list = [cat.strip().lower() for cat in exclude_categories.split(",")]
                    excluded_categories = set()
                    for cat_name in exclude_list:
                        for rule_cat in RuleCategory:
                            if rule_cat.value.lower() == cat_name:
                                excluded_categories.add(rule_cat)
                                break
                    all_categories = set(RuleCategory)
                    vcs_config.enabled_categories = all_categories - excluded_categories
                    if not quiet:
                        logger.info(f"Excluding categories: {[cat.value for cat in excluded_categories]}")
                        logger.info(f"Enabled categories: {[cat.value for cat in vcs_config.enabled_categories]}")

                # Create and start VCS engine
                engine = VibeCheckEngine(mode=EngineMode.STANDALONE, config=vcs_config)

                async def run_vcs_analysis():
                    await engine.start()
                    try:
                        # Configure single-rule testing if specified
                        if rule_id:
                            # Get all available rules to validate rule_id
                            all_rules = engine.rule_registry.get_all_rules()
                            available_rule_ids = [rule.rule_id for rule in all_rules]

                            if rule_id not in available_rule_ids:
                                logger.error(f"Rule '{rule_id}' not found. Available rules: {', '.join(sorted(available_rule_ids))}")
                                return []

                            if not quiet:
                                logger.info(f"Testing single rule: {rule_id}")

                        # Analyze all Python files in the project with filtering
                        from pathlib import Path
                        import os
                        import fnmatch

                        project_path_obj = Path(project_path)
                        results = []

                        # Default ignore patterns (like .gitignore)
                        ignore_patterns = [
                            "venv/*", "env/*", ".venv/*", ".env/*",
                            "__pycache__/*", "*.pyc", "*.pyo", "*.pyd",
                            ".git/*", ".svn/*", ".hg/*",
                            "node_modules/*", ".tox/*", ".pytest_cache/*",
                            "build/*", "dist/*", "*.egg-info/*",
                            ".coverage", ".mypy_cache/*", ".ruff_cache/*",
                            "site-packages/*", "lib/python*/site-packages/*"
                        ]

                        def should_ignore(file_path: Path) -> bool:
                            """Check if file should be ignored based on patterns."""
                            relative_path = str(file_path.relative_to(project_path_obj))
                            for pattern in ignore_patterns:
                                if fnmatch.fnmatch(relative_path, pattern) or fnmatch.fnmatch(relative_path, f"*/{pattern}"):
                                    return True
                            return False

                        total_files = 0
                        analyzed_files = 0

                        # Count total files first for progress
                        all_py_files = [f for f in project_path_obj.rglob("*.py") if f.is_file() and not should_ignore(f)]
                        total_files = len(all_py_files)

                        if not quiet:
                            print(f"Found {total_files} Python files to analyze...")

                        # Parallel processing for better performance
                        async def analyze_file(py_file):
                            """Analyze a single file."""
                            try:
                                target = AnalysisTarget.from_file(py_file)

                                # Create analysis context - filtering is now handled at config level
                                from ..core.vcs.models import AnalysisContext, EngineMode
                                context = AnalysisContext.create_default(EngineMode.STANDALONE)

                                result = await engine.analyze(target, context)

                                # Get analysis time from performance monitor
                                try:
                                    perf_stats = engine.performance_monitor.get_performance_report()
                                    analysis_time = perf_stats.get('summary', {}).get('average_execution_time', 0.0)
                                except:
                                    analysis_time = 0.0

                                # Count lines in the file
                                try:
                                    with open(py_file, 'r', encoding='utf-8') as f:
                                        lines = len(f.readlines())
                                except:
                                    lines = 0

                                return {
                                    "file": str(py_file.relative_to(project_path_obj)),
                                    "issues": len(result.issues),
                                    "rules_executed": len(result.rules_executed),
                                    "analysis_time": analysis_time,
                                    "success": result.success,
                                    "lines": lines,  # Add line count
                                    "issues_detail": [
                                        {
                                            "rule_id": issue.rule_id,
                                            "category": issue.category.value,
                                            "severity": issue.severity.value,
                                            "line": issue.line,
                                            "column": issue.column,
                                            "message": issue.message,
                                            "auto_fixable": issue.auto_fixable,
                                            "fix_suggestion": issue.fix_suggestion
                                        }
                                        for issue in result.issues
                                    ]
                                }

                            except Exception as e:
                                logger.warning(f"Failed to analyze {py_file}: {e}")
                                return {
                                    "file": str(py_file.relative_to(project_path_obj)),
                                    "issues": 0,
                                    "rules_executed": 0,
                                    "analysis_time": 0.0,
                                    "success": False,
                                    "error": str(e)
                                }

                        # Process files in parallel batches for optimal performance
                        import asyncio
                        batch_size = 4  # Process 4 files concurrently

                        for i in range(0, len(all_py_files), batch_size):
                            batch = all_py_files[i:i + batch_size]
                            batch_results = await asyncio.gather(*[analyze_file(py_file) for py_file in batch])
                            results.extend(batch_results)
                            analyzed_files += len(batch)

                            # Progress indication
                            if not quiet and analyzed_files % 10 == 0:
                                print(f"Analyzed {analyzed_files}/{total_files} files...")

                        if not quiet:
                            print(f"Analysis complete: {analyzed_files}/{total_files} files analyzed successfully")

                        return results
                    finally:
                        await engine.stop()

                # Run the async analysis
                vcs_results = asyncio.run(run_vcs_analysis())

                # Handle external tool integration
                external_tool_results: List[Dict[str, Any]] = []
                external_results = {}  # Initialize external results for all cases

                if tools or exclude_tools:
                    if not quiet:
                        print("\n🔧 EXTERNAL TOOL INTEGRATION")
                        print("-" * 50)

                    # Determine which tools to run
                    available_tools = {"ruff", "mypy", "bandit", "pylint", "flake8", "black", "isort"}
                    tools_to_run = set()

                    if tools:
                        # Run only specified tools
                        tool_list = [tool.strip().lower() for tool in tools.split(",")]
                        tools_to_run = set(tool_list) & available_tools
                    else:
                        # Run all tools except excluded ones
                        tools_to_run = available_tools.copy()
                        if exclude_tools:
                            exclude_list = [tool.strip().lower() for tool in exclude_tools.split(",")]
                            tools_to_run -= set(exclude_list)

                    # Run external tools if specified
                    if tools_to_run:
                        if not quiet:
                            print(f"📋 Running external tools: {', '.join(sorted(tools_to_run))}")

                        try:
                            import asyncio
                            from ..core.external_tools import ExternalToolRunner
                            tool_runner = ExternalToolRunner()
                            tool_results = asyncio.run(tool_runner.run_tools(tools_to_run, Path(project_path)))
                            external_results.update(tool_results)

                            if not quiet:
                                total_external_issues = sum(len(issues) for issues in external_results.values())
                                print(f"✅ External tools completed: {total_external_issues} issues found")
                        except Exception as e:
                            logger.error(f"Error running external tools: {e}")
                            if not quiet:
                                print(f"⚠️  Error running external tools: {e}")
                            # external_results remains empty dict
                    else:
                        if not quiet:
                            print("⚠️  No external tools selected for execution")

                # Convert to format expected by CLI and add summary
                total_issues = sum(r["issues"] for r in vcs_results)
                total_files = len(vcs_results)
                successful_files = len([r for r in vcs_results if r.get("success", True)])

                # Categorize issues by severity and category
                issues_by_severity = {"ERROR": 0, "WARNING": 0, "INFO": 0, "HINT": 0}
                issues_by_category = {"style": 0, "security": 0, "complexity": 0, "documentation": 0, "imports": 0, "types": 0}
                auto_fixable_count = 0
                detailed_issues = []

                for file_result in vcs_results:
                    for issue in file_result.get("issues_detail", []):
                        severity = issue.get("severity", "info").upper()  # Convert to uppercase
                        category = issue.get("category", "unknown")
                        if severity in issues_by_severity:
                            issues_by_severity[severity] += 1
                        if category in issues_by_category:
                            issues_by_category[category] += 1
                        if issue.get("auto_fixable", False):
                            auto_fixable_count += 1

                        # Collect detailed issue information if requested
                        if detailed:
                            detailed_issues.append({
                                "file": file_result.get("file", "unknown"),
                                "rule_id": issue.get("rule_id", "unknown"),
                                "line": issue.get("line", 0),
                                "column": issue.get("column", 0),
                                "message": issue.get("message", "No message"),
                                "severity": severity,
                                "category": category,
                                "auto_fixable": issue.get("auto_fixable", False),
                                "fix_suggestion": issue.get("fix_suggestion", None)
                            })

                # Process external tool results
                external_total_issues = 0
                external_auto_fixable = 0
                external_detailed_issues = []

                for tool_name, tool_issues in external_results.items():
                    external_total_issues += len(tool_issues)

                    for issue in tool_issues:
                        # Convert AnalysisIssue to dict format
                        severity = issue.severity.value.upper()
                        category = issue.category.value

                        if severity in issues_by_severity:
                            issues_by_severity[severity] += 1
                        if category in issues_by_category:
                            issues_by_category[category] += 1
                        if issue.auto_fixable:
                            auto_fixable_count += 1
                            external_auto_fixable += 1

                        # Collect detailed issue information if requested
                        if detailed:
                            external_detailed_issues.append({
                                "file": issue.metadata.get("filename", "unknown"),
                                "rule_id": issue.rule_id,
                                "line": issue.line,
                                "column": issue.column,
                                "message": issue.message,
                                "severity": severity,
                                "category": category,
                                "auto_fixable": issue.auto_fixable,
                                "fix_suggestion": issue.fix_suggestion,
                                "tool": tool_name
                            })

                # Combine totals
                total_issues += external_total_issues

                # Display summary if not quiet
                if not quiet:
                    print("\n" + "="*60)
                    if external_results:
                        print("COMBINED ANALYSIS SUMMARY (VCS + External Tools)")
                    else:
                        print("VCS ANALYSIS SUMMARY")
                    print("="*60)
                    print(f"Files analyzed: {successful_files}/{total_files}")
                    print(f"Total issues found: {total_issues}")
                    if external_results:
                        vcs_issues = total_issues - external_total_issues
                        print(f"  VCS issues: {vcs_issues}")
                        print(f"  External tool issues: {external_total_issues}")
                    print(f"Auto-fixable issues: {auto_fixable_count}")

                    # Show external tool breakdown
                    if external_results:
                        print(f"\nExternal tools used:")
                        for tool_name, tool_issues in external_results.items():
                            tool_auto_fix = sum(1 for issue in tool_issues if issue.auto_fixable)
                            print(f"  {tool_name}: {len(tool_issues)} issues ({tool_auto_fix} auto-fixable)")

                    if total_issues > 0:
                        print(f"\nIssues by severity:")
                        for severity, count in issues_by_severity.items():
                            if count > 0:
                                print(f"  {severity}: {count}")

                        print(f"\nIssues by category:")
                        for category, count in issues_by_category.items():
                            if count > 0:
                                print(f"  {category}: {count}")

                    # Get final performance stats
                    try:
                        final_perf = engine.performance_monitor.get_performance_report()
                        avg_time = final_perf.get('summary', {}).get('average_execution_time', 0)
                        total_time = final_perf.get('summary', {}).get('total_execution_time', 0)
                        print(f"\nPerformance:")
                        print(f"  Total analysis time: {total_time:.2f}s")
                        print(f"  Average per file: {avg_time:.3f}s")
                    except:
                        pass

                    print("="*60)

                metrics = {
                    "vcs_mode": True,
                    "total_files_analyzed": total_files,
                    "successful_files": successful_files,
                    "total_issues_found": total_issues,
                    "auto_fixable_issues": auto_fixable_count,
                    "issues_by_severity": issues_by_severity,
                    "issues_by_category": issues_by_category,
                    "files": vcs_results,
                    "analysis_engine": "VCS (Vibe Check Standalone)" + (f" + {len(external_results)} external tools" if external_results else ""),
                    "rules_available": 32,
                    "profile": profile,
                    "detailed_issues": detailed_issues if detailed else [],
                    "external_detailed_issues": external_detailed_issues if detailed else [],
                    "external_results": external_results,
                    "show_detailed": detailed
                }

            except Exception as e:
                logger.error(f"Error in VCS analysis: {e}")
                logger.error(traceback.format_exc())
                return {
                    "error": f"Error in VCS analysis: {e}",
                    "error_type": "vcs_analysis_error",
                    "error_details": traceback.format_exc(),
                    "suggestions": [
                        "Try running without --vcs-mode flag",
                        "Check that the project contains valid Python files",
                        "Run with --verbose for more detailed logs"
                    ]
                }
        else:
            # Use simple analyzer (original behavior)
            logger.info("Using simplified analysis engine")

            try:
                from ..core.simple_analyzer import simple_analyze_project

                # Add progress indicators
                print("🔍 Starting project analysis...")
                print(f"📁 Project: {project_path}")
                print(f"⚙️  Profile: {profile}")
                print("⏳ Analysis in progress...")

                start_time = time.time()

                # Run the simple analyzer with profile
                metrics = simple_analyze_project(
                    project_path=project_path,
                    output_dir=output_dir,
                    config=config,
                    profile=profile
                )

                # Show completion with timing
                elapsed_time = time.time() - start_time
                print(f"✅ Analysis completed in {elapsed_time:.2f} seconds")

            except Exception as e:
                logger.error(f"Error in simple_analyze_project: {e}")
                logger.error(traceback.format_exc())

                # Categorize the error for better user guidance
                error_msg = str(e).lower()
                if "memory" in error_msg or "out of memory" in error_msg:
                    error_type = "memory_error"
                    suggestions = [
                        "Try analyzing a smaller project or subset of files",
                        "Use --profile minimal to reduce memory usage",
                        "Close other applications to free up memory"
                    ]
                elif "permission" in error_msg or "access" in error_msg:
                    error_type = "permission_error"
                    suggestions = [
                        "Check file and directory permissions",
                        "Run with appropriate permissions",
                        "Try a different output directory"
                    ]
                elif "timeout" in error_msg:
                    error_type = "timeout_error"
                    suggestions = [
                        "Try analyzing a smaller project",
                        "Use --profile minimal for faster analysis",
                        "Check system resources"
                    ]
                else:
                    error_type = "analysis_error"
                    suggestions = [
                        "Run with --verbose for more detailed logs",
                        "Try analyzing a smaller project first",
                        "Check that the project contains valid Python files"
                    ]

                return {
                    "error": f"Error in analysis: {e}",
                    "error_type": error_type,
                    "error_details": traceback.format_exc(),
                    "suggestions": suggestions
                }

        end_time = time.time()
        logger.info(f"Analysis completed in {end_time - start_time:.2f} seconds")

        # Save results to database for comparison (unless --no-save is specified)
        if not no_save:
            try:
                from ..core.persistence.storage import ResultStorage
                from ..core.persistence.result_adapter import ResultAdapter
                from datetime import datetime

                storage = ResultStorage()

                # Convert results to persistence model
                start_datetime = datetime.fromtimestamp(start_time)
                end_datetime = datetime.fromtimestamp(end_time)

                analysis_run = ResultAdapter.convert_analysis_results(
                    results=metrics,
                    project_path=project_path,
                    profile=profile,
                    start_time=start_datetime,
                    end_time=end_datetime
                )

                # Save to database
                run_id = storage.save_analysis_run(analysis_run)
                logger.info(f"Analysis results saved to database with run_id: {run_id}")

                # Add run_id to metrics for reference (only if metrics is a dictionary)
                if isinstance(metrics, dict):
                    metrics['run_id'] = run_id
                    metrics['analysis_time'] = end_time - start_time

            except Exception as e:
                logger.warning(f"Failed to save analysis results to database: {e}")
                # Don't fail the analysis if database save fails
        else:
            # Add analysis time without saving to database (only if metrics is a dictionary)
            if isinstance(metrics, dict):
                metrics['analysis_time'] = end_time - start_time
            if not quiet:
                logger.info("Skipping database save (--no-save specified)")

        # Collect errors from analysis results
        collect_errors_from_analysis_results(metrics)

        # Convert metrics to dictionary if needed
        if not isinstance(metrics, dict):
            # Try to convert to dictionary using to_dict() method if available
            try:
                if hasattr(metrics, 'to_dict') and callable(getattr(metrics, 'to_dict')):
                    metrics_dict = metrics.to_dict()
                    return metrics_dict
                else:
                    # Fallback to __dict__ if no to_dict() method
                    metrics_dict = metrics.__dict__ if hasattr(metrics, '__dict__') else {"metrics": metrics}
                    return metrics_dict
            except Exception as e:
                logger.warning(f"Could not convert metrics to dictionary: {e}")
                # Return as is and let the formatter handle it
                return {"metrics": metrics}
        return metrics

    except Exception as e:
        logger.error(f"Error in analyze_command: {e}")
        logger.error(traceback.format_exc())
        return {"error": str(e), "error_details": traceback.format_exc()}


def tui_command(project_path: str,
               config_path: Optional[str] = None) -> None:
    """
    Launch the Terminal User Interface with intelligent dependency management.

    Args:
        project_path: Path to the project to analyze
        config_path: Optional path to a configuration file
    """
    try:
        from ..ui.tui.dependency_checker import (
            check_tui_dependencies, ensure_tui_dependencies,
            get_tui_startup_message, TUIMode
        )

        print("🔧 Checking TUI dependencies...")

        # Check current TUI capabilities
        mode, checker = check_tui_dependencies()

        if mode == TUIMode.MINIMAL:
            print("\n⚠️  TUI DEPENDENCIES MISSING")
            print("=" * 50)
            checker.print_dependency_status()

            # Try to install basic dependencies
            if ensure_tui_dependencies(TUIMode.BASIC):
                mode, checker = check_tui_dependencies()  # Re-check after installation
            else:
                print("\n💡 Fallback Options:")
                suggestions = checker.get_fallback_suggestions(mode)
                for suggestion in suggestions:
                    print(f"  • {suggestion}")

                response = input("\nContinue with minimal TUI? [y/N]: ").strip().lower()
                if response not in ['y', 'yes']:
                    print("TUI launch cancelled.")
                    sys.exit(1)

        # Launch TUI with appropriate mode
        print(f"\n{get_tui_startup_message(mode)}")

        if mode == TUIMode.MINIMAL:
            # Launch minimal text-based interface
            _launch_minimal_tui(project_path, config_path)
        else:
            # Launch enhanced TUI
            from ..ui.tui.app import run_tui
            exit_code = run_tui()
            sys.exit(exit_code)

    except ImportError as e:
        import logging
        logger = logging.getLogger("vibe_check_cli.tui")
        logger.error(f"TUI dependency checker failed: {e}")
        print(f"❌ TUI launch failed: {e}")
        print("💡 Try: pip install --force-reinstall vibe-check[tui]")
        sys.exit(1)
    except Exception as e:
        import logging
        logger = logging.getLogger("vibe_check_cli.tui")
        logger.error(f"TUI runtime error: {e}")
        print(f"❌ TUI error: {e}")
        sys.exit(1)


def _launch_minimal_tui(project_path: str, config_path: Optional[str] = None) -> None:
    """
    Launch minimal text-based TUI fallback.

    Args:
        project_path: Path to the project to analyze
        config_path: Optional path to configuration file
    """
    print("\n📝 MINIMAL TUI MODE")
    print("=" * 50)
    print("Running simplified text-based interface...")
    print("For full functionality, install: pip install rich textual keyboard")
    print("")

    # Simple menu-driven interface
    while True:
        print("\n🎯 VIBE CHECK - MINIMAL TUI")
        print("-" * 30)
        print("1. Analyze Project")
        print("2. View Configuration")
        print("3. Check Dependencies")
        print("4. Exit")

        choice = input("\nSelect option [1-4]: ").strip()

        if choice == "1":
            print(f"\n🔍 Analyzing project: {project_path}")
            try:
                # Run analysis using CLI command
                results = analyze_command(
                    project_path=project_path,
                    quiet=False,
                    profile="standard"
                )
                print("\n✅ Analysis completed!")

            except Exception as e:
                print(f"❌ Analysis failed: {e}")

        elif choice == "2":
            print(f"\n⚙️  Configuration")
            print(f"Project Path: {project_path}")
            print(f"Config Path: {config_path or 'Default'}")

        elif choice == "3":
            try:
                from ..ui.tui.dependency_checker import TUIDependencyChecker
                checker = TUIDependencyChecker()
                checker.print_dependency_status()
            except ImportError:
                print("❌ Dependency checker not available")

        elif choice == "4":
            print("👋 Goodbye!")
            break

        else:
            print("❌ Invalid option. Please select 1-4.")

        input("\nPress Enter to continue...")


def web_command(project_path: str,
               config_path: Optional[str] = None,
               host: str = "localhost",
               port: int = 8000) -> None:
    """
    Run the web command with intelligent dependency management.

    Args:
        project_path: Path to the project to analyze
        config_path: Optional path to a configuration file
        host: Host to bind to
        port: Port to bind to
    """
    from ..core.dependency_manager import dependency_manager

    # Check if Web UI dependencies are available
    available, missing = dependency_manager.check_interface_dependencies("web")

    if not available:
        print(f"\n🌐 Web Interface - Missing Dependencies")
        print("=" * 50)
        print("The Web User Interface requires additional packages.")
        print(f"Missing: {', '.join(missing)}")

        # Offer automatic installation
        if dependency_manager.auto_install_dependencies("web", prompt_user=True):
            print("🔄 Retrying Web UI launch...")
        else:
            print("\n💡 Manual installation:")
            print("   pip install vibe-check[web]")
            print("   # or")
            print("   pip install streamlit>=1.10.0 altair>=4.2.0 pandas>=1.4.0")
            sys.exit(1)

    # Try to launch Web UI
    try:
        from ..ui.web import run_web_server
        print(f"🚀 Launching Web interface at http://{host}:{port}")
        run_web_server(project_path, config_path, host, port)
    except ImportError as e:
        import logging
        logger = logging.getLogger("vibe_check_cli.web")
        logger.error(f"Web UI launch failed: {e}")
        print(f"❌ Web UI launch failed: {e}")
        print("💡 Try reinstalling: pip install --force-reinstall vibe-check[web]")
        sys.exit(1)


def debug_command(project_path: str,
               output_dir: Optional[str] = None,
               verbose: bool = False,
               timeout: float = 120.0) -> Dict[str, Any]:
    """
    Run the debug command (simplified - actor system removed).

    This command now provides basic debugging information for the analysis process.

    Args:
        project_path: Path to the project to analyze
        output_dir: Optional directory to write output files to
        verbose: Whether to enable verbose output
        timeout: Maximum time in seconds to wait for analysis

    Returns:
        Dictionary with debugging results
    """
    import logging
    import traceback
    import time
    from pathlib import Path

    # Set up a logger for this function
    logger = logging.getLogger("vibe_check_cli.debug_command")

    try:
        start_time = time.time()
        logger.info("Starting debug_command")
        logger.info(f"Project path: {project_path}")
        logger.info(f"Output directory: {output_dir}")
        logger.info(f"Verbose: {verbose}")
        logger.info(f"Timeout: {timeout}")

        # Create output directory if it doesn't exist
        if output_dir:
            output_dir_path = Path(output_dir)
            output_dir_path.mkdir(parents=True, exist_ok=True)
        else:
            # Use default output directory
            output_dir_path = Path("vibe_check_debug")
            output_dir_path.mkdir(parents=True, exist_ok=True)
            output_dir = str(output_dir_path)

        # Run analysis with debugging enabled
        logger.info("Running analysis with debugging enabled")

        try:
            from ..core.simple_analyzer import simple_analyze_project

            # Run analysis to get project information
            logger.info("Running analysis with simple analyzer")
            result = simple_analyze_project(
                project_path=project_path,
                output_dir=output_dir,
                config={"debug": True, "verbose": verbose}
            )

            logger.info(f"Analysis completed: {len(result.files)} files analyzed")

            end_time = time.time()
            debug_info = {
                "status": "success",
                "analysis_time": end_time - start_time,
                "files_analyzed": len(result.files),
                "total_lines": result.total_line_count,
                "issues_found": result.issue_count,
                "output_directory": output_dir
            }

            logger.info(f"Debug analysis completed successfully in {end_time - start_time:.2f} seconds")
            return debug_info

        except Exception as e:
            logger.error(f"Error in debug analysis: {e}")
            logger.error(traceback.format_exc())
            return {
                "error": f"Error in debug analysis: {e}",
                "error_details": traceback.format_exc()
            }

            # Debug analysis is now complete (actor system removed)
            return debug_info

        except Exception as e:
            logger.error(f"Error in debug analysis: {e}")
            logger.error(traceback.format_exc())
            return {
                "error": f"Error in debug analysis: {e}",
                "error_details": traceback.format_exc()
            }

    except Exception as e:
        logger.error(f"Error in debug_command: {e}")
        logger.error(traceback.format_exc())
        return {"error": str(e), "error_details": traceback.format_exc()}


def plugin_command(action: str, plugin_name: Optional[str] = None) -> None:
    """
    Run the plugin command.

    Args:
        action: Action to perform (list, install, uninstall)
        plugin_name: Name of the plugin
    """
    import logging
    logger = logging.getLogger("vibe_check_cli.plugin")

    if action == "list":
        plugins = list_plugins()
        if not plugins:
            logger.info("No plugins installed.")
            click.echo("No plugins installed.")
            return

        logger.info(f"Found {len(plugins)} installed plugins")
        click.echo("Installed plugins:")
        for plugin in plugins:
            click.echo(f"  {plugin}")
    elif action == "install":
        if not plugin_name:
            logger.error("Plugin name not specified")
            click.echo("Please specify a plugin name.", err=True)
            sys.exit(1)

        try:
            # Plugin system is not implemented yet
            logger.warning(f"Plugin installation not implemented: {plugin_name}")
            click.echo(f"Plugin installation is not yet implemented: {plugin_name}", err=True)
            sys.exit(1)
        except Exception as e:
            logger.error(f"Failed to install plugin {plugin_name}: {e}")
            click.echo(f"Failed to install plugin {plugin_name}: {e}", err=True)
            sys.exit(1)
    elif action == "uninstall":
        if not plugin_name:
            logger.error("Plugin name not specified")
            click.echo("Please specify a plugin name.", err=True)
            sys.exit(1)

        try:
            # Plugin system is not implemented yet
            logger.warning(f"Plugin uninstallation not implemented: {plugin_name}")
            click.echo(f"Plugin uninstallation is not yet implemented: {plugin_name}", err=True)
            sys.exit(1)
        except Exception as e:
            logger.error(f"Failed to uninstall plugin {plugin_name}: {e}")
            click.echo(f"Failed to uninstall plugin {plugin_name}: {e}", err=True)
            sys.exit(1)
    else:
        logger.error(f"Unknown plugin action: {action}")
        click.echo(f"Unknown action: {action}", err=True)
        click.echo("Available actions: list, install, uninstall")
        sys.exit(1)


def install_hooks_command(
    project_path: str = ".",
    validation_level: str = "standard",
    merge_existing: bool = True,
    dry_run: bool = False,
    custom_args: Optional[List[str]] = None
) -> None:
    """
    Install Vibe Check as a pre-commit hook in the specified project.

    This command provides one-step setup for integrating Vibe Check into
    pre-commit workflows with automatic conflict detection and resolution.

    Args:
        project_path: Path to the project directory (default: current directory)
        validation_level: Validation level (minimal/standard/strict, default: standard)
        merge_existing: Whether to merge with existing pre-commit config (default: True)
        dry_run: Show what would be done without making changes (default: False)
        custom_args: Additional arguments to pass to Vibe Check hooks

    Performance:
        - Minimal level: <10 seconds execution time
        - Standard level: <20 seconds execution time
        - Strict level: <30 seconds execution time

    Implementation:
        Uses PreCommitInstaller for safe installation with backup and rollback
        capabilities. Detects conflicts and provides resolution strategies.
    """
    from pathlib import Path
    from vibe_check.core.precommit.installer import PreCommitInstaller, InstallMode
    from vibe_check.core.precommit.generator import ValidationLevel
    from vibe_check.core.logging import get_logger

    logger = get_logger(__name__)

    try:
        # Validate inputs
        project_dir = Path(project_path).resolve()
        if not project_dir.exists():
            print(f"❌ Error: Project directory does not exist: {project_dir}")
            return

        # Parse validation level
        try:
            level = ValidationLevel(validation_level.lower())
        except ValueError:
            print(f"❌ Error: Invalid validation level '{validation_level}'")
            print("   Available levels: minimal, standard, strict")
            return

        # Determine installation mode
        mode = InstallMode.MERGE if merge_existing else InstallMode.FRESH
        if dry_run:
            mode = InstallMode.VALIDATE

        print(f"🔧 Installing Vibe Check pre-commit hooks...")
        print(f"   📁 Project: {project_dir}")
        print(f"   📊 Level: {level.value}")
        print(f"   🔄 Mode: {mode.value}")

        if dry_run:
            print("   🧪 Dry run: No changes will be made")

        # Create installer and run installation
        installer = PreCommitInstaller(project_dir)
        result = installer.install(
            validation_level=level,
            mode=mode,
            custom_args=custom_args,
            dry_run=dry_run
        )

        # Display results
        print(f"\n{result}")

        if result.success:
            if not dry_run:
                print(f"\n🎯 Next steps:")
                print(f"   1. Run: cd {project_dir}")
                print(f"   2. Test: pre-commit run --all-files")
                print(f"   3. Commit changes to activate hooks")

            # Show performance expectations
            from vibe_check.core.precommit.generator import PreCommitHookGenerator
            generator = PreCommitHookGenerator(project_dir)
            estimated_time = generator.estimate_execution_time(level, 10)
            target_met = generator.validate_performance_target(level, estimated_time)

            print(f"\n📈 Performance expectations:")
            print(f"   ⏱️  Estimated execution time: {estimated_time:.1f}s")
            print(f"   🎯 Target met: {'✅' if target_met else '❌'}")

            if result.conflicts_resolved:
                print(f"\n⚠️  Conflicts resolved: {len(result.conflicts_resolved)}")
                for conflict in result.conflicts_resolved:
                    print(f"   • {conflict.hook_id}: {conflict.resolution}")

        else:
            print(f"\n💡 Troubleshooting:")
            print(f"   • Ensure you're in a git repository")
            print(f"   • Check that pre-commit is installed: pip install pre-commit")
            print(f"   • Verify project permissions")

    except Exception as e:
        logger.error(f"Install hooks command failed: {e}")
        print(f"❌ Installation failed: {e}")
        print(f"💡 Try running with --dry-run to diagnose issues")
