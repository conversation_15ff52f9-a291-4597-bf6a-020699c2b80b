"""
CLI commands for developer experience features.
"""

import click
import sys
from typing import Optional

from ..core.developer_experience import (
    get_dev_experience_manager,
    enhance_cli_error,
    DeveloperTip
)


@click.command()
@click.option('--category', type=click.Choice(['general', 'performance', 'workflow', 'analysis', 'quality', 'configuration']),
              help='Filter tips by category')
@click.option('--count', type=int, default=3, help='Number of tips to show')
def tips(category: Optional[str], count: int):
    """Show helpful developer tips and best practices."""
    
    manager = get_dev_experience_manager()
    
    click.echo("💡 Vibe Check Developer Tips")
    click.echo("=" * 40)
    
    if category:
        click.echo(f"📂 Category: {category.title()}")
        click.echo("")
    
    # Get tips
    if category:
        filtered_tips = [tip for tip in manager.tips_database if tip.category == category]
        tips_to_show = filtered_tips[:count]
    else:
        tips_to_show = manager.tips_database[:count]
    
    if not tips_to_show:
        click.echo(f"❌ No tips found for category: {category}")
        return
    
    for i, tip in enumerate(tips_to_show, 1):
        click.echo(f"{i}. 🎯 {tip.title}")
        click.echo(f"   {tip.description}")
        if tip.command:
            click.echo(f"   💻 Command: {tip.command}")
        click.echo(f"   🏷️  Category: {tip.category}")
        click.echo("")
    
    # Show available categories
    if not category:
        categories = set(tip.category for tip in manager.tips_database)
        click.echo("📂 Available Categories:")
        for cat in sorted(categories):
            count_in_cat = len([t for t in manager.tips_database if t.category == cat])
            click.echo(f"  • {cat} ({count_in_cat} tips)")


@click.command()
def validate():
    """Validate Vibe Check installation and dependencies."""
    
    manager = get_dev_experience_manager()
    
    click.echo("🔍 Vibe Check Installation Validation")
    click.echo("=" * 40)
    
    # Check installation
    is_valid, issues = manager.validate_installation()
    
    if is_valid:
        click.echo("✅ Installation is valid!")
        click.echo("")
        
        # Show performance info
        startup_time = manager.get_startup_performance()
        click.echo(f"⚡ Startup Performance: {startup_time:.3f}s")
        
        if startup_time < 1.0:
            click.echo("🚀 Excellent startup performance!")
        elif startup_time < 2.0:
            click.echo("👍 Good startup performance!")
        else:
            click.echo("⚠️  Startup could be faster")
        
        click.echo("")
        
        # Show Python version
        click.echo(f"🐍 Python Version: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
        
        # Show random tip
        tip = manager.get_random_tip()
        click.echo("")
        click.echo("💡 Random Tip:")
        click.echo(f"   {tip.title}: {tip.description}")
        if tip.command:
            click.echo(f"   Command: {tip.command}")
    
    else:
        click.echo("❌ Installation issues found:")
        click.echo("")
        
        for i, issue in enumerate(issues, 1):
            click.echo(f"{i}. {issue}")
        
        click.echo("")
        click.echo("🔧 Recommended Actions:")
        click.echo("  1. Update Python to 3.8+ if needed")
        click.echo("  2. Reinstall Vibe Check: pip install --upgrade vibe-check")
        click.echo("  3. Install optional dependencies: pip install vibe-check[full]")
        click.echo("  4. Check virtual environment activation")


@click.command()
@click.argument('error_type', required=False)
@click.option('--context', default='', help='Context about what you were trying to do')
def error_help(error_type: Optional[str], context: str):
    """Get help with common errors and issues."""
    
    manager = get_dev_experience_manager()
    
    click.echo("🆘 Vibe Check Error Help")
    click.echo("=" * 30)
    
    if error_type:
        # Simulate an error for demonstration
        try:
            if error_type.lower() == 'filenotfound':
                raise FileNotFoundError("example.py not found")
            elif error_type.lower() == 'permission':
                raise PermissionError("Permission denied: /restricted/file.py")
            elif error_type.lower() == 'import':
                raise ImportError("No module named 'missing_module'")
            elif error_type.lower() == 'config':
                raise ValueError("Invalid configuration format")
            else:
                raise Exception(f"Example {error_type} error")
        except Exception as e:
            enhanced_msg = manager.enhance_error_message(e, context)
            click.echo(enhanced_msg)
    
    else:
        # Show common error types
        click.echo("🔍 Common Error Types:")
        click.echo("")
        
        error_examples = [
            ("filenotfound", "File or directory not found"),
            ("permission", "Permission denied errors"),
            ("import", "Module import errors"),
            ("config", "Configuration errors"),
        ]
        
        for error_type, description in error_examples:
            click.echo(f"  • {error_type}: {description}")
            click.echo(f"    Try: vibe-check dev error-help {error_type}")
        
        click.echo("")
        click.echo("💡 For specific help:")
        click.echo("   vibe-check dev error-help <error_type> --context 'what you were doing'")


@click.command()
def performance():
    """Show performance information and optimization tips."""
    
    manager = get_dev_experience_manager()
    
    click.echo("⚡ Vibe Check Performance Information")
    click.echo("=" * 40)
    
    # Show startup performance
    startup_time = manager.get_startup_performance()
    click.echo(f"🚀 Startup Time: {startup_time:.3f}s")
    
    # Performance rating
    if startup_time < 0.5:
        rating = "🌟 Excellent"
    elif startup_time < 1.0:
        rating = "✅ Good"
    elif startup_time < 2.0:
        rating = "⚠️  Fair"
    else:
        rating = "🔴 Needs Improvement"
    
    click.echo(f"📊 Rating: {rating}")
    click.echo("")
    
    # Show performance tips
    perf_tips = [tip for tip in manager.tips_database if tip.category == "performance"]
    
    click.echo("🎯 Performance Tips:")
    for i, tip in enumerate(perf_tips, 1):
        click.echo(f"{i}. {tip.title}")
        click.echo(f"   {tip.description}")
        if tip.command:
            click.echo(f"   Command: {tip.command}")
        click.echo("")
    
    # Show system info
    click.echo("🖥️  System Information:")
    click.echo(f"   Python: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    click.echo(f"   Platform: {sys.platform}")


@click.command()
def welcome():
    """Show welcome message and getting started information."""
    
    manager = get_dev_experience_manager()
    
    click.echo("🎉 Welcome to Vibe Check!")
    click.echo("=" * 30)
    click.echo("")
    
    startup_time = manager.get_startup_performance()
    click.echo(f"⚡ Ready in {startup_time:.3f}s")
    click.echo("")
    
    click.echo("🚀 Quick Start Guide:")
    click.echo("  1. Analyze your project:")
    click.echo("     vibe-check analyze . --vcs-mode")
    click.echo("")
    click.echo("  2. Set up pre-commit hooks:")
    click.echo("     vibe-check hooks generate-hooks")
    click.echo("")
    click.echo("  3. Compare results over time:")
    click.echo("     vibe-check compare auto")
    click.echo("")
    
    click.echo("💡 Need Help?")
    click.echo("  • Tips: vibe-check dev tips")
    click.echo("  • Validation: vibe-check dev validate")
    click.echo("  • Error Help: vibe-check dev error-help")
    click.echo("  • Performance: vibe-check dev performance")
    click.echo("")
    
    click.echo("📚 Documentation:")
    click.echo("  • GitHub: https://github.com/ptzajac/vibe_check")
    click.echo("  • Issues: https://github.com/ptzajac/vibe_check/issues")
    click.echo("")
    
    # Show random tip
    tip = manager.get_random_tip()
    click.echo("💡 Tip of the Day:")
    click.echo(f"   {tip.title}: {tip.description}")
    if tip.command:
        click.echo(f"   Try: {tip.command}")


@click.group()
def dev():
    """Developer experience and help commands."""
    pass


# Add commands to group
dev.add_command(tips)
dev.add_command(validate)
dev.add_command(error_help, name='error-help')
dev.add_command(performance)
dev.add_command(welcome)


if __name__ == '__main__':
    dev()
