"""
Error Handler Module
=================

This module provides functions for handling errors in the CLI.
"""

import logging
import sys
from typing import Any, Dict, List, Optional

import click

logger = logging.getLogger("vibe_check_cli.error_handler")


def format_error_results(results: Dict[str, Any]) -> str:
    """
    Format error results for display.

    Args:
        results: Error results dictionary

    Returns:
        Formatted error string
    """
    output = []

    # Add error header
    output.append("=== Analysis Error ===")
    output.append(f"Error: {results.get('error', 'Unknown error')}")

    # Add error details if available
    if "error_details" in results:
        output.append("\nError Details:")
        output.append(results["error_details"])

    # Add suggestions for fixing the error
    output.append("\nSuggestions:")

    # Check for specific error types
    error_msg = results.get("error", "").lower()

    if "timeout" in error_msg:
        output.append("- The operation timed out. Try the following:")
        output.append("  * Increase the timeout in the configuration")
        output.append("  * Try analyzing a smaller project first")
        output.append("  * Check system resources (CPU, memory)")
        output.append("  * Consider using --profile minimal for faster analysis")
    elif "file" in error_msg or "path" in error_msg or "directory" in error_msg:
        output.append("- There was an issue with file access. Try the following:")
        output.append("  * Check that the project path exists and is readable")
        output.append("  * Ensure you have proper permissions")
        output.append("  * Try a different output directory")
        output.append("  * Check if the path contains special characters")
    elif "import" in error_msg or "module" in error_msg:
        output.append("- There was an import or dependency issue. Try the following:")
        output.append("  * Ensure all required dependencies are installed")
        output.append("  * Check if the project has a valid Python environment")
        output.append("  * Try running: pip install vibe-check[all]")
    elif "config" in error_msg or "yaml" in error_msg:
        output.append("- There was a configuration issue. Try the following:")
        output.append("  * Check the configuration file syntax")
        output.append("  * Validate YAML formatting")
        output.append("  * Try running without a custom config file")
    elif "memory" in error_msg or "out of memory" in error_msg:
        output.append("- The system ran out of memory. Try the following:")
        output.append("  * Analyze a smaller project or subset of files")
        output.append("  * Use --profile minimal to reduce memory usage")
        output.append("  * Close other applications to free up memory")
    elif "permission" in error_msg or "access" in error_msg:
        output.append("- There was a permission issue. Try the following:")
        output.append("  * Run with appropriate permissions")
        output.append("  * Check file and directory permissions")
        output.append("  * Try a different output directory")
    else:
        output.append("- General troubleshooting steps:")
        output.append("  * Run with --verbose for more detailed logs")
        output.append("  * Check the log file for more details")
        output.append("  * Try analyzing a smaller project first")
        output.append("  * Use --profile minimal for basic analysis")

    # Add diagnostics information if available
    if "diagnostics" in results:
        output.append("\nDiagnostics Information:")
        for key, value in results["diagnostics"].items():
            output.append(f"- {key}: {value}")

    # Add visualizations information if available
    if "visualizations" in results:
        output.append("\nVisualizations:")
        for key, value in results["visualizations"].items():
            output.append(f"- {key}: {value}")

    return "\n".join(output)


def handle_analysis_error(results: Dict[str, Any]) -> None:
    """
    Handle analysis error with enhanced categorization and user guidance.

    Args:
        results: Error results dictionary
    """
    # Format and display the error
    formatted_error = format_error_results(results)
    click.echo(formatted_error)

    # Log the error with appropriate level
    error_msg = results.get("error", "Unknown error")
    error_msg_lower = error_msg.lower()

    # Categorize error severity
    if any(keyword in error_msg_lower for keyword in ["critical", "fatal", "crash"]):
        logger.critical(f"Critical analysis failure: {error_msg}")
        exit_code = 2  # Critical error
    elif any(keyword in error_msg_lower for keyword in ["timeout", "memory", "resource"]):
        logger.warning(f"Resource-related analysis failure: {error_msg}")
        exit_code = 3  # Resource error
    elif any(keyword in error_msg_lower for keyword in ["permission", "access", "file", "path"]):
        logger.error(f"Access-related analysis failure: {error_msg}")
        exit_code = 4  # Access error
    elif any(keyword in error_msg_lower for keyword in ["config", "yaml", "syntax"]):
        logger.error(f"Configuration-related analysis failure: {error_msg}")
        exit_code = 5  # Configuration error
    else:
        logger.error(f"Analysis failed: {error_msg}")
        exit_code = 1  # General error

    # Provide specific recommendations based on error type
    if "timeout" in error_msg_lower:
        click.echo("\n💡 Quick Fix: Try 'vibe-check analyze <path> --profile minimal' for faster analysis")
    elif "file" in error_msg_lower or "path" in error_msg_lower:
        click.echo("\n💡 Quick Fix: Check that the project path exists and is readable")
    elif "memory" in error_msg_lower:
        click.echo("\n💡 Quick Fix: Try analyzing a smaller subset of files or use --profile minimal")
    elif "config" in error_msg_lower:
        click.echo("\n💡 Quick Fix: Try running without a custom config file first")
    elif "import" in error_msg_lower or "module" in error_msg_lower:
        click.echo("\n💡 Quick Fix: Try 'pip install vibe-check[all]' to install all dependencies")

    # Add help information
    click.echo("\n📚 For more help:")
    click.echo("  • Run with --verbose for detailed logs")
    click.echo("  • Use 'vibe-check --help' for command options")
    click.echo("  • Check the documentation for troubleshooting guides")

    # Exit with appropriate error code
    sys.exit(exit_code)


def handle_validation_error(message: str, suggestions: Optional[List[str]] = None) -> None:
    """
    Handle validation errors with user-friendly messages.

    Args:
        message: Error message
        suggestions: Optional list of suggestions
    """
    click.echo(f"❌ Validation Error: {message}", err=True)

    if suggestions:
        click.echo("\n💡 Suggestions:", err=True)
        for suggestion in suggestions:
            click.echo(f"  • {suggestion}", err=True)

    logger.error(f"Validation error: {message}")
    sys.exit(6)  # Validation error code


def handle_unexpected_error(error: Exception, context: str = "operation") -> None:
    """
    Handle unexpected errors with debugging information.

    Args:
        error: The exception that occurred
        context: Context where the error occurred
    """
    import traceback

    click.echo(f"❌ Unexpected error during {context}: {error}", err=True)
    click.echo("\n🔍 This appears to be an unexpected issue.", err=True)
    click.echo("Please consider reporting this as a bug with the following information:", err=True)

    # Log the full traceback
    logger.error(f"Unexpected error during {context}: {error}")
    logger.error(f"Traceback: {traceback.format_exc()}")

    # Show abbreviated traceback to user
    click.echo(f"\nError type: {type(error).__name__}", err=True)
    click.echo(f"Error message: {str(error)}", err=True)

    click.echo("\n📚 For help:", err=True)
    click.echo("  • Run with --verbose for full error details", err=True)
    click.echo("  • Check the log files for complete traceback", err=True)
    click.echo("  • Report issues at: https://github.com/ptzajac/vibe_check/issues", err=True)

    sys.exit(7)  # Unexpected error code
