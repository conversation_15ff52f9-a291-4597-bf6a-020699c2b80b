"""
Quality Command Module
======================

CLI commands for quality enforcement and validation.

Commands:
- quality check: Run comprehensive quality validation
- quality report: Generate detailed quality report
- quality fix: Auto-fix quality violations where possible
"""

import click
import sys
from pathlib import Path
from typing import Optional

from vibe_check.core.quality import (
    AutomatedEnforcementEngine,
    ViolationType
)


@click.group()
def quality():
    """Quality enforcement and validation commands."""
    pass


@quality.command()
@click.argument('path', type=click.Path(exists=True), default='.')
@click.option('--severity', 
              type=click.Choice(['error', 'warning', 'info']),
              default='warning',
              help='Minimum severity level to report')
@click.option('--types',
              multiple=True,
              type=click.Choice([vt.value for vt in ViolationType]),
              help='Violation types to check')
@click.option('--output',
              type=click.Choice(['text', 'json']),
              default='text',
              help='Output format')
@click.option('--quiet', is_flag=True, help='Only show summary')
@click.option('--fail-on-violations', is_flag=True, 
              help='Exit with error code if violations found')
def check(path: str, severity: str, types: tuple, output: str, 
          quiet: bool, fail_on_violations: bool):
    """Run comprehensive quality validation on project or files."""
    
    path_obj = Path(path)
    engine = AutomatedEnforcementEngine()
    
    click.echo(f"🔍 Running quality check on: {path_obj}")
    
    # Determine what to check
    if path_obj.is_file():
        violations = engine.validate_files([path_obj])
    else:
        violations = engine.validate_project(path_obj)
    
    # Filter by severity
    severity_order = {"info": 0, "warning": 1, "error": 2}
    min_severity = severity_order[severity]
    filtered_violations = [
        v for v in violations 
        if severity_order.get(v.severity, 0) >= min_severity
    ]
    
    # Filter by types if specified
    if types:
        type_filter = set(ViolationType(t) for t in types)
        filtered_violations = [
            v for v in filtered_violations
            if v.violation_type in type_filter
        ]
    
    # Output results
    if output == 'json':
        import json
        violations_data = [
            {
                'file_path': v.file_path,
                'line_number': v.line_number,
                'violation_type': v.violation_type.value,
                'message': v.message,
                'suggestion': v.suggestion,
                'severity': v.severity
            }
            for v in filtered_violations
        ]
        click.echo(json.dumps(violations_data, indent=2))
    else:
        if not quiet:
            report = engine.generate_report(filtered_violations)
            click.echo(report)
        else:
            error_count = sum(1 for v in filtered_violations if v.severity == "error")
            warning_count = sum(1 for v in filtered_violations if v.severity == "warning")
            info_count = sum(1 for v in filtered_violations if v.severity == "info")
            
            if filtered_violations:
                click.echo(f"❌ Found {len(filtered_violations)} violations: "
                          f"{error_count} errors, {warning_count} warnings, {info_count} info")
            else:
                click.echo("✅ No quality violations found!")
    
    # Exit with appropriate code
    if fail_on_violations and filtered_violations:
        sys.exit(1)
    
    error_count = sum(1 for v in filtered_violations if v.severity == "error")
    if error_count > 0:
        sys.exit(1)


@quality.command()
@click.argument('path', type=click.Path(exists=True), default='.')
@click.option('--output-file', type=click.Path(), 
              help='Save report to file')
@click.option('--format',
              type=click.Choice(['text', 'html', 'json']),
              default='text',
              help='Report format')
def report(path: str, output_file: Optional[str], format: str):
    """Generate detailed quality report."""
    
    path_obj = Path(path)
    engine = AutomatedEnforcementEngine()
    
    click.echo(f"📊 Generating quality report for: {path_obj}")
    
    # Run validation
    if path_obj.is_file():
        violations = engine.validate_files([path_obj])
    else:
        violations = engine.validate_project(path_obj)
    
    # Generate report
    if format == 'text':
        report_content = engine.generate_report(violations)
    elif format == 'json':
        import json
        violations_data = [
            {
                'file_path': v.file_path,
                'line_number': v.line_number,
                'violation_type': v.violation_type.value,
                'message': v.message,
                'suggestion': v.suggestion,
                'severity': v.severity
            }
            for v in violations
        ]
        report_content = json.dumps(violations_data, indent=2)
    elif format == 'html':
        # HTML report generation (placeholder)
        report_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Vibe Check Quality Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .violation {{ margin: 10px 0; padding: 10px; border-left: 4px solid #ccc; }}
        .error {{ border-color: #f44336; }}
        .warning {{ border-color: #ff9800; }}
        .info {{ border-color: #2196f3; }}
    </style>
</head>
<body>
    <h1>Quality Report</h1>
    <p>Found {len(violations)} violations</p>
    <!-- Detailed violations would go here -->
</body>
</html>
        """
    
    # Output or save report
    if output_file:
        Path(output_file).write_text(report_content)
        click.echo(f"📄 Report saved to: {output_file}")
    else:
        click.echo(report_content)


@quality.command()
@click.argument('path', type=click.Path(exists=True), default='.')
@click.option('--dry-run', is_flag=True, 
              help='Show what would be fixed without making changes')
@click.option('--types',
              multiple=True,
              type=click.Choice([vt.value for vt in ViolationType]),
              help='Violation types to fix')
def fix(path: str, dry_run: bool, types: tuple):
    """Auto-fix quality violations where possible."""
    
    click.echo("🔧 Auto-fix functionality is coming soon!")
    click.echo("Currently supported manual fixes:")
    click.echo("  - Constants: Import from vibe_check.core.constants")
    click.echo("  - Terminology: Use standardized terms")
    click.echo("  - Config: Follow schema requirements")
    
    # Placeholder for future auto-fix implementation
    if dry_run:
        click.echo("🔍 Dry run mode - no changes would be made")


if __name__ == '__main__':
    quality()
