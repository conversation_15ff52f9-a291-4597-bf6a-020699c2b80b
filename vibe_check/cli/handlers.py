"""
CLI Command Handlers
==================

This module provides handler functions for CLI commands.
"""

import logging
import sys
from typing import Any, Dict, Optional

import click


logger = logging.getLogger(__name__)


def handle_analyze_command(project_path: str, config: Optional[str] = None, output: Optional[str] = None,
                          verbose: bool = False, quiet: bool = False, security_focused: bool = False,
                          performance_focused: bool = False, maintainability_focused: bool = False,
                          preset: Optional[str] = None, profile: str = "standard", analyze_trends: bool = False,
                          report_progress: bool = False, custom_report: bool = False,
                          debug: bool = False, log_file: Optional[str] = None, semantic: bool = False,
                          no_semantic: bool = False, vcs_mode: bool = False, rule_id: Optional[str] = None,
                          rule_ids: Optional[str] = None, exclude_rules: Optional[str] = None,
                          detailed: bool = False, categories: Optional[str] = None,
                          exclude_categories: Optional[str] = None, tools: Optional[str] = None,
                          exclude_tools: Optional[str] = None, no_save: bool = False) -> None:
    """Handle the analyze command."""
    from .commands import analyze_command
    from .error_handler import handle_analysis_error
    from .formatters import format_analysis_results

    try:
        # Log the command parameters
        logger.debug(f"Analyzing project: {project_path}")
        logger.debug(f"Config: {config}, Output: {output}")
        logger.debug(f"Verbose: {verbose}, Quiet: {quiet}")
        logger.debug(f"Security focused: {security_focused}, Performance focused: {performance_focused}, Maintainability focused: {maintainability_focused}")
        logger.debug(f"Preset: {preset}, Profile: {profile}")
        logger.debug(f"Analyze trends: {analyze_trends}, Report progress: {report_progress}")
        logger.debug(f"Custom report: {custom_report}")

        # Determine profile based on focus flags or explicit profile
        if security_focused:
            profile = "security"
        elif performance_focused:
            profile = "performance"
        elif maintainability_focused:
            profile = "maintainability"
        # Otherwise use the explicitly provided profile

        # Create context with priorities (legacy support)
        context: Dict[str, Any] = {"priorities": {}}
        if profile == "security":
            context["priorities"]["security"] = 0.8
            context["priorities"]["performance"] = 0.1
            context["priorities"]["maintainability"] = 0.1
        elif profile == "performance":
            context["priorities"]["security"] = 0.1
            context["priorities"]["performance"] = 0.8
            context["priorities"]["maintainability"] = 0.1
        elif profile == "maintainability":
            context["priorities"]["security"] = 0.1
            context["priorities"]["performance"] = 0.1
            context["priorities"]["maintainability"] = 0.8
        else:
            # Default priorities
            context["priorities"]["security"] = 0.33
            context["priorities"]["performance"] = 0.33
            context["priorities"]["maintainability"] = 0.34

        # Set up config overrides
        config_override: Dict[str, Any] = {}

        # Apply preset if specified
        if preset:
            config_override["preset"] = preset
            logger.debug(f"Using preset: {preset}")

        # Enable custom report if requested
        if custom_report:
            config_override["reporting"] = {
                "generate_custom_report": True
            }
            logger.debug("Custom report enabled")

        # Determine semantic analysis setting
        enable_semantic = semantic and not no_semantic
        if profile == "comprehensive":
            enable_semantic = True  # Always enable for comprehensive profile
        elif profile == "minimal":
            enable_semantic = False  # Never enable for minimal profile

        # Show a message to the user
        if not quiet:
            click.echo(f"Analyzing project: {project_path}")
            click.echo(f"Using analysis profile: {profile}")
            if preset:
                click.echo(f"Using preset: {preset}")
            if output:
                click.echo(f"Output directory: {output}")
            if debug:
                click.echo("Debug mode enabled")
            if log_file:
                click.echo(f"Logging to file: {log_file}")
            if enable_semantic:
                click.echo("Enhanced semantic analysis enabled")
            click.echo("Analysis in progress...")

        # Run the analysis
        # Validate VCS-specific parameters
        if rule_id and not vcs_mode:
            click.echo("❌ Error: --rule-id requires --vcs-mode to be enabled", err=True)
            return

        if rule_ids and not vcs_mode:
            click.echo("❌ Error: --rule-ids requires --vcs-mode to be enabled", err=True)
            return

        if exclude_rules and not vcs_mode:
            click.echo("❌ Error: --exclude-rules requires --vcs-mode to be enabled", err=True)
            return

        if categories and not vcs_mode:
            click.echo("❌ Error: --categories requires --vcs-mode to be enabled", err=True)
            return

        if exclude_categories and not vcs_mode:
            click.echo("❌ Error: --exclude-categories requires --vcs-mode to be enabled", err=True)
            return

        # Validate rule conflicts
        rule_options_count = sum(1 for opt in [rule_id, rule_ids] if opt)
        if rule_options_count > 1:
            click.echo("❌ Error: Cannot specify both --rule-id and --rule-ids simultaneously", err=True)
            return

        # Validate category names
        valid_categories = {"style", "security", "complexity", "documentation", "imports", "types"}
        if categories:
            category_list = [cat.strip().lower() for cat in categories.split(",")]
            invalid_categories = [cat for cat in category_list if cat not in valid_categories]
            if invalid_categories:
                click.echo(f"❌ Error: Invalid categories: {', '.join(invalid_categories)}", err=True)
                click.echo(f"Valid categories: {', '.join(sorted(valid_categories))}", err=True)
                return

        if exclude_categories:
            exclude_list = [cat.strip().lower() for cat in exclude_categories.split(",")]
            invalid_categories = [cat for cat in exclude_list if cat not in valid_categories]
            if invalid_categories:
                click.echo(f"❌ Error: Invalid exclude categories: {', '.join(invalid_categories)}", err=True)
                click.echo(f"Valid categories: {', '.join(sorted(valid_categories))}", err=True)
                return

        # Validate tool names
        valid_tools = {"ruff", "mypy", "bandit", "pylint", "flake8", "black", "isort"}
        if tools:
            tool_list = [tool.strip().lower() for tool in tools.split(",")]
            invalid_tools = [tool for tool in tool_list if tool not in valid_tools]
            if invalid_tools:
                click.echo(f"❌ Error: Invalid tools: {', '.join(invalid_tools)}", err=True)
                click.echo(f"Valid tools: {', '.join(sorted(valid_tools))}", err=True)
                return

        if exclude_tools:
            exclude_list = [tool.strip().lower() for tool in exclude_tools.split(",")]
            invalid_tools = [tool for tool in exclude_list if tool not in valid_tools]
            if invalid_tools:
                click.echo(f"❌ Error: Invalid exclude tools: {', '.join(invalid_tools)}", err=True)
                click.echo(f"Valid tools: {', '.join(sorted(valid_tools))}", err=True)
                return

        results = analyze_command(
            project_path=project_path,
            config_path=config,
            output_dir=output,
            verbose=verbose,
            quiet=quiet,
            config_override=config_override,
            analyze_trends=analyze_trends,
            report_progress=report_progress,
            profile=profile,
            vcs_mode=vcs_mode,
            rule_id=rule_id,
            rule_ids=rule_ids,
            exclude_rules=exclude_rules,
            detailed=detailed,
            categories=categories,
            exclude_categories=exclude_categories,
            tools=tools,
            exclude_tools=exclude_tools,
            no_save=no_save
        )

        # Check if there was an error
        if isinstance(results, dict) and "error" in results:
            # Handle the error using our error handler
            handle_analysis_error(results)
            # This will exit with code 1, so we won't reach here

        # Format and display the results
        try:
            formatted_results = format_analysis_results(results)
            click.echo(formatted_results)
        except Exception as format_error:
            logger.error(f"Error formatting results: {format_error}")
            # Fallback to simple output
            if hasattr(results, 'total_file_count'):
                click.echo(f"Analysis completed successfully!")
                click.echo(f"Files analyzed: {results.total_file_count}")
                click.echo(f"Issues found: {getattr(results, 'issue_count', 'N/A')}")
            else:
                click.echo("Analysis completed successfully!")
                click.echo(f"Results: {results}")

    except Exception as e:
        import traceback
        logger.error(f"Error during analysis: {e}")
        logger.error(traceback.format_exc())
        click.echo(f"Error during analysis: {e}")
        click.echo("For more details, run with --verbose flag")
        sys.exit(1)


def handle_profiles_command() -> None:
    """Handle the profiles command."""
    click.echo("Available Analysis Profiles:")
    click.echo("=" * 30)

    profiles = {
        "minimal": "Basic analysis with essential tools only",
        "standard": "Standard analysis with import analysis enabled",
        "comprehensive": "Full analysis with all features and visualizations"
    }

    for profile_name, description in profiles.items():
        click.echo(f"\n{profile_name}:")
        click.echo(f"  Description: {description}")
        if profile_name == "minimal":
            click.echo("  Import Analysis: No")
            click.echo("  Visualizations: No")
        elif profile_name == "standard":
            click.echo("  Import Analysis: Yes")
            click.echo("  Visualizations: No")
        else:  # comprehensive
            click.echo("  Import Analysis: Yes")
            click.echo("  Visualizations: Yes")


def handle_deps_command() -> None:
    """Handle the dependency status command."""
    try:
        from ..core.dependency_manager import dependency_manager
        dependency_manager.print_status_report()
    except Exception as e:
        logger.error(f"Error checking dependencies: {e}")
        click.echo(f"Error checking dependencies: {e}", err=True)
        sys.exit(1)


def handle_gui_command(project_path: Optional[str] = None) -> None:
    """Handle the GUI command."""
    try:
        from ..ui.gui import run_gui
        print("🖥️  Launching Vibe Check GUI...")
        run_gui(project_path=project_path)
    except ImportError as e:
        logger.error(f"GUI launch failed: {e}")
        print(f"❌ GUI launch failed: {e}")
        print("💡 GUI uses built-in Tkinter - no additional dependencies required")
        sys.exit(1)
    except Exception as e:
        logger.error(f"GUI error: {e}")
        print(f"❌ GUI error: {e}")
        sys.exit(1)


def handle_tui_command(project_path: str, config: Optional[str] = None) -> None:
    """Handle the TUI command."""
    from .commands import tui_command
    tui_command(project_path, config)


def handle_web_command(project_path: str, config: Optional[str] = None, host: str = "localhost", port: int = 8000) -> None:
    """Handle the web command."""
    from .commands import web_command
    web_command(project_path, config, host, port)


def handle_plugin_list_command() -> None:
    """Handle the plugin list command."""
    from .commands import plugin_command
    plugin_command("list")


def handle_plugin_install_command(plugin_name: str) -> None:
    """Handle the plugin install command."""
    from .commands import plugin_command
    plugin_command("install", plugin_name)


def handle_plugin_uninstall_command(plugin_name: str) -> None:
    """Handle the plugin uninstall command."""
    from .commands import plugin_command
    plugin_command("uninstall", plugin_name)


def handle_debug_command(project_path: str, output: Optional[str] = None, verbose: bool = False, timeout: float = 120.0, 
                        open_timeline: bool = False, dependency_analysis: bool = False, registry_analysis: bool = False) -> None:
    """
    Handle the debug command (simplified - actor system removed).

    This command provides basic debugging information for the analysis process.
    The actor system has been removed as part of Phase 0 stabilization.
    """
    try:
        # Show a message to the user
        click.echo(f"Debugging analysis process for project: {project_path}")
        click.echo(f"Timeout: {timeout} seconds")
        if output:
            click.echo(f"Output directory: {output}")
        if verbose:
            click.echo("Verbose mode enabled")
        click.echo("Debug analysis in progress...")

        # Run a simple analysis for debugging purposes
        try:
            from pathlib import Path
            from ..core.simple_analyzer import simple_analyze_project
            from ..core.config import load_config

            # Load default config
            config = load_config()

            # Run simple analysis
            results = simple_analyze_project(
                project_path=Path(project_path),
                output_dir=Path(output) if output else Path("debug_output"),
                config=config
            )

            # Display results
            click.echo("\n=== Debug Results ===")
            click.echo("Simple analysis completed successfully")

            if hasattr(results, 'total_file_count'):
                click.echo(f"Files analyzed: {results.total_file_count}")
            if hasattr(results, 'total_line_count'):
                click.echo(f"Lines analyzed: {results.total_line_count}")

        except Exception as e:
            click.echo(f"Error during debug analysis: {e}")
            if verbose:
                import traceback
                click.echo(f"Error details: {traceback.format_exc()}")
            sys.exit(1)

        # Actor system has been removed - these options are no longer supported
        if registry_analysis:
            click.echo("\nNote: Registry analysis is no longer available (actor system removed)")
        if dependency_analysis:
            click.echo("\nNote: Dependency analysis is no longer available (actor system removed)")
        if open_timeline:
            click.echo("\nNote: Timeline visualization is no longer available (actor system removed)")

        # All actor system analysis features have been removed

    except Exception as e:
        import traceback
        logger.error(f"Error during debugging: {e}")
        logger.error(traceback.format_exc())
        click.echo(f"Error during debugging: {e}")
        click.echo("For more details, run with --verbose flag")
        sys.exit(1)
