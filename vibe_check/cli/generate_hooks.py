"""
CLI commands for pre-commit hook generation.
"""

import click
from pathlib import Path
from typing import List, Optional

from ..core.hooks.generator import (
    PreCommitHookGenerator, 
    HookConfig, 
    ValidationLevel, 
    HookMode,
    create_hook_generator
)


@click.command()
@click.option('--level', type=click.Choice(['minimal', 'standard', 'strict']), 
              default='standard', help='Validation level')
@click.option('--mode', type=click.Choice(['merge', 'fresh', 'append']), 
              default='merge', help='Generation mode')
@click.option('--output', type=click.Path(), default='.pre-commit-config.yaml',
              help='Output file path')
@click.option('--external-tools', help='Comma-separated list of external tools (ruff,mypy,black,isort)')
@click.option('--timeout', type=int, default=30, help='Hook timeout in seconds')
@click.option('--categories', help='Comma-separated list of rule categories to enable')
@click.option('--exclude-rules', help='Comma-separated list of rules to exclude')
@click.option('--dry-run', is_flag=True, help='Show what would be generated without saving')
@click.option('--no-backup', is_flag=True, help='Do not backup existing configuration')
@click.option('--verbose', is_flag=True, help='Show detailed output')
def generate_hooks(
    level: str,
    mode: str,
    output: str,
    external_tools: Optional[str],
    timeout: int,
    categories: Optional[str],
    exclude_rules: Optional[str],
    dry_run: bool,
    no_backup: bool,
    verbose: bool
):
    """Generate pre-commit configuration with Vibe Check integration."""
    
    # Parse external tools
    tools_list = []
    if external_tools:
        tools_list = [tool.strip() for tool in external_tools.split(',')]
        
        # Validate tool names
        valid_tools = {'ruff', 'mypy', 'black', 'isort', 'bandit', 'pylint', 'flake8'}
        invalid_tools = set(tools_list) - valid_tools
        if invalid_tools:
            click.echo(f"❌ Invalid tools: {', '.join(invalid_tools)}")
            click.echo(f"Valid tools: {', '.join(sorted(valid_tools))}")
            return
    
    # Parse categories
    categories_set = set()
    if categories:
        categories_set = {cat.strip() for cat in categories.split(',')}
        
        # Validate categories
        valid_categories = {'style', 'security', 'complexity', 'documentation', 'imports', 'types'}
        invalid_categories = categories_set - valid_categories
        if invalid_categories:
            click.echo(f"❌ Invalid categories: {', '.join(invalid_categories)}")
            click.echo(f"Valid categories: {', '.join(sorted(valid_categories))}")
            return
    
    # Parse excluded rules
    excluded_rules = set()
    if exclude_rules:
        excluded_rules = {rule.strip() for rule in exclude_rules.split(',')}
    
    # Create configuration
    config = HookConfig(
        validation_level=ValidationLevel(level),
        mode=HookMode(mode),
        timeout_seconds=timeout,
        external_tools=tools_list,
        enabled_categories=categories_set or {'style', 'security', 'complexity', 'imports'},
        disabled_rules=excluded_rules,
        backup_existing=not no_backup,
        dry_run=dry_run,
        verbose=verbose
    )
    
    # Create generator
    generator = PreCommitHookGenerator(config)
    
    # Validate configuration
    issues = generator.validate_config()
    if issues:
        click.echo("⚠️  Configuration Issues:")
        for issue in issues:
            click.echo(f"  • {issue}")
        
        if not click.confirm("Continue anyway?"):
            return
    
    # Show configuration summary
    click.echo("🔧 Pre-commit Hook Configuration:")
    click.echo(f"  Validation Level: {level}")
    click.echo(f"  Mode: {mode}")
    click.echo(f"  Timeout: {timeout}s")
    click.echo(f"  Output: {output}")
    
    if tools_list:
        click.echo(f"  External Tools: {', '.join(tools_list)}")
    
    if categories_set:
        click.echo(f"  Categories: {', '.join(sorted(categories_set))}")
    
    if excluded_rules:
        click.echo(f"  Excluded Rules: {', '.join(sorted(excluded_rules))}")
    
    # Estimate execution time
    estimated_time = generator.estimate_execution_time()
    click.echo(f"  Estimated Execution Time: {estimated_time:.1f}s")
    
    # Load existing configuration
    output_path = Path(output)
    existing_config = generator.load_existing_config(output_path)
    
    if existing_config and mode == 'merge':
        click.echo(f"📄 Found existing configuration with {len(existing_config.get('repos', []))} repositories")
    elif existing_config and mode == 'fresh':
        click.echo("📄 Existing configuration will be replaced")
    elif existing_config and mode == 'append':
        click.echo("📄 Vibe Check will be appended to existing configuration")
    else:
        click.echo("📄 Creating new configuration file")
    
    # Generate configuration
    try:
        full_config = generator.generate_full_config(existing_config)
        
        if verbose or dry_run:
            click.echo("\n📋 Generated Configuration:")
            click.echo("=" * 50)
            import yaml
            click.echo(yaml.dump(full_config, default_flow_style=False, sort_keys=False))
            click.echo("=" * 50)
        
        # Save configuration
        if generator.save_config(full_config, output_path):
            if not dry_run:
                click.echo(f"✅ Successfully generated pre-commit configuration: {output}")
                
                # Show next steps
                click.echo("\n🚀 Next Steps:")
                click.echo("  1. Install pre-commit: pip install pre-commit")
                click.echo("  2. Install hooks: pre-commit install")
                click.echo("  3. Test setup: pre-commit run --all-files")
                
                # Show performance tips
                if estimated_time > 20:
                    click.echo("\n💡 Performance Tips:")
                    click.echo("  • Consider using 'minimal' validation level for faster commits")
                    click.echo("  • Use '--categories style,security' to focus on critical issues")
                    click.echo("  • Enable parallel processing with multiple workers")
            else:
                click.echo("✅ Dry run completed - no files were modified")
        else:
            click.echo("❌ Failed to generate configuration")
            
    except Exception as e:
        click.echo(f"❌ Error generating configuration: {e}")
        if verbose:
            import traceback
            traceback.print_exc()


@click.command()
@click.option('--config-file', type=click.Path(exists=True), 
              default='.pre-commit-config.yaml', help='Pre-commit config file to validate')
def validate_hooks(config_file: str):
    """Validate existing pre-commit configuration."""
    
    config_path = Path(config_file)
    
    if not config_path.exists():
        click.echo(f"❌ Configuration file not found: {config_file}")
        return
    
    # Load and validate configuration
    generator = PreCommitHookGenerator()
    config = generator.load_existing_config(config_path)
    
    if not config:
        click.echo(f"❌ Invalid configuration file: {config_file}")
        return
    
    click.echo(f"📋 Validating pre-commit configuration: {config_file}")
    
    # Check for Vibe Check integration
    vibe_check_found = False
    total_repos = len(config.get('repos', []))
    total_hooks = 0
    
    for repo in config.get('repos', []):
        if isinstance(repo, dict):
            repo_url = repo.get('repo', '')
            hooks = repo.get('hooks', [])
            total_hooks += len(hooks)
            
            if 'vibe_check' in repo_url or 'vibe-check' in repo_url:
                vibe_check_found = True
                click.echo(f"✅ Found Vibe Check repository: {repo_url}")
                click.echo(f"   Version: {repo.get('rev', 'unknown')}")
                
                # Check hooks
                for hook in hooks:
                    if isinstance(hook, dict) and hook.get('id') == 'vibe-check':
                        click.echo(f"   Hook: {hook.get('name', 'Vibe Check')}")
                        
                        args = hook.get('args', [])
                        if args:
                            click.echo(f"   Arguments: {' '.join(args)}")
    
    # Summary
    click.echo(f"\n📊 Configuration Summary:")
    click.echo(f"  Total Repositories: {total_repos}")
    click.echo(f"  Total Hooks: {total_hooks}")
    click.echo(f"  Vibe Check Integration: {'✅ Found' if vibe_check_found else '❌ Not found'}")
    
    # Global settings
    if 'fail_fast' in config:
        click.echo(f"  Fail Fast: {config['fail_fast']}")
    
    if 'default_stages' in config:
        click.echo(f"  Default Stages: {', '.join(config['default_stages'])}")
    
    if not vibe_check_found:
        click.echo("\n💡 To add Vibe Check integration, run:")
        click.echo("   vibe-check generate-hooks")


@click.group()
def hooks():
    """Pre-commit hook management commands."""
    pass


# Add commands to group
hooks.add_command(generate_hooks)
hooks.add_command(validate_hooks)


if __name__ == '__main__':
    hooks()
