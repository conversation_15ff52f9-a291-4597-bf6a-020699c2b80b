"""
Preset Management CLI Commands
==============================

CLI commands for managing Vibe Check analysis presets including creation,
listing, validation, export/import, and deletion operations.
"""

import asyncio
import json
from pathlib import Path
from typing import Optional, List, Dict, Any

import click
import yaml

from ..core.presets import PresetManager, PresetDataModel, PresetMetadata
from ..core.presets.models import (
    RuleConfiguration, ExternalToolConfiguration, 
    AnalysisConfiguration, PresetType
)
from ..core.presets.exceptions import (
    PresetError, PresetNotFoundError, PresetValidationError
)
from ..core.constants import AnalysisProfile
from ..core.version import __version__


@click.group(name='preset')
def preset_group() -> None:
    """Manage analysis presets for customized configurations."""
    pass


@preset_group.command('list')
@click.option('--type', '-t', 'preset_type', 
              type=click.Choice(['builtin', 'user', 'shared']),
              help='Filter presets by type')
@click.option('--format', '-f', 'output_format',
              type=click.Choice(['table', 'json', 'yaml']),
              default='table', help='Output format')
@click.option('--detailed', '-d', is_flag=True,
              help='Show detailed preset information')
def list_presets(preset_type: Optional[str], output_format: str, detailed: bool) -> None:
    """List all available presets."""
    async def _list_presets() -> None:
        try:
            manager = PresetManager()
            
            # Convert string to enum if provided
            type_filter = None
            if preset_type:
                type_filter = PresetType(preset_type)
            
            presets = await manager.list_presets(type_filter)
            
            if output_format == 'json':
                click.echo(json.dumps(presets, indent=2, default=str))
            elif output_format == 'yaml':
                click.echo(yaml.dump(presets, default_flow_style=False))
            else:
                # Table format
                if not presets:
                    click.echo("No presets found.")
                    return
                
                click.echo("📋 Available Presets")
                click.echo("=" * 50)
                
                for name, info in presets.items():
                    preset_type_display = info.get('type', 'unknown').upper()
                    status = info.get('status', 'unknown')
                    metadata = info.get('metadata', {})
                    
                    # Status emoji
                    status_emoji = "✅" if status == "valid" else "❌"
                    
                    click.echo(f"\n{status_emoji} {name} ({preset_type_display})")
                    
                    if detailed:
                        description = metadata.get('description', 'No description')
                        version = metadata.get('version', 'unknown')
                        author = metadata.get('author', 'unknown')
                        
                        click.echo(f"   Description: {description}")
                        click.echo(f"   Version: {version}")
                        click.echo(f"   Author: {author}")
                        
                        if 'error' in info:
                            click.echo(f"   Error: {info['error']}")
                    else:
                        description = metadata.get('description', 'No description')
                        click.echo(f"   {description}")
        
        except Exception as e:
            click.echo(f"❌ Failed to list presets: {e}", err=True)
            raise click.ClickException(str(e))
    
    asyncio.run(_list_presets())


@preset_group.command('create')
@click.argument('name')
@click.option('--description', '-d', required=True,
              help='Preset description')
@click.option('--profile', '-p', 
              type=click.Choice(['minimal', 'standard', 'comprehensive']),
              default='standard', help='Analysis profile')
@click.option('--categories', '-c',
              help='Comma-separated list of rule categories to include')
@click.option('--exclude-categories', 
              help='Comma-separated list of rule categories to exclude')
@click.option('--tools', '-t',
              help='Comma-separated list of external tools to include')
@click.option('--exclude-tools',
              help='Comma-separated list of external tools to exclude')
@click.option('--vcs-mode', is_flag=True,
              help='Enable VCS mode for built-in rules')
@click.option('--fast-mode', is_flag=True,
              help='Enable fast mode for pre-commit optimization')
@click.option('--detailed', is_flag=True,
              help='Enable detailed output by default')
@click.option('--author', help='Preset author name')
@click.option('--tags', help='Comma-separated list of tags')
@click.option('--inherit-from', help='Base preset to inherit from')
@click.option('--dry-run', is_flag=True,
              help='Show what would be created without saving')
def create_preset(name: str, description: str, profile: str,
                 categories: Optional[str], exclude_categories: Optional[str],
                 tools: Optional[str], exclude_tools: Optional[str],
                 vcs_mode: bool, fast_mode: bool, detailed: bool,
                 author: Optional[str], tags: Optional[str],
                 inherit_from: Optional[str], dry_run: bool) -> None:
    """Create a new analysis preset."""
    async def _create_preset() -> None:
        try:
            # Parse comma-separated lists
            categories_list = [c.strip() for c in categories.split(',')] if categories else None
            exclude_categories_list = [c.strip() for c in exclude_categories.split(',')] if exclude_categories else None
            tools_list = [t.strip() for t in tools.split(',')] if tools else None
            exclude_tools_list = [t.strip() for t in exclude_tools.split(',')] if exclude_tools else None
            tags_list = [t.strip() for t in tags.split(',')] if tags else None
            
            # Create preset data
            from datetime import datetime, timezone
            now = datetime.now(timezone.utc).isoformat()
            
            preset_data: PresetDataModel = {
                "metadata": PresetMetadata(
                    name=name,
                    description=description,
                    version="1.0.0",
                    author=author or "User",
                    created_at=now,
                    updated_at=now,
                    tags=tags_list,
                    compatibility_version=__version__
                ),
                "rule_config": RuleConfiguration(
                    categories=categories_list,
                    exclude_categories=exclude_categories_list,
                    vcs_rules_only=vcs_mode,
                    enable_framework_rules=True
                ),
                "tool_config": ExternalToolConfiguration(
                    tools=tools_list,
                    exclude_tools=exclude_tools_list,
                    parallel_execution=True,
                    timeout_seconds=300
                ),
                "analysis_config": AnalysisConfiguration(
                    profile=profile,  # Use string value directly
                    vcs_mode=vcs_mode,
                    fast_mode=fast_mode,
                    detailed=detailed,
                    quiet=False,
                    no_save=False,
                    output_formats=["markdown"],
                    use_gitignore=True
                ),
                "inheritance": inherit_from,
                "overrides": {},
                "schema_version": "1.0"
            }
            
            if dry_run:
                click.echo("🧪 Dry run - showing what would be created:")
                click.echo(yaml.dump(preset_data, default_flow_style=False))
                return
            
            # Save preset
            manager = PresetManager()
            preset_file = await manager.save_preset(preset_data, overwrite=False)
            
            click.echo(f"✅ Created preset '{name}' at {preset_file}")
            
        except PresetValidationError as e:
            click.echo(f"❌ Preset validation failed:", err=True)
            click.echo(e.get_detailed_message(), err=True)
            raise click.ClickException("Preset validation failed")
        except PresetError as e:
            click.echo(f"❌ Failed to create preset: {e}", err=True)
            raise click.ClickException(str(e))
        except Exception as e:
            click.echo(f"❌ Unexpected error: {e}", err=True)
            raise click.ClickException(str(e))
    
    asyncio.run(_create_preset())


@preset_group.command('validate')
@click.argument('name')
@click.option('--detailed', '-d', is_flag=True,
              help='Show detailed validation results')
def validate_preset(name: str, detailed: bool) -> None:
    """Validate a preset configuration."""
    async def _validate_preset() -> None:
        try:
            manager = PresetManager()
            
            # Find preset file
            preset_file = await manager._find_preset_file(name)
            if not preset_file:
                raise PresetNotFoundError(name, manager._get_search_paths())
            
            # Validate preset
            validation_result = await manager.validate_preset_file(preset_file)
            
            if validation_result["valid"]:
                click.echo(f"✅ Preset '{name}' is valid")
            else:
                click.echo(f"❌ Preset '{name}' validation failed")
            
            if detailed or not validation_result["valid"]:
                if validation_result.get("errors"):
                    click.echo("\nErrors:")
                    for error in validation_result["errors"]:
                        click.echo(f"  - {error}")
                
                if validation_result.get("warnings"):
                    click.echo("\nWarnings:")
                    for warning in validation_result["warnings"]:
                        click.echo(f"  - {warning}")
            
            if not validation_result["valid"]:
                raise click.ClickException("Preset validation failed")
                
        except PresetNotFoundError as e:
            click.echo(f"❌ {e}", err=True)
            raise click.ClickException(str(e))
        except Exception as e:
            click.echo(f"❌ Validation error: {e}", err=True)
            raise click.ClickException(str(e))
    
    asyncio.run(_validate_preset())


@preset_group.command('delete')
@click.argument('name')
@click.option('--force', '-f', is_flag=True,
              help='Force deletion without confirmation')
def delete_preset(name: str, force: bool) -> None:
    """Delete a user preset."""
    async def _delete_preset() -> None:
        try:
            if not force:
                if not click.confirm(f"Are you sure you want to delete preset '{name}'?"):
                    click.echo("Deletion cancelled.")
                    return
            
            manager = PresetManager()
            deleted = await manager.delete_preset(name, force=True)
            
            if deleted:
                click.echo(f"✅ Deleted preset '{name}'")
            else:
                click.echo(f"❌ Preset '{name}' not found")
                raise click.ClickException("Preset not found")
                
        except PresetError as e:
            click.echo(f"❌ {e}", err=True)
            raise click.ClickException(str(e))
        except Exception as e:
            click.echo(f"❌ Deletion error: {e}", err=True)
            raise click.ClickException(str(e))
    
    asyncio.run(_delete_preset())


@preset_group.command('export')
@click.argument('name')
@click.argument('output_file', type=click.Path())
@click.option('--format', '-f', 'export_format',
              type=click.Choice(['yaml', 'json']),
              default='yaml', help='Export format')
def export_preset(name: str, output_file: str, export_format: str) -> None:
    """Export a preset to a file."""
    async def _export_preset() -> None:
        try:
            manager = PresetManager()
            output_path = Path(output_file)

            exported_file = await manager.export_preset(name, output_path, export_format)
            click.echo(f"✅ Exported preset '{name}' to {exported_file}")

        except PresetNotFoundError as e:
            click.echo(f"❌ {e}", err=True)
            raise click.ClickException(str(e))
        except Exception as e:
            click.echo(f"❌ Export error: {e}", err=True)
            raise click.ClickException(str(e))

    asyncio.run(_export_preset())


@preset_group.command('import')
@click.argument('input_file', type=click.Path(exists=True))
@click.option('--overwrite', '-o', is_flag=True,
              help='Overwrite existing preset if it exists')
def import_preset(input_file: str, overwrite: bool) -> None:
    """Import a preset from a file."""
    async def _import_preset() -> None:
        try:
            manager = PresetManager()
            input_path = Path(input_file)

            preset_name = await manager.import_preset(input_path, overwrite=overwrite)
            click.echo(f"✅ Imported preset '{preset_name}' from {input_path}")

        except PresetValidationError as e:
            click.echo(f"❌ Imported preset validation failed:", err=True)
            click.echo(e.get_detailed_message(), err=True)
            raise click.ClickException("Preset validation failed")
        except PresetError as e:
            click.echo(f"❌ {e}", err=True)
            raise click.ClickException(str(e))
        except Exception as e:
            click.echo(f"❌ Import error: {e}", err=True)
            raise click.ClickException(str(e))

    asyncio.run(_import_preset())


@preset_group.command('show')
@click.argument('name')
@click.option('--format', '-f', 'output_format',
              type=click.Choice(['yaml', 'json']),
              default='yaml', help='Output format')
def show_preset(name: str, output_format: str) -> None:
    """Show preset configuration."""
    async def _show_preset() -> None:
        try:
            manager = PresetManager()
            preset_data = await manager.load_preset(name)

            if output_format == 'json':
                click.echo(json.dumps(preset_data, indent=2, default=str))
            else:
                click.echo(yaml.dump(preset_data, default_flow_style=False))

        except PresetNotFoundError as e:
            click.echo(f"❌ {e}", err=True)
            raise click.ClickException(str(e))
        except Exception as e:
            click.echo(f"❌ Error loading preset: {e}", err=True)
            raise click.ClickException(str(e))

    asyncio.run(_show_preset())


@preset_group.command('template')
@click.option('--list', '-l', 'list_templates', is_flag=True,
              help='List available preset templates')
@click.option('--create', '-c', 'template_name',
              help='Create preset from template')
@click.option('--name', '-n', 'preset_name',
              help='Name for the new preset (required with --create)')
@click.option('--description', '-d',
              help='Description for the new preset')
@click.option('--author', '-a',
              help='Author name for the new preset')
def template_command(list_templates: bool, template_name: Optional[str],
                    preset_name: Optional[str], description: Optional[str],
                    author: Optional[str]) -> None:
    """Manage preset templates."""
    async def _template_command() -> None:
        try:
            from vibe_check.core.presets.templates import PresetTemplateManager

            template_manager = PresetTemplateManager()

            if list_templates:
                # List available templates
                templates = template_manager.list_templates()

                if not templates:
                    click.echo("No preset templates available.")
                    return

                click.echo("📋 Available Preset Templates")
                click.echo("=" * 50)

                for name, info in templates.items():
                    click.echo(f"\n✨ {name}")
                    click.echo(f"   Description: {info['description']}")
                    click.echo(f"   Use Case: {info['use_case']}")
                    click.echo(f"   Tags: {info['tags']}")

            elif template_name:
                # Create preset from template
                if not preset_name:
                    click.echo("❌ --name is required when creating from template", err=True)
                    raise click.ClickException("Missing preset name")

                new_preset = template_manager.create_preset_from_template(
                    template_name, preset_name, description, author
                )

                if not new_preset:
                    click.echo(f"❌ Template '{template_name}' not found", err=True)
                    raise click.ClickException("Template not found")

                # Save the new preset
                from vibe_check.core.presets import PresetManager
                manager = PresetManager()
                preset_file = await manager.save_preset(new_preset, overwrite=False)

                click.echo(f"✅ Created preset '{preset_name}' from template '{template_name}' at {preset_file}")

            else:
                click.echo("Use --list to see available templates or --create to create from template")

        except Exception as e:
            click.echo(f"❌ Template command error: {e}", err=True)
            raise click.ClickException(str(e))

    asyncio.run(_template_command())


@preset_group.command('generate')
@click.argument('preset_name')
@click.option('--project-path', '-p', type=click.Path(exists=True, path_type=Path),
              default=Path.cwd(), help='Path to project for analysis')
@click.option('--author', '-a', help='Author name for the preset')
@click.option('--dry-run', is_flag=True,
              help='Show what would be generated without saving')
def generate_preset(preset_name: str, project_path: Path,
                   author: Optional[str], dry_run: bool) -> None:
    """Generate preset dynamically based on project analysis."""
    async def _generate_preset() -> None:
        try:
            from vibe_check.core.presets.dynamic_generator import DynamicPresetGenerator

            generator = DynamicPresetGenerator()

            click.echo(f"🔍 Analyzing project at {project_path}...")

            # Generate preset based on project analysis
            preset_data = await generator.generate_preset(project_path, preset_name, author)

            if dry_run:
                click.echo("🧪 Dry run - showing what would be generated:")
                import yaml
                click.echo(yaml.dump(preset_data, default_flow_style=False))
                return

            # Save the generated preset
            from vibe_check.core.presets import PresetManager
            manager = PresetManager()
            preset_file = await manager.save_preset(preset_data, overwrite=False)

            click.echo(f"✅ Generated preset '{preset_name}' based on project analysis at {preset_file}")

            # Show analysis summary
            from vibe_check.core.presets.dynamic_generator import ProjectAnalyzer
            analyzer = ProjectAnalyzer(project_path)
            analysis = await analyzer.analyze_project()

            click.echo(f"\n📊 Project Analysis Summary:")
            click.echo(f"   Project Type: {analysis.get('project_type', 'unknown')}")
            click.echo(f"   Frameworks: {', '.join(analysis.get('frameworks', []))}")
            click.echo(f"   Complexity: {analysis.get('complexity', 'unknown')}")
            click.echo(f"   Security Needs: {analysis.get('security_needs', 'unknown')}")
            click.echo(f"   Performance Focus: {analysis.get('performance_focus', 'unknown')}")

        except Exception as e:
            click.echo(f"❌ Generate preset error: {e}", err=True)
            raise click.ClickException(str(e))

    asyncio.run(_generate_preset())


@preset_group.command('analyze')
@click.option('--project-path', '-p', type=click.Path(exists=True, path_type=Path),
              default=Path.cwd(), help='Path to project for analysis')
@click.option('--detailed', '-d', is_flag=True,
              help='Show detailed analysis results')
def analyze_project(project_path: Path, detailed: bool) -> None:
    """Analyze project characteristics for preset recommendations."""
    async def _analyze_project() -> None:
        try:
            from vibe_check.core.presets.dynamic_generator import ProjectAnalyzer

            analyzer = ProjectAnalyzer(project_path)

            click.echo(f"🔍 Analyzing project at {project_path}...")
            analysis = await analyzer.analyze_project()

            click.echo(f"\n📊 Project Analysis Results")
            click.echo("=" * 40)

            click.echo(f"\n🎯 Project Type: {analysis.get('project_type', 'unknown')}")
            click.echo(f"🔧 Frameworks: {', '.join(analysis.get('frameworks', [])) or 'None detected'}")
            click.echo(f"📈 Complexity: {analysis.get('complexity', 'unknown')}")
            click.echo(f"🔒 Security Needs: {analysis.get('security_needs', 'unknown')}")
            click.echo(f"⚡ Performance Focus: {analysis.get('performance_focus', 'unknown')}")

            if detailed:
                file_patterns = analysis.get('file_patterns', {})
                dependencies = analysis.get('dependencies', {})

                click.echo(f"\n📁 File Patterns:")
                for pattern, count in file_patterns.items():
                    click.echo(f"   {pattern}: {count}")

                click.echo(f"\n📦 Dependencies:")
                for category, deps in dependencies.items():
                    if deps:
                        click.echo(f"   {category}: {', '.join(deps)}")

            # Recommend templates
            from vibe_check.core.presets.templates import PresetTemplateManager
            template_manager = PresetTemplateManager()

            # Simple recommendation logic
            project_type = analysis.get('project_type', 'general')
            security_needs = analysis.get('security_needs', 'medium')

            recommended_templates = []
            if project_type == 'data-science':
                recommended_templates.append('data-science')
            elif project_type == 'web-application':
                recommended_templates.append('web-dev')
            elif project_type == 'library':
                recommended_templates.append('library-dev')

            if security_needs == 'high':
                recommended_templates.append('security-focused')

            if recommended_templates:
                click.echo(f"\n💡 Recommended Templates:")
                for template in recommended_templates:
                    template_info = template_manager.list_templates().get(template, {})
                    click.echo(f"   ✨ {template}: {template_info.get('description', 'No description')}")

                click.echo(f"\n💡 To create a preset from a template:")
                click.echo(f"   vibe-check preset template --create {recommended_templates[0]} --name my-preset")

                click.echo(f"\n💡 To generate a dynamic preset:")
                click.echo(f"   vibe-check preset generate my-dynamic-preset --project-path {project_path}")

        except Exception as e:
            click.echo(f"❌ Project analysis error: {e}", err=True)
            raise click.ClickException(str(e))

    asyncio.run(_analyze_project())
