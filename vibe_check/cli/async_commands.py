"""
Async CLI Commands
=================

Enhanced async CLI commands that work seamlessly with the async unified analyzer
and visualization systems while maintaining backward compatibility.
"""

import asyncio
import click
import time
import sys
from pathlib import Path
from typing import Optional, Dict, Any
import logging

from vibe_check.core.async_unified_analyzer import AsyncUnifiedAnalysisEngine, AsyncAnalysisConfig
from vibe_check.core.visualization.async_dashboard_engine import AsyncDashboardEngine, AsyncRenderConfig
from vibe_check.cli.unified_formatters import get_formatter

logger = logging.getLogger(__name__)


class AsyncCLIContext:
    """Context for async CLI operations"""
    
    def __init__(self):
        self.analysis_engine = None
        self.dashboard_engine = None
        self.config = AsyncAnalysisConfig()
        self.render_config = AsyncRenderConfig()
    
    async def __aenter__(self):
        self.analysis_engine = AsyncUnifiedAnalysisEngine(self.config)
        self.dashboard_engine = AsyncDashboardEngine(self.render_config)
        await self.dashboard_engine.__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.dashboard_engine:
            await self.dashboard_engine.__aexit__(exc_type, exc_val, exc_tb)


def async_command(f):
    """Decorator to run async functions in CLI commands"""
    def wrapper(*args, **kwargs):
        return asyncio.run(f(*args, **kwargs))
    return wrapper


@click.group()
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose output')
@click.option('--max-workers', default=None, type=int, help='Maximum number of workers')
@click.option('--enable-streaming', is_flag=True, help='Enable streaming analysis')
@click.pass_context
def async_cli(ctx, verbose, max_workers, enable_streaming):
    """Async Vibe Check CLI with enhanced performance"""
    if verbose:
        logging.basicConfig(level=logging.INFO)
    
    # Store config in context
    ctx.ensure_object(dict)
    ctx.obj['verbose'] = verbose
    ctx.obj['max_workers'] = max_workers
    ctx.obj['enable_streaming'] = enable_streaming


@async_cli.command()
@click.argument('path', type=click.Path(exists=True))
@click.option('--output', '-o', help='Output file path')
@click.option('--format', '-f', default='json', type=click.Choice(['json', 'yaml', 'table', 'markdown']))
@click.option('--streaming', is_flag=True, help='Use streaming analysis')
@click.option('--max-concurrent', default=50, type=int, help='Max concurrent file operations')
@click.option('--timeout', default=300, type=float, help='Analysis timeout in seconds')
@click.pass_context
@async_command
async def analyze(ctx, path, output, format, streaming, max_concurrent, timeout):
    """Async project analysis with enhanced performance"""
    start_time = time.time()
    
    # Configure analysis
    config = AsyncAnalysisConfig(
        max_workers=ctx.obj.get('max_workers') or 4,
        enable_streaming=streaming or ctx.obj.get('enable_streaming', False),
        max_concurrent_files=max_concurrent,
        analysis_timeout=timeout
    )
    
    click.echo(f"🚀 Starting async analysis of: {path}")
    click.echo(f"⚙️  Configuration:")
    click.echo(f"   • Max workers: {config.max_workers}")
    click.echo(f"   • Max concurrent files: {config.max_concurrent_files}")
    click.echo(f"   • Streaming: {config.enable_streaming}")
    click.echo(f"   • Timeout: {config.analysis_timeout}s")
    
    try:
        async with AsyncCLIContext() as cli_ctx:
            cli_ctx.config = config
            
            if streaming:
                # Streaming analysis with progress
                click.echo("📊 Streaming analysis results...")
                file_count = 0
                
                async for file_metrics in cli_ctx.analysis_engine.analyze_project_streaming(path):
                    file_count += 1
                    if file_count % 10 == 0:
                        click.echo(f"   Processed {file_count} files...")
                
                # Get final results
                result = await cli_ctx.analysis_engine.analyze_project(path)
            else:
                # Standard analysis
                result = await cli_ctx.analysis_engine.analyze_project(path)
            
            analysis_time = time.time() - start_time
            
            # Display results
            click.echo(f"\n✅ Analysis completed in {analysis_time:.2f}s")
            click.echo(f"📊 Results:")
            click.echo(f"   • Files analyzed: {result.files_analyzed}")
            click.echo(f"   • Total lines: {result.project_metrics.total_lines}")
            click.echo(f"   • Average complexity: {result.project_metrics.average_complexity:.1f}")
            click.echo(f"   • Quality score: {result.project_metrics.quality_score:.1f}/10")
            click.echo(f"   • Analysis speed: {result.files_analyzed / analysis_time:.1f} files/sec")
            
            # Format and output results
            formatter = get_formatter(format)
            formatted_output = formatter.format(result.to_dict())
            
            if output:
                output_path = Path(output)
                output_path.parent.mkdir(parents=True, exist_ok=True)
                
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(formatted_output)
                
                click.echo(f"📄 Results saved to: {output_path}")
            else:
                click.echo(f"\n📋 Results ({format} format):")
                if format == 'table':
                    click.echo(formatted_output)
                else:
                    # Truncate long outputs for console
                    if len(formatted_output) > 2000:
                        click.echo(formatted_output[:2000] + "\n... (truncated)")
                    else:
                        click.echo(formatted_output)
    
    except Exception as e:
        click.echo(f"❌ Analysis failed: {e}", err=True)
        if ctx.obj.get('verbose'):
            import traceback
            traceback.print_exc()
        sys.exit(1)


@async_cli.command()
@click.argument('path', type=click.Path(exists=True))
@click.option('--output', '-o', default='dashboard.html', help='Output HTML file')
@click.option('--dashboard-id', default='analysis_dashboard', help='Dashboard ID')
@click.option('--title', default='Project Analysis Dashboard', help='Dashboard title')
@click.option('--auto-refresh', is_flag=True, help='Enable auto-refresh')
@click.option('--real-time', is_flag=True, help='Enable real-time updates')
@click.pass_context
@async_command
async def dashboard(ctx, path, output, dashboard_id, title, auto_refresh, real_time):
    """Generate async dashboard from analysis results"""
    start_time = time.time()
    
    click.echo(f"🎨 Creating async dashboard for: {path}")
    
    try:
        async with AsyncCLIContext() as cli_ctx:
            # Configure for dashboard generation
            cli_ctx.config.enable_streaming = False  # Use batch for dashboard
            cli_ctx.render_config.real_time_updates = real_time
            
            # Run analysis
            click.echo("🔍 Running analysis...")
            result = await cli_ctx.analysis_engine.analyze_project(path)
            
            # Create dashboard
            click.echo("🎨 Creating dashboard...")
            dashboard = cli_ctx.dashboard_engine.create_dashboard(
                dashboard_id, title, f"Analysis dashboard for {Path(path).name}"
            )
            
            # Add panels based on analysis results
            from vibe_check.core.visualization.dashboard_engine import PanelType
            
            # Summary metrics
            dashboard.add_panel({
                'id': 'total_files',
                'title': 'Total Files',
                'panel_type': PanelType.METRIC,
                'position': {'x': 0, 'y': 0, 'width': 3, 'height': 2},
                'config': {'metric_name': 'total_files'}
            })
            
            dashboard.add_panel({
                'id': 'avg_complexity',
                'title': 'Average Complexity',
                'panel_type': PanelType.METRIC,
                'position': {'x': 3, 'y': 0, 'width': 3, 'height': 2},
                'config': {'metric_name': 'avg_complexity'}
            })
            
            dashboard.add_panel({
                'id': 'quality_score',
                'title': 'Quality Score',
                'panel_type': PanelType.METRIC,
                'position': {'x': 6, 'y': 0, 'width': 3, 'height': 2},
                'config': {'metric_name': 'quality_score'}
            })
            
            dashboard.add_panel({
                'id': 'total_issues',
                'title': 'Total Issues',
                'panel_type': PanelType.METRIC,
                'position': {'x': 9, 'y': 0, 'width': 3, 'height': 2},
                'config': {'metric_name': 'total_issues'}
            })
            
            # Charts
            dashboard.add_panel({
                'id': 'complexity_distribution',
                'title': 'Complexity Distribution',
                'panel_type': PanelType.CHART,
                'position': {'x': 0, 'y': 2, 'width': 6, 'height': 4},
                'config': {'chart_type': 'bar'}
            })
            
            dashboard.add_panel({
                'id': 'quality_by_directory',
                'title': 'Quality by Directory',
                'panel_type': PanelType.CHART,
                'position': {'x': 6, 'y': 2, 'width': 6, 'height': 4},
                'config': {'chart_type': 'line'}
            })
            
            # Prepare dashboard data
            dashboard_data = {
                'total_files': {'value': result.project_metrics.total_files},
                'avg_complexity': {'value': round(result.project_metrics.average_complexity, 1)},
                'quality_score': {'value': round(result.project_metrics.quality_score, 1)},
                'total_issues': {'value': result.project_metrics.total_issues},
                'complexity_distribution': {
                    'x': ['Low (0-10)', 'Medium (10-20)', 'High (20-40)', 'Very High (40+)'],
                    'y': [
                        len([fm for fm in result.file_metrics if fm.complexity < 10]),
                        len([fm for fm in result.file_metrics if 10 <= fm.complexity < 20]),
                        len([fm for fm in result.file_metrics if 20 <= fm.complexity < 40]),
                        len([fm for fm in result.file_metrics if fm.complexity >= 40])
                    ]
                },
                'quality_by_directory': {
                    'x': [Path(dm.directory_path).name for dm in result.directory_metrics[:10]],
                    'y': [dm.average_quality for dm in result.directory_metrics[:10]]
                }
            }
            
            # Render and save dashboard
            click.echo("💾 Rendering dashboard...")
            await cli_ctx.dashboard_engine.save_dashboard_async(
                dashboard_id, Path(output), dashboard_data
            )
            
            generation_time = time.time() - start_time
            
            click.echo(f"✅ Dashboard created in {generation_time:.2f}s")
            click.echo(f"📄 Dashboard saved to: {output}")
            click.echo(f"🌐 Open in browser: file://{Path(output).absolute()}")
            
            if real_time:
                click.echo("🔄 Real-time updates enabled (WebSocket required)")
    
    except Exception as e:
        click.echo(f"❌ Dashboard generation failed: {e}", err=True)
        if ctx.obj.get('verbose'):
            import traceback
            traceback.print_exc()
        sys.exit(1)


@async_cli.command()
@click.option('--max-workers', default=4, type=int, help='Number of workers')
@click.option('--max-concurrent', default=50, type=int, help='Max concurrent operations')
@click.option('--iterations', default=3, type=int, help='Number of test iterations')
@click.pass_context
@async_command
async def benchmark(ctx, max_workers, max_concurrent, iterations):
    """Run async performance benchmarks"""
    click.echo("🏁 Running async performance benchmarks")
    
    # Create test data
    test_dir = Path("async_benchmark_test")
    test_dir.mkdir(exist_ok=True)
    
    try:
        # Create test files
        click.echo(f"📁 Creating {max_concurrent} test files...")
        for i in range(max_concurrent):
            test_file = test_dir / f"test_{i}.py"
            test_file.write_text(f'''
def function_{i}():
    """Function {i} with complexity"""
    result = []
    for j in range(20):
        if j % 2 == 0:
            try:
                if j > 10:
                    result.append(j * {i})
                else:
                    result.append(j + {i})
            except Exception as e:
                continue
        else:
            with open("temp.txt", "w") as f:
                f.write(str(j))
    return result

class Class_{i}:
    def __init__(self):
        self.data = list(range(50))
    
    def process(self):
        return sum(self.data)
''')
        
        # Run benchmarks
        results = []
        
        for iteration in range(iterations):
            click.echo(f"\n🔄 Iteration {iteration + 1}/{iterations}")
            
            config = AsyncAnalysisConfig(
                max_workers=max_workers,
                max_concurrent_files=max_concurrent,
                enable_streaming=True
            )
            
            async with AsyncCLIContext() as cli_ctx:
                cli_ctx.config = config
                
                start_time = time.time()
                result = await cli_ctx.analysis_engine.analyze_project(test_dir)
                analysis_time = time.time() - start_time
                
                files_per_second = result.files_analyzed / analysis_time
                results.append({
                    'iteration': iteration + 1,
                    'files_analyzed': result.files_analyzed,
                    'analysis_time': analysis_time,
                    'files_per_second': files_per_second
                })
                
                click.echo(f"   Files: {result.files_analyzed}")
                click.echo(f"   Time: {analysis_time:.3f}s")
                click.echo(f"   Speed: {files_per_second:.1f} files/sec")
        
        # Calculate averages
        avg_time = sum(r['analysis_time'] for r in results) / len(results)
        avg_speed = sum(r['files_per_second'] for r in results) / len(results)
        
        click.echo(f"\n📊 BENCHMARK RESULTS")
        click.echo(f"=" * 30)
        click.echo(f"Average analysis time: {avg_time:.3f}s")
        click.echo(f"Average speed: {avg_speed:.1f} files/sec")
        click.echo(f"Configuration:")
        click.echo(f"  • Max workers: {max_workers}")
        click.echo(f"  • Max concurrent: {max_concurrent}")
        click.echo(f"  • Iterations: {iterations}")
        
        # Performance assessment
        baseline_speed = 5571  # From Week 2 validation
        improvement = ((avg_speed - baseline_speed) / baseline_speed) * 100
        
        click.echo(f"\n🎯 Performance vs Baseline:")
        click.echo(f"  • Baseline: {baseline_speed} files/sec")
        click.echo(f"  • Current: {avg_speed:.1f} files/sec")
        click.echo(f"  • Improvement: {improvement:+.1f}%")
        
        if improvement >= 50:
            click.echo("✅ Target 50% improvement ACHIEVED!")
        elif improvement >= 25:
            click.echo("⚠️  Good improvement, approaching target")
        else:
            click.echo("❌ Target improvement not yet achieved")
    
    finally:
        # Cleanup
        import shutil
        shutil.rmtree(test_dir, ignore_errors=True)


if __name__ == '__main__':
    async_cli()
