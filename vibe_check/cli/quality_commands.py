"""
File: vibe_check/cli/quality_commands.py
Purpose: CLI commands for validator quality framework
Related Files: main.py, commands.py
Dependencies: click, pathlib, json
"""

import click
import json
from pathlib import Path
from typing import Optional

from vibe_check.core.quality import (
    ValidatorQualityFramework,
    get_all_validators,
    QualityMetrics,
    ValidationResult
)


@click.group(name='quality')
def quality_group():
    """Validator quality framework commands."""
    pass


@quality_group.command('validate')
@click.option('--validator', '-v', help='Specific validator to validate (e.g., ConstantsValidator)')
@click.option('--output', '-o', type=click.Path(), help='Output file for results (JSON)')
@click.option('--report', '-r', is_flag=True, help='Generate detailed report')
@click.option('--config', '-c', type=click.Path(exists=True), help='Quality framework configuration file')
def validate_validators(validator: Optional[str], output: Optional[str], report: bool, config: Optional[str]):
    """Validate quality of validators using comprehensive test framework."""
    click.echo("🔍 Starting Validator Quality Validation...")
    
    try:
        # Load configuration if provided
        framework_config = {}
        if config:
            with open(config, 'r') as f:
                framework_config = json.load(f)
        
        # Initialize quality framework
        framework = ValidatorQualityFramework(framework_config)
        
        # Get validators to test
        all_validators = get_all_validators()
        
        if validator:
            # Test specific validator
            target_validators = {}
            for violation_type, validator_instance in all_validators.items():
                if validator_instance.__class__.__name__ == validator:
                    target_validators[violation_type] = validator_instance
                    break
            
            if not target_validators:
                click.echo(f"❌ Validator '{validator}' not found")
                click.echo(f"Available validators: {', '.join(v.__class__.__name__ for v in all_validators.values())}")
                return
        else:
            # Test all validators
            target_validators = all_validators
        
        click.echo(f"📊 Testing {len(target_validators)} validator(s)...")
        
        # Run validation
        results = framework.validate_all_validators(target_validators)
        
        # Display summary
        click.echo("\n📋 VALIDATION SUMMARY")
        click.echo("=" * 40)
        
        total_validators = len(results)
        passed_gates = sum(1 for r in results.values() if r.passed_quality_gates)
        
        click.echo(f"Total Validators: {total_validators}")
        click.echo(f"Passed Quality Gates: {passed_gates}/{total_validators}")
        
        # Display individual results
        for validator_name, result in results.items():
            metrics = result.metrics
            status = "✅ PASS" if result.passed_quality_gates else "❌ FAIL"
            
            click.echo(f"\n🔧 {validator_name} - Grade: {metrics.quality_grade} {status}")
            click.echo(f"   Accuracy: {metrics.accuracy:.2%} | Precision: {metrics.precision:.2%} | Recall: {metrics.recall:.2%}")
            click.echo(f"   FP Rate: {metrics.false_positive_rate:.2%} | FN Rate: {metrics.false_negative_rate:.2%}")
            click.echo(f"   Execution Time: {metrics.execution_time_ms:.1f}ms | Test Cases: {metrics.test_cases_passed}/{metrics.test_cases_total}")
            
            if result.quality_issues:
                click.echo(f"   Issues: {'; '.join(result.quality_issues[:2])}")
            
            if result.recommendations:
                click.echo(f"   Recommendation: {result.recommendations[0]}")
        
        # Generate detailed report if requested
        if report:
            report_content = framework.generate_quality_report(results)
            click.echo(f"\n{report_content}")
        
        # Save results if output specified
        if output:
            output_path = Path(output)
            framework.save_results(results, output_path)
            click.echo(f"\n💾 Results saved to {output_path}")
        
        # Exit with appropriate code
        if passed_gates < total_validators:
            click.echo(f"\n⚠️  {total_validators - passed_gates} validator(s) failed quality gates")
            exit(1)
        else:
            click.echo(f"\n🎉 All validators passed quality gates!")
            
    except Exception as e:
        click.echo(f"❌ Validation failed: {e}")
        exit(1)


@quality_group.command('report')
@click.option('--input', '-i', type=click.Path(exists=True), required=True, help='Input results file (JSON)')
@click.option('--format', '-f', type=click.Choice(['text', 'json', 'html']), default='text', help='Report format')
@click.option('--output', '-o', type=click.Path(), help='Output file for report')
def generate_report(input: str, format: str, output: Optional[str]):
    """Generate quality report from saved validation results."""
    try:
        framework = ValidatorQualityFramework()
        results = framework.load_results(Path(input))
        
        if not results:
            click.echo("❌ No results found in input file")
            return
        
        if format == 'text':
            report_content = framework.generate_quality_report(results)
        elif format == 'json':
            report_content = json.dumps({
                'summary': {
                    'total_validators': len(results),
                    'passed_gates': sum(1 for r in results.values() if r.passed_quality_gates),
                    'avg_accuracy': sum(r.metrics.accuracy for r in results.values()) / len(results),
                    'avg_execution_time': sum(r.metrics.execution_time_ms for r in results.values()) / len(results)
                },
                'validators': {name: result.to_dict() for name, result in results.items()}
            }, indent=2)
        elif format == 'html':
            # Basic HTML report
            report_content = _generate_html_report(results)
        
        if output:
            with open(output, 'w') as f:
                f.write(report_content)
            click.echo(f"📄 Report saved to {output}")
        else:
            click.echo(report_content)
            
    except Exception as e:
        click.echo(f"❌ Report generation failed: {e}")
        exit(1)


@quality_group.command('benchmark')
@click.option('--iterations', '-n', default=10, help='Number of benchmark iterations')
@click.option('--validator', '-v', help='Specific validator to benchmark')
def benchmark_validators(iterations: int, validator: Optional[str]):
    """Benchmark validator performance."""
    click.echo(f"⏱️  Starting validator performance benchmark ({iterations} iterations)...")
    
    try:
        framework = ValidatorQualityFramework()
        all_validators = get_all_validators()
        
        if validator:
            target_validators = {}
            for violation_type, validator_instance in all_validators.items():
                if validator_instance.__class__.__name__ == validator:
                    target_validators[violation_type] = validator_instance
                    break
            
            if not target_validators:
                click.echo(f"❌ Validator '{validator}' not found")
                return
        else:
            target_validators = all_validators
        
        click.echo(f"🔧 Benchmarking {len(target_validators)} validator(s)...")
        
        # Run multiple iterations
        all_results = []
        for i in range(iterations):
            click.echo(f"   Iteration {i+1}/{iterations}...", nl=False)
            results = framework.validate_all_validators(target_validators)
            all_results.append(results)
            click.echo(" ✓")
        
        # Calculate average performance
        click.echo("\n📊 BENCHMARK RESULTS")
        click.echo("=" * 30)
        
        for validator_name in target_validators.keys():
            validator_class_name = target_validators[validator_name].__class__.__name__
            execution_times = [results[validator_class_name].metrics.execution_time_ms for results in all_results]
            
            avg_time = sum(execution_times) / len(execution_times)
            min_time = min(execution_times)
            max_time = max(execution_times)
            
            click.echo(f"🔧 {validator_class_name}")
            click.echo(f"   Average: {avg_time:.2f}ms")
            click.echo(f"   Min: {min_time:.2f}ms | Max: {max_time:.2f}ms")
            click.echo(f"   Std Dev: {(sum((t - avg_time) ** 2 for t in execution_times) / len(execution_times)) ** 0.5:.2f}ms")
        
    except Exception as e:
        click.echo(f"❌ Benchmark failed: {e}")
        exit(1)


@quality_group.command('test-cases')
@click.option('--validator', '-v', help='Show test cases for specific validator')
@click.option('--add', '-a', is_flag=True, help='Add new test case interactively')
@click.option('--export', '-e', type=click.Path(), help='Export test cases to JSON file')
def manage_test_cases(validator: Optional[str], add: bool, export: Optional[str]):
    """Manage test cases for validators."""
    try:
        framework = ValidatorQualityFramework()
        
        if add:
            _add_test_case_interactive(framework)
        elif export:
            framework.test_case_manager.save_test_cases_to_file(Path(export))
            click.echo(f"📄 Test cases exported to {export}")
        else:
            _show_test_cases(framework, validator)
            
    except Exception as e:
        click.echo(f"❌ Test case management failed: {e}")
        exit(1)


def _generate_html_report(results: dict) -> str:
    """Generate basic HTML report."""
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Validator Quality Report</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .validator { border: 1px solid #ddd; margin: 10px 0; padding: 15px; }
            .pass { border-left: 5px solid #4CAF50; }
            .fail { border-left: 5px solid #f44336; }
            .metrics { display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; }
            .metric { background: #f5f5f5; padding: 10px; text-align: center; }
        </style>
    </head>
    <body>
        <h1>🔍 Validator Quality Report</h1>
    """
    
    for validator_name, result in results.items():
        status_class = "pass" if result.passed_quality_gates else "fail"
        status_text = "✅ PASS" if result.passed_quality_gates else "❌ FAIL"
        
        html += f"""
        <div class="validator {status_class}">
            <h2>{validator_name} - Grade: {result.metrics.quality_grade} {status_text}</h2>
            <div class="metrics">
                <div class="metric">
                    <strong>Accuracy</strong><br>
                    {result.metrics.accuracy:.2%}
                </div>
                <div class="metric">
                    <strong>Precision</strong><br>
                    {result.metrics.precision:.2%}
                </div>
                <div class="metric">
                    <strong>Recall</strong><br>
                    {result.metrics.recall:.2%}
                </div>
            </div>
        </div>
        """
    
    html += "</body></html>"
    return html


def _add_test_case_interactive(framework: ValidatorQualityFramework):
    """Add test case interactively."""
    click.echo("📝 Adding new test case...")
    # Implementation for interactive test case addition
    click.echo("Interactive test case addition not yet implemented")


def _show_test_cases(framework: ValidatorQualityFramework, validator: Optional[str]):
    """Show test cases."""
    click.echo("📋 Test Cases:")
    # Implementation for showing test cases
    click.echo("Test case display not yet implemented")


# Register commands with main CLI
def register_quality_commands(cli_group):
    """Register quality commands with main CLI."""
    cli_group.add_command(quality_group)
