"""
CLI Commands for Rule Documentation Generation
==============================================

Commands for generating and managing VCS rule documentation.
"""

import click
from pathlib import Path
from typing import Optional
import logging

from vibe_check.core.vcs.documentation import RuleDocumentationGenerator
from vibe_check.core.vcs.rules.decorators import get_all_rule_metadata
import logging

logger = logging.getLogger(__name__)


@click.group(name='docs')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
def docs_group(verbose: bool) -> None:
    """Generate and manage VCS rule documentation."""
    if verbose:
        logging.basicConfig(level=logging.DEBUG)
    else:
        logging.basicConfig(level=logging.INFO)


@docs_group.command('generate')
@click.option('--rule-id', '-r', help='Generate documentation for specific rule')
@click.option('--output-dir', '-o', type=click.Path(), default='docs/rules', 
              help='Output directory for documentation files')
@click.option('--format', '-f', type=click.Choice(['markdown', 'html']), 
              default='markdown', help='Output format')
@click.option('--include-index', is_flag=True, default=True, 
              help='Generate rules index file')
@click.option('--dry-run', is_flag=True, help='Show what would be generated without creating files')
def generate_docs(rule_id: Optional[str], output_dir: str, format: str,
                  include_index: bool, dry_run: bool):
    """Generate rule documentation."""
    try:
        # Import rules to trigger decorator registration
        from vibe_check.core.vcs.rules import style_rules

        generator = RuleDocumentationGenerator()
        output_path = Path(output_dir)
        
        if dry_run:
            click.echo(f"🔍 Dry run mode - showing what would be generated:")
            click.echo(f"   Output directory: {output_path.absolute()}")
            click.echo(f"   Format: {format}")
            click.echo(f"   Include index: {include_index}")
        
        if rule_id:
            # Generate documentation for specific rule
            if dry_run:
                click.echo(f"   Would generate documentation for rule: {rule_id}")
                return
            
            doc_content = generator.generate_rule_documentation(rule_id)
            if not doc_content:
                click.echo(f"❌ Failed to generate documentation for rule {rule_id}", err=True)
                return
            
            # Save to file
            output_path.mkdir(parents=True, exist_ok=True)
            filename = f"{rule_id.lower()}.md"
            file_path = output_path / filename
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(doc_content)
            
            click.echo(f"✅ Generated documentation for {rule_id}: {file_path}")
            
        else:
            # Generate documentation for all rules
            rule_metadata = get_all_rule_metadata()
            total_rules = len(rule_metadata)
            
            if dry_run:
                click.echo(f"   Would generate documentation for {total_rules} rules:")
                for rid in sorted(rule_metadata.keys()):
                    click.echo(f"     - {rid}")
                if include_index:
                    click.echo(f"     - index.md (rules index)")
                return
            
            click.echo(f"📝 Generating documentation for {total_rules} rules...")
            
            # Generate all documentation
            saved_files = generator.save_documentation(output_path, include_index)
            
            # Report results
            rule_files = {k: v for k, v in saved_files.items() if k != 'index'}
            click.echo(f"✅ Generated documentation for {len(rule_files)} rules")
            
            if include_index and 'index' in saved_files:
                click.echo(f"✅ Generated rules index: {saved_files['index']}")
            
            click.echo(f"📁 All files saved to: {output_path.absolute()}")
            
    except Exception as e:
        click.echo(f"❌ Documentation generation failed: {e}", err=True)
        if logger.isEnabledFor(logging.DEBUG):
            logger.exception("Documentation generation error")
        raise click.ClickException(str(e))


@docs_group.command('list')
@click.option('--category', '-c', help='Filter by rule category')
@click.option('--format', '-f', type=click.Choice(['table', 'json']), 
              default='table', help='Output format')
def list_rules(category: Optional[str], format: str):
    """List all available rules with documentation status."""
    try:
        # Import rules to trigger decorator registration
        from vibe_check.core.vcs.rules import style_rules

        rule_metadata = get_all_rule_metadata()
        
        if category:
            # Filter by category
            filtered_rules = {
                rule_id: metadata for rule_id, metadata in rule_metadata.items()
                if metadata['category'].value == category
            }
            rule_metadata = filtered_rules
        
        if format == 'json':
            import json
            output = {}
            for rule_id, metadata in rule_metadata.items():
                output[rule_id] = {
                    'name': metadata['name'],
                    'category': metadata['category'].value,
                    'severity': metadata['severity'].value,
                    'description': metadata['description'],
                    'auto_fixable': metadata['auto_fixable'],
                    'tags': metadata['tags']
                }
            click.echo(json.dumps(output, indent=2))
        else:
            # Table format
            click.echo(f"📋 Available Rules ({len(rule_metadata)} total)")
            click.echo()
            
            # Group by category
            rules_by_category = {}
            for rule_id, metadata in rule_metadata.items():
                cat = metadata['category'].value
                if cat not in rules_by_category:
                    rules_by_category[cat] = []
                rules_by_category[cat].append((rule_id, metadata))
            
            for cat, rules in sorted(rules_by_category.items()):
                click.echo(f"🏷️  {cat.upper()} ({len(rules)} rules)")
                click.echo("   " + "─" * 60)
                
                for rule_id, metadata in sorted(rules):
                    severity_emoji = {
                        'error': '🚨',
                        'warning': '⚠️',
                        'info': 'ℹ️',
                        'hint': '💡'
                    }.get(metadata['severity'].value, '📋')
                    
                    auto_fix = ' 🔧' if metadata['auto_fixable'] else ''
                    
                    click.echo(f"   {rule_id}: {metadata['name']} {severity_emoji}{auto_fix}")
                    click.echo(f"      {metadata['description']}")
                
                click.echo()
                
    except Exception as e:
        click.echo(f"❌ Failed to list rules: {e}", err=True)
        raise click.ClickException(str(e))


@docs_group.command('validate')
@click.option('--rule-id', '-r', help='Validate specific rule documentation')
@click.option('--check-examples', is_flag=True, help='Validate code examples')
@click.option('--check-config', is_flag=True, help='Validate configuration schemas')
def validate_docs(rule_id: Optional[str], check_examples: bool, check_config: bool):
    """Validate rule documentation completeness and accuracy."""
    try:
        generator = RuleDocumentationGenerator()
        rule_metadata = get_all_rule_metadata()
        
        rules_to_check = [rule_id] if rule_id else list(rule_metadata.keys())
        
        click.echo(f"🔍 Validating documentation for {len(rules_to_check)} rules...")
        
        validation_results = []
        
        for rid in rules_to_check:
            result = {
                'rule_id': rid,
                'has_metadata': rid in rule_metadata,
                'can_generate_doc': False,
                'has_config_schema': False,
                'has_examples': False,
                'issues': []
            }
            
            if not result['has_metadata']:
                result['issues'].append("Missing rule metadata")
                validation_results.append(result)
                continue
            
            # Try to generate documentation
            try:
                doc = generator.extractor.extract_rule_documentation(rid)
                if doc:
                    result['can_generate_doc'] = True
                    result['has_config_schema'] = bool(doc.configuration_schema)
                    result['has_examples'] = bool(doc.examples)
                    
                    # Check for issues
                    if not doc.description or doc.description == f"Analysis rule: {doc.name}":
                        result['issues'].append("Generic or missing description")
                    
                    if not doc.tags:
                        result['issues'].append("No tags specified")
                    
                    if check_config and not doc.configuration_schema:
                        result['issues'].append("No configuration schema available")
                    
                    if check_examples and not doc.examples:
                        result['issues'].append("No code examples available")
                        
                else:
                    result['issues'].append("Failed to extract documentation")
                    
            except Exception as e:
                result['issues'].append(f"Documentation generation error: {e}")
            
            validation_results.append(result)
        
        # Report results
        total_rules = len(validation_results)
        valid_rules = sum(1 for r in validation_results if not r['issues'])
        
        click.echo(f"\n📊 Validation Results: {valid_rules}/{total_rules} rules valid")
        
        if valid_rules == total_rules:
            click.echo("✅ All rules have valid documentation!")
        else:
            click.echo("\n⚠️  Issues found:")
            
            for result in validation_results:
                if result['issues']:
                    click.echo(f"\n   {result['rule_id']}:")
                    for issue in result['issues']:
                        click.echo(f"     - {issue}")
        
        # Summary statistics
        rules_with_config = sum(1 for r in validation_results if r['has_config_schema'])
        rules_with_examples = sum(1 for r in validation_results if r['has_examples'])
        
        click.echo(f"\n📈 Statistics:")
        click.echo(f"   Rules with configuration schema: {rules_with_config}/{total_rules}")
        click.echo(f"   Rules with examples: {rules_with_examples}/{total_rules}")
        
    except Exception as e:
        click.echo(f"❌ Documentation validation failed: {e}", err=True)
        raise click.ClickException(str(e))


@docs_group.command('preview')
@click.argument('rule_id')
@click.option('--section', '-s', help='Show specific section (overview, config, examples)')
def preview_docs(rule_id: str, section: Optional[str]):
    """Preview generated documentation for a rule."""
    try:
        # Import rules to trigger decorator registration
        from vibe_check.core.vcs.rules import style_rules

        generator = RuleDocumentationGenerator()
        
        doc_content = generator.generate_rule_documentation(rule_id)
        if not doc_content:
            click.echo(f"❌ Could not generate documentation for rule {rule_id}", err=True)
            return
        
        if section:
            # Show specific section
            lines = doc_content.split('\n')
            section_lines = []
            in_section = False
            section_header = f"## {section.title()}"
            
            for line in lines:
                if line.startswith('## ') and line != section_header:
                    if in_section:
                        break
                    in_section = False
                elif line == section_header:
                    in_section = True
                
                if in_section:
                    section_lines.append(line)
            
            if section_lines:
                click.echo('\n'.join(section_lines))
            else:
                click.echo(f"❌ Section '{section}' not found in documentation for {rule_id}")
        else:
            # Show full documentation
            click.echo(doc_content)
            
    except Exception as e:
        click.echo(f"❌ Preview failed: {e}", err=True)
        raise click.ClickException(str(e))


if __name__ == '__main__':
    docs_group()
