"""
Knowledge Base Management CLI

This module provides CLI commands for managing the framework knowledge base,
including adding new rules, frameworks, and community contributions.
"""

import click
import yaml
from pathlib import Path
from typing import Optional
    FrameworkKnowledgeBase, FrameworkKnowledge, FrameworkRule
)


@click.group()
def knowledge():
    """Manage VibeCheck framework knowledge base."""
    pass


@knowledge.command()
def stats():
    """Show knowledge base statistics."""
    kb = FrameworkKnowledgeBase()
    stats = kb.get_statistics()
    
    click.echo("📊 VibeCheck Knowledge Base Statistics")
    click.echo("=" * 40)
    
    click.echo(f"Total Frameworks: {stats['total_frameworks']}")
    click.echo(f"Total Rules: {stats['total_rules']}")
    
    click.echo("\n📋 Frameworks:")
    for framework in stats['frameworks']:
        fw_knowledge = kb.get_framework_knowledge(framework)
        if fw_knowledge:
            click.echo(f"  • {fw_knowledge.name}: {len(fw_knowledge.rules)} rules")
    
    click.echo("\n📊 Rules by Category:")
    for category, count in stats['rules_by_category'].items():
        click.echo(f"  • {category.title()}: {count}")
    
    click.echo("\n⚠️  Rules by Severity:")
    for severity, count in stats['rules_by_severity'].items():
        click.echo(f"  • {severity.title()}: {count}")


@knowledge.command()
@click.argument('framework_name')
def show(framework_name: str):
    """Show detailed information about a framework."""
    kb = FrameworkKnowledgeBase()
    framework = kb.get_framework_knowledge(framework_name)
    
    if not framework:
        click.echo(f"❌ Framework '{framework_name}' not found")
        return
    
    click.echo(f"📋 {framework.name} Framework Knowledge")
    click.echo("=" * 40)
    
    click.echo(f"Description: {framework.description}")
    click.echo(f"Version: {framework.version}")
    click.echo(f"Maintainer: {framework.maintainer}")
    click.echo(f"Last Updated: {framework.last_updated}")
    
    if framework.source_url:
        click.echo(f"Documentation: {framework.source_url}")
    
    click.echo(f"\n🔧 Rules ({len(framework.rules)}):")
    for rule in framework.rules:
        click.echo(f"  • {rule.name} ({rule.severity})")
        click.echo(f"    {rule.description}")
    
    click.echo(f"\n💡 Recommendations ({len(framework.recommendations)}):")
    for rec in framework.recommendations:
        click.echo(f"  • {rec}")


@knowledge.command()
@click.argument('framework_name')
@click.argument('output_file')
def export(framework_name: str, output_file: str):
    """Export framework knowledge to YAML file."""
    kb = FrameworkKnowledgeBase()
    output_path = Path(output_file)
    
    try:
        kb.export_framework_knowledge(framework_name, output_path)
        click.echo(f"✅ Exported {framework_name} knowledge to {output_file}")
    except ValueError as e:
        click.echo(f"❌ {e}")
    except Exception as e:
        click.echo(f"❌ Export failed: {e}")


@knowledge.command()
@click.argument('yaml_file')
def import_framework(yaml_file: str):
    """Import framework knowledge from YAML file."""
    yaml_path = Path(yaml_file)
    
    if not yaml_path.exists():
        click.echo(f"❌ File not found: {yaml_file}")
        return
    
    try:
        with open(yaml_path, 'r') as f:
            data = yaml.safe_load(f)
        
        kb = FrameworkKnowledgeBase()
        framework = kb._parse_framework_knowledge(data)
        kb.add_framework_knowledge(framework)
        
        click.echo(f"✅ Imported {framework.name} framework knowledge")
        click.echo(f"   Rules: {len(framework.rules)}")
        click.echo(f"   Recommendations: {len(framework.recommendations)}")
        
    except Exception as e:
        click.echo(f"❌ Import failed: {e}")


@knowledge.command()
@click.option('--framework', required=True, help='Framework name')
@click.option('--name', required=True, help='Rule name')
@click.option('--description', required=True, help='Rule description')
@click.option('--severity', type=click.Choice(['error', 'warning', 'info']), default='warning')
@click.option('--category', type=click.Choice(['security', 'performance', 'maintainability', 'style', 'testing']), default='style')
@click.option('--message', required=True, help='Issue message')
@click.option('--suggestion', help='Suggestion for fixing the issue')
def add_rule(framework: str, name: str, description: str, severity: str, 
             category: str, message: str, suggestion: Optional[str]):
    """Add a new rule to a framework."""
    kb = FrameworkKnowledgeBase()
    
    # Check if framework exists
    if not kb.get_framework_knowledge(framework):
        click.echo(f"❌ Framework '{framework}' not found")
        return
    
    # Create new rule
    rule_id = f"{framework.lower()}_{name.lower().replace(' ', '_')}"
    rule = FrameworkRule(
        id=rule_id,
        name=name,
        description=description,
        severity=severity,
        category=category,
        framework=framework,
        message=message,
        suggestion=suggestion or "",
        author="CLI User",
        pattern_type="ast",  # Default, can be customized later
        pattern={}  # Empty pattern, needs to be filled manually
    )
    
    kb.add_rule(framework, rule)
    
    click.echo(f"✅ Added rule '{name}' to {framework}")
    click.echo(f"   ID: {rule_id}")
    click.echo(f"   Severity: {severity}")
    click.echo(f"   Category: {category}")
    click.echo("⚠️  Note: Rule pattern needs to be configured manually in the knowledge base")


@knowledge.command()
@click.argument('template_file')
def create_template(template_file: str):
    """Create a template YAML file for a new framework."""
    template_path = Path(template_file)
    
    template_data = {
        'name': 'NewFramework',
        'version': '1.0.0',
        'description': 'Description of the framework',
        'maintainer': 'Your Name',
        'source_url': 'https://framework-docs.com',
        'detection': {
            'import_patterns': ['framework', 'framework.core'],
            'file_patterns': ['*.py'],
            'class_patterns': ['BaseClass'],
            'decorator_patterns': ['@decorator']
        },
        'rules': [
            {
                'id': 'framework_example_rule',
                'name': 'Example Rule',
                'description': 'This is an example rule',
                'severity': 'warning',
                'category': 'style',
                'pattern_type': 'ast',
                'pattern': {
                    'node_type': 'ClassDef',
                    'base_classes': ['BaseClass']
                },
                'message': 'Example issue found in {class_name}',
                'suggestion': 'Fix the issue by doing X',
                'author': 'Framework Expert',
                'tags': ['example'],
                'examples': [
                    {
                        'bad': 'class Bad: pass',
                        'good': 'class Good(BaseClass): pass'
                    }
                ]
            }
        ],
        'recommendations': [
            'Follow framework best practices',
            'Use proper error handling'
        ]
    }
    
    with open(template_path, 'w') as f:
        yaml.dump(template_data, f, default_flow_style=False, sort_keys=False)
    
    click.echo(f"✅ Created template file: {template_file}")
    click.echo("📝 Edit the template and use 'vibe-check knowledge import-framework' to add it")


@knowledge.command()
@click.argument('contribution_file')
def create_contribution_template(contribution_file: str):
    """Create a template for community contributions."""
    contrib_path = Path(contribution_file)
    
    template_data = {
        'framework': 'django',  # Framework to contribute to
        'contributor': 'Your Name',
        'contribution_type': 'additional_rules',
        'version': '1.0.0',
        'rules': [
            {
                'id': 'new_rule_id',
                'name': 'New Rule Name',
                'description': 'Description of the new rule',
                'severity': 'warning',
                'category': 'security',
                'pattern_type': 'ast',
                'pattern': {
                    'node_type': 'FunctionDef',
                    'decorators': ['@app.route']
                },
                'message': 'Issue message with {placeholder}',
                'suggestion': 'How to fix the issue',
                'author': 'Your Name',
                'tags': ['community', 'security'],
                'documentation_url': 'https://docs.example.com',
                'examples': [
                    {
                        'bad': 'def bad_example(): pass',
                        'good': 'def good_example(): pass'
                    }
                ]
            }
        ],
        'recommendations': [
            'Additional recommendation for the framework'
        ]
    }
    
    with open(contrib_path, 'w') as f:
        yaml.dump(template_data, f, default_flow_style=False, sort_keys=False)
    
    click.echo(f"✅ Created contribution template: {contribution_file}")
    click.echo("📝 Edit the template and place it in the community/ directory")


@knowledge.command()
def validate():
    """Validate all knowledge base files."""
    kb = FrameworkKnowledgeBase()
    
    click.echo("🔍 Validating Knowledge Base...")
    
    errors = []
    warnings = []
    
    # Validate each framework
    for framework_name, framework in kb.frameworks.items():
        # Check for required fields
        if not framework.name:
            errors.append(f"{framework_name}: Missing name")
        if not framework.description:
            warnings.append(f"{framework_name}: Missing description")
        
        # Validate rules
        for rule in framework.rules:
            if not rule.id:
                errors.append(f"{framework_name}: Rule missing ID")
            if not rule.message:
                errors.append(f"{framework_name}: Rule {rule.id} missing message")
            if rule.pattern_type not in ['ast', 'regex', 'import', 'file_structure']:
                errors.append(f"{framework_name}: Rule {rule.id} has invalid pattern_type")
    
    # Report results
    if errors:
        click.echo(f"\n❌ Errors ({len(errors)}):")
        for error in errors:
            click.echo(f"  • {error}")
    
    if warnings:
        click.echo(f"\n⚠️  Warnings ({len(warnings)}):")
        for warning in warnings:
            click.echo(f"  • {warning}")
    
    if not errors and not warnings:
        click.echo("✅ Knowledge base validation passed!")
    
    click.echo(f"\n📊 Validation Summary:")
    click.echo(f"  • Frameworks: {len(kb.frameworks)}")
    click.echo(f"  • Total Rules: {sum(len(fw.rules) for fw in kb.frameworks.values())}")
    click.echo(f"  • Errors: {len(errors)}")
    click.echo(f"  • Warnings: {len(warnings)}")


if __name__ == '__main__':
    knowledge()
