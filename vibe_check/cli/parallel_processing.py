"""
File: vibe_check/cli/parallel_processing.py
Purpose: Parallel processing support for VCS analysis
Related Files: vibe_check/cli/standalone.py, vibe_check/core/vcs/
Dependencies: asyncio, concurrent.futures, multiprocessing
"""

import asyncio
import multiprocessing
import time
from pathlib import Path
from typing import List, Optional, Tuple, Any, Dict

from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn

from vibe_check.core.vcs.engine import VibeCheckEngine
from vibe_check.core.vcs.models import AnalysisResult, AnalysisTarget, AnalysisContext, EngineMode
from vibe_check.core.vcs.memory_manager import MemoryManager, StreamingAnalyzer
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)
console = Console()


class ParallelAnalyzer:
    """Parallel analysis coordinator for VCS."""
    
    def __init__(self, engine: VibeCheckEngine, max_workers: Optional[int] = None):
        self.engine = engine
        self.max_workers = max_workers or min(multiprocessing.cpu_count(), 8)
        self.context = AnalysisContext.create_default(EngineMode.STANDALONE)
    
    async def analyze_files_parallel(
        self,
        file_paths: List[Path],
        batch_size: int = 10,
        verbose: bool = False
    ) -> List[AnalysisResult]:
        """Analyze files in parallel using async concurrency."""
        if verbose:
            console.print(f"[blue]Analyzing {len(file_paths)} files with {self.max_workers} workers[/blue]")
        
        results = []
        
        # Process files in batches to avoid overwhelming the system
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=console
        ) as progress:
            task = progress.add_task("Analyzing files...", total=len(file_paths))
            
            for i in range(0, len(file_paths), batch_size):
                batch = file_paths[i:i + batch_size]
                
                # Create analysis tasks for this batch
                tasks = []
                for file_path in batch:
                    target = AnalysisTarget.from_file(file_path)
                    task_coro = self.engine.analyze(target, self.context)
                    tasks.append(task_coro)
                
                # Run batch concurrently
                try:
                    batch_results = await asyncio.gather(*tasks, return_exceptions=True)
                    
                    # Process results
                    for j, result in enumerate(batch_results):
                        if isinstance(result, Exception):
                            logger.error(f"Error analyzing {batch[j]}: {result}")
                            # Create error result
                            error_result = AnalysisResult(
                                target=AnalysisTarget.from_file(batch[j]),
                                issues=[],
                                execution_time=0.0,
                                success=False,
                                error_message=str(result)
                            )
                            results.append(error_result)
                        else:
                            results.append(result)
                        
                        progress.advance(task)
                        
                        if verbose and not isinstance(result, Exception):
                            issues_count = len(result.issues)
                            if issues_count > 0:
                                progress.console.print(f"  {batch[j]}: {issues_count} issues")
                
                except Exception as e:
                    logger.error(f"Batch analysis failed: {e}")
                    # Create error results for entire batch
                    for file_path in batch:
                        error_result = AnalysisResult(
                            target=AnalysisTarget.from_file(file_path),
                            issues=[],
                            execution_time=0.0,
                            success=False,
                            error_message=str(e)
                        )
                        results.append(error_result)
                        progress.advance(task)
        
        return results
    
    async def analyze_directories_parallel(
        self,
        directories: List[Path],
        verbose: bool = False
    ) -> List[AnalysisResult]:
        """Analyze directories in parallel by collecting all Python files first."""
        all_files = []
        
        for directory in directories:
            if directory.is_dir():
                python_files = list(directory.rglob("*.py"))
                # Filter out common ignore patterns
                filtered_files = []
                for file_path in python_files:
                    if not any(part in str(file_path) for part in ['venv', '__pycache__', '.git', 'node_modules']):
                        filtered_files.append(file_path)
                
                all_files.extend(filtered_files)
                
                if verbose:
                    console.print(f"Found {len(filtered_files)} Python files in {directory}")
        
        if verbose:
            console.print(f"Total files to analyze: {len(all_files)}")
        
        return await self.analyze_files_parallel(all_files, verbose=verbose)
    
    def get_optimal_batch_size(self, total_files: int) -> int:
        """Calculate optimal batch size based on file count and workers."""
        if total_files <= self.max_workers:
            return 1
        elif total_files <= self.max_workers * 10:
            return max(1, total_files // self.max_workers)
        else:
            return 10  # Cap at 10 for very large projects
    
    def get_performance_stats(self, results: List[AnalysisResult]) -> dict:
        """Get performance statistics from analysis results."""
        total_time = sum(result.execution_time for result in results)
        successful_analyses = sum(1 for result in results if result.success)
        failed_analyses = len(results) - successful_analyses
        total_issues = sum(len(result.issues) for result in results)
        
        avg_time_per_file = total_time / len(results) if results else 0
        files_per_second = len(results) / total_time if total_time > 0 else 0
        
        return {
            "total_files": len(results),
            "successful_analyses": successful_analyses,
            "failed_analyses": failed_analyses,
            "total_execution_time": total_time,
            "average_time_per_file": avg_time_per_file,
            "files_per_second": files_per_second,
            "total_issues": total_issues,
            "max_workers": self.max_workers
        }


async def analyze_with_parallel_processing(
    engine: VibeCheckEngine,
    paths: List[Path],
    max_workers: Optional[int] = None,
    verbose: bool = False
) -> Tuple[List[AnalysisResult], dict]:
    """
    Analyze paths using parallel processing.
    
    Returns:
        Tuple of (results, performance_stats)
    """
    analyzer = ParallelAnalyzer(engine, max_workers)
    
    # Separate files and directories
    files = [p for p in paths if p.is_file() and p.suffix == '.py']
    directories = [p for p in paths if p.is_dir()]
    
    results = []
    
    # Analyze individual files
    if files:
        if verbose:
            console.print(f"Analyzing {len(files)} individual files...")
        file_results = await analyzer.analyze_files_parallel(files, verbose=verbose)
        results.extend(file_results)
    
    # Analyze directories
    if directories:
        if verbose:
            console.print(f"Analyzing {len(directories)} directories...")
        dir_results = await analyzer.analyze_directories_parallel(directories, verbose=verbose)
        results.extend(dir_results)
    
    # Get performance statistics
    stats = analyzer.get_performance_stats(results)
    
    return results, stats


def get_recommended_worker_count() -> int:
    """Get recommended number of workers based on system capabilities."""
    cpu_count = multiprocessing.cpu_count()

    # Conservative approach: use 75% of available CPUs, capped at 8
    recommended = min(max(1, int(cpu_count * 0.75)), 8)

    logger.info(f"System has {cpu_count} CPUs, recommending {recommended} workers")
    return recommended


class EnhancedParallelAnalyzer:
    """Enhanced parallel analyzer with work stealing and resource monitoring."""

    def __init__(self, engine: VibeCheckEngine, max_workers: Optional[int] = None):
        self.engine = engine
        self.max_workers = max_workers or get_recommended_worker_count()
        self.memory_manager = MemoryManager()
        self.streaming_analyzer = StreamingAnalyzer(self.memory_manager)
        self.context = AnalysisContext.create_default(EngineMode.STANDALONE)

    async def analyze_large_project(
        self,
        project_root: Path,
        verbose: bool = False,
        use_streaming: bool = True
    ) -> Tuple[List[AnalysisResult], Dict[str, Any]]:
        """
        Analyze large project with memory management and streaming.

        Args:
            project_root: Root directory of the project
            verbose: Enable verbose output
            use_streaming: Use streaming analysis for memory efficiency

        Returns:
            Tuple of (results, performance_stats)
        """
        start_time = time.time()

        # Initialize memory manager
        await self.memory_manager.initialize()

        if verbose:
            console.print(f"[blue]Analyzing large project: {project_root}[/blue]")
            console.print(f"Using {self.max_workers} workers with streaming: {use_streaming}")

        all_results = []
        total_chunks = 0

        if use_streaming:
            # Use streaming analysis for memory efficiency
            async for chunk_results, memory_stats in self.streaming_analyzer.analyze_project_streaming(
                project_root, self.engine, self.context
            ):
                all_results.extend(chunk_results)
                total_chunks += 1

                if verbose:
                    console.print(f"Processed chunk {total_chunks}: {len(chunk_results)} files, "
                                f"Memory: {memory_stats.process_memory_mb:.1f} MB "
                                f"({memory_stats.memory_percent:.1f}%)")
        else:
            # Traditional parallel processing
            python_files = list(project_root.rglob("*.py"))
            filtered_files = [f for f in python_files if not any(
                part in str(f) for part in ['__pycache__', '.git', '.venv', 'venv']
            )]

            all_results = await self.analyze_files_parallel(filtered_files, verbose=verbose)

        # Generate performance statistics
        execution_time = time.time() - start_time
        memory_report = self.memory_manager.get_memory_report()

        stats = {
            "total_files": len(all_results),
            "successful_analyses": sum(1 for r in all_results if r.success),
            "failed_analyses": sum(1 for r in all_results if not r.success),
            "total_issues": sum(len(r.issues) for r in all_results),
            "total_execution_time": execution_time,
            "chunks_processed": total_chunks,
            "memory_report": memory_report,
            "max_workers": self.max_workers,
            "streaming_used": use_streaming
        }

        if verbose:
            console.print(f"\n[green]Analysis complete![/green]")
            console.print(f"Files: {stats['total_files']}, Issues: {stats['total_issues']}")
            console.print(f"Time: {execution_time:.2f}s, Memory peak: {memory_report['peak_mb']:.1f} MB")

        return all_results, stats

    async def analyze_files_parallel(
        self,
        file_paths: List[Path],
        batch_size: int = 10,
        verbose: bool = False
    ) -> List[AnalysisResult]:
        """Analyze files in parallel using async concurrency."""
        if verbose:
            console.print(f"[blue]Analyzing {len(file_paths)} files with {self.max_workers} workers[/blue]")

        results = []

        # Process files in batches to avoid overwhelming the system
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=console
        ) as progress:
            task = progress.add_task("Analyzing files...", total=len(file_paths))

            for i in range(0, len(file_paths), batch_size):
                batch = file_paths[i:i + batch_size]

                # Create analysis tasks for this batch
                tasks = []
                for file_path in batch:
                    target = AnalysisTarget.from_file(file_path)
                    task_coro = self.engine.analyze(target, self.context)
                    tasks.append(task_coro)

                # Run batch concurrently
                try:
                    batch_results = await asyncio.gather(*tasks, return_exceptions=True)

                    # Process results
                    for j, result in enumerate(batch_results):
                        if isinstance(result, Exception):
                            logger.error(f"Error analyzing {batch[j]}: {result}")
                            # Create error result
                            error_result = AnalysisResult(
                                target=AnalysisTarget.from_file(batch[j]),
                                issues=[],
                                execution_time=0.0,
                                success=False,
                                error_message=str(result)
                            )
                            results.append(error_result)
                        else:
                            results.append(result)

                        progress.advance(task)

                        if verbose and not isinstance(result, Exception):
                            issues_count = len(result.issues)
                            if issues_count > 0:
                                progress.console.print(f"  {batch[j]}: {issues_count} issues")

                except Exception as e:
                    logger.error(f"Batch analysis failed: {e}")
                    # Create error results for entire batch
                    for file_path in batch:
                        error_result = AnalysisResult(
                            target=AnalysisTarget.from_file(file_path),
                            issues=[],
                            execution_time=0.0,
                            success=False,
                            error_message=str(e)
                        )
                        results.append(error_result)
                        progress.advance(task)

        return results
