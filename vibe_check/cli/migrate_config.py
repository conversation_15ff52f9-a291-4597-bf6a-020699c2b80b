"""
CLI commands for configuration migration.
"""

import click
from pathlib import Path
from typing import Optional

from ..core.constants.migration_utility import (
    migrate_configuration_file,
    validate_migration,
    ConfigurationMigrator
)


@click.command()
@click.argument('legacy_config', type=click.Path(exists=True, path_type=Path))
@click.option('--output', '-o', type=click.Path(path_type=Path), 
              help='Output path for migrated configuration')
@click.option('--no-backup', is_flag=True, 
              help='Do not create backup of original configuration')
@click.option('--encrypt', is_flag=True, 
              help='Encrypt sensitive configuration data')
@click.option('--password', prompt=True, hide_input=True, 
              help='Password for encryption (prompted if --encrypt is used)')
@click.option('--validate', is_flag=True, 
              help='Validate migration after completion')
@click.option('--dry-run', is_flag=True, 
              help='Show what would be migrated without making changes')
def migrate(
    legacy_config: Path,
    output: Optional[Path],
    no_backup: bool,
    encrypt: bool,
    password: str,
    validate: bool,
    dry_run: bool
):
    """Migrate legacy configuration to unified format."""
    
    click.echo(f"🔄 Migrating configuration: {legacy_config}")
    
    # Determine output path
    if output is None:
        output = legacy_config.parent / "vibe-check-unified.yaml"
    
    click.echo(f"📁 Output path: {output}")
    
    if encrypt and not password:
        password = click.prompt("Enter password for encryption", hide_input=True)
    
    # Show migration preview in dry-run mode
    if dry_run:
        click.echo("\n🔍 Dry Run - Migration Preview:")
        click.echo("-" * 40)
        
        migrator = ConfigurationMigrator()
        unified_config, migration_logs = migrator.migrate_legacy_config(legacy_config)
        
        click.echo(f"Migration steps: {len(migration_logs)}")
        for i, log_entry in enumerate(migration_logs, 1):
            click.echo(f"  {i:2d}. {log_entry}")
        
        if unified_config.validation_errors:
            click.echo("\n⚠️  Validation Issues:")
            for error in unified_config.validation_errors:
                click.echo(f"  • {error}")
        
        click.echo("\n✅ Dry run completed - no files were modified")
        return
    
    # Perform actual migration
    try:
        success = migrate_configuration_file(
            legacy_config_path=legacy_config,
            output_path=output,
            create_backup=not no_backup,
            encrypt_sensitive=encrypt,
            password=password if encrypt else None
        )
        
        if success:
            click.echo("✅ Migration completed successfully!")
            click.echo(f"📄 Migrated configuration: {output}")
            
            if not no_backup:
                backup_path = legacy_config.with_suffix(f"{legacy_config.suffix}.backup")
                click.echo(f"💾 Backup created: {backup_path}")
            
            # Validate migration if requested
            if validate:
                click.echo("\n🔍 Validating migration...")
                issues = validate_migration(legacy_config, output)
                
                if issues:
                    click.echo("⚠️  Validation Issues Found:")
                    for issue in issues:
                        click.echo(f"  • {issue}")
                else:
                    click.echo("✅ Migration validation passed!")
            
            # Show next steps
            click.echo("\n🚀 Next Steps:")
            click.echo("  1. Review the migrated configuration file")
            click.echo("  2. Test with: vibe-check analyze --config-file " + str(output))
            click.echo("  3. Update any scripts to use the new configuration")
            
            if encrypt:
                click.echo("  4. Store the encryption password securely")
        
        else:
            click.echo("❌ Migration failed - check logs for details")
            
    except Exception as e:
        click.echo(f"❌ Migration error: {e}")


@click.command()
@click.argument('original_config', type=click.Path(exists=True, path_type=Path))
@click.argument('migrated_config', type=click.Path(exists=True, path_type=Path))
def validate_migration_cmd(original_config: Path, migrated_config: Path):
    """Validate that migration preserved configuration values."""
    
    click.echo(f"🔍 Validating migration:")
    click.echo(f"  Original: {original_config}")
    click.echo(f"  Migrated: {migrated_config}")
    
    try:
        issues = validate_migration(original_config, migrated_config)
        
        if issues:
            click.echo(f"\n⚠️  Found {len(issues)} validation issues:")
            for i, issue in enumerate(issues, 1):
                click.echo(f"  {i}. {issue}")
            
            click.echo("\n💡 Recommendations:")
            click.echo("  • Review the migration report for details")
            click.echo("  • Check if manual adjustments are needed")
            click.echo("  • Test the migrated configuration thoroughly")
        
        else:
            click.echo("\n✅ Migration validation passed!")
            click.echo("  All key configuration values were preserved correctly.")
            
    except Exception as e:
        click.echo(f"❌ Validation error: {e}")


@click.command()
@click.argument('config_file', type=click.Path(exists=True, path_type=Path))
@click.option('--format', type=click.Choice(['table', 'json', 'yaml']), 
              default='table', help='Output format')
def analyze_config(config_file: Path, format: str):
    """Analyze configuration file structure and identify migration needs."""
    
    click.echo(f"📊 Analyzing configuration: {config_file}")
    
    try:
        import yaml
        
        with open(config_file, 'r') as f:
            config = yaml.safe_load(f)
        
        if not isinstance(config, dict):
            click.echo("❌ Invalid configuration format")
            return
        
        # Analyze configuration structure
        analysis = {
            'total_sections': len(config),
            'has_tools_section': 'tools' in config,
            'has_analysis_settings': any(key in config for key in [
                'file_extensions', 'exclude_patterns', 'analyze_docs'
            ]),
            'has_performance_settings': any(key in config for key in [
                'max_workers', 'timeout', 'parallel_analysis'
            ]),
            'external_tools': [],
            'migration_complexity': 'low'
        }
        
        # Analyze tools section
        if 'tools' in config and isinstance(config['tools'], dict):
            analysis['external_tools'] = list(config['tools'].keys())
            if len(analysis['external_tools']) > 5:
                analysis['migration_complexity'] = 'medium'
        
        # Check for complex nested structures
        def count_nested_levels(obj, level=0):
            if isinstance(obj, dict):
                return max([count_nested_levels(v, level + 1) for v in obj.values()] + [level])
            elif isinstance(obj, list):
                return max([count_nested_levels(item, level + 1) for item in obj] + [level])
            return level
        
        max_nesting = count_nested_levels(config)
        if max_nesting > 3:
            analysis['migration_complexity'] = 'high'
        
        # Display analysis results
        if format == 'table':
            click.echo("\n📋 Configuration Analysis:")
            click.echo("-" * 30)
            click.echo(f"Total sections: {analysis['total_sections']}")
            click.echo(f"Has tools section: {analysis['has_tools_section']}")
            click.echo(f"Has analysis settings: {analysis['has_analysis_settings']}")
            click.echo(f"Has performance settings: {analysis['has_performance_settings']}")
            click.echo(f"External tools: {len(analysis['external_tools'])}")
            
            if analysis['external_tools']:
                click.echo("  Tools found:")
                for tool in analysis['external_tools']:
                    click.echo(f"    • {tool}")
            
            click.echo(f"Migration complexity: {analysis['migration_complexity']}")
            
            # Migration recommendations
            click.echo("\n💡 Migration Recommendations:")
            if analysis['migration_complexity'] == 'low':
                click.echo("  ✅ Simple migration - should be straightforward")
            elif analysis['migration_complexity'] == 'medium':
                click.echo("  ⚠️  Medium complexity - review tool configurations")
            else:
                click.echo("  🔴 High complexity - manual review recommended")
            
            click.echo("\n🔄 To migrate this configuration:")
            click.echo(f"  vibe-check config migrate {config_file}")
        
        elif format == 'json':
            import json
            click.echo(json.dumps(analysis, indent=2))
        
        elif format == 'yaml':
            click.echo(yaml.dump(analysis, default_flow_style=False))
            
    except Exception as e:
        click.echo(f"❌ Analysis error: {e}")


@click.group()
def config():
    """Configuration management commands."""
    pass


# Add commands to group
config.add_command(migrate)
config.add_command(validate_migration_cmd, name='validate')
config.add_command(analyze_config, name='analyze')


if __name__ == '__main__':
    config()
