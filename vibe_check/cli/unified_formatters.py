"""
Unified Output Formatters
========================

Consolidated formatters from cli/formatters.py and ui/cli/formatter.py
"""

import json
import yaml
from typing import Dict, Any, List
from pathlib import Path

from vibe_check.core.models import ProjectMetrics, FileMetrics


class UnifiedFormatter:
    """Base class for unified formatters"""
    
    def format(self, data: Any) -> str:
        """Format data to string"""
        raise NotImplementedError


class JSONFormatter(UnifiedFormatter):
    """JSON output formatter"""
    
    def format(self, data: Any) -> str:
        """Format data as JSON"""
        if hasattr(data, 'to_dict'):
            return json.dumps(data.to_dict(), indent=2)
        return json.dumps(data, indent=2, default=str)


class YAMLFormatter(UnifiedFormatter):
    """YAML output formatter"""
    
    def format(self, data: Any) -> str:
        """Format data as YAML"""
        if hasattr(data, 'to_dict'):
            return yaml.dump(data.to_dict(), default_flow_style=False)
        return yaml.dump(data, default_flow_style=False, default=str)


class TableFormatter(UnifiedFormatter):
    """Table output formatter"""
    
    def format(self, data: Any) -> str:
        """Format data as table"""
        if isinstance(data, ProjectMetrics):
            return self._format_project_metrics(data)
        elif isinstance(data, list) and data and isinstance(data[0], FileMetrics):
            return self._format_file_metrics(data)
        else:
            return str(data)
    
    def _format_project_metrics(self, metrics: ProjectMetrics) -> str:
        """Format project metrics as table"""
        lines = []
        lines.append("Project Analysis Results")
        lines.append("=" * 40)
        lines.append(f"Total Files: {getattr(metrics, 'total_files', 'N/A')}")
        lines.append(f"Total Lines: {getattr(metrics, 'total_lines', 'N/A')}")
        lines.append(f"Average Complexity: {getattr(metrics, 'average_complexity', 'N/A')}")
        lines.append(f"Quality Score: {getattr(metrics, 'quality_score', 'N/A')}")
        return "\n".join(lines)
    
    def _format_file_metrics(self, file_metrics: List[FileMetrics]) -> str:
        """Format file metrics as table"""
        lines = []
        lines.append("File Analysis Results")
        lines.append("=" * 60)
        lines.append(f"{'File':<30} {'Lines':<8} {'Complexity':<12} {'Issues':<8}")
        lines.append("-" * 60)
        
        for fm in file_metrics[:20]:  # Show top 20
            file_name = Path(fm.file_path).name if hasattr(fm, 'file_path') else 'Unknown'
            lines.append(f"{file_name:<30} {getattr(fm, 'lines', 0):<8} {getattr(fm, 'complexity', 0):<12} {getattr(fm, 'issues', 0):<8}")
        
        return "\n".join(lines)


class MarkdownFormatter(UnifiedFormatter):
    """Markdown output formatter"""
    
    def format(self, data: Any) -> str:
        """Format data as Markdown"""
        if isinstance(data, ProjectMetrics):
            return self._format_project_markdown(data)
        else:
            return f"```json\n{json.dumps(data, indent=2, default=str)}\n```"
    
    def _format_project_markdown(self, metrics: ProjectMetrics) -> str:
        """Format project metrics as Markdown"""
        lines = []
        lines.append("# Project Analysis Results")
        lines.append("")
        lines.append("## Summary")
        lines.append(f"- **Total Files:** {getattr(metrics, 'total_files', 'N/A')}")
        lines.append(f"- **Total Lines:** {getattr(metrics, 'total_lines', 'N/A')}")
        lines.append(f"- **Average Complexity:** {getattr(metrics, 'average_complexity', 'N/A')}")
        lines.append(f"- **Quality Score:** {getattr(metrics, 'quality_score', 'N/A')}")
        return "\n".join(lines)


# Formatter registry
FORMATTERS = {
    'json': JSONFormatter(),
    'yaml': YAMLFormatter(),
    'table': TableFormatter(),
    'markdown': MarkdownFormatter(),
}


def get_formatter(format_name: str) -> UnifiedFormatter:
    """Get formatter by name"""
    return FORMATTERS.get(format_name.lower(), JSONFormatter())
