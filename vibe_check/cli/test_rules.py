"""
CLI commands for rule testing and validation.
"""

import asyncio
import click
import json
from typing import List

from ..core.vcs.testing.rule_validator import get_test_suite, initialize_test_suite
from ..core.vcs.registry import RuleRegistry


@click.group()
def test_rules():
    """Rule testing and validation commands."""
    pass


@test_rules.command()
@click.option('--rule-id', help='Test specific rule ID')
@click.option('--category', help='Test rules in specific category')
@click.option('--format', type=click.Choice(['table', 'json', 'summary']), default='table',
              help='Output format')
@click.option('--verbose', is_flag=True, help='Show detailed test results')
def validate(rule_id: str, category: str, format: str, verbose: bool):
    """Validate rules against test cases."""
    
    # Initialize test suite
    initialize_test_suite()
    test_suite = get_test_suite()
    # Use the VCS engine's registry which has rules loaded
    from ..core.vcs.engine import VibeCheckEngine
    from ..core.vcs.config import VCSConfig
    from ..core.vcs.models import EngineMode

    # Initialize VCS engine to get loaded rules
    config = VCSConfig()
    engine = VibeCheckEngine(EngineMode.STANDALONE, config)

    # Initialize the engine to load rules
    import asyncio
    asyncio.run(engine.initialize())

    rule_registry = engine.rule_registry

    # Get rules to test
    if rule_id:
        rule = rule_registry.get_rule(rule_id)
        rules = [rule] if rule else []
        if not rules:
            click.echo(f"❌ Rule {rule_id} not found")
            return
    elif category:
        from ..core.vcs.models import RuleCategory
        try:
            cat_enum = RuleCategory(category.upper())
            rules = rule_registry.get_rules_for_category(cat_enum)
        except ValueError:
            click.echo(f"❌ Invalid category {category}")
            return
        if not rules:
            click.echo(f"❌ No rules found in category {category}")
            return
    else:
        rules = rule_registry.get_all_rules()
    
    if not rules:
        click.echo("❌ No rules to test")
        return
    
    click.echo(f"🧪 Testing {len(rules)} rules...")
    
    # Run validation
    async def run_validation():
        results = {}
        for rule in rules:
            try:
                validation_report = await test_suite.validator.validate_rule(rule)
                results[rule.rule_id] = validation_report
            except Exception as e:
                click.echo(f"❌ Error testing rule {rule.rule_id}: {e}")
                continue
        return results
    
    results = asyncio.run(run_validation())
    
    if format == 'json':
        # Convert to JSON-serializable format
        json_results = {}
        for rule_id, report in results.items():
            json_results[rule_id] = {
                'total_tests': report.total_tests,
                'passed_tests': report.passed_tests,
                'failed_tests': report.failed_tests,
                'error_tests': report.error_tests,
                'false_positive_rate': report.false_positive_rate,
                'false_negative_rate': report.false_negative_rate,
                'overall_result': report.overall_result.value,
                'test_results': report.test_results
            }
        click.echo(json.dumps(json_results, indent=2))
        return
    
    if format == 'summary':
        _print_summary_results(results)
        return
    
    # Default table format
    _print_table_results(results, verbose)


@test_rules.command()
@click.option('--rule-id', help='Benchmark specific rule ID')
@click.option('--iterations', default=10, help='Number of iterations for benchmarking')
def benchmark(rule_id: str, iterations: int):
    """Benchmark rule performance."""
    
    initialize_test_suite()
    test_suite = get_test_suite()
    rule_registry = get_rule_registry()
    
    if rule_id:
        rule = rule_registry.get_rule(rule_id)
        if not rule:
            click.echo(f"❌ Rule {rule_id} not found")
            return
        rules = [rule]
    else:
        rules = rule_registry.get_all_rules()
    
    click.echo(f"⏱️  Benchmarking {len(rules)} rules with {iterations} iterations...")
    
    async def run_benchmarks():
        for rule in rules:
            try:
                benchmark_result = await test_suite.benchmark_rule_performance(rule, iterations)
                
                if 'error' in benchmark_result:
                    click.echo(f"❌ {rule.rule_id}: {benchmark_result['error']}")
                    continue
                
                click.echo(f"📊 {rule.rule_id}:")
                click.echo(f"  Average: {benchmark_result['average_ms']:.2f}ms")
                click.echo(f"  Min: {benchmark_result['min_ms']:.2f}ms")
                click.echo(f"  Max: {benchmark_result['max_ms']:.2f}ms")
                click.echo(f"  Test Cases: {benchmark_result['test_cases_count']}")
                
            except Exception as e:
                click.echo(f"❌ Error benchmarking rule {rule.rule_id}: {e}")
    
    asyncio.run(run_benchmarks())


@test_rules.command()
def regression():
    """Run regression tests to detect rule quality degradation."""
    
    initialize_test_suite()
    test_suite = get_test_suite()
    rule_registry = get_rule_registry()
    rules = rule_registry.get_all_rules()
    
    click.echo(f"🔄 Running regression tests for {len(rules)} rules...")
    
    async def run_regression():
        results = await test_suite.run_regression_tests(rules)
        
        click.echo("\n📊 Regression Test Results:")
        click.echo("=" * 40)
        
        # Summary
        click.echo(f"Total Rules Tested: {results['total_rules_tested']}")
        click.echo(f"✅ Passed: {results['rules_passed']}")
        click.echo(f"❌ Failed: {results['rules_failed']}")
        
        # Regressions
        if results['accuracy_regressions']:
            click.echo(f"\n⚠️  Accuracy Regressions ({len(results['accuracy_regressions'])}):")
            for regression in results['accuracy_regressions']:
                click.echo(f"  {regression['rule_id']}: "
                          f"{regression['previous_fp_rate']:.1%} → {regression['current_fp_rate']:.1%} "
                          f"(+{regression['regression_amount']:.1%})")
        
        if results['performance_regressions']:
            click.echo(f"\n🐌 Performance Regressions ({len(results['performance_regressions'])}):")
            for regression in results['performance_regressions']:
                click.echo(f"  {regression}")
        
        # Overall status
        if results['accuracy_regressions'] or results['performance_regressions']:
            click.echo("\n❌ Regression tests FAILED - Quality degradation detected")
            return 1
        else:
            click.echo("\n✅ Regression tests PASSED - No quality degradation")
            return 0
    
    exit_code = asyncio.run(run_regression())
    exit(exit_code)


@test_rules.command()
@click.option('--format', type=click.Choice(['table', 'json']), default='table',
              help='Output format')
def quality_report():
    """Generate comprehensive quality report for all rules."""
    
    initialize_test_suite()
    test_suite = get_test_suite()
    rule_registry = get_rule_registry()
    rules = rule_registry.get_all_rules()
    
    click.echo(f"📊 Generating quality report for {len(rules)} rules...")
    
    async def generate_report():
        return await test_suite.generate_quality_report(rules)
    
    report = asyncio.run(generate_report())
    
    if format == 'json':
        click.echo(json.dumps(report, indent=2))
        return
    
    # Table format
    click.echo("\n🎯 Rule Quality Report")
    click.echo("=" * 60)
    
    # Summary
    summary = report['summary']
    click.echo(f"Total Rules: {summary['total_rules']}")
    click.echo(f"Rules with Tests: {summary['rules_with_tests']}")
    click.echo(f"Average Test Coverage: {summary['average_test_coverage']:.1f} tests/rule")
    click.echo(f"Overall Quality Score: {summary['overall_quality_score']:.1f}/100")
    
    # Rule details
    click.echo(f"\n📋 Rule Details:")
    click.echo(f"{'Rule ID':<12} {'Quality':<8} {'Accuracy':<9} {'Coverage':<9} {'Reliability':<11} {'Status':<8}")
    click.echo("-" * 70)
    
    for rule_id, details in report['rule_details'].items():
        click.echo(f"{rule_id:<12} {details['quality_score']:>6.1f}   "
                  f"{details['accuracy_score']:>7.1f}   {details['coverage_score']:>7.1f}   "
                  f"{details['reliability_score']:>9.1f}   {details['status']:<8}")
    
    # Recommendations
    if report['recommendations']:
        click.echo(f"\n💡 Recommendations:")
        for rec in report['recommendations']:
            click.echo(f"  • {rec}")


def _print_summary_results(results):
    """Print summary format results."""
    total_rules = len(results)
    passed_rules = sum(1 for r in results.values() if r.overall_result.value == 'PASS')
    failed_rules = total_rules - passed_rules
    
    click.echo(f"\n📊 Test Summary:")
    click.echo(f"Total Rules: {total_rules}")
    click.echo(f"✅ Passed: {passed_rules}")
    click.echo(f"❌ Failed: {failed_rules}")
    click.echo(f"Pass Rate: {(passed_rules / total_rules * 100):.1f}%")
    
    # Show failed rules
    if failed_rules > 0:
        click.echo(f"\n❌ Failed Rules:")
        for rule_id, result in results.items():
            if result.overall_result.value != 'PASS':
                click.echo(f"  {rule_id}: {result.overall_result.value} "
                          f"(FP: {result.false_positive_rate:.1%}, "
                          f"Tests: {result.passed_tests}/{result.total_tests})")


def _print_table_results(results, verbose: bool):
    """Print table format results."""
    click.echo(f"\n🧪 Rule Validation Results:")
    click.echo("=" * 80)
    
    # Header
    click.echo(f"{'Rule ID':<12} {'Status':<8} {'Tests':<8} {'FP Rate':<8} {'FN Rate':<8} {'Score':<6}")
    click.echo("-" * 80)
    
    for rule_id, result in results.items():
        status_emoji = "✅" if result.overall_result.value == 'PASS' else "❌"
        status = f"{status_emoji} {result.overall_result.value}"
        
        tests_info = f"{result.passed_tests}/{result.total_tests}"
        fp_rate = f"{result.false_positive_rate:.1%}"
        fn_rate = f"{result.false_negative_rate:.1%}"
        
        # Calculate simple score
        score = (result.passed_tests / result.total_tests * 100) if result.total_tests > 0 else 0
        
        click.echo(f"{rule_id:<12} {status:<8} {tests_info:<8} {fp_rate:<8} {fn_rate:<8} {score:>4.0f}")
        
        if verbose and result.test_results:
            for test_result in result.test_results:
                test_status = "✅" if test_result.get('passed', False) else "❌"
                click.echo(f"    {test_status} {test_result.get('test_name', 'Unknown')}")


if __name__ == '__main__':
    test_rules()
