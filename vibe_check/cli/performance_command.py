"""
Performance Command
==================

CLI command for displaying VCS rule performance metrics and analytics.
"""

import click
import logging
from pathlib import Path
from typing import Optional, Dict, Any
import json

logger = logging.getLogger(__name__)


@click.command()
@click.argument('project_path', type=click.Path(exists=True, path_type=Path))
@click.option('--format', 'output_format', 
              type=click.Choice(['table', 'json', 'detailed']), 
              default='table',
              help='Output format for performance data')
@click.option('--threshold', type=float, default=100.0,
              help='Performance threshold in milliseconds for highlighting slow rules')
@click.option('--top', type=int, default=10,
              help='Number of top slowest rules to display')
@click.option('--category', type=str,
              help='Filter by rule category (style, security, complexity, etc.)')
@click.option('--save-report', type=click.Path(path_type=Path),
              help='Save performance report to file')
@click.pass_context
def performance(ctx, project_path: Path, output_format: str, threshold: float, 
                top: int, category: Optional[str], save_report: Optional[Path]):
    """
    Display VCS rule performance metrics and analytics.
    
    This command analyzes the performance of VCS rules during analysis,
    showing execution times, bottlenecks, and optimization opportunities.
    
    Examples:
        vibe-check performance ./my_project
        vibe-check performance ./my_project --format json
        vibe-check performance ./my_project --threshold 50 --top 5
        vibe-check performance ./my_project --category security
    """
    try:
        # Import VCS components
        from ..core.vcs.engine import VibeCheckEngine
        from ..core.vcs.models import EngineMode, AnalysisTarget
        from ..core.vcs.config import VCSConfig
        import asyncio
        
        # Create VCS engine to access performance data
        config = VCSConfig()
        engine = VibeCheckEngine(mode=EngineMode.STANDALONE, config=config)
        
        async def get_performance_data():
            """Get performance data from the engine."""
            await engine.start()
            try:
                # Get performance report
                performance_report = engine.performance_monitor.get_performance_report()
                rule_performance = performance_report.get('rule_performance', {})
                
                return rule_performance
            finally:
                await engine.stop()
        
        # Get performance data
        performance_data = asyncio.run(get_performance_data())
        
        if performance_data.get('status') == 'no_data':
            click.echo("❌ No performance data available.")
            click.echo("💡 Run some VCS analyses first to collect performance metrics.")
            return
        
        # Filter by category if specified
        if category:
            filtered_rules = []
            for rule in performance_data.get('slowest_rules', []):
                # We'd need to add category info to the performance data
                # For now, just show all rules
                filtered_rules.append(rule)
            performance_data['slowest_rules'] = filtered_rules
        
        # Display performance data based on format
        if output_format == 'json':
            display_json_format(performance_data)
        elif output_format == 'detailed':
            display_detailed_format(performance_data, threshold, top)
        else:
            display_table_format(performance_data, threshold, top)
        
        # Save report if requested
        if save_report:
            save_performance_report(performance_data, save_report)
            click.echo(f"📄 Performance report saved to {save_report}")
        
    except Exception as e:
        logger.error(f"Error in performance command: {e}")
        click.echo(f"❌ Error: {e}")
        ctx.exit(1)


def display_table_format(data: Dict[str, Any], threshold: float, top: int):
    """Display performance data in table format."""
    click.echo("🎯 VCS RULE PERFORMANCE REPORT")
    click.echo("=" * 60)
    
    summary = data.get('summary', {})
    click.echo(f"📊 Rules Tracked: {summary.get('rules_tracked', 0)}")
    click.echo(f"⚡ Total Executions: {summary.get('total_executions', 0)}")
    click.echo(f"⏱️  Total Time: {summary.get('total_time', 0):.3f}s")
    click.echo(f"🐌 Slow Rules: {summary.get('slow_rules_count', 0)}")
    click.echo()
    
    # Display slowest rules
    slowest_rules = data.get('slowest_rules', [])[:top]
    if slowest_rules:
        click.echo(f"🔝 TOP {len(slowest_rules)} SLOWEST RULES")
        click.echo("-" * 60)
        click.echo(f"{'Rule ID':<12} {'Name':<25} {'Avg Time':<12} {'Max Time':<12} {'Executions':<12}")
        click.echo("-" * 60)
        
        for rule in slowest_rules:
            rule_id = rule['rule_id']
            name = rule['rule_name'][:23] + "..." if len(rule['rule_name']) > 25 else rule['rule_name']
            avg_time = f"{rule['average_time_ms']:.1f}ms"
            max_time = f"{rule['max_time_ms']:.1f}ms"
            executions = str(rule['total_executions'])
            
            # Highlight slow rules
            if rule['average_time_ms'] > threshold:
                click.echo(click.style(
                    f"{rule_id:<12} {name:<25} {avg_time:<12} {max_time:<12} {executions:<12}",
                    fg='red'
                ))
            else:
                click.echo(f"{rule_id:<12} {name:<25} {avg_time:<12} {max_time:<12} {executions:<12}")
    
    # Display problematic rules
    problematic_rules = data.get('problematic_rules', [])
    if problematic_rules:
        click.echo()
        click.echo("⚠️  PROBLEMATIC RULES")
        click.echo("-" * 60)
        for rule in problematic_rules:
            click.echo(f"🔴 {rule['rule_id']}: {rule['rule_name']}")
            click.echo(f"   Average: {rule['average_time_ms']:.1f}ms (threshold: {rule['threshold_ms']:.1f}ms)")
            click.echo(f"   Recommendation: {rule['recommendation']}")
            click.echo()


def display_detailed_format(data: Dict[str, Any], threshold: float, top: int):
    """Display performance data in detailed format."""
    click.echo("🎯 DETAILED VCS RULE PERFORMANCE REPORT")
    click.echo("=" * 80)
    
    summary = data.get('summary', {})
    click.echo("📊 SUMMARY STATISTICS")
    click.echo("-" * 40)
    click.echo(f"Rules Tracked: {summary.get('rules_tracked', 0)}")
    click.echo(f"Total Executions: {summary.get('total_executions', 0)}")
    click.echo(f"Total Time: {summary.get('total_time', 0):.3f}s")
    click.echo(f"Average Time per Rule: {summary.get('average_time_per_rule', 0):.3f}s")
    click.echo(f"Slow Rules Count: {summary.get('slow_rules_count', 0)}")
    click.echo(f"Very Slow Rules Count: {summary.get('very_slow_rules_count', 0)}")
    click.echo()
    
    # Display detailed rule information
    slowest_rules = data.get('slowest_rules', [])[:top]
    if slowest_rules:
        click.echo(f"🔝 TOP {len(slowest_rules)} SLOWEST RULES (DETAILED)")
        click.echo("-" * 80)
        
        for i, rule in enumerate(slowest_rules, 1):
            click.echo(f"{i}. {rule['rule_id']}: {rule['rule_name']}")
            click.echo(f"   Average Time: {rule['average_time_ms']:.1f}ms")
            click.echo(f"   Maximum Time: {rule['max_time_ms']:.1f}ms")
            click.echo(f"   P95 Time: {rule['p95_time_ms']:.1f}ms")
            click.echo(f"   Total Executions: {rule['total_executions']}")
            click.echo(f"   Slow Executions: {rule['slow_executions']}")
            click.echo(f"   Issues per Execution: {rule['issues_per_execution']:.1f}")
            click.echo(f"   Threshold: {rule['threshold_ms']:.1f}ms")
            
            if rule['average_time_ms'] > threshold:
                click.echo(click.style("   ⚠️  PERFORMANCE WARNING", fg='red'))
            
            click.echo()
    
    # Display alert summary
    alert_summary = data.get('alert_summary', {})
    if alert_summary.get('total_alerts', 0) > 0:
        click.echo("🚨 ALERT SUMMARY")
        click.echo("-" * 40)
        click.echo(f"Total Alerts: {alert_summary['total_alerts']}")
        click.echo(f"Rules with Alerts: {alert_summary['rules_with_alerts']}")
        
        most_problematic = alert_summary.get('most_problematic')
        if most_problematic:
            click.echo(f"Most Problematic Rule: {most_problematic[0]} ({most_problematic[1]} alerts)")


def display_json_format(data: Dict[str, Any]):
    """Display performance data in JSON format."""
    click.echo(json.dumps(data, indent=2))


def save_performance_report(data: Dict[str, Any], file_path: Path):
    """Save performance report to file."""
    with open(file_path, 'w') as f:
        json.dump(data, f, indent=2)


if __name__ == '__main__':
    performance()
