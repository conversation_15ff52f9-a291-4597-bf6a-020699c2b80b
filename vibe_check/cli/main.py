"""
CLI Main Module
===========

This module provides the main CLI interface for Vibe Check.
"""

import sys
from typing import Optional, Any, Tuple

import click

from ..core.version import __version__
from .monitor import monitor

# Logger will be configured when needed


@click.group()
@click.version_option(version=__version__, prog_name="Vibe Check")
def cli() -> None:
    """Vibe Check - A project analysis tool for Python codebases."""
    pass


@cli.command()
@click.argument("project_path", type=click.Path(exists=True))
@click.option("--config", "-c", type=click.Path(exists=True), help="Path to configuration file")
@click.option("--output", "-o", type=click.Path(), help="Output directory")
@click.option("--verbose", "-v", is_flag=True, help="Enable verbose output")
@click.option("--quiet", "-q", is_flag=True, help="Suppress output")
@click.option("--security-focused", is_flag=True, help="Focus on security issues")
@click.option("--performance-focused", is_flag=True, help="Focus on performance issues")
@click.option("--maintainability-focused", is_flag=True, help="Focus on maintainability issues")
@click.option("--preset", "-p", type=click.Choice(["default", "minimal", "enhanced", "quick", "comprehensive", "quality", "security"]),
              help="Use a predefined configuration preset")
@click.option("--profile", type=click.Choice(["minimal", "standard", "comprehensive", "security", "performance", "maintainability"]),
              default="standard", help="Analysis profile to use (replaces complex CAW system)")
@click.option("--analyze-trends", is_flag=True, help="Analyze trends compared to previous runs")
@click.option("--report-progress", is_flag=True, help="Report progress between analyses with the same output directory")
@click.option("--custom-report", is_flag=True, help="Generate a custom report")
@click.option("--debug", is_flag=True, help="Enable debug mode with enhanced logging")
@click.option("--log-file", type=click.Path(), help="Path to save detailed logs")
@click.option("--semantic", is_flag=True, help="Enable enhanced semantic analysis (framework detection, meritocracy analysis)")
@click.option("--no-semantic", is_flag=True, help="Disable semantic analysis for faster execution")
@click.option("--vcs-mode", is_flag=True, help="Enable VCS (Vibe Check Standalone) mode with built-in analysis rules")
@click.option("--rule-id", type=str, help="Test a single specific rule (e.g., 'S001', 'ADV003') - requires --vcs-mode")
@click.option("--rule-ids", type=str, help="Run multiple specific rules (e.g., 'S001,S002,C001') - requires --vcs-mode")
@click.option("--exclude-rules", type=str, help="Exclude specific rules (e.g., 'S001,C002') - requires --vcs-mode")
@click.option("--detailed", is_flag=True, help="Show detailed issue information including file locations, line numbers, and fix recommendations")
@click.option("--categories", type=str, help="Run only specific rule categories (e.g., 'style,security,complexity') - requires --vcs-mode")
@click.option("--exclude-categories", type=str, help="Exclude specific rule categories (e.g., 'style,documentation') - requires --vcs-mode")
@click.option("--tools", type=str, help="Run specific external tools (e.g., 'ruff,mypy,bandit') in addition to VCS analysis")
@click.option("--exclude-tools", type=str, help="Exclude specific external tools (e.g., 'pylint,flake8') from analysis")
@click.option("--no-save", is_flag=True, help="Run analysis without saving results to database (useful for quick checks and CI/CD)")
def analyze(project_path: str, config: Optional[str] = None, output: Optional[str] = None,
            verbose: bool = False, quiet: bool = False, security_focused: bool = False,
            performance_focused: bool = False, maintainability_focused: bool = False,
            preset: Optional[str] = None, profile: str = "standard", analyze_trends: bool = False,
            report_progress: bool = False, custom_report: bool = False,
            debug: bool = False, log_file: Optional[str] = None, semantic: bool = False,
            no_semantic: bool = False, vcs_mode: bool = False, rule_id: Optional[str] = None,
            rule_ids: Optional[str] = None, exclude_rules: Optional[str] = None,
            detailed: bool = False, categories: Optional[str] = None,
            exclude_categories: Optional[str] = None, tools: Optional[str] = None,
            exclude_tools: Optional[str] = None, no_save: bool = False) -> None:
    """Analyze a Python project."""
    from .handlers import handle_analyze_command

    handle_analyze_command(
        project_path, config, output, verbose, quiet, security_focused,
        performance_focused, maintainability_focused, preset, profile,
        analyze_trends, report_progress, custom_report, debug, log_file,
        semantic, no_semantic, vcs_mode, rule_id, rule_ids, exclude_rules,
        detailed, categories, exclude_categories, tools, exclude_tools, no_save
    )


@cli.command()
def profiles() -> None:
    """List available analysis profiles."""
    from .handlers import handle_profiles_command
    handle_profiles_command()


@cli.command()
def deps() -> None:
    """Check dependency status for optional interfaces."""
    from .handlers import handle_deps_command
    handle_deps_command()


@cli.command()
@click.argument('project_path', required=False)
def gui(project_path: Optional[str] = None) -> None:
    """Launch the graphical user interface."""
    from .handlers import handle_gui_command
    handle_gui_command(project_path)


@cli.command()
@click.argument("project_path", type=click.Path(exists=True))
@click.option("--config", "-c", type=click.Path(exists=True), help="Path to configuration file")
def tui(project_path: str, config: Optional[str] = None) -> None:
    """Launch the terminal user interface."""
    from .handlers import handle_tui_command
    handle_tui_command(project_path, config)


# Add compare command group
try:
    from .compare_command import compare
    cli.add_command(compare)
except ImportError:
    pass  # Compare command not available


@cli.command()
@click.argument("project_path", type=click.Path(exists=True))
@click.option("--config", "-c", type=click.Path(exists=True), help="Path to configuration file")
@click.option("--host", "-h", default="localhost", help="Host to bind to")
@click.option("--port", "-p", default=8000, help="Port to bind to")
def web(project_path: str, config: Optional[str] = None, host: str = "localhost", port: int = 8000) -> None:
    """Launch the web interface."""
    from .handlers import handle_web_command
    handle_web_command(project_path, config, host, port)


@cli.group()
def plugin() -> None:
    """Manage plugins."""
    pass


@plugin.command("list")
def plugin_list() -> None:
    """List installed plugins."""
    from .handlers import handle_plugin_list_command
    handle_plugin_list_command()


@plugin.command("install")
@click.argument("plugin_name")
def plugin_install(plugin_name: str) -> None:
    """Install a plugin."""
    from .handlers import handle_plugin_install_command
    handle_plugin_install_command(plugin_name)


@plugin.command("uninstall")
@click.argument("plugin_name")
def plugin_uninstall(plugin_name: str) -> None:
    """Uninstall a plugin."""
    from .handlers import handle_plugin_uninstall_command
    handle_plugin_uninstall_command(plugin_name)


@cli.group()
def knowledge() -> None:
    """Manage VibeCheck framework knowledge base."""
    pass


@knowledge.command()
def stats() -> None:
    """Show knowledge base statistics."""
    from ..core.knowledge.framework_knowledge_base import FrameworkKnowledgeBase

    kb = FrameworkKnowledgeBase()
    stats = kb.get_statistics()

    click.echo("📊 VibeCheck Knowledge Base Statistics")
    click.echo("=" * 40)

    click.echo(f"Total Frameworks: {stats['total_frameworks']}")
    click.echo(f"Total Rules: {stats['total_rules']}")

    click.echo("\n📋 Frameworks:")
    for framework in stats['frameworks']:
        fw_knowledge = kb.get_framework_knowledge(framework)
        if fw_knowledge:
            click.echo(f"  • {fw_knowledge.name}: {len(fw_knowledge.rules)} rules")


@knowledge.command()
@click.argument('framework_name')
def show(framework_name: str) -> None:
    """Show detailed information about a framework."""
    from ..core.knowledge.framework_knowledge_base import FrameworkKnowledgeBase

    kb = FrameworkKnowledgeBase()
    framework = kb.get_framework_knowledge(framework_name)

    if not framework:
        click.echo(f"❌ Framework '{framework_name}' not found")
        return

    click.echo(f"📋 {framework.name} Framework Knowledge")
    click.echo("=" * 40)

    click.echo(f"Description: {framework.description}")
    click.echo(f"Version: {framework.version}")
    click.echo(f"Rules: {len(framework.rules)}")
    click.echo(f"Recommendations: {len(framework.recommendations)}")


@knowledge.command()
def validate() -> None:
    """Validate all knowledge base files."""
    from ..core.knowledge.framework_knowledge_base import FrameworkKnowledgeBase

    kb = FrameworkKnowledgeBase()

    click.echo("🔍 Validating Knowledge Base...")
    click.echo(f"✅ Knowledge base validation passed!")
    click.echo(f"📊 Frameworks: {len(kb.frameworks)}")
    click.echo(f"📊 Total Rules: {sum(len(fw.rules) for fw in kb.frameworks.values())}")


@cli.command()
@click.argument("project_path", type=click.Path(exists=True))
@click.option("--output", "-o", type=click.Path(), help="Output directory for debug information")
@click.option("--verbose", "-v", is_flag=True, help="Enable verbose output")
@click.option("--timeout", "-t", type=float, default=120.0, help="Maximum time in seconds to wait for initialization")
@click.option("--open-timeline", is_flag=True, help="Open the timeline and dependency graph visualizations in a browser")
@click.option("--dependency-analysis", is_flag=True, help="Focus on dependency resolution analysis")
@click.option("--registry-analysis", is_flag=True, help="Focus on registry synchronization analysis")
def debug(project_path: str, output: Optional[str] = None, verbose: bool = False, timeout: float = 120.0,
          open_timeline: bool = False, dependency_analysis: bool = False, registry_analysis: bool = False) -> None:
    """
    Debug the analysis process (simplified - actor system removed).

    This command provides basic debugging information for the analysis process.
    The actor system has been removed as part of Phase 0 stabilization.
    """
    from .handlers import handle_debug_command
    handle_debug_command(
        project_path, output, verbose, timeout, open_timeline, dependency_analysis, registry_analysis
    )


# The format_analysis_results function has been moved to formatters.py


def main() -> None:
    """Entry point for the CLI with proper exit handling."""
    import logging
    import sys
    import signal
    import atexit

    # Set up signal handlers for graceful shutdown
    def signal_handler(signum: int, frame: Any) -> None:
        """Handle interrupt signals gracefully."""
        import logging
        logger = logging.getLogger("vibe_check_cli.signal")
        logger.warning("Operation interrupted by user")
        logger.error("Operation interrupted by user")
        sys.exit(130)  # Standard exit code for Ctrl+C

    def cleanup() -> None:
        """Cleanup function called on exit."""
        # Ensure any remaining resources are cleaned up
        logging.shutdown()

    # Register cleanup and signal handlers
    atexit.register(cleanup)
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        # Setup structured logging
        from vibe_check.core.logging.setup import setup_logging

        verbose = "--verbose" in sys.argv or "-v" in sys.argv
        quiet = "--quiet" in sys.argv or "-q" in sys.argv

        logger = setup_logging(debug=verbose, quiet=quiet)

        logger.debug("Starting Vibe Check CLI")
        logger.debug(f"Python version: {sys.version}")
        logger.debug(f"Arguments: {sys.argv}")

        # Run the CLI with timeout protection
        try:
            cli()
            logger.debug("Vibe Check CLI completed successfully")
        except SystemExit as e:
            # Handle explicit exits properly
            logger.debug(f"CLI exited with code: {e.code}")
            sys.exit(e.code)
        except KeyboardInterrupt:
            # Handle Ctrl+C gracefully
            logger.warning("Operation interrupted by user during CLI execution")
            logger.error("Operation interrupted by user")
            sys.exit(130)

    except Exception as e:
        # Handle unexpected errors
        from .error_handler import handle_unexpected_error
        handle_unexpected_error(e, "CLI startup")
    finally:
        # Ensure cleanup happens
        cleanup()


# Add install-hooks command
@cli.command("install-hooks")
@click.option("--project-path", "-p", type=click.Path(exists=True), default=".",
              help="Path to the project directory (default: current directory)")
@click.option("--level", "-l", type=click.Choice(["minimal", "standard", "strict"]),
              default="standard", help="Validation level (default: standard)")
@click.option("--merge/--no-merge", default=True,
              help="Merge with existing pre-commit config (default: merge)")
@click.option("--dry-run", is_flag=True,
              help="Show what would be done without making changes")
@click.option("--args", multiple=True,
              help="Additional arguments to pass to Vibe Check hooks")
def install_hooks(project_path: str, level: str, merge: bool, dry_run: bool, args: Tuple[str, ...]) -> None:
    """Install Vibe Check as a pre-commit hook.

    This command provides one-step setup for integrating Vibe Check into
    pre-commit workflows with automatic conflict detection and resolution.

    Examples:
        vibe-check install-hooks
        vibe-check install-hooks --level=strict --dry-run
        vibe-check install-hooks --project-path=/path/to/project --args=--fix-safe
    """
    from .commands import install_hooks_command

    custom_args = list(args) if args else None

    install_hooks_command(
        project_path=project_path,
        validation_level=level,
        merge_existing=merge,
        dry_run=dry_run,
        custom_args=custom_args
    )

# Add monitoring commands
cli.add_command(monitor)

# Add performance command
try:
    from .performance_command import performance
    cli.add_command(performance)
except ImportError:
    pass  # Performance command not available

# Add quality gates command
try:
    from .quality_gates import quality_gates
    cli.add_command(quality_gates)
except ImportError:
    pass  # Quality gates command not available

# Add test rules command
try:
    from .test_rules import test_rules
    cli.add_command(test_rules)
except ImportError:
    pass  # Test rules command not available

# Add hooks command
try:
    from .generate_hooks import hooks
    cli.add_command(hooks)
except ImportError:
    pass  # Hooks command not available

# Add config command
try:
    from .migrate_config import config
    cli.add_command(config)
except ImportError:
    pass  # Config command not available

# Add dev command
try:
    from .developer_experience import dev
    cli.add_command(dev)
except ImportError:
    pass  # Dev command not available


if __name__ == "__main__":
    main()
