"""
File: vibe_check/cli/standalone.py
Purpose: Standalone CLI commands for VCS (vibe-lint, vibe-format, vibe-check-standalone)
Related Files: vibe_check/cli/main.py, vibe_check/core/vcs/
Dependencies: click, vibe_check.core.vcs
"""

import asyncio
import sys
from pathlib import Path
from typing import List, Optional, Tuple

import click
from rich.console import Console
from rich.table import Table

from vibe_check.core.vcs.engine import VibeCheckEngine
from vibe_check.core.vcs.config import VCSConfig
from vibe_check.core.vcs.models import AnalysisResult, AnalysisTarget, AnalysisContext, EngineMode, RuleCategory
from vibe_check.cli.watch_mode import run_watch_mode
from vibe_check.cli.completion import (
    complete_rule_categories, complete_output_formats, complete_external_tools,
    generate_completion_script, install_completion
)
from vibe_check.cli.parallel_processing import analyze_with_parallel_processing, get_recommended_worker_count
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)
console = Console()


class StandaloneCLI:
    """Standalone CLI interface for VCS commands."""
    
    def __init__(self) -> None:
        self.engine: Optional[VibeCheckEngine] = None
        self.config: Optional[VCSConfig] = None
    
    async def initialize_engine(self, mode: EngineMode = EngineMode.STANDALONE) -> None:
        """Initialize the VCS engine."""
        try:
            self.config = VCSConfig(mode=mode)
            self.engine = VibeCheckEngine(mode=mode, config=self.config)
            await self.engine.start()
            logger.info(f"VCS engine initialized in {mode.value} mode")
        except Exception as e:
            logger.error(f"Failed to initialize VCS engine: {e}")
            raise click.ClickException(f"Engine initialization failed: {e}")
    
    async def cleanup_engine(self) -> None:
        """Clean up the VCS engine."""
        if self.engine and self.engine.is_enabled():
            await self.engine.stop()
            logger.info("VCS engine stopped")
    
    async def analyze_files(
        self,
        paths: List[Path],
        rules: Optional[List[str]] = None,
        external_tools: Optional[List[str]] = None,
        verbose: bool = False
    ) -> AnalysisResult:
        """Analyze files with specified rules and tools."""
        if not self.engine:
            raise click.ClickException("Engine not initialized")
        
        try:
            # Configure rules if specified
            if rules:
                # Filter rules by category/name
                available_rules = self.engine.rule_registry.get_all_rules()
                filtered_rules = [r for r in available_rules if any(rule in r.rule_id for rule in rules)]
                if verbose:
                    console.print(f"Using {len(filtered_rules)} rules: {[r.rule_id for r in filtered_rules]}")

            # Create analysis context
            context = AnalysisContext.create_default(EngineMode.STANDALONE)

            # Analyze files
            results = []
            for path in paths:
                if path.is_file():
                    target = AnalysisTarget.from_file(path)
                    result = await self.engine.analyze(target, context)
                    results.append(result)
                elif path.is_dir():
                    # Analyze directory
                    python_files = list(path.rglob("*.py"))
                    if verbose:
                        console.print(f"Found {len(python_files)} Python files in {path}")

                    for file_path in python_files:
                        target = AnalysisTarget.from_file(file_path)
                        result = await self.engine.analyze(target, context)
                        results.append(result)
            
            # Combine results
            combined_result = AnalysisResult.combine_results(results)
            return combined_result
            
        except Exception as e:
            logger.error(f"Analysis failed: {e}")
            raise click.ClickException(f"Analysis failed: {e}")


# Global CLI instance
cli_instance = StandaloneCLI()


@click.group()
@click.version_option()
def vibe_lint():
    """Vibe Check Standalone Linter - Advanced Python code analysis."""
    pass


@vibe_lint.command()
@click.argument('paths', nargs=-1, type=click.Path(exists=True, path_type=Path))
@click.option('--rules', '-r', help='Comma-separated list of rule categories (style,security,complexity,documentation,imports,types)', shell_complete=complete_rule_categories)
@click.option('--external-tools', '-e', help='Comma-separated list of external tools to coordinate with (ruff,mypy,bandit)', shell_complete=complete_external_tools)
@click.option('--format', '-f', 'output_format', default='text', type=click.Choice(['text', 'json', 'yaml', 'sarif', 'github']), help='Output format', shell_complete=complete_output_formats)
@click.option('--verbose', '-v', is_flag=True, help='Verbose output')
@click.option('--fix', is_flag=True, help='Auto-fix issues where possible')
@click.option('--parallel', '-p', is_flag=True, help='Use parallel processing for faster analysis')
@click.option('--workers', '-w', type=int, help='Number of parallel workers (default: auto-detect)')
def check(paths: Tuple[Path, ...], rules: Optional[str], external_tools: Optional[str],
          output_format: str, verbose: bool, fix: bool, parallel: bool, workers: Optional[int]):
    """Code analysis with configurable rules."""
    async def run_check():
        try:
            await cli_instance.initialize_engine()
            
            if not paths:
                paths_list = [Path.cwd()]
            else:
                paths_list = list(paths)
            
            # Parse rules
            rules_list = rules.split(',') if rules else None
            external_tools_list = external_tools.split(',') if external_tools else None
            
            if verbose:
                console.print(f"Analyzing {len(paths_list)} path(s)")
                console.print(f"Paths: {[str(p) for p in paths_list]}")
                if rules_list:
                    console.print(f"Using rule categories: {rules_list}")
                if external_tools_list:
                    console.print(f"Coordinating with tools: {external_tools_list}")
            
            # Run analysis
            try:
                if parallel:
                    # Use parallel processing
                    max_workers = workers or get_recommended_worker_count()
                    if verbose:
                        console.print(f"Using parallel processing with {max_workers} workers")

                    results, stats = await analyze_with_parallel_processing(
                        cli_instance.engine, paths_list, max_workers, verbose
                    )
                    result = AnalysisResult.combine_results(results)

                    if verbose:
                        console.print(f"[blue]Performance stats:[/blue]")
                        console.print(f"  Files analyzed: {stats['total_files']}")
                        console.print(f"  Total time: {stats['total_execution_time']:.2f}s")
                        console.print(f"  Files/second: {stats['files_per_second']:.1f}")
                        console.print(f"  Workers used: {stats['max_workers']}")
                else:
                    # Use sequential processing
                    result = await cli_instance.analyze_files(
                        paths_list, rules_list, external_tools_list, verbose
                    )
            except Exception as e:
                console.print(f"[red]Analysis failed: {e}[/red]")
                import traceback
                console.print(traceback.format_exc())
                raise
            
            # Format and display results
            try:
                formatted_output = format_result(result, output_format)
                console.print(formatted_output)
            except Exception as e:
                console.print(f"[red]Error formatting output: {e}[/red]")
                # Fallback to simple text format
                if result.issues:
                    console.print(f"\n[red]Found {len(result.issues)} issues:[/red]")
                    for issue in result.issues:
                        console.print(f"  {issue.rule_id}: Line {issue.line}, Column {issue.column}")
                        console.print(f"    {issue.message}")
                else:
                    console.print("\n[green]No issues found![/green]")
            
            # Exit with error code if issues found
            if result.issues:
                sys.exit(1)
                
        except Exception as e:
            console.print(f"[red]Error: {e}[/red]")
            sys.exit(1)
        finally:
            await cli_instance.cleanup_engine()
    
    asyncio.run(run_check())


@vibe_lint.command()
@click.argument('paths', nargs=-1, type=click.Path(exists=True, path_type=Path))
@click.option('--diff', is_flag=True, help='Show diff instead of applying changes')
@click.option('--line-length', default=88, help='Maximum line length')
@click.option('--check', is_flag=True, help='Check if formatting is needed without applying')
def format(paths: Tuple[Path, ...], diff: bool, line_length: int, check: bool):
    """Code formatting with style options."""
    console.print("[yellow]Code formatting feature coming in Sprint VCS-3.2[/yellow]")
    console.print("For now, use: vibe-lint check --rules style --fix")


@vibe_lint.command()
@click.argument('paths', nargs=-1, type=click.Path(exists=True, path_type=Path))
@click.option('--interactive', '-i', is_flag=True, help='Interactive fix mode')
@click.option('--rules', '-r', help='Comma-separated list of rule categories to fix')
@click.option('--safe-only', is_flag=True, default=True, help='Only apply safe fixes (default: true)')
@click.option('--verbose', '-v', is_flag=True, help='Verbose output')
def fix(paths: Tuple[Path, ...], interactive: bool, rules: Optional[str], safe_only: bool, verbose: bool):
    """Auto-fix issues where possible."""
    async def run_fix():
        try:
            await cli_instance.initialize_engine()

            if not paths:
                paths_list = [Path.cwd()]
            else:
                paths_list = list(paths)

            # Parse rules filter
            rules_list = None
            if rules:
                rules_list = [r.strip() for r in rules.split(',')]

            if verbose:
                console.print(f"Auto-fixing {len(paths_list)} path(s)")
                if rules_list:
                    console.print(f"Filtering by rule categories: {rules_list}")
                console.print(f"Safe-only mode: {safe_only}")
                console.print(f"Interactive mode: {interactive}")

            total_fixes = 0

            for path in paths_list:
                if path.is_file() and path.suffix == '.py':
                    # Fix single file
                    target = AnalysisTarget.from_file(path)
                    context = AnalysisContext.create_default(EngineMode.STANDALONE)

                    if rules_list:
                        # Filter context by rules
                        context.enabled_categories = {RuleCategory(r) for r in rules_list if r in [cat.value for cat in RuleCategory]}

                    fix_report = await cli_instance.engine.auto_fix(
                        target, context, safe_only, interactive
                    )

                    if fix_report.operations:
                        console.print(f"[green]Fixed {len(fix_report.operations)} issues in {path}[/green]")
                        total_fixes += len(fix_report.operations)

                        if verbose:
                            for operation in fix_report.operations:
                                console.print(f"  - {operation.description}")
                    else:
                        console.print(f"[dim]No fixable issues found in {path}[/dim]")

                elif path.is_dir():
                    # Fix all Python files in directory
                    python_files = list(path.rglob("*.py"))
                    filtered_files = [f for f in python_files if not any(part in str(f) for part in ['venv', '__pycache__', '.git'])]

                    console.print(f"Found {len(filtered_files)} Python files in {path}")

                    for file_path in filtered_files:
                        target = AnalysisTarget.from_file(file_path)
                        context = AnalysisContext.create_default(EngineMode.STANDALONE)

                        if rules_list:
                            context.enabled_categories = {RuleCategory(r) for r in rules_list if r in [cat.value for cat in RuleCategory]}

                        fix_report = await cli_instance.engine.auto_fix(
                            target, context, safe_only, interactive
                        )

                        if fix_report.operations:
                            console.print(f"[green]Fixed {len(fix_report.operations)} issues in {file_path}[/green]")
                            total_fixes += len(fix_report.operations)

                            if verbose:
                                for operation in fix_report.operations:
                                    console.print(f"  - {operation.description}")

            if total_fixes > 0:
                console.print(f"\n[green]Successfully applied {total_fixes} fixes![/green]")
            else:
                console.print("\n[yellow]No fixable issues found[/yellow]")

        except Exception as e:
            console.print(f"[red]Error: {e}[/red]")
            sys.exit(1)
        finally:
            await cli_instance.cleanup_engine()

    asyncio.run(run_fix())


@vibe_lint.command()
@click.argument('paths', nargs=-1, type=click.Path(exists=True, path_type=Path))
@click.option('--format', '-f', 'output_format', default='text', type=click.Choice(['text', 'json', 'yaml', 'sarif', 'github']), help='Output format')
@click.option('--verbose', '-v', is_flag=True, help='Verbose output')
def watch(paths: Tuple[Path, ...], output_format: str, verbose: bool):
    """Watch files for changes and analyze continuously."""
    async def run_watch():
        try:
            await cli_instance.initialize_engine()

            if not paths:
                paths_list = [Path.cwd()]
            else:
                paths_list = list(paths)

            console.print(f"[green]Starting watch mode for {len(paths_list)} path(s)[/green]")
            console.print("[dim]Press Ctrl+C to stop[/dim]")

            await run_watch_mode(cli_instance.engine, paths_list, output_format, verbose)

        except KeyboardInterrupt:
            console.print("\n[yellow]Watch mode stopped[/yellow]")
        except Exception as e:
            console.print(f"[red]Error: {e}[/red]")
            sys.exit(1)
        finally:
            await cli_instance.cleanup_engine()

    asyncio.run(run_watch())


@vibe_lint.group()
def config():
    """Configuration management."""
    pass


@config.command()
@click.argument('key', required=False)
def show(key: Optional[str]):
    """Show configuration values."""
    async def run_show():
        try:
            await cli_instance.initialize_engine()
            
            if key == 'rules':
                # Show available rules
                registry = cli_instance.engine.rule_registry
                rules = registry.get_all_rules()
                
                table = Table(title="Available Rules")
                table.add_column("ID", style="cyan")
                table.add_column("Category", style="green")
                table.add_column("Description", style="white")
                table.add_column("Enabled", style="yellow")
                
                for rule in rules:
                    table.add_row(
                        rule.rule_id,
                        rule.category.value,
                        rule.description,
                        "✓" if rule.enabled else "✗"
                    )
                
                console.print(table)
            else:
                console.print("Configuration management coming soon")
                
        except Exception as e:
            console.print(f"[red]Error: {e}[/red]")
        finally:
            await cli_instance.cleanup_engine()
    
    asyncio.run(run_show())


@vibe_lint.group()
def rules():
    """Rule information and management."""
    pass


@rules.command()
@click.option('--category', '-c', help='Filter by category')
def list(category: Optional[str]):
    """List available rules."""
    async def run_list():
        try:
            await cli_instance.initialize_engine()
            
            registry = cli_instance.engine.rule_registry
            all_rules = registry.get_all_rules()
            
            if category:
                filtered_rules = [r for r in all_rules if r.category.value == category]
            else:
                filtered_rules = all_rules
            
            table = Table(title=f"Rules{f' - {category}' if category else ''}")
            table.add_column("ID", style="cyan")
            table.add_column("Category", style="green")
            table.add_column("Description", style="white")
            table.add_column("Enabled", style="yellow")

            for rule in filtered_rules:
                table.add_row(
                    rule.rule_id,
                    rule.category.value,
                    rule.description,
                    "✓" if rule.enabled else "✗"
                )
            
            console.print(table)
            console.print(f"\nTotal: {len(filtered_rules)} rules")
            
        except Exception as e:
            console.print(f"[red]Error: {e}[/red]")
        finally:
            await cli_instance.cleanup_engine()
    
    asyncio.run(run_list())


@vibe_lint.group()
def completion():
    """Shell completion management."""
    pass


@completion.command()
@click.argument('shell', type=click.Choice(['bash', 'zsh', 'fish']))
@click.option('--output', '-o', type=click.Path(), help='Output file (default: stdout)')
def install(shell: str, output: Optional[str]):
    """Install shell completion."""
    output_path = Path(output) if output else None

    if install_completion(shell, output_path):
        if output_path:
            console.print(f"[green]Completion installed to {output_path}[/green]")
        else:
            console.print(f"[green]Completion script generated for {shell}[/green]")
    else:
        console.print(f"[red]Failed to install completion for {shell}[/red]")
        sys.exit(1)


@completion.command()
@click.argument('shell', type=click.Choice(['bash', 'zsh', 'fish']))
def generate(shell: str):
    """Generate completion script."""
    script = generate_completion_script(shell)
    console.print(script)


if __name__ == '__main__':
    vibe_lint()
