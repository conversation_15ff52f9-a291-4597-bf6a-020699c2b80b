"""
CLI commands for quality gate management and reporting.
"""

import click
import json
from typing import Dict, Any

from ..core.vcs.quality_gates import get_rule_quality_gate_system


@click.group()
def quality_gates():
    """Quality gate management commands."""
    pass


@quality_gates.command()
@click.option('--format', type=click.Choice(['table', 'json', 'summary']), default='table',
              help='Output format')
@click.option('--show-disabled', is_flag=True, help='Show disabled rules')
@click.option('--show-failing', is_flag=True, help='Show only failing rules')
def report(format: str, show_disabled: bool, show_failing: bool):
    """Generate quality gate report."""
    
    quality_system = get_rule_quality_gate_system()
    report_data = quality_system.get_quality_report()
    
    if format == 'json':
        click.echo(json.dumps(report_data, indent=2))
        return
    
    if format == 'summary':
        _print_summary_report(report_data)
        return
    
    # Default table format
    _print_table_report(report_data, show_disabled, show_failing)


@quality_gates.command()
@click.argument('rule_id')
def validate(rule_id: str):
    """Validate a specific rule against quality gates."""
    
    quality_system = get_rule_quality_gate_system()
    
    # Check if we have metrics for this rule
    if rule_id not in quality_system.metrics:
        click.echo(f"❌ No validation metrics found for rule {rule_id}")
        click.echo("Run analysis with this rule to generate metrics.")
        return
    
    metrics = quality_system.metrics[rule_id]
    
    click.echo(f"🔍 Quality Gate Validation for Rule {rule_id}")
    click.echo("=" * 50)
    
    # Status
    status_emoji = "✅" if metrics.validation_status == 'PASSED' else "❌"
    click.echo(f"Status: {status_emoji} {metrics.validation_status}")
    
    # Metrics
    click.echo(f"\n📊 Metrics:")
    click.echo(f"  False Positive Rate: {metrics.false_positive_rate:.2%}")
    click.echo(f"  Execution Time: {metrics.execution_time_ms:.2f}ms")
    click.echo(f"  Memory Usage: {metrics.memory_usage_mb:.2f}MB")
    click.echo(f"  Test Cases Passed: {metrics.test_cases_passed}")
    click.echo(f"  Test Cases Failed: {metrics.test_cases_failed}")
    
    # Scores
    click.echo(f"\n🎯 Scores:")
    click.echo(f"  Accuracy: {metrics.accuracy_score:.1f}/100")
    click.echo(f"  Performance: {metrics.performance_score:.1f}/100")
    click.echo(f"  Reliability: {metrics.reliability_score:.1f}/100")
    click.echo(f"  Overall: {metrics.overall_score:.1f}/100")
    
    # Thresholds
    config = quality_system.config
    click.echo(f"\n⚠️  Thresholds:")
    click.echo(f"  Max False Positive Rate: {config.max_false_positive_rate:.2%}")
    click.echo(f"  Max Execution Time: {config.max_execution_time_ms:.0f}ms")
    click.echo(f"  Max Memory Usage: {config.max_memory_usage_mb:.0f}MB")


@quality_gates.command()
@click.argument('rule_id')
@click.confirmation_option(prompt='Are you sure you want to disable this rule?')
def disable(rule_id: str):
    """Disable a rule from execution."""
    
    quality_system = get_rule_quality_gate_system()
    quality_system.disabled_rules.add(rule_id)
    quality_system._save_metrics()
    
    click.echo(f"✅ Rule {rule_id} has been disabled")


@quality_gates.command()
@click.argument('rule_id')
def enable(rule_id: str):
    """Enable a previously disabled rule."""
    
    quality_system = get_rule_quality_gate_system()
    
    if rule_id in quality_system.disabled_rules:
        quality_system.disabled_rules.remove(rule_id)
        quality_system._save_metrics()
        click.echo(f"✅ Rule {rule_id} has been enabled")
    else:
        click.echo(f"ℹ️  Rule {rule_id} is not disabled")


def _print_summary_report(report_data: Dict[str, Any]) -> None:
    """Print summary format report."""
    summary = report_data['summary']
    scores = report_data['scores']
    issues = report_data['issues']
    
    click.echo("🎯 Quality Gate Summary")
    click.echo("=" * 30)
    
    # Overall status
    pass_rate = summary['pass_rate']
    if pass_rate >= 90:
        status_emoji = "🟢"
        status_text = "EXCELLENT"
    elif pass_rate >= 75:
        status_emoji = "🟡"
        status_text = "GOOD"
    else:
        status_emoji = "🔴"
        status_text = "NEEDS ATTENTION"
    
    click.echo(f"Overall Status: {status_emoji} {status_text} ({pass_rate:.1f}% pass rate)")
    
    # Rule counts
    click.echo(f"\n📊 Rule Status:")
    click.echo(f"  Total Rules: {summary['total_rules']}")
    click.echo(f"  ✅ Passed: {summary['passed_rules']}")
    click.echo(f"  ❌ Failed: {summary['failed_rules']}")
    click.echo(f"  🚫 Disabled: {summary['disabled_rules']}")
    
    # Average scores
    click.echo(f"\n🎯 Average Scores:")
    click.echo(f"  Accuracy: {scores['average_accuracy']:.1f}/100")
    click.echo(f"  Performance: {scores['average_performance']:.1f}/100")
    click.echo(f"  Reliability: {scores['average_reliability']:.1f}/100")
    click.echo(f"  Overall: {scores['average_overall']:.1f}/100")
    
    # Issues
    if issues['high_false_positive_rules']:
        click.echo(f"\n⚠️  High False Positive Rules: {', '.join(issues['high_false_positive_rules'])}")
    
    if issues['slow_rules']:
        click.echo(f"🐌 Slow Rules: {', '.join(issues['slow_rules'])}")
    
    if issues['disabled_rules']:
        click.echo(f"🚫 Disabled Rules: {', '.join(issues['disabled_rules'])}")


def _print_table_report(report_data: Dict[str, Any], show_disabled: bool, show_failing: bool) -> None:
    """Print table format report."""
    quality_system = get_rule_quality_gate_system()
    
    click.echo("🎯 Quality Gate Report")
    click.echo("=" * 80)
    
    # Header
    click.echo(f"{'Rule ID':<12} {'Status':<8} {'FP Rate':<8} {'Time(ms)':<10} {'Accuracy':<9} {'Overall':<8}")
    click.echo("-" * 80)
    
    # Rules
    for rule_id, metrics in quality_system.metrics.items():
        # Filter based on options
        if show_failing and metrics.validation_status == 'PASSED':
            continue
        
        if not show_disabled and rule_id in quality_system.disabled_rules:
            continue
        
        # Status emoji
        if rule_id in quality_system.disabled_rules:
            status = "🚫 DISABLED"
        elif metrics.validation_status == 'PASSED':
            status = "✅ PASS"
        elif metrics.validation_status == 'FAILED':
            status = "❌ FAIL"
        else:
            status = "⏳ PENDING"
        
        click.echo(f"{rule_id:<12} {status:<8} {metrics.false_positive_rate:.1%}    "
                  f"{metrics.execution_time_ms:>6.1f}    {metrics.accuracy_score:>6.1f}    "
                  f"{metrics.overall_score:>6.1f}")
    
    # Summary
    summary = report_data['summary']
    click.echo("-" * 80)
    click.echo(f"Total: {summary['total_rules']} | "
              f"Passed: {summary['passed_rules']} | "
              f"Failed: {summary['failed_rules']} | "
              f"Disabled: {summary['disabled_rules']} | "
              f"Pass Rate: {summary['pass_rate']:.1f}%")


if __name__ == '__main__':
    quality_gates()
