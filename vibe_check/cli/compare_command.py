"""
File: vibe_check/cli/compare_command.py
Purpose: CLI command for comparing VCS vs Plugin analysis results
Related Files:
    - vibe_check/core/persistence/comparison.py - Comparison engine
    - vibe_check/core/persistence/storage.py - Result storage
    - vibe_check/cli/commands.py - Main CLI commands
Dependencies: click, rich, json

Provides user-facing commands for comparing analysis results.
"""

import json
import click
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any

from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text

from ..core.persistence.storage import ResultStorage
from ..core.persistence.comparison import ComparisonEngine
from ..core.persistence.models import AnalysisMode

console = Console()


@click.group()
def compare():
    """Compare VCS and Plugin analysis results."""
    pass


@compare.command()
@click.option('--project', '-p', help='Project path to find runs for')
@click.option('--limit', '-l', default=10, help='Maximum number of runs to show')
@click.option('--mode', type=click.Choice(['vcs', 'plugin', 'all']), default='all', 
              help='Filter by analysis mode')
def list_runs(project: Optional[str], limit: int, mode: str):
    """List recent analysis runs."""
    storage = ResultStorage()
    
    if project:
        runs = storage.find_runs_by_project(project, limit)
        console.print(f"\n📁 Analysis runs for project: {project}")
    else:
        runs = storage.get_recent_runs(limit)
        console.print(f"\n📊 Recent analysis runs (last {limit})")
    
    if not runs:
        console.print("❌ No analysis runs found")
        return
    
    # Filter by mode if specified
    if mode != 'all':
        target_mode = AnalysisMode.VCS if mode == 'vcs' else AnalysisMode.PLUGIN
        runs = [run for run in runs if run.analysis_mode == target_mode]
    
    # Create table
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("Run ID", style="cyan", width=8)
    table.add_column("Mode", style="green", width=8)
    table.add_column("Project", style="blue", width=30)
    table.add_column("Start Time", style="yellow", width=20)
    table.add_column("Files", style="white", width=8)
    table.add_column("Issues", style="red", width=8)
    table.add_column("Duration", style="magenta", width=10)
    table.add_column("Status", style="white", width=10)
    
    for run in runs:
        status = "✅ Success" if run.success else "❌ Failed"
        mode_display = "🔧 VCS" if run.analysis_mode == AnalysisMode.VCS else "🔌 Plugin"
        
        table.add_row(
            str(run.run_id),
            mode_display,
            run.project_path[-30:] if len(run.project_path) > 30 else run.project_path,
            run.start_time.strftime("%Y-%m-%d %H:%M"),
            str(run.total_files),
            str(run.total_issues),
            f"{run.analysis_duration:.1f}s",
            status
        )
    
    console.print(table)


@compare.command()
@click.option('--project', '-p', help='Project path to find comparison pairs for')
@click.option('--limit', '-l', default=5, help='Maximum number of pairs to show')
def find_pairs(project: Optional[str], limit: int):
    """Find VCS/Plugin analysis pairs for comparison."""
    if not project:
        console.print("❌ Project path is required for finding comparison pairs")
        return
    
    storage = ResultStorage()
    pairs = storage.find_comparison_pairs(project)
    
    if not pairs:
        console.print(f"❌ No comparison pairs found for project: {project}")
        return
    
    console.print(f"\n🔍 Found {len(pairs)} comparison pairs for project: {project}")
    
    # Create table
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("Pair #", style="cyan", width=8)
    table.add_column("VCS Run", style="green", width=12)
    table.add_column("Plugin Run", style="blue", width=12)
    table.add_column("Time Diff", style="yellow", width=12)
    table.add_column("VCS Issues", style="red", width=12)
    table.add_column("Plugin Issues", style="red", width=12)
    table.add_column("Compare Command", style="white", width=40)
    
    for i, pair in enumerate(pairs[:limit], 1):
        vcs_run = pair['vcs_run']
        plugin_run = pair['plugin_run']
        time_diff = pair['time_diff_seconds']
        
        time_diff_str = f"{time_diff:.0f}s" if time_diff < 60 else f"{time_diff/60:.1f}m"
        
        compare_cmd = f"vibe-check compare run {vcs_run.run_id} {plugin_run.run_id}"
        
        table.add_row(
            str(i),
            str(vcs_run.run_id),
            str(plugin_run.run_id),
            time_diff_str,
            str(vcs_run.total_issues),
            str(plugin_run.total_issues),
            compare_cmd
        )
    
    console.print(table)


@compare.command()
@click.argument('vcs_run_id', type=int)
@click.argument('plugin_run_id', type=int)
@click.option('--output', '-o', help='Output file for detailed report (JSON)')
@click.option('--show-files', is_flag=True, help='Show file-level comparison details')
@click.option('--show-issues', is_flag=True, help='Show issue-level comparison details')
def run(vcs_run_id: int, plugin_run_id: int, output: Optional[str], 
        show_files: bool, show_issues: bool):
    """Compare two analysis runs (VCS vs Plugin)."""
    try:
        engine = ComparisonEngine()
        comparison = engine.compare_runs(vcs_run_id, plugin_run_id)
        report = engine.generate_comparison_report(comparison)
        
        # Display summary
        _display_comparison_summary(comparison, report)
        
        # Display file details if requested
        if show_files:
            _display_file_comparison(comparison)
        
        # Display issue details if requested
        if show_issues:
            _display_issue_comparison(comparison)
        
        # Save detailed report if requested
        if output:
            output_path = Path(output)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            console.print(f"\n💾 Detailed report saved to: {output_path}")
        
    except Exception as e:
        console.print(f"❌ Error comparing runs: {e}")


def _display_comparison_summary(comparison, report):
    """Display comparison summary."""
    summary = report['summary']
    
    # Create summary panel
    summary_text = Text()
    summary_text.append("🔧 VCS Run: ", style="bold green")
    summary_text.append(f"ID {comparison.vcs_run.run_id} - ", style="cyan")
    summary_text.append(f"{summary['vcs_total_issues']} issues in {summary['vcs_total_files']} files\n")
    
    summary_text.append("🔌 Plugin Run: ", style="bold blue")
    summary_text.append(f"ID {comparison.plugin_run.run_id} - ", style="cyan")
    summary_text.append(f"{summary['plugin_total_issues']} issues in {summary['plugin_total_files']} files\n\n")
    
    # Performance comparison
    if 'speed_ratio' in summary:
        if summary['vcs_faster']:
            summary_text.append("⚡ Performance: ", style="bold yellow")
            summary_text.append(f"VCS is {summary['speed_ratio']:.1f}x faster\n", style="green")
        else:
            summary_text.append("⚡ Performance: ", style="bold yellow")
            summary_text.append(f"Plugin is {1/summary['speed_ratio']:.1f}x faster\n", style="red")
    
    # Coverage metrics
    if 'vcs_coverage_percent' in summary:
        summary_text.append("📊 Coverage: ", style="bold magenta")
        summary_text.append(f"VCS covers {summary['vcs_coverage_percent']:.1f}% of plugin issues\n", style="white")
        summary_text.append("🎯 Plugin Advantage: ", style="bold red")
        summary_text.append(f"{summary['plugin_advantage_percent']:.1f}% unique issues\n", style="white")
    
    console.print(Panel(summary_text, title="📊 Comparison Summary", border_style="blue"))


def _display_file_comparison(comparison):
    """Display file-level comparison details."""
    console.print("\n📁 File-Level Comparison")
    
    # Create file comparison table
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("File", style="blue", width=40)
    table.add_column("VCS Issues", style="green", width=12)
    table.add_column("Plugin Issues", style="red", width=12)
    table.add_column("Shared", style="yellow", width=8)
    table.add_column("Complexity Δ", style="cyan", width=12)
    
    for file_comp in comparison.file_comparisons:
        if not file_comp.has_both_results():
            continue
        
        vcs_issues = len(file_comp.vcs_result.issues)
        plugin_issues = len(file_comp.plugin_result.issues)
        shared = file_comp.issue_comparison.shared_count if file_comp.issue_comparison else 0
        
        metrics_diff = file_comp.get_metrics_diff()
        complexity_diff = metrics_diff.get('complexity_diff', 0)
        complexity_str = f"{complexity_diff:+.1f}" if complexity_diff != 0 else "0.0"
        
        table.add_row(
            file_comp.file_path[-40:] if len(file_comp.file_path) > 40 else file_comp.file_path,
            str(vcs_issues),
            str(plugin_issues),
            str(shared),
            complexity_str
        )
    
    console.print(table)


def _display_issue_comparison(comparison):
    """Display issue-level comparison details."""
    if not comparison.overall_issue_comparison:
        return
    
    ic = comparison.overall_issue_comparison
    console.print("\n🔍 Issue-Level Analysis")
    
    # Enhancement opportunities
    report = ComparisonEngine().generate_comparison_report(comparison)
    opportunities = report['issue_analysis']['enhancement_opportunities']
    
    if opportunities['by_tool']:
        console.print("\n🛠️ Enhancement Opportunities by Tool:")
        for tool, count in list(opportunities['by_tool'].items())[:5]:
            console.print(f"   • {tool}: {count} unique issues")
    
    if opportunities['by_category']:
        console.print("\n📂 Enhancement Opportunities by Category:")
        for category, count in list(opportunities['by_category'].items())[:5]:
            console.print(f"   • {category}: {count} unique issues")
    
    if opportunities['top_rules']:
        console.print("\n🔧 Top Missing Rules:")
        for rule, count in list(opportunities['top_rules'].items())[:5]:
            console.print(f"   • {rule}: {count} occurrences")


@compare.command()
@click.option('--project', '-p', help='Project path to analyze')
@click.option('--auto-run', is_flag=True, help='Automatically run both VCS and plugin analysis')
@click.option('--profile', default='standard', help='Analysis profile to use')
def auto(project: Optional[str], auto_run: bool, profile: str):
    """Automatically compare VCS vs Plugin for a project."""
    if not project:
        console.print("❌ Project path is required for auto comparison")
        return
    
    project_path = Path(project)
    if not project_path.exists():
        console.print(f"❌ Project path does not exist: {project}")
        return
    
    console.print(f"🔍 Auto-comparing VCS vs Plugin for: {project}")
    
    if auto_run:
        console.print("\n🚀 Running both analysis modes...")
        
        # Import here to avoid circular imports
        from .commands import analyze_command
        
        # Run plugin analysis
        console.print("🔌 Running plugin analysis...")
        try:
            plugin_result = analyze_command(
                project_path=str(project_path),
                vcs_mode=False,
                profile=profile,
                quiet=True
            )
            console.print("✅ Plugin analysis completed")
        except Exception as e:
            console.print(f"❌ Plugin analysis failed: {e}")
            return
        
        # Run VCS analysis
        console.print("🔧 Running VCS analysis...")
        try:
            vcs_result = analyze_command(
                project_path=str(project_path),
                vcs_mode=True,
                profile=profile,
                quiet=True
            )
            console.print("✅ VCS analysis completed")
        except Exception as e:
            console.print(f"❌ VCS analysis failed: {e}")
            console.print("🔧 This indicates VCS engine needs fixes - see Epic 7 in roadmap")
            return
    
    # Find and display comparison pairs
    storage = ResultStorage()
    pairs = storage.find_comparison_pairs(str(project_path))
    
    if pairs:
        latest_pair = pairs[0]  # Most recent pair
        console.print(f"\n🎯 Comparing latest runs: VCS {latest_pair['vcs_run'].run_id} vs Plugin {latest_pair['plugin_run'].run_id}")
        
        # Run comparison
        engine = ComparisonEngine()
        comparison = engine.compare_runs(
            latest_pair['vcs_run'].run_id,
            latest_pair['plugin_run'].run_id
        )
        report = engine.generate_comparison_report(comparison)
        
        _display_comparison_summary(comparison, report)
        _display_issue_comparison(comparison)
    else:
        console.print("❌ No comparison pairs found. Run both VCS and plugin analysis first.")


if __name__ == '__main__':
    compare()
