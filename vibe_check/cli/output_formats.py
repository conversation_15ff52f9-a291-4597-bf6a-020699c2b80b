"""
File: vibe_check/cli/output_formats.py
Purpose: Output formatters for VCS analysis results
Related Files: vibe_check/cli/standalone.py, vibe_check/core/vcs/models.py
Dependencies: json, yaml, typing
"""

import json
from datetime import datetime

from vibe_check.core.vcs.models import AnalysisR<PERSON>ult, AnalysisIssue
from vibe_check.core.logging import get_logger
from typing import Any, Dict, List

logger = get_logger(__name__)

# Try to import yaml, but make it optional
try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False
    logger.warning("PyYAML not available - YAML output format disabled")


class OutputFormatter:
    """Base class for output formatters."""
    
    def format(self, result: AnalysisResult) -> str:
        """Format analysis result."""
        raise NotImplementedError


class TextFormatter(OutputFormatter):
    """Plain text formatter."""
    
    def format(self, result: AnalysisResult) -> str:
        """Format as plain text."""
        lines = []
        
        if result.issues:
            lines.append(f"Found {len(result.issues)} issues:")
            lines.append("")
            
            for issue in result.issues:
                lines.append(f"{issue.rule_id}: Line {issue.line}, Column {issue.column}")
                lines.append(f"  {issue.message}")
                if issue.fix_suggestion:
                    lines.append(f"  Suggestion: {issue.fix_suggestion}")
                lines.append("")
        else:
            lines.append("No issues found!")
        
        return "\n".join(lines)


class JSONFormatter(OutputFormatter):
    """JSON formatter."""
    
    def format(self, result: AnalysisResult) -> str:
        """Format as JSON."""
        data = {
            "summary": {
                "total_issues": len(result.issues),
                "execution_time": result.execution_time,
                "success": result.success,
                "timestamp": datetime.now().isoformat(),
                "rules_executed": result.rules_executed
            },
            "issues": [self._format_issue(issue) for issue in result.issues],
            "target": str(result.target.path),
            "metadata": result.metadata
        }
        
        if result.error_message:
            data["summary"]["error"] = result.error_message
        
        return json.dumps(data, indent=2, ensure_ascii=False)
    
    def _format_issue(self, issue: AnalysisIssue) -> Dict[str, Any]:
        """Format a single issue for JSON output."""
        return {
            "rule_id": issue.rule_id,
            "category": issue.category.value,
            "severity": issue.severity.value,
            "line": issue.line,
            "column": issue.column,
            "message": issue.message,
            "fix_suggestion": issue.fix_suggestion,
            "auto_fixable": issue.auto_fixable,
            "source": issue.source,
            "metadata": issue.metadata
        }


class YAMLFormatter(OutputFormatter):
    """YAML formatter."""
    
    def format(self, result: AnalysisResult) -> str:
        """Format as YAML."""
        if not YAML_AVAILABLE:
            return "Error: PyYAML not installed. Install with: pip install pyyaml"
        
        data = {
            "summary": {
                "total_issues": len(result.issues),
                "execution_time": result.execution_time,
                "success": result.success,
                "timestamp": datetime.now().isoformat(),
                "rules_executed": result.rules_executed
            },
            "issues": [self._format_issue(issue) for issue in result.issues],
            "target": str(result.target.path),
            "metadata": result.metadata
        }
        
        if result.error_message:
            data["summary"]["error"] = result.error_message
        
        return yaml.dump(data, default_flow_style=False, allow_unicode=True)
    
    def _format_issue(self, issue: AnalysisIssue) -> Dict[str, Any]:
        """Format a single issue for YAML output."""
        return {
            "rule_id": issue.rule_id,
            "category": issue.category.value,
            "severity": issue.severity.value,
            "line": issue.line,
            "column": issue.column,
            "message": issue.message,
            "fix_suggestion": issue.fix_suggestion,
            "auto_fixable": issue.auto_fixable,
            "source": issue.source,
            "metadata": issue.metadata
        }


class SARIFFormatter(OutputFormatter):
    """SARIF (Static Analysis Results Interchange Format) formatter."""
    
    def format(self, result: AnalysisResult) -> str:
        """Format as SARIF."""
        sarif_data = {
            "$schema": "https://raw.githubusercontent.com/oasis-tcs/sarif-spec/master/Schemata/sarif-schema-2.1.0.json",
            "version": "2.1.0",
            "runs": [
                {
                    "tool": {
                        "driver": {
                            "name": "Vibe Check VCS",
                            "version": "1.0.0",
                            "informationUri": "https://github.com/ptzajac/vibe_check",
                            "rules": self._get_sarif_rules(result)
                        }
                    },
                    "results": [self._format_sarif_result(issue) for issue in result.issues],
                    "invocations": [
                        {
                            "executionSuccessful": result.success,
                            "startTimeUtc": datetime.now().isoformat() + "Z",
                            "endTimeUtc": datetime.now().isoformat() + "Z"
                        }
                    ]
                }
            ]
        }
        
        return json.dumps(sarif_data, indent=2, ensure_ascii=False)
    
    def _get_sarif_rules(self, result: AnalysisResult) -> List[Dict[str, Any]]:
        """Get SARIF rule definitions."""
        rules = {}
        for issue in result.issues:
            if issue.rule_id not in rules:
                rules[issue.rule_id] = {
                    "id": issue.rule_id,
                    "name": issue.rule_id,
                    "shortDescription": {
                        "text": issue.message.split('.')[0] + '.'
                    },
                    "fullDescription": {
                        "text": issue.message
                    },
                    "defaultConfiguration": {
                        "level": self._severity_to_sarif_level(issue.severity.value)
                    },
                    "properties": {
                        "category": issue.category.value,
                        "source": issue.source
                    }
                }
        
        return list(rules.values())
    
    def _format_sarif_result(self, issue: AnalysisIssue) -> Dict[str, Any]:
        """Format a single issue for SARIF output."""
        return {
            "ruleId": issue.rule_id,
            "level": self._severity_to_sarif_level(issue.severity.value),
            "message": {
                "text": issue.message
            },
            "locations": [
                {
                    "physicalLocation": {
                        "artifactLocation": {
                            "uri": str(issue.metadata.get("file_path", "unknown"))
                        },
                        "region": {
                            "startLine": issue.line,
                            "startColumn": issue.column + 1  # SARIF uses 1-based columns
                        }
                    }
                }
            ],
            "fixes": [
                {
                    "description": {
                        "text": issue.fix_suggestion
                    }
                }
            ] if issue.fix_suggestion else []
        }
    
    def _severity_to_sarif_level(self, severity: str) -> str:
        """Convert VCS severity to SARIF level."""
        mapping = {
            "error": "error",
            "warning": "warning", 
            "info": "note",
            "hint": "note"
        }
        return mapping.get(severity.lower(), "warning")


class GitHubFormatter(OutputFormatter):
    """GitHub Actions annotation formatter."""
    
    def format(self, result: AnalysisResult) -> str:
        """Format as GitHub Actions annotations."""
        lines = []
        
        for issue in result.issues:
            level = self._severity_to_github_level(issue.severity.value)
            file_path = issue.metadata.get("file_path", "unknown")
            
            annotation = f"::{level} file={file_path},line={issue.line},col={issue.column}::{issue.rule_id}: {issue.message}"
            lines.append(annotation)
        
        if not lines:
            lines.append("::notice::No issues found!")
        
        return "\n".join(lines)
    
    def _severity_to_github_level(self, severity: str) -> str:
        """Convert VCS severity to GitHub annotation level."""
        mapping = {
            "error": "error",
            "warning": "warning",
            "info": "notice",
            "hint": "notice"
        }
        return mapping.get(severity.lower(), "warning")


# Registry of available formatters
FORMATTERS = {
    "text": TextFormatter(),
    "json": JSONFormatter(),
    "yaml": YAMLFormatter(),
    "sarif": SARIFFormatter(),
    "github": GitHubFormatter()
}


def format_result(result: AnalysisResult, format_type: str) -> str:
    """Format analysis result using specified formatter."""
    formatter = FORMATTERS.get(format_type)
    if not formatter:
        available = ", ".join(FORMATTERS.keys())
        raise ValueError(f"Unknown format '{format_type}'. Available: {available}")
    
    return formatter.format(result)


def get_available_formats() -> List[str]:
    """Get list of available output formats."""
    return list(FORMATTERS.keys())
