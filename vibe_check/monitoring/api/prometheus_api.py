"""
Prometheus-Compatible REST API
==============================

This module provides a Prometheus-compatible HTTP API with instant and range queries
for the Vibe Check monitoring platform.

File: vibe_check/monitoring/api/prometheus_api.py
Purpose: Prometheus-compatible REST API with instant and range queries
Related Files: promql_engine.py, time_series_engine.py
Dependencies: asyncio, aiohttp, json, time
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from urllib.parse import parse_qs

try:
    import aiohttp
    from aiohttp import web
    AIOHTTP_AVAILABLE = True
except ImportError:
    AIOHTTP_AVAILABLE = False
    aiohttp = None
    web = None

try:
    # Try relative imports first
    from ..query.promql_engine import PromQLEngine
    from ..storage.time_series_engine import TimeSeriesStorageEngine
except ImportError:
    # Fallback to absolute imports for testing
    import sys
    from pathlib import Path
    
    # Add the project root to Python path
    project_root = Path(__file__).parent.parent.parent.parent
    sys.path.insert(0, str(project_root))
    
    from vibe_check.monitoring.query.promql_engine import PromQLEngine
    from vibe_check.monitoring.storage.time_series_engine import TimeSeriesStorageEngine

logger = logging.getLogger(__name__)


@dataclass
class PrometheusResponse:
    """Prometheus API response structure"""
    status: str  # "success" or "error"
    data: Optional[Dict[str, Any]] = None
    error_type: Optional[str] = None
    error: Optional[str] = None
    warnings: Optional[List[str]] = None


class PrometheusAPI:
    """Prometheus-compatible REST API server"""
    
    def __init__(self, promql_engine: PromQLEngine, host: str = "localhost", port: int = 9090):
        if not AIOHTTP_AVAILABLE:
            raise ImportError("aiohttp is required for Prometheus API")
        
        self.promql_engine = promql_engine
        self.host = host
        self.port = port
        
        # Server components
        self.app: Optional[web.Application] = None
        self.runner: Optional[web.AppRunner] = None
        self.site: Optional[web.TCPSite] = None
        
        # State
        self.running = False
        
        logger.info(f"PrometheusAPI initialized on {host}:{port}")
    
    async def start(self):
        """Start the Prometheus API server"""
        if self.running:
            logger.warning("Prometheus API already running")
            return
        
        # Create aiohttp application
        self.app = web.Application()
        
        # Setup routes
        self._setup_routes()
        
        # Setup middleware
        self._setup_middleware()
        
        # Start server
        self.runner = web.AppRunner(self.app)
        await self.runner.setup()
        
        self.site = web.TCPSite(self.runner, self.host, self.port)
        await self.site.start()
        
        self.running = True
        
        logger.info(f"Prometheus API started on http://{self.host}:{self.port}")
    
    async def stop(self):
        """Stop the Prometheus API server"""
        self.running = False
        
        if self.site:
            await self.site.stop()
        
        if self.runner:
            await self.runner.cleanup()
        
        logger.info("Prometheus API stopped")
    
    def _setup_routes(self):
        """Setup Prometheus API routes"""
        # Query API endpoints
        self.app.router.add_get('/api/v1/query', self._handle_instant_query)
        self.app.router.add_post('/api/v1/query', self._handle_instant_query)
        self.app.router.add_get('/api/v1/query_range', self._handle_range_query)
        self.app.router.add_post('/api/v1/query_range', self._handle_range_query)
        
        # Metadata endpoints
        self.app.router.add_get('/api/v1/label/__name__/values', self._handle_metric_names)
        self.app.router.add_get('/api/v1/labels', self._handle_label_names)
        self.app.router.add_get('/api/v1/label/{label_name}/values', self._handle_label_values)
        
        # Series endpoint
        self.app.router.add_get('/api/v1/series', self._handle_series)
        self.app.router.add_post('/api/v1/series', self._handle_series)
        
        # Status endpoints
        self.app.router.add_get('/api/v1/status/config', self._handle_config)
        self.app.router.add_get('/api/v1/status/flags', self._handle_flags)
        self.app.router.add_get('/api/v1/status/buildinfo', self._handle_buildinfo)
        
        # Health check
        self.app.router.add_get('/-/healthy', self._handle_health)
        self.app.router.add_get('/-/ready', self._handle_ready)
        
        # Root endpoint
        self.app.router.add_get('/', self._handle_root)
    
    def _setup_middleware(self):
        """Setup middleware"""
        # CORS middleware
        async def cors_middleware(request, handler):
            response = await handler(request)
            response.headers['Access-Control-Allow-Origin'] = '*'
            response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
            response.headers['Access-Control-Allow-Headers'] = 'Content-Type'
            return response
        
        # Error handling middleware
        async def error_middleware(request, handler):
            try:
                return await handler(request)
            except Exception as e:
                logger.error(f"API error: {str(e)}")
                error_response = PrometheusResponse(
                    status="error",
                    error_type="internal",
                    error=str(e)
                )
                return web.json_response(
                    self._response_to_dict(error_response),
                    status=500
                )
        
        self.app.middlewares.append(cors_middleware)
        self.app.middlewares.append(error_middleware)
    
    async def _handle_instant_query(self, request):
        """Handle instant query endpoint"""
        # Get query parameters
        if request.method == 'POST':
            data = await request.post()
            query = data.get('query', '')
            time_param = data.get('time')
            timeout = data.get('timeout', '30s')
        else:
            query = request.query.get('query', '')
            time_param = request.query.get('time')
            timeout = request.query.get('timeout', '30s')
        
        if not query:
            error_response = PrometheusResponse(
                status="error",
                error_type="bad_data",
                error="query parameter is required"
            )
            return web.json_response(
                self._response_to_dict(error_response),
                status=400
            )
        
        # Parse time parameter
        query_time = None
        if time_param:
            try:
                query_time = float(time_param)
            except ValueError:
                error_response = PrometheusResponse(
                    status="error",
                    error_type="bad_data",
                    error="invalid time parameter"
                )
                return web.json_response(
                    self._response_to_dict(error_response),
                    status=400
                )
        
        try:
            # Execute query
            results = await self.promql_engine.execute_query(query, timestamp=query_time)
            
            # Format response
            response_data = {
                "resultType": "vector",
                "result": [result.to_prometheus_format() for result in results]
            }
            
            success_response = PrometheusResponse(
                status="success",
                data=response_data
            )
            
            return web.json_response(self._response_to_dict(success_response))
        
        except Exception as e:
            logger.error(f"Query execution error: {str(e)}")
            error_response = PrometheusResponse(
                status="error",
                error_type="execution",
                error=str(e)
            )
            return web.json_response(
                self._response_to_dict(error_response),
                status=422
            )
    
    async def _handle_range_query(self, request):
        """Handle range query endpoint"""
        # Get query parameters
        if request.method == 'POST':
            data = await request.post()
            query = data.get('query', '')
            start = data.get('start')
            end = data.get('end')
            step = data.get('step', '60s')
            timeout = data.get('timeout', '30s')
        else:
            query = request.query.get('query', '')
            start = request.query.get('start')
            end = request.query.get('end')
            step = request.query.get('step', '60s')
            timeout = request.query.get('timeout', '30s')
        
        if not query:
            error_response = PrometheusResponse(
                status="error",
                error_type="bad_data",
                error="query parameter is required"
            )
            return web.json_response(
                self._response_to_dict(error_response),
                status=400
            )
        
        if not start or not end:
            error_response = PrometheusResponse(
                status="error",
                error_type="bad_data",
                error="start and end parameters are required"
            )
            return web.json_response(
                self._response_to_dict(error_response),
                status=400
            )
        
        try:
            # Parse time parameters
            start_time = float(start)
            end_time = float(end)
            
            # Parse step (simplified - assume seconds)
            step_seconds = 60.0  # Default 1 minute
            if step.endswith('s'):
                step_seconds = float(step[:-1])
            elif step.endswith('m'):
                step_seconds = float(step[:-1]) * 60
            elif step.endswith('h'):
                step_seconds = float(step[:-1]) * 3600
            
        except ValueError:
            error_response = PrometheusResponse(
                status="error",
                error_type="bad_data",
                error="invalid time parameters"
            )
            return web.json_response(
                self._response_to_dict(error_response),
                status=400
            )
        
        try:
            # Execute range query
            results = await self.promql_engine.execute_query(
                query,
                start_time=start_time,
                end_time=end_time
            )
            
            # Format response
            response_data = {
                "resultType": "matrix",
                "result": [result.to_prometheus_format() for result in results]
            }
            
            success_response = PrometheusResponse(
                status="success",
                data=response_data
            )
            
            return web.json_response(self._response_to_dict(success_response))
        
        except Exception as e:
            logger.error(f"Range query execution error: {str(e)}")
            error_response = PrometheusResponse(
                status="error",
                error_type="execution",
                error=str(e)
            )
            return web.json_response(
                self._response_to_dict(error_response),
                status=422
            )
    
    async def _handle_metric_names(self, request):
        """Handle metric names endpoint"""
        # Simplified implementation
        metric_names = ["cpu_usage_percent", "memory_usage_bytes", "http_requests_total"]
        
        success_response = PrometheusResponse(
            status="success",
            data=metric_names
        )
        
        return web.json_response(self._response_to_dict(success_response))
    
    async def _handle_label_names(self, request):
        """Handle label names endpoint"""
        # Simplified implementation
        label_names = ["__name__", "instance", "job", "method", "status"]
        
        success_response = PrometheusResponse(
            status="success",
            data=label_names
        )
        
        return web.json_response(self._response_to_dict(success_response))
    
    async def _handle_label_values(self, request):
        """Handle label values endpoint"""
        label_name = request.match_info['label_name']
        
        # Simplified implementation
        label_values = {
            "instance": ["server1", "server2", "server3"],
            "job": ["node", "prometheus", "grafana"],
            "method": ["GET", "POST", "PUT", "DELETE"],
            "status": ["200", "404", "500"]
        }
        
        values = label_values.get(label_name, [])
        
        success_response = PrometheusResponse(
            status="success",
            data=values
        )
        
        return web.json_response(self._response_to_dict(success_response))
    
    async def _handle_series(self, request):
        """Handle series endpoint"""
        # Simplified implementation
        series_data = [
            {"__name__": "cpu_usage_percent", "instance": "server1", "job": "node"},
            {"__name__": "memory_usage_bytes", "instance": "server1", "job": "node"},
            {"__name__": "http_requests_total", "method": "GET", "status": "200"}
        ]
        
        success_response = PrometheusResponse(
            status="success",
            data=series_data
        )
        
        return web.json_response(self._response_to_dict(success_response))
    
    async def _handle_config(self, request):
        """Handle config endpoint"""
        config_data = {
            "yaml": "# Vibe Check Monitoring Configuration\nglobal:\n  scrape_interval: 15s\n"
        }
        
        success_response = PrometheusResponse(
            status="success",
            data=config_data
        )
        
        return web.json_response(self._response_to_dict(success_response))
    
    async def _handle_flags(self, request):
        """Handle flags endpoint"""
        flags_data = {
            "storage.tsdb.path": "/tmp/vibe_check_tsdb",
            "web.listen-address": f"{self.host}:{self.port}",
            "log.level": "info"
        }
        
        success_response = PrometheusResponse(
            status="success",
            data=flags_data
        )
        
        return web.json_response(self._response_to_dict(success_response))
    
    async def _handle_buildinfo(self, request):
        """Handle build info endpoint"""
        buildinfo_data = {
            "version": "vibe-check-1.0.0",
            "revision": "main",
            "branch": "main",
            "buildUser": "vibe-check",
            "buildDate": "2024-01-01T00:00:00Z",
            "goVersion": "python3.11"
        }
        
        success_response = PrometheusResponse(
            status="success",
            data=buildinfo_data
        )
        
        return web.json_response(self._response_to_dict(success_response))
    
    async def _handle_health(self, request):
        """Handle health check endpoint"""
        return web.Response(text="Prometheus is Healthy.\n")
    
    async def _handle_ready(self, request):
        """Handle readiness check endpoint"""
        return web.Response(text="Prometheus is Ready.\n")
    
    async def _handle_root(self, request):
        """Handle root endpoint"""
        html_content = """
<!DOCTYPE html>
<html>
<head>
    <title>Vibe Check Prometheus API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { background: #e6522c; color: white; padding: 20px; border-radius: 5px; }
        .endpoints { margin: 20px 0; }
        .endpoint { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔥 Vibe Check Prometheus API</h1>
        <p>Prometheus-compatible monitoring API</p>
    </div>
    
    <div class="endpoints">
        <h3>📡 API Endpoints</h3>
        <div class="endpoint"><strong>GET/POST /api/v1/query</strong> - Instant queries</div>
        <div class="endpoint"><strong>GET/POST /api/v1/query_range</strong> - Range queries</div>
        <div class="endpoint"><strong>GET /api/v1/labels</strong> - Label names</div>
        <div class="endpoint"><strong>GET /api/v1/series</strong> - Series metadata</div>
        <div class="endpoint"><strong>GET /-/healthy</strong> - Health check</div>
        <div class="endpoint"><strong>GET /-/ready</strong> - Readiness check</div>
    </div>
    
    <div class="status">
        <h3>📊 Status</h3>
        <p><strong>Server:</strong> Running on {self.host}:{self.port}</p>
        <p><strong>API Version:</strong> v1</p>
        <p><strong>Compatibility:</strong> Prometheus API</p>
    </div>
</body>
</html>
"""
        return web.Response(text=html_content, content_type='text/html')
    
    def _response_to_dict(self, response: PrometheusResponse) -> Dict[str, Any]:
        """Convert PrometheusResponse to dictionary"""
        result = {"status": response.status}
        
        if response.data is not None:
            result["data"] = response.data
        
        if response.error_type:
            result["errorType"] = response.error_type
        
        if response.error:
            result["error"] = response.error
        
        if response.warnings:
            result["warnings"] = response.warnings
        
        return result
    
    def get_stats(self) -> Dict[str, Any]:
        """Get API server statistics"""
        return {
            'running': self.running,
            'host': self.host,
            'port': self.port,
            'endpoints': 12,  # Number of endpoints
            'prometheus_compatible': True
        }
