"""
User Experience Optimization Module
===================================

This module provides user experience optimization capabilities for the
Vibe Check monitoring platform with responsive design and accessibility.

Components:
- UXOptimizer: Main UX optimization system
- ResponsiveBreakpoints: Responsive design breakpoints
- AccessibilityConfig: Accessibility configuration
- PerformanceConfig: Performance optimization settings
- UXMetrics: User experience metrics tracking
"""

from .ux_optimizer import (
    UXOptimizer,
    ResponsiveBreakpoints,
    AccessibilityConfig,
    PerformanceConfig,
    UXMetrics,
    DeviceType,
    AccessibilityLevel,
    ThemeMode
)

__all__ = [
    'UXOptimizer',
    'ResponsiveBreakpoints',
    'AccessibilityConfig',
    'PerformanceConfig',
    'UXMetrics',
    'DeviceType',
    'AccessibilityLevel',
    'ThemeMode'
]
