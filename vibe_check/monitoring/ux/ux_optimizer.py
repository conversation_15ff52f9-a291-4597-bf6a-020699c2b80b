"""
User Experience Optimization System
===================================

This module provides user experience optimization with responsive design,
accessibility features, and performance enhancements for the monitoring platform.

File: vibe_check/monitoring/ux/ux_optimizer.py
Purpose: UX optimization with responsive design and accessibility
Related Files: web_interface.py, dashboard_components.py
Dependencies: asyncio, json, time
"""

import asyncio
import json
import logging
import time
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Callable
from enum import Enum
from pathlib import Path

logger = logging.getLogger(__name__)


class DeviceType(Enum):
    """Device types for responsive design"""
    MOBILE = "mobile"
    TABLET = "tablet"
    DESKTOP = "desktop"
    LARGE_SCREEN = "large_screen"


class AccessibilityLevel(Enum):
    """Accessibility compliance levels"""
    AA = "AA"
    AAA = "AAA"
    BASIC = "basic"


class ThemeMode(Enum):
    """Theme modes"""
    LIGHT = "light"
    DARK = "dark"
    AUTO = "auto"
    HIGH_CONTRAST = "high_contrast"


@dataclass
class ResponsiveBreakpoints:
    """Responsive design breakpoints"""
    mobile_max: int = 768
    tablet_max: int = 1024
    desktop_max: int = 1440
    large_screen_min: int = 1441


@dataclass
class AccessibilityConfig:
    """Accessibility configuration"""
    level: AccessibilityLevel = AccessibilityLevel.AA
    enable_screen_reader: bool = True
    enable_keyboard_navigation: bool = True
    enable_high_contrast: bool = True
    enable_focus_indicators: bool = True
    enable_alt_text: bool = True
    font_size_scaling: float = 1.0
    animation_reduced: bool = False


@dataclass
class PerformanceConfig:
    """Performance optimization configuration"""
    enable_lazy_loading: bool = True
    enable_data_compression: bool = True
    enable_caching: bool = True
    max_chart_points: int = 1000
    update_throttle_ms: int = 100
    enable_virtualization: bool = True
    preload_critical_resources: bool = True


@dataclass
class UXMetrics:
    """User experience metrics"""
    page_load_time: float = 0.0
    first_contentful_paint: float = 0.0
    largest_contentful_paint: float = 0.0
    cumulative_layout_shift: float = 0.0
    first_input_delay: float = 0.0
    interaction_to_next_paint: float = 0.0
    
    # Custom metrics
    dashboard_render_time: float = 0.0
    chart_update_time: float = 0.0
    websocket_latency: float = 0.0
    api_response_time: float = 0.0


class UXOptimizer:
    """User experience optimization system"""
    
    def __init__(self):
        self.breakpoints = ResponsiveBreakpoints()
        self.accessibility_config = AccessibilityConfig()
        self.performance_config = PerformanceConfig()
        
        # UX state
        self.current_theme = ThemeMode.LIGHT
        self.current_device = DeviceType.DESKTOP
        self.user_preferences: Dict[str, Any] = {}
        
        # Metrics tracking
        self.metrics = UXMetrics()
        self.metrics_history: List[UXMetrics] = []
        
        # Event callbacks
        self.on_theme_change: Optional[Callable[[ThemeMode], None]] = None
        self.on_device_change: Optional[Callable[[DeviceType], None]] = None
        self.on_accessibility_change: Optional[Callable[[AccessibilityConfig], None]] = None
        
        logger.info("UXOptimizer initialized")
    
    def detect_device_type(self, viewport_width: int) -> DeviceType:
        """Detect device type based on viewport width"""
        if viewport_width <= self.breakpoints.mobile_max:
            return DeviceType.MOBILE
        elif viewport_width <= self.breakpoints.tablet_max:
            return DeviceType.TABLET
        elif viewport_width <= self.breakpoints.desktop_max:
            return DeviceType.DESKTOP
        else:
            return DeviceType.LARGE_SCREEN
    
    def set_device_type(self, device_type: DeviceType):
        """Set current device type"""
        if self.current_device != device_type:
            self.current_device = device_type
            if self.on_device_change:
                self.on_device_change(device_type)
            logger.info(f"Device type changed to: {device_type.value}")
    
    def set_theme(self, theme: ThemeMode):
        """Set current theme"""
        if self.current_theme != theme:
            self.current_theme = theme
            if self.on_theme_change:
                self.on_theme_change(theme)
            logger.info(f"Theme changed to: {theme.value}")
    
    def update_accessibility_config(self, config: AccessibilityConfig):
        """Update accessibility configuration"""
        self.accessibility_config = config
        if self.on_accessibility_change:
            self.on_accessibility_change(config)
        logger.info(f"Accessibility config updated: {config.level.value}")
    
    def get_responsive_css(self) -> str:
        """Generate responsive CSS based on current configuration"""
        css = f"""
/* Responsive Design CSS */
:root {{
    --mobile-max: {self.breakpoints.mobile_max}px;
    --tablet-max: {self.breakpoints.tablet_max}px;
    --desktop-max: {self.breakpoints.desktop_max}px;
    --font-scale: {self.accessibility_config.font_size_scaling};
}}

/* Base styles */
* {{
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}}

body {{
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: calc(16px * var(--font-scale));
    line-height: 1.6;
    transition: all 0.3s ease;
}}

/* Mobile styles */
@media (max-width: {self.breakpoints.mobile_max}px) {{
    .dashboard-grid {{
        grid-template-columns: 1fr !important;
        gap: 10px;
    }}
    
    .chart-container {{
        padding: 10px;
        margin-bottom: 15px;
    }}
    
    .header {{
        padding: 10px;
        font-size: 1.2em;
    }}
    
    .navigation {{
        flex-direction: column;
    }}
}}

/* Tablet styles */
@media (min-width: {self.breakpoints.mobile_max + 1}px) and (max-width: {self.breakpoints.tablet_max}px) {{
    .dashboard-grid {{
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 15px;
    }}
    
    .chart-container {{
        padding: 12px;
    }}
}}

/* Desktop styles */
@media (min-width: {self.breakpoints.tablet_max + 1}px) {{
    .dashboard-grid {{
        gap: 20px;
    }}
    
    .chart-container {{
        padding: 15px;
    }}
}}

/* Large screen styles */
@media (min-width: {self.breakpoints.large_screen_min}px) {{
    .container {{
        max-width: 1400px;
        margin: 0 auto;
    }}
}}
"""
        
        # Add accessibility styles
        if self.accessibility_config.enable_focus_indicators:
            css += """
/* Focus indicators */
*:focus {{
    outline: 3px solid #4A90E2;
    outline-offset: 2px;
}}

button:focus, a:focus {{
    outline: 3px solid #4A90E2;
    outline-offset: 2px;
}}
"""
        
        # Add high contrast styles
        if self.accessibility_config.enable_high_contrast:
            css += """
/* High contrast mode */
@media (prefers-contrast: high) {{
    body {{
        background: #000000;
        color: #ffffff;
    }}
    
    .chart-container {{
        background: #000000;
        border: 2px solid #ffffff;
        color: #ffffff;
    }}
    
    .header {{
        background: #000000;
        color: #ffffff;
        border: 2px solid #ffffff;
    }}
}}
"""
        
        # Add reduced motion styles
        if self.accessibility_config.animation_reduced:
            css += """
/* Reduced motion */
@media (prefers-reduced-motion: reduce) {{
    *, *::before, *::after {{
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }}
}}
"""
        
        return css
    
    def get_theme_css(self) -> str:
        """Generate theme-specific CSS"""
        if self.current_theme == ThemeMode.DARK:
            return """
/* Dark theme */
body {
    background-color: #1a1a1a;
    color: #e0e0e0;
}

.header {
    background-color: #2d2d2d;
    color: #ffffff;
}

.chart-container {
    background-color: #2d2d2d;
    border-color: #404040;
    color: #e0e0e0;
}

.dashboard-grid {
    background-color: #1a1a1a;
}

.status {
    background-color: #2d4a2d;
    color: #90ee90;
}
"""
        elif self.current_theme == ThemeMode.HIGH_CONTRAST:
            return """
/* High contrast theme */
body {
    background-color: #000000;
    color: #ffffff;
}

.header {
    background-color: #000000;
    color: #ffffff;
    border: 3px solid #ffffff;
}

.chart-container {
    background-color: #000000;
    border: 3px solid #ffffff;
    color: #ffffff;
}

.status {
    background-color: #000000;
    color: #ffff00;
    border: 2px solid #ffff00;
}
"""
        else:  # Light theme (default)
            return """
/* Light theme */
body {
    background-color: #ffffff;
    color: #333333;
}

.header {
    background-color: #34495e;
    color: #ffffff;
}

.chart-container {
    background-color: #ffffff;
    border-color: #dddddd;
    color: #333333;
}

.status {
    background-color: #e8f5e8;
    color: #2d5a2d;
}
"""
    
    def get_accessibility_attributes(self) -> Dict[str, str]:
        """Get accessibility attributes for HTML elements"""
        attributes = {}
        
        if self.accessibility_config.enable_screen_reader:
            attributes.update({
                'role': 'main',
                'aria-label': 'Monitoring Dashboard',
                'aria-live': 'polite'
            })
        
        if self.accessibility_config.enable_keyboard_navigation:
            attributes.update({
                'tabindex': '0',
                'aria-describedby': 'dashboard-description'
            })
        
        return attributes
    
    def optimize_chart_data(self, data_points: List[Any]) -> List[Any]:
        """Optimize chart data based on performance configuration"""
        if not self.performance_config.enable_data_compression:
            return data_points
        
        max_points = self.performance_config.max_chart_points
        
        if len(data_points) <= max_points:
            return data_points
        
        # Simple downsampling - take every nth point
        step = len(data_points) // max_points
        return data_points[::step]
    
    def should_lazy_load(self, component_id: str) -> bool:
        """Determine if component should be lazy loaded"""
        if not self.performance_config.enable_lazy_loading:
            return False
        
        # Lazy load components that are not immediately visible
        # This is a simplified implementation
        return self.current_device in [DeviceType.MOBILE, DeviceType.TABLET]
    
    def get_performance_hints(self) -> Dict[str, Any]:
        """Get performance optimization hints"""
        hints = {
            'preload_critical': self.performance_config.preload_critical_resources,
            'enable_caching': self.performance_config.enable_caching,
            'lazy_loading': self.performance_config.enable_lazy_loading,
            'virtualization': self.performance_config.enable_virtualization,
            'update_throttle': self.performance_config.update_throttle_ms
        }
        
        # Device-specific hints
        if self.current_device == DeviceType.MOBILE:
            hints.update({
                'reduce_animations': True,
                'compress_images': True,
                'minimize_requests': True
            })
        
        return hints
    
    def record_metric(self, metric_name: str, value: float):
        """Record a UX metric"""
        if hasattr(self.metrics, metric_name):
            setattr(self.metrics, metric_name, value)
            logger.debug(f"UX metric recorded: {metric_name} = {value}")
    
    def get_ux_score(self) -> float:
        """Calculate overall UX score (0-100)"""
        scores = []
        
        # Performance score (40% weight)
        perf_score = 0
        if self.metrics.page_load_time > 0:
            # Good: <2s, Poor: >4s
            load_score = max(0, 100 - (self.metrics.page_load_time - 2) * 25)
            perf_score += load_score * 0.4
        
        if self.metrics.first_contentful_paint > 0:
            # Good: <1.8s, Poor: >3s
            fcp_score = max(0, 100 - (self.metrics.first_contentful_paint - 1.8) * 50)
            perf_score += fcp_score * 0.3
        
        if self.metrics.cumulative_layout_shift >= 0:
            # Good: <0.1, Poor: >0.25
            cls_score = max(0, 100 - self.metrics.cumulative_layout_shift * 400)
            perf_score += cls_score * 0.3
        
        scores.append(perf_score)
        
        # Accessibility score (30% weight)
        acc_score = 50  # Base score
        if self.accessibility_config.enable_screen_reader:
            acc_score += 15
        if self.accessibility_config.enable_keyboard_navigation:
            acc_score += 15
        if self.accessibility_config.enable_focus_indicators:
            acc_score += 10
        if self.accessibility_config.enable_high_contrast:
            acc_score += 10
        
        scores.append(acc_score)
        
        # Responsiveness score (30% weight)
        resp_score = 80  # Base score for responsive design
        if self.current_device == DeviceType.MOBILE:
            # Mobile-specific optimizations
            if self.performance_config.enable_lazy_loading:
                resp_score += 10
            if self.accessibility_config.font_size_scaling >= 1.0:
                resp_score += 10
        
        scores.append(resp_score)
        
        # Calculate weighted average
        weights = [0.4, 0.3, 0.3]
        return sum(score * weight for score, weight in zip(scores, weights))
    
    def get_optimization_recommendations(self) -> List[str]:
        """Get UX optimization recommendations"""
        recommendations = []
        
        # Performance recommendations
        if self.metrics.page_load_time > 3.0:
            recommendations.append("Consider enabling data compression and lazy loading")
        
        if self.metrics.cumulative_layout_shift > 0.1:
            recommendations.append("Reduce layout shifts by reserving space for dynamic content")
        
        # Accessibility recommendations
        if not self.accessibility_config.enable_screen_reader:
            recommendations.append("Enable screen reader support for better accessibility")
        
        if self.accessibility_config.font_size_scaling < 1.2 and self.current_device == DeviceType.MOBILE:
            recommendations.append("Consider increasing font size for mobile devices")
        
        # Device-specific recommendations
        if self.current_device == DeviceType.MOBILE:
            recommendations.append("Optimize for touch interactions and smaller screens")
        
        return recommendations
    
    def export_ux_report(self) -> Dict[str, Any]:
        """Export comprehensive UX report"""
        return {
            'timestamp': time.time(),
            'ux_score': self.get_ux_score(),
            'metrics': {
                'page_load_time': self.metrics.page_load_time,
                'first_contentful_paint': self.metrics.first_contentful_paint,
                'largest_contentful_paint': self.metrics.largest_contentful_paint,
                'cumulative_layout_shift': self.metrics.cumulative_layout_shift,
                'first_input_delay': self.metrics.first_input_delay,
                'dashboard_render_time': self.metrics.dashboard_render_time,
                'chart_update_time': self.metrics.chart_update_time,
                'websocket_latency': self.metrics.websocket_latency,
                'api_response_time': self.metrics.api_response_time
            },
            'configuration': {
                'device_type': self.current_device.value,
                'theme': self.current_theme.value,
                'accessibility_level': self.accessibility_config.level.value,
                'font_scaling': self.accessibility_config.font_size_scaling,
                'performance_optimizations': {
                    'lazy_loading': self.performance_config.enable_lazy_loading,
                    'data_compression': self.performance_config.enable_data_compression,
                    'caching': self.performance_config.enable_caching,
                    'virtualization': self.performance_config.enable_virtualization
                }
            },
            'recommendations': self.get_optimization_recommendations(),
            'responsive_breakpoints': {
                'mobile_max': self.breakpoints.mobile_max,
                'tablet_max': self.breakpoints.tablet_max,
                'desktop_max': self.breakpoints.desktop_max,
                'large_screen_min': self.breakpoints.large_screen_min
            }
        }
