"""
Memory Usage Tracker
===================

Advanced memory usage tracking with allocation monitoring, leak detection,
and garbage collection analysis. Integrates with the profiling foundation.
"""

import gc
import sys
import time
import threading
import tracemalloc
import weakref
from typing import Dict, List, Optional, Any, Set, Tuple, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
from functools import wraps
import logging
from pathlib import Path
import json

logger = logging.getLogger(__name__)


@dataclass
class MemorySnapshot:
    """Represents a memory snapshot at a specific point in time"""
    timestamp: float
    total_memory: int
    peak_memory: int
    current_allocations: int
    gc_objects: int
    gc_collections: Dict[int, int]
    tracemalloc_stats: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'timestamp': self.timestamp,
            'total_memory': self.total_memory,
            'peak_memory': self.peak_memory,
            'current_allocations': self.current_allocations,
            'gc_objects': self.gc_objects,
            'gc_collections': self.gc_collections,
            'tracemalloc_stats': self.tracemalloc_stats
        }


@dataclass
class FunctionMemoryProfile:
    """Memory profile for a specific function"""
    function_name: str
    module_name: str
    call_count: int = 0
    total_allocated: int = 0
    peak_allocated: int = 0
    total_freed: int = 0
    net_allocated: int = 0
    avg_per_call: float = 0.0
    memory_snapshots: List[Tuple[float, int]] = field(default_factory=list)
    potential_leaks: int = 0
    
    def update_stats(self):
        """Update calculated statistics"""
        self.net_allocated = self.total_allocated - self.total_freed
        self.avg_per_call = self.total_allocated / self.call_count if self.call_count > 0 else 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'function_name': self.function_name,
            'module_name': self.module_name,
            'call_count': self.call_count,
            'total_allocated': self.total_allocated,
            'peak_allocated': self.peak_allocated,
            'total_freed': self.total_freed,
            'net_allocated': self.net_allocated,
            'avg_per_call': self.avg_per_call,
            'potential_leaks': self.potential_leaks,
            'memory_snapshots': self.memory_snapshots
        }


@dataclass
class MemoryLeak:
    """Represents a detected memory leak"""
    function_name: str
    module_name: str
    leak_size: int
    detection_time: float
    allocation_trace: Optional[str] = None
    leak_rate: float = 0.0  # bytes per second
    confidence: float = 0.0  # 0.0 to 1.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'function_name': self.function_name,
            'module_name': self.module_name,
            'leak_size': self.leak_size,
            'detection_time': self.detection_time,
            'allocation_trace': self.allocation_trace,
            'leak_rate': self.leak_rate,
            'confidence': self.confidence
        }


@dataclass
class GCStats:
    """Garbage collection statistics"""
    generation: int
    collections: int
    collected: int
    uncollectable: int
    collection_time: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'generation': self.generation,
            'collections': self.collections,
            'collected': self.collected,
            'uncollectable': self.uncollectable,
            'collection_time': self.collection_time
        }


class MemoryTracker:
    """Advanced memory usage tracker with leak detection and GC monitoring"""
    
    def __init__(self, enable_tracemalloc: bool = True, 
                 leak_detection_threshold: int = 1024*1024,  # 1MB
                 snapshot_interval: float = 1.0):
        self.enable_tracemalloc = enable_tracemalloc
        self.leak_detection_threshold = leak_detection_threshold
        self.snapshot_interval = snapshot_interval
        
        # Tracking state
        self.is_tracking = False
        self.start_time: Optional[float] = None
        
        # Memory profiles
        self.function_profiles: Dict[str, FunctionMemoryProfile] = {}
        self.memory_snapshots: List[MemorySnapshot] = []
        self.detected_leaks: List[MemoryLeak] = []
        
        # GC monitoring
        self.gc_stats_history: List[GCStats] = []
        self.initial_gc_stats: Optional[Dict[int, Tuple[int, int, int]]] = None
        
        # Instrumented functions
        self.instrumented_functions: Set[Callable] = set()
        
        # Background monitoring
        self._monitoring_thread: Optional[threading.Thread] = None
        self._stop_monitoring = threading.Event()
        
        # Weak references for leak detection
        self._tracked_objects: weakref.WeakSet = weakref.WeakSet()
        
        logger.info("MemoryTracker initialized")
    
    def start_tracking(self) -> bool:
        """Start memory tracking"""
        if self.is_tracking:
            logger.warning("Memory tracking already active")
            return False
        
        try:
            # Start tracemalloc if enabled
            if self.enable_tracemalloc and not tracemalloc.is_tracing():
                tracemalloc.start()
                logger.info("Started tracemalloc")
            
            # Record initial GC stats
            self.initial_gc_stats = {}
            for i in range(3):  # Python has 3 GC generations
                stats = gc.get_stats()[i] if i < len(gc.get_stats()) else {'collections': 0, 'collected': 0, 'uncollectable': 0}
                self.initial_gc_stats[i] = (stats['collections'], stats['collected'], stats['uncollectable'])
            
            # Clear previous data
            self.function_profiles.clear()
            self.memory_snapshots.clear()
            self.detected_leaks.clear()
            self.gc_stats_history.clear()
            
            # Start tracking
            self.is_tracking = True
            self.start_time = time.time()
            
            # Start background monitoring
            self._stop_monitoring.clear()
            self._monitoring_thread = threading.Thread(target=self._background_monitor, daemon=True)
            self._monitoring_thread.start()
            
            logger.info("Memory tracking started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start memory tracking: {e}")
            return False
    
    def stop_tracking(self) -> bool:
        """Stop memory tracking"""
        if not self.is_tracking:
            logger.warning("Memory tracking not active")
            return False
        
        try:
            # Stop background monitoring
            self._stop_monitoring.set()
            if self._monitoring_thread and self._monitoring_thread.is_alive():
                self._monitoring_thread.join(timeout=2.0)
            
            # Final snapshot
            self._take_memory_snapshot()
            
            # Detect leaks
            self._detect_memory_leaks()
            
            # Update function statistics
            for profile in self.function_profiles.values():
                profile.update_stats()
            
            self.is_tracking = False
            
            logger.info("Memory tracking stopped")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop memory tracking: {e}")
            return False
    
    def track_function(self, func: Optional[Callable] = None, *, 
                      name: Optional[str] = None, track_objects: bool = True):
        """Decorator to track function memory usage"""
        def decorator(f: Callable) -> Callable:
            func_name = name or f.__name__
            func_module = f.__module__ or "unknown"
            func_key = f"{func_module}.{func_name}"
            
            self.instrumented_functions.add(f)
            
            @wraps(f)
            def sync_wrapper(*args, **kwargs):
                return self._execute_tracked_sync(f, func_key, track_objects, *args, **kwargs)
            
            @wraps(f)
            async def async_wrapper(*args, **kwargs):
                return await self._execute_tracked_async(f, func_key, track_objects, *args, **kwargs)
            
            # Return appropriate wrapper
            import asyncio
            if asyncio.iscoroutinefunction(f):
                return async_wrapper
            else:
                return sync_wrapper
        
        if func is None:
            return decorator
        else:
            return decorator(func)
    
    def _execute_tracked_sync(self, func: Callable, func_key: str, 
                             track_objects: bool, *args, **kwargs):
        """Execute tracked sync function"""
        if not self.is_tracking:
            return func(*args, **kwargs)
        
        # Get initial memory state
        initial_memory = self._get_current_memory()
        
        # Initialize profile if needed
        if func_key not in self.function_profiles:
            parts = func_key.split('.', 1)
            self.function_profiles[func_key] = FunctionMemoryProfile(
                function_name=parts[1] if len(parts) > 1 else parts[0],
                module_name=parts[0] if len(parts) > 1 else "unknown"
            )
        
        profile = self.function_profiles[func_key]
        
        try:
            # Execute function
            result = func(*args, **kwargs)
            
            # Track objects if enabled
            if track_objects and hasattr(result, '__dict__'):
                self._tracked_objects.add(result)
            
            # Get final memory state
            final_memory = self._get_current_memory()
            
            # Update profile
            memory_delta = final_memory - initial_memory
            profile.call_count += 1
            
            if memory_delta > 0:
                profile.total_allocated += memory_delta
                profile.peak_allocated = max(profile.peak_allocated, memory_delta)
            else:
                profile.total_freed += abs(memory_delta)
            
            profile.memory_snapshots.append((time.time(), memory_delta))
            
            return result
            
        except Exception as e:
            # Still update call count on exception
            profile.call_count += 1
            raise
    
    async def _execute_tracked_async(self, func: Callable, func_key: str,
                                   track_objects: bool, *args, **kwargs):
        """Execute tracked async function"""
        if not self.is_tracking:
            return await func(*args, **kwargs)
        
        # Get initial memory state
        initial_memory = self._get_current_memory()
        
        # Initialize profile if needed
        if func_key not in self.function_profiles:
            parts = func_key.split('.', 1)
            self.function_profiles[func_key] = FunctionMemoryProfile(
                function_name=parts[1] if len(parts) > 1 else parts[0],
                module_name=parts[0] if len(parts) > 1 else "unknown"
            )
        
        profile = self.function_profiles[func_key]
        
        try:
            # Execute function
            result = await func(*args, **kwargs)
            
            # Track objects if enabled
            if track_objects and hasattr(result, '__dict__'):
                self._tracked_objects.add(result)
            
            # Get final memory state
            final_memory = self._get_current_memory()
            
            # Update profile
            memory_delta = final_memory - initial_memory
            profile.call_count += 1
            
            if memory_delta > 0:
                profile.total_allocated += memory_delta
                profile.peak_allocated = max(profile.peak_allocated, memory_delta)
            else:
                profile.total_freed += abs(memory_delta)
            
            profile.memory_snapshots.append((time.time(), memory_delta))
            
            return result
            
        except Exception as e:
            # Still update call count on exception
            profile.call_count += 1
            raise
    
    def _get_current_memory(self) -> int:
        """Get current memory usage"""
        try:
            if tracemalloc.is_tracing():
                current, peak = tracemalloc.get_traced_memory()
                return current
            else:
                # Fallback to GC object count as proxy
                return len(gc.get_objects())
        except Exception:
            return 0
    
    def _background_monitor(self):
        """Background monitoring thread"""
        while not self._stop_monitoring.is_set():
            try:
                self._take_memory_snapshot()
                self._monitor_gc_stats()
                
                # Wait for next interval
                self._stop_monitoring.wait(self.snapshot_interval)
                
            except Exception as e:
                logger.error(f"Error in background monitoring: {e}")
                break
    
    def _take_memory_snapshot(self):
        """Take a memory snapshot"""
        try:
            current_memory = 0
            peak_memory = 0
            tracemalloc_stats = None
            
            if tracemalloc.is_tracing():
                current_memory, peak_memory = tracemalloc.get_traced_memory()
                
                # Get top allocations
                top_stats = tracemalloc.take_snapshot().statistics('lineno')
                tracemalloc_stats = {
                    'top_allocations': [
                        {
                            'filename': stat.traceback.format()[0] if stat.traceback.format() else 'unknown',
                            'size': stat.size,
                            'count': stat.count
                        }
                        for stat in top_stats[:5]
                    ]
                }
            
            # Get GC stats
            gc_objects = len(gc.get_objects())
            gc_collections = {}
            
            for i in range(3):
                if i < len(gc.get_stats()):
                    stats = gc.get_stats()[i]
                    gc_collections[i] = stats['collections']
            
            snapshot = MemorySnapshot(
                timestamp=time.time(),
                total_memory=current_memory,
                peak_memory=peak_memory,
                current_allocations=current_memory,
                gc_objects=gc_objects,
                gc_collections=gc_collections,
                tracemalloc_stats=tracemalloc_stats
            )
            
            self.memory_snapshots.append(snapshot)
            
            # Keep only recent snapshots (last 1000)
            if len(self.memory_snapshots) > 1000:
                self.memory_snapshots = self.memory_snapshots[-1000:]
                
        except Exception as e:
            logger.error(f"Error taking memory snapshot: {e}")
    
    def _monitor_gc_stats(self):
        """Monitor garbage collection statistics"""
        try:
            if not self.initial_gc_stats:
                return
            
            current_time = time.time()
            
            for i in range(3):
                if i < len(gc.get_stats()):
                    current_stats = gc.get_stats()[i]
                    initial = self.initial_gc_stats.get(i, (0, 0, 0))
                    
                    gc_stat = GCStats(
                        generation=i,
                        collections=current_stats['collections'] - initial[0],
                        collected=current_stats['collected'] - initial[1],
                        uncollectable=current_stats['uncollectable'] - initial[2],
                        collection_time=current_time
                    )
                    
                    self.gc_stats_history.append(gc_stat)
            
            # Keep only recent GC stats (last 1000)
            if len(self.gc_stats_history) > 1000:
                self.gc_stats_history = self.gc_stats_history[-1000:]
                
        except Exception as e:
            logger.error(f"Error monitoring GC stats: {e}")
    
    def _detect_memory_leaks(self):
        """Detect potential memory leaks"""
        try:
            current_time = time.time()
            
            for func_key, profile in self.function_profiles.items():
                # Simple leak detection based on net allocation
                if profile.net_allocated > self.leak_detection_threshold:
                    
                    # Calculate leak rate
                    time_elapsed = current_time - (self.start_time or current_time)
                    leak_rate = profile.net_allocated / max(time_elapsed, 1.0)
                    
                    # Calculate confidence based on call count and consistency
                    confidence = min(1.0, profile.call_count / 10.0)  # More calls = higher confidence
                    
                    leak = MemoryLeak(
                        function_name=profile.function_name,
                        module_name=profile.module_name,
                        leak_size=profile.net_allocated,
                        detection_time=current_time,
                        leak_rate=leak_rate,
                        confidence=confidence
                    )
                    
                    self.detected_leaks.append(leak)
                    profile.potential_leaks += 1
                    
                    logger.warning(f"Potential memory leak detected in {func_key}: {profile.net_allocated} bytes")
        
        except Exception as e:
            logger.error(f"Error detecting memory leaks: {e}")
    
    def get_memory_report(self) -> Dict[str, Any]:
        """Get comprehensive memory usage report"""
        if not self.function_profiles:
            return {'error': 'No memory tracking data available'}
        
        # Update all profiles
        for profile in self.function_profiles.values():
            profile.update_stats()
        
        # Calculate summary statistics
        total_allocated = sum(p.total_allocated for p in self.function_profiles.values())
        total_freed = sum(p.total_freed for p in self.function_profiles.values())
        net_allocated = total_allocated - total_freed
        
        # Top memory consumers
        top_consumers = sorted(
            self.function_profiles.values(),
            key=lambda p: p.total_allocated,
            reverse=True
        )[:10]
        
        # Recent snapshots
        recent_snapshots = self.memory_snapshots[-10:] if self.memory_snapshots else []
        
        return {
            'summary': {
                'total_allocated': total_allocated,
                'total_freed': total_freed,
                'net_allocated': net_allocated,
                'tracked_functions': len(self.function_profiles),
                'detected_leaks': len(self.detected_leaks),
                'snapshots_taken': len(self.memory_snapshots)
            },
            'top_consumers': [p.to_dict() for p in top_consumers],
            'detected_leaks': [leak.to_dict() for leak in self.detected_leaks],
            'recent_snapshots': [snapshot.to_dict() for snapshot in recent_snapshots],
            'gc_stats': [stat.to_dict() for stat in self.gc_stats_history[-10:]]
        }
    
    def get_function_profile(self, function_name: str) -> Optional[Dict[str, Any]]:
        """Get memory profile for specific function"""
        for func_key, profile in self.function_profiles.items():
            if function_name in func_key:
                profile.update_stats()
                return profile.to_dict()
        return None
    
    def export_report(self, filepath: Path) -> bool:
        """Export memory report to file"""
        try:
            report = self.get_memory_report()
            with open(filepath, 'w') as f:
                json.dump(report, f, indent=2)
            return True
        except Exception as e:
            logger.error(f"Failed to export memory report: {e}")
            return False
    
    def cleanup(self):
        """Cleanup resources"""
        if self.is_tracking:
            self.stop_tracking()
        
        if tracemalloc.is_tracing():
            tracemalloc.stop()
        
        logger.info("MemoryTracker cleanup completed")


# Global memory tracker instance
_global_memory_tracker: Optional[MemoryTracker] = None


def get_memory_tracker(enable_tracemalloc: bool = True) -> MemoryTracker:
    """Get or create global memory tracker"""
    global _global_memory_tracker
    
    if _global_memory_tracker is None:
        _global_memory_tracker = MemoryTracker(enable_tracemalloc=enable_tracemalloc)
    
    return _global_memory_tracker


def track_memory(func: Optional[Callable] = None, *, name: Optional[str] = None, 
                track_objects: bool = True):
    """Convenience decorator for memory tracking"""
    tracker = get_memory_tracker()
    return tracker.track_function(func, name=name, track_objects=track_objects)


def start_memory_tracking() -> bool:
    """Start global memory tracking"""
    tracker = get_memory_tracker()
    return tracker.start_tracking()


def stop_memory_tracking() -> bool:
    """Stop global memory tracking"""
    tracker = get_memory_tracker()
    return tracker.stop_tracking()


def get_memory_report() -> Dict[str, Any]:
    """Get memory report from global tracker"""
    tracker = get_memory_tracker()
    return tracker.get_memory_report()
