"""
Real-Time Stream Processing Engine
==================================

This module provides real-time stream processing capabilities with aggregation
functions, windowing operations, and complex event processing.

File: vibe_check/monitoring/streaming/stream_processor.py
Purpose: Real-time metrics stream processing with aggregations and windowing
Related Files: high_frequency_ingester.py, time_series_engine.py
Dependencies: asyncio, time, statistics
"""

import asyncio
import logging
import time
import statistics
from collections import defaultdict, deque
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Callable, Union, Tuple
from enum import Enum
import math

try:
    # Try relative imports first
    from ..collectors.base_collector import MetricValue
except ImportError:
    # Fallback to absolute imports for testing
    import sys
    from pathlib import Path
    
    # Add the project root to Python path
    project_root = Path(__file__).parent.parent.parent.parent
    sys.path.insert(0, str(project_root))
    
    from vibe_check.monitoring.collectors.base_collector import MetricValue

logger = logging.getLogger(__name__)


class WindowType(Enum):
    """Window types for stream processing"""
    TUMBLING = "tumbling"      # Non-overlapping fixed windows
    SLIDING = "sliding"        # Overlapping windows
    SESSION = "session"        # Dynamic windows based on activity


class AggregationType(Enum):
    """Aggregation function types"""
    SUM = "sum"
    AVG = "avg"
    MIN = "min"
    MAX = "max"
    COUNT = "count"
    STDDEV = "stddev"
    PERCENTILE = "percentile"
    RATE = "rate"
    DERIVATIVE = "derivative"


@dataclass
class WindowConfig:
    """Window configuration"""
    window_type: WindowType
    size_seconds: float
    slide_seconds: Optional[float] = None  # For sliding windows
    session_timeout_seconds: Optional[float] = None  # For session windows


@dataclass
class AggregationConfig:
    """Aggregation configuration"""
    aggregation_type: AggregationType
    metric_pattern: str  # Regex pattern for metric names
    group_by_labels: List[str] = field(default_factory=list)
    percentile: Optional[float] = None  # For percentile aggregations
    output_metric_name: Optional[str] = None


@dataclass
class StreamWindow:
    """Stream processing window"""
    window_id: str
    start_time: float
    end_time: float
    metrics: List[MetricValue] = field(default_factory=list)
    is_closed: bool = False


@dataclass
class AggregationResult:
    """Result of stream aggregation"""
    metric_name: str
    value: float
    labels: Dict[str, str]
    timestamp: float
    window_id: str
    aggregation_type: AggregationType


class StreamProcessor:
    """Real-time stream processing engine"""
    
    def __init__(self, window_config: WindowConfig):
        self.window_config = window_config
        self.aggregations: List[AggregationConfig] = []
        
        # Window management
        self.active_windows: Dict[str, StreamWindow] = {}
        self.window_counter = 0
        
        # Stream processing
        self.metric_buffer: deque = deque(maxlen=10000)
        self.processing_task: Optional[asyncio.Task] = None
        self.running = False
        
        # Results and callbacks
        self.result_handlers: List[Callable[[AggregationResult], None]] = []
        self.window_handlers: List[Callable[[StreamWindow], None]] = []
        
        # Performance tracking
        self.processed_metrics = 0
        self.generated_aggregations = 0
        self.processing_times: deque = deque(maxlen=1000)
        
        logger.info(f"StreamProcessor initialized with {window_config.window_type.value} windows")
    
    def add_aggregation(self, config: AggregationConfig):
        """Add an aggregation configuration"""
        self.aggregations.append(config)
        logger.info(f"Added {config.aggregation_type.value} aggregation for pattern: {config.metric_pattern}")
    
    def add_result_handler(self, handler: Callable[[AggregationResult], None]):
        """Add a result handler callback"""
        self.result_handlers.append(handler)
    
    def add_window_handler(self, handler: Callable[[StreamWindow], None]):
        """Add a window handler callback"""
        self.window_handlers.append(handler)
    
    async def start(self):
        """Start stream processing"""
        if self.running:
            logger.warning("Stream processor already running")
            return
        
        self.running = True
        self.processing_task = asyncio.create_task(self._processing_loop())
        logger.info("Stream processor started")
    
    async def stop(self):
        """Stop stream processing"""
        self.running = False
        
        if self.processing_task:
            self.processing_task.cancel()
            try:
                await self.processing_task
            except asyncio.CancelledError:
                pass
        
        # Process remaining windows
        await self._close_all_windows()
        
        logger.info("Stream processor stopped")
    
    async def process_metric(self, metric: MetricValue):
        """Process a single metric"""
        self.metric_buffer.append(metric)
        self.processed_metrics += 1
    
    async def process_metrics_batch(self, metrics: List[MetricValue]):
        """Process a batch of metrics"""
        for metric in metrics:
            await self.process_metric(metric)
    
    async def _processing_loop(self):
        """Main processing loop"""
        while self.running:
            try:
                if self.metric_buffer:
                    start_time = time.time()
                    
                    # Process available metrics
                    batch_size = min(100, len(self.metric_buffer))
                    batch = [self.metric_buffer.popleft() for _ in range(batch_size)]
                    
                    await self._process_metric_batch(batch)
                    
                    # Track processing time
                    processing_time = (time.time() - start_time) * 1000
                    self.processing_times.append(processing_time)
                
                # Check for window closures
                await self._check_window_closures()
                
                # Small delay to prevent busy waiting
                await asyncio.sleep(0.01)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in processing loop: {e}")
                await asyncio.sleep(0.1)
    
    async def _process_metric_batch(self, metrics: List[MetricValue]):
        """Process a batch of metrics"""
        for metric in metrics:
            # Assign metric to appropriate windows
            windows = self._get_windows_for_metric(metric)
            
            for window in windows:
                window.metrics.append(metric)
    
    def _get_windows_for_metric(self, metric: MetricValue) -> List[StreamWindow]:
        """Get windows that should contain this metric"""
        if metric.timestamp is None:
            metric.timestamp = time.time()
        
        windows = []
        
        if self.window_config.window_type == WindowType.TUMBLING:
            window = self._get_tumbling_window(metric.timestamp)
            windows.append(window)
        
        elif self.window_config.window_type == WindowType.SLIDING:
            windows.extend(self._get_sliding_windows(metric.timestamp))
        
        elif self.window_config.window_type == WindowType.SESSION:
            window = self._get_session_window(metric.timestamp)
            windows.append(window)
        
        return windows
    
    def _get_tumbling_window(self, timestamp: float) -> StreamWindow:
        """Get tumbling window for timestamp"""
        window_size = self.window_config.size_seconds
        window_start = math.floor(timestamp / window_size) * window_size
        window_end = window_start + window_size
        window_id = f"tumbling_{window_start}_{window_end}"
        
        if window_id not in self.active_windows:
            self.active_windows[window_id] = StreamWindow(
                window_id=window_id,
                start_time=window_start,
                end_time=window_end
            )
        
        return self.active_windows[window_id]
    
    def _get_sliding_windows(self, timestamp: float) -> List[StreamWindow]:
        """Get sliding windows for timestamp"""
        windows = []
        window_size = self.window_config.size_seconds
        slide_size = self.window_config.slide_seconds or window_size
        
        # Find all windows that should contain this timestamp
        current_time = time.time()
        
        # Look back to find relevant windows
        for i in range(10):  # Limit to prevent infinite loops
            window_start = current_time - (i * slide_size)
            window_end = window_start + window_size
            
            if window_end < timestamp:
                break
            
            if window_start <= timestamp <= window_end:
                window_id = f"sliding_{window_start}_{window_end}"
                
                if window_id not in self.active_windows:
                    self.active_windows[window_id] = StreamWindow(
                        window_id=window_id,
                        start_time=window_start,
                        end_time=window_end
                    )
                
                windows.append(self.active_windows[window_id])
        
        return windows
    
    def _get_session_window(self, timestamp: float) -> StreamWindow:
        """Get session window for timestamp"""
        session_timeout = self.window_config.session_timeout_seconds or 300.0
        
        # Find existing session window or create new one
        for window in self.active_windows.values():
            if not window.is_closed:
                # Check if this metric extends the session
                if timestamp - window.end_time <= session_timeout:
                    window.end_time = timestamp + session_timeout
                    return window
        
        # Create new session window
        self.window_counter += 1
        window_id = f"session_{self.window_counter}_{timestamp}"
        window = StreamWindow(
            window_id=window_id,
            start_time=timestamp,
            end_time=timestamp + session_timeout
        )
        
        self.active_windows[window_id] = window
        return window
    
    async def _check_window_closures(self):
        """Check for windows that should be closed"""
        current_time = time.time()
        windows_to_close = []
        
        for window_id, window in self.active_windows.items():
            if not window.is_closed and current_time >= window.end_time:
                windows_to_close.append(window_id)
        
        for window_id in windows_to_close:
            await self._close_window(window_id)
    
    async def _close_window(self, window_id: str):
        """Close a window and generate aggregations"""
        if window_id not in self.active_windows:
            return
        
        window = self.active_windows[window_id]
        window.is_closed = True
        
        # Generate aggregations for this window
        await self._generate_aggregations(window)
        
        # Notify window handlers
        for handler in self.window_handlers:
            try:
                handler(window)
            except Exception as e:
                logger.error(f"Error in window handler: {e}")
        
        # Remove closed window
        del self.active_windows[window_id]
    
    async def _close_all_windows(self):
        """Close all active windows"""
        window_ids = list(self.active_windows.keys())
        for window_id in window_ids:
            await self._close_window(window_id)
    
    async def _generate_aggregations(self, window: StreamWindow):
        """Generate aggregations for a closed window"""
        if not window.metrics:
            return
        
        for agg_config in self.aggregations:
            try:
                results = await self._compute_aggregation(window, agg_config)
                
                for result in results:
                    self.generated_aggregations += 1
                    
                    # Notify result handlers
                    for handler in self.result_handlers:
                        try:
                            handler(result)
                        except Exception as e:
                            logger.error(f"Error in result handler: {e}")
            
            except Exception as e:
                logger.error(f"Error computing aggregation {agg_config.aggregation_type}: {e}")
    
    async def _compute_aggregation(self, window: StreamWindow, 
                                 config: AggregationConfig) -> List[AggregationResult]:
        """Compute aggregation for a window"""
        import re
        
        # Filter metrics by pattern
        pattern = re.compile(config.metric_pattern)
        matching_metrics = [
            m for m in window.metrics 
            if pattern.match(m.name)
        ]
        
        if not matching_metrics:
            return []
        
        # Group by labels if specified
        if config.group_by_labels:
            groups = defaultdict(list)
            for metric in matching_metrics:
                key = tuple(
                    metric.labels.get(label, "") 
                    for label in config.group_by_labels
                )
                groups[key].append(metric)
        else:
            groups = {(): matching_metrics}
        
        results = []
        
        for group_key, group_metrics in groups.items():
            values = [m.value for m in group_metrics]
            
            if not values:
                continue
            
            # Compute aggregation
            agg_value = self._compute_aggregation_value(values, config)
            
            # Create labels
            labels = {}
            if config.group_by_labels:
                for i, label in enumerate(config.group_by_labels):
                    labels[label] = group_key[i]
            
            # Create result
            result = AggregationResult(
                metric_name=config.output_metric_name or f"{config.aggregation_type.value}_{config.metric_pattern}",
                value=agg_value,
                labels=labels,
                timestamp=window.end_time,
                window_id=window.window_id,
                aggregation_type=config.aggregation_type
            )
            
            results.append(result)
        
        return results
    
    def _compute_aggregation_value(self, values: List[float], 
                                 config: AggregationConfig) -> float:
        """Compute aggregation value"""
        if not values:
            return 0.0
        
        agg_type = config.aggregation_type
        
        if agg_type == AggregationType.SUM:
            return sum(values)
        elif agg_type == AggregationType.AVG:
            return statistics.mean(values)
        elif agg_type == AggregationType.MIN:
            return min(values)
        elif agg_type == AggregationType.MAX:
            return max(values)
        elif agg_type == AggregationType.COUNT:
            return float(len(values))
        elif agg_type == AggregationType.STDDEV:
            return statistics.stdev(values) if len(values) > 1 else 0.0
        elif agg_type == AggregationType.PERCENTILE:
            percentile = config.percentile or 95.0
            sorted_values = sorted(values)
            index = int((percentile / 100.0) * len(sorted_values))
            return sorted_values[min(index, len(sorted_values) - 1)]
        elif agg_type == AggregationType.RATE:
            # Simple rate calculation (could be enhanced)
            return sum(values) / len(values) if values else 0.0
        elif agg_type == AggregationType.DERIVATIVE:
            # Simple derivative (difference between first and last)
            return values[-1] - values[0] if len(values) > 1 else 0.0
        else:
            logger.warning(f"Unknown aggregation type: {agg_type}")
            return 0.0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get stream processing statistics"""
        avg_processing_time = (
            statistics.mean(self.processing_times) 
            if self.processing_times else 0.0
        )
        
        return {
            "processed_metrics": self.processed_metrics,
            "generated_aggregations": self.generated_aggregations,
            "active_windows": len(self.active_windows),
            "buffer_size": len(self.metric_buffer),
            "avg_processing_time_ms": avg_processing_time,
            "aggregation_configs": len(self.aggregations),
            "result_handlers": len(self.result_handlers),
            "window_handlers": len(self.window_handlers),
            "running": self.running
        }
