"""
Real-Time Stream Processing Module
==================================

This module provides real-time stream processing capabilities for the
Vibe Check monitoring platform with aggregation and windowing operations.

Components:
- StreamProcessor: Main stream processing engine
- WindowConfig: Window configuration for stream processing
- AggregationConfig: Aggregation configuration
- WindowType: Types of windows (tumbling, sliding, session)
- AggregationType: Types of aggregations (sum, avg, min, max, etc.)
"""

from .stream_processor import (
    StreamProcessor,
    WindowConfig,
    AggregationConfig,
    AggregationResult,
    StreamWindow,
    WindowType,
    AggregationType
)

__all__ = [
    'StreamProcessor',
    'WindowConfig',
    'AggregationConfig',
    'AggregationResult',
    'StreamWindow',
    'WindowType',
    'AggregationType'
]
