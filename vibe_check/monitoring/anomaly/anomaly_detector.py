"""
Anomaly Detection Engine
========================

This module provides anomaly detection algorithms for automatic alert generation
based on statistical analysis and machine learning techniques.

File: vibe_check/monitoring/anomaly/anomaly_detector.py
Purpose: Anomaly detection with statistical and ML algorithms
Related Files: alerting_engine.py, time_series_engine.py
Dependencies: numpy, scipy, asyncio
"""

import asyncio
import logging
import time
import math
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Tuple, Union
from enum import Enum
import statistics

logger = logging.getLogger(__name__)

try:
    import numpy as np
    HAS_NUMPY = True
except ImportError:
    HAS_NUMPY = False
    logger.warning("NumPy not available, using basic statistical methods")

try:
    from scipy import stats
    HAS_SCIPY = True
except ImportError:
    HAS_SCIPY = False
    logger.warning("SciPy not available, using basic statistical methods")


class AnomalyType(Enum):
    """Types of anomalies"""
    POINT = "point"  # Single point anomaly
    CONTEXTUAL = "contextual"  # Anomaly in specific context
    COLLECTIVE = "collective"  # Pattern anomaly


class DetectionMethod(Enum):
    """Anomaly detection methods"""
    STATISTICAL = "statistical"  # Z-score, IQR
    MOVING_AVERAGE = "moving_average"  # Moving average deviation
    SEASONAL = "seasonal"  # Seasonal decomposition
    ISOLATION_FOREST = "isolation_forest"  # Isolation Forest (if sklearn available)
    LSTM = "lstm"  # LSTM autoencoder (if tensorflow available)


@dataclass
class AnomalyConfig:
    """Configuration for anomaly detection"""
    method: DetectionMethod
    sensitivity: float = 2.0  # Standard deviations for statistical methods
    window_size: int = 50  # Window size for moving calculations
    min_samples: int = 10  # Minimum samples needed for detection
    
    # Statistical thresholds
    z_score_threshold: float = 2.0
    iqr_multiplier: float = 1.5
    
    # Moving average parameters
    ma_window: int = 20
    ma_threshold: float = 2.0
    
    # Seasonal parameters
    seasonal_period: int = 24  # Hours for daily seasonality
    seasonal_threshold: float = 2.0
    
    enabled: bool = True


@dataclass
class Anomaly:
    """Detected anomaly"""
    timestamp: float
    value: float
    expected_value: float
    deviation: float
    anomaly_type: AnomalyType
    detection_method: DetectionMethod
    confidence: float  # 0.0 to 1.0
    metric_name: str
    labels: Dict[str, str] = field(default_factory=dict)
    context: Dict[str, Any] = field(default_factory=dict)


class StatisticalDetector:
    """Statistical anomaly detection methods"""
    
    @staticmethod
    def z_score_detection(values: List[float], threshold: float = 2.0) -> List[Tuple[int, float]]:
        """Detect anomalies using Z-score method"""
        if len(values) < 3:
            return []
        
        if HAS_NUMPY:
            arr = np.array(values)
            mean = np.mean(arr)
            std = np.std(arr)
            if std == 0:
                return []
            z_scores = np.abs((arr - mean) / std)
            anomalies = [(i, z_scores[i]) for i in range(len(values)) if z_scores[i] > threshold]
        else:
            mean = statistics.mean(values)
            if len(values) < 2:
                return []
            std = statistics.stdev(values)
            if std == 0:
                return []
            anomalies = []
            for i, value in enumerate(values):
                z_score = abs((value - mean) / std)
                if z_score > threshold:
                    anomalies.append((i, z_score))
        
        return anomalies
    
    @staticmethod
    def iqr_detection(values: List[float], multiplier: float = 1.5) -> List[Tuple[int, float]]:
        """Detect anomalies using Interquartile Range method"""
        if len(values) < 4:
            return []
        
        if HAS_NUMPY:
            arr = np.array(values)
            q1 = np.percentile(arr, 25)
            q3 = np.percentile(arr, 75)
        else:
            sorted_values = sorted(values)
            n = len(sorted_values)
            q1 = sorted_values[n // 4]
            q3 = sorted_values[3 * n // 4]
        
        iqr = q3 - q1
        lower_bound = q1 - multiplier * iqr
        upper_bound = q3 + multiplier * iqr
        
        anomalies = []
        for i, value in enumerate(values):
            if value < lower_bound or value > upper_bound:
                deviation = min(abs(value - lower_bound), abs(value - upper_bound))
                anomalies.append((i, deviation))
        
        return anomalies


class MovingAverageDetector:
    """Moving average based anomaly detection"""
    
    @staticmethod
    def simple_moving_average(values: List[float], window: int = 20, threshold: float = 2.0) -> List[Tuple[int, float]]:
        """Detect anomalies using simple moving average"""
        if len(values) < window + 1:
            return []
        
        anomalies = []
        
        for i in range(window, len(values)):
            # Calculate moving average of previous window
            if HAS_NUMPY:
                ma = np.mean(values[i-window:i])
                std = np.std(values[i-window:i])
            else:
                window_values = values[i-window:i]
                ma = statistics.mean(window_values)
                std = statistics.stdev(window_values) if len(window_values) > 1 else 0
            
            # Check if current value is anomalous
            if std == 0:
                # If no variance in window, check for absolute deviation
                deviation = abs(values[i] - ma)
                # Use a simple threshold for zero-variance case
                if deviation > ma * 0.1:  # 10% deviation from mean
                    anomalies.append((i, deviation))
            else:
                deviation = abs(values[i] - ma) / std
                if deviation > threshold:
                    anomalies.append((i, deviation))
        
        return anomalies
    
    @staticmethod
    def exponential_moving_average(values: List[float], alpha: float = 0.3, threshold: float = 2.0) -> List[Tuple[int, float]]:
        """Detect anomalies using exponential moving average"""
        if len(values) < 10:
            return []
        
        # Calculate EMA
        ema = [values[0]]
        for i in range(1, len(values)):
            ema.append(alpha * values[i] + (1 - alpha) * ema[i-1])
        
        # Calculate deviations
        anomalies = []
        deviations = []
        
        for i in range(len(values)):
            deviation = abs(values[i] - ema[i])
            deviations.append(deviation)
        
        # Use statistical threshold on deviations
        if len(deviations) > 1:
            if HAS_NUMPY:
                mean_dev = np.mean(deviations)
                std_dev = np.std(deviations)
            else:
                mean_dev = statistics.mean(deviations)
                std_dev = statistics.stdev(deviations) if len(deviations) > 1 else 0
            
            if std_dev > 0:
                for i, deviation in enumerate(deviations):
                    if (deviation - mean_dev) / std_dev > threshold:
                        anomalies.append((i, (deviation - mean_dev) / std_dev))
        
        return anomalies


class SeasonalDetector:
    """Seasonal anomaly detection"""
    
    @staticmethod
    def seasonal_decomposition(values: List[float], timestamps: List[float], 
                             period: int = 24, threshold: float = 2.0) -> List[Tuple[int, float]]:
        """Simple seasonal anomaly detection"""
        if len(values) < period * 2:
            return []
        
        # Group values by seasonal position
        seasonal_groups = {}
        for i, (value, timestamp) in enumerate(zip(values, timestamps)):
            # Convert timestamp to seasonal position (e.g., hour of day)
            seasonal_pos = int((timestamp % (period * 3600)) // 3600)  # Hour of day
            if seasonal_pos not in seasonal_groups:
                seasonal_groups[seasonal_pos] = []
            seasonal_groups[seasonal_pos].append((i, value))
        
        anomalies = []
        
        # Check each seasonal group
        for seasonal_pos, group_values in seasonal_groups.items():
            if len(group_values) < 3:
                continue
            
            indices, values_only = zip(*group_values)
            
            # Calculate seasonal baseline
            if HAS_NUMPY:
                mean_val = np.mean(values_only)
                std_val = np.std(values_only)
            else:
                mean_val = statistics.mean(values_only)
                std_val = statistics.stdev(values_only) if len(values_only) > 1 else 0
            
            if std_val == 0:
                continue
            
            # Check for anomalies in this seasonal group
            for idx, value in group_values:
                z_score = abs((value - mean_val) / std_val)
                if z_score > threshold:
                    anomalies.append((idx, z_score))
        
        return sorted(anomalies)


class AnomalyDetector:
    """Main anomaly detection engine"""
    
    def __init__(self, config: Optional[AnomalyConfig] = None):
        self.config = config or AnomalyConfig(method=DetectionMethod.STATISTICAL)
        self.detectors = {
            DetectionMethod.STATISTICAL: StatisticalDetector(),
            DetectionMethod.MOVING_AVERAGE: MovingAverageDetector(),
            DetectionMethod.SEASONAL: SeasonalDetector()
        }
        
        # Detection history
        self.detection_history: Dict[str, List[Anomaly]] = {}
        self.metric_data: Dict[str, List[Tuple[float, float]]] = {}  # (timestamp, value)
        
        logger.info(f"AnomalyDetector initialized with method: {self.config.method.value}")
    
    async def add_data_point(self, metric_name: str, timestamp: float, value: float, 
                           labels: Optional[Dict[str, str]] = None):
        """Add a data point for anomaly detection"""
        metric_key = self._get_metric_key(metric_name, labels or {})
        
        if metric_key not in self.metric_data:
            self.metric_data[metric_key] = []
        
        self.metric_data[metric_key].append((timestamp, value))
        
        # Keep only recent data (configurable window)
        max_points = max(1000, self.config.window_size * 10)
        if len(self.metric_data[metric_key]) > max_points:
            self.metric_data[metric_key] = self.metric_data[metric_key][-max_points:]
        
        # Trigger anomaly detection if we have enough data
        if len(self.metric_data[metric_key]) >= self.config.min_samples:
            await self._detect_anomalies(metric_key, metric_name, labels or {})
    
    async def _detect_anomalies(self, metric_key: str, metric_name: str, labels: Dict[str, str]):
        """Detect anomalies for a specific metric"""
        if not self.config.enabled:
            return
        
        data_points = self.metric_data[metric_key]
        if len(data_points) < self.config.min_samples:
            return
        
        timestamps, values = zip(*data_points)
        
        # Apply detection method
        anomaly_indices = []
        
        if self.config.method == DetectionMethod.STATISTICAL:
            z_anomalies = StatisticalDetector.z_score_detection(
                list(values), self.config.z_score_threshold
            )
            iqr_anomalies = StatisticalDetector.iqr_detection(
                list(values), self.config.iqr_multiplier
            )
            # Combine both methods
            anomaly_indices = z_anomalies + iqr_anomalies
        
        elif self.config.method == DetectionMethod.MOVING_AVERAGE:
            anomaly_indices = MovingAverageDetector.simple_moving_average(
                list(values), self.config.ma_window, self.config.ma_threshold
            )
        
        elif self.config.method == DetectionMethod.SEASONAL:
            anomaly_indices = SeasonalDetector.seasonal_decomposition(
                list(values), list(timestamps), 
                self.config.seasonal_period, self.config.seasonal_threshold
            )
        
        # Create anomaly objects
        for idx, confidence in anomaly_indices:
            if idx >= len(data_points):
                continue
            
            timestamp, value = data_points[idx]
            
            # Calculate expected value (simple baseline)
            if idx > 0:
                recent_values = values[max(0, idx-10):idx]
                expected_value = statistics.mean(recent_values) if recent_values else value
            else:
                expected_value = value
            
            anomaly = Anomaly(
                timestamp=timestamp,
                value=value,
                expected_value=expected_value,
                deviation=abs(value - expected_value),
                anomaly_type=AnomalyType.POINT,
                detection_method=self.config.method,
                confidence=min(1.0, confidence / 5.0),  # Normalize confidence
                metric_name=metric_name,
                labels=labels,
                context={
                    'window_size': len(values),
                    'method_params': {
                        'threshold': confidence,
                        'method': self.config.method.value
                    }
                }
            )
            
            # Store anomaly
            if metric_key not in self.detection_history:
                self.detection_history[metric_key] = []
            
            self.detection_history[metric_key].append(anomaly)
            
            # Keep only recent anomalies
            max_anomalies = 100
            if len(self.detection_history[metric_key]) > max_anomalies:
                self.detection_history[metric_key] = self.detection_history[metric_key][-max_anomalies:]
            
            logger.info(f"Anomaly detected: {metric_name} = {value} (expected: {expected_value:.2f}, confidence: {anomaly.confidence:.2f})")
    
    def _get_metric_key(self, metric_name: str, labels: Dict[str, str]) -> str:
        """Generate unique key for metric with labels"""
        label_str = ','.join(f"{k}={v}" for k, v in sorted(labels.items()))
        return f"{metric_name}:{label_str}"
    
    def get_anomalies(self, metric_name: Optional[str] = None, 
                     since: Optional[float] = None) -> List[Anomaly]:
        """Get detected anomalies"""
        all_anomalies = []
        
        for metric_key, anomalies in self.detection_history.items():
            if metric_name and not metric_key.startswith(f"{metric_name}:"):
                continue
            
            for anomaly in anomalies:
                if since and anomaly.timestamp < since:
                    continue
                all_anomalies.append(anomaly)
        
        return sorted(all_anomalies, key=lambda x: x.timestamp, reverse=True)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get anomaly detection statistics"""
        total_anomalies = sum(len(anomalies) for anomalies in self.detection_history.values())
        total_metrics = len(self.metric_data)
        
        return {
            'total_anomalies': total_anomalies,
            'monitored_metrics': total_metrics,
            'detection_method': self.config.method.value,
            'config': {
                'sensitivity': self.config.sensitivity,
                'window_size': self.config.window_size,
                'min_samples': self.config.min_samples,
                'enabled': self.config.enabled
            }
        }
    
    async def clear_history(self, metric_name: Optional[str] = None):
        """Clear anomaly detection history"""
        if metric_name:
            keys_to_clear = [k for k in self.detection_history.keys() 
                           if k.startswith(f"{metric_name}:")]
            for key in keys_to_clear:
                del self.detection_history[key]
                if key in self.metric_data:
                    del self.metric_data[key]
        else:
            self.detection_history.clear()
            self.metric_data.clear()
        
        logger.info(f"Cleared anomaly detection history for {metric_name or 'all metrics'}")
