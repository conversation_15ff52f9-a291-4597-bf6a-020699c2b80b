"""
Python Process Instrumentation
=============================

Comprehensive instrumentation library for monitoring running Python processes.
Provides low-overhead monitoring of process metrics, memory usage, and execution statistics.
"""

import asyncio
import time
import os
import sys
import threading
import psutil
import gc
import tracemalloc
from typing import Dict, Any, Optional, Callable, List, Union
from dataclasses import dataclass, field
from functools import wraps
from collections import defaultdict, deque
import logging
from pathlib import Path

logger = logging.getLogger(__name__)


@dataclass
class ProcessMetrics:
    """Process metrics snapshot"""
    timestamp: float
    pid: int
    
    # CPU metrics
    cpu_percent: float
    cpu_times_user: float
    cpu_times_system: float
    cpu_count: int
    
    # Memory metrics
    memory_rss: int  # Resident Set Size
    memory_vms: int  # Virtual Memory Size
    memory_percent: float
    memory_available: int
    
    # Thread metrics
    thread_count: int
    
    # File descriptor metrics
    open_files: int
    
    # Network metrics
    connections: int
    
    # Python-specific metrics
    gc_collections: Dict[int, int] = field(default_factory=dict)
    gc_objects: int = 0
    
    # Application metrics
    app_name: str = ""
    app_version: str = ""


@dataclass
class FunctionMetrics:
    """Function execution metrics"""
    name: str
    module: str
    call_count: int = 0
    total_time: float = 0.0
    min_time: float = float('inf')
    max_time: float = 0.0
    avg_time: float = 0.0
    last_called: float = 0.0
    memory_peak: int = 0
    errors: int = 0


class ProcessInstrumentor:
    """Main process instrumentation class"""
    
    def __init__(self, app_name: str = "unknown", app_version: str = "1.0.0",
                 collection_interval: float = 30.0, enable_memory_tracking: bool = False,
                 lightweight_mode: bool = True):
        self.app_name = app_name
        self.app_version = app_version
        self.collection_interval = collection_interval
        self.enable_memory_tracking = enable_memory_tracking
        self.lightweight_mode = lightweight_mode
        
        # Process info
        self.process = psutil.Process()
        self.pid = os.getpid()
        self.start_time = time.time()
        
        # Metrics storage
        self.process_metrics_history: deque = deque(maxlen=1000)
        self.function_metrics: Dict[str, FunctionMetrics] = {}
        self.custom_metrics: Dict[str, Any] = {}
        
        # Collection control
        self.collection_task: Optional[asyncio.Task] = None
        self.shutdown_event = asyncio.Event()
        self.enabled = True
        
        # Memory tracking
        if self.enable_memory_tracking:
            tracemalloc.start()
        
        # Performance tracking
        self.overhead_start = 0.0
        self.overhead_total = 0.0
        self.overhead_calls = 0
        
        logger.info(f"ProcessInstrumentor initialized for {app_name} v{app_version} (PID: {self.pid})")
    
    async def start_monitoring(self):
        """Start background process monitoring"""
        if self.collection_task is None or self.collection_task.done():
            self.collection_task = asyncio.create_task(self._monitoring_loop())
            logger.info("Process monitoring started")
    
    async def stop_monitoring(self):
        """Stop background process monitoring"""
        self.shutdown_event.set()
        if self.collection_task:
            self.collection_task.cancel()
            try:
                await self.collection_task
            except asyncio.CancelledError:
                pass
        logger.info("Process monitoring stopped")
    
    async def _monitoring_loop(self):
        """Background monitoring loop"""
        while not self.shutdown_event.is_set():
            try:
                if self.enabled:
                    await self._collect_process_metrics()
                
                await asyncio.wait_for(
                    self.shutdown_event.wait(),
                    timeout=self.collection_interval
                )
            except asyncio.TimeoutError:
                continue
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(1.0)
    
    async def _collect_process_metrics(self):
        """Collect current process metrics with optimized performance"""
        overhead_start = time.perf_counter()

        try:
            # Batch psutil calls to reduce overhead
            with self.process.oneshot():
                cpu_percent = self.process.cpu_percent()
                cpu_times = self.process.cpu_times()
                memory_info = self.process.memory_info()
                memory_percent = self.process.memory_percent()

                # Get thread count (optimized)
                try:
                    thread_count = self.process.num_threads()
                except (psutil.AccessDenied, AttributeError):
                    thread_count = threading.active_count()

            # Cache expensive operations
            if not hasattr(self, '_cpu_count_cache'):
                self._cpu_count_cache = psutil.cpu_count()

            # Reduce frequency of expensive operations
            current_time = time.time()
            if not hasattr(self, '_last_expensive_check') or (current_time - self._last_expensive_check) > 30:
                # Only check these every 30 seconds
                try:
                    open_files = self.process.num_fds() if hasattr(self.process, 'num_fds') else 0
                except (psutil.AccessDenied, AttributeError):
                    open_files = 0

                try:
                    connections = len(self.process.connections())
                except (psutil.AccessDenied, AttributeError):
                    connections = 0

                # Cache expensive GC operations
                gc_count = gc.get_count()
                gc_stats = {i: gc_count[i] if i < len(gc_count) else 0 for i in range(3)}
                gc_objects = len(gc.get_objects()) if self.enable_memory_tracking else 0

                # Cache system memory (changes slowly)
                system_memory = psutil.virtual_memory()

                # Store cached values
                self._cached_open_files = open_files
                self._cached_connections = connections
                self._cached_gc_stats = gc_stats
                self._cached_gc_objects = gc_objects
                self._cached_memory_available = system_memory.available
                self._last_expensive_check = current_time
            else:
                # Use cached values
                open_files = getattr(self, '_cached_open_files', 0)
                connections = getattr(self, '_cached_connections', 0)
                gc_stats = getattr(self, '_cached_gc_stats', {})
                gc_objects = getattr(self, '_cached_gc_objects', 0)
                memory_available = getattr(self, '_cached_memory_available', 0)

            # Create metrics snapshot with minimal object creation
            metrics = ProcessMetrics(
                timestamp=current_time,
                pid=self.pid,
                cpu_percent=cpu_percent,
                cpu_times_user=cpu_times.user,
                cpu_times_system=cpu_times.system,
                cpu_count=self._cpu_count_cache,
                memory_rss=memory_info.rss,
                memory_vms=memory_info.vms,
                memory_percent=memory_percent,
                memory_available=getattr(self, '_cached_memory_available', 0),
                thread_count=thread_count,
                open_files=open_files,
                connections=connections,
                gc_collections=gc_stats,
                gc_objects=gc_objects,
                app_name=self.app_name,
                app_version=self.app_version
            )

            # Store metrics
            self.process_metrics_history.append(metrics)

            # Track overhead (optimized)
            overhead_time = time.perf_counter() - overhead_start
            self.overhead_total += overhead_time
            self.overhead_calls += 1

            # Only log debug info occasionally to reduce overhead
            if self.overhead_calls % 100 == 0:
                avg_overhead = self.overhead_total / self.overhead_calls
                logger.debug(f"Collected process metrics in {overhead_time:.4f}s (avg: {avg_overhead:.4f}s)")

        except Exception as e:
            logger.error(f"Error collecting process metrics: {e}")
    
    def instrument_function(self, func: Optional[Callable] = None, *,
                          name: Optional[str] = None, track_memory: bool = False):
        """Decorator to instrument function calls with minimal overhead"""
        def decorator(f: Callable) -> Callable:
            # In ultra-lightweight mode, return the original function unchanged
            if self.lightweight_mode:
                return f

            func_name = name or f.__name__
            func_module = f.__module__ or "unknown"
            func_key = f"{func_module}.{func_name}"

            # Pre-initialize function metrics to avoid runtime checks
            if func_key not in self.function_metrics:
                self.function_metrics[func_key] = FunctionMetrics(
                    name=func_name,
                    module=func_module
                )

            # Cache the metrics object to avoid dictionary lookups
            metrics_obj = self.function_metrics[func_key]

            @wraps(f)
            async def async_wrapper(*args, **kwargs):
                # Optimized async execution with minimal overhead
                start_time = time.perf_counter()

                try:
                    result = await f(*args, **kwargs)
                    execution_time = time.perf_counter() - start_time

                    # Fast metrics update without function call overhead
                    metrics_obj.call_count += 1
                    metrics_obj.total_time += execution_time
                    metrics_obj.last_called = start_time

                    # Only update min/max/avg every 10 calls to reduce overhead
                    if metrics_obj.call_count % 10 == 0:
                        if execution_time < metrics_obj.min_time:
                            metrics_obj.min_time = execution_time
                        if execution_time > metrics_obj.max_time:
                            metrics_obj.max_time = execution_time
                        metrics_obj.avg_time = metrics_obj.total_time / metrics_obj.call_count

                    return result
                except Exception as e:
                    execution_time = time.perf_counter() - start_time
                    metrics_obj.call_count += 1
                    metrics_obj.total_time += execution_time
                    metrics_obj.errors += 1
                    raise

            @wraps(f)
            def sync_wrapper(*args, **kwargs):
                # Ultra-optimized sync execution with minimal overhead
                start_time = time.perf_counter()

                try:
                    result = f(*args, **kwargs)

                    # Inline metrics update for maximum speed
                    metrics_obj.call_count += 1
                    execution_time = time.perf_counter() - start_time
                    metrics_obj.total_time += execution_time

                    # Only update expensive operations every 100 calls
                    if metrics_obj.call_count % 100 == 0:
                        metrics_obj.last_called = start_time
                        if execution_time < metrics_obj.min_time:
                            metrics_obj.min_time = execution_time
                        if execution_time > metrics_obj.max_time:
                            metrics_obj.max_time = execution_time
                        metrics_obj.avg_time = metrics_obj.total_time / metrics_obj.call_count

                    return result
                except Exception as e:
                    metrics_obj.call_count += 1
                    metrics_obj.total_time += time.perf_counter() - start_time
                    metrics_obj.errors += 1
                    raise

            # Return appropriate wrapper based on function type
            if asyncio.iscoroutinefunction(f):
                return async_wrapper
            else:
                return sync_wrapper

        if func is None:
            return decorator
        else:
            return decorator(func)
    
    async def _execute_instrumented(self, func: Callable, func_key: str, 
                                  track_memory: bool, *args, **kwargs):
        """Execute instrumented async function"""
        start_time = time.perf_counter()
        memory_before = 0
        
        if track_memory and self.enable_memory_tracking:
            memory_before = tracemalloc.get_traced_memory()[0]
        
        try:
            result = await func(*args, **kwargs)
            
            # Update metrics
            execution_time = time.perf_counter() - start_time
            self._update_function_metrics(func_key, execution_time, track_memory, memory_before)
            
            return result
            
        except Exception as e:
            execution_time = time.perf_counter() - start_time
            self._update_function_metrics(func_key, execution_time, track_memory, memory_before, error=True)
            raise
    

    
    def _update_function_metrics(self, func_key: str, execution_time: float,
                               track_memory: bool, memory_before: int, error: bool = False):
        """Update function execution metrics"""
        metrics = self.function_metrics[func_key]
        
        metrics.call_count += 1
        metrics.total_time += execution_time
        metrics.min_time = min(metrics.min_time, execution_time)
        metrics.max_time = max(metrics.max_time, execution_time)
        metrics.avg_time = metrics.total_time / metrics.call_count
        metrics.last_called = time.time()
        
        if error:
            metrics.errors += 1
        
        if track_memory and self.enable_memory_tracking:
            try:
                memory_after = tracemalloc.get_traced_memory()[0]
                memory_used = memory_after - memory_before
                metrics.memory_peak = max(metrics.memory_peak, memory_used)
            except Exception:
                pass
    
    def add_custom_metric(self, name: str, value: Union[int, float, str]):
        """Add custom application metric"""
        self.custom_metrics[name] = {
            'value': value,
            'timestamp': time.time()
        }
    
    def get_current_metrics(self) -> Optional[ProcessMetrics]:
        """Get the most recent process metrics"""
        return self.process_metrics_history[-1] if self.process_metrics_history else None
    
    def get_function_metrics(self) -> Dict[str, FunctionMetrics]:
        """Get all function metrics"""
        return self.function_metrics.copy()
    
    def get_overhead_percentage(self) -> float:
        """Calculate instrumentation overhead percentage"""
        if self.overhead_calls == 0:
            return 0.0
        
        avg_overhead = self.overhead_total / self.overhead_calls
        uptime = time.time() - self.start_time
        
        if uptime == 0:
            return 0.0
        
        return (self.overhead_total / uptime) * 100
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """Get comprehensive instrumentation summary"""
        current_metrics = self.get_current_metrics()
        overhead = self.get_overhead_percentage()
        
        return {
            'app_info': {
                'name': self.app_name,
                'version': self.app_version,
                'pid': self.pid,
                'uptime': time.time() - self.start_time
            },
            'current_process': current_metrics.__dict__ if current_metrics else None,
            'function_count': len(self.function_metrics),
            'total_function_calls': sum(m.call_count for m in self.function_metrics.values()),
            'custom_metrics_count': len(self.custom_metrics),
            'overhead_percentage': overhead,
            'monitoring_enabled': self.enabled,
            'memory_tracking_enabled': self.enable_memory_tracking
        }
    
    def cleanup(self):
        """Cleanup resources"""
        if self.enable_memory_tracking:
            try:
                tracemalloc.stop()
            except Exception:
                pass


# Global instrumentor instance
_global_instrumentor: Optional[ProcessInstrumentor] = None


def get_instrumentor(app_name: str = "unknown", app_version: str = "1.0.0") -> ProcessInstrumentor:
    """Get or create global process instrumentor"""
    global _global_instrumentor
    
    if _global_instrumentor is None:
        _global_instrumentor = ProcessInstrumentor(app_name, app_version)
    
    return _global_instrumentor


def instrument(func: Optional[Callable] = None, *, name: Optional[str] = None, 
               track_memory: bool = False):
    """Convenience decorator for function instrumentation"""
    instrumentor = get_instrumentor()
    return instrumentor.instrument_function(func, name=name, track_memory=track_memory)


async def start_monitoring(app_name: str = "unknown", app_version: str = "1.0.0"):
    """Start global process monitoring"""
    instrumentor = get_instrumentor(app_name, app_version)
    await instrumentor.start_monitoring()


async def stop_monitoring():
    """Stop global process monitoring"""
    global _global_instrumentor
    if _global_instrumentor:
        await _global_instrumentor.stop_monitoring()


def add_metric(name: str, value: Union[int, float, str]):
    """Add custom metric to global instrumentor"""
    instrumentor = get_instrumentor()
    instrumentor.add_custom_metric(name, value)
