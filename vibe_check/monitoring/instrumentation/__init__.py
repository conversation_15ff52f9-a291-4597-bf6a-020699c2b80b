"""
Python Process Instrumentation Module
====================================

Comprehensive instrumentation library for monitoring running Python processes.
Provides low-overhead monitoring with <5% performance impact.
"""

from .process_monitor import (
    ProcessInstrumentor,
    ProcessMetrics,
    FunctionMetrics,
    get_instrumentor,
    instrument,
    start_monitoring,
    stop_monitoring,
    add_metric,
)

from .framework_integrations import (
    # Main instrumentation functions
    instrument_flask,
    instrument_django,
    instrument_fastapi,
    auto_instrument,
    get_instrumentation_metrics,

    # Configuration and management
    InstrumentationManager,
    InstrumentationConfig,

    # Data structures
    RequestMetrics,
    FrameworkType,

    # Performance monitoring utilities
    monitor_performance,
    monitor_operation,

    # Base classes for extensions
    BaseInstrumentation,
    MetricsCollector,

    # Specific instrumentation classes
    FlaskInstrumentation,
    DjangoInstrumentation,
    FastAPIInstrumentation
)

__all__ = [
    # Process monitoring
    'ProcessInstrumentor',
    'ProcessMetrics',
    'FunctionMetrics',
    'get_instrumentor',
    'instrument',
    'start_monitoring',
    'stop_monitoring',
    'add_metric',

    # Framework integrations
    'instrument_flask',
    'instrument_django',
    'instrument_fastapi',
    'auto_instrument',
    'get_instrumentation_metrics',

    # Configuration and management
    'InstrumentationManager',
    'InstrumentationConfig',

    # Data structures
    'RequestMetrics',
    'FrameworkType',

    # Performance monitoring utilities
    'monitor_performance',
    'monitor_operation',

    # Base classes for extensions
    'BaseInstrumentation',
    'MetricsCollector',

    # Specific instrumentation classes
    'FlaskInstrumentation',
    'DjangoInstrumentation',
    'FastAPIInstrumentation'
]
