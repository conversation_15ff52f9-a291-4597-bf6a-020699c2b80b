"""
Web-Based Monitoring Interface
==============================

This module provides a web-based monitoring interface with responsive design
and user interactions for the Vibe Check monitoring platform.

File: vibe_check/monitoring/web/web_interface.py
Purpose: Web-based monitoring interface with responsive design
Related Files: dashboard_components.py, stream_processor.py
Dependencies: asyncio, aiohttp, json
"""

import asyncio
import json
import logging
import time
from pathlib import Path
from typing import Dict, List, Optional, Any
import weakref

try:
    import aiohttp
    from aiohttp import web, WSMsgType
    AIOHTTP_AVAILABLE = True
except ImportError:
    AIOHTTP_AVAILABLE = False
    aiohttp = None
    web = None
    WSMsgType = None

try:
    # Try relative imports first
    from ..dashboard.dashboard_components import Dashboard, ChartComponent, DataPoint
    from ..storage.time_series_engine import TimeSeriesStorageEngine
except ImportError:
    # Fallback to absolute imports for testing
    import sys
    from pathlib import Path
    
    # Add the project root to Python path
    project_root = Path(__file__).parent.parent.parent.parent
    sys.path.insert(0, str(project_root))
    
    from vibe_check.monitoring.dashboard.dashboard_components import Dashboard, ChartComponent, DataPoint
    from vibe_check.monitoring.storage.time_series_engine import TimeSeriesStorageEngine

logger = logging.getLogger(__name__)


class WebSocketManager:
    """Manages WebSocket connections for real-time updates"""
    
    def __init__(self):
        self.connections: weakref.WeakSet = weakref.WeakSet()
        self.subscriptions: Dict[str, List[web.WebSocketResponse]] = {}
    
    def add_connection(self, ws: 'web.WebSocketResponse'):
        """Add a WebSocket connection"""
        self.connections.add(ws)
        logger.debug(f"WebSocket connection added. Total: {len(self.connections)}")
    
    def remove_connection(self, ws: 'web.WebSocketResponse'):
        """Remove a WebSocket connection"""
        # Remove from all subscriptions
        for topic, subscribers in self.subscriptions.items():
            if ws in subscribers:
                subscribers.remove(ws)
        logger.debug(f"WebSocket connection removed")
    
    def subscribe(self, ws: 'web.WebSocketResponse', topic: str):
        """Subscribe WebSocket to a topic"""
        if topic not in self.subscriptions:
            self.subscriptions[topic] = []
        
        if ws not in self.subscriptions[topic]:
            self.subscriptions[topic].append(ws)
            logger.debug(f"WebSocket subscribed to topic: {topic}")
    
    def unsubscribe(self, ws: 'web.WebSocketResponse', topic: str):
        """Unsubscribe WebSocket from a topic"""
        if topic in self.subscriptions and ws in self.subscriptions[topic]:
            self.subscriptions[topic].remove(ws)
            logger.debug(f"WebSocket unsubscribed from topic: {topic}")
    
    async def broadcast(self, topic: str, message: Dict[str, Any]):
        """Broadcast message to all subscribers of a topic"""
        if topic not in self.subscriptions:
            return
        
        message_str = json.dumps(message)
        disconnected = []
        
        for ws in self.subscriptions[topic]:
            try:
                await ws.send_str(message_str)
            except ConnectionResetError:
                disconnected.append(ws)
            except Exception as e:
                logger.error(f"Error sending WebSocket message: {e}")
                disconnected.append(ws)
        
        # Clean up disconnected WebSockets
        for ws in disconnected:
            self.remove_connection(ws)


class WebInterface:
    """Web-based monitoring interface"""
    
    def __init__(self, host: str = "localhost", port: int = 8080):
        if not AIOHTTP_AVAILABLE:
            raise ImportError("aiohttp is required for web interface")
        
        self.host = host
        self.port = port
        self.app: Optional[web.Application] = None
        self.runner: Optional[web.AppRunner] = None
        self.site: Optional[web.TCPSite] = None
        
        # Components
        self.dashboards: Dict[str, Dashboard] = {}
        self.tsdb: Optional[TimeSeriesStorageEngine] = None
        self.ws_manager = WebSocketManager()
        
        # State
        self.running = False
        self.update_task: Optional[asyncio.Task] = None
        
        logger.info(f"WebInterface initialized on {host}:{port}")
    
    def set_tsdb(self, tsdb: TimeSeriesStorageEngine):
        """Set the time-series database"""
        self.tsdb = tsdb
        logger.info("TSDB connected to web interface")
    
    def add_dashboard(self, dashboard_id: str, dashboard: Dashboard):
        """Add a dashboard to the web interface"""
        self.dashboards[dashboard_id] = dashboard
        logger.info(f"Dashboard added: {dashboard_id}")
    
    async def start(self):
        """Start the web server"""
        if self.running:
            logger.warning("Web interface already running")
            return
        
        # Create aiohttp application
        self.app = web.Application()
        
        # Setup routes
        self._setup_routes()
        
        # Setup middleware
        self._setup_middleware()
        
        # Start server
        self.runner = web.AppRunner(self.app)
        await self.runner.setup()
        
        self.site = web.TCPSite(self.runner, self.host, self.port)
        await self.site.start()
        
        self.running = True
        
        # Start background update task
        self.update_task = asyncio.create_task(self._update_loop())
        
        logger.info(f"Web interface started on http://{self.host}:{self.port}")
    
    async def stop(self):
        """Stop the web server"""
        self.running = False
        
        if self.update_task:
            self.update_task.cancel()
            try:
                await self.update_task
            except asyncio.CancelledError:
                pass
        
        if self.site:
            await self.site.stop()
        
        if self.runner:
            await self.runner.cleanup()
        
        logger.info("Web interface stopped")
    
    def _setup_routes(self):
        """Setup HTTP routes"""
        # Static files
        self.app.router.add_get('/', self._handle_index)
        self.app.router.add_get('/dashboard/{dashboard_id}', self._handle_dashboard)
        
        # API endpoints
        self.app.router.add_get('/api/dashboards', self._handle_api_dashboards)
        self.app.router.add_get('/api/dashboard/{dashboard_id}', self._handle_api_dashboard)
        self.app.router.add_get('/api/metrics/{metric_name}', self._handle_api_metrics)
        
        # WebSocket endpoint
        self.app.router.add_get('/ws', self._handle_websocket)
        
        # Health check
        self.app.router.add_get('/health', self._handle_health)
    
    def _setup_middleware(self):
        """Setup middleware"""
        # CORS middleware
        async def cors_middleware(request, handler):
            response = await handler(request)
            response.headers['Access-Control-Allow-Origin'] = '*'
            response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
            response.headers['Access-Control-Allow-Headers'] = 'Content-Type'
            return response
        
        self.app.middlewares.append(cors_middleware)
    
    async def _handle_index(self, request):
        """Handle index page"""
        html_content = self._generate_index_html()
        return web.Response(text=html_content, content_type='text/html')
    
    async def _handle_dashboard(self, request):
        """Handle dashboard page"""
        dashboard_id = request.match_info['dashboard_id']
        
        if dashboard_id not in self.dashboards:
            return web.Response(text="Dashboard not found", status=404)
        
        html_content = self._generate_dashboard_html(dashboard_id)
        return web.Response(text=html_content, content_type='text/html')
    
    async def _handle_api_dashboards(self, request):
        """Handle API request for dashboard list"""
        dashboard_list = []
        
        for dashboard_id, dashboard in self.dashboards.items():
            stats = dashboard.get_dashboard_stats()
            dashboard_list.append({
                'id': dashboard_id,
                'title': dashboard.layout.title,
                'description': dashboard.layout.description,
                'components': stats['total_components'],
                'data_points': stats['total_data_points']
            })
        
        return web.json_response(dashboard_list)
    
    async def _handle_api_dashboard(self, request):
        """Handle API request for dashboard data"""
        dashboard_id = request.match_info['dashboard_id']
        
        if dashboard_id not in self.dashboards:
            return web.json_response({'error': 'Dashboard not found'}, status=404)
        
        dashboard = self.dashboards[dashboard_id]
        dashboard_data = dashboard.get_dashboard_data()
        
        return web.json_response(dashboard_data)
    
    async def _handle_api_metrics(self, request):
        """Handle API request for metrics data"""
        metric_name = request.match_info['metric_name']
        
        if not self.tsdb:
            return web.json_response({'error': 'TSDB not available'}, status=503)
        
        # Get query parameters
        start_time = float(request.query.get('start', time.time() - 3600))
        end_time = float(request.query.get('end', time.time()))
        
        try:
            # Query TSDB
            series_list = await self.tsdb.query_range(
                metric_name=metric_name,
                start_time=start_time,
                end_time=end_time
            )
            
            # Format response
            response_data = []
            for series in series_list:
                series_data = {
                    'metric': series.metric_name,
                    'labels': series.labels,
                    'samples': [
                        {'timestamp': sample.timestamp, 'value': sample.value}
                        for sample in series.samples
                    ]
                }
                response_data.append(series_data)
            
            return web.json_response(response_data)
        
        except Exception as e:
            logger.error(f"Error querying metrics: {e}")
            return web.json_response({'error': str(e)}, status=500)
    
    async def _handle_websocket(self, request):
        """Handle WebSocket connections"""
        ws = web.WebSocketResponse()
        await ws.prepare(request)
        
        self.ws_manager.add_connection(ws)
        
        try:
            async for msg in ws:
                if msg.type == WSMsgType.TEXT:
                    try:
                        data = json.loads(msg.data)
                        await self._handle_websocket_message(ws, data)
                    except json.JSONDecodeError:
                        await ws.send_str(json.dumps({'error': 'Invalid JSON'}))
                elif msg.type == WSMsgType.ERROR:
                    logger.error(f'WebSocket error: {ws.exception()}')
        
        except Exception as e:
            logger.error(f"WebSocket error: {e}")
        
        finally:
            self.ws_manager.remove_connection(ws)
        
        return ws
    
    async def _handle_websocket_message(self, ws, data):
        """Handle WebSocket message"""
        message_type = data.get('type')
        
        if message_type == 'subscribe':
            topic = data.get('topic')
            if topic:
                self.ws_manager.subscribe(ws, topic)
                await ws.send_str(json.dumps({
                    'type': 'subscribed',
                    'topic': topic
                }))
        
        elif message_type == 'unsubscribe':
            topic = data.get('topic')
            if topic:
                self.ws_manager.unsubscribe(ws, topic)
                await ws.send_str(json.dumps({
                    'type': 'unsubscribed',
                    'topic': topic
                }))
        
        else:
            await ws.send_str(json.dumps({'error': 'Unknown message type'}))
    
    async def _handle_health(self, request):
        """Handle health check"""
        health_data = {
            'status': 'healthy',
            'timestamp': time.time(),
            'dashboards': len(self.dashboards),
            'tsdb_available': self.tsdb is not None,
            'websocket_connections': len(self.ws_manager.connections)
        }
        
        return web.json_response(health_data)
    
    async def _update_loop(self):
        """Background update loop for real-time data"""
        while self.running:
            try:
                # Broadcast dashboard updates
                for dashboard_id, dashboard in self.dashboards.items():
                    dashboard_data = dashboard.get_dashboard_data()
                    
                    await self.ws_manager.broadcast(
                        f'dashboard_{dashboard_id}',
                        {
                            'type': 'dashboard_update',
                            'dashboard_id': dashboard_id,
                            'data': dashboard_data
                        }
                    )
                
                # Wait before next update
                await asyncio.sleep(5.0)  # Update every 5 seconds
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in update loop: {e}")
                await asyncio.sleep(1.0)
    
    def _generate_index_html(self) -> str:
        """Generate index HTML page"""
        dashboard_links = ""
        for dashboard_id, dashboard in self.dashboards.items():
            dashboard_links += f'<li><a href="/dashboard/{dashboard_id}">{dashboard.layout.title}</a></li>\n'
        
        return f"""
<!DOCTYPE html>
<html>
<head>
    <title>Vibe Check Monitoring</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; }}
        .header {{ background: #2c3e50; color: white; padding: 20px; border-radius: 5px; }}
        .dashboard-list {{ margin: 20px 0; }}
        .dashboard-list ul {{ list-style-type: none; padding: 0; }}
        .dashboard-list li {{ margin: 10px 0; }}
        .dashboard-list a {{ 
            display: block; padding: 15px; background: #ecf0f1; 
            text-decoration: none; color: #2c3e50; border-radius: 3px;
        }}
        .dashboard-list a:hover {{ background: #bdc3c7; }}
        .stats {{ margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 3px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Vibe Check Monitoring Platform</h1>
        <p>Real-time monitoring and observability dashboard</p>
    </div>
    
    <div class="stats">
        <h3>📊 System Status</h3>
        <p><strong>Dashboards:</strong> {len(self.dashboards)}</p>
        <p><strong>TSDB:</strong> {'Connected' if self.tsdb else 'Not Available'}</p>
        <p><strong>WebSocket Connections:</strong> {len(self.ws_manager.connections)}</p>
    </div>
    
    <div class="dashboard-list">
        <h3>📈 Available Dashboards</h3>
        <ul>
            {dashboard_links}
        </ul>
    </div>
    
    <div class="api-info">
        <h3>🔌 API Endpoints</h3>
        <ul>
            <li><a href="/api/dashboards">/api/dashboards</a> - List all dashboards</li>
            <li><a href="/health">/health</a> - Health check</li>
            <li><code>/ws</code> - WebSocket endpoint for real-time updates</li>
        </ul>
    </div>
</body>
</html>
"""
    
    def _generate_dashboard_html(self, dashboard_id: str) -> str:
        """Generate dashboard HTML page"""
        dashboard = self.dashboards[dashboard_id]
        
        return f"""
<!DOCTYPE html>
<html>
<head>
    <title>{dashboard.layout.title} - Vibe Check</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; }}
        .header {{ background: #34495e; color: white; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
        .dashboard-grid {{ 
            display: grid; 
            grid-template-columns: repeat({dashboard.layout.columns}, 1fr); 
            gap: 20px; 
        }}
        .chart-container {{ 
            background: white; border: 1px solid #ddd; border-radius: 5px; 
            padding: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .chart-title {{ font-size: 18px; font-weight: bold; margin-bottom: 10px; }}
        .chart-placeholder {{ 
            height: 300px; background: #f8f9fa; border-radius: 3px;
            display: flex; align-items: center; justify-content: center;
            color: #6c757d;
        }}
        .back-link {{ margin-bottom: 20px; }}
        .back-link a {{ color: #3498db; text-decoration: none; }}
        .status {{ margin: 10px 0; padding: 10px; background: #e8f5e8; border-radius: 3px; }}
    </style>
</head>
<body>
    <div class="back-link">
        <a href="/">← Back to Dashboard List</a>
    </div>
    
    <div class="header">
        <h1>📊 {dashboard.layout.title}</h1>
        <p>{dashboard.layout.description}</p>
    </div>
    
    <div class="status">
        <strong>Status:</strong> Real-time monitoring active | 
        <strong>Components:</strong> {len(dashboard.components)} | 
        <strong>Auto-refresh:</strong> {'Enabled' if dashboard.layout.auto_refresh else 'Disabled'}
    </div>
    
    <div class="dashboard-grid" id="dashboard-grid">
        <!-- Charts will be populated by JavaScript -->
    </div>
    
    <script>
        // WebSocket connection for real-time updates
        const ws = new WebSocket('ws://localhost:{self.port}/ws');
        
        ws.onopen = function() {{
            console.log('WebSocket connected');
            ws.send(JSON.stringify({{
                type: 'subscribe',
                topic: 'dashboard_{dashboard_id}'
            }}));
        }};
        
        ws.onmessage = function(event) {{
            const data = JSON.parse(event.data);
            if (data.type === 'dashboard_update') {{
                updateDashboard(data.data);
            }}
        }};
        
        function updateDashboard(dashboardData) {{
            const grid = document.getElementById('dashboard-grid');
            grid.innerHTML = '';
            
            for (const [componentId, componentData] of Object.entries(dashboardData.components)) {{
                const chartDiv = document.createElement('div');
                chartDiv.className = 'chart-container';
                chartDiv.innerHTML = `
                    <div class="chart-title">${{componentData.title || componentId}}</div>
                    <div class="chart-placeholder">
                        📈 ${{componentData.type}} Chart<br>
                        Data Points: ${{componentData.metadata?.data_points || 0}}<br>
                        Last Update: ${{new Date(componentData.metadata?.last_update * 1000).toLocaleTimeString()}}
                    </div>
                `;
                grid.appendChild(chartDiv);
            }}
        }}
        
        // Initial load
        fetch('/api/dashboard/{dashboard_id}')
            .then(response => response.json())
            .then(data => updateDashboard(data))
            .catch(error => console.error('Error loading dashboard:', error));
    </script>
</body>
</html>
"""
    
    def get_stats(self) -> Dict[str, Any]:
        """Get web interface statistics"""
        return {
            'running': self.running,
            'host': self.host,
            'port': self.port,
            'dashboards': len(self.dashboards),
            'websocket_connections': len(self.ws_manager.connections),
            'tsdb_available': self.tsdb is not None
        }
