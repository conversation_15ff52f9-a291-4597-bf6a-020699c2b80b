"""
System Metrics Collector
========================

Enhanced system metrics collector that integrates with the SystemMonitor
for comprehensive CPU, memory, disk, and network monitoring.
"""

import asyncio
import time
import platform
import os
import shutil
from typing import List, Dict, Optional, Any
from pathlib import Path
import logging

from .base_collector import (
    MetricsCollector, MetricDefinition, MetricValue, MetricType,
    CollectorConfig, CollectionInterval
)

try:
    from ..infrastructure.system_monitor import SystemMonitor, get_system_monitor
    SYSTEM_MONITOR_AVAILABLE = True
except ImportError:
    SYSTEM_MONITOR_AVAILABLE = False
    SystemMonitor = None
    get_system_monitor = None

logger = logging.getLogger(__name__)


class SystemMetricsCollector(MetricsCollector):
    """Enhanced collector for system-level metrics with SystemMonitor integration"""

    def __init__(self, system_monitor: Optional[SystemMonitor] = None,
                 config: Optional[CollectorConfig] = None):
        # Default to faster collection for system metrics
        if config is None:
            config = CollectorConfig(
                collection_interval=CollectionInterval.FAST.value,
                labels={"collector": "system", "host": platform.node()}
            )
        
        super().__init__(config)
        
        # Platform detection
        self.platform = platform.system().lower()
        self.is_linux = self.platform == "linux"
        self.is_darwin = self.platform == "darwin"
        self.is_windows = self.platform == "windows"
        
        # Cache for previous values (for rate calculations)
        self._previous_values: Dict[str, Any] = {}
        self._previous_timestamp = time.time()
        
        logger.info(f"Initialized SystemMetricsCollector for platform: {self.platform}")
    
    def _register_metrics(self):
        """Register system metrics"""
        # CPU metrics
        self.register_metric(MetricDefinition(
            name="system_cpu_usage_percent",
            metric_type=MetricType.GAUGE,
            description="CPU usage percentage",
            unit="percent",
            help_text="Overall CPU utilization as percentage"
        ))
        
        self.register_metric(MetricDefinition(
            name="system_cpu_load_average_1m",
            metric_type=MetricType.GAUGE,
            description="1-minute load average",
            help_text="System load average over 1 minute"
        ))
        
        # Memory metrics
        self.register_metric(MetricDefinition(
            name="system_memory_total_bytes",
            metric_type=MetricType.GAUGE,
            description="Total system memory in bytes",
            unit="bytes"
        ))
        
        self.register_metric(MetricDefinition(
            name="system_memory_used_bytes",
            metric_type=MetricType.GAUGE,
            description="Used system memory in bytes",
            unit="bytes"
        ))
        
        self.register_metric(MetricDefinition(
            name="system_memory_available_bytes",
            metric_type=MetricType.GAUGE,
            description="Available system memory in bytes",
            unit="bytes"
        ))
        
        self.register_metric(MetricDefinition(
            name="system_memory_usage_percent",
            metric_type=MetricType.GAUGE,
            description="Memory usage percentage",
            unit="percent"
        ))
        
        # Disk metrics
        self.register_metric(MetricDefinition(
            name="system_disk_total_bytes",
            metric_type=MetricType.GAUGE,
            description="Total disk space in bytes",
            unit="bytes",
            labels={"device": "", "mountpoint": ""}
        ))
        
        self.register_metric(MetricDefinition(
            name="system_disk_used_bytes",
            metric_type=MetricType.GAUGE,
            description="Used disk space in bytes",
            unit="bytes",
            labels={"device": "", "mountpoint": ""}
        ))
        
        self.register_metric(MetricDefinition(
            name="system_disk_usage_percent",
            metric_type=MetricType.GAUGE,
            description="Disk usage percentage",
            unit="percent",
            labels={"device": "", "mountpoint": ""}
        ))
        
        # Network metrics (basic)
        self.register_metric(MetricDefinition(
            name="system_network_connections_total",
            metric_type=MetricType.GAUGE,
            description="Total number of network connections",
            help_text="Number of active network connections"
        ))
        
        # Process metrics
        self.register_metric(MetricDefinition(
            name="system_processes_total",
            metric_type=MetricType.GAUGE,
            description="Total number of processes",
            help_text="Number of running processes"
        ))
        
        self.register_metric(MetricDefinition(
            name="system_uptime_seconds",
            metric_type=MetricType.COUNTER,
            description="System uptime in seconds",
            unit="seconds"
        ))
    
    async def collect_metrics(self) -> List[MetricValue]:
        """Collect all system metrics"""
        metrics = []
        current_time = time.time()
        
        # Collect CPU metrics
        cpu_metrics = await self._collect_cpu_metrics()
        metrics.extend(cpu_metrics)
        
        # Collect memory metrics
        memory_metrics = await self._collect_memory_metrics()
        metrics.extend(memory_metrics)
        
        # Collect disk metrics
        disk_metrics = await self._collect_disk_metrics()
        metrics.extend(disk_metrics)
        
        # Collect network metrics
        network_metrics = await self._collect_network_metrics()
        metrics.extend(network_metrics)
        
        # Collect process metrics
        process_metrics = await self._collect_process_metrics()
        metrics.extend(process_metrics)
        
        # Update timestamp
        self._previous_timestamp = current_time
        
        return metrics
    
    async def _collect_cpu_metrics(self) -> List[MetricValue]:
        """Collect CPU-related metrics"""
        metrics = []
        
        try:
            # CPU usage percentage (simple implementation)
            if self.is_linux or self.is_darwin:
                # Use /proc/loadavg for load average on Linux
                if self.is_linux and Path("/proc/loadavg").exists():
                    with open("/proc/loadavg", "r") as f:
                        load_avg = float(f.read().split()[0])
                    
                    metrics.append(MetricValue(
                        name="system_cpu_load_average_1m",
                        value=load_avg
                    ))
                
                # Simple CPU usage estimation
                # In a real implementation, you'd use psutil or similar
                cpu_usage = await self._estimate_cpu_usage()
                metrics.append(MetricValue(
                    name="system_cpu_usage_percent",
                    value=cpu_usage
                ))
            
        except Exception as e:
            logger.warning(f"Error collecting CPU metrics: {e}")
        
        return metrics
    
    async def _collect_memory_metrics(self) -> List[MetricValue]:
        """Collect memory-related metrics"""
        metrics = []
        
        try:
            if self.is_linux and Path("/proc/meminfo").exists():
                # Parse /proc/meminfo on Linux
                memory_info = await self._parse_proc_meminfo()
                
                total_kb = memory_info.get("MemTotal", 0)
                available_kb = memory_info.get("MemAvailable", memory_info.get("MemFree", 0))
                used_kb = total_kb - available_kb
                
                total_bytes = total_kb * 1024
                used_bytes = used_kb * 1024
                available_bytes = available_kb * 1024
                
                usage_percent = (used_bytes / total_bytes * 100) if total_bytes > 0 else 0
                
                metrics.extend([
                    MetricValue("system_memory_total_bytes", total_bytes),
                    MetricValue("system_memory_used_bytes", used_bytes),
                    MetricValue("system_memory_available_bytes", available_bytes),
                    MetricValue("system_memory_usage_percent", usage_percent)
                ])
            
            elif self.is_darwin:
                # macOS memory estimation
                memory_info = await self._get_darwin_memory()
                metrics.extend(memory_info)
            
            else:
                # Fallback for other platforms
                memory_info = await self._get_fallback_memory()
                metrics.extend(memory_info)
        
        except Exception as e:
            logger.warning(f"Error collecting memory metrics: {e}")
        
        return metrics
    
    async def _collect_disk_metrics(self) -> List[MetricValue]:
        """Collect disk-related metrics"""
        metrics = []
        
        try:
            # Get disk usage for root filesystem
            disk_usage = shutil.disk_usage("/")
            
            total_bytes = disk_usage.total
            used_bytes = disk_usage.used
            free_bytes = disk_usage.free
            usage_percent = (used_bytes / total_bytes * 100) if total_bytes > 0 else 0
            
            labels = {"device": "root", "mountpoint": "/"}
            
            metrics.extend([
                MetricValue("system_disk_total_bytes", total_bytes, labels),
                MetricValue("system_disk_used_bytes", used_bytes, labels),
                MetricValue("system_disk_usage_percent", usage_percent, labels)
            ])
            
        except Exception as e:
            logger.warning(f"Error collecting disk metrics: {e}")
        
        return metrics
    
    async def _collect_network_metrics(self) -> List[MetricValue]:
        """Collect network-related metrics"""
        metrics = []
        
        try:
            # Simple network connection count
            if self.is_linux and Path("/proc/net/tcp").exists():
                with open("/proc/net/tcp", "r") as f:
                    # Count lines (excluding header)
                    connection_count = len(f.readlines()) - 1
                
                metrics.append(MetricValue(
                    name="system_network_connections_total",
                    value=max(0, connection_count)
                ))
            else:
                # Fallback - estimate based on open files
                try:
                    import subprocess
                    result = subprocess.run(
                        ["netstat", "-an"], 
                        capture_output=True, 
                        text=True, 
                        timeout=2
                    )
                    connection_count = len(result.stdout.splitlines()) - 2
                    metrics.append(MetricValue(
                        name="system_network_connections_total",
                        value=max(0, connection_count)
                    ))
                except:
                    # Final fallback
                    metrics.append(MetricValue(
                        name="system_network_connections_total",
                        value=0
                    ))
        
        except Exception as e:
            logger.warning(f"Error collecting network metrics: {e}")
        
        return metrics
    
    async def _collect_process_metrics(self) -> List[MetricValue]:
        """Collect process-related metrics"""
        metrics = []
        
        try:
            # Count processes
            if self.is_linux and Path("/proc").exists():
                # Count directories in /proc that are numeric (PIDs)
                proc_count = len([
                    d for d in Path("/proc").iterdir() 
                    if d.is_dir() and d.name.isdigit()
                ])
            else:
                # Fallback using ps command
                try:
                    import subprocess
                    result = subprocess.run(
                        ["ps", "aux"], 
                        capture_output=True, 
                        text=True, 
                        timeout=2
                    )
                    proc_count = len(result.stdout.splitlines()) - 1
                except:
                    proc_count = 0
            
            metrics.append(MetricValue(
                name="system_processes_total",
                value=proc_count
            ))
            
            # System uptime
            uptime = await self._get_system_uptime()
            if uptime is not None:
                metrics.append(MetricValue(
                    name="system_uptime_seconds",
                    value=uptime
                ))
        
        except Exception as e:
            logger.warning(f"Error collecting process metrics: {e}")
        
        return metrics
    
    async def _estimate_cpu_usage(self) -> float:
        """Estimate CPU usage (simple implementation)"""
        try:
            # Simple load-based estimation
            load_avg = os.getloadavg()[0] if hasattr(os, 'getloadavg') else 0.5
            cpu_count = os.cpu_count() or 1
            cpu_usage = min(100.0, (load_avg / cpu_count) * 100)
            return cpu_usage
        except:
            return 0.0
    
    async def _parse_proc_meminfo(self) -> Dict[str, int]:
        """Parse /proc/meminfo on Linux"""
        memory_info = {}
        try:
            with open("/proc/meminfo", "r") as f:
                for line in f:
                    if ":" in line:
                        key, value = line.split(":", 1)
                        # Extract numeric value (in kB)
                        value_kb = int(value.strip().split()[0])
                        memory_info[key.strip()] = value_kb
        except Exception as e:
            logger.warning(f"Error parsing /proc/meminfo: {e}")
        
        return memory_info
    
    async def _get_darwin_memory(self) -> List[MetricValue]:
        """Get memory info for macOS"""
        # Simplified implementation - in practice would use vm_stat
        try:
            import subprocess
            result = subprocess.run(
                ["sysctl", "hw.memsize"], 
                capture_output=True, 
                text=True, 
                timeout=2
            )
            total_bytes = int(result.stdout.split(":")[1].strip())
            
            # Estimate used memory (simplified)
            used_bytes = int(total_bytes * 0.6)  # Rough estimate
            available_bytes = total_bytes - used_bytes
            usage_percent = (used_bytes / total_bytes * 100)
            
            return [
                MetricValue("system_memory_total_bytes", total_bytes),
                MetricValue("system_memory_used_bytes", used_bytes),
                MetricValue("system_memory_available_bytes", available_bytes),
                MetricValue("system_memory_usage_percent", usage_percent)
            ]
        except:
            return await self._get_fallback_memory()
    
    async def _get_fallback_memory(self) -> List[MetricValue]:
        """Fallback memory info for unsupported platforms"""
        # Very basic fallback
        total_bytes = 8 * 1024 * 1024 * 1024  # Assume 8GB
        used_bytes = int(total_bytes * 0.5)   # Assume 50% used
        available_bytes = total_bytes - used_bytes
        usage_percent = 50.0
        
        return [
            MetricValue("system_memory_total_bytes", total_bytes),
            MetricValue("system_memory_used_bytes", used_bytes),
            MetricValue("system_memory_available_bytes", available_bytes),
            MetricValue("system_memory_usage_percent", usage_percent)
        ]
    
    async def _get_system_uptime(self) -> Optional[float]:
        """Get system uptime in seconds"""
        try:
            if self.is_linux and Path("/proc/uptime").exists():
                with open("/proc/uptime", "r") as f:
                    uptime = float(f.read().split()[0])
                return uptime
            elif self.is_darwin:
                import subprocess
                result = subprocess.run(
                    ["sysctl", "-n", "kern.boottime"], 
                    capture_output=True, 
                    text=True, 
                    timeout=2
                )
                # Parse boot time and calculate uptime
                # This is simplified - real implementation would parse the format
                return time.time() - 3600  # Placeholder
            else:
                return None
        except:
            return None
