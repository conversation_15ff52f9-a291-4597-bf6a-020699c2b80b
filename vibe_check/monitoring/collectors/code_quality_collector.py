"""
Code Quality Metrics Collector
==============================

Collects code quality metrics by integrating with the existing Vibe Check
analysis engine to provide monitoring data for code quality trends.
"""

import asyncio
import time
from typing import List, Dict, Optional, Any
from pathlib import Path
import logging

from .base_collector import (
    MetricsCollector, MetricDefinition, MetricValue, MetricType, 
    CollectorConfig, CollectionInterval
)

logger = logging.getLogger(__name__)


class CodeQualityMetricsCollector(MetricsCollector):
    """Collector for code quality metrics from Vibe Check analysis"""
    
    def __init__(self, project_paths: List[Path], config: Optional[CollectorConfig] = None):
        # Default to slower collection for code quality (it's more expensive)
        if config is None:
            config = CollectorConfig(
                collection_interval=CollectionInterval.SLOW.value,
                max_collection_time=30.0,  # Code analysis can take longer
                labels={"collector": "code_quality"}
            )
        
        super().__init__(config)
        self.project_paths = project_paths
        self.last_analysis_results: Dict[str, Any] = {}
        
        logger.info(f"Initialized CodeQualityMetricsCollector for {len(project_paths)} projects")
    
    def _register_metrics(self):
        """Register code quality metrics"""
        # File-level metrics
        self.register_metric(MetricDefinition(
            name="code_quality_files_total",
            metric_type=MetricType.GAUGE,
            description="Total number of files analyzed",
            labels={"project": ""}
        ))
        
        self.register_metric(MetricDefinition(
            name="code_quality_lines_total",
            metric_type=MetricType.GAUGE,
            description="Total lines of code",
            labels={"project": ""}
        ))
        
        self.register_metric(MetricDefinition(
            name="code_quality_lines_code",
            metric_type=MetricType.GAUGE,
            description="Lines of actual code (excluding comments/blank)",
            labels={"project": ""}
        ))
        
        # Quality metrics
        self.register_metric(MetricDefinition(
            name="code_quality_score_average",
            metric_type=MetricType.GAUGE,
            description="Average code quality score (0-10)",
            labels={"project": ""},
            unit="score"
        ))
        
        self.register_metric(MetricDefinition(
            name="code_quality_complexity_average",
            metric_type=MetricType.GAUGE,
            description="Average cyclomatic complexity",
            labels={"project": ""}
        ))
        
        self.register_metric(MetricDefinition(
            name="code_quality_issues_total",
            metric_type=MetricType.GAUGE,
            description="Total number of code quality issues",
            labels={"project": "", "severity": ""}
        ))
        
        # Function/class metrics
        self.register_metric(MetricDefinition(
            name="code_quality_functions_total",
            metric_type=MetricType.GAUGE,
            description="Total number of functions",
            labels={"project": ""}
        ))
        
        self.register_metric(MetricDefinition(
            name="code_quality_classes_total",
            metric_type=MetricType.GAUGE,
            description="Total number of classes",
            labels={"project": ""}
        ))
        
        # Analysis performance metrics
        self.register_metric(MetricDefinition(
            name="code_quality_analysis_duration_seconds",
            metric_type=MetricType.GAUGE,
            description="Time taken for code quality analysis",
            labels={"project": ""},
            unit="seconds"
        ))
        
        self.register_metric(MetricDefinition(
            name="code_quality_analysis_files_per_second",
            metric_type=MetricType.GAUGE,
            description="Analysis throughput in files per second",
            labels={"project": ""},
            unit="files_per_second"
        ))
        
        # Tool-specific metrics
        self.register_metric(MetricDefinition(
            name="code_quality_tool_issues",
            metric_type=MetricType.GAUGE,
            description="Issues found by specific tools",
            labels={"project": "", "tool": "", "category": ""}
        ))
    
    async def collect_metrics(self) -> List[MetricValue]:
        """Collect code quality metrics for all configured projects"""
        metrics = []
        
        for project_path in self.project_paths:
            project_name = project_path.name
            
            try:
                # Perform code quality analysis
                analysis_start = time.time()
                project_metrics = await self._analyze_project(project_path, project_name)
                analysis_duration = time.time() - analysis_start
                
                # Add analysis performance metrics
                if project_metrics:
                    files_analyzed = sum(1 for m in project_metrics if m.name == "code_quality_files_total")
                    if files_analyzed > 0 and analysis_duration > 0:
                        throughput = files_analyzed / analysis_duration
                        project_metrics.extend([
                            MetricValue(
                                name="code_quality_analysis_duration_seconds",
                                value=analysis_duration,
                                labels={"project": project_name}
                            ),
                            MetricValue(
                                name="code_quality_analysis_files_per_second",
                                value=throughput,
                                labels={"project": project_name}
                            )
                        ])
                
                metrics.extend(project_metrics)
                
            except Exception as e:
                logger.error(f"Error analyzing project {project_name}: {e}")
                # Add error metric
                metrics.append(MetricValue(
                    name="code_quality_analysis_errors_total",
                    value=1,
                    labels={"project": project_name, "error": str(e)[:100]}
                ))
        
        return metrics
    
    async def _analyze_project(self, project_path: Path, project_name: str) -> List[MetricValue]:
        """Analyze a single project and return metrics"""
        metrics = []
        
        try:
            # Use simplified analysis for now (would integrate with full Vibe Check engine)
            analysis_result = await self._perform_simple_analysis(project_path)
            
            # Convert analysis results to metrics
            base_labels = {"project": project_name}
            
            # File and line metrics
            metrics.extend([
                MetricValue(
                    name="code_quality_files_total",
                    value=analysis_result.get("files_analyzed", 0),
                    labels=base_labels
                ),
                MetricValue(
                    name="code_quality_lines_total",
                    value=analysis_result.get("total_lines", 0),
                    labels=base_labels
                ),
                MetricValue(
                    name="code_quality_lines_code",
                    value=analysis_result.get("code_lines", 0),
                    labels=base_labels
                )
            ])
            
            # Quality metrics
            if analysis_result.get("average_quality_score") is not None:
                metrics.append(MetricValue(
                    name="code_quality_score_average",
                    value=analysis_result["average_quality_score"],
                    labels=base_labels
                ))
            
            if analysis_result.get("average_complexity") is not None:
                metrics.append(MetricValue(
                    name="code_quality_complexity_average",
                    value=analysis_result["average_complexity"],
                    labels=base_labels
                ))
            
            # Structure metrics
            metrics.extend([
                MetricValue(
                    name="code_quality_functions_total",
                    value=analysis_result.get("total_functions", 0),
                    labels=base_labels
                ),
                MetricValue(
                    name="code_quality_classes_total",
                    value=analysis_result.get("total_classes", 0),
                    labels=base_labels
                )
            ])
            
            # Issue metrics by severity
            issues_by_severity = analysis_result.get("issues_by_severity", {})
            for severity, count in issues_by_severity.items():
                metrics.append(MetricValue(
                    name="code_quality_issues_total",
                    value=count,
                    labels={**base_labels, "severity": severity}
                ))
            
            # Tool-specific metrics
            tool_results = analysis_result.get("tool_results", {})
            for tool_name, tool_data in tool_results.items():
                for category, count in tool_data.items():
                    metrics.append(MetricValue(
                        name="code_quality_tool_issues",
                        value=count,
                        labels={**base_labels, "tool": tool_name, "category": category}
                    ))
            
        except Exception as e:
            logger.error(f"Error in project analysis for {project_name}: {e}")
        
        return metrics
    
    async def _perform_simple_analysis(self, project_path: Path) -> Dict[str, Any]:
        """Perform simplified code analysis"""
        # This is a simplified version - in practice would use the full Vibe Check engine
        
        result = {
            "files_analyzed": 0,
            "total_lines": 0,
            "code_lines": 0,
            "total_functions": 0,
            "total_classes": 0,
            "average_quality_score": 0.0,
            "average_complexity": 0.0,
            "issues_by_severity": {"high": 0, "medium": 0, "low": 0},
            "tool_results": {}
        }
        
        try:
            # Find Python files
            python_files = list(project_path.rglob("*.py"))
            
            # Filter out common directories to ignore
            ignore_patterns = {".venv", "venv", "__pycache__", ".git", "node_modules"}
            python_files = [
                f for f in python_files 
                if not any(ignore in str(f) for ignore in ignore_patterns)
            ]
            
            result["files_analyzed"] = len(python_files)
            
            if not python_files:
                return result
            
            # Analyze each file
            total_quality_score = 0.0
            total_complexity = 0.0
            
            for file_path in python_files[:50]:  # Limit to 50 files for performance
                try:
                    file_analysis = await self._analyze_file(file_path)
                    
                    result["total_lines"] += file_analysis["lines"]
                    result["code_lines"] += file_analysis["code_lines"]
                    result["total_functions"] += file_analysis["functions"]
                    result["total_classes"] += file_analysis["classes"]
                    
                    total_quality_score += file_analysis["quality_score"]
                    total_complexity += file_analysis["complexity"]
                    
                    # Aggregate issues
                    for severity in result["issues_by_severity"]:
                        result["issues_by_severity"][severity] += file_analysis.get("issues", {}).get(severity, 0)
                
                except Exception as e:
                    logger.debug(f"Error analyzing file {file_path}: {e}")
                    continue
            
            # Calculate averages
            if result["files_analyzed"] > 0:
                result["average_quality_score"] = total_quality_score / result["files_analyzed"]
                result["average_complexity"] = total_complexity / result["files_analyzed"]
            
            # Simulate tool results
            result["tool_results"] = {
                "ruff": {"style": result["issues_by_severity"]["low"]},
                "mypy": {"type": result["issues_by_severity"]["medium"]},
                "bandit": {"security": result["issues_by_severity"]["high"]}
            }
            
        except Exception as e:
            logger.error(f"Error in simple analysis: {e}")
        
        return result
    
    async def _analyze_file(self, file_path: Path) -> Dict[str, Any]:
        """Analyze a single file"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            lines = content.split('\n')
            total_lines = len(lines)
            
            # Count code lines (non-empty, non-comment)
            code_lines = len([
                line for line in lines 
                if line.strip() and not line.strip().startswith('#')
            ])
            
            # Count functions and classes
            functions = content.count('def ')
            classes = content.count('class ')
            
            # Simple complexity calculation
            complexity = 1  # Base complexity
            complexity += content.count('if ')
            complexity += content.count('for ')
            complexity += content.count('while ')
            complexity += content.count('try:')
            complexity += content.count('except')
            
            # Simple quality score (0-10)
            quality_score = 10.0
            
            # Deduct for high complexity
            if complexity > 20:
                quality_score -= min(3.0, (complexity - 20) * 0.1)
            
            # Deduct for very long files
            if total_lines > 500:
                quality_score -= min(2.0, (total_lines - 500) * 0.001)
            
            # Deduct for low code ratio
            code_ratio = code_lines / total_lines if total_lines > 0 else 0
            if code_ratio < 0.3:
                quality_score -= 1.0
            
            quality_score = max(0.0, min(10.0, quality_score))
            
            # Generate issues based on analysis
            issues = {
                "high": max(0, complexity - 30),
                "medium": max(0, complexity - 20),
                "low": max(0, total_lines - 300) // 100
            }
            
            return {
                "lines": total_lines,
                "code_lines": code_lines,
                "functions": functions,
                "classes": classes,
                "complexity": complexity,
                "quality_score": quality_score,
                "issues": issues
            }
            
        except Exception as e:
            logger.debug(f"Error analyzing file {file_path}: {e}")
            return {
                "lines": 0, "code_lines": 0, "functions": 0, "classes": 0,
                "complexity": 0, "quality_score": 0.0, "issues": {"high": 0, "medium": 0, "low": 0}
            }
    
    def add_project_path(self, project_path: Path):
        """Add a new project path to monitor"""
        if project_path not in self.project_paths:
            self.project_paths.append(project_path)
            logger.info(f"Added project path: {project_path}")
    
    def remove_project_path(self, project_path: Path):
        """Remove a project path from monitoring"""
        if project_path in self.project_paths:
            self.project_paths.remove(project_path)
            logger.info(f"Removed project path: {project_path}")
    
    def get_monitored_projects(self) -> List[Path]:
        """Get list of monitored project paths"""
        return self.project_paths.copy()
