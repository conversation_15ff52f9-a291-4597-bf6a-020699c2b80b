"""
Base Metrics Collector
=====================

Base classes and interfaces for metrics collection in the Vibe Check monitoring platform.
Provides foundation for system metrics, code quality metrics, and custom metrics collection.
"""

import asyncio
import time
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union, Callable, AsyncIterator
from dataclasses import dataclass, field
from enum import Enum
import threading
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)


class MetricType(Enum):
    """Types of metrics that can be collected"""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    SUMMARY = "summary"


class CollectionInterval(Enum):
    """Standard collection intervals"""
    REALTIME = 1.0      # 1 second
    FAST = 5.0          # 5 seconds
    NORMAL = 10.0       # 10 seconds
    SLOW = 30.0         # 30 seconds
    VERY_SLOW = 60.0    # 1 minute


@dataclass
class MetricDefinition:
    """Definition of a metric to be collected"""
    name: str
    metric_type: MetricType
    description: str
    labels: Dict[str, str] = field(default_factory=dict)
    unit: Optional[str] = None
    help_text: Optional[str] = None


@dataclass
class MetricValue:
    """A collected metric value"""
    name: str
    value: Union[float, int]
    labels: Dict[str, str] = field(default_factory=dict)
    timestamp: Optional[float] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


@dataclass
class CollectorConfig:
    """Configuration for metrics collectors"""
    enabled: bool = True
    collection_interval: float = CollectionInterval.NORMAL.value
    max_collection_time: float = 5.0  # Max time for single collection
    error_threshold: int = 5  # Max consecutive errors before disabling
    retry_interval: float = 60.0  # Retry interval after errors
    labels: Dict[str, str] = field(default_factory=dict)  # Default labels


class MetricsCollector(ABC):
    """Abstract base class for all metrics collectors"""
    
    def __init__(self, config: Optional[CollectorConfig] = None):
        self.config = config or CollectorConfig()
        self.name = self.__class__.__name__
        self.enabled = self.config.enabled
        self.error_count = 0
        self.last_collection_time = 0.0
        self.collection_count = 0
        self.total_collection_time = 0.0
        
        # Background collection task
        self.collection_task: Optional[asyncio.Task] = None
        self.shutdown_event = asyncio.Event()
        
        # Thread pool for blocking operations
        self.executor = ThreadPoolExecutor(max_workers=2)
        
        # Metrics registry
        self._metrics_definitions: Dict[str, MetricDefinition] = {}
        self._register_metrics()
    
    @abstractmethod
    def _register_metrics(self):
        """Register metrics that this collector will provide"""
        pass
    
    @abstractmethod
    async def collect_metrics(self) -> List[MetricValue]:
        """Collect metrics and return list of metric values"""
        pass
    
    def register_metric(self, definition: MetricDefinition):
        """Register a metric definition"""
        self._metrics_definitions[definition.name] = definition
        logger.debug(f"Registered metric: {definition.name} ({definition.metric_type.value})")
    
    def get_metric_definitions(self) -> Dict[str, MetricDefinition]:
        """Get all registered metric definitions"""
        return self._metrics_definitions.copy()
    
    async def start_collection(self):
        """Start background metrics collection"""
        if self.collection_task is None or self.collection_task.done():
            self.collection_task = asyncio.create_task(self._collection_loop())
            logger.info(f"Started metrics collection for {self.name}")
    
    async def stop_collection(self):
        """Stop background metrics collection"""
        self.shutdown_event.set()
        if self.collection_task:
            self.collection_task.cancel()
            try:
                await self.collection_task
            except asyncio.CancelledError:
                pass
        logger.info(f"Stopped metrics collection for {self.name}")
    
    async def _collection_loop(self):
        """Background collection loop"""
        while not self.shutdown_event.is_set():
            try:
                if self.enabled:
                    await self._collect_with_error_handling()
                
                # Wait for next collection interval
                await asyncio.wait_for(
                    self.shutdown_event.wait(),
                    timeout=self.config.collection_interval
                )
            except asyncio.TimeoutError:
                # Normal timeout, continue collection
                continue
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Unexpected error in collection loop for {self.name}: {e}")
                await asyncio.sleep(1.0)
    
    async def _collect_with_error_handling(self):
        """Collect metrics with error handling and timing"""
        start_time = time.time()
        
        try:
            # Collect metrics with timeout
            metrics = await asyncio.wait_for(
                self.collect_metrics(),
                timeout=self.config.max_collection_time
            )
            
            # Process collected metrics
            if metrics:
                await self._process_collected_metrics(metrics)
            
            # Update statistics
            collection_time = time.time() - start_time
            self.last_collection_time = collection_time
            self.collection_count += 1
            self.total_collection_time += collection_time
            self.error_count = 0  # Reset error count on success
            
            logger.debug(f"Collected {len(metrics) if metrics else 0} metrics from {self.name} in {collection_time:.3f}s")
            
        except asyncio.TimeoutError:
            self.error_count += 1
            logger.warning(f"Collection timeout for {self.name} (attempt {self.error_count})")
            await self._handle_collection_error("timeout")
            
        except Exception as e:
            self.error_count += 1
            logger.error(f"Collection error for {self.name}: {e} (attempt {self.error_count})")
            await self._handle_collection_error(str(e))
    
    async def _process_collected_metrics(self, metrics: List[MetricValue]):
        """Process collected metrics (can be overridden by subclasses)"""
        # Add default labels from config
        for metric in metrics:
            metric.labels.update(self.config.labels)
        
        # Emit metrics event (can be connected to TSDB)
        await self._emit_metrics(metrics)
    
    async def _emit_metrics(self, metrics: List[MetricValue]):
        """Emit metrics to the monitoring system"""
        # This will be connected to the TSDB in the metrics manager
        # For now, just log the metrics
        for metric in metrics:
            logger.debug(f"Metric: {metric.name}={metric.value} {metric.labels}")
    
    async def _handle_collection_error(self, error_type: str):
        """Handle collection errors"""
        if self.error_count >= self.config.error_threshold:
            logger.error(f"Disabling collector {self.name} after {self.error_count} consecutive errors")
            self.enabled = False
            
            # Schedule re-enable after retry interval
            asyncio.create_task(self._schedule_retry())
    
    async def _schedule_retry(self):
        """Schedule retry after error threshold reached"""
        await asyncio.sleep(self.config.retry_interval)
        logger.info(f"Re-enabling collector {self.name} after retry interval")
        self.enabled = True
        self.error_count = 0
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """Get collection statistics"""
        avg_collection_time = (
            self.total_collection_time / self.collection_count
            if self.collection_count > 0 else 0.0
        )
        
        return {
            'name': self.name,
            'enabled': self.enabled,
            'collection_count': self.collection_count,
            'error_count': self.error_count,
            'last_collection_time': self.last_collection_time,
            'avg_collection_time': avg_collection_time,
            'total_collection_time': self.total_collection_time,
            'metrics_registered': len(self._metrics_definitions)
        }
    
    def cleanup(self):
        """Cleanup resources"""
        self.executor.shutdown(wait=False)


class MetricsRegistry:
    """Registry for managing multiple metrics collectors"""
    
    def __init__(self):
        self.collectors: Dict[str, MetricsCollector] = {}
        self.metrics_handlers: List[Callable[[List[MetricValue]], None]] = []
        self._lock = threading.RLock()
    
    def register_collector(self, collector: MetricsCollector):
        """Register a metrics collector"""
        with self._lock:
            self.collectors[collector.name] = collector
            logger.info(f"Registered collector: {collector.name}")
    
    def unregister_collector(self, name: str):
        """Unregister a metrics collector"""
        with self._lock:
            if name in self.collectors:
                collector = self.collectors.pop(name)
                asyncio.create_task(collector.stop_collection())
                logger.info(f"Unregistered collector: {name}")
    
    def add_metrics_handler(self, handler: Callable[[List[MetricValue]], None]):
        """Add a handler for collected metrics"""
        self.metrics_handlers.append(handler)
    
    async def start_all_collectors(self):
        """Start all registered collectors"""
        tasks = []
        for collector in self.collectors.values():
            tasks.append(collector.start_collection())
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
        
        logger.info(f"Started {len(tasks)} metrics collectors")
    
    async def stop_all_collectors(self):
        """Stop all registered collectors"""
        tasks = []
        for collector in self.collectors.values():
            tasks.append(collector.stop_collection())
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
        
        logger.info(f"Stopped {len(tasks)} metrics collectors")
    
    def get_all_metric_definitions(self) -> Dict[str, MetricDefinition]:
        """Get all metric definitions from all collectors"""
        all_definitions = {}
        for collector in self.collectors.values():
            all_definitions.update(collector.get_metric_definitions())
        return all_definitions
    
    def get_registry_stats(self) -> Dict[str, Any]:
        """Get statistics for all collectors"""
        stats = {
            'total_collectors': len(self.collectors),
            'enabled_collectors': sum(1 for c in self.collectors.values() if c.enabled),
            'collectors': {}
        }
        
        for name, collector in self.collectors.items():
            stats['collectors'][name] = collector.get_collection_stats()
        
        return stats
    
    def cleanup(self):
        """Cleanup all collectors"""
        for collector in self.collectors.values():
            collector.cleanup()


# Global metrics registry instance
metrics_registry = MetricsRegistry()


# Convenience functions
def register_collector(collector: MetricsCollector):
    """Register a collector with the global registry"""
    metrics_registry.register_collector(collector)


def get_metrics_registry() -> MetricsRegistry:
    """Get the global metrics registry"""
    return metrics_registry
