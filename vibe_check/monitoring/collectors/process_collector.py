"""
Process Metrics Collector
=========================

Collector that integrates Python process instrumentation with the metrics collection framework.
Bridges process monitoring data to the time-series storage system.
"""

import asyncio
import time
from typing import List, Dict, Optional, Any
from pathlib import Path
import logging

from .base_collector import (
    MetricsCollector, MetricDefinition, MetricValue, MetricType, 
    CollectorConfig, CollectionInterval
)
from ..instrumentation.process_monitor import ProcessInstrumentor, get_instrumentor

logger = logging.getLogger(__name__)


class ProcessMetricsCollector(MetricsCollector):
    """Collector for Python process instrumentation metrics"""
    
    def __init__(self, instrumentor: Optional[ProcessInstrumentor] = None,
                 config: Optional[CollectorConfig] = None):
        # Default to normal collection for process metrics
        if config is None:
            config = CollectorConfig(
                collection_interval=CollectionInterval.NORMAL.value,
                max_collection_time=5.0,
                labels={"collector": "process"}
            )
        
        super().__init__(config)
        
        # Use provided instrumentor or get global one
        self.instrumentor = instrumentor or get_instrumentor()
        
        logger.info(f"Initialized ProcessMetricsCollector for app: {self.instrumentor.app_name}")
    
    def _register_metrics(self):
        """Register process metrics"""
        # Process identification metrics
        self.register_metric(MetricDefinition(
            name="process_info",
            metric_type=MetricType.GAUGE,
            description="Process information",
            labels={"app_name": "", "app_version": "", "pid": ""}
        ))
        
        self.register_metric(MetricDefinition(
            name="process_uptime_seconds",
            metric_type=MetricType.COUNTER,
            description="Process uptime in seconds",
            unit="seconds"
        ))
        
        # CPU metrics
        self.register_metric(MetricDefinition(
            name="process_cpu_usage_percent",
            metric_type=MetricType.GAUGE,
            description="Process CPU usage percentage",
            unit="percent"
        ))
        
        self.register_metric(MetricDefinition(
            name="process_cpu_time_user_seconds",
            metric_type=MetricType.COUNTER,
            description="Process user CPU time",
            unit="seconds"
        ))
        
        self.register_metric(MetricDefinition(
            name="process_cpu_time_system_seconds",
            metric_type=MetricType.COUNTER,
            description="Process system CPU time",
            unit="seconds"
        ))
        
        # Memory metrics
        self.register_metric(MetricDefinition(
            name="process_memory_rss_bytes",
            metric_type=MetricType.GAUGE,
            description="Process resident set size",
            unit="bytes"
        ))
        
        self.register_metric(MetricDefinition(
            name="process_memory_vms_bytes",
            metric_type=MetricType.GAUGE,
            description="Process virtual memory size",
            unit="bytes"
        ))
        
        self.register_metric(MetricDefinition(
            name="process_memory_usage_percent",
            metric_type=MetricType.GAUGE,
            description="Process memory usage percentage",
            unit="percent"
        ))
        
        # Thread and resource metrics
        self.register_metric(MetricDefinition(
            name="process_threads_total",
            metric_type=MetricType.GAUGE,
            description="Number of process threads"
        ))
        
        self.register_metric(MetricDefinition(
            name="process_open_files_total",
            metric_type=MetricType.GAUGE,
            description="Number of open file descriptors"
        ))
        
        self.register_metric(MetricDefinition(
            name="process_network_connections_total",
            metric_type=MetricType.GAUGE,
            description="Number of network connections"
        ))
        
        # Python-specific metrics
        self.register_metric(MetricDefinition(
            name="process_gc_objects_total",
            metric_type=MetricType.GAUGE,
            description="Number of objects tracked by garbage collector"
        ))
        
        self.register_metric(MetricDefinition(
            name="process_gc_collections_total",
            metric_type=MetricType.COUNTER,
            description="Number of garbage collections by generation",
            labels={"generation": ""}
        ))
        
        # Function instrumentation metrics
        self.register_metric(MetricDefinition(
            name="process_function_calls_total",
            metric_type=MetricType.COUNTER,
            description="Total function calls",
            labels={"function": "", "module": ""}
        ))
        
        self.register_metric(MetricDefinition(
            name="process_function_duration_seconds",
            metric_type=MetricType.GAUGE,
            description="Function execution duration statistics",
            labels={"function": "", "module": "", "stat": ""}  # stat: avg, min, max
        ))
        
        self.register_metric(MetricDefinition(
            name="process_function_errors_total",
            metric_type=MetricType.COUNTER,
            description="Function execution errors",
            labels={"function": "", "module": ""}
        ))
        
        # Instrumentation overhead metrics
        self.register_metric(MetricDefinition(
            name="process_instrumentation_overhead_percent",
            metric_type=MetricType.GAUGE,
            description="Instrumentation overhead percentage",
            unit="percent"
        ))
        
        # Custom metrics
        self.register_metric(MetricDefinition(
            name="process_custom_metric",
            metric_type=MetricType.GAUGE,
            description="Custom application metrics",
            labels={"metric_name": ""}
        ))
    
    async def collect_metrics(self) -> List[MetricValue]:
        """Collect process instrumentation metrics"""
        metrics = []
        
        try:
            # Get current process metrics
            current_metrics = self.instrumentor.get_current_metrics()
            
            if current_metrics:
                # Process identification
                metrics.append(MetricValue(
                    name="process_info",
                    value=1,
                    labels={
                        "app_name": current_metrics.app_name,
                        "app_version": current_metrics.app_version,
                        "pid": str(current_metrics.pid)
                    }
                ))
                
                # Process uptime
                uptime = time.time() - self.instrumentor.start_time
                metrics.append(MetricValue(
                    name="process_uptime_seconds",
                    value=uptime
                ))
                
                # CPU metrics
                metrics.extend([
                    MetricValue("process_cpu_usage_percent", current_metrics.cpu_percent),
                    MetricValue("process_cpu_time_user_seconds", current_metrics.cpu_times_user),
                    MetricValue("process_cpu_time_system_seconds", current_metrics.cpu_times_system)
                ])
                
                # Memory metrics
                metrics.extend([
                    MetricValue("process_memory_rss_bytes", current_metrics.memory_rss),
                    MetricValue("process_memory_vms_bytes", current_metrics.memory_vms),
                    MetricValue("process_memory_usage_percent", current_metrics.memory_percent)
                ])
                
                # Thread and resource metrics
                metrics.extend([
                    MetricValue("process_threads_total", current_metrics.thread_count),
                    MetricValue("process_open_files_total", current_metrics.open_files),
                    MetricValue("process_network_connections_total", current_metrics.connections)
                ])
                
                # Python-specific metrics
                metrics.append(MetricValue("process_gc_objects_total", current_metrics.gc_objects))
                
                # GC collections by generation
                for generation, count in current_metrics.gc_collections.items():
                    metrics.append(MetricValue(
                        name="process_gc_collections_total",
                        value=count,
                        labels={"generation": str(generation)}
                    ))
            
            # Function instrumentation metrics
            function_metrics = self.instrumentor.get_function_metrics()
            
            for func_key, func_stats in function_metrics.items():
                base_labels = {
                    "function": func_stats.name,
                    "module": func_stats.module
                }
                
                # Function call counts
                metrics.append(MetricValue(
                    name="process_function_calls_total",
                    value=func_stats.call_count,
                    labels=base_labels
                ))
                
                # Function duration statistics
                if func_stats.call_count > 0:
                    duration_metrics = [
                        ("avg", func_stats.avg_time),
                        ("min", func_stats.min_time if func_stats.min_time != float('inf') else 0),
                        ("max", func_stats.max_time)
                    ]
                    
                    for stat_name, stat_value in duration_metrics:
                        metrics.append(MetricValue(
                            name="process_function_duration_seconds",
                            value=stat_value,
                            labels={**base_labels, "stat": stat_name}
                        ))
                
                # Function errors
                if func_stats.errors > 0:
                    metrics.append(MetricValue(
                        name="process_function_errors_total",
                        value=func_stats.errors,
                        labels=base_labels
                    ))
            
            # Instrumentation overhead
            overhead = self.instrumentor.get_overhead_percentage()
            metrics.append(MetricValue(
                name="process_instrumentation_overhead_percent",
                value=overhead
            ))
            
            # Custom metrics
            for metric_name, metric_data in self.instrumentor.custom_metrics.items():
                if isinstance(metric_data['value'], (int, float)):
                    metrics.append(MetricValue(
                        name="process_custom_metric",
                        value=metric_data['value'],
                        labels={"metric_name": metric_name}
                    ))
            
        except Exception as e:
            logger.error(f"Error collecting process metrics: {e}")
        
        return metrics
    
    def set_instrumentor(self, instrumentor: ProcessInstrumentor):
        """Set the process instrumentor"""
        self.instrumentor = instrumentor
        logger.info(f"Updated instrumentor for app: {instrumentor.app_name}")
    
    def get_instrumentation_summary(self) -> Dict[str, Any]:
        """Get comprehensive instrumentation summary"""
        return self.instrumentor.get_summary_stats()
    
    async def start_instrumentation(self):
        """Start the underlying process instrumentation"""
        await self.instrumentor.start_monitoring()
        logger.info("Process instrumentation started")
    
    async def stop_instrumentation(self):
        """Stop the underlying process instrumentation"""
        await self.instrumentor.stop_monitoring()
        logger.info("Process instrumentation stopped")
    
    def cleanup(self):
        """Cleanup resources"""
        super().cleanup()
        self.instrumentor.cleanup()
