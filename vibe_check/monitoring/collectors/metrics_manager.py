"""
Metrics Manager
==============

Central manager that coordinates metrics collection and ingestion into the TSDB.
Connects collectors to the time-series storage engine for real-time monitoring.
"""

import asyncio
import time
import logging
from typing import List, Dict, Optional, Any, Callable
from pathlib import Path
from dataclasses import dataclass

from .base_collector import MetricsCollector, MetricValue, MetricsRegistry, get_metrics_registry
from .system_collector import SystemMetricsCollector
from .code_quality_collector import CodeQualityMetricsCollector
from vibe_check.monitoring.storage.time_series_engine import TimeSeriesStorageEngine, TSDBConfig

logger = logging.getLogger(__name__)


@dataclass
class MetricsManagerConfig:
    """Configuration for the metrics manager"""
    # TSDB settings
    tsdb_data_dir: Path = Path(".vibe_check_monitoring")
    tsdb_retention_days: int = 30
    
    # Collection settings
    enable_system_metrics: bool = True
    enable_code_quality_metrics: bool = True
    code_quality_projects: List[Path] = None
    
    # Performance settings
    max_ingestion_batch_size: int = 1000
    ingestion_flush_interval: float = 5.0
    
    # Monitoring settings
    enable_self_monitoring: bool = True
    stats_collection_interval: float = 60.0
    
    def __post_init__(self):
        if self.code_quality_projects is None:
            self.code_quality_projects = []


class MetricsManager:
    """Central manager for metrics collection and storage"""
    
    def __init__(self, config: Optional[MetricsManagerConfig] = None):
        self.config = config or MetricsManagerConfig()
        
        # Initialize TSDB
        tsdb_config = TSDBConfig(
            data_dir=self.config.tsdb_data_dir,
            retention_days=self.config.tsdb_retention_days,
            max_ingestion_rate=5000,  # High rate for monitoring
            write_batch_size=self.config.max_ingestion_batch_size,
            flush_interval_seconds=self.config.ingestion_flush_interval
        )
        self.tsdb: Optional[TimeSeriesStorageEngine] = None
        self.tsdb_config = tsdb_config
        
        # Metrics registry
        self.registry = get_metrics_registry()
        
        # Collectors
        self.system_collector: Optional[SystemMetricsCollector] = None
        self.code_quality_collector: Optional[CodeQualityMetricsCollector] = None
        
        # Ingestion buffer
        self.ingestion_buffer: List[MetricValue] = []
        self.buffer_lock = asyncio.Lock()
        
        # Background tasks
        self.ingestion_task: Optional[asyncio.Task] = None
        self.stats_task: Optional[asyncio.Task] = None
        self.shutdown_event = asyncio.Event()
        
        # Statistics
        self.metrics_ingested = 0
        self.ingestion_errors = 0
        self.last_ingestion_time = 0.0
        self.start_time = time.time()
        
        logger.info("Initialized MetricsManager")
    
    async def start(self):
        """Start the metrics manager and all collectors"""
        try:
            # Initialize TSDB
            from ..storage.time_series_engine import create_tsdb
            self.tsdb = await create_tsdb(self.tsdb_config)
            logger.info("TSDB initialized")
            
            # Setup collectors
            await self._setup_collectors()
            
            # Connect metrics handler
            self.registry.add_metrics_handler(self._handle_metrics)
            
            # Start background tasks
            self.ingestion_task = asyncio.create_task(self._ingestion_loop())
            
            if self.config.enable_self_monitoring:
                self.stats_task = asyncio.create_task(self._stats_loop())
            
            # Start all collectors
            await self.registry.start_all_collectors()
            
            logger.info("MetricsManager started successfully")
            
        except Exception as e:
            logger.error(f"Error starting MetricsManager: {e}")
            raise
    
    async def stop(self):
        """Stop the metrics manager and all collectors"""
        try:
            # Signal shutdown
            self.shutdown_event.set()
            
            # Stop collectors
            await self.registry.stop_all_collectors()
            
            # Cancel background tasks
            if self.ingestion_task:
                self.ingestion_task.cancel()
                try:
                    await self.ingestion_task
                except asyncio.CancelledError:
                    pass
            
            if self.stats_task:
                self.stats_task.cancel()
                try:
                    await self.stats_task
                except asyncio.CancelledError:
                    pass
            
            # Final flush
            await self._flush_ingestion_buffer()
            
            # Shutdown TSDB
            if self.tsdb:
                await self.tsdb.shutdown()
            
            logger.info("MetricsManager stopped")
            
        except Exception as e:
            logger.error(f"Error stopping MetricsManager: {e}")
    
    async def _setup_collectors(self):
        """Setup and register collectors"""
        # System metrics collector
        if self.config.enable_system_metrics:
            self.system_collector = SystemMetricsCollector()
            self.registry.register_collector(self.system_collector)
            logger.info("System metrics collector registered")
        
        # Code quality metrics collector
        if self.config.enable_code_quality_metrics and self.config.code_quality_projects:
            self.code_quality_collector = CodeQualityMetricsCollector(
                self.config.code_quality_projects
            )
            self.registry.register_collector(self.code_quality_collector)
            logger.info(f"Code quality metrics collector registered for {len(self.config.code_quality_projects)} projects")
    
    def _handle_metrics(self, metrics: List[MetricValue]):
        """Handle metrics from collectors"""
        asyncio.create_task(self._queue_metrics_for_ingestion(metrics))
    
    async def _queue_metrics_for_ingestion(self, metrics: List[MetricValue]):
        """Queue metrics for ingestion into TSDB"""
        async with self.buffer_lock:
            self.ingestion_buffer.extend(metrics)
            
            # Flush if buffer is getting large
            if len(self.ingestion_buffer) >= self.config.max_ingestion_batch_size:
                await self._flush_ingestion_buffer()
    
    async def _ingestion_loop(self):
        """Background loop for ingesting metrics into TSDB"""
        while not self.shutdown_event.is_set():
            try:
                await asyncio.wait_for(
                    self.shutdown_event.wait(),
                    timeout=self.config.ingestion_flush_interval
                )
            except asyncio.TimeoutError:
                # Normal timeout - flush buffer
                await self._flush_ingestion_buffer()
            except asyncio.CancelledError:
                break
    
    async def _flush_ingestion_buffer(self):
        """Flush metrics buffer to TSDB"""
        if not self.tsdb:
            return
        
        async with self.buffer_lock:
            if not self.ingestion_buffer:
                return
            
            metrics_to_ingest = self.ingestion_buffer.copy()
            self.ingestion_buffer.clear()
        
        try:
            ingestion_start = time.time()
            
            # Convert MetricValue objects to TSDB format
            for metric in metrics_to_ingest:
                await self.tsdb.ingest_sample(
                    metric.name,
                    metric.value,
                    metric.labels,
                    metric.timestamp
                )
            
            ingestion_time = time.time() - ingestion_start
            self.last_ingestion_time = ingestion_time
            self.metrics_ingested += len(metrics_to_ingest)
            
            logger.debug(f"Ingested {len(metrics_to_ingest)} metrics in {ingestion_time:.3f}s")
            
        except Exception as e:
            self.ingestion_errors += 1
            logger.error(f"Error ingesting metrics: {e}")
    
    async def _stats_loop(self):
        """Background loop for collecting manager statistics"""
        while not self.shutdown_event.is_set():
            try:
                await self._collect_self_metrics()
                
                await asyncio.wait_for(
                    self.shutdown_event.wait(),
                    timeout=self.config.stats_collection_interval
                )
            except asyncio.TimeoutError:
                continue
            except asyncio.CancelledError:
                break
    
    async def _collect_self_metrics(self):
        """Collect metrics about the metrics manager itself"""
        if not self.tsdb:
            return
        
        try:
            current_time = time.time()
            uptime = current_time - self.start_time
            
            # Manager statistics
            manager_metrics = [
                MetricValue(
                    name="metrics_manager_uptime_seconds",
                    value=uptime,
                    labels={"component": "metrics_manager"}
                ),
                MetricValue(
                    name="metrics_manager_metrics_ingested_total",
                    value=self.metrics_ingested,
                    labels={"component": "metrics_manager"}
                ),
                MetricValue(
                    name="metrics_manager_ingestion_errors_total",
                    value=self.ingestion_errors,
                    labels={"component": "metrics_manager"}
                ),
                MetricValue(
                    name="metrics_manager_last_ingestion_duration_seconds",
                    value=self.last_ingestion_time,
                    labels={"component": "metrics_manager"}
                )
            ]
            
            # Buffer statistics
            async with self.buffer_lock:
                buffer_size = len(self.ingestion_buffer)
            
            manager_metrics.append(MetricValue(
                name="metrics_manager_buffer_size",
                value=buffer_size,
                labels={"component": "metrics_manager"}
            ))
            
            # TSDB statistics
            if self.tsdb:
                tsdb_stats = self.tsdb.get_stats()
                for stat_name, stat_value in tsdb_stats.items():
                    if isinstance(stat_value, (int, float)):
                        manager_metrics.append(MetricValue(
                            name=f"metrics_manager_tsdb_{stat_name}",
                            value=stat_value,
                            labels={"component": "tsdb"}
                        ))
            
            # Collector statistics
            registry_stats = self.registry.get_registry_stats()
            for collector_name, collector_stats in registry_stats.get("collectors", {}).items():
                for stat_name, stat_value in collector_stats.items():
                    if isinstance(stat_value, (int, float)):
                        manager_metrics.append(MetricValue(
                            name=f"metrics_manager_collector_{stat_name}",
                            value=stat_value,
                            labels={"component": "collector", "collector": collector_name}
                        ))
            
            # Ingest self-metrics
            await self._queue_metrics_for_ingestion(manager_metrics)
            
        except Exception as e:
            logger.error(f"Error collecting self-metrics: {e}")
    
    def add_code_quality_project(self, project_path: Path):
        """Add a project for code quality monitoring"""
        if project_path not in self.config.code_quality_projects:
            self.config.code_quality_projects.append(project_path)
            
            if self.code_quality_collector:
                self.code_quality_collector.add_project_path(project_path)
            
            logger.info(f"Added code quality monitoring for: {project_path}")
    
    def remove_code_quality_project(self, project_path: Path):
        """Remove a project from code quality monitoring"""
        if project_path in self.config.code_quality_projects:
            self.config.code_quality_projects.remove(project_path)
            
            if self.code_quality_collector:
                self.code_quality_collector.remove_project_path(project_path)
            
            logger.info(f"Removed code quality monitoring for: {project_path}")
    
    async def query_metrics(self, metric_name: str, start_time: float, end_time: float,
                          labels: Optional[Dict[str, str]] = None):
        """Query metrics from TSDB"""
        if not self.tsdb:
            raise RuntimeError("TSDB not initialized")
        
        return await self.tsdb.query_range(metric_name, start_time, end_time, labels)
    
    def get_manager_stats(self) -> Dict[str, Any]:
        """Get comprehensive manager statistics"""
        uptime = time.time() - self.start_time
        ingestion_rate = self.metrics_ingested / uptime if uptime > 0 else 0
        
        stats = {
            'uptime_seconds': uptime,
            'metrics_ingested': self.metrics_ingested,
            'ingestion_errors': self.ingestion_errors,
            'ingestion_rate': ingestion_rate,
            'last_ingestion_time': self.last_ingestion_time,
            'buffer_size': len(self.ingestion_buffer),
            'collectors': self.registry.get_registry_stats(),
        }
        
        if self.tsdb:
            stats['tsdb'] = self.tsdb.get_stats()
        
        return stats


# Global metrics manager instance
_metrics_manager: Optional[MetricsManager] = None


async def get_metrics_manager(config: Optional[MetricsManagerConfig] = None) -> MetricsManager:
    """Get or create the global metrics manager"""
    global _metrics_manager
    
    if _metrics_manager is None:
        _metrics_manager = MetricsManager(config)
        await _metrics_manager.start()
    
    return _metrics_manager


async def shutdown_metrics_manager():
    """Shutdown the global metrics manager"""
    global _metrics_manager
    
    if _metrics_manager:
        await _metrics_manager.stop()
        _metrics_manager = None
