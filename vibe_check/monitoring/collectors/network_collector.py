"""
Network Performance Monitoring Collector
========================================

This module provides comprehensive network performance monitoring capabilities
including latency, bandwidth utilization, and connection tracking.

File: vibe_check/monitoring/collectors/network_collector.py
Purpose: Monitor network performance metrics for system observability
Related Files: base_collector.py, system_collector.py
Dependencies: psutil, socket, time
"""

import asyncio
import logging
import socket
import time
from dataclasses import dataclass
from typing import Dict, List, Optional, Any, Union
import subprocess
import platform

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    psutil = None

try:
    # Try relative imports first
    from .base_collector import (
        MetricsCollector, MetricDefinition, MetricValue, MetricType, 
        CollectorConfig, CollectionInterval
    )
except ImportError:
    # Fallback to absolute imports for testing
    import sys
    from pathlib import Path
    
    # Add the project root to Python path
    project_root = Path(__file__).parent.parent.parent.parent
    sys.path.insert(0, str(project_root))
    
    from vibe_check.monitoring.collectors.base_collector import (
        MetricsCollector, MetricDefinition, MetricValue, MetricType, 
        CollectorConfig, CollectionInterval
    )

logger = logging.getLogger(__name__)


@dataclass
class NetworkInterface:
    """Network interface information"""
    name: str
    bytes_sent: int = 0
    bytes_recv: int = 0
    packets_sent: int = 0
    packets_recv: int = 0
    errors_in: int = 0
    errors_out: int = 0
    drops_in: int = 0
    drops_out: int = 0


@dataclass
class NetworkConnection:
    """Network connection information"""
    local_address: str
    local_port: int
    remote_address: str
    remote_port: int
    status: str
    pid: Optional[int] = None


class NetworkMetricsCollector(MetricsCollector):
    """Collector for network performance metrics"""
    
    def __init__(self, config: Optional[CollectorConfig] = None):
        # Default to normal collection for network metrics
        if config is None:
            config = CollectorConfig(
                collection_interval=CollectionInterval.NORMAL.value,
                enabled=True
            )
        
        super().__init__(config)
        
        # Network monitoring state
        self.previous_stats: Dict[str, NetworkInterface] = {}
        self.previous_timestamp: Optional[float] = None
        
        # Connection tracking
        self.connection_count_history: List[int] = []
        self.max_history_size = 100
        
        logger.info("Initialized NetworkMetricsCollector")
    
    def _register_metrics(self):
        """Register network metrics"""
        # Interface metrics
        self.register_metric(MetricDefinition(
            name="network_bytes_sent_total",
            metric_type=MetricType.COUNTER,
            description="Total bytes sent across all network interfaces",
            labels={"interface"}
        ))
        
        self.register_metric(MetricDefinition(
            name="network_bytes_received_total",
            metric_type=MetricType.COUNTER,
            description="Total bytes received across all network interfaces",
            labels={"interface"}
        ))
        
        self.register_metric(MetricDefinition(
            name="network_packets_sent_total",
            metric_type=MetricType.COUNTER,
            description="Total packets sent across all network interfaces",
            labels={"interface"}
        ))
        
        self.register_metric(MetricDefinition(
            name="network_packets_received_total",
            metric_type=MetricType.COUNTER,
            description="Total packets received across all network interfaces",
            labels={"interface"}
        ))
        
        self.register_metric(MetricDefinition(
            name="network_errors_total",
            metric_type=MetricType.COUNTER,
            description="Total network errors",
            labels={"interface", "direction"}
        ))
        
        self.register_metric(MetricDefinition(
            name="network_drops_total",
            metric_type=MetricType.COUNTER,
            description="Total network packet drops",
            labels={"interface", "direction"}
        ))
        
        # Bandwidth metrics
        self.register_metric(MetricDefinition(
            name="network_bandwidth_bytes_per_second",
            metric_type=MetricType.GAUGE,
            description="Network bandwidth utilization in bytes per second",
            labels={"interface", "direction"}
        ))
        
        # Connection metrics
        self.register_metric(MetricDefinition(
            name="network_connections_total",
            metric_type=MetricType.GAUGE,
            description="Total number of network connections",
            labels={"status"}
        ))
        
        # Latency metrics (basic ping-based)
        self.register_metric(MetricDefinition(
            name="network_latency_seconds",
            metric_type=MetricType.GAUGE,
            description="Network latency to common targets",
            labels={"target"}
        ))
    
    async def collect_metrics(self) -> List[MetricValue]:
        """Collect network performance metrics"""
        metrics = []
        current_time = time.time()
        
        try:
            # Collect interface statistics
            interface_metrics = await self._collect_interface_metrics(current_time)
            metrics.extend(interface_metrics)
            
            # Collect connection statistics
            connection_metrics = await self._collect_connection_metrics(current_time)
            metrics.extend(connection_metrics)
            
            # Collect basic latency metrics
            latency_metrics = await self._collect_latency_metrics(current_time)
            metrics.extend(latency_metrics)
            
        except Exception as e:
            logger.error(f"Error collecting network metrics: {e}")
        
        return metrics
    
    async def _collect_interface_metrics(self, current_time: float) -> List[MetricValue]:
        """Collect network interface metrics"""
        metrics = []
        
        if not PSUTIL_AVAILABLE:
            logger.warning("psutil not available, skipping interface metrics")
            return metrics
        
        try:
            # Get network interface statistics
            net_io = psutil.net_io_counters(pernic=True)
            
            for interface_name, stats in net_io.items():
                # Skip loopback interface
                if interface_name.startswith('lo'):
                    continue
                
                interface = NetworkInterface(
                    name=interface_name,
                    bytes_sent=stats.bytes_sent,
                    bytes_recv=stats.bytes_recv,
                    packets_sent=stats.packets_sent,
                    packets_recv=stats.packets_recv,
                    errors_in=stats.errin,
                    errors_out=stats.errout,
                    drops_in=stats.dropin,
                    drops_out=stats.dropout
                )
                
                # Basic metrics
                metrics.extend([
                    MetricValue(
                        name="network_bytes_sent_total",
                        value=float(interface.bytes_sent),
                        labels={"interface": interface_name},
                        timestamp=current_time
                    ),
                    MetricValue(
                        name="network_bytes_received_total",
                        value=float(interface.bytes_recv),
                        labels={"interface": interface_name},
                        timestamp=current_time
                    ),
                    MetricValue(
                        name="network_packets_sent_total",
                        value=float(interface.packets_sent),
                        labels={"interface": interface_name},
                        timestamp=current_time
                    ),
                    MetricValue(
                        name="network_packets_received_total",
                        value=float(interface.packets_recv),
                        labels={"interface": interface_name},
                        timestamp=current_time
                    ),
                    MetricValue(
                        name="network_errors_total",
                        value=float(interface.errors_in),
                        labels={"interface": interface_name, "direction": "in"},
                        timestamp=current_time
                    ),
                    MetricValue(
                        name="network_errors_total",
                        value=float(interface.errors_out),
                        labels={"interface": interface_name, "direction": "out"},
                        timestamp=current_time
                    ),
                    MetricValue(
                        name="network_drops_total",
                        value=float(interface.drops_in),
                        labels={"interface": interface_name, "direction": "in"},
                        timestamp=current_time
                    ),
                    MetricValue(
                        name="network_drops_total",
                        value=float(interface.drops_out),
                        labels={"interface": interface_name, "direction": "out"},
                        timestamp=current_time
                    )
                ])
                
                # Calculate bandwidth if we have previous data
                if (self.previous_stats and interface_name in self.previous_stats 
                    and self.previous_timestamp):
                    
                    time_delta = current_time - self.previous_timestamp
                    if time_delta > 0:
                        prev_interface = self.previous_stats[interface_name]
                        
                        # Calculate bytes per second
                        bytes_sent_rate = (interface.bytes_sent - prev_interface.bytes_sent) / time_delta
                        bytes_recv_rate = (interface.bytes_recv - prev_interface.bytes_recv) / time_delta
                        
                        metrics.extend([
                            MetricValue(
                                name="network_bandwidth_bytes_per_second",
                                value=max(0.0, bytes_sent_rate),  # Ensure non-negative
                                labels={"interface": interface_name, "direction": "out"},
                                timestamp=current_time
                            ),
                            MetricValue(
                                name="network_bandwidth_bytes_per_second",
                                value=max(0.0, bytes_recv_rate),  # Ensure non-negative
                                labels={"interface": interface_name, "direction": "in"},
                                timestamp=current_time
                            )
                        ])
                
                # Store current stats for next calculation
                self.previous_stats[interface_name] = interface
            
            self.previous_timestamp = current_time
            
        except Exception as e:
            logger.error(f"Error collecting interface metrics: {e}")
        
        return metrics
    
    async def _collect_connection_metrics(self, current_time: float) -> List[MetricValue]:
        """Collect network connection metrics"""
        metrics = []
        
        if not PSUTIL_AVAILABLE:
            logger.warning("psutil not available, skipping connection metrics")
            return metrics
        
        try:
            # Get network connections
            connections = psutil.net_connections()
            
            # Count connections by status
            status_counts: Dict[str, int] = {}
            for conn in connections:
                status = conn.status if conn.status else "UNKNOWN"
                status_counts[status] = status_counts.get(status, 0) + 1
            
            # Create metrics for each status
            for status, count in status_counts.items():
                metrics.append(MetricValue(
                    name="network_connections_total",
                    value=float(count),
                    labels={"status": status},
                    timestamp=current_time
                ))
            
            # Track connection count history for trend analysis
            total_connections = len(connections)
            self.connection_count_history.append(total_connections)
            if len(self.connection_count_history) > self.max_history_size:
                self.connection_count_history.pop(0)
            
        except Exception as e:
            logger.error(f"Error collecting connection metrics: {e}")
        
        return metrics
    
    async def _collect_latency_metrics(self, current_time: float) -> List[MetricValue]:
        """Collect basic network latency metrics"""
        metrics = []
        
        # Common targets for latency testing
        targets = [
            ("google_dns", "*******"),
            ("cloudflare_dns", "*******"),
            ("localhost", "127.0.0.1")
        ]
        
        for target_name, target_ip in targets:
            try:
                latency = await self._ping_target(target_ip)
                if latency is not None:
                    metrics.append(MetricValue(
                        name="network_latency_seconds",
                        value=latency,
                        labels={"target": target_name},
                        timestamp=current_time
                    ))
            except Exception as e:
                logger.debug(f"Error pinging {target_name} ({target_ip}): {e}")
        
        return metrics
    
    async def _ping_target(self, target_ip: str, timeout: float = 1.0) -> Optional[float]:
        """Ping a target and return latency in seconds"""
        try:
            # Use socket-based approach for cross-platform compatibility
            start_time = time.time()
            
            # Create a socket and attempt to connect
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            
            try:
                # Try to connect to port 80 (HTTP)
                result = sock.connect_ex((target_ip, 80))
                end_time = time.time()
                
                if result == 0:  # Connection successful
                    return end_time - start_time
                else:
                    return None
            finally:
                sock.close()
                
        except Exception as e:
            logger.debug(f"Socket ping to {target_ip} failed: {e}")
            return None
    
    def get_network_summary(self) -> Dict[str, Any]:
        """Get comprehensive network summary"""
        summary = {
            "interfaces_monitored": len(self.previous_stats),
            "connection_history_size": len(self.connection_count_history),
            "psutil_available": PSUTIL_AVAILABLE
        }
        
        if self.connection_count_history:
            summary.update({
                "current_connections": self.connection_count_history[-1],
                "avg_connections": sum(self.connection_count_history) / len(self.connection_count_history),
                "max_connections": max(self.connection_count_history),
                "min_connections": min(self.connection_count_history)
            })
        
        return summary
