"""
Memory Metrics Collector
========================

Collector that integrates memory usage tracking with the metrics collection framework.
Bridges memory tracking data to the time-series storage system.
"""

import asyncio
import time
from typing import List, Dict, Optional, Any
from pathlib import Path
import logging

try:
    # Try relative imports first
    from .base_collector import (
        MetricsCollector, MetricDefinition, MetricValue, MetricType, 
        CollectorConfig, CollectionInterval
    )
    from ..memory.memory_tracker import MemoryTracker, get_memory_tracker
except ImportError:
    # Fallback to absolute imports for testing
    import sys
    from pathlib import Path
    
    # Add project root to path
    project_root = Path(__file__).parent.parent.parent.parent
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))
    
    from vibe_check.monitoring.collectors.base_collector import (
        MetricsCollector, MetricDefinition, MetricValue, MetricType, 
        CollectorConfig, CollectionInterval
    )
    from vibe_check.monitoring.memory.memory_tracker import MemoryTracker, get_memory_tracker

logger = logging.getLogger(__name__)


class MemoryMetricsCollector(MetricsCollector):
    """Collector for memory usage metrics"""
    
    def __init__(self, memory_tracker: Optional[MemoryTracker] = None,
                 config: Optional[CollectorConfig] = None):
        # Default to normal collection for memory metrics
        if config is None:
            config = CollectorConfig(
                collection_interval=CollectionInterval.NORMAL.value,
                max_collection_time=8.0,
                labels={"collector": "memory"}
            )
        
        super().__init__(config)
        
        # Use provided tracker or get global one
        self.memory_tracker = memory_tracker or get_memory_tracker()
        
        logger.info("Initialized MemoryMetricsCollector")
    
    def _register_metrics(self):
        """Register memory metrics"""
        # Memory allocation metrics
        self.register_metric(MetricDefinition(
            name="memory_allocations_total",
            metric_type=MetricType.COUNTER,
            description="Total memory allocations tracked",
            unit="bytes"
        ))
        
        self.register_metric(MetricDefinition(
            name="memory_freed_total",
            metric_type=MetricType.COUNTER,
            description="Total memory freed",
            unit="bytes"
        ))
        
        self.register_metric(MetricDefinition(
            name="memory_net_allocated",
            metric_type=MetricType.GAUGE,
            description="Net memory allocated (allocated - freed)",
            unit="bytes"
        ))
        
        # Function-level memory metrics
        self.register_metric(MetricDefinition(
            name="memory_function_allocated_bytes",
            metric_type=MetricType.GAUGE,
            description="Memory allocated by function",
            labels={"function": "", "module": ""},
            unit="bytes"
        ))
        
        self.register_metric(MetricDefinition(
            name="memory_function_peak_bytes",
            metric_type=MetricType.GAUGE,
            description="Peak memory allocation by function",
            labels={"function": "", "module": ""},
            unit="bytes"
        ))
        
        self.register_metric(MetricDefinition(
            name="memory_function_calls_total",
            metric_type=MetricType.COUNTER,
            description="Number of tracked function calls",
            labels={"function": "", "module": ""}
        ))
        
        self.register_metric(MetricDefinition(
            name="memory_function_avg_per_call_bytes",
            metric_type=MetricType.GAUGE,
            description="Average memory allocation per function call",
            labels={"function": "", "module": ""},
            unit="bytes"
        ))
        
        # Memory leak metrics
        self.register_metric(MetricDefinition(
            name="memory_leaks_detected_total",
            metric_type=MetricType.COUNTER,
            description="Total memory leaks detected"
        ))
        
        self.register_metric(MetricDefinition(
            name="memory_leak_size_bytes",
            metric_type=MetricType.GAUGE,
            description="Size of detected memory leak",
            labels={"function": "", "module": ""},
            unit="bytes"
        ))
        
        self.register_metric(MetricDefinition(
            name="memory_leak_rate_bytes_per_second",
            metric_type=MetricType.GAUGE,
            description="Memory leak rate",
            labels={"function": "", "module": ""},
            unit="bytes_per_second"
        ))
        
        self.register_metric(MetricDefinition(
            name="memory_leak_confidence",
            metric_type=MetricType.GAUGE,
            description="Confidence level of leak detection",
            labels={"function": "", "module": ""},
            unit="ratio"
        ))
        
        # Garbage collection metrics
        self.register_metric(MetricDefinition(
            name="gc_collections_total",
            metric_type=MetricType.COUNTER,
            description="Total garbage collections",
            labels={"generation": ""}
        ))
        
        self.register_metric(MetricDefinition(
            name="gc_objects_collected_total",
            metric_type=MetricType.COUNTER,
            description="Total objects collected by GC",
            labels={"generation": ""}
        ))
        
        self.register_metric(MetricDefinition(
            name="gc_uncollectable_objects_total",
            metric_type=MetricType.COUNTER,
            description="Total uncollectable objects",
            labels={"generation": ""}
        ))
        
        self.register_metric(MetricDefinition(
            name="gc_objects_current",
            metric_type=MetricType.GAUGE,
            description="Current number of objects tracked by GC"
        ))
        
        # Memory snapshot metrics
        self.register_metric(MetricDefinition(
            name="memory_current_usage_bytes",
            metric_type=MetricType.GAUGE,
            description="Current memory usage",
            unit="bytes"
        ))
        
        self.register_metric(MetricDefinition(
            name="memory_peak_usage_bytes",
            metric_type=MetricType.GAUGE,
            description="Peak memory usage",
            unit="bytes"
        ))
        
        self.register_metric(MetricDefinition(
            name="memory_snapshots_taken_total",
            metric_type=MetricType.COUNTER,
            description="Total memory snapshots taken"
        ))
        
        # Tracking status metrics
        self.register_metric(MetricDefinition(
            name="memory_tracking_active",
            metric_type=MetricType.GAUGE,
            description="Whether memory tracking is active",
            unit="boolean"
        ))
        
        self.register_metric(MetricDefinition(
            name="memory_tracked_functions_total",
            metric_type=MetricType.GAUGE,
            description="Number of functions being tracked"
        ))
    
    async def collect_metrics(self) -> List[MetricValue]:
        """Collect memory metrics"""
        metrics = []
        
        try:
            # Get memory report
            report = self.memory_tracker.get_memory_report()
            
            if 'error' in report:
                logger.warning(f"Memory tracking data not available: {report['error']}")
                return metrics
            
            summary = report.get('summary', {})
            
            # Summary metrics
            metrics.extend([
                MetricValue(
                    name="memory_allocations_total",
                    value=summary.get('total_allocated', 0)
                ),
                MetricValue(
                    name="memory_freed_total",
                    value=summary.get('total_freed', 0)
                ),
                MetricValue(
                    name="memory_net_allocated",
                    value=summary.get('net_allocated', 0)
                ),
                MetricValue(
                    name="memory_leaks_detected_total",
                    value=summary.get('detected_leaks', 0)
                ),
                MetricValue(
                    name="memory_snapshots_taken_total",
                    value=summary.get('snapshots_taken', 0)
                ),
                MetricValue(
                    name="memory_tracking_active",
                    value=1 if self.memory_tracker.is_tracking else 0
                ),
                MetricValue(
                    name="memory_tracked_functions_total",
                    value=summary.get('tracked_functions', 0)
                )
            ])
            
            # Function-level metrics
            top_consumers = report.get('top_consumers', [])
            
            for consumer in top_consumers:
                base_labels = {
                    "function": consumer.get('function_name', 'unknown'),
                    "module": consumer.get('module_name', 'unknown')
                }
                
                metrics.extend([
                    MetricValue(
                        name="memory_function_allocated_bytes",
                        value=consumer.get('total_allocated', 0),
                        labels=base_labels
                    ),
                    MetricValue(
                        name="memory_function_peak_bytes",
                        value=consumer.get('peak_allocated', 0),
                        labels=base_labels
                    ),
                    MetricValue(
                        name="memory_function_calls_total",
                        value=consumer.get('call_count', 0),
                        labels=base_labels
                    ),
                    MetricValue(
                        name="memory_function_avg_per_call_bytes",
                        value=consumer.get('avg_per_call', 0.0),
                        labels=base_labels
                    )
                ])
            
            # Memory leak metrics
            detected_leaks = report.get('detected_leaks', [])
            
            for leak in detected_leaks:
                leak_labels = {
                    "function": leak.get('function_name', 'unknown'),
                    "module": leak.get('module_name', 'unknown')
                }
                
                metrics.extend([
                    MetricValue(
                        name="memory_leak_size_bytes",
                        value=leak.get('leak_size', 0),
                        labels=leak_labels
                    ),
                    MetricValue(
                        name="memory_leak_rate_bytes_per_second",
                        value=leak.get('leak_rate', 0.0),
                        labels=leak_labels
                    ),
                    MetricValue(
                        name="memory_leak_confidence",
                        value=leak.get('confidence', 0.0),
                        labels=leak_labels
                    )
                ])
            
            # Garbage collection metrics
            gc_stats = report.get('gc_stats', [])
            
            # Aggregate GC stats by generation
            gc_by_generation = {}
            for stat in gc_stats:
                gen = stat.get('generation', 0)
                if gen not in gc_by_generation:
                    gc_by_generation[gen] = {
                        'collections': 0,
                        'collected': 0,
                        'uncollectable': 0
                    }
                
                gc_by_generation[gen]['collections'] += stat.get('collections', 0)
                gc_by_generation[gen]['collected'] += stat.get('collected', 0)
                gc_by_generation[gen]['uncollectable'] += stat.get('uncollectable', 0)
            
            for generation, stats in gc_by_generation.items():
                gen_labels = {"generation": str(generation)}
                
                metrics.extend([
                    MetricValue(
                        name="gc_collections_total",
                        value=stats['collections'],
                        labels=gen_labels
                    ),
                    MetricValue(
                        name="gc_objects_collected_total",
                        value=stats['collected'],
                        labels=gen_labels
                    ),
                    MetricValue(
                        name="gc_uncollectable_objects_total",
                        value=stats['uncollectable'],
                        labels=gen_labels
                    )
                ])
            
            # Memory snapshot metrics
            recent_snapshots = report.get('recent_snapshots', [])
            
            if recent_snapshots:
                latest_snapshot = recent_snapshots[-1]
                
                metrics.extend([
                    MetricValue(
                        name="memory_current_usage_bytes",
                        value=latest_snapshot.get('total_memory', 0)
                    ),
                    MetricValue(
                        name="memory_peak_usage_bytes",
                        value=latest_snapshot.get('peak_memory', 0)
                    ),
                    MetricValue(
                        name="gc_objects_current",
                        value=latest_snapshot.get('gc_objects', 0)
                    )
                ])
            
        except Exception as e:
            logger.error(f"Error collecting memory metrics: {e}")
        
        return metrics
    
    def set_memory_tracker(self, tracker: MemoryTracker):
        """Set the memory tracker"""
        self.memory_tracker = tracker
        logger.info("Updated memory tracker")
    
    def get_memory_summary(self) -> Dict[str, Any]:
        """Get comprehensive memory summary"""
        report = self.memory_tracker.get_memory_report()
        
        if 'error' in report:
            return {'error': report['error']}
        
        summary = report.get('summary', {})
        
        return {
            'tracking_active': self.memory_tracker.is_tracking,
            'total_allocated': summary.get('total_allocated', 0),
            'total_freed': summary.get('total_freed', 0),
            'net_allocated': summary.get('net_allocated', 0),
            'tracked_functions': summary.get('tracked_functions', 0),
            'detected_leaks': summary.get('detected_leaks', 0),
            'snapshots_taken': summary.get('snapshots_taken', 0),
            'top_consumers': len(report.get('top_consumers', [])),
            'gc_stats_available': len(report.get('gc_stats', [])) > 0
        }
    
    def cleanup(self):
        """Cleanup resources"""
        super().cleanup()
