"""
Profiling Metrics Collector
===========================

Collector that integrates execution time profiling with the metrics collection framework.
Bridges profiling data to the time-series storage system.
"""

import asyncio
import time
from typing import List, Dict, Optional, Any
from pathlib import Path
import logging

# Use absolute imports to avoid relative import issues
import sys
from pathlib import Path

# Ensure project root is in path for imports
project_root = Path(__file__).parent.parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

try:
    from vibe_check.monitoring.collectors.base_collector import (
        MetricsCollector, MetricDefinition, MetricValue, MetricType,
        CollectorConfig, CollectionInterval
    )
    from vibe_check.monitoring.profiling.execution_profiler import ExecutionProfiler, get_profiler
except ImportError:
    # Fallback for direct testing
    try:
        from .base_collector import (
            MetricsCollector, MetricDefinition, MetricValue, MetricType,
            CollectorConfig, CollectionInterval
        )
        from ..profiling.execution_profiler import ExecutionProfiler, get_profiler
    except ImportError:
        # Final fallback for isolated testing
        from base_collector import (
            MetricsCollector, MetricDefinition, MetricValue, MetricType,
            CollectorConfig, CollectionInterval
        )
        from profiling.execution_profiler import ExecutionProfiler, get_profiler

logger = logging.getLogger(__name__)


class ProfilingMetricsCollector(MetricsCollector):
    """Collector for execution profiling metrics"""
    
    def __init__(self, profiler: Optional[ExecutionProfiler] = None,
                 config: Optional[CollectorConfig] = None):
        # Default to slower collection for profiling metrics
        if config is None:
            config = CollectorConfig(
                collection_interval=CollectionInterval.SLOW.value,
                max_collection_time=10.0,
                labels={"collector": "profiling"}
            )
        
        super().__init__(config)
        
        # Use provided profiler or get global one
        self.profiler = profiler or get_profiler()
        
        logger.info("Initialized ProfilingMetricsCollector")
    
    def _register_metrics(self):
        """Register profiling metrics"""
        # Session metrics
        self.register_metric(MetricDefinition(
            name="profiling_sessions_total",
            metric_type=MetricType.COUNTER,
            description="Total number of profiling sessions"
        ))
        
        self.register_metric(MetricDefinition(
            name="profiling_session_duration_seconds",
            metric_type=MetricType.GAUGE,
            description="Duration of profiling sessions",
            labels={"session_id": ""},
            unit="seconds"
        ))
        
        self.register_metric(MetricDefinition(
            name="profiling_session_functions_total",
            metric_type=MetricType.GAUGE,
            description="Number of functions profiled in session",
            labels={"session_id": ""}
        ))
        
        # Function execution metrics
        self.register_metric(MetricDefinition(
            name="profiling_function_calls_total",
            metric_type=MetricType.COUNTER,
            description="Total function calls profiled",
            labels={"function": "", "module": ""}
        ))
        
        self.register_metric(MetricDefinition(
            name="profiling_function_duration_seconds",
            metric_type=MetricType.GAUGE,
            description="Function execution duration statistics",
            labels={"function": "", "module": "", "stat": ""},  # stat: avg, min, max, total
            unit="seconds"
        ))
        
        self.register_metric(MetricDefinition(
            name="profiling_function_memory_bytes",
            metric_type=MetricType.GAUGE,
            description="Function memory usage",
            labels={"function": "", "module": ""},
            unit="bytes"
        ))
        
        self.register_metric(MetricDefinition(
            name="profiling_function_errors_total",
            metric_type=MetricType.COUNTER,
            description="Function execution errors",
            labels={"function": "", "module": ""}
        ))
        
        # Bottleneck metrics
        self.register_metric(MetricDefinition(
            name="profiling_bottleneck_duration_seconds",
            metric_type=MetricType.GAUGE,
            description="Performance bottleneck duration",
            labels={"function": "", "module": "", "session_id": ""},
            unit="seconds"
        ))
        
        self.register_metric(MetricDefinition(
            name="profiling_bottleneck_depth",
            metric_type=MetricType.GAUGE,
            description="Call stack depth of bottleneck",
            labels={"function": "", "module": "", "session_id": ""}
        ))
        
        # Call graph metrics
        self.register_metric(MetricDefinition(
            name="profiling_call_depth_max",
            metric_type=MetricType.GAUGE,
            description="Maximum call stack depth",
            labels={"session_id": ""}
        ))
        
        self.register_metric(MetricDefinition(
            name="profiling_call_graph_nodes_total",
            metric_type=MetricType.GAUGE,
            description="Total nodes in call graph",
            labels={"session_id": ""}
        ))
        
        # Profiler overhead metrics
        self.register_metric(MetricDefinition(
            name="profiling_overhead_percent",
            metric_type=MetricType.GAUGE,
            description="Profiler overhead percentage",
            unit="percent"
        ))
        
        self.register_metric(MetricDefinition(
            name="profiling_instrumented_functions_total",
            metric_type=MetricType.GAUGE,
            description="Number of instrumented functions"
        ))
    
    async def collect_metrics(self) -> List[MetricValue]:
        """Collect profiling metrics"""
        metrics = []
        
        try:
            # Session metrics
            total_sessions = len(self.profiler.sessions)
            metrics.append(MetricValue(
                name="profiling_sessions_total",
                value=total_sessions
            ))
            
            # Recent session metrics
            if self.profiler.sessions:
                latest_session = self.profiler.sessions[-1]
                
                metrics.extend([
                    MetricValue(
                        name="profiling_session_duration_seconds",
                        value=latest_session.total_duration,
                        labels={"session_id": latest_session.session_id}
                    ),
                    MetricValue(
                        name="profiling_session_functions_total",
                        value=latest_session.total_functions,
                        labels={"session_id": latest_session.session_id}
                    )
                ])
                
                # Call graph metrics for latest session
                call_graph = self.profiler.get_call_graph(latest_session.session_id)
                if call_graph:
                    max_depth = self._calculate_max_depth(call_graph['root_frames'])
                    total_nodes = self._count_total_nodes(call_graph['root_frames'])
                    
                    metrics.extend([
                        MetricValue(
                            name="profiling_call_depth_max",
                            value=max_depth,
                            labels={"session_id": latest_session.session_id}
                        ),
                        MetricValue(
                            name="profiling_call_graph_nodes_total",
                            value=total_nodes,
                            labels={"session_id": latest_session.session_id}
                        )
                    ])
                
                # Bottleneck metrics for latest session
                bottlenecks = self.profiler.get_bottlenecks(latest_session.session_id, limit=5)
                for bottleneck in bottlenecks:
                    function_parts = bottleneck['function'].split('.', 1)
                    module_name = function_parts[0] if len(function_parts) > 1 else "unknown"
                    function_name = function_parts[1] if len(function_parts) > 1 else function_parts[0]
                    
                    metrics.extend([
                        MetricValue(
                            name="profiling_bottleneck_duration_seconds",
                            value=bottleneck['duration'],
                            labels={
                                "function": function_name,
                                "module": module_name,
                                "session_id": latest_session.session_id
                            }
                        ),
                        MetricValue(
                            name="profiling_bottleneck_depth",
                            value=bottleneck['depth'],
                            labels={
                                "function": function_name,
                                "module": module_name,
                                "session_id": latest_session.session_id
                            }
                        )
                    ])
            
            # Function statistics
            function_stats = self.profiler.get_function_statistics()
            
            for func_key, stats in function_stats.items():
                function_parts = func_key.split('.', 1)
                module_name = function_parts[0] if len(function_parts) > 1 else "unknown"
                function_name = function_parts[1] if len(function_parts) > 1 else function_parts[0]
                
                base_labels = {
                    "function": function_name,
                    "module": module_name
                }
                
                # Function call counts
                metrics.append(MetricValue(
                    name="profiling_function_calls_total",
                    value=stats['call_count'],
                    labels=base_labels
                ))
                
                # Function duration statistics
                if stats['call_count'] > 0:
                    duration_metrics = [
                        ("avg", stats['avg_time']),
                        ("min", stats['min_time'] if stats['min_time'] != float('inf') else 0),
                        ("max", stats['max_time']),
                        ("total", stats['total_time'])
                    ]
                    
                    for stat_name, stat_value in duration_metrics:
                        metrics.append(MetricValue(
                            name="profiling_function_duration_seconds",
                            value=stat_value,
                            labels={**base_labels, "stat": stat_name}
                        ))
                
                # Function memory usage
                if stats['total_memory'] != 0:
                    metrics.append(MetricValue(
                        name="profiling_function_memory_bytes",
                        value=stats['total_memory'],
                        labels=base_labels
                    ))
                
                # Function errors
                if stats['error_count'] > 0:
                    metrics.append(MetricValue(
                        name="profiling_function_errors_total",
                        value=stats['error_count'],
                        labels=base_labels
                    ))
            
            # Profiler overhead
            overhead = self.profiler.get_profiler_overhead()
            metrics.append(MetricValue(
                name="profiling_overhead_percent",
                value=overhead
            ))
            
            # Instrumented functions count
            instrumented_count = len(self.profiler.instrumented_functions)
            metrics.append(MetricValue(
                name="profiling_instrumented_functions_total",
                value=instrumented_count
            ))
            
        except Exception as e:
            logger.error(f"Error collecting profiling metrics: {e}")
        
        return metrics
    
    def _calculate_max_depth(self, frames: List[Dict[str, Any]], current_depth: int = 0) -> int:
        """Calculate maximum call stack depth"""
        if not frames:
            return current_depth
        
        max_depth = current_depth
        for frame in frames:
            child_frames = frame.get('child_frames', [])
            if child_frames:
                depth = self._calculate_max_depth(child_frames, current_depth + 1)
                max_depth = max(max_depth, depth)
            else:
                max_depth = max(max_depth, current_depth + 1)
        
        return max_depth
    
    def _count_total_nodes(self, frames: List[Dict[str, Any]]) -> int:
        """Count total nodes in call graph"""
        if not frames:
            return 0
        
        count = len(frames)
        for frame in frames:
            child_frames = frame.get('child_frames', [])
            count += self._count_total_nodes(child_frames)
        
        return count
    
    def set_profiler(self, profiler: ExecutionProfiler):
        """Set the execution profiler"""
        self.profiler = profiler
        logger.info("Updated execution profiler")
    
    def get_profiling_summary(self) -> Dict[str, Any]:
        """Get comprehensive profiling summary"""
        function_stats = self.profiler.get_function_statistics()
        
        return {
            'total_sessions': len(self.profiler.sessions),
            'instrumented_functions': len(self.profiler.instrumented_functions),
            'total_function_calls': sum(stats['call_count'] for stats in function_stats.values()),
            'total_execution_time': sum(stats['total_time'] for stats in function_stats.values()),
            'profiler_overhead': self.profiler.get_profiler_overhead(),
            'latest_session': self.profiler.sessions[-1].session_id if self.profiler.sessions else None
        }
    
    def cleanup(self):
        """Cleanup resources"""
        super().cleanup()
