"""
Metrics Collectors Module
========================

Comprehensive metrics collection framework for the Vibe Check monitoring platform.
Provides system metrics, code quality metrics, and extensible collector framework.
"""

from .base_collector import (
    MetricsCollector,
    MetricDefinition,
    MetricValue,
    MetricType,
    CollectionInterval,
    CollectorConfig,
    MetricsRegistry,
    metrics_registry,
    register_collector,
    get_metrics_registry,
)

from .system_collector import SystemMetricsCollector

from .code_quality_collector import CodeQualityMetricsCollector

try:
    from .profiling_collector import ProfilingMetricsCollector
except ImportError:
    # Profiling collector may not be available in all environments
    ProfilingMetricsCollector = None

from .metrics_manager import (
    MetricsManager,
    MetricsManagerConfig,
    get_metrics_manager,
    shutdown_metrics_manager,
)

__all__ = [
    # Base collector framework
    'MetricsCollector',
    'MetricDefinition',
    'MetricValue',
    'MetricType',
    'CollectionInterval',
    'CollectorConfig',
    'MetricsRegistry',
    'metrics_registry',
    'register_collector',
    'get_metrics_registry',

    # Specific collectors
    'SystemMetricsCollector',
    'CodeQualityMetricsCollector',
    'ProfilingMetricsCollector',

    # Metrics manager
    'MetricsManager',
    'MetricsManagerConfig',
    'get_metrics_manager',
    'shutdown_metrics_manager',
]
