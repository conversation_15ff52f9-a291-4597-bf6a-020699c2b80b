"""
Vibe Check Monitoring Platform
=============================

Comprehensive monitoring and observability platform that replaces Prometheus and Grafana
functionality while maintaining existing code analysis capabilities.

This module provides:
- High-performance time-series storage engine
- PromQL-compatible query engine
- Real-time metrics collection
- Interactive dashboards and visualization
- Alerting and notification system
"""

from .storage.time_series_engine import (
    TimeSeriesStorageEngine,
    TSDBConfig,
    MetricSample,
    MetricSeries,
    MetricType,
    create_tsdb,
    ingest_metrics,
)

from .query.promql_engine import (
    PromQLEngine,
    QueryResult,
    QueryType,
    PromQLFunction,
    PromQLParser,
    create_promql_engine,
    execute_promql,
)

__all__ = [
    # Storage Engine
    'TimeSeriesStorageEngine',
    'TSDBConfig',
    'MetricSample',
    'MetricSeries',
    'MetricType',
    'create_tsdb',
    'ingest_metrics',
    
    # Query Engine
    'PromQLEngine',
    'QueryResult',
    'QueryType',
    'PromQLFunction',
    'PromQLParser',
    'create_promql_engine',
    'execute_promql',
]


def create_monitoring_platform(data_dir: str = ".vibe_check_monitoring") -> tuple:
    """
    Create a complete monitoring platform with storage and query engines.
    
    Args:
        data_dir: Directory for storing monitoring data
        
    Returns:
        Tuple of (storage_engine, query_engine)
    """
    import asyncio
    from pathlib import Path
    
    async def _create_platform():
        # Configure storage
        config = TSDBConfig(
            data_dir=Path(data_dir),
            retention_days=30,
            compression_enabled=True,
            max_series_in_memory=10000,
            max_samples_per_series=8640,  # ~1 day at 10s intervals
            write_batch_size=1000,
            flush_interval_seconds=60.0,
            enable_query_cache=True,
            cache_ttl_seconds=300.0,
            max_ingestion_rate=2000
        )
        
        # Create storage engine
        storage = await create_tsdb(config)
        
        # Create query engine
        query_engine = await create_promql_engine(storage)
        
        return storage, query_engine
    
    return asyncio.run(_create_platform())


def get_version() -> str:
    """Get monitoring platform version"""
    return "1.0.0"


def get_supported_features() -> dict:
    """Get list of supported monitoring features"""
    return {
        'storage': {
            'time_series_database': True,
            'compression': True,
            'retention_policies': True,
            'multi_level_caching': True,
            'async_operations': True,
        },
        'query': {
            'promql_compatibility': True,
            'instant_queries': True,
            'range_queries': True,
            'functions': [
                'rate', 'increase', 'avg_over_time', 
                'max_over_time', 'min_over_time'
            ],
            'query_optimization': True,
            'result_caching': True,
        },
        'ingestion': {
            'high_frequency': True,
            'batch_processing': True,
            'rate_limiting': True,
            'backpressure_handling': True,
        },
        'performance': {
            'target_ingestion_rate': '2000+ samples/sec',
            'query_response_time': '<100ms typical',
            'memory_efficiency': 'LRU eviction + compression',
            'disk_persistence': True,
        }
    }
