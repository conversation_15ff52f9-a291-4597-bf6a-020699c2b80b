"""
Real-time Dashboard Components
==============================

This module provides interactive dashboard components for real-time monitoring
visualization including charts, graphs, and data display widgets.

File: vibe_check/monitoring/dashboard/dashboard_components.py
Purpose: Interactive charts, graphs, and real-time data visualization
Related Files: stream_processor.py, time_series_engine.py
Dependencies: asyncio, json, time
"""

import asyncio
import json
import logging
import time
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Callable, Union
from enum import Enum
from collections import deque
import statistics

logger = logging.getLogger(__name__)


class ChartType(Enum):
    """Chart visualization types"""
    LINE = "line"
    BAR = "bar"
    AREA = "area"
    SCATTER = "scatter"
    PIE = "pie"
    GAUGE = "gauge"
    HEATMAP = "heatmap"


class TimeRange(Enum):
    """Time range options for charts"""
    LAST_5_MINUTES = "5m"
    LAST_15_MINUTES = "15m"
    LAST_HOUR = "1h"
    LAST_6_HOURS = "6h"
    LAST_24_HOURS = "24h"
    LAST_7_DAYS = "7d"


@dataclass
class DataPoint:
    """Single data point for visualization"""
    timestamp: float
    value: float
    labels: Dict[str, str] = field(default_factory=dict)


@dataclass
class ChartConfig:
    """Configuration for chart components"""
    chart_type: ChartType
    title: str
    metric_name: str
    time_range: TimeRange = TimeRange.LAST_HOUR
    refresh_interval: float = 5.0  # seconds
    max_data_points: int = 1000
    aggregation_function: Optional[str] = None  # avg, sum, min, max
    group_by_labels: List[str] = field(default_factory=list)
    
    # Visual configuration
    width: int = 800
    height: int = 400
    color_scheme: str = "default"
    show_legend: bool = True
    show_grid: bool = True


@dataclass
class DashboardLayout:
    """Dashboard layout configuration"""
    title: str
    description: str = ""
    columns: int = 2
    auto_refresh: bool = True
    refresh_interval: float = 10.0  # seconds
    theme: str = "light"


class ChartComponent:
    """Interactive chart component for real-time data visualization"""
    
    def __init__(self, config: ChartConfig):
        self.config = config
        self.data_buffer: deque = deque(maxlen=config.max_data_points)
        self.last_update = 0.0
        
        # Event callbacks
        self.on_data_update: Optional[Callable[[List[DataPoint]], None]] = None
        self.on_error: Optional[Callable[[str], None]] = None
        
        logger.info(f"ChartComponent created: {config.title} ({config.chart_type.value})")
    
    def add_data_point(self, data_point: DataPoint):
        """Add a new data point to the chart"""
        self.data_buffer.append(data_point)
        self.last_update = time.time()
        
        if self.on_data_update:
            self.on_data_update([data_point])
    
    def add_data_points(self, data_points: List[DataPoint]):
        """Add multiple data points to the chart"""
        for point in data_points:
            self.data_buffer.append(point)
        
        self.last_update = time.time()
        
        if self.on_data_update:
            self.on_data_update(data_points)
    
    def get_chart_data(self) -> Dict[str, Any]:
        """Get chart data in visualization-ready format"""
        # Filter data by time range
        current_time = time.time()
        time_range_seconds = self._get_time_range_seconds()
        cutoff_time = current_time - time_range_seconds
        
        filtered_data = [
            point for point in self.data_buffer
            if point.timestamp >= cutoff_time
        ]
        
        # Group data if needed
        if self.config.group_by_labels:
            grouped_data = self._group_data_points(filtered_data)
        else:
            grouped_data = {"default": filtered_data}
        
        # Apply aggregation if specified
        if self.config.aggregation_function:
            grouped_data = self._apply_aggregation(grouped_data)
        
        # Format for visualization
        chart_data = self._format_chart_data(grouped_data)
        
        return {
            "type": self.config.chart_type.value,
            "title": self.config.title,
            "data": chart_data,
            "config": {
                "width": self.config.width,
                "height": self.config.height,
                "color_scheme": self.config.color_scheme,
                "show_legend": self.config.show_legend,
                "show_grid": self.config.show_grid
            },
            "metadata": {
                "last_update": self.last_update,
                "data_points": len(filtered_data),
                "time_range": self.config.time_range.value
            }
        }
    
    def _get_time_range_seconds(self) -> float:
        """Convert time range enum to seconds"""
        time_ranges = {
            TimeRange.LAST_5_MINUTES: 5 * 60,
            TimeRange.LAST_15_MINUTES: 15 * 60,
            TimeRange.LAST_HOUR: 60 * 60,
            TimeRange.LAST_6_HOURS: 6 * 60 * 60,
            TimeRange.LAST_24_HOURS: 24 * 60 * 60,
            TimeRange.LAST_7_DAYS: 7 * 24 * 60 * 60
        }
        return time_ranges.get(self.config.time_range, 3600)
    
    def _group_data_points(self, data_points: List[DataPoint]) -> Dict[str, List[DataPoint]]:
        """Group data points by specified labels"""
        groups = {}
        
        for point in data_points:
            # Create group key from labels
            key_parts = []
            for label in self.config.group_by_labels:
                key_parts.append(point.labels.get(label, "unknown"))
            group_key = "_".join(key_parts)
            
            if group_key not in groups:
                groups[group_key] = []
            groups[group_key].append(point)
        
        return groups
    
    def _apply_aggregation(self, grouped_data: Dict[str, List[DataPoint]]) -> Dict[str, List[DataPoint]]:
        """Apply aggregation function to grouped data"""
        if not self.config.aggregation_function:
            return grouped_data
        
        aggregated = {}
        
        for group_key, points in grouped_data.items():
            if not points:
                continue
            
            # Group points by time buckets (e.g., 1-minute buckets)
            bucket_size = 60.0  # 1 minute buckets
            buckets = {}
            
            for point in points:
                bucket_time = int(point.timestamp / bucket_size) * bucket_size
                if bucket_time not in buckets:
                    buckets[bucket_time] = []
                buckets[bucket_time].append(point.value)
            
            # Apply aggregation to each bucket
            aggregated_points = []
            for bucket_time, values in buckets.items():
                if values:
                    agg_value = self._compute_aggregation(values)
                    aggregated_points.append(DataPoint(
                        timestamp=bucket_time,
                        value=agg_value,
                        labels=points[0].labels if points else {}
                    ))
            
            aggregated[group_key] = aggregated_points
        
        return aggregated
    
    def _compute_aggregation(self, values: List[float]) -> float:
        """Compute aggregation value"""
        if not values:
            return 0.0
        
        func = self.config.aggregation_function
        
        if func == "avg":
            return statistics.mean(values)
        elif func == "sum":
            return sum(values)
        elif func == "min":
            return min(values)
        elif func == "max":
            return max(values)
        elif func == "count":
            return float(len(values))
        else:
            return statistics.mean(values)  # Default to average
    
    def _format_chart_data(self, grouped_data: Dict[str, List[DataPoint]]) -> Dict[str, Any]:
        """Format data for specific chart types"""
        if self.config.chart_type in [ChartType.LINE, ChartType.AREA]:
            return self._format_time_series_data(grouped_data)
        elif self.config.chart_type == ChartType.BAR:
            return self._format_bar_data(grouped_data)
        elif self.config.chart_type == ChartType.PIE:
            return self._format_pie_data(grouped_data)
        elif self.config.chart_type == ChartType.GAUGE:
            return self._format_gauge_data(grouped_data)
        else:
            return self._format_time_series_data(grouped_data)  # Default
    
    def _format_time_series_data(self, grouped_data: Dict[str, List[DataPoint]]) -> Dict[str, Any]:
        """Format data for time series charts"""
        series = []
        
        for group_key, points in grouped_data.items():
            series_data = [
                {"x": point.timestamp * 1000, "y": point.value}  # Convert to milliseconds
                for point in sorted(points, key=lambda p: p.timestamp)
            ]
            
            series.append({
                "name": group_key,
                "data": series_data
            })
        
        return {"series": series}
    
    def _format_bar_data(self, grouped_data: Dict[str, List[DataPoint]]) -> Dict[str, Any]:
        """Format data for bar charts"""
        categories = []
        values = []
        
        for group_key, points in grouped_data.items():
            if points:
                # Use latest value for each group
                latest_point = max(points, key=lambda p: p.timestamp)
                categories.append(group_key)
                values.append(latest_point.value)
        
        return {
            "categories": categories,
            "series": [{"name": self.config.metric_name, "data": values}]
        }
    
    def _format_pie_data(self, grouped_data: Dict[str, List[DataPoint]]) -> Dict[str, Any]:
        """Format data for pie charts"""
        data = []
        
        for group_key, points in grouped_data.items():
            if points:
                # Sum values for each group
                total_value = sum(point.value for point in points)
                data.append({
                    "name": group_key,
                    "value": total_value
                })
        
        return {"data": data}
    
    def _format_gauge_data(self, grouped_data: Dict[str, List[DataPoint]]) -> Dict[str, Any]:
        """Format data for gauge charts"""
        # Use the latest value from the first group
        for points in grouped_data.values():
            if points:
                latest_point = max(points, key=lambda p: p.timestamp)
                return {
                    "value": latest_point.value,
                    "timestamp": latest_point.timestamp
                }
        
        return {"value": 0, "timestamp": time.time()}
    
    def clear_data(self):
        """Clear all data from the chart"""
        self.data_buffer.clear()
        self.last_update = time.time()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get chart statistics"""
        if not self.data_buffer:
            return {
                "data_points": 0,
                "time_span": 0,
                "last_update": self.last_update
            }
        
        timestamps = [point.timestamp for point in self.data_buffer]
        values = [point.value for point in self.data_buffer]
        
        return {
            "data_points": len(self.data_buffer),
            "time_span": max(timestamps) - min(timestamps) if timestamps else 0,
            "value_range": {
                "min": min(values) if values else 0,
                "max": max(values) if values else 0,
                "avg": statistics.mean(values) if values else 0
            },
            "last_update": self.last_update,
            "chart_type": self.config.chart_type.value,
            "title": self.config.title
        }


class Dashboard:
    """Interactive dashboard for real-time monitoring"""
    
    def __init__(self, layout: DashboardLayout):
        self.layout = layout
        self.components: Dict[str, ChartComponent] = {}
        self.running = False
        self.refresh_task: Optional[asyncio.Task] = None
        
        logger.info(f"Dashboard created: {layout.title}")
    
    def add_component(self, component_id: str, component: ChartComponent):
        """Add a chart component to the dashboard"""
        self.components[component_id] = component
        logger.info(f"Added component {component_id} to dashboard")
    
    def remove_component(self, component_id: str) -> bool:
        """Remove a chart component from the dashboard"""
        if component_id in self.components:
            del self.components[component_id]
            logger.info(f"Removed component {component_id} from dashboard")
            return True
        return False
    
    async def start(self):
        """Start dashboard auto-refresh"""
        if self.running:
            logger.warning("Dashboard already running")
            return
        
        self.running = True
        
        if self.layout.auto_refresh:
            self.refresh_task = asyncio.create_task(self._refresh_loop())
        
        logger.info("Dashboard started")
    
    async def stop(self):
        """Stop dashboard auto-refresh"""
        self.running = False
        
        if self.refresh_task:
            self.refresh_task.cancel()
            try:
                await self.refresh_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Dashboard stopped")
    
    async def _refresh_loop(self):
        """Auto-refresh loop for dashboard"""
        while self.running:
            try:
                await asyncio.sleep(self.layout.refresh_interval)
                # Refresh logic would trigger UI updates here
                logger.debug("Dashboard refresh triggered")
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in dashboard refresh loop: {e}")
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """Get complete dashboard data for rendering"""
        components_data = {}
        
        for component_id, component in self.components.items():
            try:
                components_data[component_id] = component.get_chart_data()
            except Exception as e:
                logger.error(f"Error getting data for component {component_id}: {e}")
                components_data[component_id] = {"error": str(e)}
        
        return {
            "layout": {
                "title": self.layout.title,
                "description": self.layout.description,
                "columns": self.layout.columns,
                "theme": self.layout.theme
            },
            "components": components_data,
            "metadata": {
                "last_refresh": time.time(),
                "component_count": len(self.components),
                "running": self.running
            }
        }
    
    def get_dashboard_stats(self) -> Dict[str, Any]:
        """Get dashboard statistics"""
        component_stats = {}
        total_data_points = 0
        
        for component_id, component in self.components.items():
            stats = component.get_stats()
            component_stats[component_id] = stats
            total_data_points += stats.get("data_points", 0)
        
        return {
            "total_components": len(self.components),
            "total_data_points": total_data_points,
            "component_stats": component_stats,
            "layout": self.layout.title,
            "running": self.running
        }
