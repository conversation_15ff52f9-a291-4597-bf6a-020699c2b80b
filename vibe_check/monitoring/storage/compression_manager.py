"""
Data Compression and Retention Manager
======================================

This module provides data compression and automated retention policies
for historical time-series data in the monitoring platform.

File: vibe_check/monitoring/storage/compression_manager.py
Purpose: Compress historical data and manage retention policies
Related Files: time_series_engine.py, stream_processor.py
Dependencies: asyncio, gzip, json, time
"""

import asyncio
import gzip
import json
import logging
import time
import shutil
from dataclasses import dataclass, field
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
import os

logger = logging.getLogger(__name__)


class CompressionType(Enum):
    """Compression algorithm types"""
    GZIP = "gzip"
    NONE = "none"


class RetentionPolicy(Enum):
    """Data retention policies"""
    KEEP_ALL = "keep_all"
    TIME_BASED = "time_based"
    SIZE_BASED = "size_based"
    COUNT_BASED = "count_based"


@dataclass
class CompressionConfig:
    """Configuration for data compression"""
    enabled: bool = True
    compression_type: CompressionType = CompressionType.GZIP
    compression_level: int = 6  # 1-9 for gzip
    min_file_size_bytes: int = 1024  # Don't compress files smaller than this
    compress_after_hours: float = 24.0  # Compress data older than this
    batch_size: int = 100  # Files to process in one batch


@dataclass
class RetentionConfig:
    """Configuration for data retention"""
    policy: RetentionPolicy = RetentionPolicy.TIME_BASED
    max_age_days: Optional[float] = 30.0  # Keep data for 30 days
    max_size_gb: Optional[float] = 10.0   # Keep max 10GB
    max_files: Optional[int] = 10000      # Keep max 10k files
    cleanup_interval_hours: float = 24.0  # Run cleanup daily


@dataclass
class CompressionStats:
    """Compression and retention statistics"""
    files_compressed: int = 0
    files_deleted: int = 0
    bytes_saved: int = 0
    bytes_deleted: int = 0
    compression_ratio: float = 0.0
    last_compression_time: Optional[float] = None
    last_cleanup_time: Optional[float] = None
    errors: int = 0


class CompressionManager:
    """Data compression and retention manager"""
    
    def __init__(self, data_dir: Path, 
                 compression_config: Optional[CompressionConfig] = None,
                 retention_config: Optional[RetentionConfig] = None):
        self.data_dir = Path(data_dir)
        self.compression_config = compression_config or CompressionConfig()
        self.retention_config = retention_config or RetentionConfig()
        
        # Statistics
        self.stats = CompressionStats()
        
        # Background tasks
        self.compression_task: Optional[asyncio.Task] = None
        self.retention_task: Optional[asyncio.Task] = None
        self.running = False
        
        # Ensure data directory exists
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"CompressionManager initialized for {data_dir}")
    
    async def start(self):
        """Start compression and retention background tasks"""
        if self.running:
            logger.warning("CompressionManager already running")
            return
        
        self.running = True
        
        if self.compression_config.enabled:
            self.compression_task = asyncio.create_task(self._compression_loop())
        
        self.retention_task = asyncio.create_task(self._retention_loop())
        
        logger.info("CompressionManager started")
    
    async def stop(self):
        """Stop compression and retention tasks"""
        self.running = False
        
        if self.compression_task:
            self.compression_task.cancel()
        
        if self.retention_task:
            self.retention_task.cancel()
        
        # Wait for tasks to complete
        tasks = [t for t in [self.compression_task, self.retention_task] if t]
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
        
        logger.info("CompressionManager stopped")
    
    async def _compression_loop(self):
        """Background compression loop"""
        while self.running:
            try:
                await self._run_compression()
                
                # Wait before next compression cycle
                await asyncio.sleep(self.compression_config.compress_after_hours * 3600)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in compression loop: {e}")
                self.stats.errors += 1
                await asyncio.sleep(300)  # Wait 5 minutes on error
    
    async def _retention_loop(self):
        """Background retention cleanup loop"""
        while self.running:
            try:
                await self._run_retention_cleanup()
                
                # Wait before next cleanup cycle
                await asyncio.sleep(self.retention_config.cleanup_interval_hours * 3600)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in retention loop: {e}")
                self.stats.errors += 1
                await asyncio.sleep(300)  # Wait 5 minutes on error
    
    async def _run_compression(self):
        """Run compression on eligible files"""
        if not self.compression_config.enabled:
            return
        
        logger.info("Starting compression cycle")
        start_time = time.time()
        
        # Find files eligible for compression
        eligible_files = await self._find_compression_candidates()
        
        if not eligible_files:
            logger.debug("No files eligible for compression")
            return
        
        # Process files in batches
        batch_size = self.compression_config.batch_size
        for i in range(0, len(eligible_files), batch_size):
            batch = eligible_files[i:i + batch_size]
            await self._compress_file_batch(batch)
        
        self.stats.last_compression_time = time.time()
        duration = self.stats.last_compression_time - start_time
        
        logger.info(f"Compression cycle completed in {duration:.2f}s, "
                   f"processed {len(eligible_files)} files")
    
    async def _find_compression_candidates(self) -> List[Path]:
        """Find files that should be compressed"""
        candidates = []
        cutoff_time = time.time() - (self.compression_config.compress_after_hours * 3600)
        
        try:
            for file_path in self.data_dir.rglob("*.json"):
                # Skip already compressed files
                if file_path.suffix == '.gz':
                    continue
                
                # Check file age
                if file_path.stat().st_mtime < cutoff_time:
                    # Check file size
                    if file_path.stat().st_size >= self.compression_config.min_file_size_bytes:
                        candidates.append(file_path)
        
        except Exception as e:
            logger.error(f"Error finding compression candidates: {e}")
            self.stats.errors += 1
        
        return candidates
    
    async def _compress_file_batch(self, files: List[Path]):
        """Compress a batch of files"""
        for file_path in files:
            try:
                await self._compress_file(file_path)
            except Exception as e:
                logger.error(f"Error compressing {file_path}: {e}")
                self.stats.errors += 1
    
    async def _compress_file(self, file_path: Path):
        """Compress a single file"""
        if self.compression_config.compression_type == CompressionType.GZIP:
            await self._gzip_compress_file(file_path)
        else:
            logger.warning(f"Unsupported compression type: {self.compression_config.compression_type}")
    
    async def _gzip_compress_file(self, file_path: Path):
        """Compress file using gzip"""
        compressed_path = file_path.with_suffix(file_path.suffix + '.gz')
        
        try:
            # Get original size
            original_size = file_path.stat().st_size
            
            # Compress file
            with open(file_path, 'rb') as f_in:
                with gzip.open(compressed_path, 'wb', 
                             compresslevel=self.compression_config.compression_level) as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            # Get compressed size
            compressed_size = compressed_path.stat().st_size
            
            # Remove original file
            file_path.unlink()
            
            # Update statistics
            self.stats.files_compressed += 1
            bytes_saved = original_size - compressed_size
            self.stats.bytes_saved += bytes_saved
            
            if original_size > 0:
                ratio = compressed_size / original_size
                self.stats.compression_ratio = (
                    (self.stats.compression_ratio * (self.stats.files_compressed - 1) + ratio)
                    / self.stats.files_compressed
                )
            
            logger.debug(f"Compressed {file_path.name}: {original_size} -> {compressed_size} bytes "
                        f"({ratio:.2%} ratio)")
        
        except Exception as e:
            # Clean up partial compression
            if compressed_path.exists():
                compressed_path.unlink()
            raise e
    
    async def _run_retention_cleanup(self):
        """Run retention cleanup"""
        logger.info("Starting retention cleanup")
        start_time = time.time()
        
        policy = self.retention_config.policy
        
        if policy == RetentionPolicy.TIME_BASED:
            await self._cleanup_by_time()
        elif policy == RetentionPolicy.SIZE_BASED:
            await self._cleanup_by_size()
        elif policy == RetentionPolicy.COUNT_BASED:
            await self._cleanup_by_count()
        elif policy == RetentionPolicy.KEEP_ALL:
            logger.debug("Retention policy is KEEP_ALL, skipping cleanup")
            return
        
        self.stats.last_cleanup_time = time.time()
        duration = self.stats.last_cleanup_time - start_time
        
        logger.info(f"Retention cleanup completed in {duration:.2f}s")
    
    async def _cleanup_by_time(self):
        """Clean up files based on age"""
        if not self.retention_config.max_age_days:
            return
        
        cutoff_time = time.time() - (self.retention_config.max_age_days * 24 * 3600)
        
        for file_path in self.data_dir.rglob("*"):
            if file_path.is_file():
                try:
                    if file_path.stat().st_mtime < cutoff_time:
                        await self._delete_file(file_path)
                except Exception as e:
                    logger.error(f"Error checking file {file_path}: {e}")
                    self.stats.errors += 1
    
    async def _cleanup_by_size(self):
        """Clean up files to stay under size limit"""
        if not self.retention_config.max_size_gb:
            return
        
        max_size_bytes = self.retention_config.max_size_gb * 1024 * 1024 * 1024
        
        # Get all files with their sizes and modification times
        files_info = []
        total_size = 0
        
        for file_path in self.data_dir.rglob("*"):
            if file_path.is_file():
                try:
                    stat = file_path.stat()
                    files_info.append((file_path, stat.st_size, stat.st_mtime))
                    total_size += stat.st_size
                except Exception as e:
                    logger.error(f"Error getting file info for {file_path}: {e}")
                    self.stats.errors += 1
        
        if total_size <= max_size_bytes:
            return
        
        # Sort by modification time (oldest first)
        files_info.sort(key=lambda x: x[2])
        
        # Delete oldest files until under size limit
        for file_path, file_size, _ in files_info:
            if total_size <= max_size_bytes:
                break
            
            await self._delete_file(file_path)
            total_size -= file_size
    
    async def _cleanup_by_count(self):
        """Clean up files to stay under count limit"""
        if not self.retention_config.max_files:
            return
        
        # Get all files with modification times
        files_info = []
        
        for file_path in self.data_dir.rglob("*"):
            if file_path.is_file():
                try:
                    stat = file_path.stat()
                    files_info.append((file_path, stat.st_mtime))
                except Exception as e:
                    logger.error(f"Error getting file info for {file_path}: {e}")
                    self.stats.errors += 1
        
        if len(files_info) <= self.retention_config.max_files:
            return
        
        # Sort by modification time (oldest first)
        files_info.sort(key=lambda x: x[1])
        
        # Delete oldest files to get under count limit
        files_to_delete = len(files_info) - self.retention_config.max_files
        
        for i in range(files_to_delete):
            file_path, _ = files_info[i]
            await self._delete_file(file_path)
    
    async def _delete_file(self, file_path: Path):
        """Delete a file and update statistics"""
        try:
            file_size = file_path.stat().st_size
            file_path.unlink()
            
            self.stats.files_deleted += 1
            self.stats.bytes_deleted += file_size
            
            logger.debug(f"Deleted file: {file_path.name} ({file_size} bytes)")
        
        except Exception as e:
            logger.error(f"Error deleting file {file_path}: {e}")
            self.stats.errors += 1
            raise e
    
    async def force_compression(self) -> Dict[str, Any]:
        """Force immediate compression of eligible files"""
        logger.info("Starting forced compression")
        start_time = time.time()
        
        initial_stats = self.get_stats()
        await self._run_compression()
        final_stats = self.get_stats()
        
        duration = time.time() - start_time
        
        return {
            "duration_seconds": duration,
            "files_compressed": final_stats["files_compressed"] - initial_stats["files_compressed"],
            "bytes_saved": final_stats["bytes_saved"] - initial_stats["bytes_saved"],
            "compression_ratio": final_stats["compression_ratio"]
        }
    
    async def force_cleanup(self) -> Dict[str, Any]:
        """Force immediate retention cleanup"""
        logger.info("Starting forced cleanup")
        start_time = time.time()
        
        initial_stats = self.get_stats()
        await self._run_retention_cleanup()
        final_stats = self.get_stats()
        
        duration = time.time() - start_time
        
        return {
            "duration_seconds": duration,
            "files_deleted": final_stats["files_deleted"] - initial_stats["files_deleted"],
            "bytes_deleted": final_stats["bytes_deleted"] - initial_stats["bytes_deleted"]
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """Get compression and retention statistics"""
        return {
            "files_compressed": self.stats.files_compressed,
            "files_deleted": self.stats.files_deleted,
            "bytes_saved": self.stats.bytes_saved,
            "bytes_deleted": self.stats.bytes_deleted,
            "compression_ratio": self.stats.compression_ratio,
            "last_compression_time": self.stats.last_compression_time,
            "last_cleanup_time": self.stats.last_cleanup_time,
            "errors": self.stats.errors,
            "compression_enabled": self.compression_config.enabled,
            "retention_policy": self.retention_config.policy.value,
            "running": self.running
        }
    
    def get_storage_summary(self) -> Dict[str, Any]:
        """Get storage usage summary"""
        total_size = 0
        total_files = 0
        compressed_files = 0
        
        try:
            for file_path in self.data_dir.rglob("*"):
                if file_path.is_file():
                    total_files += 1
                    total_size += file_path.stat().st_size
                    
                    if file_path.suffix == '.gz':
                        compressed_files += 1
        
        except Exception as e:
            logger.error(f"Error calculating storage summary: {e}")
        
        return {
            "total_files": total_files,
            "compressed_files": compressed_files,
            "compression_percentage": (compressed_files / total_files * 100) if total_files > 0 else 0,
            "total_size_bytes": total_size,
            "total_size_mb": total_size / (1024 * 1024),
            "data_directory": str(self.data_dir)
        }
