"""
Time-Series Storage Engine
=========================

High-performance time-series database with PromQL compatibility for the Vibe Check
monitoring platform. Handles 1000+ metrics/sec with efficient storage and querying.

This engine builds upon the async + caching foundation from Week 3 to provide
optimal performance for monitoring data storage and retrieval.
"""

import asyncio
import time
import struct
import gzip
from pathlib import Path
from typing import Dict, List, Optional, Union, Tuple, Any, AsyncIterator
from dataclasses import dataclass, field
from collections import defaultdict, deque
from enum import Enum
import logging
import hashlib
import json
from concurrent.futures import ThreadPoolExecutor

from vibe_check.core.caching import MultiLevelCache, CacheConfig, SmartCacheManager

logger = logging.getLogger(__name__)


class MetricType(Enum):
    """Metric types supported by the time-series engine"""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    SUMMARY = "summary"


@dataclass
class MetricSample:
    """Single time-series sample"""
    timestamp: float  # Unix timestamp
    value: float
    labels: Dict[str, str] = field(default_factory=dict)
    
    def __post_init__(self):
        # Ensure timestamp is in seconds (not milliseconds)
        if self.timestamp > 1e12:  # Likely milliseconds
            self.timestamp = self.timestamp / 1000.0
    
    def to_bytes(self) -> bytes:
        """Serialize sample to bytes for storage"""
        # Format: timestamp(8) + value(8) + labels_json_length(4) + labels_json
        labels_json = json.dumps(self.labels, sort_keys=True).encode('utf-8')
        return struct.pack('!ddI', self.timestamp, self.value, len(labels_json)) + labels_json
    
    @classmethod
    def from_bytes(cls, data: bytes) -> 'MetricSample':
        """Deserialize sample from bytes"""
        timestamp, value, labels_len = struct.unpack('!ddI', data[:20])
        labels_json = data[20:20+labels_len].decode('utf-8')
        labels = json.loads(labels_json)
        return cls(timestamp=timestamp, value=value, labels=labels)


@dataclass
class MetricSeries:
    """Time-series for a specific metric"""
    name: str
    metric_type: MetricType
    labels: Dict[str, str] = field(default_factory=dict)
    samples: deque = field(default_factory=deque)
    
    def __post_init__(self):
        if not isinstance(self.samples, deque):
            self.samples = deque(self.samples)
    
    @property
    def series_id(self) -> str:
        """Generate unique series ID from name and labels"""
        label_str = ",".join(f"{k}={v}" for k, v in sorted(self.labels.items()))
        series_key = f"{self.name}{{{label_str}}}"
        return hashlib.sha256(series_key.encode()).hexdigest()[:16]
    
    def add_sample(self, sample: MetricSample):
        """Add sample to series, maintaining time order"""
        # Insert in correct position to maintain time order
        if not self.samples or sample.timestamp >= self.samples[-1].timestamp:
            self.samples.append(sample)
        else:
            # Binary search for insertion point
            left, right = 0, len(self.samples)
            while left < right:
                mid = (left + right) // 2
                if self.samples[mid].timestamp < sample.timestamp:
                    left = mid + 1
                else:
                    right = mid
            self.samples.insert(left, sample)
    
    def get_samples_range(self, start_time: float, end_time: float) -> List[MetricSample]:
        """Get samples within time range"""
        result = []
        for sample in self.samples:
            if start_time <= sample.timestamp <= end_time:
                result.append(sample)
            elif sample.timestamp > end_time:
                break
        return result
    
    def get_latest_sample(self) -> Optional[MetricSample]:
        """Get the most recent sample"""
        return self.samples[-1] if self.samples else None


@dataclass
class TSDBConfig:
    """Time-series database configuration"""
    # Storage settings
    data_dir: Path = field(default_factory=lambda: Path(".vibe_check_tsdb"))
    retention_days: int = 30
    compression_enabled: bool = True
    
    # Performance settings
    max_series_in_memory: int = 10000
    max_samples_per_series: int = 8640  # ~1 day at 10s intervals
    write_batch_size: int = 1000
    flush_interval_seconds: float = 60.0
    
    # Cache settings
    enable_query_cache: bool = True
    cache_ttl_seconds: float = 300.0  # 5 minutes
    
    # Ingestion settings
    max_ingestion_rate: int = 2000  # samples/sec
    ingestion_buffer_size: int = 10000


class TimeSeriesStorageEngine:
    """High-performance time-series storage engine"""
    
    def __init__(self, config: Optional[TSDBConfig] = None):
        self.config = config or TSDBConfig()
        self.config.data_dir.mkdir(parents=True, exist_ok=True)
        
        # In-memory storage
        self.series_by_id: Dict[str, MetricSeries] = {}
        self.series_by_name: Dict[str, List[MetricSeries]] = defaultdict(list)
        
        # Write buffer for batching
        self.write_buffer: List[Tuple[str, MetricSample]] = []
        self.buffer_lock = asyncio.Lock()
        
        # Background tasks
        self.flush_task: Optional[asyncio.Task] = None
        self.cleanup_task: Optional[asyncio.Task] = None
        
        # Performance tracking
        self.ingestion_rate = 0
        self.last_ingestion_time = time.time()
        self.ingestion_count = 0
        
        # Thread pool for I/O operations
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # Cache integration
        cache_config = CacheConfig(
            memory_cache_size=1000,
            memory_cache_ttl=self.config.cache_ttl_seconds,
            disk_cache_enabled=True,
            disk_cache_dir=self.config.data_dir / "cache"
        )
        self.cache = MultiLevelCache(cache_config)
        self.cache_manager = SmartCacheManager(self.cache, cache_config)
        
        # Start background tasks
        self._start_background_tasks()
    
    def _start_background_tasks(self):
        """Start background maintenance tasks"""
        self.flush_task = asyncio.create_task(self._flush_loop())
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
    
    async def _flush_loop(self):
        """Background task to flush write buffer periodically"""
        while True:
            try:
                await asyncio.sleep(self.config.flush_interval_seconds)
                await self._flush_write_buffer()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in flush loop: {e}")
    
    async def _cleanup_loop(self):
        """Background task for data cleanup and maintenance"""
        while True:
            try:
                await asyncio.sleep(3600)  # Run every hour
                await self._cleanup_old_data()
                await self._optimize_storage()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
    
    async def ingest_sample(self, metric_name: str, value: float, 
                          labels: Optional[Dict[str, str]] = None,
                          timestamp: Optional[float] = None) -> bool:
        """Ingest a single metric sample"""
        timestamp = timestamp or time.time()
        labels = labels or {}
        
        sample = MetricSample(timestamp=timestamp, value=value, labels=labels)
        
        # Create or get series
        series = await self._get_or_create_series(metric_name, labels)
        
        # Add to write buffer
        async with self.buffer_lock:
            self.write_buffer.append((series.series_id, sample))
            
            # Update ingestion rate tracking
            self.ingestion_count += 1
            current_time = time.time()
            if current_time - self.last_ingestion_time >= 1.0:
                self.ingestion_rate = self.ingestion_count / (current_time - self.last_ingestion_time)
                self.last_ingestion_time = current_time
                self.ingestion_count = 0
            
            # Flush if buffer is full
            if len(self.write_buffer) >= self.config.write_batch_size:
                await self._flush_write_buffer()
        
        return True
    
    async def ingest_batch(self, samples: List[Tuple[str, float, Dict[str, str], float]]) -> int:
        """Ingest multiple samples efficiently"""
        ingested = 0
        
        for metric_name, value, labels, timestamp in samples:
            if await self.ingest_sample(metric_name, value, labels, timestamp):
                ingested += 1
        
        return ingested
    
    async def _get_or_create_series(self, metric_name: str, labels: Dict[str, str]) -> MetricSeries:
        """Get existing series or create new one"""
        # Create temporary series to get ID
        temp_series = MetricSeries(
            name=metric_name,
            metric_type=MetricType.GAUGE,  # Default type
            labels=labels
        )
        series_id = temp_series.series_id
        
        if series_id not in self.series_by_id:
            # Create new series
            series = MetricSeries(
                name=metric_name,
                metric_type=MetricType.GAUGE,
                labels=labels
            )
            
            self.series_by_id[series_id] = series
            self.series_by_name[metric_name].append(series)
            
            # Check memory limits
            if len(self.series_by_id) > self.config.max_series_in_memory:
                await self._evict_old_series()
        
        return self.series_by_id[series_id]
    
    async def _flush_write_buffer(self):
        """Flush write buffer to in-memory storage"""
        async with self.buffer_lock:
            if not self.write_buffer:
                return
            
            buffer_copy = self.write_buffer.copy()
            self.write_buffer.clear()
        
        # Process buffer
        for series_id, sample in buffer_copy:
            if series_id in self.series_by_id:
                series = self.series_by_id[series_id]
                series.add_sample(sample)
                
                # Limit samples per series
                while len(series.samples) > self.config.max_samples_per_series:
                    series.samples.popleft()
        
        logger.debug(f"Flushed {len(buffer_copy)} samples to storage")
    
    async def query_range(self, metric_name: str, start_time: float, end_time: float,
                         labels: Optional[Dict[str, str]] = None) -> List[MetricSeries]:
        """Query time-series data for a time range"""
        # Check cache first
        cache_key = f"query_range:{metric_name}:{start_time}:{end_time}:{hash(str(labels))}"
        
        if self.config.enable_query_cache:
            cached_result = await self.cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result
        
        # Find matching series
        matching_series = []
        
        if metric_name in self.series_by_name:
            for series in self.series_by_name[metric_name]:
                if self._labels_match(series.labels, labels):
                    # Create filtered series with samples in range
                    samples = series.get_samples_range(start_time, end_time)
                    if samples:
                        filtered_series = MetricSeries(
                            name=series.name,
                            metric_type=series.metric_type,
                            labels=series.labels,
                            samples=deque(samples)
                        )
                        matching_series.append(filtered_series)
        
        # Cache result
        if self.config.enable_query_cache:
            await self.cache_manager.set(
                cache_key, 
                matching_series, 
                ttl=self.config.cache_ttl_seconds
            )
        
        return matching_series
    
    async def query_instant(self, metric_name: str, timestamp: Optional[float] = None,
                          labels: Optional[Dict[str, str]] = None) -> List[MetricSample]:
        """Query instant values at a specific timestamp"""
        timestamp = timestamp or time.time()
        
        # Use a small time window around the timestamp
        window = 60.0  # 1 minute window
        series_list = await self.query_range(
            metric_name, 
            timestamp - window, 
            timestamp + window, 
            labels
        )
        
        results = []
        for series in series_list:
            # Find closest sample to timestamp
            closest_sample = None
            min_diff = float('inf')
            
            for sample in series.samples:
                diff = abs(sample.timestamp - timestamp)
                if diff < min_diff:
                    min_diff = diff
                    closest_sample = sample
            
            if closest_sample and min_diff <= window:
                results.append(closest_sample)
        
        return results
    
    def _labels_match(self, series_labels: Dict[str, str], 
                     query_labels: Optional[Dict[str, str]]) -> bool:
        """Check if series labels match query labels"""
        if not query_labels:
            return True
        
        for key, value in query_labels.items():
            if key not in series_labels or series_labels[key] != value:
                return False
        
        return True
    
    async def _evict_old_series(self):
        """Evict old series to free memory"""
        # Simple LRU eviction based on last sample timestamp
        series_by_age = sorted(
            self.series_by_id.items(),
            key=lambda x: x[1].get_latest_sample().timestamp if x[1].get_latest_sample() else 0
        )
        
        # Remove oldest 10%
        to_remove = len(series_by_age) // 10
        for i in range(to_remove):
            series_id, series = series_by_age[i]
            del self.series_by_id[series_id]
            self.series_by_name[series.name].remove(series)
    
    async def _cleanup_old_data(self):
        """Clean up data older than retention period"""
        cutoff_time = time.time() - (self.config.retention_days * 24 * 3600)
        
        for series in self.series_by_id.values():
            # Remove old samples
            while series.samples and series.samples[0].timestamp < cutoff_time:
                series.samples.popleft()
    
    async def _optimize_storage(self):
        """Optimize storage and perform maintenance"""
        # Remove empty series
        empty_series = [
            series_id for series_id, series in self.series_by_id.items()
            if not series.samples
        ]
        
        for series_id in empty_series:
            series = self.series_by_id[series_id]
            del self.series_by_id[series_id]
            self.series_by_name[series.name].remove(series)
        
        logger.info(f"Removed {len(empty_series)} empty series during optimization")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get storage engine statistics"""
        total_samples = sum(len(series.samples) for series in self.series_by_id.values())
        
        return {
            'series_count': len(self.series_by_id),
            'total_samples': total_samples,
            'ingestion_rate': self.ingestion_rate,
            'write_buffer_size': len(self.write_buffer),
            'memory_usage_mb': total_samples * 32 / (1024 * 1024),  # Rough estimate
            'cache_stats': self.cache_manager.get_stats() if hasattr(self.cache_manager, 'get_stats') else {}
        }
    
    async def shutdown(self):
        """Graceful shutdown"""
        # Cancel background tasks
        if self.flush_task:
            self.flush_task.cancel()
        if self.cleanup_task:
            self.cleanup_task.cancel()
        
        # Final flush
        await self._flush_write_buffer()
        
        # Cleanup resources
        self.cache_manager.cleanup()
        self.executor.shutdown(wait=False)
        
        logger.info("Time-series storage engine shutdown complete")


# Convenience functions
async def create_tsdb(config: Optional[TSDBConfig] = None) -> TimeSeriesStorageEngine:
    """Create and initialize time-series database"""
    return TimeSeriesStorageEngine(config)


async def ingest_metrics(tsdb: TimeSeriesStorageEngine,
                        metrics: List[Dict[str, Any]]) -> int:
    """Convenience function to ingest multiple metrics"""
    samples = []
    for metric in metrics:
        samples.append((
            metric['name'],
            metric['value'],
            metric.get('labels', {}),
            metric.get('timestamp', time.time())
        ))

    return await tsdb.ingest_batch(samples)
