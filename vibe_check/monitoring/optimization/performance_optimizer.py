"""
Performance Optimization Engine
===============================

This module provides performance optimization capabilities for the monitoring
system including caching, indexing, and efficient data structures.

File: vibe_check/monitoring/optimization/performance_optimizer.py
Purpose: Performance optimization with caching, indexing, and data structures
Related Files: time_series_engine.py, promql_engine.py
Dependencies: asyncio, time, collections
"""

import asyncio
import time
import logging
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Tuple, Set, Union
from collections import defaultdict, deque
from enum import Enum
import hashlib
import weakref

logger = logging.getLogger(__name__)


class OptimizationType(Enum):
    """Types of optimizations"""
    CACHING = "caching"
    INDEXING = "indexing"
    COMPRESSION = "compression"
    BATCHING = "batching"
    PREFETCHING = "prefetching"


class CacheStrategy(Enum):
    """Cache strategies"""
    LRU = "lru"
    LFU = "lfu"
    TTL = "ttl"
    ADAPTIVE = "adaptive"


@dataclass
class CacheConfig:
    """Cache configuration"""
    strategy: CacheStrategy = CacheStrategy.LRU
    max_size: int = 1000
    ttl_seconds: float = 300.0  # 5 minutes
    cleanup_interval: float = 60.0  # 1 minute
    hit_ratio_threshold: float = 0.8
    enabled: bool = True


@dataclass
class IndexConfig:
    """Index configuration"""
    enabled: bool = True
    max_cardinality: int = 10000
    rebuild_threshold: float = 0.5  # Rebuild when 50% of data changes
    bloom_filter_size: int = 10000
    hash_functions: int = 3


@dataclass
class CompressionConfig:
    """Compression configuration"""
    enabled: bool = True
    algorithm: str = "gzip"  # gzip, lz4, snappy
    compression_level: int = 6
    min_size_bytes: int = 1024  # Only compress if larger than 1KB
    batch_size: int = 100


@dataclass
class OptimizationConfig:
    """Overall optimization configuration"""
    cache: CacheConfig = field(default_factory=CacheConfig)
    index: IndexConfig = field(default_factory=IndexConfig)
    compression: CompressionConfig = field(default_factory=CompressionConfig)
    enable_batching: bool = True
    enable_prefetching: bool = True
    batch_size: int = 100
    prefetch_window: int = 10


class LRUCache:
    """Lightweight LRU cache implementation"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache: Dict[str, Any] = {}
        self.access_order: deque = deque()
        self.hits = 0
        self.misses = 0
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        if key in self.cache:
            # Move to end (most recently used)
            self.access_order.remove(key)
            self.access_order.append(key)
            self.hits += 1
            return self.cache[key]
        
        self.misses += 1
        return None
    
    def put(self, key: str, value: Any) -> None:
        """Put value in cache"""
        if key in self.cache:
            # Update existing
            self.access_order.remove(key)
        elif len(self.cache) >= self.max_size:
            # Remove least recently used
            lru_key = self.access_order.popleft()
            del self.cache[lru_key]
        
        self.cache[key] = value
        self.access_order.append(key)
    
    def clear(self) -> None:
        """Clear cache"""
        self.cache.clear()
        self.access_order.clear()
        self.hits = 0
        self.misses = 0
    
    def hit_ratio(self) -> float:
        """Calculate hit ratio"""
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0
    
    def size(self) -> int:
        """Get cache size"""
        return len(self.cache)


class BloomFilter:
    """Simple Bloom filter for membership testing"""
    
    def __init__(self, size: int = 10000, hash_functions: int = 3):
        self.size = size
        self.hash_functions = hash_functions
        self.bit_array = [False] * size
        self.items_added = 0
    
    def _hash(self, item: str, seed: int) -> int:
        """Hash function"""
        return hash(f"{item}:{seed}") % self.size
    
    def add(self, item: str) -> None:
        """Add item to filter"""
        for i in range(self.hash_functions):
            index = self._hash(item, i)
            self.bit_array[index] = True
        self.items_added += 1
    
    def contains(self, item: str) -> bool:
        """Check if item might be in set"""
        for i in range(self.hash_functions):
            index = self._hash(item, i)
            if not self.bit_array[index]:
                return False
        return True
    
    def clear(self) -> None:
        """Clear filter"""
        self.bit_array = [False] * self.size
        self.items_added = 0


class MetricIndex:
    """Index for fast metric lookups"""
    
    def __init__(self, config: IndexConfig):
        self.config = config
        
        # Label-based indexes
        self.label_indexes: Dict[str, Dict[str, Set[str]]] = defaultdict(lambda: defaultdict(set))
        self.metric_labels: Dict[str, Dict[str, str]] = {}
        
        # Bloom filter for fast negative lookups
        self.bloom_filter = BloomFilter(config.bloom_filter_size, config.hash_functions)
        
        # Statistics
        self.index_hits = 0
        self.index_misses = 0
        self.last_rebuild = time.time()
        
        logger.info("MetricIndex initialized")
    
    def add_metric(self, metric_name: str, labels: Dict[str, str]) -> None:
        """Add metric to index"""
        if not self.config.enabled:
            return
        
        metric_key = self._get_metric_key(metric_name, labels)
        
        # Add to bloom filter
        self.bloom_filter.add(metric_key)
        
        # Add to label indexes
        for label_name, label_value in labels.items():
            self.label_indexes[label_name][label_value].add(metric_key)
        
        # Store metric labels
        self.metric_labels[metric_key] = labels.copy()
    
    def find_metrics(self, label_filters: Dict[str, str]) -> Set[str]:
        """Find metrics matching label filters"""
        if not self.config.enabled:
            return set()
        
        if not label_filters:
            return set(self.metric_labels.keys())
        
        # Start with all metrics matching first filter
        first_label, first_value = next(iter(label_filters.items()))
        
        if first_label not in self.label_indexes:
            self.index_misses += 1
            return set()
        
        if first_value not in self.label_indexes[first_label]:
            self.index_misses += 1
            return set()
        
        result_set = self.label_indexes[first_label][first_value].copy()
        
        # Intersect with remaining filters
        for label_name, label_value in list(label_filters.items())[1:]:
            if label_name not in self.label_indexes:
                result_set.clear()
                break
            
            if label_value not in self.label_indexes[label_name]:
                result_set.clear()
                break
            
            result_set &= self.label_indexes[label_name][label_value]
        
        if result_set:
            self.index_hits += 1
        else:
            self.index_misses += 1
        
        return result_set
    
    def _get_metric_key(self, metric_name: str, labels: Dict[str, str]) -> str:
        """Generate metric key"""
        label_str = ','.join(f"{k}={v}" for k, v in sorted(labels.items()))
        return f"{metric_name}:{label_str}"
    
    def get_stats(self) -> Dict[str, Any]:
        """Get index statistics"""
        total_queries = self.index_hits + self.index_misses
        hit_ratio = self.index_hits / total_queries if total_queries > 0 else 0.0
        
        return {
            'enabled': self.config.enabled,
            'total_metrics': len(self.metric_labels),
            'label_indexes': len(self.label_indexes),
            'bloom_filter_items': self.bloom_filter.items_added,
            'index_hits': self.index_hits,
            'index_misses': self.index_misses,
            'hit_ratio': hit_ratio,
            'last_rebuild': self.last_rebuild
        }


class QueryCache:
    """Cache for query results"""
    
    def __init__(self, config: CacheConfig):
        self.config = config
        self.cache = LRUCache(config.max_size)
        self.ttl_cache: Dict[str, float] = {}  # key -> expiry_time
        self.cleanup_task: Optional[asyncio.Task] = None
        
        if config.enabled:
            self.cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        logger.info(f"QueryCache initialized with strategy: {config.strategy.value}")
    
    async def get(self, query: str, params: Optional[Dict[str, Any]] = None) -> Optional[Any]:
        """Get cached query result"""
        if not self.config.enabled:
            return None
        
        cache_key = self._get_cache_key(query, params)
        
        # Check TTL
        if cache_key in self.ttl_cache:
            if time.time() > self.ttl_cache[cache_key]:
                # Expired
                self.cache.cache.pop(cache_key, None)
                self.ttl_cache.pop(cache_key, None)
                return None
        
        return self.cache.get(cache_key)
    
    async def put(self, query: str, result: Any, params: Optional[Dict[str, Any]] = None) -> None:
        """Cache query result"""
        if not self.config.enabled:
            return
        
        cache_key = self._get_cache_key(query, params)
        
        # Set TTL
        if self.config.strategy in [CacheStrategy.TTL, CacheStrategy.ADAPTIVE]:
            self.ttl_cache[cache_key] = time.time() + self.config.ttl_seconds
        
        self.cache.put(cache_key, result)
    
    def _get_cache_key(self, query: str, params: Optional[Dict[str, Any]]) -> str:
        """Generate cache key"""
        if params:
            params_str = str(sorted(params.items()))
            combined = f"{query}:{params_str}"
        else:
            combined = query
        
        # Use hash for consistent key length
        return hashlib.md5(combined.encode()).hexdigest()
    
    async def _cleanup_loop(self) -> None:
        """Background cleanup of expired entries"""
        while True:
            try:
                await asyncio.sleep(self.config.cleanup_interval)
                await self._cleanup_expired()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cache cleanup: {e}")
    
    async def _cleanup_expired(self) -> None:
        """Clean up expired cache entries"""
        current_time = time.time()
        expired_keys = [
            key for key, expiry_time in self.ttl_cache.items()
            if current_time > expiry_time
        ]
        
        for key in expired_keys:
            self.cache.cache.pop(key, None)
            self.ttl_cache.pop(key, None)
        
        if expired_keys:
            logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            'enabled': self.config.enabled,
            'strategy': self.config.strategy.value,
            'size': self.cache.size(),
            'max_size': self.config.max_size,
            'hits': self.cache.hits,
            'misses': self.cache.misses,
            'hit_ratio': self.cache.hit_ratio(),
            'ttl_entries': len(self.ttl_cache)
        }
    
    async def clear(self) -> None:
        """Clear cache"""
        self.cache.clear()
        self.ttl_cache.clear()
    
    async def shutdown(self) -> None:
        """Shutdown cache"""
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass


class PerformanceOptimizer:
    """Main performance optimization engine"""
    
    def __init__(self, config: Optional[OptimizationConfig] = None):
        self.config = config or OptimizationConfig()
        
        # Components
        self.query_cache = QueryCache(self.config.cache)
        self.metric_index = MetricIndex(self.config.index)
        
        # Batching
        self.batch_queues: Dict[str, List[Any]] = defaultdict(list)
        self.batch_timers: Dict[str, float] = {}
        
        # Statistics
        self.optimization_stats = {
            'cache_optimizations': 0,
            'index_optimizations': 0,
            'batch_optimizations': 0,
            'total_queries_optimized': 0
        }
        
        logger.info("PerformanceOptimizer initialized")
    
    async def optimize_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> Optional[Any]:
        """Optimize query execution"""
        # Try cache first
        cached_result = await self.query_cache.get(query, params)
        if cached_result is not None:
            self.optimization_stats['cache_optimizations'] += 1
            return cached_result
        
        return None
    
    async def cache_result(self, query: str, result: Any, params: Optional[Dict[str, Any]] = None) -> None:
        """Cache query result"""
        await self.query_cache.put(query, result, params)
    
    def optimize_metric_lookup(self, label_filters: Dict[str, str]) -> Set[str]:
        """Optimize metric lookup using indexes"""
        result = self.metric_index.find_metrics(label_filters)
        if result:
            self.optimization_stats['index_optimizations'] += 1
        return result
    
    def add_metric_to_index(self, metric_name: str, labels: Dict[str, str]) -> None:
        """Add metric to index"""
        self.metric_index.add_metric(metric_name, labels)
    
    async def batch_operation(self, operation_type: str, operation: Any) -> None:
        """Add operation to batch queue"""
        if not self.config.enable_batching:
            return
        
        self.batch_queues[operation_type].append(operation)
        
        # Check if batch is ready
        if len(self.batch_queues[operation_type]) >= self.config.batch_size:
            await self._process_batch(operation_type)
    
    async def _process_batch(self, operation_type: str) -> None:
        """Process batched operations"""
        if operation_type not in self.batch_queues:
            return
        
        operations = self.batch_queues[operation_type]
        self.batch_queues[operation_type] = []
        
        if operations:
            self.optimization_stats['batch_optimizations'] += 1
            logger.debug(f"Processed batch of {len(operations)} {operation_type} operations")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get optimization statistics"""
        cache_stats = self.query_cache.get_stats()
        index_stats = self.metric_index.get_stats()
        
        return {
            'config': {
                'caching_enabled': self.config.cache.enabled,
                'indexing_enabled': self.config.index.enabled,
                'batching_enabled': self.config.enable_batching,
                'prefetching_enabled': self.config.enable_prefetching
            },
            'cache': cache_stats,
            'index': index_stats,
            'optimizations': self.optimization_stats,
            'batch_queues': {k: len(v) for k, v in self.batch_queues.items()}
        }
    
    async def shutdown(self) -> None:
        """Shutdown optimizer"""
        await self.query_cache.shutdown()
        logger.info("PerformanceOptimizer shutdown complete")
