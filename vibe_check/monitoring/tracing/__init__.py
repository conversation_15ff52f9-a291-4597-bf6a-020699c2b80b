"""
Tracing Module
==============

This module provides comprehensive distributed tracing capabilities with OpenTelemetry
integration, trace correlation, and span analysis for microservices monitoring.

Components:
- TracingManager: Main tracing manager with span processing
- Tracer: Distributed tracer for creating and managing spans
- Span: Individual trace span with events and attributes
- SpanContext: Trace correlation context
- TraceContext: Thread-local trace context management
- SpanProcessor: Process and export spans

Usage Examples:

Basic tracing:
```python
from vibe_check.monitoring.tracing import get_tracer, span_context

tracer = get_tracer("my-service")

with span_context(tracer, "operation") as span:
    span.set_attribute("user.id", "123")
    span.add_event("processing started")
    # ... do work ...
    span.add_event("processing completed")
```

Function decoration:
```python
from vibe_check.monitoring.tracing import trace

@trace("my_function")
def my_function():
    # Function will be automatically traced
    pass

@trace()  # Uses function name
async def async_function():
    # Async functions supported
    pass
```

Manual span management:
```python
tracer = get_tracer("my-service")
span = tracer.start_span("manual-operation")
span.set_attribute("operation.type", "manual")
# ... do work ...
tracer.end_span(span)
```
"""

from .distributed_tracing import (
    # Main classes
    TracingManager,
    Tracer,
    SpanProcessor,
    TraceContext,
    
    # Data structures
    Span,
    SpanContext,
    Trace,
    SpanEvent,
    SpanLink,
    
    # Enums
    SpanKind,
    SpanStatus,
    
    # Utilities
    TraceIdGenerator,
    
    # Context managers and decorators
    span_context,
    trace_function,
    
    # Global functions
    get_tracer,
    start_span,
    trace
)

__all__ = [
    # Main classes
    'TracingManager',
    'Tracer',
    'SpanProcessor',
    'TraceContext',
    
    # Data structures
    'Span',
    'SpanContext',
    'Trace',
    'SpanEvent',
    'SpanLink',
    
    # Enums
    'SpanKind',
    'SpanStatus',
    
    # Utilities
    'TraceIdGenerator',
    
    # Context managers and decorators
    'span_context',
    'trace_function',
    
    # Global functions
    'get_tracer',
    'start_span',
    'trace'
]
