"""
Distributed Tracing System
===========================

This module provides comprehensive distributed tracing with OpenTelemetry integration,
trace correlation, and span analysis for microservices monitoring.

File: vibe_check/monitoring/tracing/distributed_tracing.py
Purpose: OpenTelemetry integration with trace correlation and span analysis
Related Files: instrumentation/framework_integrations.py, logging/log_aggregation.py
Dependencies: asyncio, time, uuid, threading
"""

import asyncio
import time
import uuid
import threading
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Set, Callable
from enum import Enum
from collections import defaultdict, deque
import json
import logging

logger = logging.getLogger(__name__)


class SpanKind(Enum):
    """Span kinds following OpenTelemetry specification"""
    INTERNAL = "internal"
    SERVER = "server"
    CLIENT = "client"
    PRODUCER = "producer"
    CONSUMER = "consumer"


class SpanStatus(Enum):
    """Span status codes"""
    UNSET = "unset"
    OK = "ok"
    ERROR = "error"


@dataclass
class SpanContext:
    """Span context for trace correlation"""
    trace_id: str
    span_id: str
    parent_span_id: Optional[str] = None
    trace_flags: int = 1  # Sampled by default
    trace_state: str = ""
    
    def is_valid(self) -> bool:
        """Check if span context is valid"""
        return bool(self.trace_id and self.span_id)
    
    def is_sampled(self) -> bool:
        """Check if trace is sampled"""
        return bool(self.trace_flags & 1)


@dataclass
class SpanEvent:
    """Span event (log within a span)"""
    name: str
    timestamp: float
    attributes: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SpanLink:
    """Link to another span"""
    context: SpanContext
    attributes: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Span:
    """Distributed tracing span"""
    context: SpanContext
    name: str
    kind: SpanKind
    start_time: float
    end_time: Optional[float] = None
    
    # Span data
    attributes: Dict[str, Any] = field(default_factory=dict)
    events: List[SpanEvent] = field(default_factory=list)
    links: List[SpanLink] = field(default_factory=list)
    
    # Status
    status: SpanStatus = SpanStatus.UNSET
    status_message: str = ""
    
    # Metadata
    service_name: str = "unknown"
    resource_attributes: Dict[str, Any] = field(default_factory=dict)
    
    def duration_ms(self) -> Optional[float]:
        """Get span duration in milliseconds"""
        if self.end_time:
            return (self.end_time - self.start_time) * 1000
        return None
    
    def is_finished(self) -> bool:
        """Check if span is finished"""
        return self.end_time is not None
    
    def add_event(self, name: str, attributes: Optional[Dict[str, Any]] = None):
        """Add event to span"""
        event = SpanEvent(
            name=name,
            timestamp=time.time(),
            attributes=attributes or {}
        )
        self.events.append(event)
    
    def set_attribute(self, key: str, value: Any):
        """Set span attribute"""
        self.attributes[key] = value
    
    def set_status(self, status: SpanStatus, message: str = ""):
        """Set span status"""
        self.status = status
        self.status_message = message


@dataclass
class Trace:
    """Complete trace with all spans"""
    trace_id: str
    spans: List[Span] = field(default_factory=list)
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    
    def duration_ms(self) -> Optional[float]:
        """Get total trace duration"""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time) * 1000
        return None
    
    def get_root_spans(self) -> List[Span]:
        """Get root spans (no parent)"""
        return [span for span in self.spans if not span.context.parent_span_id]
    
    def get_children(self, parent_span_id: str) -> List[Span]:
        """Get child spans of a parent"""
        return [span for span in self.spans if span.context.parent_span_id == parent_span_id]
    
    def get_span_by_id(self, span_id: str) -> Optional[Span]:
        """Get span by ID"""
        for span in self.spans:
            if span.context.span_id == span_id:
                return span
        return None
    
    def is_complete(self) -> bool:
        """Check if all spans are finished"""
        return all(span.is_finished() for span in self.spans)


class TraceIdGenerator:
    """Generate trace and span IDs"""
    
    @staticmethod
    def generate_trace_id() -> str:
        """Generate 128-bit trace ID"""
        return uuid.uuid4().hex  # 32 characters
    
    @staticmethod
    def generate_span_id() -> str:
        """Generate 64-bit span ID"""
        return uuid.uuid4().hex[:16]


class SpanProcessor:
    """Process spans for export"""
    
    def __init__(self, max_queue_size: int = 2048):
        self.max_queue_size = max_queue_size
        self._span_queue: deque = deque(maxlen=max_queue_size)
        self._lock = threading.Lock()
        self._exporters: List[Callable[[List[Span]], None]] = []
        
        # Statistics
        self._stats = {
            "spans_processed": 0,
            "spans_exported": 0,
            "export_errors": 0
        }
    
    def add_exporter(self, exporter: Callable[[List[Span]], None]):
        """Add span exporter"""
        self._exporters.append(exporter)
    
    def on_end(self, span: Span):
        """Called when span ends"""
        with self._lock:
            self._span_queue.append(span)
            self._stats["spans_processed"] += 1

        # Always export immediately for testing
        self._export_batch()
    
    def _export_batch(self):
        """Export batch of spans"""
        with self._lock:
            if not self._span_queue:
                return
            
            # Get batch of spans
            batch_size = min(100, len(self._span_queue))
            batch = [self._span_queue.popleft() for _ in range(batch_size)]
        
        # Export to all exporters
        for exporter in self._exporters:
            try:
                exporter(batch)
                with self._lock:
                    self._stats["spans_exported"] += len(batch)
            except Exception as e:
                logger.error(f"Span export failed: {e}")
                with self._lock:
                    self._stats["export_errors"] += 1
    
    def force_flush(self):
        """Force export all queued spans"""
        while self._span_queue:
            self._export_batch()
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get processor statistics"""
        with self._lock:
            return {
                **self._stats,
                "queue_size": len(self._span_queue),
                "exporters_count": len(self._exporters)
            }


class TraceContext:
    """Thread-local trace context"""
    
    def __init__(self):
        self._local = threading.local()
    
    def get_current_span(self) -> Optional[Span]:
        """Get current active span"""
        return getattr(self._local, 'current_span', None)
    
    def set_current_span(self, span: Optional[Span]):
        """Set current active span"""
        self._local.current_span = span
    
    def get_current_context(self) -> Optional[SpanContext]:
        """Get current span context"""
        span = self.get_current_span()
        return span.context if span else None


class Tracer:
    """Distributed tracer"""
    
    def __init__(self, name: str, processor: SpanProcessor, context: TraceContext):
        self.name = name
        self.processor = processor
        self.context = context
        self._active_spans: Dict[str, Span] = {}
        self._lock = threading.Lock()
    
    def start_span(self, 
                   name: str,
                   kind: SpanKind = SpanKind.INTERNAL,
                   parent_context: Optional[SpanContext] = None,
                   attributes: Optional[Dict[str, Any]] = None,
                   links: Optional[List[SpanLink]] = None) -> Span:
        """Start a new span"""
        
        # Determine parent context
        if parent_context is None:
            parent_context = self.context.get_current_context()
        
        # Generate span context
        if parent_context and parent_context.is_valid():
            trace_id = parent_context.trace_id
            parent_span_id = parent_context.span_id
        else:
            trace_id = TraceIdGenerator.generate_trace_id()
            parent_span_id = None
        
        span_context = SpanContext(
            trace_id=trace_id,
            span_id=TraceIdGenerator.generate_span_id(),
            parent_span_id=parent_span_id
        )
        
        # Create span
        span = Span(
            context=span_context,
            name=name,
            kind=kind,
            start_time=time.time(),
            attributes=attributes or {},
            links=links or [],
            service_name=self.name
        )
        
        # Store active span
        with self._lock:
            self._active_spans[span.context.span_id] = span
        
        return span
    
    def end_span(self, span: Span):
        """End a span"""
        if span.is_finished():
            return
        
        span.end_time = time.time()
        
        # Remove from active spans
        with self._lock:
            self._active_spans.pop(span.context.span_id, None)
        
        # Process span
        self.processor.on_end(span)
    
    def get_active_spans(self) -> List[Span]:
        """Get all active spans"""
        with self._lock:
            return list(self._active_spans.values())


class TracingManager:
    """Main tracing manager"""
    
    def __init__(self, service_name: str = "vibe-check"):
        self.service_name = service_name
        self.processor = SpanProcessor()
        self.context = TraceContext()
        self.tracer = Tracer(service_name, self.processor, self.context)
        
        # Trace storage
        self._traces: Dict[str, Trace] = {}
        self._trace_lock = threading.Lock()
        
        # Setup default exporter
        self.processor.add_exporter(self._store_span)
        
        logger.info(f"TracingManager initialized for service: {service_name}")
    
    def _store_span(self, spans: List[Span]):
        """Store spans in trace collection"""
        try:
            with self._trace_lock:
                for span in spans:
                    trace_id = span.context.trace_id

                    if trace_id not in self._traces:
                        self._traces[trace_id] = Trace(trace_id=trace_id)

                    trace = self._traces[trace_id]

                    # Check if span already exists
                    existing_span = trace.get_span_by_id(span.context.span_id)
                    if existing_span is None:
                        trace.spans.append(span)

                    # Update trace timing
                    if trace.start_time is None or span.start_time < trace.start_time:
                        trace.start_time = span.start_time

                    if span.end_time:
                        if trace.end_time is None or span.end_time > trace.end_time:
                            trace.end_time = span.end_time
        except Exception as e:
            logger.error(f"Failed to store span: {e}")
    
    def start_span(self, name: str, **kwargs) -> Span:
        """Start a new span"""
        return self.tracer.start_span(name, **kwargs)
    
    def end_span(self, span: Span):
        """End a span"""
        self.tracer.end_span(span)
    
    def get_trace(self, trace_id: str) -> Optional[Trace]:
        """Get trace by ID"""
        with self._trace_lock:
            return self._traces.get(trace_id)
    
    def get_recent_traces(self, count: int = 100) -> List[Trace]:
        """Get recent traces"""
        with self._trace_lock:
            traces = list(self._traces.values())
            traces.sort(key=lambda t: t.start_time or 0, reverse=True)
            return traces[:count]
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get tracing statistics"""
        processor_stats = self.processor.get_statistics()
        
        with self._trace_lock:
            trace_count = len(self._traces)
            complete_traces = sum(1 for t in self._traces.values() if t.is_complete())
            total_spans = sum(len(t.spans) for t in self._traces.values())
        
        return {
            **processor_stats,
            "service_name": self.service_name,
            "traces_count": trace_count,
            "complete_traces": complete_traces,
            "total_spans": total_spans,
            "active_spans": len(self.tracer.get_active_spans())
        }


# Context managers and decorators
class span_context:
    """Context manager for spans"""
    
    def __init__(self, tracer: TracingManager, name: str, **kwargs):
        self.tracer = tracer
        self.name = name
        self.kwargs = kwargs
        self.span: Optional[Span] = None
    
    def __enter__(self) -> Span:
        self.span = self.tracer.start_span(self.name, **self.kwargs)
        self.tracer.context.set_current_span(self.span)
        return self.span
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.span:
            if exc_type:
                self.span.set_status(SpanStatus.ERROR, str(exc_val))
                self.span.add_event("exception", {
                    "exception.type": exc_type.__name__,
                    "exception.message": str(exc_val)
                })
            else:
                self.span.set_status(SpanStatus.OK)
            
            self.tracer.end_span(self.span)
            self.tracer.context.set_current_span(None)


def trace_function(tracer: TracingManager, span_name: Optional[str] = None):
    """Decorator to trace function calls"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            name = span_name or f"{func.__module__}.{func.__name__}"
            
            with span_context(tracer, name, kind=SpanKind.INTERNAL) as span:
                span.set_attribute("function.name", func.__name__)
                span.set_attribute("function.module", func.__module__)
                
                try:
                    result = func(*args, **kwargs)
                    span.add_event("function.completed")
                    return result
                except Exception as e:
                    span.add_event("function.error", {
                        "error.type": type(e).__name__,
                        "error.message": str(e)
                    })
                    raise
        
        async def async_wrapper(*args, **kwargs):
            name = span_name or f"{func.__module__}.{func.__name__}"
            
            with span_context(tracer, name, kind=SpanKind.INTERNAL) as span:
                span.set_attribute("function.name", func.__name__)
                span.set_attribute("function.module", func.__module__)
                span.set_attribute("function.async", True)
                
                try:
                    result = await func(*args, **kwargs)
                    span.add_event("function.completed")
                    return result
                except Exception as e:
                    span.add_event("function.error", {
                        "error.type": type(e).__name__,
                        "error.message": str(e)
                    })
                    raise
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else wrapper
    return decorator


# Global tracing manager instance
_global_tracer: Optional[TracingManager] = None


def get_tracer(service_name: str = "vibe-check") -> TracingManager:
    """Get global tracer instance"""
    global _global_tracer
    if _global_tracer is None:
        _global_tracer = TracingManager(service_name)
    return _global_tracer


def start_span(name: str, **kwargs) -> Span:
    """Start a span using global tracer"""
    return get_tracer().start_span(name, **kwargs)


def trace(name: Optional[str] = None):
    """Decorator to trace function with global tracer"""
    return trace_function(get_tracer(), name)
