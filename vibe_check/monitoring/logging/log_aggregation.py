"""
Log Aggregation Integration
===========================

This module provides comprehensive log aggregation with parsing, structured logging support,
and log-based metrics extraction for the monitoring platform.

File: vibe_check/monitoring/logging/log_aggregation.py
Purpose: Log parsing, structured logging support, and log-based metrics extraction
Related Files: instrumentation/framework_integrations.py, metrics/manager.py
Dependencies: asyncio, json, re, time
"""

import asyncio
import json
import re
import time
import logging
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Callable, Pattern, Union
from enum import Enum
from pathlib import Path
import threading
from collections import defaultdict, deque

logger = logging.getLogger(__name__)


class LogLevel(Enum):
    """Log levels"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class LogFormat(Enum):
    """Supported log formats"""
    JSON = "json"
    STRUCTURED = "structured"
    APACHE_COMMON = "apache_common"
    NGINX = "nginx"
    SYSLOG = "syslog"
    CUSTOM = "custom"


@dataclass
class LogEntry:
    """Parsed log entry"""
    timestamp: float
    level: LogLevel
    message: str
    source: str = "unknown"
    
    # Structured fields
    fields: Dict[str, Any] = field(default_factory=dict)
    
    # Request context (if applicable)
    request_id: Optional[str] = None
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    
    # Performance metrics
    duration_ms: Optional[float] = None
    status_code: Optional[int] = None
    
    # Error context
    error_type: Optional[str] = None
    stack_trace: Optional[str] = None
    
    # Raw log data
    raw_line: str = ""


@dataclass
class LogPattern:
    """Log parsing pattern"""
    name: str
    pattern: Pattern[str]
    field_mapping: Dict[str, str]
    format_type: LogFormat
    sample_rate: float = 1.0


@dataclass
class LogMetric:
    """Log-derived metric"""
    name: str
    value: float
    labels: Dict[str, str] = field(default_factory=dict)
    timestamp: float = field(default_factory=time.time)


class LogParser:
    """Parse logs from various formats"""
    
    def __init__(self):
        self.patterns: List[LogPattern] = []
        self._setup_default_patterns()
    
    def _setup_default_patterns(self):
        """Setup default parsing patterns"""
        
        # JSON logs
        json_pattern = LogPattern(
            name="json",
            pattern=re.compile(r'^.*$'),  # Match any line for JSON parsing
            field_mapping={},
            format_type=LogFormat.JSON
        )
        self.patterns.append(json_pattern)
        
        # Apache Common Log Format
        apache_pattern = LogPattern(
            name="apache_common",
            pattern=re.compile(
                r'^(?P<ip>\S+) \S+ \S+ \[(?P<timestamp>[^\]]+)\] '
                r'"(?P<method>\S+) (?P<path>\S+) (?P<protocol>\S+)" '
                r'(?P<status>\d+) (?P<size>\S+)'
            ),
            field_mapping={
                "ip": "client_ip",
                "method": "http_method",
                "path": "http_path",
                "status": "status_code",
                "size": "response_size"
            },
            format_type=LogFormat.APACHE_COMMON
        )
        self.patterns.append(apache_pattern)
        
        # Nginx logs
        nginx_pattern = LogPattern(
            name="nginx",
            pattern=re.compile(
                r'^(?P<ip>\S+) - \S+ \[(?P<timestamp>[^\]]+)\] '
                r'"(?P<method>\S+) (?P<path>\S+) (?P<protocol>\S+)" '
                r'(?P<status>\d+) (?P<size>\d+) "(?P<referer>[^"]*)" '
                r'"(?P<user_agent>[^"]*)" "(?P<forwarded>[^"]*)"'
            ),
            field_mapping={
                "ip": "client_ip",
                "method": "http_method", 
                "path": "http_path",
                "status": "status_code",
                "size": "response_size",
                "referer": "http_referer",
                "user_agent": "http_user_agent"
            },
            format_type=LogFormat.NGINX
        )
        self.patterns.append(nginx_pattern)
        
        # Python structured logs
        python_pattern = LogPattern(
            name="python_structured",
            pattern=re.compile(
                r'^(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) '
                r'(?P<level>\w+) (?P<logger>\S+) (?P<message>.*)'
            ),
            field_mapping={
                "level": "log_level",
                "logger": "logger_name",
                "message": "log_message"
            },
            format_type=LogFormat.STRUCTURED
        )
        self.patterns.append(python_pattern)
    
    def parse_line(self, line: str, source: str = "unknown") -> Optional[LogEntry]:
        """Parse a single log line"""
        line = line.strip()
        if not line:
            return None
        
        # Try JSON parsing first
        if line.startswith('{') and line.endswith('}'):
            try:
                data = json.loads(line)
                return self._parse_json_log(data, line, source)
            except json.JSONDecodeError:
                pass
        
        # Try pattern matching
        for pattern in self.patterns:
            if pattern.format_type == LogFormat.JSON:
                continue
                
            match = pattern.pattern.match(line)
            if match:
                return self._parse_structured_log(match, pattern, line, source)
        
        # Fallback: create basic log entry
        return LogEntry(
            timestamp=time.time(),
            level=LogLevel.INFO,
            message=line,
            source=source,
            raw_line=line
        )
    
    def _parse_json_log(self, data: Dict[str, Any], raw_line: str, source: str) -> LogEntry:
        """Parse JSON log entry"""
        # Extract common fields
        timestamp = data.get('timestamp', data.get('time', time.time()))
        if isinstance(timestamp, str):
            # Try to parse timestamp string
            try:
                import datetime
                dt = datetime.datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                timestamp = dt.timestamp()
            except:
                timestamp = time.time()
        
        level_str = data.get('level', data.get('severity', 'INFO')).upper()
        try:
            level = LogLevel(level_str)
        except ValueError:
            level = LogLevel.INFO
        
        message = data.get('message', data.get('msg', ''))
        
        # Extract structured fields
        fields = {k: v for k, v in data.items() 
                 if k not in ['timestamp', 'time', 'level', 'severity', 'message', 'msg']}
        
        return LogEntry(
            timestamp=timestamp,
            level=level,
            message=message,
            source=source,
            fields=fields,
            request_id=data.get('request_id'),
            user_id=data.get('user_id'),
            session_id=data.get('session_id'),
            duration_ms=data.get('duration_ms'),
            status_code=data.get('status_code'),
            error_type=data.get('error_type'),
            stack_trace=data.get('stack_trace'),
            raw_line=raw_line
        )
    
    def _parse_structured_log(self, match, pattern: LogPattern, raw_line: str, source: str) -> LogEntry:
        """Parse structured log entry using regex pattern"""
        groups = match.groupdict()
        
        # Map fields using pattern mapping
        fields = {}
        for regex_field, mapped_field in pattern.field_mapping.items():
            if regex_field in groups:
                fields[mapped_field] = groups[regex_field]
        
        # Extract timestamp
        timestamp = time.time()
        if 'timestamp' in groups:
            timestamp_str = groups['timestamp']
            try:
                import datetime
                # Try common timestamp formats
                for fmt in ['%d/%b/%Y:%H:%M:%S %z', '%Y-%m-%d %H:%M:%S,%f']:
                    try:
                        dt = datetime.datetime.strptime(timestamp_str, fmt)
                        timestamp = dt.timestamp()
                        break
                    except ValueError:
                        continue
            except:
                pass
        
        # Extract log level
        level = LogLevel.INFO
        if 'level' in groups:
            try:
                level = LogLevel(groups['level'].upper())
            except ValueError:
                pass
        
        # Extract message
        message = groups.get('message', raw_line)
        
        return LogEntry(
            timestamp=timestamp,
            level=level,
            message=message,
            source=source,
            fields=fields,
            status_code=int(groups['status']) if 'status' in groups else None,
            raw_line=raw_line
        )
    
    def add_pattern(self, pattern: LogPattern):
        """Add custom parsing pattern"""
        self.patterns.append(pattern)


class LogMetricsExtractor:
    """Extract metrics from log entries"""
    
    def __init__(self):
        self.extractors: List[Callable[[LogEntry], List[LogMetric]]] = []
        self._setup_default_extractors()
    
    def _setup_default_extractors(self):
        """Setup default metric extractors"""
        self.extractors.extend([
            self._extract_log_level_metrics,
            self._extract_http_metrics,
            self._extract_error_metrics,
            self._extract_performance_metrics
        ])
    
    def extract_metrics(self, log_entry: LogEntry) -> List[LogMetric]:
        """Extract all metrics from log entry"""
        metrics = []
        for extractor in self.extractors:
            try:
                extracted = extractor(log_entry)
                metrics.extend(extracted)
            except Exception as e:
                logger.warning(f"Metric extraction failed: {e}")
        return metrics
    
    def _extract_log_level_metrics(self, entry: LogEntry) -> List[LogMetric]:
        """Extract log level metrics"""
        return [
            LogMetric(
                name="log_entries_total",
                value=1.0,
                labels={
                    "level": entry.level.value.lower(),
                    "source": entry.source
                },
                timestamp=entry.timestamp
            )
        ]
    
    def _extract_http_metrics(self, entry: LogEntry) -> List[LogMetric]:
        """Extract HTTP-related metrics"""
        metrics = []
        
        if entry.status_code:
            # HTTP status code metrics
            metrics.append(LogMetric(
                name="http_requests_total",
                value=1.0,
                labels={
                    "status_code": str(entry.status_code),
                    "method": entry.fields.get("http_method", "unknown"),
                    "source": entry.source
                },
                timestamp=entry.timestamp
            ))
            
            # HTTP error rate
            if entry.status_code >= 400:
                metrics.append(LogMetric(
                    name="http_errors_total",
                    value=1.0,
                    labels={
                        "status_code": str(entry.status_code),
                        "source": entry.source
                    },
                    timestamp=entry.timestamp
                ))
        
        return metrics
    
    def _extract_error_metrics(self, entry: LogEntry) -> List[LogMetric]:
        """Extract error-related metrics"""
        metrics = []
        
        if entry.level in [LogLevel.ERROR, LogLevel.CRITICAL]:
            metrics.append(LogMetric(
                name="errors_total",
                value=1.0,
                labels={
                    "level": entry.level.value.lower(),
                    "error_type": entry.error_type or "unknown",
                    "source": entry.source
                },
                timestamp=entry.timestamp
            ))
        
        return metrics
    
    def _extract_performance_metrics(self, entry: LogEntry) -> List[LogMetric]:
        """Extract performance-related metrics"""
        metrics = []
        
        if entry.duration_ms is not None:
            metrics.append(LogMetric(
                name="request_duration_ms",
                value=entry.duration_ms,
                labels={"source": entry.source},
                timestamp=entry.timestamp
            ))
            
            # Slow request detection
            if entry.duration_ms > 1000:  # > 1 second
                metrics.append(LogMetric(
                    name="slow_requests_total",
                    value=1.0,
                    labels={"source": entry.source},
                    timestamp=entry.timestamp
                ))
        
        return metrics
    
    def add_extractor(self, extractor: Callable[[LogEntry], List[LogMetric]]):
        """Add custom metric extractor"""
        self.extractors.append(extractor)


class LogAggregator:
    """Main log aggregation engine"""
    
    def __init__(self, max_buffer_size: int = 10000):
        self.parser = LogParser()
        self.metrics_extractor = LogMetricsExtractor()
        self.max_buffer_size = max_buffer_size
        
        # Buffers
        self._log_buffer: deque = deque(maxlen=max_buffer_size)
        self._metrics_buffer: deque = deque(maxlen=max_buffer_size)
        
        # Statistics
        self._stats = {
            "logs_processed": 0,
            "metrics_extracted": 0,
            "parse_errors": 0,
            "last_processed": 0
        }
        
        # Thread safety
        self._lock = threading.Lock()
        
        logger.info("LogAggregator initialized")
    
    def process_log_line(self, line: str, source: str = "unknown") -> Optional[LogEntry]:
        """Process a single log line"""
        try:
            log_entry = self.parser.parse_line(line, source)
            if log_entry:
                with self._lock:
                    self._log_buffer.append(log_entry)
                    self._stats["logs_processed"] += 1
                    self._stats["last_processed"] = time.time()
                
                # Extract metrics
                metrics = self.metrics_extractor.extract_metrics(log_entry)
                if metrics:
                    with self._lock:
                        self._metrics_buffer.extend(metrics)
                        self._stats["metrics_extracted"] += len(metrics)
                
                return log_entry
        except Exception as e:
            with self._lock:
                self._stats["parse_errors"] += 1
            logger.warning(f"Failed to process log line: {e}")
        
        return None
    
    async def process_log_file(self, file_path: Path, source: str = None) -> int:
        """Process entire log file"""
        if source is None:
            source = file_path.name
        
        processed_count = 0
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line in f:
                    log_entry = self.process_log_line(line.strip(), source)
                    if log_entry:
                        processed_count += 1
                    
                    # Yield control periodically
                    if processed_count % 1000 == 0:
                        await asyncio.sleep(0.001)
        
        except Exception as e:
            logger.error(f"Failed to process log file {file_path}: {e}")
        
        return processed_count
    
    def get_recent_logs(self, count: int = 100, level: Optional[LogLevel] = None) -> List[LogEntry]:
        """Get recent log entries"""
        with self._lock:
            logs = list(self._log_buffer)
        
        if level:
            logs = [log for log in logs if log.level == level]
        
        return logs[-count:]
    
    def get_recent_metrics(self, count: int = 100) -> List[LogMetric]:
        """Get recent metrics"""
        with self._lock:
            return list(self._metrics_buffer)[-count:]
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get aggregation statistics"""
        with self._lock:
            stats = self._stats.copy()
        
        stats.update({
            "buffer_size": len(self._log_buffer),
            "metrics_buffer_size": len(self._metrics_buffer),
            "patterns_count": len(self.parser.patterns),
            "extractors_count": len(self.metrics_extractor.extractors)
        })
        
        return stats
    
    def clear_buffers(self):
        """Clear internal buffers"""
        with self._lock:
            self._log_buffer.clear()
            self._metrics_buffer.clear()


# Structured logging integration
class VibeCheckLogHandler(logging.Handler):
    """Custom log handler for Vibe Check integration"""
    
    def __init__(self, aggregator: LogAggregator, source: str = "application"):
        super().__init__()
        self.aggregator = aggregator
        self.source = source
    
    def emit(self, record: logging.LogRecord):
        """Emit log record to aggregator"""
        try:
            # Format the record
            message = self.format(record)
            
            # Create structured log entry
            log_data = {
                "timestamp": record.created,
                "level": record.levelname,
                "message": record.getMessage(),
                "logger": record.name,
                "module": record.module,
                "function": record.funcName,
                "line": record.lineno
            }
            
            # Add exception info if present
            if record.exc_info:
                log_data["error_type"] = record.exc_info[0].__name__
                import traceback
                log_data["stack_trace"] = ''.join(traceback.format_exception(*record.exc_info))
            
            # Convert to JSON and process
            json_line = json.dumps(log_data)
            self.aggregator.process_log_line(json_line, self.source)
            
        except Exception:
            self.handleError(record)


# Convenience functions
def setup_log_aggregation(max_buffer_size: int = 10000) -> LogAggregator:
    """Setup log aggregation with default configuration"""
    return LogAggregator(max_buffer_size)


def integrate_with_logging(aggregator: LogAggregator, 
                          logger_name: str = None,
                          source: str = "application") -> VibeCheckLogHandler:
    """Integrate with Python logging system"""
    handler = VibeCheckLogHandler(aggregator, source)
    handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s %(name)s %(message)s'
    ))
    
    target_logger = logging.getLogger(logger_name)
    target_logger.addHandler(handler)
    
    return handler
