"""
Logging Module
==============

This module provides comprehensive log aggregation, parsing, and metrics extraction
capabilities for the Vibe Check monitoring platform.

Components:
- LogAggregator: Main log aggregation engine
- LogParser: Multi-format log parsing (JSON, Apache, Nginx, structured)
- LogMetricsExtractor: Extract metrics from log entries
- VibeCheckLogHandler: Python logging integration
- LogEntry: Structured log entry representation
- LogMetric: Log-derived metric representation

Usage Examples:

Basic log aggregation:
```python
from vibe_check.monitoring.logging import setup_log_aggregation

aggregator = setup_log_aggregation()
log_entry = aggregator.process_log_line('{"level": "INFO", "message": "Hello"}')
metrics = aggregator.get_recent_metrics()
```

Python logging integration:
```python
from vibe_check.monitoring.logging import integrate_with_logging
import logging

aggregator = setup_log_aggregation()
handler = integrate_with_logging(aggregator, source="my_app")

logger = logging.getLogger("my_app")
logger.info("This will be aggregated and converted to metrics")
```

File processing:
```python
import asyncio
from pathlib import Path

async def process_logs():
    aggregator = setup_log_aggregation()
    count = await aggregator.process_log_file(Path("/var/log/app.log"))
    print(f"Processed {count} log entries")
```
"""

from .log_aggregation import (
    # Main classes
    LogAggregator,
    LogParser,
    LogMetricsExtractor,
    VibeCheckLogHandler,
    
    # Data structures
    LogEntry,
    LogMetric,
    LogPattern,
    
    # Enums
    LogLevel,
    LogFormat,
    
    # Convenience functions
    setup_log_aggregation,
    integrate_with_logging
)

__all__ = [
    # Main classes
    'LogAggregator',
    'LogParser',
    'LogMetricsExtractor',
    'VibeCheckLogHandler',
    
    # Data structures
    'LogEntry',
    'LogMetric',
    'LogPattern',
    
    # Enums
    'LogLevel',
    'LogFormat',
    
    # Convenience functions
    'setup_log_aggregation',
    'integrate_with_logging'
]
