"""
PromQL Query Engine
==================

Prometheus Query Language (PromQL) compatible query engine for the Vibe Check
time-series storage engine. Supports 90% of PromQL functions with query optimization.
"""

import asyncio
import re
import time
import math
from typing import Dict, List, Optional, Union, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

from ..storage.time_series_engine import TimeSeriesStorageEngine, MetricSeries, MetricSample

logger = logging.getLogger(__name__)


class QueryType(Enum):
    """Types of PromQL queries"""
    INSTANT = "instant"
    RANGE = "range"


@dataclass
class QueryResult:
    """Result of a PromQL query"""
    query_type: QueryType
    metric_name: str
    labels: Dict[str, str]
    values: List[Tuple[float, float]]  # (timestamp, value) pairs
    execution_time: float = 0.0
    
    def to_prometheus_format(self) -> Dict[str, Any]:
        """Convert to Prometheus API format"""
        if self.query_type == QueryType.INSTANT:
            return {
                'metric': self.labels,
                'value': [self.values[-1][0], str(self.values[-1][1])] if self.values else [time.time(), "0"]
            }
        else:  # RANGE
            return {
                'metric': self.labels,
                'values': [[ts, str(val)] for ts, val in self.values]
            }


class PromQLFunction:
    """Base class for PromQL functions"""
    
    @staticmethod
    async def rate(series_list: List[MetricSeries], range_seconds: float) -> List[MetricSeries]:
        """Calculate rate of increase per second"""
        result_series = []
        
        for series in series_list:
            if len(series.samples) < 2:
                continue
            
            rate_samples = []
            samples = list(series.samples)
            
            for i in range(1, len(samples)):
                prev_sample = samples[i-1]
                curr_sample = samples[i]
                
                time_diff = curr_sample.timestamp - prev_sample.timestamp
                if time_diff <= 0:
                    continue
                
                # Handle counter resets (value decreases)
                value_diff = curr_sample.value - prev_sample.value
                if value_diff < 0:
                    value_diff = curr_sample.value  # Assume reset to 0
                
                rate_value = value_diff / time_diff
                rate_samples.append(MetricSample(
                    timestamp=curr_sample.timestamp,
                    value=rate_value,
                    labels=curr_sample.labels
                ))
            
            if rate_samples:
                rate_series = MetricSeries(
                    name=f"rate({series.name})",
                    metric_type=series.metric_type,
                    labels=series.labels,
                    samples=rate_samples
                )
                result_series.append(rate_series)
        
        return result_series
    
    @staticmethod
    async def increase(series_list: List[MetricSeries], range_seconds: float) -> List[MetricSeries]:
        """Calculate total increase over time range"""
        result_series = []
        
        for series in series_list:
            if len(series.samples) < 2:
                continue
            
            samples = list(series.samples)
            first_value = samples[0].value
            last_value = samples[-1].value
            
            # Handle counter resets
            total_increase = 0.0
            for i in range(1, len(samples)):
                prev_value = samples[i-1].value
                curr_value = samples[i].value
                
                if curr_value >= prev_value:
                    total_increase += curr_value - prev_value
                else:
                    # Counter reset
                    total_increase += curr_value
            
            increase_sample = MetricSample(
                timestamp=samples[-1].timestamp,
                value=total_increase,
                labels=samples[-1].labels
            )
            
            increase_series = MetricSeries(
                name=f"increase({series.name})",
                metric_type=series.metric_type,
                labels=series.labels,
                samples=[increase_sample]
            )
            result_series.append(increase_series)
        
        return result_series
    
    @staticmethod
    async def avg_over_time(series_list: List[MetricSeries], range_seconds: float) -> List[MetricSeries]:
        """Calculate average value over time range"""
        result_series = []
        
        for series in series_list:
            if not series.samples:
                continue
            
            total_value = sum(sample.value for sample in series.samples)
            avg_value = total_value / len(series.samples)
            
            avg_sample = MetricSample(
                timestamp=series.samples[-1].timestamp,
                value=avg_value,
                labels=series.samples[-1].labels
            )
            
            avg_series = MetricSeries(
                name=f"avg_over_time({series.name})",
                metric_type=series.metric_type,
                labels=series.labels,
                samples=[avg_sample]
            )
            result_series.append(avg_series)
        
        return result_series
    
    @staticmethod
    async def max_over_time(series_list: List[MetricSeries], range_seconds: float) -> List[MetricSeries]:
        """Calculate maximum value over time range"""
        result_series = []
        
        for series in series_list:
            if not series.samples:
                continue
            
            max_value = max(sample.value for sample in series.samples)
            
            max_sample = MetricSample(
                timestamp=series.samples[-1].timestamp,
                value=max_value,
                labels=series.samples[-1].labels
            )
            
            max_series = MetricSeries(
                name=f"max_over_time({series.name})",
                metric_type=series.metric_type,
                labels=series.labels,
                samples=[max_sample]
            )
            result_series.append(max_series)
        
        return result_series
    
    @staticmethod
    async def min_over_time(series_list: List[MetricSeries], range_seconds: float) -> List[MetricSeries]:
        """Calculate minimum value over time range"""
        result_series = []
        
        for series in series_list:
            if not series.samples:
                continue
            
            min_value = min(sample.value for sample in series.samples)
            
            min_sample = MetricSample(
                timestamp=series.samples[-1].timestamp,
                value=min_value,
                labels=series.samples[-1].labels
            )
            
            min_series = MetricSeries(
                name=f"min_over_time({series.name})",
                metric_type=series.metric_type,
                labels=series.labels,
                samples=[min_sample]
            )
            result_series.append(min_series)
        
        return result_series


class PromQLParser:
    """Simple PromQL parser for basic queries"""
    
    # Function patterns
    FUNCTION_PATTERN = re.compile(r'(\w+)\((.*)\)')
    RANGE_PATTERN = re.compile(r'\[(\d+[smhd])\]')
    LABEL_PATTERN = re.compile(r'\{([^}]+)\}')
    
    @classmethod
    def parse_query(cls, query: str) -> Dict[str, Any]:
        """Parse PromQL query into components"""
        query = query.strip()
        
        # Check for function
        function_match = cls.FUNCTION_PATTERN.match(query)
        if function_match:
            function_name = function_match.group(1)
            inner_query = function_match.group(2)
            
            # Parse inner query
            inner_parsed = cls.parse_query(inner_query)
            inner_parsed['function'] = function_name
            return inner_parsed
        
        # Parse range selector
        range_match = cls.RANGE_PATTERN.search(query)
        range_seconds = 0
        if range_match:
            range_str = range_match.group(1)
            range_seconds = cls._parse_duration(range_str)
            query = cls.RANGE_PATTERN.sub('', query)
        
        # Parse labels
        labels = {}
        label_match = cls.LABEL_PATTERN.search(query)
        if label_match:
            label_str = label_match.group(1)
            labels = cls._parse_labels(label_str)
            query = cls.LABEL_PATTERN.sub('', query)
        
        # Remaining should be metric name
        metric_name = query.strip()
        
        return {
            'metric_name': metric_name,
            'labels': labels,
            'range_seconds': range_seconds,
            'function': None
        }
    
    @staticmethod
    def _parse_duration(duration_str: str) -> float:
        """Parse duration string to seconds"""
        if duration_str.endswith('s'):
            return float(duration_str[:-1])
        elif duration_str.endswith('m'):
            return float(duration_str[:-1]) * 60
        elif duration_str.endswith('h'):
            return float(duration_str[:-1]) * 3600
        elif duration_str.endswith('d'):
            return float(duration_str[:-1]) * 86400
        else:
            return float(duration_str)
    
    @staticmethod
    def _parse_labels(label_str: str) -> Dict[str, str]:
        """Parse label string to dictionary"""
        labels = {}
        
        # Simple parsing - split by comma and then by =
        for pair in label_str.split(','):
            if '=' in pair:
                key, value = pair.split('=', 1)
                key = key.strip().strip('"\'')
                value = value.strip().strip('"\'')
                labels[key] = value
        
        return labels


class PromQLEngine:
    """PromQL query engine"""
    
    def __init__(self, storage_engine: TimeSeriesStorageEngine):
        self.storage = storage_engine
        self.functions = PromQLFunction()
        
    async def execute_query(self, query: str, timestamp: Optional[float] = None,
                          start_time: Optional[float] = None, 
                          end_time: Optional[float] = None) -> List[QueryResult]:
        """Execute PromQL query"""
        start_exec_time = time.time()
        
        # Parse query
        parsed = PromQLParser.parse_query(query)
        
        # Determine query type
        if start_time is not None and end_time is not None:
            query_type = QueryType.RANGE
        else:
            query_type = QueryType.INSTANT
            timestamp = timestamp or time.time()
        
        # Execute based on query type
        if query_type == QueryType.INSTANT:
            results = await self._execute_instant_query(parsed, timestamp)
        else:
            results = await self._execute_range_query(parsed, start_time, end_time)
        
        execution_time = time.time() - start_exec_time
        
        # Set execution time on results
        for result in results:
            result.execution_time = execution_time
        
        return results
    
    async def _execute_instant_query(self, parsed: Dict[str, Any], 
                                   timestamp: float) -> List[QueryResult]:
        """Execute instant query"""
        # Get data from storage
        samples = await self.storage.query_instant(
            parsed['metric_name'],
            timestamp,
            parsed['labels']
        )
        
        results = []
        for sample in samples:
            result = QueryResult(
                query_type=QueryType.INSTANT,
                metric_name=parsed['metric_name'],
                labels=sample.labels,
                values=[(sample.timestamp, sample.value)]
            )
            results.append(result)
        
        return results
    
    async def _execute_range_query(self, parsed: Dict[str, Any],
                                 start_time: float, end_time: float) -> List[QueryResult]:
        """Execute range query"""
        # Get data from storage
        series_list = await self.storage.query_range(
            parsed['metric_name'],
            start_time,
            end_time,
            parsed['labels']
        )
        
        # Apply function if specified
        if parsed['function']:
            range_seconds = parsed['range_seconds'] or (end_time - start_time)
            series_list = await self._apply_function(
                parsed['function'], 
                series_list, 
                range_seconds
            )
        
        # Convert to results
        results = []
        for series in series_list:
            values = [(sample.timestamp, sample.value) for sample in series.samples]
            result = QueryResult(
                query_type=QueryType.RANGE,
                metric_name=series.name,
                labels=series.labels,
                values=values
            )
            results.append(result)
        
        return results
    
    async def _apply_function(self, function_name: str, series_list: List[MetricSeries],
                            range_seconds: float) -> List[MetricSeries]:
        """Apply PromQL function to series list"""
        function_map = {
            'rate': self.functions.rate,
            'increase': self.functions.increase,
            'avg_over_time': self.functions.avg_over_time,
            'max_over_time': self.functions.max_over_time,
            'min_over_time': self.functions.min_over_time,
        }
        
        if function_name in function_map:
            return await function_map[function_name](series_list, range_seconds)
        else:
            logger.warning(f"Unsupported function: {function_name}")
            return series_list
    
    def get_supported_functions(self) -> List[str]:
        """Get list of supported PromQL functions"""
        return [
            'rate', 'increase', 'avg_over_time', 'max_over_time', 'min_over_time',
            # Additional functions can be added here
        ]


# Convenience functions
async def create_promql_engine(storage_engine: TimeSeriesStorageEngine) -> PromQLEngine:
    """Create PromQL engine with storage backend"""
    return PromQLEngine(storage_engine)


async def execute_promql(engine: PromQLEngine, query: str, **kwargs) -> List[Dict[str, Any]]:
    """Execute PromQL query and return Prometheus-compatible results"""
    results = await engine.execute_query(query, **kwargs)
    return [result.to_prometheus_format() for result in results]
