"""
Query Engine Module
==================

PromQL-compatible query engine with function support and query optimization
for the Vibe Check monitoring platform.
"""

from .promql_engine import (
    PromQLEngine,
    QueryResult,
    QueryType,
    PromQLFunction,
    PromQLParser,
    create_promql_engine,
    execute_promql,
)

__all__ = [
    'PromQLEngine',
    'QueryResult',
    'QueryType',
    'PromQLFunction',
    'PromQLParser',
    'create_promql_engine',
    'execute_promql',
]
