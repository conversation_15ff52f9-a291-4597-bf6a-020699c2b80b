"""
Code Quality Visualization Components
=====================================

This module provides code quality visualization components including heatmaps,
dependency graphs, and technical debt visualization.

File: vibe_check/monitoring/visualization/charts/code_quality_charts.py
Purpose: Code quality heatmaps, dependency graphs, and technical debt visualization
Related Files: time_series_charts.py, analysis engines
Dependencies: asyncio, json, pathlib
"""

import json
import logging
import time
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Tuple, Set
from enum import Enum
from pathlib import Path
import math

logger = logging.getLogger(__name__)


class QualityMetric(Enum):
    """Code quality metrics"""
    COMPLEXITY = "complexity"
    MAINTAINABILITY = "maintainability"
    COVERAGE = "coverage"
    DUPLICATION = "duplication"
    SECURITY = "security"
    PERFORMANCE = "performance"
    DOCUMENTATION = "documentation"
    STYLE = "style"


class VisualizationType(Enum):
    """Visualization types"""
    HEATMAP = "heatmap"
    TREEMAP = "treemap"
    DEPENDENCY_GRAPH = "dependency_graph"
    SUNBURST = "sunburst"
    RADAR_CHART = "radar_chart"
    MATRIX = "matrix"


class SeverityLevel(Enum):
    """Issue severity levels"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


@dataclass
class CodeQualityData:
    """Code quality data for a file or module"""
    file_path: str
    metrics: Dict[QualityMetric, float] = field(default_factory=dict)
    issues: List[Dict[str, Any]] = field(default_factory=list)
    lines_of_code: int = 0
    complexity_score: float = 0.0
    maintainability_index: float = 0.0
    test_coverage: float = 0.0
    technical_debt_minutes: float = 0.0
    
    # Metadata
    last_modified: Optional[float] = None
    author: Optional[str] = None
    language: str = "python"


@dataclass
class DependencyRelation:
    """Dependency relationship between modules"""
    source: str
    target: str
    dependency_type: str = "import"  # import, inheritance, composition
    strength: float = 1.0  # Dependency strength (0.0 to 1.0)
    is_circular: bool = False


@dataclass
class TechnicalDebtItem:
    """Technical debt item"""
    file_path: str
    line_number: int
    debt_type: str  # code_smell, bug, vulnerability, duplication
    severity: SeverityLevel
    description: str
    effort_minutes: float
    tags: List[str] = field(default_factory=list)


@dataclass
class QualityVisualizationConfig:
    """Configuration for quality visualizations"""
    visualization_type: VisualizationType
    title: str = ""
    width: int = 800
    height: int = 600
    
    # Color schemes
    color_scheme: str = "viridis"  # viridis, plasma, inferno, magma
    show_labels: bool = True
    show_values: bool = True
    
    # Filtering
    min_complexity: float = 0.0
    max_complexity: float = 100.0
    severity_filter: List[SeverityLevel] = field(default_factory=list)
    
    # Layout
    hierarchical_layout: bool = True
    cluster_related: bool = True


class CodeQualityProcessor:
    """Process code quality data for visualization"""
    
    @staticmethod
    def calculate_quality_score(data: CodeQualityData) -> float:
        """Calculate overall quality score (0-100)"""
        scores = []
        
        # Complexity score (lower is better)
        if data.complexity_score > 0:
            complexity_score = max(0, 100 - data.complexity_score * 2)
            scores.append(complexity_score)
        
        # Maintainability index (higher is better)
        if data.maintainability_index > 0:
            scores.append(min(100, data.maintainability_index))
        
        # Test coverage (higher is better)
        if data.test_coverage > 0:
            scores.append(data.test_coverage)
        
        # Technical debt (lower is better)
        if data.technical_debt_minutes > 0:
            debt_score = max(0, 100 - data.technical_debt_minutes / 10)
            scores.append(debt_score)
        
        return sum(scores) / len(scores) if scores else 50.0
    
    @staticmethod
    def categorize_file_by_quality(quality_score: float) -> str:
        """Categorize file by quality score"""
        if quality_score >= 80:
            return "excellent"
        elif quality_score >= 60:
            return "good"
        elif quality_score >= 40:
            return "fair"
        elif quality_score >= 20:
            return "poor"
        else:
            return "critical"
    
    @staticmethod
    def calculate_technical_debt_ratio(data: List[CodeQualityData]) -> float:
        """Calculate technical debt ratio for codebase"""
        total_debt = sum(d.technical_debt_minutes for d in data)
        total_loc = sum(d.lines_of_code for d in data)
        
        if total_loc == 0:
            return 0.0
        
        # Debt ratio: minutes of debt per 1000 lines of code
        return (total_debt / total_loc) * 1000
    
    @staticmethod
    def identify_hotspots(data: List[CodeQualityData], threshold: float = 70.0) -> List[CodeQualityData]:
        """Identify quality hotspots (files with high complexity and low quality)"""
        hotspots = []
        
        for file_data in data:
            quality_score = CodeQualityProcessor.calculate_quality_score(file_data)
            
            # High complexity and low quality = hotspot
            if file_data.complexity_score > threshold and quality_score < 50:
                hotspots.append(file_data)
        
        # Sort by severity (worst first)
        hotspots.sort(key=lambda x: CodeQualityProcessor.calculate_quality_score(x))
        return hotspots


class HeatmapGenerator:
    """Generate code quality heatmaps"""
    
    @staticmethod
    def generate_file_heatmap(data: List[CodeQualityData], 
                            metric: QualityMetric,
                            config: QualityVisualizationConfig) -> Dict[str, Any]:
        """Generate file-level quality heatmap"""
        heatmap_data = {
            "type": "heatmap",
            "title": config.title or f"Code Quality Heatmap - {metric.value.title()}",
            "width": config.width,
            "height": config.height,
            "colorScheme": config.color_scheme,
            "data": [],
            "config": {
                "showLabels": config.show_labels,
                "showValues": config.show_values
            }
        }
        
        # Process files into heatmap cells
        for i, file_data in enumerate(data):
            if metric in file_data.metrics:
                value = file_data.metrics[metric]
            elif metric == QualityMetric.COMPLEXITY:
                value = file_data.complexity_score
            elif metric == QualityMetric.COVERAGE:
                value = file_data.test_coverage
            elif metric == QualityMetric.MAINTAINABILITY:
                value = file_data.maintainability_index
            else:
                value = 0.0
            
            # Calculate position in grid
            grid_size = math.ceil(math.sqrt(len(data)))
            x = i % grid_size
            y = i // grid_size
            
            cell = {
                "x": x,
                "y": y,
                "value": value,
                "label": Path(file_data.file_path).name,
                "fullPath": file_data.file_path,
                "category": CodeQualityProcessor.categorize_file_by_quality(
                    CodeQualityProcessor.calculate_quality_score(file_data)
                ),
                "metadata": {
                    "linesOfCode": file_data.lines_of_code,
                    "complexity": file_data.complexity_score,
                    "maintainability": file_data.maintainability_index,
                    "coverage": file_data.test_coverage,
                    "technicalDebt": file_data.technical_debt_minutes
                }
            }
            
            heatmap_data["data"].append(cell)
        
        return heatmap_data
    
    @staticmethod
    def generate_directory_treemap(data: List[CodeQualityData],
                                 config: QualityVisualizationConfig) -> Dict[str, Any]:
        """Generate directory-level treemap visualization"""
        # Group files by directory
        directory_data = {}
        
        for file_data in data:
            dir_path = str(Path(file_data.file_path).parent)
            
            if dir_path not in directory_data:
                directory_data[dir_path] = {
                    "files": [],
                    "total_loc": 0,
                    "total_complexity": 0,
                    "total_debt": 0,
                    "avg_quality": 0
                }
            
            directory_data[dir_path]["files"].append(file_data)
            directory_data[dir_path]["total_loc"] += file_data.lines_of_code
            directory_data[dir_path]["total_complexity"] += file_data.complexity_score
            directory_data[dir_path]["total_debt"] += file_data.technical_debt_minutes
        
        # Calculate averages
        for dir_path, dir_data in directory_data.items():
            file_count = len(dir_data["files"])
            if file_count > 0:
                dir_data["avg_complexity"] = dir_data["total_complexity"] / file_count
                
                # Calculate average quality score
                quality_scores = [
                    CodeQualityProcessor.calculate_quality_score(f) 
                    for f in dir_data["files"]
                ]
                dir_data["avg_quality"] = sum(quality_scores) / len(quality_scores)
        
        # Generate treemap data
        treemap_data = {
            "type": "treemap",
            "title": config.title or "Code Quality Treemap",
            "width": config.width,
            "height": config.height,
            "data": {
                "name": "root",
                "children": []
            }
        }
        
        for dir_path, dir_data in directory_data.items():
            dir_node = {
                "name": Path(dir_path).name or "root",
                "value": dir_data["total_loc"],
                "quality": dir_data["avg_quality"],
                "complexity": dir_data["avg_complexity"],
                "debt": dir_data["total_debt"],
                "children": []
            }
            
            # Add files as children
            for file_data in dir_data["files"]:
                file_node = {
                    "name": Path(file_data.file_path).name,
                    "value": file_data.lines_of_code,
                    "quality": CodeQualityProcessor.calculate_quality_score(file_data),
                    "complexity": file_data.complexity_score,
                    "debt": file_data.technical_debt_minutes
                }
                dir_node["children"].append(file_node)
            
            treemap_data["data"]["children"].append(dir_node)
        
        return treemap_data


class DependencyGraphGenerator:
    """Generate dependency graph visualizations"""
    
    @staticmethod
    def generate_dependency_graph(dependencies: List[DependencyRelation],
                                quality_data: List[CodeQualityData],
                                config: QualityVisualizationConfig) -> Dict[str, Any]:
        """Generate dependency graph visualization"""
        # Create quality lookup
        quality_lookup = {
            data.file_path: CodeQualityProcessor.calculate_quality_score(data)
            for data in quality_data
        }
        
        # Extract unique nodes
        nodes = set()
        for dep in dependencies:
            nodes.add(dep.source)
            nodes.add(dep.target)
        
        # Generate graph data
        graph_data = {
            "type": "dependency_graph",
            "title": config.title or "Dependency Graph",
            "width": config.width,
            "height": config.height,
            "nodes": [],
            "edges": [],
            "config": {
                "hierarchicalLayout": config.hierarchical_layout,
                "clusterRelated": config.cluster_related
            }
        }
        
        # Add nodes
        for node in nodes:
            quality_score = quality_lookup.get(node, 50.0)
            
            node_data = {
                "id": node,
                "label": Path(node).name,
                "fullPath": node,
                "quality": quality_score,
                "category": CodeQualityProcessor.categorize_file_by_quality(quality_score),
                "size": 10 + (quality_score / 10),  # Size based on quality
                "color": DependencyGraphGenerator._get_quality_color(quality_score)
            }
            
            graph_data["nodes"].append(node_data)
        
        # Add edges
        for dep in dependencies:
            edge_data = {
                "source": dep.source,
                "target": dep.target,
                "type": dep.dependency_type,
                "strength": dep.strength,
                "isCircular": dep.is_circular,
                "width": 1 + dep.strength * 3,  # Width based on strength
                "color": "#ff0000" if dep.is_circular else "#666666"
            }
            
            graph_data["edges"].append(edge_data)
        
        return graph_data
    
    @staticmethod
    def _get_quality_color(quality_score: float) -> str:
        """Get color based on quality score"""
        if quality_score >= 80:
            return "#2ecc71"  # Green
        elif quality_score >= 60:
            return "#f39c12"  # Orange
        elif quality_score >= 40:
            return "#e67e22"  # Dark orange
        elif quality_score >= 20:
            return "#e74c3c"  # Red
        else:
            return "#8e44ad"  # Purple (critical)


class TechnicalDebtVisualizer:
    """Visualize technical debt"""
    
    @staticmethod
    def generate_debt_overview(debt_items: List[TechnicalDebtItem],
                             config: QualityVisualizationConfig) -> Dict[str, Any]:
        """Generate technical debt overview visualization"""
        # Group by file
        file_debt = {}
        for item in debt_items:
            if item.file_path not in file_debt:
                file_debt[item.file_path] = {
                    "items": [],
                    "total_effort": 0,
                    "severity_counts": {s.value: 0 for s in SeverityLevel}
                }
            
            file_debt[item.file_path]["items"].append(item)
            file_debt[item.file_path]["total_effort"] += item.effort_minutes
            file_debt[item.file_path]["severity_counts"][item.severity.value] += 1
        
        # Generate visualization data
        debt_data = {
            "type": "debt_overview",
            "title": config.title or "Technical Debt Overview",
            "width": config.width,
            "height": config.height,
            "summary": {
                "totalItems": len(debt_items),
                "totalEffort": sum(item.effort_minutes for item in debt_items),
                "averageEffortPerItem": sum(item.effort_minutes for item in debt_items) / len(debt_items) if debt_items else 0,
                "severityBreakdown": {s.value: 0 for s in SeverityLevel}
            },
            "files": []
        }
        
        # Calculate severity breakdown
        for item in debt_items:
            debt_data["summary"]["severityBreakdown"][item.severity.value] += 1
        
        # Add file data
        for file_path, file_data in file_debt.items():
            file_info = {
                "path": file_path,
                "name": Path(file_path).name,
                "itemCount": len(file_data["items"]),
                "totalEffort": file_data["total_effort"],
                "severityCounts": file_data["severity_counts"],
                "debtDensity": file_data["total_effort"] / max(1, len(file_data["items"])),
                "items": [
                    {
                        "line": item.line_number,
                        "type": item.debt_type,
                        "severity": item.severity.value,
                        "description": item.description,
                        "effort": item.effort_minutes,
                        "tags": item.tags
                    }
                    for item in file_data["items"]
                ]
            }
            
            debt_data["files"].append(file_info)
        
        # Sort files by total effort (worst first)
        debt_data["files"].sort(key=lambda x: x["totalEffort"], reverse=True)
        
        return debt_data


class CodeQualityDashboard:
    """Main code quality dashboard component"""
    
    def __init__(self, config: QualityVisualizationConfig):
        self.config = config
        self.quality_data: List[CodeQualityData] = []
        self.dependencies: List[DependencyRelation] = []
        self.debt_items: List[TechnicalDebtItem] = []
        
        logger.info("CodeQualityDashboard initialized")
    
    def add_quality_data(self, data: CodeQualityData) -> None:
        """Add code quality data"""
        self.quality_data.append(data)
    
    def add_dependency(self, dependency: DependencyRelation) -> None:
        """Add dependency relationship"""
        self.dependencies.append(dependency)
    
    def add_debt_item(self, debt_item: TechnicalDebtItem) -> None:
        """Add technical debt item"""
        self.debt_items.append(debt_item)
    
    def generate_dashboard_data(self) -> Dict[str, Any]:
        """Generate complete dashboard data"""
        dashboard_data = {
            "title": "Code Quality Dashboard",
            "timestamp": time.time(),
            "summary": self._generate_summary(),
            "visualizations": {}
        }
        
        # Generate different visualizations
        if self.quality_data:
            # Quality heatmap
            heatmap_config = QualityVisualizationConfig(
                visualization_type=VisualizationType.HEATMAP,
                title="Code Quality Heatmap"
            )
            dashboard_data["visualizations"]["heatmap"] = HeatmapGenerator.generate_file_heatmap(
                self.quality_data, QualityMetric.COMPLEXITY, heatmap_config
            )
            
            # Directory treemap
            treemap_config = QualityVisualizationConfig(
                visualization_type=VisualizationType.TREEMAP,
                title="Code Structure Treemap"
            )
            dashboard_data["visualizations"]["treemap"] = HeatmapGenerator.generate_directory_treemap(
                self.quality_data, treemap_config
            )
        
        # Dependency graph
        if self.dependencies:
            graph_config = QualityVisualizationConfig(
                visualization_type=VisualizationType.DEPENDENCY_GRAPH,
                title="Dependency Graph"
            )
            dashboard_data["visualizations"]["dependency_graph"] = DependencyGraphGenerator.generate_dependency_graph(
                self.dependencies, self.quality_data, graph_config
            )
        
        # Technical debt overview
        if self.debt_items:
            debt_config = QualityVisualizationConfig(
                visualization_type=VisualizationType.HEATMAP,
                title="Technical Debt Overview"
            )
            dashboard_data["visualizations"]["technical_debt"] = TechnicalDebtVisualizer.generate_debt_overview(
                self.debt_items, debt_config
            )
        
        return dashboard_data
    
    def _generate_summary(self) -> Dict[str, Any]:
        """Generate dashboard summary statistics"""
        summary = {
            "totalFiles": len(self.quality_data),
            "totalDependencies": len(self.dependencies),
            "totalDebtItems": len(self.debt_items),
            "qualityDistribution": {category: 0 for category in ["excellent", "good", "fair", "poor", "critical"]},
            "averageQuality": 0.0,
            "technicalDebtRatio": 0.0,
            "hotspots": []
        }
        
        if self.quality_data:
            # Calculate quality distribution
            quality_scores = []
            for data in self.quality_data:
                score = CodeQualityProcessor.calculate_quality_score(data)
                quality_scores.append(score)
                category = CodeQualityProcessor.categorize_file_by_quality(score)
                summary["qualityDistribution"][category] += 1
            
            summary["averageQuality"] = sum(quality_scores) / len(quality_scores)
            summary["technicalDebtRatio"] = CodeQualityProcessor.calculate_technical_debt_ratio(self.quality_data)
            
            # Identify hotspots
            hotspots = CodeQualityProcessor.identify_hotspots(self.quality_data)
            summary["hotspots"] = [
                {
                    "path": h.file_path,
                    "quality": CodeQualityProcessor.calculate_quality_score(h),
                    "complexity": h.complexity_score,
                    "debt": h.technical_debt_minutes
                }
                for h in hotspots[:10]  # Top 10 hotspots
            ]
        
        return summary
    
    def export_data(self, format: str = "json") -> str:
        """Export dashboard data"""
        dashboard_data = self.generate_dashboard_data()
        
        if format.lower() == "json":
            return json.dumps(dashboard_data, indent=2)
        else:
            raise ValueError(f"Unsupported export format: {format}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get dashboard statistics"""
        return {
            "quality_data_count": len(self.quality_data),
            "dependencies_count": len(self.dependencies),
            "debt_items_count": len(self.debt_items),
            "config": {
                "visualization_type": self.config.visualization_type.value,
                "color_scheme": self.config.color_scheme,
                "hierarchical_layout": self.config.hierarchical_layout
            }
        }
