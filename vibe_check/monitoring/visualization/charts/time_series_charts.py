"""
Time-Series Chart Components
============================

This module provides advanced time-series chart components with multi-metric
overlays, correlation visualization, and interactive features.

File: vibe_check/monitoring/visualization/charts/time_series_charts.py
Purpose: Advanced time-series charts with multi-metric overlays and correlation
Related Files: time_series_engine.py, promql_engine.py
Dependencies: asyncio, json, time, statistics
"""

import asyncio
import json
import time
import logging
import statistics
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Tuple, Union
from enum import Enum
import math

logger = logging.getLogger(__name__)


class ChartType(Enum):
    """Chart types"""
    LINE = "line"
    AREA = "area"
    BAR = "bar"
    SCATTER = "scatter"
    HEATMAP = "heatmap"
    CANDLESTICK = "candlestick"


class AggregationType(Enum):
    """Aggregation types for time-series data"""
    MEAN = "mean"
    SUM = "sum"
    MIN = "min"
    MAX = "max"
    COUNT = "count"
    PERCENTILE_95 = "p95"
    PERCENTILE_99 = "p99"


class CorrelationType(Enum):
    """Correlation analysis types"""
    PEARSON = "pearson"
    SPEARMAN = "spearman"
    KENDALL = "kendall"
    CROSS_CORRELATION = "cross_correlation"


@dataclass
class TimeSeriesPoint:
    """Single time-series data point"""
    timestamp: float
    value: float
    labels: Dict[str, str] = field(default_factory=dict)


@dataclass
class TimeSeriesData:
    """Time-series dataset"""
    metric_name: str
    points: List[TimeSeriesPoint]
    labels: Dict[str, str] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ChartConfig:
    """Chart configuration"""
    chart_type: ChartType = ChartType.LINE
    title: str = ""
    width: int = 800
    height: int = 400
    
    # Time axis configuration
    time_range_start: Optional[float] = None
    time_range_end: Optional[float] = None
    time_format: str = "%H:%M:%S"
    
    # Y-axis configuration
    y_min: Optional[float] = None
    y_max: Optional[float] = None
    y_label: str = "Value"
    
    # Visual configuration
    show_legend: bool = True
    show_grid: bool = True
    show_tooltips: bool = True
    
    # Multi-metric configuration
    enable_dual_axis: bool = False
    secondary_metrics: List[str] = field(default_factory=list)
    
    # Correlation configuration
    show_correlation: bool = False
    correlation_type: CorrelationType = CorrelationType.PEARSON
    
    # Interactive features
    enable_zoom: bool = True
    enable_pan: bool = True
    enable_crosshair: bool = True


@dataclass
class CorrelationResult:
    """Correlation analysis result"""
    metric1: str
    metric2: str
    correlation_type: CorrelationType
    coefficient: float
    p_value: Optional[float] = None
    lag: int = 0  # For cross-correlation
    confidence_interval: Optional[Tuple[float, float]] = None


class TimeSeriesProcessor:
    """Time-series data processing utilities"""
    
    @staticmethod
    def resample_data(data: List[TimeSeriesPoint], interval_seconds: float) -> List[TimeSeriesPoint]:
        """Resample time-series data to specified interval"""
        if not data:
            return []
        
        # Sort by timestamp
        sorted_data = sorted(data, key=lambda p: p.timestamp)
        
        # Determine time range
        start_time = sorted_data[0].timestamp
        end_time = sorted_data[-1].timestamp
        
        # Create time buckets
        resampled = []
        current_time = start_time
        
        while current_time <= end_time:
            bucket_end = current_time + interval_seconds
            
            # Find points in this bucket
            bucket_points = [
                p for p in sorted_data 
                if current_time <= p.timestamp < bucket_end
            ]
            
            if bucket_points:
                # Aggregate values in bucket (using mean)
                avg_value = statistics.mean(p.value for p in bucket_points)
                
                # Use labels from first point
                labels = bucket_points[0].labels.copy()
                
                resampled.append(TimeSeriesPoint(
                    timestamp=current_time,
                    value=avg_value,
                    labels=labels
                ))
            
            current_time = bucket_end
        
        return resampled
    
    @staticmethod
    def aggregate_data(data: List[TimeSeriesPoint], 
                      aggregation: AggregationType) -> Optional[float]:
        """Aggregate time-series data"""
        if not data:
            return None
        
        values = [p.value for p in data]
        
        if aggregation == AggregationType.MEAN:
            return statistics.mean(values)
        elif aggregation == AggregationType.SUM:
            return sum(values)
        elif aggregation == AggregationType.MIN:
            return min(values)
        elif aggregation == AggregationType.MAX:
            return max(values)
        elif aggregation == AggregationType.COUNT:
            return len(values)
        elif aggregation == AggregationType.PERCENTILE_95:
            return TimeSeriesProcessor._percentile(values, 95)
        elif aggregation == AggregationType.PERCENTILE_99:
            return TimeSeriesProcessor._percentile(values, 99)
        
        return None
    
    @staticmethod
    def _percentile(values: List[float], percentile: float) -> float:
        """Calculate percentile"""
        sorted_values = sorted(values)
        k = (len(sorted_values) - 1) * percentile / 100
        f = math.floor(k)
        c = math.ceil(k)
        
        if f == c:
            return sorted_values[int(k)]
        
        d0 = sorted_values[int(f)] * (c - k)
        d1 = sorted_values[int(c)] * (k - f)
        return d0 + d1
    
    @staticmethod
    def align_time_series(series1: List[TimeSeriesPoint], 
                         series2: List[TimeSeriesPoint],
                         tolerance_seconds: float = 60.0) -> Tuple[List[float], List[float]]:
        """Align two time series for correlation analysis"""
        aligned_values1 = []
        aligned_values2 = []
        
        # Sort both series
        sorted_series1 = sorted(series1, key=lambda p: p.timestamp)
        sorted_series2 = sorted(series2, key=lambda p: p.timestamp)
        
        # Find overlapping time points
        for point1 in sorted_series1:
            # Find closest point in series2
            closest_point2 = None
            min_diff = float('inf')
            
            for point2 in sorted_series2:
                time_diff = abs(point1.timestamp - point2.timestamp)
                if time_diff < min_diff and time_diff <= tolerance_seconds:
                    min_diff = time_diff
                    closest_point2 = point2
            
            if closest_point2:
                aligned_values1.append(point1.value)
                aligned_values2.append(closest_point2.value)
        
        return aligned_values1, aligned_values2


class CorrelationAnalyzer:
    """Correlation analysis for time-series data"""
    
    @staticmethod
    def calculate_pearson_correlation(values1: List[float], 
                                    values2: List[float]) -> float:
        """Calculate Pearson correlation coefficient"""
        if len(values1) != len(values2) or len(values1) < 2:
            return 0.0
        
        n = len(values1)
        
        # Calculate means
        mean1 = statistics.mean(values1)
        mean2 = statistics.mean(values2)
        
        # Calculate correlation coefficient
        numerator = sum((x - mean1) * (y - mean2) for x, y in zip(values1, values2))
        
        sum_sq1 = sum((x - mean1) ** 2 for x in values1)
        sum_sq2 = sum((y - mean2) ** 2 for y in values2)
        
        denominator = math.sqrt(sum_sq1 * sum_sq2)
        
        if denominator == 0:
            return 0.0
        
        return numerator / denominator
    
    @staticmethod
    def calculate_cross_correlation(values1: List[float], 
                                  values2: List[float],
                                  max_lag: int = 10) -> List[Tuple[int, float]]:
        """Calculate cross-correlation with different lags"""
        correlations = []
        
        for lag in range(-max_lag, max_lag + 1):
            if lag == 0:
                corr = CorrelationAnalyzer.calculate_pearson_correlation(values1, values2)
            elif lag > 0:
                # values2 leads values1
                if len(values1) > lag and len(values2) > lag:
                    corr = CorrelationAnalyzer.calculate_pearson_correlation(
                        values1[lag:], values2[:-lag]
                    )
                else:
                    corr = 0.0
            else:
                # values1 leads values2
                lag_abs = abs(lag)
                if len(values1) > lag_abs and len(values2) > lag_abs:
                    corr = CorrelationAnalyzer.calculate_pearson_correlation(
                        values1[:-lag_abs], values2[lag_abs:]
                    )
                else:
                    corr = 0.0
            
            correlations.append((lag, corr))
        
        return correlations
    
    @staticmethod
    def analyze_correlation(series1: TimeSeriesData, 
                          series2: TimeSeriesData,
                          correlation_type: CorrelationType = CorrelationType.PEARSON) -> CorrelationResult:
        """Analyze correlation between two time series"""
        # Align time series
        values1, values2 = TimeSeriesProcessor.align_time_series(
            series1.points, series2.points
        )
        
        if len(values1) < 2:
            return CorrelationResult(
                metric1=series1.metric_name,
                metric2=series2.metric_name,
                correlation_type=correlation_type,
                coefficient=0.0
            )
        
        if correlation_type == CorrelationType.PEARSON:
            coefficient = CorrelationAnalyzer.calculate_pearson_correlation(values1, values2)
            
            return CorrelationResult(
                metric1=series1.metric_name,
                metric2=series2.metric_name,
                correlation_type=correlation_type,
                coefficient=coefficient
            )
        
        elif correlation_type == CorrelationType.CROSS_CORRELATION:
            correlations = CorrelationAnalyzer.calculate_cross_correlation(values1, values2)
            
            # Find maximum correlation
            max_corr = max(correlations, key=lambda x: abs(x[1]))
            
            return CorrelationResult(
                metric1=series1.metric_name,
                metric2=series2.metric_name,
                correlation_type=correlation_type,
                coefficient=max_corr[1],
                lag=max_corr[0]
            )
        
        # Default to Pearson for unsupported types
        coefficient = CorrelationAnalyzer.calculate_pearson_correlation(values1, values2)
        return CorrelationResult(
            metric1=series1.metric_name,
            metric2=series2.metric_name,
            correlation_type=CorrelationType.PEARSON,
            coefficient=coefficient
        )


class TimeSeriesChart:
    """Advanced time-series chart component"""
    
    def __init__(self, config: ChartConfig):
        self.config = config
        self.datasets: List[TimeSeriesData] = []
        self.correlations: List[CorrelationResult] = []
        
        logger.info(f"TimeSeriesChart initialized: {config.chart_type.value}")
    
    def add_dataset(self, dataset: TimeSeriesData) -> None:
        """Add time-series dataset to chart"""
        self.datasets.append(dataset)
        logger.debug(f"Added dataset: {dataset.metric_name} ({len(dataset.points)} points)")
    
    def remove_dataset(self, metric_name: str) -> bool:
        """Remove dataset by metric name"""
        original_count = len(self.datasets)
        self.datasets = [d for d in self.datasets if d.metric_name != metric_name]
        return len(self.datasets) < original_count
    
    def calculate_correlations(self) -> List[CorrelationResult]:
        """Calculate correlations between all dataset pairs"""
        correlations = []
        
        if not self.config.show_correlation or len(self.datasets) < 2:
            return correlations
        
        # Calculate pairwise correlations
        for i in range(len(self.datasets)):
            for j in range(i + 1, len(self.datasets)):
                correlation = CorrelationAnalyzer.analyze_correlation(
                    self.datasets[i],
                    self.datasets[j],
                    self.config.correlation_type
                )
                correlations.append(correlation)
        
        self.correlations = correlations
        return correlations
    
    def generate_chart_data(self) -> Dict[str, Any]:
        """Generate chart data structure for visualization"""
        if not self.datasets:
            return {"error": "No datasets available"}
        
        # Process datasets
        chart_data = {
            "type": self.config.chart_type.value,
            "title": self.config.title,
            "width": self.config.width,
            "height": self.config.height,
            "datasets": [],
            "config": {
                "showLegend": self.config.show_legend,
                "showGrid": self.config.show_grid,
                "showTooltips": self.config.show_tooltips,
                "enableZoom": self.config.enable_zoom,
                "enablePan": self.config.enable_pan,
                "enableCrosshair": self.config.enable_crosshair
            },
            "axes": {
                "x": {
                    "type": "time",
                    "format": self.config.time_format,
                    "min": self.config.time_range_start,
                    "max": self.config.time_range_end
                },
                "y": {
                    "label": self.config.y_label,
                    "min": self.config.y_min,
                    "max": self.config.y_max
                }
            }
        }
        
        # Add datasets
        for i, dataset in enumerate(self.datasets):
            is_secondary = dataset.metric_name in self.config.secondary_metrics
            
            dataset_config = {
                "name": dataset.metric_name,
                "labels": dataset.labels,
                "metadata": dataset.metadata,
                "yAxisID": "y2" if is_secondary and self.config.enable_dual_axis else "y",
                "data": [
                    {
                        "x": point.timestamp * 1000,  # Convert to milliseconds for JS
                        "y": point.value,
                        "labels": point.labels
                    }
                    for point in dataset.points
                ],
                "style": {
                    "color": self._get_color(i),
                    "lineWidth": 2,
                    "pointRadius": 3 if self.config.chart_type == ChartType.SCATTER else 0
                }
            }
            
            chart_data["datasets"].append(dataset_config)
        
        # Add secondary y-axis if needed
        if self.config.enable_dual_axis and self.config.secondary_metrics:
            chart_data["axes"]["y2"] = {
                "label": "Secondary Axis",
                "position": "right",
                "min": None,
                "max": None
            }
        
        # Add correlation data
        if self.config.show_correlation:
            correlations = self.calculate_correlations()
            chart_data["correlations"] = [
                {
                    "metric1": corr.metric1,
                    "metric2": corr.metric2,
                    "type": corr.correlation_type.value,
                    "coefficient": corr.coefficient,
                    "lag": corr.lag,
                    "strength": self._correlation_strength(corr.coefficient)
                }
                for corr in correlations
            ]
        
        return chart_data
    
    def _get_color(self, index: int) -> str:
        """Get color for dataset by index"""
        colors = [
            "#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0",
            "#9966FF", "#FF9F40", "#FF6384", "#C9CBCF"
        ]
        return colors[index % len(colors)]
    
    def _correlation_strength(self, coefficient: float) -> str:
        """Classify correlation strength"""
        abs_coeff = abs(coefficient)
        if abs_coeff >= 0.8:
            return "very_strong"
        elif abs_coeff >= 0.6:
            return "strong"
        elif abs_coeff >= 0.4:
            return "moderate"
        elif abs_coeff >= 0.2:
            return "weak"
        else:
            return "very_weak"
    
    def export_data(self, format: str = "json") -> str:
        """Export chart data in specified format"""
        chart_data = self.generate_chart_data()
        
        if format.lower() == "json":
            return json.dumps(chart_data, indent=2)
        elif format.lower() == "csv":
            return self._export_csv(chart_data)
        else:
            raise ValueError(f"Unsupported export format: {format}")
    
    def _export_csv(self, chart_data: Dict[str, Any]) -> str:
        """Export chart data as CSV"""
        if not chart_data.get("datasets"):
            return ""
        
        # Create CSV header
        headers = ["timestamp"]
        for dataset in chart_data["datasets"]:
            headers.append(dataset["name"])
        
        csv_lines = [",".join(headers)]
        
        # Collect all timestamps
        all_timestamps = set()
        for dataset in chart_data["datasets"]:
            for point in dataset["data"]:
                all_timestamps.add(point["x"])
        
        # Sort timestamps
        sorted_timestamps = sorted(all_timestamps)
        
        # Create CSV rows
        for timestamp in sorted_timestamps:
            row = [str(timestamp)]
            
            for dataset in chart_data["datasets"]:
                # Find value for this timestamp
                value = None
                for point in dataset["data"]:
                    if point["x"] == timestamp:
                        value = point["y"]
                        break
                
                row.append(str(value) if value is not None else "")
            
            csv_lines.append(",".join(row))
        
        return "\n".join(csv_lines)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get chart statistics"""
        if not self.datasets:
            return {}
        
        stats = {
            "total_datasets": len(self.datasets),
            "total_points": sum(len(d.points) for d in self.datasets),
            "time_range": {},
            "value_ranges": {},
            "correlations_count": len(self.correlations)
        }
        
        # Calculate time range
        all_timestamps = []
        for dataset in self.datasets:
            all_timestamps.extend(p.timestamp for p in dataset.points)
        
        if all_timestamps:
            stats["time_range"] = {
                "start": min(all_timestamps),
                "end": max(all_timestamps),
                "duration_seconds": max(all_timestamps) - min(all_timestamps)
            }
        
        # Calculate value ranges for each dataset
        for dataset in self.datasets:
            if dataset.points:
                values = [p.value for p in dataset.points]
                stats["value_ranges"][dataset.metric_name] = {
                    "min": min(values),
                    "max": max(values),
                    "mean": statistics.mean(values),
                    "count": len(values)
                }
        
        return stats
