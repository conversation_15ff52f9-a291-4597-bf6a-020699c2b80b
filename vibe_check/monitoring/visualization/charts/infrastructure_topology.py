"""
Infrastructure Topology Visualization
=====================================

This module provides infrastructure topology visualization with service dependencies
and resource overlays for monitoring system architecture.

File: vibe_check/monitoring/visualization/charts/infrastructure_topology.py
Purpose: System topology visualization with service dependencies and resource overlays
Related Files: code_quality_charts.py, time_series_charts.py
Dependencies: asyncio, json, time
"""

import json
import time
import logging
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Tuple, Set
from enum import Enum
import math

logger = logging.getLogger(__name__)


class NodeType(Enum):
    """Infrastructure node types"""
    SERVICE = "service"
    DATABASE = "database"
    CACHE = "cache"
    LOAD_BALANCER = "load_balancer"
    API_GATEWAY = "api_gateway"
    MESSAGE_QUEUE = "message_queue"
    STORAGE = "storage"
    EXTERNAL_SERVICE = "external_service"
    CONTAINER = "container"
    VM = "virtual_machine"
    PHYSICAL_SERVER = "physical_server"


class ConnectionType(Enum):
    """Connection types between nodes"""
    HTTP = "http"
    HTTPS = "https"
    TCP = "tcp"
    UDP = "udp"
    DATABASE_CONNECTION = "database"
    MESSAGE_QUEUE = "message_queue"
    FILE_SYSTEM = "file_system"
    NETWORK_SHARE = "network_share"


class HealthStatus(Enum):
    """Health status of infrastructure components"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"
    MAINTENANCE = "maintenance"


class LayoutType(Enum):
    """Topology layout types"""
    HIERARCHICAL = "hierarchical"
    FORCE_DIRECTED = "force_directed"
    CIRCULAR = "circular"
    GRID = "grid"
    LAYERED = "layered"


@dataclass
class ResourceMetrics:
    """Resource utilization metrics"""
    cpu_percent: float = 0.0
    memory_percent: float = 0.0
    disk_percent: float = 0.0
    network_in_mbps: float = 0.0
    network_out_mbps: float = 0.0
    connections_count: int = 0
    requests_per_second: float = 0.0
    response_time_ms: float = 0.0
    error_rate_percent: float = 0.0


@dataclass
class InfrastructureNode:
    """Infrastructure node representation"""
    id: str
    name: str
    node_type: NodeType
    health_status: HealthStatus
    
    # Location and grouping
    zone: str = "default"
    region: str = "default"
    environment: str = "production"
    
    # Resource information
    metrics: ResourceMetrics = field(default_factory=ResourceMetrics)
    capacity: Dict[str, float] = field(default_factory=dict)
    
    # Network information
    ip_address: Optional[str] = None
    port: Optional[int] = None
    hostname: Optional[str] = None
    
    # Metadata
    labels: Dict[str, str] = field(default_factory=dict)
    annotations: Dict[str, str] = field(default_factory=dict)
    created_at: float = field(default_factory=time.time)
    last_seen: float = field(default_factory=time.time)


@dataclass
class InfrastructureConnection:
    """Connection between infrastructure nodes"""
    source_id: str
    target_id: str
    connection_type: ConnectionType
    
    # Connection properties
    bandwidth_mbps: float = 0.0
    latency_ms: float = 0.0
    packet_loss_percent: float = 0.0
    
    # Traffic metrics
    bytes_sent: int = 0
    bytes_received: int = 0
    requests_count: int = 0
    errors_count: int = 0
    
    # Configuration
    is_encrypted: bool = False
    is_load_balanced: bool = False
    weight: float = 1.0
    
    # Metadata
    labels: Dict[str, str] = field(default_factory=dict)


@dataclass
class TopologyConfig:
    """Configuration for topology visualization"""
    layout_type: LayoutType = LayoutType.FORCE_DIRECTED
    title: str = "Infrastructure Topology"
    width: int = 1200
    height: int = 800
    
    # Visual configuration
    show_labels: bool = True
    show_metrics: bool = True
    show_health_status: bool = True
    show_connections: bool = True
    
    # Filtering
    environment_filter: List[str] = field(default_factory=list)
    zone_filter: List[str] = field(default_factory=list)
    node_type_filter: List[NodeType] = field(default_factory=list)
    health_filter: List[HealthStatus] = field(default_factory=list)
    
    # Grouping
    group_by_zone: bool = True
    group_by_environment: bool = False
    group_by_type: bool = False
    
    # Resource overlays
    show_cpu_overlay: bool = False
    show_memory_overlay: bool = False
    show_network_overlay: bool = False
    
    # Animation
    enable_animations: bool = True
    auto_refresh_seconds: float = 30.0


class TopologyProcessor:
    """Process topology data for visualization"""
    
    @staticmethod
    def calculate_node_health_score(node: InfrastructureNode) -> float:
        """Calculate overall health score for a node (0-100)"""
        if node.health_status == HealthStatus.HEALTHY:
            base_score = 90
        elif node.health_status == HealthStatus.WARNING:
            base_score = 60
        elif node.health_status == HealthStatus.CRITICAL:
            base_score = 20
        elif node.health_status == HealthStatus.MAINTENANCE:
            base_score = 50
        else:  # UNKNOWN
            base_score = 40
        
        # Adjust based on resource utilization
        metrics = node.metrics
        resource_penalty = 0
        
        # High resource usage reduces health score
        if metrics.cpu_percent > 80:
            resource_penalty += 10
        if metrics.memory_percent > 80:
            resource_penalty += 10
        if metrics.error_rate_percent > 5:
            resource_penalty += 15
        if metrics.response_time_ms > 1000:
            resource_penalty += 10
        
        return max(0, base_score - resource_penalty)
    
    @staticmethod
    def identify_critical_paths(nodes: List[InfrastructureNode], 
                              connections: List[InfrastructureConnection]) -> List[List[str]]:
        """Identify critical paths in the infrastructure"""
        # Build adjacency list
        graph = {}
        for node in nodes:
            graph[node.id] = []
        
        for conn in connections:
            if conn.source_id in graph:
                graph[conn.source_id].append(conn.target_id)
        
        # Find paths from external services to databases (simplified critical path detection)
        critical_paths = []
        
        external_nodes = [n.id for n in nodes if n.node_type == NodeType.EXTERNAL_SERVICE]
        database_nodes = [n.id for n in nodes if n.node_type == NodeType.DATABASE]
        
        for external in external_nodes:
            for database in database_nodes:
                path = TopologyProcessor._find_path(graph, external, database)
                if path:
                    critical_paths.append(path)
        
        return critical_paths
    
    @staticmethod
    def _find_path(graph: Dict[str, List[str]], start: str, end: str, 
                  visited: Optional[Set[str]] = None) -> Optional[List[str]]:
        """Find path between two nodes using DFS"""
        if visited is None:
            visited = set()
        
        if start == end:
            return [start]
        
        if start in visited:
            return None
        
        visited.add(start)
        
        for neighbor in graph.get(start, []):
            path = TopologyProcessor._find_path(graph, neighbor, end, visited.copy())
            if path:
                return [start] + path
        
        return None
    
    @staticmethod
    def calculate_zone_metrics(nodes: List[InfrastructureNode], zone: str) -> Dict[str, float]:
        """Calculate aggregated metrics for a zone"""
        zone_nodes = [n for n in nodes if n.zone == zone]
        
        if not zone_nodes:
            return {}
        
        total_cpu = sum(n.metrics.cpu_percent for n in zone_nodes)
        total_memory = sum(n.metrics.memory_percent for n in zone_nodes)
        total_requests = sum(n.metrics.requests_per_second for n in zone_nodes)
        avg_response_time = sum(n.metrics.response_time_ms for n in zone_nodes) / len(zone_nodes)
        avg_error_rate = sum(n.metrics.error_rate_percent for n in zone_nodes) / len(zone_nodes)
        
        return {
            "avg_cpu_percent": total_cpu / len(zone_nodes),
            "avg_memory_percent": total_memory / len(zone_nodes),
            "total_requests_per_second": total_requests,
            "avg_response_time_ms": avg_response_time,
            "avg_error_rate_percent": avg_error_rate,
            "node_count": len(zone_nodes),
            "healthy_nodes": len([n for n in zone_nodes if n.health_status == HealthStatus.HEALTHY])
        }


class TopologyLayoutEngine:
    """Generate layout coordinates for topology visualization"""
    
    @staticmethod
    def generate_force_directed_layout(nodes: List[InfrastructureNode],
                                     connections: List[InfrastructureConnection],
                                     width: int, height: int) -> Dict[str, Tuple[float, float]]:
        """Generate force-directed layout coordinates"""
        positions = {}
        
        # Initialize random positions
        import random
        for node in nodes:
            positions[node.id] = (
                random.uniform(50, width - 50),
                random.uniform(50, height - 50)
            )
        
        # Simple force-directed algorithm (simplified)
        for iteration in range(50):  # 50 iterations
            forces = {node.id: (0.0, 0.0) for node in nodes}
            
            # Repulsive forces between all nodes
            for i, node1 in enumerate(nodes):
                for node2 in nodes[i+1:]:
                    x1, y1 = positions[node1.id]
                    x2, y2 = positions[node2.id]
                    
                    dx = x2 - x1
                    dy = y2 - y1
                    distance = math.sqrt(dx*dx + dy*dy)
                    
                    if distance > 0:
                        # Repulsive force
                        force = 1000 / (distance * distance)
                        fx = force * dx / distance
                        fy = force * dy / distance
                        
                        forces[node1.id] = (forces[node1.id][0] - fx, forces[node1.id][1] - fy)
                        forces[node2.id] = (forces[node2.id][0] + fx, forces[node2.id][1] + fy)
            
            # Attractive forces for connected nodes
            for conn in connections:
                if conn.source_id in positions and conn.target_id in positions:
                    x1, y1 = positions[conn.source_id]
                    x2, y2 = positions[conn.target_id]
                    
                    dx = x2 - x1
                    dy = y2 - y1
                    distance = math.sqrt(dx*dx + dy*dy)
                    
                    if distance > 0:
                        # Attractive force
                        force = distance * 0.01
                        fx = force * dx / distance
                        fy = force * dy / distance
                        
                        forces[conn.source_id] = (forces[conn.source_id][0] + fx, forces[conn.source_id][1] + fy)
                        forces[conn.target_id] = (forces[conn.target_id][0] - fx, forces[conn.target_id][1] - fy)
            
            # Apply forces
            for node in nodes:
                fx, fy = forces[node.id]
                x, y = positions[node.id]
                
                # Damping
                fx *= 0.1
                fy *= 0.1
                
                # Update position
                new_x = max(50, min(width - 50, x + fx))
                new_y = max(50, min(height - 50, y + fy))
                positions[node.id] = (new_x, new_y)
        
        return positions
    
    @staticmethod
    def generate_hierarchical_layout(nodes: List[InfrastructureNode],
                                   connections: List[InfrastructureConnection],
                                   width: int, height: int) -> Dict[str, Tuple[float, float]]:
        """Generate hierarchical layout based on node types"""
        positions = {}
        
        # Define hierarchy levels
        hierarchy = {
            NodeType.EXTERNAL_SERVICE: 0,
            NodeType.LOAD_BALANCER: 1,
            NodeType.API_GATEWAY: 1,
            NodeType.SERVICE: 2,
            NodeType.CACHE: 3,
            NodeType.MESSAGE_QUEUE: 3,
            NodeType.DATABASE: 4,
            NodeType.STORAGE: 4,
            NodeType.CONTAINER: 2,
            NodeType.VM: 5,
            NodeType.PHYSICAL_SERVER: 6
        }
        
        # Group nodes by level
        levels = {}
        for node in nodes:
            level = hierarchy.get(node.node_type, 3)
            if level not in levels:
                levels[level] = []
            levels[level].append(node)
        
        # Position nodes
        level_height = height / (len(levels) + 1)
        
        for level, level_nodes in levels.items():
            y = level_height * (level + 1)
            node_width = width / (len(level_nodes) + 1)
            
            for i, node in enumerate(level_nodes):
                x = node_width * (i + 1)
                positions[node.id] = (x, y)
        
        return positions


class InfrastructureTopologyVisualizer:
    """Main infrastructure topology visualizer"""
    
    def __init__(self, config: TopologyConfig):
        self.config = config
        self.nodes: List[InfrastructureNode] = []
        self.connections: List[InfrastructureConnection] = []
        
        logger.info("InfrastructureTopologyVisualizer initialized")
    
    def add_node(self, node: InfrastructureNode) -> None:
        """Add infrastructure node"""
        self.nodes.append(node)
    
    def add_connection(self, connection: InfrastructureConnection) -> None:
        """Add infrastructure connection"""
        self.connections.append(connection)
    
    def generate_topology_data(self) -> Dict[str, Any]:
        """Generate topology visualization data"""
        # Apply filters
        filtered_nodes = self._apply_filters(self.nodes)
        filtered_connections = self._filter_connections(self.connections, filtered_nodes)
        
        # Generate layout
        if self.config.layout_type == LayoutType.FORCE_DIRECTED:
            positions = TopologyLayoutEngine.generate_force_directed_layout(
                filtered_nodes, filtered_connections, self.config.width, self.config.height
            )
        elif self.config.layout_type == LayoutType.HIERARCHICAL:
            positions = TopologyLayoutEngine.generate_hierarchical_layout(
                filtered_nodes, filtered_connections, self.config.width, self.config.height
            )
        else:
            # Default to force-directed
            positions = TopologyLayoutEngine.generate_force_directed_layout(
                filtered_nodes, filtered_connections, self.config.width, self.config.height
            )
        
        # Generate visualization data
        topology_data = {
            "type": "infrastructure_topology",
            "title": self.config.title,
            "width": self.config.width,
            "height": self.config.height,
            "layout": self.config.layout_type.value,
            "timestamp": time.time(),
            "nodes": [],
            "connections": [],
            "zones": {},
            "summary": self._generate_summary(filtered_nodes, filtered_connections),
            "config": {
                "showLabels": self.config.show_labels,
                "showMetrics": self.config.show_metrics,
                "showHealthStatus": self.config.show_health_status,
                "showConnections": self.config.show_connections,
                "groupByZone": self.config.group_by_zone,
                "enableAnimations": self.config.enable_animations
            }
        }
        
        # Add nodes
        for node in filtered_nodes:
            x, y = positions.get(node.id, (0, 0))
            
            node_data = {
                "id": node.id,
                "name": node.name,
                "type": node.node_type.value,
                "healthStatus": node.health_status.value,
                "zone": node.zone,
                "region": node.region,
                "environment": node.environment,
                "position": {"x": x, "y": y},
                "metrics": {
                    "cpu": node.metrics.cpu_percent,
                    "memory": node.metrics.memory_percent,
                    "disk": node.metrics.disk_percent,
                    "networkIn": node.metrics.network_in_mbps,
                    "networkOut": node.metrics.network_out_mbps,
                    "requestsPerSecond": node.metrics.requests_per_second,
                    "responseTime": node.metrics.response_time_ms,
                    "errorRate": node.metrics.error_rate_percent
                },
                "healthScore": TopologyProcessor.calculate_node_health_score(node),
                "labels": node.labels,
                "network": {
                    "ipAddress": node.ip_address,
                    "port": node.port,
                    "hostname": node.hostname
                }
            }
            
            topology_data["nodes"].append(node_data)
        
        # Add connections
        for conn in filtered_connections:
            connection_data = {
                "source": conn.source_id,
                "target": conn.target_id,
                "type": conn.connection_type.value,
                "bandwidth": conn.bandwidth_mbps,
                "latency": conn.latency_ms,
                "packetLoss": conn.packet_loss_percent,
                "traffic": {
                    "bytesSent": conn.bytes_sent,
                    "bytesReceived": conn.bytes_received,
                    "requestsCount": conn.requests_count,
                    "errorsCount": conn.errors_count
                },
                "isEncrypted": conn.is_encrypted,
                "isLoadBalanced": conn.is_load_balanced,
                "weight": conn.weight,
                "labels": conn.labels
            }
            
            topology_data["connections"].append(connection_data)
        
        # Add zone information
        zones = set(node.zone for node in filtered_nodes)
        for zone in zones:
            zone_metrics = TopologyProcessor.calculate_zone_metrics(filtered_nodes, zone)
            topology_data["zones"][zone] = zone_metrics
        
        return topology_data
    
    def _apply_filters(self, nodes: List[InfrastructureNode]) -> List[InfrastructureNode]:
        """Apply filters to nodes"""
        filtered = nodes
        
        if self.config.environment_filter:
            filtered = [n for n in filtered if n.environment in self.config.environment_filter]
        
        if self.config.zone_filter:
            filtered = [n for n in filtered if n.zone in self.config.zone_filter]
        
        if self.config.node_type_filter:
            filtered = [n for n in filtered if n.node_type in self.config.node_type_filter]
        
        if self.config.health_filter:
            filtered = [n for n in filtered if n.health_status in self.config.health_filter]
        
        return filtered
    
    def _filter_connections(self, connections: List[InfrastructureConnection],
                          nodes: List[InfrastructureNode]) -> List[InfrastructureConnection]:
        """Filter connections based on available nodes"""
        node_ids = set(node.id for node in nodes)
        return [
            conn for conn in connections
            if conn.source_id in node_ids and conn.target_id in node_ids
        ]
    
    def _generate_summary(self, nodes: List[InfrastructureNode],
                         connections: List[InfrastructureConnection]) -> Dict[str, Any]:
        """Generate topology summary"""
        health_counts = {}
        type_counts = {}
        zone_counts = {}
        
        for node in nodes:
            # Health status counts
            health = node.health_status.value
            health_counts[health] = health_counts.get(health, 0) + 1
            
            # Node type counts
            node_type = node.node_type.value
            type_counts[node_type] = type_counts.get(node_type, 0) + 1
            
            # Zone counts
            zone = node.zone
            zone_counts[zone] = zone_counts.get(zone, 0) + 1
        
        # Calculate overall health
        healthy_nodes = health_counts.get("healthy", 0)
        total_nodes = len(nodes)
        overall_health = (healthy_nodes / total_nodes * 100) if total_nodes > 0 else 0
        
        # Identify critical paths
        critical_paths = TopologyProcessor.identify_critical_paths(nodes, connections)
        
        return {
            "totalNodes": total_nodes,
            "totalConnections": len(connections),
            "overallHealth": overall_health,
            "healthDistribution": health_counts,
            "typeDistribution": type_counts,
            "zoneDistribution": zone_counts,
            "criticalPaths": len(critical_paths),
            "zones": list(zone_counts.keys()),
            "environments": list(set(node.environment for node in nodes))
        }
    
    def export_data(self, format: str = "json") -> str:
        """Export topology data"""
        topology_data = self.generate_topology_data()
        
        if format.lower() == "json":
            return json.dumps(topology_data, indent=2)
        else:
            raise ValueError(f"Unsupported export format: {format}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get topology statistics"""
        return {
            "nodes_count": len(self.nodes),
            "connections_count": len(self.connections),
            "zones": list(set(node.zone for node in self.nodes)),
            "node_types": list(set(node.node_type.value for node in self.nodes)),
            "config": {
                "layout_type": self.config.layout_type.value,
                "group_by_zone": self.config.group_by_zone,
                "show_metrics": self.config.show_metrics
            }
        }
