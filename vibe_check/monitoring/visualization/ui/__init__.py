"""
Dashboard UI Module
===================

This module provides user interface components for the Vibe Check monitoring dashboard
with responsive design, accessibility features, and enhanced user experience.

Components:
- DashboardUIManager: Main dashboard UI manager
- DashboardLayoutManager: Manage dashboard layout and responsive behavior
- AccessibilityManager: Manage dashboard accessibility features
- ThemeManager: Manage dashboard themes and appearance
- DashboardWidget: Dashboard widget configuration
- DashboardConfig: Complete dashboard configuration
- DashboardTheme: Dashboard theme configuration
- AccessibilityConfig: Accessibility configuration
- ResponsiveConfig: Responsive design configuration
- ThemeMode: Dashboard theme modes
- LayoutMode: Dashboard layout modes
- ResponsiveBreakpoint: Responsive design breakpoints
"""

from .dashboard_ui import (
    DashboardUIManager,
    DashboardLayoutManager,
    AccessibilityManager,
    ThemeManager,
    DashboardWidget,
    DashboardConfig,
    DashboardTheme,
    AccessibilityConfig,
    ResponsiveConfig,
    ThemeMode,
    LayoutMode,
    ResponsiveBreakpoint
)

__all__ = [
    'DashboardUIManager',
    'DashboardLayoutManager',
    'AccessibilityManager',
    'ThemeManager',
    'DashboardWidget',
    'DashboardConfig',
    'DashboardTheme',
    'AccessibilityConfig',
    'ResponsiveConfig',
    'ThemeMode',
    'LayoutMode',
    'ResponsiveBreakpoint'
]
