"""
Dashboard User Interface Components
===================================

This module provides user interface components for the monitoring dashboard
with responsive design, accessibility features, and enhanced user experience.

File: vibe_check/monitoring/visualization/ui/dashboard_ui.py
Purpose: Dashboard UX improvements with responsive design and accessibility
Related Files: charts modules, alerting_engine.py
Dependencies: asyncio, json, time
"""

import json
import time
import logging
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum

logger = logging.getLogger(__name__)


class ThemeMode(Enum):
    """Dashboard theme modes"""
    LIGHT = "light"
    DARK = "dark"
    AUTO = "auto"


class LayoutMode(Enum):
    """Dashboard layout modes"""
    GRID = "grid"
    MASONRY = "masonry"
    TABS = "tabs"
    SIDEBAR = "sidebar"


class ResponsiveBreakpoint(Enum):
    """Responsive design breakpoints"""
    MOBILE = "mobile"      # < 768px
    TABLET = "tablet"      # 768px - 1024px
    DESKTOP = "desktop"    # 1024px - 1440px
    LARGE = "large"        # > 1440px


@dataclass
class AccessibilityConfig:
    """Accessibility configuration"""
    enable_screen_reader: bool = True
    enable_keyboard_navigation: bool = True
    enable_high_contrast: bool = False
    enable_reduced_motion: bool = False
    font_size_scale: float = 1.0
    color_blind_friendly: bool = True
    focus_indicators: bool = True


@dataclass
class ResponsiveConfig:
    """Responsive design configuration"""
    mobile_columns: int = 1
    tablet_columns: int = 2
    desktop_columns: int = 3
    large_columns: int = 4
    
    # Breakpoints in pixels
    mobile_max: int = 767
    tablet_min: int = 768
    tablet_max: int = 1023
    desktop_min: int = 1024
    desktop_max: int = 1439
    large_min: int = 1440


@dataclass
class DashboardWidget:
    """Dashboard widget configuration"""
    id: str
    title: str
    widget_type: str  # chart, metric, alert, table, etc.
    
    # Layout properties
    grid_x: int = 0
    grid_y: int = 0
    grid_width: int = 1
    grid_height: int = 1
    
    # Responsive properties
    mobile_width: int = 1
    tablet_width: int = 1
    desktop_width: int = 1
    
    # Widget configuration
    config: Dict[str, Any] = field(default_factory=dict)
    data_source: Optional[str] = None
    refresh_interval: float = 30.0
    
    # UI properties
    collapsible: bool = True
    resizable: bool = True
    draggable: bool = True
    
    # Accessibility
    aria_label: Optional[str] = None
    description: Optional[str] = None


@dataclass
class DashboardTheme:
    """Dashboard theme configuration"""
    mode: ThemeMode = ThemeMode.AUTO
    
    # Color palette
    primary_color: str = "#007bff"
    secondary_color: str = "#6c757d"
    success_color: str = "#28a745"
    warning_color: str = "#ffc107"
    danger_color: str = "#dc3545"
    info_color: str = "#17a2b8"
    
    # Background colors
    background_primary: str = "#ffffff"
    background_secondary: str = "#f8f9fa"
    background_tertiary: str = "#e9ecef"
    
    # Text colors
    text_primary: str = "#212529"
    text_secondary: str = "#6c757d"
    text_muted: str = "#adb5bd"
    
    # Border and shadow
    border_color: str = "#dee2e6"
    shadow_color: str = "rgba(0,0,0,0.1)"
    
    # Typography
    font_family: str = "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
    font_size_base: str = "14px"
    line_height: float = 1.5


@dataclass
class DashboardConfig:
    """Complete dashboard configuration"""
    title: str = "Monitoring Dashboard"
    layout_mode: LayoutMode = LayoutMode.GRID
    theme: DashboardTheme = field(default_factory=DashboardTheme)
    responsive: ResponsiveConfig = field(default_factory=ResponsiveConfig)
    accessibility: AccessibilityConfig = field(default_factory=AccessibilityConfig)
    
    # Auto-refresh
    auto_refresh: bool = True
    refresh_interval: float = 30.0
    
    # User preferences
    save_layout: bool = True
    save_filters: bool = True
    
    # Performance
    lazy_loading: bool = True
    virtual_scrolling: bool = True
    debounce_ms: int = 300


class DashboardLayoutManager:
    """Manage dashboard layout and responsive behavior"""
    
    def __init__(self, config: DashboardConfig):
        self.config = config
        self.widgets: List[DashboardWidget] = []
        self.current_breakpoint = ResponsiveBreakpoint.DESKTOP
        
        logger.info("DashboardLayoutManager initialized")
    
    def add_widget(self, widget: DashboardWidget) -> None:
        """Add widget to dashboard"""
        self.widgets.append(widget)
        logger.debug(f"Added widget: {widget.id}")
    
    def remove_widget(self, widget_id: str) -> bool:
        """Remove widget from dashboard"""
        original_count = len(self.widgets)
        self.widgets = [w for w in self.widgets if w.id != widget_id]
        return len(self.widgets) < original_count
    
    def update_breakpoint(self, viewport_width: int) -> ResponsiveBreakpoint:
        """Update current responsive breakpoint"""
        if viewport_width <= self.config.responsive.mobile_max:
            self.current_breakpoint = ResponsiveBreakpoint.MOBILE
        elif viewport_width <= self.config.responsive.tablet_max:
            self.current_breakpoint = ResponsiveBreakpoint.TABLET
        elif viewport_width <= self.config.responsive.desktop_max:
            self.current_breakpoint = ResponsiveBreakpoint.DESKTOP
        else:
            self.current_breakpoint = ResponsiveBreakpoint.LARGE
        
        return self.current_breakpoint
    
    def generate_responsive_layout(self, viewport_width: int) -> Dict[str, Any]:
        """Generate responsive layout for current viewport"""
        breakpoint = self.update_breakpoint(viewport_width)
        
        # Get column count for current breakpoint
        if breakpoint == ResponsiveBreakpoint.MOBILE:
            columns = self.config.responsive.mobile_columns
        elif breakpoint == ResponsiveBreakpoint.TABLET:
            columns = self.config.responsive.tablet_columns
        elif breakpoint == ResponsiveBreakpoint.DESKTOP:
            columns = self.config.responsive.desktop_columns
        else:  # LARGE
            columns = self.config.responsive.large_columns
        
        # Calculate widget positions
        layout_items = []
        current_row = 0
        current_col = 0
        
        for widget in self.widgets:
            # Get widget width for current breakpoint
            if breakpoint == ResponsiveBreakpoint.MOBILE:
                widget_width = widget.mobile_width
            elif breakpoint == ResponsiveBreakpoint.TABLET:
                widget_width = widget.tablet_width
            else:
                widget_width = widget.desktop_width
            
            # Ensure widget fits in available columns
            widget_width = min(widget_width, columns)
            
            # Check if widget fits in current row
            if current_col + widget_width > columns:
                current_row += 1
                current_col = 0
            
            layout_item = {
                "id": widget.id,
                "x": current_col,
                "y": current_row,
                "width": widget_width,
                "height": widget.grid_height,
                "breakpoint": breakpoint.value
            }
            
            layout_items.append(layout_item)
            current_col += widget_width
        
        return {
            "breakpoint": breakpoint.value,
            "columns": columns,
            "items": layout_items,
            "viewport_width": viewport_width
        }


class AccessibilityManager:
    """Manage dashboard accessibility features"""
    
    def __init__(self, config: AccessibilityConfig):
        self.config = config
        logger.info("AccessibilityManager initialized")
    
    def generate_aria_attributes(self, widget: DashboardWidget) -> Dict[str, str]:
        """Generate ARIA attributes for widget"""
        attributes = {}
        
        if self.config.enable_screen_reader:
            attributes["role"] = "region"
            attributes["aria-label"] = widget.aria_label or widget.title
            
            if widget.description:
                attributes["aria-describedby"] = f"{widget.id}-description"
        
        if widget.collapsible:
            attributes["aria-expanded"] = "true"
        
        return attributes
    
    def generate_keyboard_navigation(self) -> Dict[str, Any]:
        """Generate keyboard navigation configuration"""
        if not self.config.enable_keyboard_navigation:
            return {}
        
        return {
            "tabIndex": 0,
            "keyboardShortcuts": {
                "Tab": "Navigate to next widget",
                "Shift+Tab": "Navigate to previous widget",
                "Enter": "Activate widget",
                "Escape": "Close modal/dropdown",
                "Arrow keys": "Navigate within widget"
            },
            "focusManagement": {
                "trapFocus": True,
                "restoreFocus": True,
                "skipLinks": True
            }
        }
    
    def generate_color_scheme(self, theme: DashboardTheme) -> Dict[str, str]:
        """Generate accessible color scheme"""
        if not self.config.color_blind_friendly:
            return {}
        
        # Color-blind friendly palette
        return {
            "success": "#2E8B57",  # Sea Green
            "warning": "#FF8C00",  # Dark Orange
            "danger": "#DC143C",   # Crimson
            "info": "#4682B4",     # Steel Blue
            "primary": "#191970",  # Midnight Blue
            "secondary": "#708090" # Slate Gray
        }


class ThemeManager:
    """Manage dashboard themes and appearance"""
    
    def __init__(self, theme: DashboardTheme):
        self.theme = theme
        self.current_mode = theme.mode
        logger.info(f"ThemeManager initialized with mode: {theme.mode.value}")
    
    def detect_system_theme(self) -> ThemeMode:
        """Detect system theme preference"""
        # In a real implementation, this would check system preferences
        # For now, return a default
        return ThemeMode.LIGHT
    
    def get_effective_theme(self) -> ThemeMode:
        """Get the effective theme mode"""
        if self.theme.mode == ThemeMode.AUTO:
            return self.detect_system_theme()
        return self.theme.mode
    
    def generate_css_variables(self) -> Dict[str, str]:
        """Generate CSS custom properties for theme"""
        effective_mode = self.get_effective_theme()
        
        if effective_mode == ThemeMode.DARK:
            # Dark theme colors
            return {
                "--color-primary": self.theme.primary_color,
                "--color-background-primary": "#1a1a1a",
                "--color-background-secondary": "#2d2d2d",
                "--color-background-tertiary": "#404040",
                "--color-text-primary": "#ffffff",
                "--color-text-secondary": "#cccccc",
                "--color-text-muted": "#999999",
                "--color-border": "#555555",
                "--color-shadow": "rgba(0,0,0,0.3)",
                "--font-family": self.theme.font_family,
                "--font-size-base": self.theme.font_size_base,
                "--line-height": str(self.theme.line_height)
            }
        else:
            # Light theme colors
            return {
                "--color-primary": self.theme.primary_color,
                "--color-background-primary": self.theme.background_primary,
                "--color-background-secondary": self.theme.background_secondary,
                "--color-background-tertiary": self.theme.background_tertiary,
                "--color-text-primary": self.theme.text_primary,
                "--color-text-secondary": self.theme.text_secondary,
                "--color-text-muted": self.theme.text_muted,
                "--color-border": self.theme.border_color,
                "--color-shadow": self.theme.shadow_color,
                "--font-family": self.theme.font_family,
                "--font-size-base": self.theme.font_size_base,
                "--line-height": str(self.theme.line_height)
            }


class DashboardUIManager:
    """Main dashboard UI manager"""
    
    def __init__(self, config: DashboardConfig):
        self.config = config
        self.layout_manager = DashboardLayoutManager(config)
        self.accessibility_manager = AccessibilityManager(config.accessibility)
        self.theme_manager = ThemeManager(config.theme)
        
        # UI state
        self.is_loading = False
        self.error_message: Optional[str] = None
        self.last_updated = time.time()
        
        logger.info("DashboardUIManager initialized")
    
    def add_widget(self, widget: DashboardWidget) -> None:
        """Add widget to dashboard"""
        self.layout_manager.add_widget(widget)
    
    def generate_dashboard_ui(self, viewport_width: int = 1200) -> Dict[str, Any]:
        """Generate complete dashboard UI configuration"""
        # Generate responsive layout
        layout = self.layout_manager.generate_responsive_layout(viewport_width)
        
        # Generate theme
        css_variables = self.theme_manager.generate_css_variables()
        
        # Generate accessibility features
        keyboard_nav = self.accessibility_manager.generate_keyboard_navigation()
        
        dashboard_ui = {
            "config": {
                "title": self.config.title,
                "layoutMode": self.config.layout_mode.value,
                "autoRefresh": self.config.auto_refresh,
                "refreshInterval": self.config.refresh_interval,
                "lazyLoading": self.config.lazy_loading,
                "virtualScrolling": self.config.virtual_scrolling
            },
            "layout": layout,
            "theme": {
                "mode": self.theme_manager.get_effective_theme().value,
                "cssVariables": css_variables
            },
            "accessibility": {
                "enabled": self.config.accessibility.enable_screen_reader,
                "keyboardNavigation": keyboard_nav,
                "highContrast": self.config.accessibility.enable_high_contrast,
                "reducedMotion": self.config.accessibility.enable_reduced_motion,
                "fontScale": self.config.accessibility.font_size_scale
            },
            "widgets": [],
            "state": {
                "isLoading": self.is_loading,
                "errorMessage": self.error_message,
                "lastUpdated": self.last_updated,
                "currentBreakpoint": layout["breakpoint"]
            },
            "performance": {
                "debounceMs": self.config.debounce_ms,
                "lazyLoading": self.config.lazy_loading,
                "virtualScrolling": self.config.virtual_scrolling
            }
        }
        
        # Add widget configurations
        for widget in self.layout_manager.widgets:
            aria_attributes = self.accessibility_manager.generate_aria_attributes(widget)
            
            widget_config = {
                "id": widget.id,
                "title": widget.title,
                "type": widget.widget_type,
                "config": widget.config,
                "dataSource": widget.data_source,
                "refreshInterval": widget.refresh_interval,
                "ui": {
                    "collapsible": widget.collapsible,
                    "resizable": widget.resizable,
                    "draggable": widget.draggable
                },
                "accessibility": aria_attributes,
                "responsive": {
                    "mobileWidth": widget.mobile_width,
                    "tabletWidth": widget.tablet_width,
                    "desktopWidth": widget.desktop_width
                }
            }
            
            dashboard_ui["widgets"].append(widget_config)
        
        return dashboard_ui
    
    def update_loading_state(self, is_loading: bool, error_message: Optional[str] = None) -> None:
        """Update dashboard loading state"""
        self.is_loading = is_loading
        self.error_message = error_message
        if not is_loading:
            self.last_updated = time.time()
    
    def export_configuration(self) -> str:
        """Export dashboard configuration as JSON"""
        config_data = {
            "title": self.config.title,
            "layout_mode": self.config.layout_mode.value,
            "theme": {
                "mode": self.config.theme.mode.value,
                "primary_color": self.config.theme.primary_color,
                "font_family": self.config.theme.font_family
            },
            "responsive": {
                "mobile_columns": self.config.responsive.mobile_columns,
                "tablet_columns": self.config.responsive.tablet_columns,
                "desktop_columns": self.config.responsive.desktop_columns
            },
            "accessibility": {
                "screen_reader": self.config.accessibility.enable_screen_reader,
                "keyboard_navigation": self.config.accessibility.enable_keyboard_navigation,
                "high_contrast": self.config.accessibility.enable_high_contrast,
                "font_scale": self.config.accessibility.font_size_scale
            },
            "widgets": [
                {
                    "id": w.id,
                    "title": w.title,
                    "type": w.widget_type,
                    "grid_x": w.grid_x,
                    "grid_y": w.grid_y,
                    "grid_width": w.grid_width,
                    "grid_height": w.grid_height,
                    "config": w.config
                }
                for w in self.layout_manager.widgets
            ]
        }
        
        return json.dumps(config_data, indent=2)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get dashboard UI statistics"""
        return {
            "widgets_count": len(self.layout_manager.widgets),
            "current_breakpoint": self.layout_manager.current_breakpoint.value,
            "theme_mode": self.theme_manager.get_effective_theme().value,
            "accessibility_enabled": self.config.accessibility.enable_screen_reader,
            "auto_refresh": self.config.auto_refresh,
            "last_updated": self.last_updated
        }
