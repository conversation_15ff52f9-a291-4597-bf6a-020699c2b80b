"""
Execution Time Profiler
=======================

Advanced execution time profiling with call graph generation, bottleneck detection,
and performance analysis. Builds upon the process instrumentation foundation.
"""

import asyncio
import time
import sys
import threading
import traceback
import inspect
from typing import Dict, List, Optional, Any, Callable, Set, Tuple, Union
from dataclasses import dataclass, field
from collections import defaultdict, deque
from functools import wraps
import logging
from pathlib import Path
import json

logger = logging.getLogger(__name__)


@dataclass
class CallFrame:
    """Represents a single call frame in the execution stack"""
    function_name: str
    module_name: str
    filename: str
    line_number: int
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    memory_start: int = 0
    memory_end: int = 0
    memory_delta: int = 0
    parent_frame: Optional['CallFrame'] = None
    child_frames: List['CallFrame'] = field(default_factory=list)
    exception_info: Optional[str] = None
    
    @property
    def is_complete(self) -> bool:
        """Check if frame execution is complete"""
        return self.end_time is not None
    
    def complete_frame(self, end_time: float, memory_end: int = 0):
        """Mark frame as complete"""
        self.end_time = end_time
        self.duration = end_time - self.start_time
        self.memory_end = memory_end
        self.memory_delta = memory_end - self.memory_start
    
    def add_child(self, child_frame: 'CallFrame'):
        """Add child frame"""
        child_frame.parent_frame = self
        self.child_frames.append(child_frame)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'function_name': self.function_name,
            'module_name': self.module_name,
            'filename': self.filename,
            'line_number': self.line_number,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'duration': self.duration,
            'memory_delta': self.memory_delta,
            'child_frames': [child.to_dict() for child in self.child_frames],
            'exception_info': self.exception_info
        }


@dataclass
class ProfileSession:
    """Represents a profiling session"""
    session_id: str
    start_time: float
    end_time: Optional[float] = None
    root_frames: List[CallFrame] = field(default_factory=list)
    total_functions: int = 0
    total_duration: float = 0.0
    bottlenecks: List[Dict[str, Any]] = field(default_factory=list)
    
    def add_root_frame(self, frame: CallFrame):
        """Add root-level frame"""
        self.root_frames.append(frame)
    
    def complete_session(self):
        """Mark session as complete and calculate statistics"""
        self.end_time = time.time()
        self._calculate_statistics()
    
    def _calculate_statistics(self):
        """Calculate session statistics"""
        def traverse_frames(frames: List[CallFrame]):
            count = 0
            total_time = 0.0
            for frame in frames:
                count += 1
                if frame.duration:
                    total_time += frame.duration
                child_count, child_time = traverse_frames(frame.child_frames)
                count += child_count
                total_time += child_time
            return count, total_time
        
        self.total_functions, self.total_duration = traverse_frames(self.root_frames)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'session_id': self.session_id,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'total_functions': self.total_functions,
            'total_duration': self.total_duration,
            'root_frames': [frame.to_dict() for frame in self.root_frames],
            'bottlenecks': self.bottlenecks
        }


class ExecutionProfiler:
    """Advanced execution time profiler with call graph generation"""
    
    def __init__(self, enable_memory_tracking: bool = True, 
                 max_call_depth: int = 100, bottleneck_threshold: float = 0.1):
        self.enable_memory_tracking = enable_memory_tracking
        self.max_call_depth = max_call_depth
        self.bottleneck_threshold = bottleneck_threshold
        
        # Profiling state
        self.is_profiling = False
        self.current_session: Optional[ProfileSession] = None
        self.call_stack: Dict[int, List[CallFrame]] = defaultdict(list)  # Per-thread stacks
        self.instrumented_functions: Set[Callable] = set()
        
        # Statistics
        self.sessions: List[ProfileSession] = []
        self.function_stats: Dict[str, Dict[str, Any]] = defaultdict(lambda: {
            'call_count': 0,
            'total_time': 0.0,
            'min_time': float('inf'),
            'max_time': 0.0,
            'avg_time': 0.0,
            'total_memory': 0,
            'error_count': 0
        })
        
        # Performance tracking
        self.profiler_overhead = 0.0
        self.profiler_calls = 0
        
        logger.info("ExecutionProfiler initialized")
    
    def start_profiling(self, session_id: Optional[str] = None) -> str:
        """Start a new profiling session"""
        if self.is_profiling:
            raise RuntimeError("Profiling session already active")
        
        session_id = session_id or f"session_{int(time.time())}"
        self.current_session = ProfileSession(
            session_id=session_id,
            start_time=time.time()
        )
        
        self.is_profiling = True
        self.call_stack.clear()
        
        logger.info(f"Started profiling session: {session_id}")
        return session_id
    
    def stop_profiling(self) -> Optional[ProfileSession]:
        """Stop current profiling session"""
        if not self.is_profiling or not self.current_session:
            return None
        
        self.current_session.complete_session()
        self._detect_bottlenecks()
        
        session = self.current_session
        self.sessions.append(session)
        
        self.is_profiling = False
        self.current_session = None
        self.call_stack.clear()
        
        logger.info(f"Stopped profiling session: {session.session_id}")
        return session
    
    def profile_function(self, func: Optional[Callable] = None, *, 
                        name: Optional[str] = None, track_memory: bool = None):
        """Decorator to profile function execution"""
        if track_memory is None:
            track_memory = self.enable_memory_tracking
        
        def decorator(f: Callable) -> Callable:
            func_name = name or f.__name__
            func_module = f.__module__ or "unknown"
            func_file = inspect.getfile(f) if hasattr(f, '__code__') else "unknown"
            func_line = f.__code__.co_firstlineno if hasattr(f, '__code__') else 0
            
            self.instrumented_functions.add(f)
            
            @wraps(f)
            async def async_wrapper(*args, **kwargs):
                return await self._execute_profiled_async(
                    f, func_name, func_module, func_file, func_line, track_memory, *args, **kwargs
                )
            
            @wraps(f)
            def sync_wrapper(*args, **kwargs):
                return self._execute_profiled_sync(
                    f, func_name, func_module, func_file, func_line, track_memory, *args, **kwargs
                )
            
            # Return appropriate wrapper
            if asyncio.iscoroutinefunction(f):
                return async_wrapper
            else:
                return sync_wrapper
        
        if func is None:
            return decorator
        else:
            return decorator(func)
    
    async def _execute_profiled_async(self, func: Callable, func_name: str, 
                                    func_module: str, func_file: str, func_line: int,
                                    track_memory: bool, *args, **kwargs):
        """Execute profiled async function"""
        if not self.is_profiling:
            return await func(*args, **kwargs)
        
        thread_id = threading.get_ident()
        profiler_start = time.perf_counter()
        
        # Create call frame
        frame = self._create_call_frame(func_name, func_module, func_file, func_line, track_memory)
        
        # Add to call stack
        current_stack = self.call_stack[thread_id]
        if current_stack and len(current_stack) < self.max_call_depth:
            current_stack[-1].add_child(frame)
        elif not current_stack:
            self.current_session.add_root_frame(frame)
        
        current_stack.append(frame)
        
        try:
            result = await func(*args, **kwargs)
            self._complete_call_frame(frame, track_memory)
            return result
            
        except Exception as e:
            self._complete_call_frame(frame, track_memory, exception=e)
            raise
        finally:
            current_stack.pop()
            
            # Track profiler overhead
            profiler_time = time.perf_counter() - profiler_start
            self.profiler_overhead += profiler_time
            self.profiler_calls += 1
    
    def _execute_profiled_sync(self, func: Callable, func_name: str,
                             func_module: str, func_file: str, func_line: int,
                             track_memory: bool, *args, **kwargs):
        """Execute profiled sync function"""
        if not self.is_profiling:
            return func(*args, **kwargs)
        
        thread_id = threading.get_ident()
        profiler_start = time.perf_counter()
        
        # Create call frame
        frame = self._create_call_frame(func_name, func_module, func_file, func_line, track_memory)
        
        # Add to call stack
        current_stack = self.call_stack[thread_id]
        if current_stack and len(current_stack) < self.max_call_depth:
            current_stack[-1].add_child(frame)
        elif not current_stack:
            self.current_session.add_root_frame(frame)
        
        current_stack.append(frame)
        
        try:
            result = func(*args, **kwargs)
            self._complete_call_frame(frame, track_memory)
            return result
            
        except Exception as e:
            self._complete_call_frame(frame, track_memory, exception=e)
            raise
        finally:
            current_stack.pop()
            
            # Track profiler overhead
            profiler_time = time.perf_counter() - profiler_start
            self.profiler_overhead += profiler_time
            self.profiler_calls += 1
    
    def _create_call_frame(self, func_name: str, func_module: str, 
                          func_file: str, func_line: int, track_memory: bool) -> CallFrame:
        """Create new call frame"""
        memory_start = 0
        if track_memory:
            try:
                import tracemalloc
                if tracemalloc.is_tracing():
                    memory_start = tracemalloc.get_traced_memory()[0]
            except Exception:
                pass
        
        return CallFrame(
            function_name=func_name,
            module_name=func_module,
            filename=func_file,
            line_number=func_line,
            start_time=time.perf_counter(),
            memory_start=memory_start
        )
    
    def _complete_call_frame(self, frame: CallFrame, track_memory: bool, 
                           exception: Optional[Exception] = None):
        """Complete call frame execution"""
        end_time = time.perf_counter()
        memory_end = frame.memory_start
        
        if track_memory:
            try:
                import tracemalloc
                if tracemalloc.is_tracing():
                    memory_end = tracemalloc.get_traced_memory()[0]
            except Exception:
                pass
        
        frame.complete_frame(end_time, memory_end)
        
        if exception:
            frame.exception_info = f"{type(exception).__name__}: {str(exception)}"
        
        # Update function statistics
        func_key = f"{frame.module_name}.{frame.function_name}"
        stats = self.function_stats[func_key]
        stats['call_count'] += 1
        stats['total_time'] += frame.duration
        stats['min_time'] = min(stats['min_time'], frame.duration)
        stats['max_time'] = max(stats['max_time'], frame.duration)
        stats['avg_time'] = stats['total_time'] / stats['call_count']
        stats['total_memory'] += frame.memory_delta
        
        if exception:
            stats['error_count'] += 1
    
    def _detect_bottlenecks(self):
        """Detect performance bottlenecks in the session"""
        if not self.current_session:
            return
        
        bottlenecks = []
        
        def analyze_frame(frame: CallFrame, depth: int = 0):
            if frame.duration and frame.duration > self.bottleneck_threshold:
                bottlenecks.append({
                    'function': f"{frame.module_name}.{frame.function_name}",
                    'duration': frame.duration,
                    'memory_delta': frame.memory_delta,
                    'depth': depth,
                    'filename': frame.filename,
                    'line_number': frame.line_number,
                    'child_count': len(frame.child_frames)
                })
            
            for child in frame.child_frames:
                analyze_frame(child, depth + 1)
        
        for root_frame in self.current_session.root_frames:
            analyze_frame(root_frame)
        
        # Sort by duration (descending)
        bottlenecks.sort(key=lambda x: x['duration'], reverse=True)
        self.current_session.bottlenecks = bottlenecks[:10]  # Top 10 bottlenecks
    
    def get_call_graph(self, session_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Get call graph for a session"""
        session = None
        if session_id:
            session = next((s for s in self.sessions if s.session_id == session_id), None)
        else:
            session = self.current_session or (self.sessions[-1] if self.sessions else None)
        
        if not session:
            return None
        
        return session.to_dict()
    
    def get_function_statistics(self) -> Dict[str, Dict[str, Any]]:
        """Get comprehensive function statistics"""
        return dict(self.function_stats)
    
    def get_bottlenecks(self, session_id: Optional[str] = None, limit: int = 10) -> List[Dict[str, Any]]:
        """Get performance bottlenecks"""
        session = None
        if session_id:
            session = next((s for s in self.sessions if s.session_id == session_id), None)
        else:
            session = self.current_session or (self.sessions[-1] if self.sessions else None)
        
        if not session:
            return []
        
        return session.bottlenecks[:limit]
    
    def get_profiler_overhead(self) -> float:
        """Get profiler overhead percentage"""
        if self.profiler_calls == 0:
            return 0.0
        
        avg_overhead = self.profiler_overhead / self.profiler_calls
        total_session_time = sum(s.total_duration for s in self.sessions if s.total_duration)
        
        if total_session_time == 0:
            return 0.0
        
        return (self.profiler_overhead / total_session_time) * 100
    
    def export_session(self, session_id: str, filepath: Path) -> bool:
        """Export session data to file"""
        session = next((s for s in self.sessions if s.session_id == session_id), None)
        if not session:
            return False
        
        try:
            with open(filepath, 'w') as f:
                json.dump(session.to_dict(), f, indent=2)
            return True
        except Exception as e:
            logger.error(f"Failed to export session {session_id}: {e}")
            return False
    
    def clear_sessions(self):
        """Clear all stored sessions"""
        self.sessions.clear()
        self.function_stats.clear()
        self.profiler_overhead = 0.0
        self.profiler_calls = 0
        logger.info("Cleared all profiling sessions")


# Global profiler instance
_global_profiler: Optional[ExecutionProfiler] = None


def get_profiler(enable_memory_tracking: bool = True) -> ExecutionProfiler:
    """Get or create global execution profiler"""
    global _global_profiler
    
    if _global_profiler is None:
        _global_profiler = ExecutionProfiler(enable_memory_tracking=enable_memory_tracking)
    
    return _global_profiler


def profile(func: Optional[Callable] = None, *, name: Optional[str] = None, 
           track_memory: bool = True):
    """Convenience decorator for function profiling"""
    profiler = get_profiler()
    return profiler.profile_function(func, name=name, track_memory=track_memory)


def start_profiling(session_id: Optional[str] = None) -> str:
    """Start global profiling session"""
    profiler = get_profiler()
    return profiler.start_profiling(session_id)


def stop_profiling() -> Optional[ProfileSession]:
    """Stop global profiling session"""
    profiler = get_profiler()
    return profiler.stop_profiling()


def get_call_graph(session_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """Get call graph from global profiler"""
    profiler = get_profiler()
    return profiler.get_call_graph(session_id)


def get_bottlenecks(session_id: Optional[str] = None, limit: int = 10) -> List[Dict[str, Any]]:
    """Get bottlenecks from global profiler"""
    profiler = get_profiler()
    return profiler.get_bottlenecks(session_id, limit)
