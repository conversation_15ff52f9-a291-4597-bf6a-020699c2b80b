"""
Alerting Engine
===============

This module provides a comprehensive alerting system with rules engine,
notification channels, and escalation policies for the monitoring platform.

File: vibe_check/monitoring/alerting/alerting_engine.py
Purpose: Comprehensive alerting system with rules and notifications
Related Files: promql_engine.py, time_series_engine.py
Dependencies: asyncio, json, time
"""

import asyncio
import json
import logging
import time
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Callable, Union
from enum import Enum
import re

logger = logging.getLogger(__name__)


class AlertSeverity(Enum):
    """Alert severity levels"""
    CRITICAL = "critical"
    WARNING = "warning"
    INFO = "info"


class AlertState(Enum):
    """Alert states"""
    PENDING = "pending"
    FIRING = "firing"
    RESOLVED = "resolved"
    SILENCED = "silenced"


class NotificationChannel(Enum):
    """Notification channel types"""
    EMAIL = "email"
    SLACK = "slack"
    WEBHOOK = "webhook"
    SMS = "sms"
    PAGERDUTY = "pagerduty"


@dataclass
class AlertRule:
    """Alert rule definition"""
    name: str
    query: str  # PromQL query
    condition: str  # e.g., "> 0.8", "< 100", "== 0"
    severity: AlertSeverity
    description: str = ""
    labels: Dict[str, str] = field(default_factory=dict)
    annotations: Dict[str, str] = field(default_factory=dict)
    
    # Timing configuration
    evaluation_interval: float = 60.0  # seconds
    for_duration: float = 300.0  # seconds (5 minutes)
    
    # State
    enabled: bool = True
    created_at: float = field(default_factory=time.time)
    last_evaluated: float = 0.0


@dataclass
class Alert:
    """Active alert instance"""
    rule_name: str
    state: AlertState
    severity: AlertSeverity
    value: float
    query: str
    condition: str
    description: str
    labels: Dict[str, str] = field(default_factory=dict)
    annotations: Dict[str, str] = field(default_factory=dict)
    
    # Timing
    started_at: float = field(default_factory=time.time)
    resolved_at: Optional[float] = None
    last_sent: float = 0.0
    
    # Metadata
    fingerprint: str = ""
    notification_count: int = 0


@dataclass
class NotificationConfig:
    """Notification channel configuration"""
    channel: NotificationChannel
    config: Dict[str, Any] = field(default_factory=dict)
    enabled: bool = True
    
    # Filtering
    severity_filter: List[AlertSeverity] = field(default_factory=list)
    label_filters: Dict[str, str] = field(default_factory=dict)
    
    # Rate limiting
    rate_limit_interval: float = 300.0  # 5 minutes
    max_notifications_per_interval: int = 10


@dataclass
class EscalationPolicy:
    """Alert escalation policy"""
    name: str
    steps: List[Dict[str, Any]] = field(default_factory=list)
    enabled: bool = True
    
    # Example steps:
    # [
    #   {"delay": 0, "channels": ["email"], "severity": ["warning", "critical"]},
    #   {"delay": 300, "channels": ["slack"], "severity": ["critical"]},
    #   {"delay": 900, "channels": ["pagerduty"], "severity": ["critical"]}
    # ]


class AlertingEngine:
    """Comprehensive alerting engine"""
    
    def __init__(self, promql_engine=None):
        self.promql_engine = promql_engine
        
        # Alert management
        self.alert_rules: Dict[str, AlertRule] = {}
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: List[Alert] = []
        
        # Notification system
        self.notification_configs: Dict[str, NotificationConfig] = {}
        self.escalation_policies: Dict[str, EscalationPolicy] = {}
        
        # State
        self.running = False
        self.evaluation_task: Optional[asyncio.Task] = None
        self.notification_task: Optional[asyncio.Task] = None
        
        # Metrics
        self.evaluation_count = 0
        self.alert_count = 0
        self.notification_count = 0
        
        logger.info("AlertingEngine initialized")
    
    async def start(self):
        """Start the alerting engine"""
        if self.running:
            logger.warning("Alerting engine already running")
            return
        
        self.running = True
        
        # Start background tasks
        self.evaluation_task = asyncio.create_task(self._evaluation_loop())
        self.notification_task = asyncio.create_task(self._notification_loop())
        
        logger.info("Alerting engine started")
    
    async def stop(self):
        """Stop the alerting engine"""
        self.running = False
        
        if self.evaluation_task:
            self.evaluation_task.cancel()
            try:
                await self.evaluation_task
            except asyncio.CancelledError:
                pass
        
        if self.notification_task:
            self.notification_task.cancel()
            try:
                await self.notification_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Alerting engine stopped")
    
    def add_alert_rule(self, rule: AlertRule):
        """Add an alert rule"""
        self.alert_rules[rule.name] = rule
        logger.info(f"Added alert rule: {rule.name}")
    
    def remove_alert_rule(self, rule_name: str) -> bool:
        """Remove an alert rule"""
        if rule_name in self.alert_rules:
            del self.alert_rules[rule_name]
            logger.info(f"Removed alert rule: {rule_name}")
            return True
        return False
    
    def add_notification_config(self, name: str, config: NotificationConfig):
        """Add notification configuration"""
        self.notification_configs[name] = config
        logger.info(f"Added notification config: {name}")
    
    def add_escalation_policy(self, policy: EscalationPolicy):
        """Add escalation policy"""
        self.escalation_policies[policy.name] = policy
        logger.info(f"Added escalation policy: {policy.name}")
    
    async def _evaluation_loop(self):
        """Main evaluation loop"""
        while self.running:
            try:
                await self._evaluate_rules()
                await asyncio.sleep(10.0)  # Evaluate every 10 seconds
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in evaluation loop: {e}")
                await asyncio.sleep(5.0)
    
    async def _evaluate_rules(self):
        """Evaluate all alert rules"""
        current_time = time.time()
        logger.debug(f"Evaluating {len(self.alert_rules)} rules at {current_time}")

        for rule_name, rule in self.alert_rules.items():
            logger.debug(f"Checking rule {rule_name}: enabled={rule.enabled}")
            if not rule.enabled:
                continue

            # Check if it's time to evaluate this rule
            time_since_last = current_time - rule.last_evaluated
            logger.debug(f"Rule {rule_name}: time since last evaluation: {time_since_last}s, interval: {rule.evaluation_interval}s")
            if time_since_last < rule.evaluation_interval:
                continue

            try:
                logger.info(f"Evaluating rule {rule_name}")
                await self._evaluate_rule(rule, current_time)
                rule.last_evaluated = current_time
                self.evaluation_count += 1
                logger.debug(f"Rule {rule_name} evaluated successfully")
            except Exception as e:
                logger.error(f"Error evaluating rule {rule_name}: {e}")
                import traceback
                traceback.print_exc()
    
    async def _evaluate_rule(self, rule: AlertRule, current_time: float):
        """Evaluate a single alert rule"""
        if not self.promql_engine:
            logger.warning("No PromQL engine available for rule evaluation")
            return
        
        try:
            # Execute the PromQL query
            results = await self.promql_engine.execute_query(rule.query)
            logger.debug(f"Rule {rule.name}: Got {len(results)} results")

            # Check each result against the condition
            for i, result in enumerate(results):
                value = self._extract_value(result)
                logger.debug(f"Rule {rule.name}: Result {i} value: {value}")

                if value is None:
                    logger.debug(f"Rule {rule.name}: Skipping result {i} - no value extracted")
                    continue

                # Evaluate condition
                condition_met = self._evaluate_condition(value, rule.condition)
                logger.debug(f"Rule {rule.name}: Condition '{rule.condition}' with value {value}: {condition_met}")

                # Generate alert fingerprint
                fingerprint = self._generate_fingerprint(rule, result)

                if condition_met:
                    logger.info(f"Rule {rule.name}: Condition met, handling firing alert")
                    await self._handle_firing_alert(rule, result, value, fingerprint, current_time)
                else:
                    await self._handle_resolved_alert(rule, fingerprint, current_time)
        
        except Exception as e:
            logger.error(f"Error executing query for rule {rule.name}: {e}")
    
    def _extract_value(self, result) -> Optional[float]:
        """Extract numeric value from query result"""
        try:
            # Handle QueryResult objects from PromQL engine
            if hasattr(result, 'values') and result.values:
                # QueryResult.values is a list of (timestamp, value) tuples
                return float(result.values[-1][1])  # Get the latest value
            elif hasattr(result, 'value'):
                return float(result.value)
            elif hasattr(result, 'samples') and result.samples:
                return float(result.samples[-1].value)
            elif isinstance(result, dict):
                if 'value' in result:
                    return float(result['value'])
                elif 'values' in result and result['values']:
                    # Handle range query format
                    return float(result['values'][-1][1])  # [timestamp, value]

            # Debug logging to understand the format
            logger.debug(f"Unknown result format: {type(result)} - {result}")
            return None
        except (ValueError, TypeError, AttributeError, IndexError) as e:
            logger.debug(f"Error extracting value: {e}")
            return None
    
    def _evaluate_condition(self, value: float, condition: str) -> bool:
        """Evaluate alert condition"""
        try:
            # Parse condition (e.g., "> 0.8", "< 100", "== 0")
            condition = condition.strip()
            
            if condition.startswith('>='):
                threshold = float(condition[2:].strip())
                return value >= threshold
            elif condition.startswith('<='):
                threshold = float(condition[2:].strip())
                return value <= threshold
            elif condition.startswith('>'):
                threshold = float(condition[1:].strip())
                return value > threshold
            elif condition.startswith('<'):
                threshold = float(condition[1:].strip())
                return value < threshold
            elif condition.startswith('=='):
                threshold = float(condition[2:].strip())
                return abs(value - threshold) < 1e-9
            elif condition.startswith('!='):
                threshold = float(condition[2:].strip())
                return abs(value - threshold) >= 1e-9
            else:
                logger.warning(f"Unknown condition format: {condition}")
                return False
        
        except (ValueError, IndexError):
            logger.error(f"Invalid condition: {condition}")
            return False
    
    def _generate_fingerprint(self, rule: AlertRule, result) -> str:
        """Generate unique fingerprint for alert"""
        # Combine rule name with result labels
        if hasattr(result, 'labels'):
            labels = result.labels
        else:
            labels = getattr(result, 'labels', {})

        label_str = ','.join(f"{k}={v}" for k, v in sorted(labels.items()))
        return f"{rule.name}:{label_str}"
    
    async def _handle_firing_alert(self, rule: AlertRule, result, value: float, 
                                 fingerprint: str, current_time: float):
        """Handle a firing alert"""
        if fingerprint in self.active_alerts:
            # Update existing alert
            alert = self.active_alerts[fingerprint]
            alert.value = value
            alert.state = AlertState.FIRING
        else:
            # Create new alert
            alert = Alert(
                rule_name=rule.name,
                state=AlertState.PENDING,
                severity=rule.severity,
                value=value,
                query=rule.query,
                condition=rule.condition,
                description=rule.description,
                labels=dict(rule.labels),
                annotations=dict(rule.annotations),
                fingerprint=fingerprint,
                started_at=current_time
            )
            
            # Add result labels
            if hasattr(result, 'labels'):
                alert.labels.update(result.labels)
            
            self.active_alerts[fingerprint] = alert
            self.alert_count += 1
        
        # Check if alert should transition to firing
        alert = self.active_alerts[fingerprint]
        if (alert.state == AlertState.PENDING and 
            current_time - alert.started_at >= rule.for_duration):
            alert.state = AlertState.FIRING
            logger.info(f"Alert firing: {rule.name} (value: {value})")
    
    async def _handle_resolved_alert(self, rule: AlertRule, fingerprint: str, current_time: float):
        """Handle a resolved alert"""
        if fingerprint in self.active_alerts:
            alert = self.active_alerts[fingerprint]
            if alert.state in [AlertState.PENDING, AlertState.FIRING]:
                alert.state = AlertState.RESOLVED
                alert.resolved_at = current_time
                
                # Move to history
                self.alert_history.append(alert)
                del self.active_alerts[fingerprint]
                
                logger.info(f"Alert resolved: {rule.name}")
    
    async def _notification_loop(self):
        """Notification processing loop"""
        while self.running:
            try:
                await self._process_notifications()
                await asyncio.sleep(30.0)  # Check every 30 seconds
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in notification loop: {e}")
                await asyncio.sleep(10.0)
    
    async def _process_notifications(self):
        """Process pending notifications"""
        current_time = time.time()
        
        for alert in self.active_alerts.values():
            if alert.state == AlertState.FIRING:
                # Check if notification should be sent
                if (current_time - alert.last_sent > 300.0 or  # 5 minutes
                    alert.notification_count == 0):
                    await self._send_notifications(alert, current_time)
    
    async def _send_notifications(self, alert: Alert, current_time: float):
        """Send notifications for an alert"""
        for config_name, config in self.notification_configs.items():
            if not config.enabled:
                continue
            
            # Check severity filter
            if (config.severity_filter and 
                alert.severity not in config.severity_filter):
                continue
            
            # Check label filters
            if not self._matches_label_filters(alert.labels, config.label_filters):
                continue
            
            # Check rate limiting
            if not self._check_rate_limit(config, current_time):
                continue
            
            try:
                await self._send_notification(alert, config)
                alert.last_sent = current_time
                alert.notification_count += 1
                self.notification_count += 1
                
                logger.info(f"Sent notification for alert {alert.rule_name} via {config.channel.value}")
            
            except Exception as e:
                logger.error(f"Failed to send notification via {config.channel.value}: {e}")
    
    def _matches_label_filters(self, alert_labels: Dict[str, str], 
                             filters: Dict[str, str]) -> bool:
        """Check if alert labels match filters"""
        for key, pattern in filters.items():
            if key not in alert_labels:
                return False
            if not re.match(pattern, alert_labels[key]):
                return False
        return True
    
    def _check_rate_limit(self, config: NotificationConfig, current_time: float) -> bool:
        """Check notification rate limiting"""
        # Simplified rate limiting - in production, use more sophisticated tracking
        return True
    
    async def _send_notification(self, alert: Alert, config: NotificationConfig):
        """Send notification via specific channel"""
        if config.channel == NotificationChannel.EMAIL:
            await self._send_email_notification(alert, config)
        elif config.channel == NotificationChannel.SLACK:
            await self._send_slack_notification(alert, config)
        elif config.channel == NotificationChannel.WEBHOOK:
            await self._send_webhook_notification(alert, config)
        else:
            logger.warning(f"Unsupported notification channel: {config.channel}")
    
    async def _send_email_notification(self, alert: Alert, config: NotificationConfig):
        """Send email notification (placeholder)"""
        logger.info(f"EMAIL: {alert.severity.value.upper()} - {alert.description}")
    
    async def _send_slack_notification(self, alert: Alert, config: NotificationConfig):
        """Send Slack notification (placeholder)"""
        logger.info(f"SLACK: {alert.severity.value.upper()} - {alert.description}")
    
    async def _send_webhook_notification(self, alert: Alert, config: NotificationConfig):
        """Send webhook notification (placeholder)"""
        logger.info(f"WEBHOOK: {alert.severity.value.upper()} - {alert.description}")
    
    def get_active_alerts(self) -> List[Alert]:
        """Get all active alerts"""
        return list(self.active_alerts.values())
    
    def get_alert_history(self, limit: int = 100) -> List[Alert]:
        """Get alert history"""
        return self.alert_history[-limit:]
    
    def get_stats(self) -> Dict[str, Any]:
        """Get alerting engine statistics"""
        return {
            'running': self.running,
            'alert_rules': len(self.alert_rules),
            'active_alerts': len(self.active_alerts),
            'notification_configs': len(self.notification_configs),
            'escalation_policies': len(self.escalation_policies),
            'evaluation_count': self.evaluation_count,
            'alert_count': self.alert_count,
            'notification_count': self.notification_count,
            'alert_history_size': len(self.alert_history)
        }
