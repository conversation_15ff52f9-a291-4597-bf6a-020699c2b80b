"""
Alerting Module
===============

This module provides comprehensive alerting capabilities for the
Vibe Check monitoring platform.

Components:
- AlertingEngine: Main alerting engine with rules and notifications
- AlertRule: Alert rule definition
- Alert: Active alert instance
- NotificationConfig: Notification channel configuration
- EscalationPolicy: Alert escalation policy
- AlertSeverity: Alert severity levels
- AlertState: Alert states
- NotificationChannel: Notification channel types
"""

from .alerting_engine import (
    AlertingEngine,
    AlertRule,
    Alert,
    NotificationConfig,
    EscalationPolicy,
    AlertSeverity,
    AlertState,
    NotificationChannel
)

__all__ = [
    'AlertingEngine',
    'AlertRule',
    'Alert',
    'NotificationConfig',
    'EscalationPolicy',
    'AlertSeverity',
    'AlertState',
    'NotificationChannel'
]
