"""
Service Discovery Module
========================

This module provides service discovery and health monitoring capabilities
for the Vibe Check monitoring platform.

Components:
- ServiceDiscovery: Main service discovery engine
- ServiceEndpoint: Service endpoint representation
- HealthCheck: Health check configuration
- ServiceInstance: Service instance with health status
"""

from .service_discovery import (
    ServiceDiscovery,
    ServiceEndpoint,
    HealthCheck,
    ServiceInstance,
    ServiceStatus,
    DiscoveryMethod
)

__all__ = [
    'ServiceDiscovery',
    'ServiceEndpoint', 
    'HealthCheck',
    'ServiceInstance',
    'ServiceStatus',
    'DiscoveryMethod'
]
