"""
Service Discovery System
========================

This module provides automatic service discovery capabilities with health checks
and dynamic configuration management for monitoring infrastructure.

File: vibe_check/monitoring/discovery/service_discovery.py
Purpose: Automatic service discovery and health monitoring
Related Files: network_collector.py, metrics_manager.py
Dependencies: asyncio, aiohttp, socket
"""

import asyncio
import logging
import socket
import time
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Set, Callable
from enum import Enum
import json

try:
    import aiohttp
    AIOHTTP_AVAILABLE = True
except ImportError:
    AIOHTTP_AVAILABLE = False
    aiohttp = None

logger = logging.getLogger(__name__)


class ServiceStatus(Enum):
    """Service health status"""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"
    DEGRADED = "degraded"


class DiscoveryMethod(Enum):
    """Service discovery methods"""
    STATIC = "static"
    DNS = "dns"
    HTTP = "http"
    TCP = "tcp"
    CONSUL = "consul"
    ETCD = "etcd"


@dataclass
class ServiceEndpoint:
    """Service endpoint information"""
    name: str
    host: str
    port: int
    protocol: str = "http"
    path: str = "/"
    tags: Set[str] = field(default_factory=set)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if isinstance(self.tags, list):
            self.tags = set(self.tags)
    
    @property
    def url(self) -> str:
        """Get full URL for the service"""
        return f"{self.protocol}://{self.host}:{self.port}{self.path}"
    
    @property
    def address(self) -> str:
        """Get address string"""
        return f"{self.host}:{self.port}"


@dataclass
class HealthCheck:
    """Health check configuration"""
    method: str = "GET"
    path: str = "/health"
    interval: float = 30.0  # seconds
    timeout: float = 5.0    # seconds
    retries: int = 3
    expected_status: int = 200
    expected_body: Optional[str] = None
    headers: Dict[str, str] = field(default_factory=dict)


@dataclass
class ServiceInstance:
    """Service instance with health status"""
    endpoint: ServiceEndpoint
    status: ServiceStatus = ServiceStatus.UNKNOWN
    last_check: Optional[float] = None
    consecutive_failures: int = 0
    response_time: Optional[float] = None
    error_message: Optional[str] = None
    health_check: Optional[HealthCheck] = None
    
    def __post_init__(self):
        if self.health_check is None:
            self.health_check = HealthCheck()


class ServiceDiscovery:
    """Service discovery and health monitoring system"""
    
    def __init__(self, check_interval: float = 30.0):
        self.check_interval = check_interval
        self.services: Dict[str, ServiceInstance] = {}
        self.discovery_methods: Dict[DiscoveryMethod, Callable] = {}
        self.health_check_task: Optional[asyncio.Task] = None
        self.running = False
        
        # Event callbacks
        self.on_service_up: Optional[Callable[[ServiceInstance], None]] = None
        self.on_service_down: Optional[Callable[[ServiceInstance], None]] = None
        self.on_service_discovered: Optional[Callable[[ServiceInstance], None]] = None
        
        # Initialize discovery methods
        self._setup_discovery_methods()
        
        logger.info("ServiceDiscovery initialized")
    
    def _setup_discovery_methods(self):
        """Setup available discovery methods"""
        self.discovery_methods = {
            DiscoveryMethod.STATIC: self._discover_static,
            DiscoveryMethod.DNS: self._discover_dns,
            DiscoveryMethod.HTTP: self._discover_http,
            DiscoveryMethod.TCP: self._discover_tcp
        }
    
    async def start(self):
        """Start service discovery and health checking"""
        if self.running:
            logger.warning("Service discovery already running")
            return
        
        self.running = True
        self.health_check_task = asyncio.create_task(self._health_check_loop())
        logger.info("Service discovery started")
    
    async def stop(self):
        """Stop service discovery"""
        self.running = False
        
        if self.health_check_task:
            self.health_check_task.cancel()
            try:
                await self.health_check_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Service discovery stopped")
    
    def register_service(self, endpoint: ServiceEndpoint, 
                        health_check: Optional[HealthCheck] = None) -> str:
        """Register a service for monitoring"""
        service_id = f"{endpoint.name}_{endpoint.address}"
        
        instance = ServiceInstance(
            endpoint=endpoint,
            health_check=health_check or HealthCheck()
        )
        
        self.services[service_id] = instance
        
        if self.on_service_discovered:
            self.on_service_discovered(instance)
        
        logger.info(f"Registered service: {service_id} at {endpoint.url}")
        return service_id
    
    def unregister_service(self, service_id: str) -> bool:
        """Unregister a service"""
        if service_id in self.services:
            del self.services[service_id]
            logger.info(f"Unregistered service: {service_id}")
            return True
        return False
    
    async def discover_services(self, method: DiscoveryMethod, 
                              config: Dict[str, Any]) -> List[ServiceEndpoint]:
        """Discover services using specified method"""
        if method not in self.discovery_methods:
            raise ValueError(f"Unsupported discovery method: {method}")
        
        try:
            return await self.discovery_methods[method](config)
        except Exception as e:
            logger.error(f"Error discovering services with {method}: {e}")
            return []
    
    async def _discover_static(self, config: Dict[str, Any]) -> List[ServiceEndpoint]:
        """Discover services from static configuration"""
        endpoints = []
        
        for service_config in config.get('services', []):
            endpoint = ServiceEndpoint(
                name=service_config['name'],
                host=service_config['host'],
                port=service_config['port'],
                protocol=service_config.get('protocol', 'http'),
                path=service_config.get('path', '/'),
                tags=set(service_config.get('tags', [])),
                metadata=service_config.get('metadata', {})
            )
            endpoints.append(endpoint)
        
        return endpoints
    
    async def _discover_dns(self, config: Dict[str, Any]) -> List[ServiceEndpoint]:
        """Discover services via DNS SRV records"""
        endpoints = []
        
        # This is a simplified DNS discovery
        # In production, you'd use proper DNS SRV record lookup
        domain = config.get('domain', 'localhost')
        service_name = config.get('service', 'http')
        
        try:
            # Basic hostname resolution
            host_info = socket.gethostbyname(domain)
            endpoint = ServiceEndpoint(
                name=service_name,
                host=host_info,
                port=config.get('port', 80),
                protocol=config.get('protocol', 'http')
            )
            endpoints.append(endpoint)
        except socket.gaierror as e:
            logger.warning(f"DNS resolution failed for {domain}: {e}")
        
        return endpoints
    
    async def _discover_http(self, config: Dict[str, Any]) -> List[ServiceEndpoint]:
        """Discover services via HTTP API"""
        if not AIOHTTP_AVAILABLE:
            logger.warning("aiohttp not available, skipping HTTP discovery")
            return []
        
        endpoints = []
        discovery_url = config.get('url')
        
        if not discovery_url:
            return endpoints
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(discovery_url, timeout=5.0) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        # Parse service list (format depends on your API)
                        for service_data in data.get('services', []):
                            endpoint = ServiceEndpoint(
                                name=service_data['name'],
                                host=service_data['host'],
                                port=service_data['port'],
                                protocol=service_data.get('protocol', 'http'),
                                tags=set(service_data.get('tags', [])),
                                metadata=service_data.get('metadata', {})
                            )
                            endpoints.append(endpoint)
        
        except Exception as e:
            logger.error(f"HTTP discovery failed: {e}")
        
        return endpoints
    
    async def _discover_tcp(self, config: Dict[str, Any]) -> List[ServiceEndpoint]:
        """Discover services via TCP port scanning"""
        endpoints = []
        
        host = config.get('host', 'localhost')
        ports = config.get('ports', [80, 443, 8080])
        
        for port in ports:
            if await self._check_tcp_port(host, port):
                endpoint = ServiceEndpoint(
                    name=f"tcp_service_{port}",
                    host=host,
                    port=port,
                    protocol="tcp"
                )
                endpoints.append(endpoint)
        
        return endpoints
    
    async def _check_tcp_port(self, host: str, port: int, timeout: float = 1.0) -> bool:
        """Check if TCP port is open"""
        try:
            _, writer = await asyncio.wait_for(
                asyncio.open_connection(host, port),
                timeout=timeout
            )
            writer.close()
            await writer.wait_closed()
            return True
        except (asyncio.TimeoutError, ConnectionRefusedError, OSError):
            return False
    
    async def _health_check_loop(self):
        """Main health check loop"""
        while self.running:
            try:
                await self._perform_health_checks()
                await asyncio.sleep(self.check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health check loop: {e}")
                await asyncio.sleep(1.0)  # Brief pause on error
    
    async def _perform_health_checks(self):
        """Perform health checks on all registered services"""
        if not self.services:
            return
        
        # Run health checks concurrently
        tasks = []
        for service_id, instance in self.services.items():
            task = asyncio.create_task(
                self._check_service_health(service_id, instance)
            )
            tasks.append(task)
        
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _check_service_health(self, service_id: str, instance: ServiceInstance):
        """Check health of a single service"""
        if not instance.health_check:
            return
        
        start_time = time.time()
        previous_status = instance.status
        
        try:
            is_healthy = await self._perform_health_check(instance)
            response_time = time.time() - start_time
            
            if is_healthy:
                instance.status = ServiceStatus.HEALTHY
                instance.consecutive_failures = 0
                instance.response_time = response_time
                instance.error_message = None
                
                # Trigger service up event if it was down
                if previous_status != ServiceStatus.HEALTHY and self.on_service_up:
                    self.on_service_up(instance)
            else:
                instance.consecutive_failures += 1
                instance.status = ServiceStatus.UNHEALTHY
                
                # Trigger service down event if it was up
                if previous_status == ServiceStatus.HEALTHY and self.on_service_down:
                    self.on_service_down(instance)
        
        except Exception as e:
            instance.status = ServiceStatus.UNHEALTHY
            instance.consecutive_failures += 1
            instance.error_message = str(e)
            
            if previous_status == ServiceStatus.HEALTHY and self.on_service_down:
                self.on_service_down(instance)
        
        instance.last_check = time.time()
    
    async def _perform_health_check(self, instance: ServiceInstance) -> bool:
        """Perform actual health check"""
        endpoint = instance.endpoint
        health_check = instance.health_check
        
        if endpoint.protocol in ['http', 'https']:
            return await self._http_health_check(endpoint, health_check)
        elif endpoint.protocol == 'tcp':
            return await self._tcp_health_check(endpoint, health_check)
        else:
            logger.warning(f"Unsupported protocol for health check: {endpoint.protocol}")
            return False
    
    async def _http_health_check(self, endpoint: ServiceEndpoint, 
                                health_check: HealthCheck) -> bool:
        """Perform HTTP health check"""
        if not AIOHTTP_AVAILABLE:
            logger.warning("aiohttp not available, skipping HTTP health check")
            return False
        
        url = f"{endpoint.protocol}://{endpoint.host}:{endpoint.port}{health_check.path}"
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.request(
                    health_check.method,
                    url,
                    headers=health_check.headers,
                    timeout=aiohttp.ClientTimeout(total=health_check.timeout)
                ) as response:
                    
                    # Check status code
                    if response.status != health_check.expected_status:
                        return False
                    
                    # Check response body if specified
                    if health_check.expected_body:
                        body = await response.text()
                        if health_check.expected_body not in body:
                            return False
                    
                    return True
        
        except Exception:
            return False
    
    async def _tcp_health_check(self, endpoint: ServiceEndpoint, 
                               health_check: HealthCheck) -> bool:
        """Perform TCP health check"""
        return await self._check_tcp_port(
            endpoint.host, 
            endpoint.port, 
            health_check.timeout
        )
    
    def get_healthy_services(self) -> List[ServiceInstance]:
        """Get list of healthy services"""
        return [
            instance for instance in self.services.values()
            if instance.status == ServiceStatus.HEALTHY
        ]
    
    def get_service_summary(self) -> Dict[str, Any]:
        """Get comprehensive service discovery summary"""
        status_counts = {}
        for instance in self.services.values():
            status = instance.status.value
            status_counts[status] = status_counts.get(status, 0) + 1
        
        return {
            "total_services": len(self.services),
            "status_counts": status_counts,
            "discovery_methods": list(self.discovery_methods.keys()),
            "aiohttp_available": AIOHTTP_AVAILABLE,
            "running": self.running
        }
