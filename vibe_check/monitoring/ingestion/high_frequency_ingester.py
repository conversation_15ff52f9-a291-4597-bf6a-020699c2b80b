"""
High-Frequency Data Ingestion System
====================================

This module provides high-performance data ingestion capabilities for handling
10,000+ metrics per second with efficient batching and backpressure handling.

File: vibe_check/monitoring/ingestion/high_frequency_ingester.py
Purpose: High-performance metrics ingestion with batching and backpressure
Related Files: time_series_engine.py, metrics_manager.py
Dependencies: asyncio, time, collections
"""

import asyncio
import logging
import time
from collections import deque
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Callable, Tuple
from enum import Enum
import statistics

try:
    # Try relative imports first
    from ..storage.time_series_engine import TimeSeriesStorageEngine
    from ..collectors.base_collector import MetricValue
except ImportError:
    # Fallback to absolute imports for testing
    import sys
    from pathlib import Path
    
    # Add the project root to Python path
    project_root = Path(__file__).parent.parent.parent.parent
    sys.path.insert(0, str(project_root))
    
    from vibe_check.monitoring.storage.time_series_engine import TimeSeriesStorageEngine
    from vibe_check.monitoring.collectors.base_collector import MetricValue

logger = logging.getLogger(__name__)


class BackpressureStrategy(Enum):
    """Backpressure handling strategies"""
    DROP_OLDEST = "drop_oldest"
    DROP_NEWEST = "drop_newest"
    BLOCK = "block"
    SAMPLE = "sample"


@dataclass
class IngestionConfig:
    """Configuration for high-frequency ingestion"""
    max_batch_size: int = 1000
    max_batch_wait_ms: float = 100.0  # milliseconds
    max_queue_size: int = 50000
    backpressure_strategy: BackpressureStrategy = BackpressureStrategy.DROP_OLDEST
    compression_enabled: bool = True
    parallel_workers: int = 4
    metrics_per_second_target: int = 10000
    
    # Performance monitoring
    enable_performance_tracking: bool = True
    performance_window_size: int = 1000


@dataclass
class IngestionStats:
    """Ingestion performance statistics"""
    total_metrics_ingested: int = 0
    total_batches_processed: int = 0
    total_errors: int = 0
    current_queue_size: int = 0
    
    # Performance metrics
    avg_batch_size: float = 0.0
    avg_processing_time_ms: float = 0.0
    current_throughput: float = 0.0  # metrics/second
    peak_throughput: float = 0.0
    
    # Backpressure metrics
    dropped_metrics: int = 0
    backpressure_events: int = 0
    
    # Timing windows
    processing_times: deque = field(default_factory=lambda: deque(maxlen=1000))
    throughput_samples: deque = field(default_factory=lambda: deque(maxlen=100))


class HighFrequencyIngester:
    """High-performance metrics ingestion system"""
    
    def __init__(self, tsdb: TimeSeriesStorageEngine, config: Optional[IngestionConfig] = None):
        self.tsdb = tsdb
        self.config = config or IngestionConfig()
        
        # Ingestion queue and batching
        self.ingestion_queue: asyncio.Queue = asyncio.Queue(maxsize=self.config.max_queue_size)
        self.batch_buffer: List[MetricValue] = []
        self.last_batch_time = time.time()
        
        # Worker management
        self.workers: List[asyncio.Task] = []
        self.batch_processor_task: Optional[asyncio.Task] = None
        self.running = False
        
        # Performance tracking
        self.stats = IngestionStats()
        self.performance_tracker_task: Optional[asyncio.Task] = None
        
        # Event callbacks
        self.on_backpressure: Optional[Callable[[int], None]] = None
        self.on_high_throughput: Optional[Callable[[float], None]] = None
        
        logger.info(f"HighFrequencyIngester initialized with target {self.config.metrics_per_second_target} metrics/sec")
    
    async def start(self):
        """Start the high-frequency ingestion system"""
        if self.running:
            logger.warning("High-frequency ingester already running")
            return
        
        self.running = True
        
        # Start batch processor
        self.batch_processor_task = asyncio.create_task(self._batch_processor())
        
        # Start worker pool
        for i in range(self.config.parallel_workers):
            worker = asyncio.create_task(self._ingestion_worker(f"worker-{i}"))
            self.workers.append(worker)
        
        # Start performance tracker
        if self.config.enable_performance_tracking:
            self.performance_tracker_task = asyncio.create_task(self._performance_tracker())
        
        logger.info(f"High-frequency ingester started with {self.config.parallel_workers} workers")
    
    async def stop(self):
        """Stop the ingestion system"""
        self.running = False
        
        # Process remaining items in queue
        await self._flush_remaining()
        
        # Cancel tasks
        if self.batch_processor_task:
            self.batch_processor_task.cancel()
        
        for worker in self.workers:
            worker.cancel()
        
        if self.performance_tracker_task:
            self.performance_tracker_task.cancel()
        
        # Wait for tasks to complete
        tasks = [self.batch_processor_task] + self.workers
        if self.performance_tracker_task:
            tasks.append(self.performance_tracker_task)
        
        await asyncio.gather(*tasks, return_exceptions=True)
        
        logger.info("High-frequency ingester stopped")
    
    async def ingest_metric(self, metric: MetricValue) -> bool:
        """Ingest a single metric"""
        try:
            self.ingestion_queue.put_nowait(metric)
            return True
        except asyncio.QueueFull:
            return await self._handle_backpressure(metric)
    
    async def ingest_metrics_batch(self, metrics: List[MetricValue]) -> int:
        """Ingest a batch of metrics"""
        ingested_count = 0
        
        for metric in metrics:
            if await self.ingest_metric(metric):
                ingested_count += 1
        
        return ingested_count
    
    async def _handle_backpressure(self, metric: MetricValue) -> bool:
        """Handle backpressure when queue is full"""
        self.stats.backpressure_events += 1
        
        if self.on_backpressure:
            self.on_backpressure(self.ingestion_queue.qsize())
        
        strategy = self.config.backpressure_strategy
        
        if strategy == BackpressureStrategy.DROP_NEWEST:
            self.stats.dropped_metrics += 1
            logger.debug("Dropped newest metric due to backpressure")
            return False
        
        elif strategy == BackpressureStrategy.DROP_OLDEST:
            try:
                # Remove oldest item and add new one
                self.ingestion_queue.get_nowait()
                self.ingestion_queue.put_nowait(metric)
                self.stats.dropped_metrics += 1
                logger.debug("Dropped oldest metric due to backpressure")
                return True
            except asyncio.QueueEmpty:
                return False
        
        elif strategy == BackpressureStrategy.BLOCK:
            # Wait for space in queue
            await self.ingestion_queue.put(metric)
            return True
        
        elif strategy == BackpressureStrategy.SAMPLE:
            # Sample every Nth metric when under pressure
            if self.stats.backpressure_events % 10 == 0:
                try:
                    self.ingestion_queue.put_nowait(metric)
                    return True
                except asyncio.QueueFull:
                    pass
            self.stats.dropped_metrics += 1
            return False
        
        return False
    
    async def _batch_processor(self):
        """Process metrics in batches"""
        while self.running:
            try:
                # Wait for metrics or timeout
                try:
                    metric = await asyncio.wait_for(
                        self.ingestion_queue.get(),
                        timeout=self.config.max_batch_wait_ms / 1000.0
                    )
                    self.batch_buffer.append(metric)
                except asyncio.TimeoutError:
                    # Timeout reached, process current batch if any
                    if self.batch_buffer:
                        await self._process_batch()
                    continue
                
                # Check if batch is ready
                current_time = time.time()
                batch_age_ms = (current_time - self.last_batch_time) * 1000
                
                should_process = (
                    len(self.batch_buffer) >= self.config.max_batch_size or
                    batch_age_ms >= self.config.max_batch_wait_ms
                )
                
                if should_process:
                    await self._process_batch()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in batch processor: {e}")
                await asyncio.sleep(0.1)
    
    async def _process_batch(self):
        """Process a batch of metrics"""
        if not self.batch_buffer:
            return
        
        batch = self.batch_buffer.copy()
        self.batch_buffer.clear()
        self.last_batch_time = time.time()
        
        start_time = time.time()
        
        try:
            # Process batch in parallel chunks if large
            if len(batch) > 500:
                await self._process_large_batch(batch)
            else:
                await self._process_small_batch(batch)
            
            # Update statistics
            processing_time = (time.time() - start_time) * 1000  # ms
            self.stats.total_batches_processed += 1
            self.stats.total_metrics_ingested += len(batch)
            self.stats.avg_batch_size = (
                (self.stats.avg_batch_size * (self.stats.total_batches_processed - 1) + len(batch))
                / self.stats.total_batches_processed
            )
            
            if self.config.enable_performance_tracking:
                self.stats.processing_times.append(processing_time)
                if len(self.stats.processing_times) > 0:
                    self.stats.avg_processing_time_ms = statistics.mean(self.stats.processing_times)
            
        except Exception as e:
            self.stats.total_errors += 1
            logger.error(f"Error processing batch of {len(batch)} metrics: {e}")
    
    async def _process_small_batch(self, batch: List[MetricValue]):
        """Process a small batch sequentially"""
        for metric in batch:
            await self.tsdb.ingest_sample(
                metric_name=metric.name,
                value=metric.value,
                labels=metric.labels,
                timestamp=metric.timestamp
            )
    
    async def _process_large_batch(self, batch: List[MetricValue]):
        """Process a large batch in parallel chunks"""
        chunk_size = len(batch) // self.config.parallel_workers
        if chunk_size == 0:
            chunk_size = 1
        
        chunks = [
            batch[i:i + chunk_size]
            for i in range(0, len(batch), chunk_size)
        ]
        
        tasks = [
            asyncio.create_task(self._process_small_batch(chunk))
            for chunk in chunks
        ]
        
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _ingestion_worker(self, worker_id: str):
        """Worker for processing ingestion queue"""
        logger.debug(f"Ingestion worker {worker_id} started")
        
        while self.running:
            try:
                await asyncio.sleep(0.01)  # Small delay to prevent busy waiting
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in ingestion worker {worker_id}: {e}")
        
        logger.debug(f"Ingestion worker {worker_id} stopped")
    
    async def _performance_tracker(self):
        """Track performance metrics"""
        last_metrics_count = 0
        last_time = time.time()
        
        while self.running:
            try:
                await asyncio.sleep(1.0)  # Update every second
                
                current_time = time.time()
                current_metrics = self.stats.total_metrics_ingested
                
                # Calculate throughput
                time_delta = current_time - last_time
                metrics_delta = current_metrics - last_metrics_count
                
                if time_delta > 0:
                    throughput = metrics_delta / time_delta
                    self.stats.current_throughput = throughput
                    self.stats.throughput_samples.append(throughput)
                    
                    if throughput > self.stats.peak_throughput:
                        self.stats.peak_throughput = throughput
                    
                    # Trigger high throughput callback
                    if (throughput > self.config.metrics_per_second_target * 0.8 
                        and self.on_high_throughput):
                        self.on_high_throughput(throughput)
                
                # Update queue size
                self.stats.current_queue_size = self.ingestion_queue.qsize()
                
                last_metrics_count = current_metrics
                last_time = current_time
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in performance tracker: {e}")
    
    async def _flush_remaining(self):
        """Flush remaining metrics in queue and buffer"""
        # Process remaining buffer
        if self.batch_buffer:
            await self._process_batch()
        
        # Process remaining queue items
        remaining_metrics = []
        while not self.ingestion_queue.empty():
            try:
                metric = self.ingestion_queue.get_nowait()
                remaining_metrics.append(metric)
            except asyncio.QueueEmpty:
                break
        
        if remaining_metrics:
            self.batch_buffer = remaining_metrics
            await self._process_batch()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive ingestion statistics"""
        return {
            "total_metrics_ingested": self.stats.total_metrics_ingested,
            "total_batches_processed": self.stats.total_batches_processed,
            "total_errors": self.stats.total_errors,
            "current_queue_size": self.stats.current_queue_size,
            "avg_batch_size": self.stats.avg_batch_size,
            "avg_processing_time_ms": self.stats.avg_processing_time_ms,
            "current_throughput": self.stats.current_throughput,
            "peak_throughput": self.stats.peak_throughput,
            "dropped_metrics": self.stats.dropped_metrics,
            "backpressure_events": self.stats.backpressure_events,
            "target_throughput": self.config.metrics_per_second_target,
            "parallel_workers": self.config.parallel_workers,
            "running": self.running
        }
    
    async def benchmark_ingestion(self, num_metrics: int = 10000) -> Dict[str, Any]:
        """Benchmark ingestion performance"""
        logger.info(f"Starting ingestion benchmark with {num_metrics} metrics")
        
        # Generate test metrics
        test_metrics = []
        for i in range(num_metrics):
            metric = MetricValue(
                name=f"benchmark_metric_{i % 100}",
                value=float(i),
                labels={"test": "benchmark", "batch": str(i // 1000)},
                timestamp=time.time()
            )
            test_metrics.append(metric)
        
        # Benchmark ingestion
        start_time = time.time()
        initial_stats = self.get_stats()
        
        ingested_count = await self.ingest_metrics_batch(test_metrics)
        
        # Wait for processing to complete
        await asyncio.sleep(2.0)
        
        end_time = time.time()
        final_stats = self.get_stats()
        
        # Calculate results
        total_time = end_time - start_time
        throughput = ingested_count / total_time if total_time > 0 else 0
        
        return {
            "metrics_generated": num_metrics,
            "metrics_ingested": ingested_count,
            "ingestion_rate": ingested_count / num_metrics if num_metrics > 0 else 0,
            "total_time_seconds": total_time,
            "throughput_metrics_per_second": throughput,
            "target_met": throughput >= self.config.metrics_per_second_target,
            "initial_stats": initial_stats,
            "final_stats": final_stats
        }
