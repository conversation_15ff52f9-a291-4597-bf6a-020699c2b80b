"""
High-Frequency Data Ingestion Module
====================================

This module provides high-performance data ingestion capabilities for the
Vibe Check monitoring platform, supporting 10,000+ metrics per second.

Components:
- HighFrequencyIngester: Main high-performance ingestion engine
- IngestionConfig: Configuration for ingestion parameters
- IngestionStats: Performance statistics tracking
- BackpressureStrategy: Backpressure handling strategies
"""

from .high_frequency_ingester import (
    HighFrequencyIngester,
    IngestionConfig,
    IngestionStats,
    BackpressureStrategy
)

__all__ = [
    'HighFrequencyIngester',
    'IngestionConfig',
    'IngestionStats',
    'BackpressureStrategy'
]
