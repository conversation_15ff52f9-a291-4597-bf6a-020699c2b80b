"""
System Resource Monitor
======================

Comprehensive system resource monitoring with CPU, memory, disk, and network tracking.
Cross-platform support for Linux, macOS, and Windows with graceful failure handling.
"""

import asyncio
import time
import platform
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from pathlib import Path
import json

# Import psutil for cross-platform system monitoring
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    psutil = None

logger = logging.getLogger(__name__)


@dataclass
class CPUMetrics:
    """CPU utilization metrics"""
    timestamp: float
    overall_percent: float
    per_core_percent: List[float]
    load_average: Optional[Tuple[float, float, float]] = None  # 1, 5, 15 min averages
    context_switches: int = 0
    interrupts: int = 0
    soft_interrupts: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'timestamp': self.timestamp,
            'overall_percent': self.overall_percent,
            'per_core_percent': self.per_core_percent,
            'load_average': self.load_average,
            'context_switches': self.context_switches,
            'interrupts': self.interrupts,
            'soft_interrupts': self.soft_interrupts
        }


@dataclass
class MemoryMetrics:
    """Memory usage metrics"""
    timestamp: float
    total: int
    available: int
    used: int
    free: int
    percent: float
    swap_total: int = 0
    swap_used: int = 0
    swap_free: int = 0
    swap_percent: float = 0.0
    buffers: int = 0
    cached: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'timestamp': self.timestamp,
            'total': self.total,
            'available': self.available,
            'used': self.used,
            'free': self.free,
            'percent': self.percent,
            'swap_total': self.swap_total,
            'swap_used': self.swap_used,
            'swap_free': self.swap_free,
            'swap_percent': self.swap_percent,
            'buffers': self.buffers,
            'cached': self.cached
        }


@dataclass
class DiskMetrics:
    """Disk I/O and usage metrics"""
    timestamp: float
    usage_by_device: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    io_counters: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'timestamp': self.timestamp,
            'usage_by_device': self.usage_by_device,
            'io_counters': self.io_counters
        }


@dataclass
class NetworkMetrics:
    """Network interface metrics"""
    timestamp: float
    interfaces: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    connections: Dict[str, int] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'timestamp': self.timestamp,
            'interfaces': self.interfaces,
            'connections': self.connections
        }


@dataclass
class SystemSnapshot:
    """Complete system resource snapshot"""
    timestamp: float
    cpu: CPUMetrics
    memory: MemoryMetrics
    disk: DiskMetrics
    network: NetworkMetrics
    system_info: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'timestamp': self.timestamp,
            'cpu': self.cpu.to_dict(),
            'memory': self.memory.to_dict(),
            'disk': self.disk.to_dict(),
            'network': self.network.to_dict(),
            'system_info': self.system_info
        }


class SystemMonitor:
    """Comprehensive system resource monitor with cross-platform support"""
    
    def __init__(self, collection_interval: float = 5.0, 
                 enable_detailed_metrics: bool = True):
        self.collection_interval = collection_interval
        self.enable_detailed_metrics = enable_detailed_metrics
        
        # Monitoring state
        self.is_monitoring = False
        self.start_time: Optional[float] = None
        
        # Metrics storage
        self.snapshots: List[SystemSnapshot] = []
        self.max_snapshots = 1000  # Keep last 1000 snapshots
        
        # Platform detection
        self.platform = platform.system().lower()
        self.platform_version = platform.release()
        
        # Background monitoring
        self._monitoring_task: Optional[asyncio.Task] = None
        
        # Performance tracking
        self.collection_times: List[float] = []
        self.collection_errors: int = 0
        
        # Check psutil availability
        if not PSUTIL_AVAILABLE:
            logger.warning("psutil not available - system monitoring will be limited")
        
        logger.info(f"SystemMonitor initialized for {self.platform}")
    
    async def start_monitoring(self) -> bool:
        """Start system resource monitoring"""
        if self.is_monitoring:
            logger.warning("System monitoring already active")
            return False
        
        if not PSUTIL_AVAILABLE:
            logger.error("Cannot start monitoring - psutil not available")
            return False
        
        try:
            self.is_monitoring = True
            self.start_time = time.time()
            self.snapshots.clear()
            self.collection_times.clear()
            self.collection_errors = 0
            
            # Start background monitoring task
            self._monitoring_task = asyncio.create_task(self._monitoring_loop())
            
            logger.info(f"System monitoring started with {self.collection_interval}s interval")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start system monitoring: {e}")
            self.is_monitoring = False
            return False
    
    async def stop_monitoring(self) -> bool:
        """Stop system resource monitoring"""
        if not self.is_monitoring:
            logger.warning("System monitoring not active")
            return False
        
        try:
            self.is_monitoring = False
            
            # Cancel monitoring task
            if self._monitoring_task and not self._monitoring_task.done():
                self._monitoring_task.cancel()
                try:
                    await self._monitoring_task
                except asyncio.CancelledError:
                    pass
            
            logger.info("System monitoring stopped")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop system monitoring: {e}")
            return False
    
    async def _monitoring_loop(self):
        """Background monitoring loop"""
        while self.is_monitoring:
            try:
                start_time = time.time()
                
                # Collect system snapshot
                snapshot = await self.collect_system_snapshot()
                
                if snapshot:
                    self.snapshots.append(snapshot)
                    
                    # Keep only recent snapshots
                    if len(self.snapshots) > self.max_snapshots:
                        self.snapshots = self.snapshots[-self.max_snapshots:]
                
                # Track collection performance
                collection_time = time.time() - start_time
                self.collection_times.append(collection_time)
                
                # Keep only recent collection times
                if len(self.collection_times) > 100:
                    self.collection_times = self.collection_times[-100:]
                
                # Wait for next collection
                await asyncio.sleep(self.collection_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                self.collection_errors += 1
                await asyncio.sleep(self.collection_interval)
    
    async def collect_system_snapshot(self) -> Optional[SystemSnapshot]:
        """Collect complete system resource snapshot"""
        if not PSUTIL_AVAILABLE:
            return None
        
        try:
            timestamp = time.time()
            
            # Collect all metrics concurrently
            cpu_task = asyncio.create_task(self.collect_cpu_metrics())
            memory_task = asyncio.create_task(self.collect_memory_metrics())
            disk_task = asyncio.create_task(self.collect_disk_metrics())
            network_task = asyncio.create_task(self.collect_network_metrics())
            
            # Wait for all collections to complete
            cpu_metrics, memory_metrics, disk_metrics, network_metrics = await asyncio.gather(
                cpu_task, memory_task, disk_task, network_task,
                return_exceptions=True
            )
            
            # Handle any exceptions
            if isinstance(cpu_metrics, Exception):
                logger.error(f"CPU metrics collection failed: {cpu_metrics}")
                cpu_metrics = CPUMetrics(timestamp=timestamp, overall_percent=0.0, per_core_percent=[])
            
            if isinstance(memory_metrics, Exception):
                logger.error(f"Memory metrics collection failed: {memory_metrics}")
                memory_metrics = MemoryMetrics(timestamp=timestamp, total=0, available=0, used=0, free=0, percent=0.0)
            
            if isinstance(disk_metrics, Exception):
                logger.error(f"Disk metrics collection failed: {disk_metrics}")
                disk_metrics = DiskMetrics(timestamp=timestamp)
            
            if isinstance(network_metrics, Exception):
                logger.error(f"Network metrics collection failed: {network_metrics}")
                network_metrics = NetworkMetrics(timestamp=timestamp)
            
            # Get system info
            system_info = self._get_system_info()
            
            return SystemSnapshot(
                timestamp=timestamp,
                cpu=cpu_metrics,
                memory=memory_metrics,
                disk=disk_metrics,
                network=network_metrics,
                system_info=system_info
            )
            
        except Exception as e:
            logger.error(f"Failed to collect system snapshot: {e}")
            return None
    
    async def collect_cpu_metrics(self) -> CPUMetrics:
        """Collect CPU utilization metrics"""
        timestamp = time.time()
        
        try:
            # Get overall CPU percentage
            overall_percent = psutil.cpu_percent(interval=0.1)
            
            # Get per-core percentages
            per_core_percent = psutil.cpu_percent(interval=0.1, percpu=True)
            
            # Get load average (Unix-like systems only)
            load_average = None
            if hasattr(psutil, 'getloadavg'):
                try:
                    load_average = psutil.getloadavg()
                except (AttributeError, OSError):
                    pass
            
            # Get CPU stats if available
            context_switches = 0
            interrupts = 0
            soft_interrupts = 0
            
            if hasattr(psutil, 'cpu_stats'):
                try:
                    cpu_stats = psutil.cpu_stats()
                    context_switches = cpu_stats.ctx_switches
                    interrupts = cpu_stats.interrupts
                    soft_interrupts = getattr(cpu_stats, 'soft_interrupts', 0)
                except (AttributeError, OSError):
                    pass
            
            return CPUMetrics(
                timestamp=timestamp,
                overall_percent=overall_percent,
                per_core_percent=per_core_percent,
                load_average=load_average,
                context_switches=context_switches,
                interrupts=interrupts,
                soft_interrupts=soft_interrupts
            )
            
        except Exception as e:
            logger.error(f"Failed to collect CPU metrics: {e}")
            return CPUMetrics(timestamp=timestamp, overall_percent=0.0, per_core_percent=[])
    
    async def collect_memory_metrics(self) -> MemoryMetrics:
        """Collect memory usage metrics"""
        timestamp = time.time()
        
        try:
            # Get virtual memory stats
            virtual_memory = psutil.virtual_memory()
            
            # Get swap memory stats
            swap_memory = psutil.swap_memory()
            
            return MemoryMetrics(
                timestamp=timestamp,
                total=virtual_memory.total,
                available=virtual_memory.available,
                used=virtual_memory.used,
                free=virtual_memory.free,
                percent=virtual_memory.percent,
                swap_total=swap_memory.total,
                swap_used=swap_memory.used,
                swap_free=swap_memory.free,
                swap_percent=swap_memory.percent,
                buffers=getattr(virtual_memory, 'buffers', 0),
                cached=getattr(virtual_memory, 'cached', 0)
            )
            
        except Exception as e:
            logger.error(f"Failed to collect memory metrics: {e}")
            return MemoryMetrics(timestamp=timestamp, total=0, available=0, used=0, free=0, percent=0.0)
    
    async def collect_disk_metrics(self) -> DiskMetrics:
        """Collect disk I/O and usage metrics"""
        timestamp = time.time()
        
        try:
            disk_metrics = DiskMetrics(timestamp=timestamp)
            
            # Get disk usage for all mounted filesystems
            disk_partitions = psutil.disk_partitions()
            
            for partition in disk_partitions:
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disk_metrics.usage_by_device[partition.device] = {
                        'mountpoint': partition.mountpoint,
                        'fstype': partition.fstype,
                        'total': usage.total,
                        'used': usage.used,
                        'free': usage.free,
                        'percent': (usage.used / usage.total * 100) if usage.total > 0 else 0.0
                    }
                except (PermissionError, OSError) as e:
                    logger.debug(f"Cannot access disk {partition.device}: {e}")
            
            # Get disk I/O counters
            if hasattr(psutil, 'disk_io_counters'):
                try:
                    io_counters = psutil.disk_io_counters(perdisk=True)
                    if io_counters:
                        for device, counters in io_counters.items():
                            disk_metrics.io_counters[device] = {
                                'read_count': counters.read_count,
                                'write_count': counters.write_count,
                                'read_bytes': counters.read_bytes,
                                'write_bytes': counters.write_bytes,
                                'read_time': counters.read_time,
                                'write_time': counters.write_time
                            }
                except (AttributeError, OSError):
                    pass
            
            return disk_metrics
            
        except Exception as e:
            logger.error(f"Failed to collect disk metrics: {e}")
            return DiskMetrics(timestamp=timestamp)
    
    async def collect_network_metrics(self) -> NetworkMetrics:
        """Collect network interface metrics"""
        timestamp = time.time()
        
        try:
            network_metrics = NetworkMetrics(timestamp=timestamp)
            
            # Get network interface statistics
            if hasattr(psutil, 'net_io_counters'):
                try:
                    net_io = psutil.net_io_counters(pernic=True)
                    if net_io:
                        for interface, counters in net_io.items():
                            network_metrics.interfaces[interface] = {
                                'bytes_sent': counters.bytes_sent,
                                'bytes_recv': counters.bytes_recv,
                                'packets_sent': counters.packets_sent,
                                'packets_recv': counters.packets_recv,
                                'errin': counters.errin,
                                'errout': counters.errout,
                                'dropin': counters.dropin,
                                'dropout': counters.dropout
                            }
                except (AttributeError, OSError):
                    pass
            
            # Get network connections
            if hasattr(psutil, 'net_connections'):
                try:
                    connections = psutil.net_connections()
                    connection_counts = {}
                    
                    for conn in connections:
                        status = conn.status
                        connection_counts[status] = connection_counts.get(status, 0) + 1
                    
                    network_metrics.connections = connection_counts
                    
                except (AttributeError, OSError, psutil.AccessDenied):
                    pass
            
            return network_metrics
            
        except Exception as e:
            logger.error(f"Failed to collect network metrics: {e}")
            return NetworkMetrics(timestamp=timestamp)
    
    def _get_system_info(self) -> Dict[str, Any]:
        """Get system information"""
        try:
            info = {
                'platform': self.platform,
                'platform_version': self.platform_version,
                'architecture': platform.machine(),
                'processor': platform.processor(),
                'hostname': platform.node(),
                'python_version': platform.python_version()
            }
            
            if PSUTIL_AVAILABLE:
                try:
                    info['cpu_count'] = psutil.cpu_count()
                    info['cpu_count_logical'] = psutil.cpu_count(logical=True)
                    info['boot_time'] = psutil.boot_time()
                except (AttributeError, OSError):
                    pass
            
            return info
            
        except Exception as e:
            logger.error(f"Failed to get system info: {e}")
            return {}
    
    def get_latest_snapshot(self) -> Optional[SystemSnapshot]:
        """Get the most recent system snapshot"""
        return self.snapshots[-1] if self.snapshots else None
    
    def get_snapshots(self, limit: int = 10) -> List[SystemSnapshot]:
        """Get recent system snapshots"""
        return self.snapshots[-limit:] if self.snapshots else []
    
    def get_monitoring_stats(self) -> Dict[str, Any]:
        """Get monitoring performance statistics"""
        avg_collection_time = sum(self.collection_times) / len(self.collection_times) if self.collection_times else 0.0
        
        return {
            'is_monitoring': self.is_monitoring,
            'snapshots_collected': len(self.snapshots),
            'collection_errors': self.collection_errors,
            'avg_collection_time': avg_collection_time,
            'collection_interval': self.collection_interval,
            'psutil_available': PSUTIL_AVAILABLE,
            'platform': self.platform,
            'start_time': self.start_time
        }
    
    def export_snapshots(self, filepath: Path, limit: int = 100) -> bool:
        """Export recent snapshots to file"""
        try:
            recent_snapshots = self.get_snapshots(limit)
            data = {
                'monitoring_stats': self.get_monitoring_stats(),
                'snapshots': [snapshot.to_dict() for snapshot in recent_snapshots]
            }
            
            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to export snapshots: {e}")
            return False
    
    def cleanup(self):
        """Cleanup resources"""
        if self.is_monitoring:
            asyncio.create_task(self.stop_monitoring())
        
        logger.info("SystemMonitor cleanup completed")


# Global system monitor instance
_global_system_monitor: Optional[SystemMonitor] = None


def get_system_monitor(collection_interval: float = 5.0) -> SystemMonitor:
    """Get or create global system monitor"""
    global _global_system_monitor
    
    if _global_system_monitor is None:
        _global_system_monitor = SystemMonitor(collection_interval=collection_interval)
    
    return _global_system_monitor


async def start_system_monitoring(collection_interval: float = 5.0) -> bool:
    """Start global system monitoring"""
    monitor = get_system_monitor(collection_interval)
    return await monitor.start_monitoring()


async def stop_system_monitoring() -> bool:
    """Stop global system monitoring"""
    monitor = get_system_monitor()
    return await monitor.stop_monitoring()


def get_latest_system_snapshot() -> Optional[SystemSnapshot]:
    """Get latest system snapshot from global monitor"""
    monitor = get_system_monitor()
    return monitor.get_latest_snapshot()
