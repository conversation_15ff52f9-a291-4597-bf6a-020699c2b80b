"""
Unified Analysis Engine
======================

Consolidated analysis engine that merges redundant analyzers and provides
a single, high-performance analysis pipeline for Vibe Check.
"""

import asyncio
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import multiprocessing

from vibe_check.core.models import FileMetrics, ProjectMetrics, DirectoryMetrics


@dataclass
class AnalysisConfig:
    """Configuration for unified analysis"""
    max_workers: int = multiprocessing.cpu_count()
    use_async: bool = True
    enable_caching: bool = True
    analysis_timeout: float = 300.0  # 5 minutes
    include_tests: bool = False
    include_docs: bool = True
    complexity_threshold: int = 10
    quality_threshold: float = 7.0


@dataclass
class AnalysisResult:
    """Unified analysis result"""
    project_metrics: ProjectMetrics
    file_metrics: List[FileMetrics]
    directory_metrics: List[DirectoryMetrics]
    analysis_time: float
    files_analyzed: int
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'project_metrics': self.project_metrics.to_dict() if hasattr(self.project_metrics, 'to_dict') else str(self.project_metrics),
            'file_metrics': [fm.to_dict() if hasattr(fm, 'to_dict') else str(fm) for fm in self.file_metrics],
            'directory_metrics': [dm.to_dict() if hasattr(dm, 'to_dict') else str(dm) for dm in self.directory_metrics],
            'analysis_time': self.analysis_time,
            'files_analyzed': self.files_analyzed,
            'errors': self.errors,
            'warnings': self.warnings
        }


class UnifiedAnalysisEngine:
    """Main unified analysis engine"""
    
    def __init__(self, config: Optional[AnalysisConfig] = None):
        self.config = config or AnalysisConfig()
        self.cache = {} if self.config.enable_caching else None
        self.executor = None
        
    async def analyze_project(self, path: Union[str, Path]) -> AnalysisResult:
        """Analyze entire project"""
        start_time = time.time()
        project_path = Path(path)
        
        if not project_path.exists():
            raise ValueError(f"Project path does not exist: {path}")
        
        print(f"🔍 Starting unified analysis of: {project_path}")
        
        try:
            # Initialize executor
            if self.config.use_async:
                self.executor = ThreadPoolExecutor(max_workers=self.config.max_workers)
            
            # Discover files
            python_files = self._discover_python_files(project_path)
            print(f"📁 Found {len(python_files)} Python files")
            
            # Analyze files
            if self.config.use_async:
                file_metrics = await self._analyze_files_async(python_files)
            else:
                file_metrics = self._analyze_files_sync(python_files)
            
            # Analyze directories
            directory_metrics = await self._analyze_directories(project_path, file_metrics)
            
            # Calculate project metrics
            project_metrics = self._calculate_project_metrics(file_metrics, directory_metrics)
            
            analysis_time = time.time() - start_time
            
            result = AnalysisResult(
                project_metrics=project_metrics,
                file_metrics=file_metrics,
                directory_metrics=directory_metrics,
                analysis_time=analysis_time,
                files_analyzed=len(file_metrics)
            )
            
            print(f"✅ Analysis completed in {analysis_time:.2f}s")
            print(f"📊 Analyzed {len(file_metrics)} files, {len(directory_metrics)} directories")
            
            return result
            
        finally:
            if self.executor:
                self.executor.shutdown(wait=True)
    
    def _discover_python_files(self, project_path: Path) -> List[Path]:
        """Discover Python files in project"""
        python_files = []
        
        for file_path in project_path.rglob("*.py"):
            # Skip virtual environments and cache directories
            if any(skip in str(file_path) for skip in ['.venv', 'venv', '__pycache__', '.git']):
                continue
            
            # Skip test files if not included
            if not self.config.include_tests and ('test_' in file_path.name or '/tests/' in str(file_path)):
                continue
            
            python_files.append(file_path)
        
        return python_files
    
    async def _analyze_files_async(self, files: List[Path]) -> List[FileMetrics]:
        """Analyze files asynchronously"""
        print(f"⚡ Analyzing {len(files)} files asynchronously...")
        
        # Create analysis tasks
        tasks = []
        for file_path in files:
            task = asyncio.create_task(self._analyze_single_file_async(file_path))
            tasks.append(task)
        
        # Execute with timeout
        try:
            file_metrics = await asyncio.wait_for(
                asyncio.gather(*tasks, return_exceptions=True),
                timeout=self.config.analysis_timeout
            )
        except asyncio.TimeoutError:
            print("⚠️  Analysis timed out, returning partial results")
            file_metrics = []
        
        # Filter out exceptions and None results
        valid_metrics = []
        for metric in file_metrics:
            if isinstance(metric, FileMetrics):
                valid_metrics.append(metric)
            elif isinstance(metric, Exception):
                print(f"⚠️  Analysis error: {metric}")
        
        return valid_metrics
    
    async def _analyze_single_file_async(self, file_path: Path) -> Optional[FileMetrics]:
        """Analyze single file asynchronously"""
        
        # Check cache first
        if self.cache and str(file_path) in self.cache:
            return self.cache[str(file_path)]
        
        try:
            # Run analysis in thread pool
            loop = asyncio.get_event_loop()
            metrics = await loop.run_in_executor(
                self.executor,
                self._analyze_single_file_sync,
                file_path
            )
            
            # Cache result
            if self.cache and metrics:
                self.cache[str(file_path)] = metrics
            
            return metrics
            
        except Exception as e:
            print(f"❌ Error analyzing {file_path}: {e}")
            return None
    
    def _analyze_files_sync(self, files: List[Path]) -> List[FileMetrics]:
        """Analyze files synchronously"""
        print(f"🔄 Analyzing {len(files)} files synchronously...")
        
        file_metrics = []
        for i, file_path in enumerate(files):
            if i % 10 == 0:
                print(f"  Progress: {i}/{len(files)} files")
            
            metrics = self._analyze_single_file_sync(file_path)
            if metrics:
                file_metrics.append(metrics)
        
        return file_metrics
    
    def _analyze_single_file_sync(self, file_path: Path) -> Optional[FileMetrics]:
        """Analyze single file synchronously"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Basic metrics calculation
            lines = content.split('\n')
            total_lines = len(lines)
            code_lines = len([line for line in lines if line.strip() and not line.strip().startswith('#')])
            
            # Simple complexity calculation (count control structures)
            complexity = self._calculate_complexity(content)
            
            # Count functions and classes
            functions = content.count('def ')
            classes = content.count('class ')
            
            # Simple quality score (0-10)
            quality_score = self._calculate_quality_score(
                complexity, total_lines, code_lines, functions, classes
            )
            
            # Create FileMetrics (simplified)
            metrics = FileMetrics(
                file_path=str(file_path),
                lines=total_lines,
                code_lines=code_lines,
                complexity=complexity,
                functions=functions,
                classes=classes,
                quality_score=quality_score,
                issues=max(0, complexity - self.config.complexity_threshold),
                analysis_time=time.time()
            )
            
            return metrics
            
        except Exception as e:
            print(f"❌ Error analyzing {file_path}: {e}")
            return None
    
    def _calculate_complexity(self, content: str) -> int:
        """Calculate cyclomatic complexity"""
        complexity = 1  # Base complexity
        
        # Count control structures
        complexity += content.count('if ')
        complexity += content.count('elif ')
        complexity += content.count('while ')
        complexity += content.count('for ')
        complexity += content.count('except ')
        complexity += content.count('with ')
        complexity += content.count('and ')
        complexity += content.count('or ')
        
        return complexity
    
    def _calculate_quality_score(self, complexity: int, total_lines: int, 
                                code_lines: int, functions: int, classes: int) -> float:
        """Calculate quality score (0-10)"""
        score = 10.0
        
        # Penalize high complexity
        if complexity > 20:
            score -= min(3.0, (complexity - 20) * 0.1)
        
        # Penalize very long files
        if total_lines > 500:
            score -= min(2.0, (total_lines - 500) * 0.001)
        
        # Reward good structure (functions and classes)
        if code_lines > 0:
            structure_ratio = (functions + classes) / (code_lines / 50)  # Rough heuristic
            if structure_ratio > 0.5:
                score += min(1.0, structure_ratio - 0.5)
        
        return max(0.0, min(10.0, score))
    
    async def _analyze_directories(self, project_path: Path, file_metrics: List[FileMetrics]) -> List[DirectoryMetrics]:
        """Analyze directory structure"""
        print("📁 Analyzing directory structure...")
        
        directory_metrics = []
        
        # Group files by directory
        dir_files = {}
        for fm in file_metrics:
            dir_path = Path(fm.file_path).parent
            if str(dir_path) not in dir_files:
                dir_files[str(dir_path)] = []
            dir_files[str(dir_path)].append(fm)
        
        # Calculate directory metrics
        for dir_path, files in dir_files.items():
            if len(files) == 0:
                continue
            
            total_lines = sum(fm.lines for fm in files)
            avg_complexity = sum(fm.complexity for fm in files) / len(files)
            avg_quality = sum(fm.quality_score for fm in files) / len(files)
            total_issues = sum(fm.issues for fm in files)
            
            # Create DirectoryMetrics (simplified)
            dm = DirectoryMetrics(
                directory_path=dir_path,
                file_count=len(files),
                total_lines=total_lines,
                average_complexity=avg_complexity,
                average_quality=avg_quality,
                total_issues=total_issues,
                files=files
            )
            
            directory_metrics.append(dm)
        
        return directory_metrics
    
    def _calculate_project_metrics(self, file_metrics: List[FileMetrics], 
                                 directory_metrics: List[DirectoryMetrics]) -> ProjectMetrics:
        """Calculate overall project metrics"""
        print("📊 Calculating project metrics...")
        
        if not file_metrics:
            return ProjectMetrics(
                total_files=0,
                total_lines=0,
                average_complexity=0,
                max_complexity=0,
                average_quality=0,
                total_issues=0,
                quality_score=0
            )
        
        total_files = len(file_metrics)
        total_lines = sum(fm.lines for fm in file_metrics)
        avg_complexity = sum(fm.complexity for fm in file_metrics) / total_files
        max_complexity = max(fm.complexity for fm in file_metrics)
        avg_quality = sum(fm.quality_score for fm in file_metrics) / total_files
        total_issues = sum(fm.issues for fm in file_metrics)
        
        # Overall quality score
        quality_score = avg_quality
        if avg_complexity > 20:
            quality_score -= 1.0
        if total_issues > total_files * 2:
            quality_score -= 1.0
        
        quality_score = max(0.0, min(10.0, quality_score))
        
        # Create ProjectMetrics (simplified)
        pm = ProjectMetrics(
            total_files=total_files,
            total_lines=total_lines,
            average_complexity=avg_complexity,
            max_complexity=max_complexity,
            average_quality=avg_quality,
            total_issues=total_issues,
            quality_score=quality_score,
            directories=directory_metrics,
            files=file_metrics
        )
        
        return pm


# Convenience function for backward compatibility
async def analyze_project(path: Union[str, Path], config: Optional[AnalysisConfig] = None) -> AnalysisResult:
    """Analyze project using unified engine"""
    engine = UnifiedAnalysisEngine(config)
    return await engine.analyze_project(path)


# Synchronous wrapper for backward compatibility
def analyze_project_sync(path: Union[str, Path], config: Optional[AnalysisConfig] = None) -> AnalysisResult:
    """Synchronous wrapper for project analysis"""
    return asyncio.run(analyze_project(path, config))
