"""
Simple Analyzer Module
==================

This module provides a simplified version of the project analyzer that doesn't
rely on the actor system. It's designed to be used in environments where
the actor system might not work well, such as in Streamlit applications.

This implementation uses the shared analysis core to provide a linear execution
flow while maintaining consistency with the actor-based system.
"""

import logging
import time
from pathlib import Path
from typing import Union, Optional, Dict, Any

from vibe_check.core.models import ProjectMetrics
from vibe_check.core.utils import generate_reports
from vibe_check.core.analysis import ProjectAnalyzer
from vibe_check.core.analysis.import_analyzer import ImportAnalyzer
from vibe_check.core.analysis.import_visualizer import ImportVisualizer
from vibe_check.core.config import load_config
from vibe_check.core.utils.async_utils import run_async

logger = logging.getLogger("vibe_check_simple_analyzer")


def simple_analyze_project(
    project_path: Union[str, Path],
    output_dir: Optional[Union[str, Path]] = None,
    config: Optional[Dict[str, Any]] = None,
    profile: str = "standard"
) -> ProjectMetrics:
    """
    Analyze a project using a simplified approach that doesn't rely on the actor system.

    This function uses the shared analysis core to execute the analysis linearly,
    processing each file and running each tool sequentially. It now includes
    adaptive configuration management for context-aware analysis behavior.

    Args:
        project_path: Path to the project directory
        output_dir: Optional directory to write output files to
        config: Optional configuration dictionary
        profile: Analysis profile to use ("minimal", "standard", "comprehensive", etc.)

    Returns:
        ProjectMetrics object with analysis results
    """
    logger.info(f"Starting simple analysis of project: {project_path}")
    logger.info(f"Using analysis profile: {profile}")
    start_time = time.time()

    # Load simple configuration
    base_config = load_config()

    # Apply any overrides
    if config:
        base_config.update(config)

    # Set simple defaults based on profile
    if profile == "minimal":
        base_config.setdefault("import_analysis", {})["enabled"] = False
        base_config.setdefault("visualization", {})["enabled"] = False
    elif profile == "comprehensive":
        base_config.setdefault("import_analysis", {})["enabled"] = True
        base_config.setdefault("visualization", {})["enabled"] = True
    else:  # standard
        base_config.setdefault("import_analysis", {})["enabled"] = True
        base_config.setdefault("visualization", {})["enabled"] = False

    logger.info(f"Configuration loaded for profile: {profile}")
    logger.info(f"Import analysis enabled: {base_config.get('import_analysis', {}).get('enabled', True)}")
    logger.info(f"Visualizations enabled: {base_config.get('visualization', {}).get('enabled', False)}")

    # Create a project analyzer with simple configuration
    analyzer = ProjectAnalyzer(
        project_path=project_path,
        config=base_config,
        output_dir=output_dir
    )

    # Run the analysis
    metrics = run_async(analyzer.analyze_project)

    # Run advanced import analysis if enabled
    import_results = None
    if base_config.get('import_analysis', {}).get('enabled', True):
        logger.info("Running advanced import analysis...")
        try:
            import_analyzer = ImportAnalyzer(str(project_path))
            import_results = import_analyzer.analyze_project()

            # Add import analysis results to metrics
            metrics.import_analysis = import_results

            # Log import analysis results
            logger.info(f"Import analysis - Circular dependencies: {len(import_results.circular_dependencies)}")
            logger.info(f"Import analysis - Files with unused imports: {len(import_results.unused_imports)}")
        except Exception as e:
            logger.warning(f"Import analysis failed: {e}")
            logger.debug(f"Import analysis error details: {e}", exc_info=True)
    else:
        logger.info("Import analysis disabled by configuration")

    # Log completion
    end_time = time.time()
    logger.info(f"Analysis completed in {end_time - start_time:.2f} seconds")
    logger.info(f"Total files: {metrics.total_file_count}")
    logger.info(f"Total lines: {metrics.total_line_count}")
    logger.info(f"Average complexity: {metrics.avg_complexity:.2f}")
    logger.info(f"Total issues: {metrics.issue_count}")

    # Generate reports if output_dir is provided
    if output_dir:
        output_path = Path(output_dir)
        generate_reports(metrics, output_path)

        # Generate import analysis visualizations if enabled and available
        if base_config.get('visualization', {}).get('enabled', False) and import_results:
            logger.info("Generating import analysis visualizations...")
            try:
                import_viz_dir = output_path / "import_analysis"
                import_visualizer = ImportVisualizer(import_results, str(import_viz_dir))
                visualizations = import_visualizer.generate_all_visualizations()

                if visualizations:
                    logger.info(f"Generated {len(visualizations)} import visualizations:")
                    for viz_name, viz_path in visualizations.items():
                        logger.info(f"  - {viz_name}: {viz_path}")
                else:
                    logger.warning("No import visualizations were generated")

            except Exception as e:
                logger.error(f"Error generating import visualizations: {e}")
        elif not base_config.get('visualization', {}).get('enabled', False):
            logger.info("Visualizations disabled by configuration")
        elif not import_results:
            logger.info("No import analysis results available for visualization")

    return metrics
