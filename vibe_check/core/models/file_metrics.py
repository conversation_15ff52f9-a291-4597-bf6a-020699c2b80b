"""
File Metrics Model
================

This module defines the FileMetrics class, which stores analysis results
for a single file in the project.
"""

from dataclasses import dataclass, field
from pathlib import Path
from typing import List, Dict, Optional, Any


@dataclass
class FileMetrics:
    """
    Detailed metrics for a single file.

    This is a core domain entity representing comprehensive analysis
    results for an individual file in the project.

    Attributes:
        path: File path (relative to project root)
        name: File name
        size: File size in bytes
        lines: Number of lines in the file
        line_count: Number of lines in the file (alias for lines)
        imports: List of imported modules/packages
        imported_by: List of modules/packages that import this file
        functions: List of function names defined in the file
        classes: List of class names defined in the file
        complexity: Cyclomatic complexity score
        docstring_coverage: Percentage of elements with docstrings (0-100)
        type_coverage: Percentage of elements with type annotations (0-100)
        maintainability_index: Maintainability score (0-100)
        circular_deps: List of circular dependencies
        internal_deps: List of internal dependencies (within the project)
        external_deps: List of external dependencies (outside the project)
        coupled_modules: List of modules this file is tightly coupled with
        description: Brief description of the file's purpose
        is_documentation: Whether this is a documentation file
        is_package: Whether this is a package __init__ file
        is_test: Whether this is a test file
        documentation_quality: Quality score for documentation (0-100)
        sections: Number of sections in documentation files
        code_examples: Number of code examples in documentation files
        docstrings: Module/class/function docstrings
        roles: Architectural role (PC: Pattern Component, AN: Analysis, etc.)
        tool_results: Tool outputs for this file
        issues: List of issues found in the file
        dependencies: List of dependencies (alias for internal_deps)
    """
    path: str
    name: str = ""
    size: int = 0
    lines: int = 0
    imports: List[str] = field(default_factory=list)
    imported_by: List[str] = field(default_factory=list)
    functions: List[str] = field(default_factory=list)
    classes: List[str] = field(default_factory=list)
    complexity: int = 0
    docstring_coverage: float = 0.0
    type_coverage: float = 0.0
    maintainability_index: float = 0.0
    circular_deps: List[str] = field(default_factory=list)
    internal_deps: List[str] = field(default_factory=list)
    external_deps: List[str] = field(default_factory=list)
    coupled_modules: List[str] = field(default_factory=list)
    description: str = ""
    issues: List[Dict[str, Any]] = field(default_factory=list)

    # Documentation-specific metrics
    is_documentation: bool = False
    is_package: bool = False
    is_test: bool = False
    documentation_quality: float = 0.0
    sections: int = 0
    code_examples: int = 0

    # Content extraction fields
    docstrings: Dict[str, Any] = field(default_factory=dict)
    roles: str = ""
    tool_results: Dict[str, Any] = field(default_factory=dict)

    # Security metrics
    security_score: float = 100.0
    security_issues: int = 0
    high_severity_issues: int = 0

    def __init__(
        self,
        path: str,
        name: str = "",
        size: int = 0,
        lines: int = 0,
        line_count: Optional[int] = None,
        imports: Optional[List[str]] = None,
        imported_by: Optional[List[str]] = None,
        functions: Optional[List[str]] = None,
        classes: Optional[List[str]] = None,
        complexity: int = 0,
        docstring_coverage: float = 0.0,
        type_coverage: float = 0.0,
        maintainability_index: float = 0.0,
        circular_deps: Optional[List[str]] = None,
        internal_deps: Optional[List[str]] = None,
        external_deps: Optional[List[str]] = None,
        coupled_modules: Optional[List[str]] = None,
        description: str = "",
        issues: Optional[List[Dict[str, Any]]] = None,
        is_documentation: bool = False,
        is_package: bool = False,
        is_test: bool = False,
        documentation_quality: float = 0.0,
        sections: int = 0,
        code_examples: int = 0,
        docstrings: Optional[Dict[str, Any]] = None,
        roles: str = "",
        tool_results: Optional[Dict[str, Any]] = None,
        dependencies: Optional[List[str]] = None,
        security_score: float = 100.0,
        security_issues: int = 0,
        high_severity_issues: int = 0,
    ):
        """
        Initialize a FileMetrics instance.

        Args:
            path: File path (relative to project root)
            name: File name
            size: File size in bytes
            lines: Number of lines in the file
            line_count: Number of lines in the file (alias for lines)
            imports: List of imported modules/packages
            imported_by: List of modules/packages that import this file
            functions: List of function names defined in the file
            classes: List of class names defined in the file
            complexity: Cyclomatic complexity score
            docstring_coverage: Percentage of elements with docstrings (0-100)
            type_coverage: Percentage of elements with type annotations (0-100)
            maintainability_index: Maintainability score (0-100)
            circular_deps: List of circular dependencies
            internal_deps: List of internal dependencies (within the project)
            external_deps: List of external dependencies (outside the project)
            coupled_modules: List of modules this file is tightly coupled with
            description: Brief description of the file's purpose
            issues: List of issues found in the file
            is_documentation: Whether this is a documentation file
            is_package: Whether this is a package __init__ file
            is_test: Whether this is a test file
            documentation_quality: Quality score for documentation (0-100)
            sections: Number of sections in documentation files
            code_examples: Number of code examples in documentation files
            docstrings: Module/class/function docstrings
            roles: Architectural role (PC: Pattern Component, AN: Analysis, etc.)
            tool_results: Tool outputs for this file
            dependencies: List of dependencies (alias for internal_deps)
        """
        self.path = path
        self.name = name
        self.size = size
        self.lines = line_count if line_count is not None else lines
        self.imports = imports or []
        self.imported_by = imported_by or []
        self.functions = functions or []
        self.classes = classes or []
        self.complexity = complexity
        self.docstring_coverage = docstring_coverage
        self.type_coverage = type_coverage
        self.maintainability_index = maintainability_index
        self.circular_deps = circular_deps or []
        self.internal_deps = dependencies or internal_deps or []
        self.external_deps = external_deps or []
        self.coupled_modules = coupled_modules or []
        self.description = description
        self.issues = issues or []
        self.is_documentation = is_documentation
        self.is_package = is_package
        self.is_test = is_test
        self.documentation_quality = documentation_quality
        self.sections = sections
        self.code_examples = code_examples
        self.docstrings = docstrings or {}
        self.roles = roles
        self.tool_results = tool_results or {}
        self.security_score = security_score
        self.security_issues = security_issues
        self.high_severity_issues = high_severity_issues

    @property
    def line_count(self) -> int:
        """Get the number of lines in the file (alias for lines)."""
        return self.lines

    @line_count.setter
    def line_count(self, value: int) -> None:
        """Set the number of lines in the file."""
        self.lines = value

    @property
    def dependencies(self) -> List[str]:
        """Get the dependencies of the file (alias for internal_deps)."""
        return self.internal_deps

    @dependencies.setter
    def dependencies(self, value: List[str]) -> None:
        """Set the dependencies of the file."""
        self.internal_deps = value

    def add_issue(self, code: str, message: str, line: int, severity: str) -> None:
        """
        Add an issue to the file metrics.

        Args:
            code: Issue code (e.g., "E101")
            message: Issue message
            line: Line number where the issue was found
            severity: Issue severity (e.g., "error", "warning")
        """
        self.issues.append({
            "code": code,
            "message": message,
            "line": line,
            "severity": severity
        })

    def add_dependency(self, dependency_path: str) -> None:
        """
        Add a dependency to the file metrics.

        Args:
            dependency_path: Path to the dependency
        """
        if dependency_path not in self.internal_deps:
            self.internal_deps.append(dependency_path)

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the file metrics to a dictionary.

        Returns:
            Dictionary representation of the file metrics
        """
        return {
            "path": self.path,
            "name": self.name,
            "size": self.size,
            "line_count": self.lines,
            "lines": self.lines,
            "imports": self.imports,
            "imported_by": self.imported_by,
            "functions": self.functions,
            "classes": self.classes,
            "complexity": self.complexity,
            "docstring_coverage": self.docstring_coverage,
            "type_coverage": self.type_coverage,
            "maintainability_index": self.maintainability_index,
            "circular_deps": self.circular_deps,
            "internal_deps": self.internal_deps,
            "external_deps": self.external_deps,
            "coupled_modules": self.coupled_modules,
            "description": self.description,
            "is_documentation": self.is_documentation,
            "is_package": self.is_package,
            "is_test": self.is_test,
            "documentation_quality": self.documentation_quality,
            "sections": self.sections,
            "code_examples": self.code_examples,
            "issues": self.issues,
            "dependencies": self.internal_deps,
            "security_score": self.security_score,
            "security_issues": self.security_issues,
            "high_severity_issues": self.high_severity_issues
        }

    @property
    def is_python(self) -> bool:
        """Check if the file is a Python file."""
        return self.path.endswith(".py")

    @property
    def is_markdown(self) -> bool:
        """Check if the file is a Markdown file."""
        return self.path.endswith(".md")

    @property
    def basename(self) -> str:
        """Get the base name of the file."""
        return Path(self.path).name

    @property
    def directory(self) -> str:
        """Get the directory containing the file."""
        return str(Path(self.path).parent)

    def calculate_health_score(self) -> float:
        """
        Calculate an overall health score for the file.

        This takes into account code quality, security issues, and other metrics.

        Returns:
            Health score from 0-100 (higher is better)
        """
        # Start with a perfect score
        health_score = 100.0

        # Deduct points for security issues
        if hasattr(self, 'security_issues') and self.security_issues > 0:
            # High severity issues have more impact
            high_impact = self.high_severity_issues * 10
            medium_impact = (self.security_issues - self.high_severity_issues) * 5
            security_deduction = min(50, high_impact + medium_impact)
            health_score -= security_deduction

        # Deduct points for complexity
        if hasattr(self, 'complexity') and self.complexity > 0:
            complexity_deduction = min(20, self.complexity / 5)
            health_score -= complexity_deduction

        # Deduct points for poor documentation
        if hasattr(self, 'docstring_coverage'):
            doc_deduction = min(15, (100 - self.docstring_coverage) / 5)
            health_score -= doc_deduction

        # Ensure the score stays within bounds
        return max(0, min(100, health_score))

    @classmethod
    def from_path(cls, path: str) -> 'FileMetrics':
        """Create a FileMetrics instance from a file path."""
        path_obj = Path(path)
        name = path_obj.name

        # Determine if this is a package __init__ file
        is_package = name == "__init__.py"

        # Determine if this is a test file
        is_test = "test" in name.lower() or "/tests/" in path.replace("\\", "/")

        # Determine if this is a documentation file
        is_documentation = name.endswith(".md") or name.endswith(".rst")

        return cls(
            path=path,
            name=name,
            is_package=is_package,
            is_test=is_test,
            is_documentation=is_documentation
        )

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FileMetrics':
        """
        Create a FileMetrics instance from a dictionary.

        Args:
            data: Dictionary containing file metrics data

        Returns:
            A new FileMetrics instance
        """
        # Extract required path
        path = data.get("path", "")
        if not path:
            raise ValueError("FileMetrics requires a path")

        # Create instance with all available fields
        return cls(
            path=path,
            name=data.get("name", ""),
            size=data.get("size", 0),
            lines=data.get("lines", data.get("line_count", 0)),
            imports=data.get("imports", []),
            imported_by=data.get("imported_by", []),
            functions=data.get("functions", []),
            classes=data.get("classes", []),
            complexity=data.get("complexity", 0),
            docstring_coverage=data.get("docstring_coverage", 0.0),
            type_coverage=data.get("type_coverage", 0.0),
            maintainability_index=data.get("maintainability_index", 0.0),
            circular_deps=data.get("circular_deps", []),
            internal_deps=data.get("internal_deps", data.get("dependencies", [])),
            external_deps=data.get("external_deps", []),
            coupled_modules=data.get("coupled_modules", []),
            description=data.get("description", ""),
            issues=data.get("issues", []),
            is_documentation=data.get("is_documentation", False),
            is_package=data.get("is_package", False),
            is_test=data.get("is_test", False),
            documentation_quality=data.get("documentation_quality", 0.0),
            sections=data.get("sections", 0),
            code_examples=data.get("code_examples", 0),
            docstrings=data.get("docstrings", {}),
            roles=data.get("roles", ""),
            tool_results=data.get("tool_results", {}),
            security_score=data.get("security_score", 100.0),
            security_issues=data.get("security_issues", 0),
            high_severity_issues=data.get("high_severity_issues", 0)
        )
