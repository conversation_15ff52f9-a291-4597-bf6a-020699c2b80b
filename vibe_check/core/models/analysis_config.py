"""
Analysis Configuration Models
============================

This module provides configuration models for analysis operations.
"""

from dataclasses import dataclass
from typing import Dict, Any, Optional, List
from pathlib import Path


@dataclass
class AnalysisConfig:
    """Base configuration for analysis operations"""
    
    # Basic settings
    enable_caching: bool = True
    cache_ttl: int = 3600  # Cache time-to-live in seconds
    
    # Performance settings
    max_workers: int = 4
    timeout: float = 300.0  # 5 minutes
    
    # Analysis settings
    include_tests: bool = True
    include_docs: bool = True
    
    # Tool settings
    tools_config: Optional[Dict[str, Any]] = None
    
    def __post_init__(self) -> None:
        if self.tools_config is None:
            self.tools_config = {}


@dataclass
class AnalysisResult:
    """Base result for analysis operations"""
    
    # Basic result data
    success: bool = True
    error_message: Optional[str] = None
    
    # Analysis data
    data: Optional[Dict[str, Any]] = None
    
    def __post_init__(self) -> None:
        if self.data is None:
            self.data = {}

    def has_errors(self) -> bool:
        """Check if result has errors."""
        return not self.success or self.error_message is not None


@dataclass
class AnalysisIssue:
    """Represents a single analysis issue found in code."""

    # Issue identification
    rule_id: str
    message: str
    severity: str  # 'error', 'warning', 'info', 'hint'

    # Location information
    file_path: Path
    line_number: Optional[int] = None
    column_number: Optional[int] = None

    # Additional context
    tool: Optional[str] = None
    category: Optional[str] = None
    fix_suggestion: Optional[str] = None

    def __str__(self) -> str:
        location = f"{self.file_path}"
        if self.line_number:
            location += f":{self.line_number}"
            if self.column_number:
                location += f":{self.column_number}"

        return f"{location}: {self.severity}: {self.message} ({self.rule_id})"
