"""
Analysis Configuration Models
============================

This module provides configuration models for analysis operations.
"""

from dataclasses import dataclass
from typing import Dict, Any, Optional


@dataclass
class AnalysisConfig:
    """Base configuration for analysis operations"""
    
    # Basic settings
    enable_caching: bool = True
    cache_ttl: int = 3600  # Cache time-to-live in seconds
    
    # Performance settings
    max_workers: int = 4
    timeout: float = 300.0  # 5 minutes
    
    # Analysis settings
    include_tests: bool = True
    include_docs: bool = True
    
    # Tool settings
    tools_config: Optional[Dict[str, Any]] = None
    
    def __post_init__(self) -> None:
        if self.tools_config is None:
            self.tools_config = {}


@dataclass
class AnalysisResult:
    """Base result for analysis operations"""
    
    # Basic result data
    success: bool = True
    error_message: Optional[str] = None
    
    # Analysis data
    data: Optional[Dict[str, Any]] = None
    
    def __post_init__(self) -> None:
        if self.data is None:
            self.data = {}
