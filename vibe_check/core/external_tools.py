"""
External Tool Integration
========================

This module provides integration with external analysis tools like ruff, mypy, bandit.
It executes tools, parses their output, and converts results to unified AnalysisIssue format.
"""

import asyncio
import logging
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Set, Any, Union

from .vcs.models import AnalysisIssue, IssueSeverity, RuleCategory

logger = logging.getLogger(__name__)


class ExternalToolRunner:
    """
    Manages execution of external analysis tools and result integration.
    """
    
    def __init__(self) -> None:
        """Initialize the external tool runner."""
        self.available_tools = {
            'ruff': self._run_ruff,
            'mypy': self._run_mypy,
            'bandit': self._run_bandit,
            'pylint': self._run_pylint,
            'flake8': self._run_flake8,
            'black': self._run_black,
            'isort': self._run_isort
        }
    
    async def run_tools(self, tools: Set[str], project_path: Path) -> Dict[str, List[AnalysisIssue]]:
        """
        Run specified external tools on the project.
        
        Args:
            tools: Set of tool names to run
            project_path: Path to the project directory
            
        Returns:
            Dictionary mapping tool names to lists of AnalysisIssue objects
        """
        results = {}
        
        for tool in tools:
            if tool not in self.available_tools:
                logger.warning(f"Unknown tool: {tool}")
                continue
                
            if not self._is_tool_available(tool):
                logger.warning(f"Tool not available: {tool}")
                continue
            
            try:
                logger.info(f"Running {tool} on {project_path}")
                issues = await self.available_tools[tool](project_path)
                results[tool] = issues
                logger.info(f"{tool} found {len(issues)} issues")
            except Exception as e:
                logger.error(f"Error running {tool}: {e}")
                results[tool] = []
        
        return results
    
    def _is_tool_available(self, tool: str) -> bool:
        """Check if a tool is available in the system."""
        try:
            subprocess.run([tool, '--version'], 
                         capture_output=True, 
                         check=False, 
                         timeout=5)
            return True
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False
    
    async def _run_ruff(self, project_path: Path) -> List[AnalysisIssue]:
        """Run ruff and parse results."""
        try:
            cmd = ['ruff', 'check', '--format=json', str(project_path)]
            result = await self._execute_command(cmd)
            
            if result['returncode'] == 0:
                return []  # No issues found
            
            return self._parse_ruff_output(result['stdout'])
        except Exception as e:
            logger.error(f"Error running ruff: {e}")
            return []
    
    async def _run_mypy(self, project_path: Path) -> List[AnalysisIssue]:
        """Run mypy and parse results."""
        try:
            cmd = ['mypy', '--show-error-codes', '--no-error-summary', str(project_path)]
            result = await self._execute_command(cmd)
            
            return self._parse_mypy_output(result['stdout'])
        except Exception as e:
            logger.error(f"Error running mypy: {e}")
            return []
    
    async def _run_bandit(self, project_path: Path) -> List[AnalysisIssue]:
        """Run bandit and parse results."""
        try:
            cmd = ['bandit', '-r', '-f', 'json', str(project_path)]
            result = await self._execute_command(cmd)
            
            return self._parse_bandit_output(result['stdout'])
        except Exception as e:
            logger.error(f"Error running bandit: {e}")
            return []
    
    async def _run_pylint(self, project_path: Path) -> List[AnalysisIssue]:
        """Run pylint and parse results."""
        try:
            cmd = ['pylint', '--output-format=json', str(project_path)]
            result = await self._execute_command(cmd)
            
            return self._parse_pylint_output(result['stdout'])
        except Exception as e:
            logger.error(f"Error running pylint: {e}")
            return []
    
    async def _run_flake8(self, project_path: Path) -> List[AnalysisIssue]:
        """Run flake8 and parse results."""
        try:
            cmd = ['flake8', '--format=json', str(project_path)]
            result = await self._execute_command(cmd)
            
            return self._parse_flake8_output(result['stdout'])
        except Exception as e:
            logger.error(f"Error running flake8: {e}")
            return []
    
    async def _run_black(self, project_path: Path) -> List[AnalysisIssue]:
        """Run black and parse results."""
        try:
            cmd = ['black', '--check', '--diff', str(project_path)]
            result = await self._execute_command(cmd)
            
            return self._parse_black_output(result['stdout'])
        except Exception as e:
            logger.error(f"Error running black: {e}")
            return []
    
    async def _run_isort(self, project_path: Path) -> List[AnalysisIssue]:
        """Run isort and parse results."""
        try:
            cmd = ['isort', '--check-only', '--diff', str(project_path)]
            result = await self._execute_command(cmd)
            
            return self._parse_isort_output(result['stdout'])
        except Exception as e:
            logger.error(f"Error running isort: {e}")
            return []
    
    async def _execute_command(self, cmd: List[str]) -> Dict[str, Any]:
        """Execute a command and return the result."""
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=None
            )
            
            stdout, stderr = await process.communicate()
            
            return {
                'returncode': process.returncode,
                'stdout': stdout.decode('utf-8', errors='replace'),
                'stderr': stderr.decode('utf-8', errors='replace')
            }
        except Exception as e:
            logger.error(f"Error executing command {' '.join(cmd)}: {e}")
            return {
                'returncode': -1,
                'stdout': '',
                'stderr': str(e)
            }

    def _parse_ruff_output(self, output: str) -> List[AnalysisIssue]:
        """Parse ruff JSON output into AnalysisIssue objects."""
        import json

        issues: List[AnalysisIssue] = []
        try:
            if not output.strip():
                return issues

            data = json.loads(output)
            for item in data:
                issues.append(AnalysisIssue(
                    rule_id=item.get('code', 'RUFF'),
                    category=self._map_ruff_category(item.get('code', '')),
                    severity=IssueSeverity.WARNING,
                    message=item.get('message', ''),
                    line=item.get('location', {}).get('row', 0),
                    column=item.get('location', {}).get('column', 0),
                    fix_suggestion=item.get('fix', {}).get('message', '') if item.get('fix') else None,
                    auto_fixable=bool(item.get('fix')),
                    source='ruff',
                    metadata={'tool': 'ruff', 'url': item.get('url', ''), 'filename': item.get('filename', '')}
                ))
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing ruff output: {e}")

        return issues

    def _parse_mypy_output(self, output: str) -> List[AnalysisIssue]:
        """Parse mypy output into AnalysisIssue objects."""
        issues: List[AnalysisIssue] = []

        for line in output.strip().split('\n'):
            if not line.strip():
                continue

            # Parse mypy output format: file:line:column: severity: message [error-code]
            parts = line.split(':', 3)
            if len(parts) >= 4:
                file_path = parts[0]
                try:
                    line_num = int(parts[1])
                    column = int(parts[2]) if parts[2].isdigit() else 0
                except ValueError:
                    continue

                rest = parts[3].strip()
                severity_msg = rest.split(':', 1)
                if len(severity_msg) >= 2:
                    severity_str = severity_msg[0].strip()
                    message = severity_msg[1].strip()

                    # Extract error code if present
                    rule_id = 'MYPY'
                    if '[' in message and ']' in message:
                        start = message.rfind('[')
                        end = message.rfind(']')
                        if start < end:
                            rule_id = message[start+1:end]
                            message = message[:start].strip()

                    issues.append(AnalysisIssue(
                        rule_id=rule_id,
                        category=RuleCategory.TYPES,
                        severity=self._map_mypy_severity(severity_str),
                        message=message,
                        line=line_num,
                        column=column,
                        source='mypy',
                        metadata={'tool': 'mypy', 'filename': file_path}
                    ))

        return issues

    def _parse_bandit_output(self, output: str) -> List[AnalysisIssue]:
        """Parse bandit JSON output into AnalysisIssue objects."""
        import json

        issues: List[AnalysisIssue] = []
        try:
            if not output.strip():
                return issues

            data = json.loads(output)
            for result in data.get('results', []):
                issues.append(AnalysisIssue(
                    rule_id=result.get('test_id', 'BANDIT'),
                    category=RuleCategory.SECURITY,
                    severity=self._map_bandit_severity(result.get('issue_severity', 'MEDIUM')),
                    message=result.get('issue_text', ''),
                    line=result.get('line_number', 0),
                    column=result.get('col_offset', 0),
                    source='bandit',
                    metadata={
                        'tool': 'bandit',
                        'confidence': result.get('issue_confidence', ''),
                        'cwe': result.get('issue_cwe', {}),
                        'filename': result.get('filename', '')
                    }
                ))
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing bandit output: {e}")

        return issues

    def _parse_pylint_output(self, output: str) -> List[AnalysisIssue]:
        """Parse pylint JSON output into AnalysisIssue objects."""
        import json

        issues: List[AnalysisIssue] = []
        try:
            if not output.strip():
                return issues

            data = json.loads(output)
            for item in data:
                issues.append(AnalysisIssue(
                    rule_id=item.get('message-id', 'PYLINT'),
                    category=self._map_pylint_category(item.get('type', '')),
                    severity=self._map_pylint_severity(item.get('type', '')),
                    message=item.get('message', ''),
                    line=item.get('line', 0),
                    column=item.get('column', 0),
                    source='pylint',
                    metadata={'tool': 'pylint', 'symbol': item.get('symbol', ''), 'filename': item.get('path', '')}
                ))
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing pylint output: {e}")

        return issues

    def _parse_flake8_output(self, output: str) -> List[AnalysisIssue]:
        """Parse flake8 output into AnalysisIssue objects."""
        issues = []

        for line in output.strip().split('\n'):
            if not line.strip():
                continue

            # Parse flake8 output format: file:line:column: code message
            parts = line.split(':', 3)
            if len(parts) >= 4:
                file_path = parts[0]
                try:
                    line_num = int(parts[1])
                    column = int(parts[2])
                except ValueError:
                    continue

                code_message = parts[3].strip()
                code_parts = code_message.split(' ', 1)
                rule_id = code_parts[0] if code_parts else 'FLAKE8'
                message = code_parts[1] if len(code_parts) > 1 else code_message

                issues.append(AnalysisIssue(
                    rule_id=rule_id,
                    category=self._map_flake8_category(rule_id),
                    severity=IssueSeverity.WARNING,
                    message=message,
                    line=line_num,
                    column=column,
                    source='flake8',
                    metadata={'tool': 'flake8', 'filename': file_path}
                ))

        return issues

    def _parse_black_output(self, output: str) -> List[AnalysisIssue]:
        """Parse black diff output into AnalysisIssue objects."""
        issues = []

        if 'would reformat' in output:
            # Parse the files that would be reformatted
            lines = output.split('\n')
            for line in lines:
                if 'would reformat' in line:
                    # Extract filename from "would reformat <filename>"
                    parts = line.split()
                    if len(parts) >= 3:
                        file_path = parts[2]
                        issues.append(AnalysisIssue(
                            rule_id='BLACK001',
                            category=RuleCategory.STYLE,
                            severity=IssueSeverity.INFO,
                            message='File would be reformatted by black',
                            line=1,
                            column=0,
                            auto_fixable=True,
                            fix_suggestion='Run black to reformat this file',
                            source='black',
                            metadata={'tool': 'black', 'filename': file_path}
                        ))

        return issues

    def _parse_isort_output(self, output: str) -> List[AnalysisIssue]:
        """Parse isort diff output into AnalysisIssue objects."""
        issues = []

        if 'Skipped' in output or 'would be reformatted' in output:
            # Parse the files that would be reformatted
            lines = output.split('\n')
            for line in lines:
                if 'Skipped' in line or 'would be reformatted' in line:
                    # Extract filename
                    if ':' in line:
                        file_path = line.split(':')[0].strip()
                        issues.append(AnalysisIssue(
                            rule_id='ISORT001',
                            category=RuleCategory.IMPORTS,
                            severity=IssueSeverity.INFO,
                            message='Import order would be reformatted by isort',
                            line=1,
                            column=0,
                            auto_fixable=True,
                            fix_suggestion='Run isort to fix import order',
                            source='isort',
                            metadata={'tool': 'isort', 'filename': file_path}
                        ))

        return issues

    # Helper methods for mapping tool-specific categories and severities

    def _map_ruff_category(self, code: str) -> RuleCategory:
        """Map ruff error codes to categories."""
        if code.startswith(('E', 'W')):  # pycodestyle
            return RuleCategory.STYLE
        elif code.startswith('F'):  # pyflakes
            return RuleCategory.IMPORTS
        elif code.startswith('C'):  # complexity
            return RuleCategory.COMPLEXITY
        elif code.startswith('S'):  # bandit
            return RuleCategory.SECURITY
        elif code.startswith('D'):  # pydocstyle
            return RuleCategory.DOCS
        else:
            return RuleCategory.STYLE

    def _map_mypy_severity(self, severity: str) -> IssueSeverity:
        """Map mypy severity to IssueSeverity."""
        severity_lower = severity.lower()
        if 'error' in severity_lower:
            return IssueSeverity.ERROR
        elif 'warning' in severity_lower:
            return IssueSeverity.WARNING
        else:
            return IssueSeverity.INFO

    def _map_bandit_severity(self, severity: str) -> IssueSeverity:
        """Map bandit severity to IssueSeverity."""
        severity_lower = severity.lower()
        if severity_lower == 'high':
            return IssueSeverity.ERROR
        elif severity_lower == 'medium':
            return IssueSeverity.WARNING
        else:
            return IssueSeverity.INFO

    def _map_pylint_category(self, msg_type: str) -> RuleCategory:
        """Map pylint message types to categories."""
        if msg_type == 'convention':
            return RuleCategory.STYLE
        elif msg_type == 'refactor':
            return RuleCategory.COMPLEXITY
        elif msg_type == 'warning':
            return RuleCategory.STYLE
        elif msg_type == 'error':
            return RuleCategory.TYPES
        else:
            return RuleCategory.STYLE

    def _map_pylint_severity(self, msg_type: str) -> IssueSeverity:
        """Map pylint message types to severity."""
        if msg_type == 'error':
            return IssueSeverity.ERROR
        elif msg_type == 'warning':
            return IssueSeverity.WARNING
        else:
            return IssueSeverity.INFO

    def _map_flake8_category(self, code: str) -> RuleCategory:
        """Map flake8 error codes to categories."""
        if code.startswith(('E', 'W')):  # pycodestyle
            return RuleCategory.STYLE
        elif code.startswith('F'):  # pyflakes
            return RuleCategory.IMPORTS
        elif code.startswith('C'):  # complexity
            return RuleCategory.COMPLEXITY
        else:
            return RuleCategory.STYLE
