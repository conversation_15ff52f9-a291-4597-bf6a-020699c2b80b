"""
Error Handling Module
==================

This module provides centralized error handling functionality for the Vibe Check tool.
It implements robust error catching, logging, and recovery mechanisms to ensure
the system can gracefully handle failures during analysis.
"""

from functools import wraps
import logging
from pathlib import Path
import traceback
from typing import Any, Callable, Dict, List, Optional, TypeVar, Union, cast

# Set up logging
logger = logging.getLogger("vibe_check_error_handler")

# Type definitions
F = TypeVar("F", bound=Callable[..., Any])
ErrorHandler = Callable[[Exception, Dict[str, Any]], Any]


class VibeCheckError(Exception):
    """Base class for all Vibe Check exceptions."""

    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize the exception.

        Args:
            message: Error message
            details: Optional details about the error
        """
        super().__init__(message)
        self.message = message
        self.details = details or {}


class ConfigurationError(VibeCheckError):
    """Error related to configuration issues."""


class ToolError(VibeCheckError):
    """Error related to analysis tools."""

    def __init__(self, message: str, tool_name: str, details: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize the exception.

        Args:
            message: Error message
            tool_name: Name of the tool that caused the error
            details: Optional details about the error
        """
        super().__init__(message, details)
        self.tool_name = tool_name


class FileError(VibeCheckError):
    """Error related to file operations."""

    def __init__(self, message: str, file_path: Union[str, Path],
                details: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize the exception.

        Args:
            message: Error message
            file_path: Path to the file that caused the error
            details: Optional details about the error
        """
        super().__init__(message, details)
        self.file_path = str(file_path)


class FileSystemError(VibeCheckError):
    """Error related to file system operations."""

    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize the exception.

        Args:
            message: Error message
            details: Optional details about the error
        """
        super().__init__(message, details)





class PluginError(VibeCheckError):
    """Error related to plugin operations."""

    def __init__(self, message: str, plugin_name: str,
                details: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize the exception.

        Args:
            message: Error message
            plugin_name: Name of the plugin that caused the error
            details: Optional details about the error
        """
        super().__init__(message, details)
        self.plugin_name = plugin_name


# Error handling decorators

def handle_errors(default_return: Any = None,
                error_handler: Optional[ErrorHandler] = None) -> Callable[[F], F]:
    """
    Decorator to handle errors in functions.

    Args:
        default_return: Value to return if an error occurs
        error_handler: Optional function to handle errors

    Returns:
        Decorated function
    """
    def decorator(func: F) -> F:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(f"Error in {func.__name__}: {e}")
                logger.debug(traceback.format_exc())

                # Create error context
                error_context = {
                    "function": func.__name__,
                    "args": args,
                    "kwargs": kwargs,
                    "exception": e,
                    "traceback": traceback.format_exc(),
                }

                # Call error handler if provided
                if error_handler:
                    try:
                        return error_handler(e, error_context)
                    except Exception as handler_error:
                        logger.error(f"Error handler failed: {handler_error}")

                # Return default value
                return default_return

        return cast(F, wrapper)

    return decorator


async def handle_async_errors(coro: Any,
                            default_return: Any = None,
                            error_handler: Optional[ErrorHandler] = None) -> Any:
    """
    Handle errors in asynchronous coroutines.

    Args:
        coro: Awaitable coroutine object to execute
        default_return: Value to return if an error occurs
        error_handler: Optional function to handle errors

    Returns:
        Result of the coroutine or default value if an error occurs
    """
    try:
        # Ensure we have an awaitable object, not just a callable
        if callable(coro) and not hasattr(coro, "__await__"):
            raise TypeError("Expected an awaitable object, got a callable")
        return await coro
    except Exception as e:
        logger.error(f"Error in async coroutine: {e}")
        logger.debug(traceback.format_exc())

        # Create error context
        error_context = {
            "coroutine": str(coro),
            "exception": e,
            "traceback": traceback.format_exc(),
        }

        # Call error handler if provided
        if error_handler:
            try:
                return error_handler(e, error_context)
            except Exception as handler_error:
                logger.error(f"Error handler failed: {handler_error}")

        # Return default value
        return default_return


def format_error_for_user(error: Exception) -> Dict[str, Any]:
    """
    Format an error for user display.

    Args:
        error: The exception to format

    Returns:
        Formatted error information
    """
    error_info = {
        "error_type": error.__class__.__name__,
        "message": str(error),
        "details": {},
    }

    # Add specific details based on error type
    if isinstance(error, VibeCheckError):
        error_info["details"] = error.details

        if isinstance(error, ToolError):
            error_info["tool_name"] = error.tool_name
        elif isinstance(error, FileError):
            error_info["file_path"] = error.file_path

        elif isinstance(error, PluginError):
            error_info["plugin_name"] = error.plugin_name

    return error_info


def log_error(error: Exception, context: Dict[str, Any]) -> None:
    """
    Log an error with context details.

    Args:
        error: The exception to log
        context: Context information about where the error occurred
    """
    # Format the error message
    error_msg = f"Error: {error.__class__.__name__}: {error}"

    # Add context information
    if "function" in context:
        error_msg += f" (in {context['function']})"

    # Log the error with appropriate level based on type
    if isinstance(error, VibeCheckError):
        logger.error(error_msg)
    else:
        logger.critical(error_msg)
        logger.debug(context.get("traceback", "No traceback available"))


def recover_from_error(error: Exception, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Attempt to recover from an error.

    Args:
        error: The exception to recover from
        context: Context information about where the error occurred

    Returns:
        Recovery information or None if recovery wasn't possible
    """
    # Log the error
    log_error(error, context)

    # Explicitly define recovery_info with type annotations
    recovery_info: Dict[str, Any] = {
        "recovered": False,
        "error": format_error_for_user(error),
        "actions_taken": [],  # Initialize as a list
    }

    # Create a properly typed reference to actions_taken
    actions_taken: List[str] = recovery_info["actions_taken"]

    # Attempt to recover based on error type
    if isinstance(error, ToolError):
        # For tool errors, we can continue without this tool
        recovery_info["recovered"] = True
        actions_taken.append(f"Disabled tool: {error.tool_name}")

    elif isinstance(error, FileError):
        # For file errors, we can skip this file
        recovery_info["recovered"] = True
        actions_taken.append(f"Skipped file: {error.file_path}")



    elif isinstance(error, ConfigurationError):
        # For configuration errors, we can use default config
        recovery_info["recovered"] = True
        actions_taken.append("Using default configuration")

    else:
        # For other errors, recovery isn't possible
        recovery_info["recovered"] = False
        actions_taken.append("No recovery action available")

    return recovery_info if recovery_info["recovered"] else None
