"""
Configuration Security Module
============================

Provides secure configuration encryption, decryption, and access control
for sensitive Vibe Check configuration data.
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64

logger = logging.getLogger(__name__)


@dataclass
class EncryptionConfig:
    """Configuration for encryption settings."""
    
    algorithm: str = "AES-256"
    key_derivation: str = "PBKDF2"
    iterations: int = 100000
    salt_length: int = 32


@dataclass
class SecurityAuditEntry:
    """Security audit log entry."""
    
    timestamp: float
    action: str
    user: str
    config_path: str
    success: bool
    details: Optional[str] = None


class ConfigEncryption:
    """
    Secure configuration encryption and decryption.
    
    Provides AES-256 encryption for sensitive configuration data
    with PBKDF2 key derivation and secure key management.
    """
    
    def __init__(self, config: Optional[EncryptionConfig] = None):
        """Initialize encryption with configuration."""
        self.config = config or EncryptionConfig()
        self._key_cache: Optional[bytes] = None
    
    def generate_key_from_password(self, password: str, salt: Optional[bytes] = None) -> tuple[bytes, bytes]:
        """
        Generate encryption key from password using PBKDF2.
        
        Args:
            password: Password for key derivation
            salt: Optional salt (generated if not provided)
            
        Returns:
            Tuple of (key, salt)
        """
        if salt is None:
            salt = os.urandom(self.config.salt_length)
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=self.config.iterations,
        )
        
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return key, salt
    
    def encrypt_sensitive_data(self, data: Dict[str, Any], password: str) -> Dict[str, Any]:
        """
        Encrypt sensitive configuration sections.
        
        Args:
            data: Configuration data to encrypt
            password: Password for encryption
            
        Returns:
            Encrypted configuration with metadata
        """
        try:
            # Generate key and salt
            key, salt = self.generate_key_from_password(password)
            fernet = Fernet(key)
            
            # Identify sensitive fields
            sensitive_data = self._extract_sensitive_data(data)
            
            if not sensitive_data:
                logger.info("No sensitive data found to encrypt")
                return data
            
            # Encrypt sensitive data
            encrypted_data = fernet.encrypt(json.dumps(sensitive_data).encode())
            
            # Create encrypted configuration
            encrypted_config = data.copy()
            encrypted_config['_encrypted'] = {
                'data': base64.urlsafe_b64encode(encrypted_data).decode(),
                'salt': base64.urlsafe_b64encode(salt).decode(),
                'algorithm': self.config.algorithm,
                'iterations': self.config.iterations,
                'encrypted_fields': list(sensitive_data.keys())
            }
            
            # Remove sensitive data from plain config
            for field in sensitive_data.keys():
                if field in encrypted_config:
                    del encrypted_config[field]
            
            logger.info(f"Encrypted {len(sensitive_data)} sensitive configuration fields")
            return encrypted_config
            
        except Exception as e:
            logger.error(f"Failed to encrypt configuration: {e}")
            raise ValueError(f"Encryption failed: {e}")
    
    def decrypt_sensitive_data(self, encrypted_config: Dict[str, Any], password: str) -> Dict[str, Any]:
        """
        Decrypt sensitive configuration sections.
        
        Args:
            encrypted_config: Encrypted configuration data
            password: Password for decryption
            
        Returns:
            Decrypted configuration data
        """
        try:
            if '_encrypted' not in encrypted_config:
                logger.debug("No encrypted data found in configuration")
                return encrypted_config
            
            encrypted_section = encrypted_config['_encrypted']
            
            # Reconstruct key from password and salt
            salt = base64.urlsafe_b64decode(encrypted_section['salt'].encode())
            key, _ = self.generate_key_from_password(password, salt)
            fernet = Fernet(key)
            
            # Decrypt data
            encrypted_data = base64.urlsafe_b64decode(encrypted_section['data'].encode())
            decrypted_json = fernet.decrypt(encrypted_data).decode()
            sensitive_data = json.loads(decrypted_json)
            
            # Merge decrypted data back into configuration
            decrypted_config = encrypted_config.copy()
            del decrypted_config['_encrypted']
            decrypted_config.update(sensitive_data)
            
            logger.info(f"Decrypted {len(sensitive_data)} sensitive configuration fields")
            return decrypted_config
            
        except Exception as e:
            logger.error(f"Failed to decrypt configuration: {e}")
            raise ValueError(f"Decryption failed: {e}")
    
    def is_encrypted_config(self, config_data: Dict[str, Any]) -> bool:
        """
        Check if configuration contains encrypted data.
        
        Args:
            config_data: Configuration data to check
            
        Returns:
            True if configuration is encrypted
        """
        return '_encrypted' in config_data
    
    def _extract_sensitive_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract sensitive fields from configuration data."""
        sensitive_fields = {
            'api_key', 'api_secret', 'password', 'token', 'secret',
            'private_key', 'certificate', 'credentials', 'auth_token',
            'database_password', 'smtp_password', 'webhook_secret'
        }
        
        sensitive_data = {}
        
        def extract_recursive(obj: Any, path: str = "") -> None:
            if isinstance(obj, dict):
                for key, value in obj.items():
                    current_path = f"{path}.{key}" if path else key
                    
                    # Check if key contains sensitive information
                    if any(sensitive in key.lower() for sensitive in sensitive_fields):
                        sensitive_data[current_path] = value
                    elif isinstance(value, (dict, list)):
                        extract_recursive(value, current_path)
        
        extract_recursive(data)
        return sensitive_data


class ConfigAccessControl:
    """
    Configuration access control and audit logging.
    
    Provides file permission validation, user-based access control,
    and comprehensive audit logging for configuration operations.
    """
    
    def __init__(self, audit_log_path: Optional[Path] = None):
        """Initialize access control with audit logging."""
        self.audit_log_path = audit_log_path or Path.home() / '.vibe-check' / 'audit.log'
        self.audit_log_path.parent.mkdir(parents=True, exist_ok=True)
    
    def validate_file_permissions(self, config_path: Path) -> bool:
        """
        Validate configuration file permissions.
        
        Args:
            config_path: Path to configuration file
            
        Returns:
            True if permissions are secure
        """
        try:
            if not config_path.exists():
                return True  # File doesn't exist yet
            
            # Check file permissions (should be 600 for sensitive configs)
            stat_info = config_path.stat()
            permissions = oct(stat_info.st_mode)[-3:]
            
            # Warn if file is readable by group or others
            if permissions != '600':
                logger.warning(f"Configuration file {config_path} has insecure permissions: {permissions}")
                logger.warning("Recommended: chmod 600 for sensitive configuration files")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to validate file permissions for {config_path}: {e}")
            return False
    
    def set_secure_permissions(self, config_path: Path) -> bool:
        """
        Set secure permissions on configuration file.
        
        Args:
            config_path: Path to configuration file
            
        Returns:
            True if permissions were set successfully
        """
        try:
            # Set permissions to 600 (read/write for owner only)
            config_path.chmod(0o600)
            logger.info(f"Set secure permissions (600) on {config_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to set secure permissions on {config_path}: {e}")
            return False
    
    def log_config_access(self, action: str, config_path: Path, success: bool = True, details: Optional[str] = None) -> None:
        """
        Log configuration access for audit purposes.
        
        Args:
            action: Action performed (read, write, encrypt, decrypt)
            config_path: Path to configuration file
            success: Whether the action was successful
            details: Additional details about the action
        """
        try:
            import time
            import getpass
            
            audit_entry = SecurityAuditEntry(
                timestamp=time.time(),
                action=action,
                user=getpass.getuser(),
                config_path=str(config_path),
                success=success,
                details=details
            )
            
            # Write to audit log
            with open(self.audit_log_path, 'a') as f:
                log_line = (f"{audit_entry.timestamp:.3f} | {audit_entry.user} | "
                           f"{audit_entry.action} | {audit_entry.config_path} | "
                           f"{'SUCCESS' if audit_entry.success else 'FAILURE'}")
                
                if audit_entry.details:
                    log_line += f" | {audit_entry.details}"
                
                f.write(log_line + "\n")
            
        except Exception as e:
            logger.error(f"Failed to write audit log: {e}")
    
    def get_audit_log(self, limit: int = 100) -> List[SecurityAuditEntry]:
        """
        Get recent audit log entries.
        
        Args:
            limit: Maximum number of entries to return
            
        Returns:
            List of audit log entries
        """
        try:
            if not self.audit_log_path.exists():
                return []
            
            entries = []
            with open(self.audit_log_path, 'r') as f:
                lines = f.readlines()
                
                # Get last 'limit' lines
                for line in lines[-limit:]:
                    parts = line.strip().split(' | ')
                    if len(parts) >= 5:
                        entry = SecurityAuditEntry(
                            timestamp=float(parts[0]),
                            user=parts[1],
                            action=parts[2],
                            config_path=parts[3],
                            success=parts[4] == 'SUCCESS',
                            details=parts[5] if len(parts) > 5 else None
                        )
                        entries.append(entry)
            
            return entries
            
        except Exception as e:
            logger.error(f"Failed to read audit log: {e}")
            return []


class SecureConfigManager:
    """
    Integrated secure configuration manager.
    
    Combines encryption, access control, and audit logging
    for comprehensive configuration security.
    """
    
    def __init__(self):
        """Initialize secure configuration manager."""
        self.encryption = ConfigEncryption()
        self.access_control = ConfigAccessControl()
    
    def save_secure_config(self, config: Dict[str, Any], config_path: Path, password: Optional[str] = None) -> bool:
        """
        Save configuration with security measures.
        
        Args:
            config: Configuration data to save
            config_path: Path to save configuration
            password: Optional password for encryption
            
        Returns:
            True if configuration was saved successfully
        """
        try:
            # Encrypt if password provided
            if password:
                config = self.encryption.encrypt_sensitive_data(config, password)
                self.access_control.log_config_access("encrypt", config_path, True)
            
            # Save configuration
            import yaml
            with open(config_path, 'w') as f:
                yaml.dump(config, f, default_flow_style=False)
            
            # Set secure permissions
            self.access_control.set_secure_permissions(config_path)
            
            # Log access
            self.access_control.log_config_access("write", config_path, True)
            
            return True
            
        except Exception as e:
            self.access_control.log_config_access("write", config_path, False, str(e))
            logger.error(f"Failed to save secure configuration: {e}")
            return False
    
    def load_secure_config(self, config_path: Path, password: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Load configuration with security validation.
        
        Args:
            config_path: Path to configuration file
            password: Optional password for decryption
            
        Returns:
            Configuration data or None if failed
        """
        try:
            # Validate file permissions
            if not self.access_control.validate_file_permissions(config_path):
                logger.warning(f"Configuration file {config_path} has insecure permissions")
            
            # Load configuration
            import yaml
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            # Decrypt if encrypted
            if self.encryption.is_encrypted_config(config):
                if not password:
                    raise ValueError("Password required for encrypted configuration")
                
                config = self.encryption.decrypt_sensitive_data(config, password)
                self.access_control.log_config_access("decrypt", config_path, True)
            
            # Log access
            self.access_control.log_config_access("read", config_path, True)
            
            return config
            
        except Exception as e:
            self.access_control.log_config_access("read", config_path, False, str(e))
            logger.error(f"Failed to load secure configuration: {e}")
            return None
