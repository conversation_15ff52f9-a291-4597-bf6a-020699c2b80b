"""
Intelligent Dependency Manager
=============================

This module provides smart dependency management for optional Vibe Check features.
It handles automatic installation, graceful degradation, and user-friendly error messages.
"""

import subprocess
import sys
import importlib
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class DependencyStatus(Enum):
    """Status of a dependency."""
    AVAILABLE = "available"
    MISSING = "missing"
    INSTALLING = "installing"
    FAILED = "failed"


@dataclass
class DependencyInfo:
    """Information about a dependency."""
    name: str
    package: str
    version: Optional[str] = None
    description: str = ""
    install_command: str = ""
    import_name: Optional[str] = None
    status: DependencyStatus = DependencyStatus.MISSING


class DependencyManager:
    """
    Manages optional dependencies for Vibe Check interfaces.
    
    Provides automatic installation, status checking, and graceful degradation
    for optional features like TUI, Web UI, and enhanced analysis tools.
    """
    
    def __init__(self):
        """Initialize the dependency manager."""
        self.dependencies = self._define_dependencies()
        self._status_cache: Dict[str, DependencyStatus] = {}
    
    def _define_dependencies(self) -> Dict[str, DependencyInfo]:
        """Define all optional dependencies."""
        return {
            "tui": DependencyInfo(
                name="TUI Interface",
                package="textual>=0.9.0",
                description="Terminal User Interface with rich interactive components",
                install_command="pip install textual>=0.9.0 keyboard>=0.13.5",
                import_name="textual"
            ),
            "tui_keyboard": DependencyInfo(
                name="TUI Keyboard Support",
                package="keyboard>=0.13.5",
                description="Keyboard input handling for TUI",
                install_command="pip install keyboard>=0.13.5",
                import_name="keyboard"
            ),
            "web": DependencyInfo(
                name="Web Interface",
                package="streamlit>=1.10.0",
                description="Web-based user interface with interactive dashboards",
                install_command="pip install streamlit>=1.10.0 altair>=4.2.0 pandas>=1.4.0",
                import_name="streamlit"
            ),
            "web_viz": DependencyInfo(
                name="Web Visualization",
                package="altair>=4.2.0",
                description="Interactive charts and visualizations for web interface",
                install_command="pip install altair>=4.2.0",
                import_name="altair"
            ),
            "web_data": DependencyInfo(
                name="Web Data Processing",
                package="pandas>=1.4.0",
                description="Data processing and manipulation for web interface",
                install_command="pip install pandas>=1.4.0",
                import_name="pandas"
            ),
            "visualization": DependencyInfo(
                name="Enhanced Visualizations",
                package="matplotlib>=3.5.0",
                description="Advanced plotting and visualization capabilities",
                install_command="pip install matplotlib>=3.5.0 plotly>=5.10.0",
                import_name="matplotlib"
            ),
            "plotly": DependencyInfo(
                name="Interactive Plots",
                package="plotly>=5.10.0",
                description="Interactive plotting library",
                install_command="pip install plotly>=5.10.0",
                import_name="plotly"
            ),
            "security": DependencyInfo(
                name="Enhanced Security Analysis",
                package="safety>=2.0.0",
                description="Additional security vulnerability scanning",
                install_command="pip install safety>=2.0.0",
                import_name="safety"
            )
        }
    
    def check_dependency(self, dep_key: str) -> DependencyStatus:
        """
        Check if a dependency is available.
        
        Args:
            dep_key: Key of the dependency to check
            
        Returns:
            Status of the dependency
        """
        if dep_key in self._status_cache:
            return self._status_cache[dep_key]
        
        if dep_key not in self.dependencies:
            logger.warning(f"Unknown dependency: {dep_key}")
            return DependencyStatus.FAILED
        
        dep_info = self.dependencies[dep_key]
        import_name = dep_info.import_name or dep_info.name.lower()
        
        try:
            importlib.import_module(import_name)
            status = DependencyStatus.AVAILABLE
            logger.debug(f"Dependency {dep_key} is available")
        except ImportError:
            status = DependencyStatus.MISSING
            logger.debug(f"Dependency {dep_key} is missing")
        
        self._status_cache[dep_key] = status
        return status
    
    def check_interface_dependencies(self, interface: str) -> Tuple[bool, List[str]]:
        """
        Check all dependencies for a specific interface.
        
        Args:
            interface: Interface name ('tui', 'web', 'visualization')
            
        Returns:
            Tuple of (all_available, missing_dependencies)
        """
        interface_deps = {
            "tui": ["tui", "tui_keyboard"],
            "web": ["web", "web_viz", "web_data"],
            "visualization": ["visualization", "plotly"],
            "security": ["security"]
        }
        
        if interface not in interface_deps:
            logger.warning(f"Unknown interface: {interface}")
            return False, [f"Unknown interface: {interface}"]
        
        missing = []
        for dep_key in interface_deps[interface]:
            if self.check_dependency(dep_key) != DependencyStatus.AVAILABLE:
                missing.append(dep_key)
        
        return len(missing) == 0, missing
    
    def get_install_command(self, interface: str) -> str:
        """
        Get the installation command for an interface.
        
        Args:
            interface: Interface name
            
        Returns:
            Installation command string
        """
        interface_commands = {
            "tui": "pip install vibe-check[tui]",
            "web": "pip install vibe-check[web]", 
            "visualization": "pip install vibe-check[visualization]",
            "security": "pip install vibe-check[security]",
            "all": "pip install vibe-check[full]"
        }
        
        return interface_commands.get(interface, f"pip install vibe-check[{interface}]")
    
    def auto_install_dependencies(self, interface: str, prompt_user: bool = True) -> bool:
        """
        Automatically install dependencies for an interface.
        
        Args:
            interface: Interface name
            prompt_user: Whether to prompt user before installing
            
        Returns:
            True if installation succeeded, False otherwise
        """
        available, missing = self.check_interface_dependencies(interface)
        if available:
            return True
        
        install_cmd = self.get_install_command(interface)
        
        if prompt_user:
            print(f"\n🔧 Missing dependencies for {interface} interface.")
            print(f"📦 Required packages: {', '.join(missing)}")
            print(f"💡 Install command: {install_cmd}")
            
            response = input("\n❓ Would you like to install them now? (y/N): ").strip().lower()
            if response not in ['y', 'yes']:
                print("⏭️  Skipping installation. You can install manually later.")
                return False
        
        print(f"📥 Installing {interface} dependencies...")
        
        try:
            # Use the package manager installation command
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install"] + install_cmd.split()[2:],
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            if result.returncode == 0:
                print(f"✅ Successfully installed {interface} dependencies!")
                # Clear cache to force recheck
                self._status_cache.clear()
                return True
            else:
                print(f"❌ Installation failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("⏰ Installation timed out. Please try installing manually.")
            return False
        except Exception as e:
            print(f"❌ Installation error: {e}")
            return False
    
    def get_status_report(self) -> Dict[str, Any]:
        """
        Get a comprehensive status report of all dependencies.
        
        Returns:
            Dictionary with dependency status information
        """
        report = {
            "interfaces": {},
            "dependencies": {},
            "summary": {
                "total_dependencies": len(self.dependencies),
                "available": 0,
                "missing": 0
            }
        }
        
        # Check individual dependencies
        for dep_key, dep_info in self.dependencies.items():
            status = self.check_dependency(dep_key)
            report["dependencies"][dep_key] = {
                "name": dep_info.name,
                "status": status.value,
                "description": dep_info.description,
                "install_command": dep_info.install_command
            }
            
            if status == DependencyStatus.AVAILABLE:
                report["summary"]["available"] += 1
            else:
                report["summary"]["missing"] += 1
        
        # Check interface status
        for interface in ["tui", "web", "visualization", "security"]:
            available, missing = self.check_interface_dependencies(interface)
            report["interfaces"][interface] = {
                "available": available,
                "missing_dependencies": missing,
                "install_command": self.get_install_command(interface)
            }
        
        return report
    
    def print_status_report(self) -> None:
        """Print a user-friendly status report."""
        report = self.get_status_report()
        
        print("\n🔍 Vibe Check Dependency Status Report")
        print("=" * 50)
        
        # Summary
        summary = report["summary"]
        print(f"📊 Summary: {summary['available']}/{summary['total_dependencies']} dependencies available")
        
        # Interface status
        print("\n🖥️  Interface Status:")
        for interface, info in report["interfaces"].items():
            status_icon = "✅" if info["available"] else "❌"
            print(f"  {status_icon} {interface.upper()}: {'Available' if info['available'] else 'Missing dependencies'}")
            if not info["available"]:
                print(f"    💡 Install: {info['install_command']}")
        
        # Missing dependencies details
        missing_deps = [k for k, v in report["dependencies"].items() 
                       if v["status"] == "missing"]
        
        if missing_deps:
            print(f"\n📦 Missing Dependencies ({len(missing_deps)}):")
            for dep_key in missing_deps:
                dep_info = report["dependencies"][dep_key]
                print(f"  • {dep_info['name']}: {dep_info['description']}")
        
        print(f"\n💡 Quick install: pip install vibe-check[full]")


# Global dependency manager instance
dependency_manager = DependencyManager()


def check_interface_available(interface: str) -> bool:
    """
    Quick check if an interface is available.
    
    Args:
        interface: Interface name
        
    Returns:
        True if interface is available, False otherwise
    """
    available, _ = dependency_manager.check_interface_dependencies(interface)
    return available


def ensure_interface_available(interface: str, auto_install: bool = False) -> bool:
    """
    Ensure an interface is available, optionally installing dependencies.
    
    Args:
        interface: Interface name
        auto_install: Whether to automatically install missing dependencies
        
    Returns:
        True if interface is available after check/install, False otherwise
    """
    if check_interface_available(interface):
        return True
    
    if auto_install:
        return dependency_manager.auto_install_dependencies(interface, prompt_user=True)
    
    return False
