"""
Pre-commit Installation and Setup System
========================================

This module provides automated installation and configuration of Vibe Check
as a pre-commit hook in analyzed projects.

Features:
- One-command installation (vibe-check install-hooks)
- Existing configuration merging and conflict detection
- Validation and compatibility checking
- Rollback capability for safe installation
- Comprehensive installation reporting

Installation Modes:
- Fresh installation (new .pre-commit-config.yaml)
- Merge with existing configuration
- Update existing Vibe Check hooks
- Validation-only mode (dry run)
"""

import subprocess
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from vibe_check.core.logging import get_logger
from .generator import PreCommitHookGenerator, ValidationLevel, get_current_version

logger = get_logger(__name__)


class InstallMode(Enum):
    """Installation modes for pre-commit hooks."""
    
    FRESH = "fresh"          # Create new configuration
    MERGE = "merge"          # Merge with existing configuration
    UPDATE = "update"        # Update existing Vibe Check hooks
    VALIDATE = "validate"    # Validation only (dry run)


@dataclass
class ConflictInfo:
    """Information about configuration conflicts."""
    
    hook_id: str
    existing_repo: str
    new_repo: str
    resolution: str


@dataclass
class InstallResult:
    """Result of pre-commit installation."""
    
    success: bool
    mode: InstallMode
    config_path: Path
    validation_level: ValidationLevel
    conflicts_resolved: List[ConflictInfo]
    backup_path: Optional[Path] = None
    error_message: Optional[str] = None
    
    def __str__(self) -> str:
        if self.success:
            return (
                f"✅ Successfully installed Vibe Check pre-commit hooks\n"
                f"   Mode: {self.mode.value}\n"
                f"   Level: {self.validation_level.value}\n"
                f"   Config: {self.config_path}\n"
                f"   Conflicts resolved: {len(self.conflicts_resolved)}"
            )
        else:
            return f"❌ Installation failed: {self.error_message}"


class PreCommitInstaller:
    """Automated installer for Vibe Check pre-commit hooks."""
    
    def __init__(self, project_path: Path):
        self.project_path = project_path
        self.config_path = project_path / ".pre-commit-config.yaml"
        self.generator = PreCommitHookGenerator(project_path)
    
    def install(
        self,
        validation_level: ValidationLevel = ValidationLevel.STANDARD,
        mode: InstallMode = InstallMode.MERGE,
        custom_args: Optional[List[str]] = None,
        dry_run: bool = False
    ) -> InstallResult:
        """Install Vibe Check as pre-commit hook."""
        
        logger.info(f"Installing Vibe Check pre-commit hooks (level: {validation_level.value})")
        
        try:
            # Check prerequisites
            self._check_prerequisites()
            
            # Determine installation mode
            actual_mode = self._determine_mode(mode)
            
            # Create backup if needed
            backup_path = self._create_backup() if self.config_path.exists() else None
            
            # Detect and resolve conflicts
            conflicts = self._detect_conflicts(validation_level)
            
            if dry_run:
                return InstallResult(
                    success=True,
                    mode=actual_mode,
                    config_path=self.config_path,
                    validation_level=validation_level,
                    conflicts_resolved=conflicts,
                    backup_path=backup_path
                )
            
            # Perform installation
            self._perform_installation(actual_mode, validation_level, custom_args)
            
            # Install pre-commit hooks
            self._install_precommit_hooks()
            
            # Validate installation
            self._validate_installation()
            
            return InstallResult(
                success=True,
                mode=actual_mode,
                config_path=self.config_path,
                validation_level=validation_level,
                conflicts_resolved=conflicts,
                backup_path=backup_path
            )
            
        except Exception as e:
            error_msg = f"Installation failed: {e}"
            logger.error(error_msg)
            
            # Restore backup if installation failed
            if backup_path and backup_path.exists():
                self._restore_backup(backup_path)
            
            return InstallResult(
                success=False,
                mode=mode,
                config_path=self.config_path,
                validation_level=validation_level,
                conflicts_resolved=[],
                error_message=str(e)
            )
    
    def _check_prerequisites(self) -> None:
        """Check installation prerequisites."""
        
        # Check if pre-commit is available
        try:
            result = subprocess.run(
                ["pre-commit", "--version"],
                capture_output=True,
                timeout=10
            )
            if result.returncode != 0:
                raise RuntimeError("pre-commit is not installed or not working")
        except FileNotFoundError:
            raise RuntimeError("pre-commit is not installed. Install with: pip install pre-commit")
        except subprocess.TimeoutExpired:
            raise RuntimeError("pre-commit command timed out")
        
        # Check if we're in a git repository
        try:
            result = subprocess.run(
                ["git", "rev-parse", "--git-dir"],
                cwd=self.project_path,
                capture_output=True,
                timeout=5
            )
            if result.returncode != 0:
                raise RuntimeError("Not in a git repository")
        except FileNotFoundError:
            raise RuntimeError("git is not installed")
        except subprocess.TimeoutExpired:
            raise RuntimeError("git command timed out")
    
    def _determine_mode(self, requested_mode: InstallMode) -> InstallMode:
        """Determine actual installation mode based on current state."""
        
        if not self.config_path.exists():
            return InstallMode.FRESH
        
        if requested_mode == InstallMode.VALIDATE:
            return InstallMode.VALIDATE
        
        # Check if Vibe Check is already configured
        existing_config = self._load_existing_config()
        has_vibe_check = self._has_vibe_check_hooks(existing_config)
        
        if has_vibe_check and requested_mode == InstallMode.MERGE:
            return InstallMode.UPDATE
        
        return requested_mode
    
    def _load_existing_config(self) -> Dict[str, Any]:
        """Load existing pre-commit configuration."""
        
        if not self.config_path.exists():
            return {}
        
        try:
            with open(self.config_path, 'r') as f:
                return yaml.safe_load(f) or {}
        except Exception as e:
            logger.warning(f"Failed to load existing config: {e}")
            return {}
    
    def _has_vibe_check_hooks(self, config: Dict[str, Any]) -> bool:
        """Check if configuration already has Vibe Check hooks."""
        
        repos = config.get('repos', [])
        
        for repo in repos:
            repo_url = repo.get('repo', '')
            if 'vibe_check' in repo_url or 'vibe-check' in repo_url:
                return True
        
        return False
    
    def _detect_conflicts(self, validation_level: ValidationLevel) -> List[ConflictInfo]:
        """Detect potential conflicts with existing configuration."""
        
        conflicts = []
        existing_config = self._load_existing_config()
        
        if not existing_config:
            return conflicts
        
        # Check for conflicting tools
        conflicting_tools = {
            'flake8': 'Replaced by Vibe Check linting',
            'pylint': 'Replaced by Vibe Check linting', 
            'bandit': 'Replaced by Vibe Check security analysis',
            'mypy': 'Complementary - can run alongside Vibe Check',
        }
        
        repos = existing_config.get('repos', [])
        
        for repo in repos:
            repo_url = repo.get('repo', '')
            hooks = repo.get('hooks', [])
            
            for hook in hooks:
                hook_id = hook.get('id', '')
                
                if hook_id in conflicting_tools:
                    conflicts.append(ConflictInfo(
                        hook_id=hook_id,
                        existing_repo=repo_url,
                        new_repo="https://github.com/ptzajac/vibe_check",
                        resolution=conflicting_tools[hook_id]
                    ))
        
        return conflicts
    
    def _create_backup(self) -> Path:
        """Create backup of existing configuration."""
        
        backup_path = self.config_path.with_suffix('.yaml.backup')
        
        try:
            import shutil
            shutil.copy2(self.config_path, backup_path)
            logger.info(f"Created backup: {backup_path}")
            return backup_path
        except Exception as e:
            logger.warning(f"Failed to create backup: {e}")
            raise
    
    def _restore_backup(self, backup_path: Path) -> None:
        """Restore configuration from backup."""
        
        try:
            import shutil
            shutil.copy2(backup_path, self.config_path)
            logger.info(f"Restored configuration from backup")
        except Exception as e:
            logger.error(f"Failed to restore backup: {e}")
    
    def _perform_installation(
        self,
        mode: InstallMode,
        validation_level: ValidationLevel,
        custom_args: Optional[List[str]]
    ) -> None:
        """Perform the actual installation."""
        
        if mode == InstallMode.FRESH:
            self._install_fresh(validation_level, custom_args)
        elif mode == InstallMode.MERGE:
            self._install_merge(validation_level, custom_args)
        elif mode == InstallMode.UPDATE:
            self._install_update(validation_level, custom_args)
        else:
            raise ValueError(f"Unsupported installation mode: {mode}")
    
    def _install_fresh(
        self,
        validation_level: ValidationLevel,
        custom_args: Optional[List[str]]
    ) -> None:
        """Install fresh pre-commit configuration."""
        
        config_content = self.generator.generate_project_config(
            validation_level=validation_level,
            custom_args=custom_args,
            repo_rev=f"v{get_current_version()}"
        )
        
        with open(self.config_path, 'w') as f:
            f.write(config_content)
        
        logger.info(f"Created fresh pre-commit configuration")
    
    def _install_merge(
        self,
        validation_level: ValidationLevel,
        custom_args: Optional[List[str]]
    ) -> None:
        """Merge with existing pre-commit configuration."""
        
        existing_config = self._load_existing_config()
        
        # Generate Vibe Check repo configuration
        vibe_check_repo = {
            "repo": "https://github.com/ptzajac/vibe_check",
            "rev": f"v{get_current_version()}",
            "hooks": [
                {
                    "id": f"vibe-check-{validation_level.value}",
                    "args": custom_args or []
                }
            ]
        }
        
        # Add to existing repos
        repos = existing_config.get('repos', [])
        repos.append(vibe_check_repo)
        existing_config['repos'] = repos
        
        # Save merged configuration
        with open(self.config_path, 'w') as f:
            yaml.dump(existing_config, f, default_flow_style=False, sort_keys=False)
        
        logger.info("Merged Vibe Check with existing pre-commit configuration")
    
    def _install_update(
        self,
        validation_level: ValidationLevel,
        custom_args: Optional[List[str]]
    ) -> None:
        """Update existing Vibe Check hooks."""
        
        existing_config = self._load_existing_config()
        repos = existing_config.get('repos', [])
        
        # Find and update Vibe Check repo
        for repo in repos:
            repo_url = repo.get('repo', '')
            if 'vibe_check' in repo_url or 'vibe-check' in repo_url:
                # Update revision
                repo['rev'] = f"v{get_current_version()}"
                
                # Update hooks
                repo['hooks'] = [
                    {
                        "id": f"vibe-check-{validation_level.value}",
                        "args": custom_args or []
                    }
                ]
                break
        
        # Save updated configuration
        with open(self.config_path, 'w') as f:
            yaml.dump(existing_config, f, default_flow_style=False, sort_keys=False)
        
        logger.info("Updated existing Vibe Check pre-commit hooks")
    
    def _install_precommit_hooks(self) -> None:
        """Install pre-commit hooks using pre-commit install."""
        
        try:
            result = subprocess.run(
                ["pre-commit", "install"],
                cwd=self.project_path,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode != 0:
                raise RuntimeError(f"pre-commit install failed: {result.stderr}")
            
            logger.info("Installed pre-commit hooks")
            
        except subprocess.TimeoutExpired:
            raise RuntimeError("pre-commit install timed out")
    
    def _validate_installation(self) -> None:
        """Validate that installation was successful."""
        
        try:
            # Test with a dry run
            result = subprocess.run(
                ["pre-commit", "run", "--all-files", "--dry-run"],
                cwd=self.project_path,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            # Note: pre-commit run may return non-zero even on success
            # We just check that it doesn't crash
            logger.info("Validated pre-commit installation")
            
        except subprocess.TimeoutExpired:
            logger.warning("Installation validation timed out")
        except Exception as e:
            logger.warning(f"Installation validation failed: {e}")
    
    def uninstall(self) -> bool:
        """Remove Vibe Check from pre-commit configuration."""
        
        try:
            existing_config = self._load_existing_config()
            
            if not existing_config:
                logger.info("No pre-commit configuration found")
                return True
            
            # Remove Vibe Check repos
            repos = existing_config.get('repos', [])
            filtered_repos = [
                repo for repo in repos
                if 'vibe_check' not in repo.get('repo', '') and 'vibe-check' not in repo.get('repo', '')
            ]
            
            if len(filtered_repos) == len(repos):
                logger.info("Vibe Check not found in pre-commit configuration")
                return True
            
            # Save updated configuration
            existing_config['repos'] = filtered_repos
            
            with open(self.config_path, 'w') as f:
                yaml.dump(existing_config, f, default_flow_style=False, sort_keys=False)
            
            logger.info("Removed Vibe Check from pre-commit configuration")
            return True
            
        except Exception as e:
            logger.error(f"Failed to uninstall: {e}")
            return False
