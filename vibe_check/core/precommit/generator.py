"""
Pre-commit Hook Configuration Generator
======================================

This module generates pre-commit hook configurations for Vibe Check integration,
supporting multiple validation levels and customizable rule sets.

Features:
- Auto-generation of .pre-commit-hooks.yaml
- Configurable validation levels (minimal/standard/strict)
- Custom rule set support
- Performance-optimized configurations
- Compatibility with pre-commit ecosystem standards

Performance Targets:
- Minimal Level: <10 seconds execution time
- Standard Level: <20 seconds execution time  
- Strict Level: <30 seconds execution time
"""

import yaml
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from vibe_check.core.constants import ToolNames, FileExtensions
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class ValidationLevel(Enum):
    """Pre-commit validation levels with different performance/quality trade-offs."""
    
    MINIMAL = "minimal"
    STANDARD = "standard" 
    STRICT = "strict"


@dataclass
class HookConfig:
    """Configuration for a single pre-commit hook."""
    
    id: str
    name: str
    description: str
    entry: str
    language: str = "python"
    types_or: List[str] = None
    args: List[str] = None
    require_serial: bool = False
    minimum_pre_commit_version: str = "2.9.2"
    
    def __post_init__(self):
        if self.types_or is None:
            self.types_or = ["python", "javascript", "typescript", "yaml", "json"]
        if self.args is None:
            self.args = []


class PreCommitHookGenerator:
    """Generates pre-commit hook configurations for Vibe Check."""
    
    def __init__(self, project_path: Path):
        self.project_path = project_path
        self.hooks_config_path = project_path / ".pre-commit-hooks.yaml"
    
    def generate_hooks_config(self) -> str:
        """Generate .pre-commit-hooks.yaml configuration."""
        
        hooks = [
            self._create_minimal_hook(),
            self._create_standard_hook(),
            self._create_strict_hook(),
        ]
        
        # Convert to dictionaries for YAML serialization
        hooks_data = [self._hook_to_dict(hook) for hook in hooks]
        
        return yaml.dump(hooks_data, default_flow_style=False, sort_keys=False)
    
    def _create_minimal_hook(self) -> HookConfig:
        """Create minimal validation level hook configuration."""
        return HookConfig(
            id="vibe-check-minimal",
            name="Vibe Check (Minimal)",
            description="Fast code quality analysis for pre-commit (minimal rules)",
            entry="vibe-check analyze --mode=precommit --level=minimal",
            require_serial=False,
        )
    
    def _create_standard_hook(self) -> HookConfig:
        """Create standard validation level hook configuration."""
        return HookConfig(
            id="vibe-check-standard", 
            name="Vibe Check (Standard)",
            description="Balanced code quality analysis for pre-commit",
            entry="vibe-check analyze --mode=precommit --level=standard",
            require_serial=False,
        )
    
    def _create_strict_hook(self) -> HookConfig:
        """Create strict validation level hook configuration."""
        return HookConfig(
            id="vibe-check-strict",
            name="Vibe Check (Strict)", 
            description="Comprehensive code quality analysis for pre-commit",
            entry="vibe-check analyze --mode=precommit --level=strict",
            require_serial=True,  # Strict mode may need serial execution
        )
    
    def _hook_to_dict(self, hook: HookConfig) -> Dict[str, Any]:
        """Convert HookConfig to dictionary for YAML serialization."""
        return {
            "id": hook.id,
            "name": hook.name,
            "description": hook.description,
            "entry": hook.entry,
            "language": hook.language,
            "types_or": hook.types_or,
            "args": hook.args,
            "require_serial": hook.require_serial,
            "minimum_pre_commit_version": hook.minimum_pre_commit_version,
        }
    
    def generate_project_config(
        self,
        validation_level: ValidationLevel = ValidationLevel.STANDARD,
        custom_args: Optional[List[str]] = None,
        repo_url: str = "https://github.com/ptzajac/vibe_check",
        repo_rev: str = "main"
    ) -> str:
        """Generate .pre-commit-config.yaml for a project."""
        
        hook_id = f"vibe-check-{validation_level.value}"
        
        config = {
            "repos": [
                {
                    "repo": repo_url,
                    "rev": repo_rev,
                    "hooks": [
                        {
                            "id": hook_id,
                            "args": custom_args or []
                        }
                    ]
                }
            ]
        }
        
        return yaml.dump(config, default_flow_style=False, sort_keys=False)
    
    def save_hooks_config(self) -> Path:
        """Save .pre-commit-hooks.yaml to project root."""
        
        config_content = self.generate_hooks_config()
        
        with open(self.hooks_config_path, 'w') as f:
            f.write(config_content)
        
        logger.info(f"Generated pre-commit hooks configuration: {self.hooks_config_path}")
        return self.hooks_config_path
    
    def get_validation_rules(self, level: ValidationLevel) -> List[str]:
        """Get rule set for specific validation level."""
        
        base_rules = [
            "syntax_errors",
            "import_errors", 
            "security_critical",
        ]
        
        if level == ValidationLevel.MINIMAL:
            return base_rules + [
                "type_safety_basic",
            ]
        
        elif level == ValidationLevel.STANDARD:
            return base_rules + [
                "type_safety_basic",
                "code_complexity",
                "style_violations",
                "documentation_basic",
                "performance_basic",
            ]
        
        elif level == ValidationLevel.STRICT:
            return base_rules + [
                "type_safety_advanced",
                "code_complexity",
                "style_violations", 
                "documentation_comprehensive",
                "performance_advanced",
                "architecture_violations",
                "advanced_patterns",
            ]
        
        else:
            raise ValueError(f"Unknown validation level: {level}")
    
    def estimate_execution_time(self, level: ValidationLevel, file_count: int) -> float:
        """Estimate execution time for validation level and file count."""
        
        # Base time per file (seconds)
        base_times = {
            ValidationLevel.MINIMAL: 0.1,
            ValidationLevel.STANDARD: 0.3,
            ValidationLevel.STRICT: 0.5,
        }
        
        # Overhead time (seconds)
        overhead = {
            ValidationLevel.MINIMAL: 2.0,
            ValidationLevel.STANDARD: 3.0, 
            ValidationLevel.STRICT: 5.0,
        }
        
        base_time = base_times[level] * file_count
        total_time = base_time + overhead[level]
        
        return total_time
    
    def validate_performance_target(
        self, 
        level: ValidationLevel, 
        estimated_time: float
    ) -> bool:
        """Check if estimated time meets performance targets."""
        
        targets = {
            ValidationLevel.MINIMAL: 10.0,
            ValidationLevel.STANDARD: 20.0,
            ValidationLevel.STRICT: 30.0,
        }
        
        return estimated_time <= targets[level]


def create_sample_config() -> str:
    """Create a sample .pre-commit-config.yaml for demonstration."""

    generator = PreCommitHookGenerator(Path.cwd())

    return generator.generate_project_config(
        validation_level=ValidationLevel.STANDARD,
        custom_args=["--fix-safe"],
        repo_rev="v1.0.0"
    )


def get_current_version() -> str:
    """Get current Vibe Check version for hook configuration."""
    try:
        from vibe_check import __version__
        return __version__
    except ImportError:
        return "main"  # Fallback to main branch
