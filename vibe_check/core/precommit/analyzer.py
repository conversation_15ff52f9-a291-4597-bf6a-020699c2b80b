"""
Pre-commit Fast Analysis Mode
============================

This module provides optimized analysis specifically designed for pre-commit usage,
focusing on changed files only with performance targets under 30 seconds.

Features:
- Git integration for changed file detection
- Incremental analysis with caching
- Configurable validation levels
- Proper exit code compliance
- Performance monitoring and optimization

Performance Targets:
- Minimal: <10 seconds
- Standard: <20 seconds  
- Strict: <30 seconds
"""

import asyncio
import subprocess
import time
from pathlib import Path
from typing import List, Optional, Set, Dict, Any
from dataclasses import dataclass
from enum import Enum

from vibe_check.core.logging import get_logger
from vibe_check.core.models import AnalysisResult, AnalysisIssue
from .generator import ValidationLevel
from .cache import IncrementalCacheManager

logger = get_logger(__name__)


class PreCommitExitCodes:
    """Exit codes for pre-commit integration compliance."""
    
    SUCCESS = 0          # No violations found
    VIOLATIONS_FOUND = 1 # Quality violations detected
    ANALYSIS_ERROR = 2   # Analysis failed (tool error)
    CONFIG_ERROR = 3     # Configuration issues


@dataclass
class PreCommitConfig:
    """Configuration for pre-commit analysis mode."""
    
    validation_level: ValidationLevel
    project_path: Path
    changed_files_only: bool = True
    use_cache: bool = True
    timeout_seconds: int = 30
    fail_fast: bool = False
    fix_safe_issues: bool = False
    
    @classmethod
    def from_args(cls, args: List[str]) -> 'PreCommitConfig':
        """Create configuration from command line arguments."""
        
        # Parse arguments (simplified for now)
        validation_level = ValidationLevel.STANDARD
        project_path = Path.cwd()
        
        # Extract level from args
        for i, arg in enumerate(args):
            if arg == "--level" and i + 1 < len(args):
                level_str = args[i + 1]
                try:
                    validation_level = ValidationLevel(level_str)
                except ValueError:
                    logger.warning(f"Unknown validation level: {level_str}, using standard")
        
        return cls(
            validation_level=validation_level,
            project_path=project_path,
            fix_safe_issues="--fix-safe" in args,
            fail_fast="--fail-fast" in args,
        )


class GitIntegration:
    """Git integration for detecting changed files."""
    
    def __init__(self, project_path: Path):
        self.project_path = project_path
    
    def get_staged_files(self) -> List[Path]:
        """Get files staged for commit."""
        try:
            result = subprocess.run(
                ["git", "diff", "--cached", "--name-only"],
                cwd=self.project_path,
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode == 0:
                files = [
                    self.project_path / line.strip() 
                    for line in result.stdout.split('\n') 
                    if line.strip()
                ]
                return [f for f in files if f.exists()]
            else:
                logger.warning(f"Git diff failed: {result.stderr}")
                return []
                
        except subprocess.TimeoutExpired:
            logger.warning("Git diff timed out")
            return []
        except Exception as e:
            logger.warning(f"Git integration failed: {e}")
            return []
    
    def get_changed_files(self, base_ref: str = "HEAD") -> List[Path]:
        """Get files changed compared to base reference."""
        try:
            result = subprocess.run(
                ["git", "diff", "--name-only", base_ref],
                cwd=self.project_path,
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode == 0:
                files = [
                    self.project_path / line.strip()
                    for line in result.stdout.split('\n')
                    if line.strip()
                ]
                return [f for f in files if f.exists()]
            else:
                logger.warning(f"Git diff failed: {result.stderr}")
                return []
                
        except Exception as e:
            logger.warning(f"Git integration failed: {e}")
            return []
    
    def is_git_repository(self) -> bool:
        """Check if current directory is a git repository."""
        try:
            result = subprocess.run(
                ["git", "rev-parse", "--git-dir"],
                cwd=self.project_path,
                capture_output=True,
                timeout=2
            )
            return result.returncode == 0
        except Exception:
            return False


class PreCommitAnalyzer:
    """Fast analyzer optimized for pre-commit usage."""
    
    def __init__(self, config: PreCommitConfig):
        self.config = config
        self.git = GitIntegration(config.project_path)
        self.cache = IncrementalCacheManager(config.project_path) if config.use_cache else None
        self.start_time = time.time()
    
    async def analyze_changed_files(self) -> AnalysisResult:
        """Analyze only changed files for pre-commit."""
        
        logger.info(f"Starting pre-commit analysis (level: {self.config.validation_level.value})")
        
        try:
            # Get files to analyze
            files_to_analyze = self._get_files_to_analyze()
            
            if not files_to_analyze:
                logger.info("No files to analyze")
                return AnalysisResult(
                    total_files=0,
                    issues=[],
                    execution_time=time.time() - self.start_time
                )
            
            logger.info(f"Analyzing {len(files_to_analyze)} changed files")
            
            # Perform fast analysis
            result = await self._analyze_files_fast(files_to_analyze)
            
            # Check performance target
            execution_time = time.time() - self.start_time
            self._check_performance_target(execution_time)
            
            return result
            
        except asyncio.TimeoutError:
            logger.error(f"Analysis timed out after {self.config.timeout_seconds} seconds")
            raise
        except Exception as e:
            logger.error(f"Pre-commit analysis failed: {e}")
            raise
    
    def _get_files_to_analyze(self) -> List[Path]:
        """Get list of files to analyze based on configuration."""
        
        if not self.config.changed_files_only:
            # Fallback to all files (not recommended for pre-commit)
            return list(self.config.project_path.rglob("*.py"))
        
        if not self.git.is_git_repository():
            logger.warning("Not a git repository, analyzing all files")
            return list(self.config.project_path.rglob("*.py"))
        
        # Get staged files (primary mode for pre-commit)
        staged_files = self.git.get_staged_files()
        
        if staged_files:
            return self._filter_analyzable_files(staged_files)
        
        # Fallback to changed files
        changed_files = self.git.get_changed_files()
        return self._filter_analyzable_files(changed_files)
    
    def _filter_analyzable_files(self, files: List[Path]) -> List[Path]:
        """Filter files to only those that can be analyzed."""
        
        analyzable_extensions = {'.py', '.js', '.ts', '.yaml', '.yml', '.json'}
        
        return [
            f for f in files 
            if f.suffix.lower() in analyzable_extensions
            and f.is_file()
            and not self._should_skip_file(f)
        ]
    
    def _should_skip_file(self, file_path: Path) -> bool:
        """Check if file should be skipped."""
        
        skip_patterns = {
            '__pycache__',
            '.git',
            'node_modules',
            '.venv',
            'venv',
            '.pytest_cache',
            '.mypy_cache',
        }
        
        return any(pattern in str(file_path) for pattern in skip_patterns)
    
    async def _analyze_files_fast(self, files: List[Path]) -> AnalysisResult:
        """Perform fast analysis on files."""
        
        # Import here to avoid circular imports
        from vibe_check.core.vcs.engine import VCSEngine
        
        # Create lightweight analysis configuration
        analysis_config = self._create_fast_config()
        
        # Use VCS engine for analysis
        engine = VCSEngine()
        
        # Analyze files with timeout
        try:
            result = await asyncio.wait_for(
                engine.analyze_files(files, analysis_config),
                timeout=self.config.timeout_seconds
            )
            
            # Filter results based on validation level
            filtered_result = self._filter_results_by_level(result)
            
            return filtered_result
            
        except asyncio.TimeoutError:
            logger.error("Analysis timed out")
            raise
    
    def _create_fast_config(self) -> Dict[str, Any]:
        """Create optimized configuration for fast analysis."""
        
        # Get rules for validation level
        from .generator import PreCommitHookGenerator
        generator = PreCommitHookGenerator(self.config.project_path)
        rules = generator.get_validation_rules(self.config.validation_level)
        
        return {
            "enabled_rules": rules,
            "fail_fast": self.config.fail_fast,
            "fix_safe_issues": self.config.fix_safe_issues,
            "performance_mode": True,
            "cache_enabled": self.config.use_cache,
        }
    
    def _filter_results_by_level(self, result: AnalysisResult) -> AnalysisResult:
        """Filter analysis results based on validation level."""
        
        if self.config.validation_level == ValidationLevel.MINIMAL:
            # Only critical issues
            critical_issues = [
                issue for issue in result.issues
                if issue.severity in ['error', 'critical']
            ]
            result.issues = critical_issues
        
        elif self.config.validation_level == ValidationLevel.STANDARD:
            # Critical and warning issues
            important_issues = [
                issue for issue in result.issues
                if issue.severity in ['error', 'critical', 'warning']
            ]
            result.issues = important_issues
        
        # Strict level includes all issues (no filtering)
        
        return result
    
    def _check_performance_target(self, execution_time: float) -> None:
        """Check if execution time meets performance targets."""
        
        targets = {
            ValidationLevel.MINIMAL: 10.0,
            ValidationLevel.STANDARD: 20.0,
            ValidationLevel.STRICT: 30.0,
        }
        
        target = targets[self.config.validation_level]
        
        if execution_time > target:
            logger.warning(
                f"Analysis took {execution_time:.2f}s, "
                f"exceeding target of {target}s for {self.config.validation_level.value} level"
            )
        else:
            logger.info(
                f"Analysis completed in {execution_time:.2f}s "
                f"(target: {target}s for {self.config.validation_level.value} level)"
            )
    
    def get_exit_code(self, result: AnalysisResult) -> int:
        """Get appropriate exit code for pre-commit."""
        
        if result.has_errors():
            return PreCommitExitCodes.ANALYSIS_ERROR
        elif result.issues:
            return PreCommitExitCodes.VIOLATIONS_FOUND
        else:
            return PreCommitExitCodes.SUCCESS
