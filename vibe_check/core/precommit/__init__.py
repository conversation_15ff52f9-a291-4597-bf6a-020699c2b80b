"""
Pre-commit Integration Module for Vibe Check
============================================

This module provides comprehensive pre-commit hook integration for Vibe Check,
enabling automated code quality enforcement during development workflows.

Features:
- Fast execution mode with changed file detection
- Configurable validation levels (minimal/standard/strict)
- Seamless installation and configuration generation
- Proper exit code compliance for pre-commit framework
- Incremental caching for performance optimization

Related Files:
- vibe_check/core/precommit/generator.py - Hook configuration generation
- vibe_check/core/precommit/installer.py - Installation and setup
- vibe_check/core/precommit/analyzer.py - Fast analysis mode
- vibe_check/core/precommit/cache.py - Incremental caching system

Dependencies:
- vibe_check.core.constants - Configuration constants
- vibe_check.core.logging - Logging infrastructure
- vibe_check.core.models - Data models
"""

from .analyzer import PreCommitAnalyzer, PreCommitConfig
from .cache import IncrementalCacheManager
from .generator import PreCommitHookGenerator, ValidationLevel
from .installer import PreCommitInstaller, InstallResult

__all__ = [
    "PreCommitAnalyzer",
    "PreCommitConfig", 
    "IncrementalCacheManager",
    "PreCommitHookGenerator",
    "ValidationLevel",
    "PreCommitInstaller",
    "InstallResult",
]
