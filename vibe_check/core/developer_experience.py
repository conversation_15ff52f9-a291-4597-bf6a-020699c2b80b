"""
Developer Experience Enhancement Module
=======================================

Provides enhanced developer experience features including improved error handling,
helpful suggestions, and user-friendly interactions.
"""

import sys
import time
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import click

logger = logging.getLogger(__name__)


@dataclass
class DeveloperTip:
    """A helpful tip for developers."""
    
    title: str
    description: str
    command: Optional[str] = None
    category: str = "general"


@dataclass
class ErrorContext:
    """Context information for enhanced error reporting."""
    
    error_type: str
    user_action: str
    suggested_fixes: List[str]
    related_docs: List[str]
    tips: List[DeveloperTip]


class DeveloperExperienceManager:
    """
    Manages developer experience enhancements.
    
    Provides helpful error messages, suggestions, and tips to improve
    the overall developer experience with Vibe Check.
    """
    
    def __init__(self):
        """Initialize developer experience manager."""
        self.startup_time = time.time()
        self.tips_database = self._load_tips_database()
        
    def _load_tips_database(self) -> List[DeveloperTip]:
        """Load database of helpful developer tips."""
        return [
            DeveloperTip(
                title="Fast Analysis",
                description="Use --vcs-mode for fastest analysis with built-in rules",
                command="vibe-check analyze . --vcs-mode --profile minimal",
                category="performance"
            ),
            DeveloperTip(
                title="Pre-commit Integration",
                description="Generate pre-commit hooks for automated quality checks",
                command="vibe-check hooks generate-hooks --level standard",
                category="workflow"
            ),
            DeveloperTip(
                title="Compare Results",
                description="Compare analysis results to track improvements",
                command="vibe-check compare auto",
                category="analysis"
            ),
            DeveloperTip(
                title="Rule Testing",
                description="Test and validate analysis rules for quality",
                command="vibe-check test-rules validate",
                category="quality"
            ),
            DeveloperTip(
                title="Performance Monitoring",
                description="Monitor analysis performance and optimize settings",
                command="vibe-check performance summary",
                category="performance"
            ),
            DeveloperTip(
                title="Configuration Management",
                description="Migrate legacy configurations to unified format",
                command="vibe-check config migrate legacy-config.yaml",
                category="configuration"
            ),
        ]
    
    def get_startup_performance(self) -> float:
        """Get startup time performance."""
        return time.time() - self.startup_time
    
    def enhance_error_message(self, error: Exception, context: str = "") -> str:
        """
        Enhance error messages with helpful suggestions.
        
        Args:
            error: The original error
            context: Additional context about what the user was trying to do
            
        Returns:
            Enhanced error message with suggestions
        """
        error_type = type(error).__name__
        error_msg = str(error)
        
        # Create enhanced error message
        enhanced_msg = [
            f"❌ {error_type}: {error_msg}",
            ""
        ]
        
        # Add context-specific suggestions
        suggestions = self._get_error_suggestions(error_type, error_msg, context)
        if suggestions:
            enhanced_msg.append("💡 Suggestions:")
            for suggestion in suggestions:
                enhanced_msg.append(f"  • {suggestion}")
            enhanced_msg.append("")
        
        # Add relevant tips
        tips = self._get_relevant_tips(error_type, context)
        if tips:
            enhanced_msg.append("🎯 Helpful Tips:")
            for tip in tips[:2]:  # Limit to 2 tips
                enhanced_msg.append(f"  • {tip.title}: {tip.description}")
                if tip.command:
                    enhanced_msg.append(f"    Command: {tip.command}")
            enhanced_msg.append("")
        
        # Add documentation links
        docs = self._get_relevant_docs(error_type, context)
        if docs:
            enhanced_msg.append("📚 Documentation:")
            for doc in docs:
                enhanced_msg.append(f"  • {doc}")
            enhanced_msg.append("")
        
        return "\n".join(enhanced_msg)
    
    def _get_error_suggestions(self, error_type: str, error_msg: str, context: str) -> List[str]:
        """Get suggestions based on error type and context."""
        suggestions = []
        
        # File not found errors
        if "FileNotFoundError" in error_type or "No such file" in error_msg:
            suggestions.extend([
                "Check if the file path is correct",
                "Ensure the file exists and is readable",
                "Try using absolute path instead of relative path"
            ])
        
        # Permission errors
        elif "PermissionError" in error_type or "Permission denied" in error_msg:
            suggestions.extend([
                "Check file permissions (try chmod 644 for files, 755 for directories)",
                "Ensure you have read access to the directory",
                "Try running with appropriate user permissions"
            ])
        
        # Import errors
        elif "ImportError" in error_type or "ModuleNotFoundError" in error_type:
            suggestions.extend([
                "Install missing dependencies: pip install vibe-check[full]",
                "Check if you're in the correct virtual environment",
                "Try reinstalling Vibe Check: pip install --upgrade vibe-check"
            ])
        
        # Configuration errors
        elif "config" in error_msg.lower() or "configuration" in context.lower():
            suggestions.extend([
                "Check configuration file syntax (YAML format)",
                "Validate configuration: vibe-check config analyze config.yaml",
                "Use configuration migration: vibe-check config migrate"
            ])
        
        # Analysis errors
        elif "analysis" in context.lower():
            suggestions.extend([
                "Try with minimal profile: --profile minimal",
                "Use VCS mode for faster analysis: --vcs-mode",
                "Check if project contains Python files"
            ])
        
        # Database errors
        elif "database" in error_msg.lower() or "sqlite" in error_msg.lower():
            suggestions.extend([
                "Check database file permissions",
                "Try deleting and recreating database",
                "Ensure sufficient disk space"
            ])
        
        return suggestions
    
    def _get_relevant_tips(self, error_type: str, context: str) -> List[DeveloperTip]:
        """Get relevant tips based on error and context."""
        relevant_tips = []
        
        # Performance-related tips
        if "timeout" in context.lower() or "slow" in context.lower():
            relevant_tips.extend([
                tip for tip in self.tips_database 
                if tip.category == "performance"
            ])
        
        # Configuration-related tips
        elif "config" in context.lower():
            relevant_tips.extend([
                tip for tip in self.tips_database 
                if tip.category == "configuration"
            ])
        
        # Analysis-related tips
        elif "analysis" in context.lower():
            relevant_tips.extend([
                tip for tip in self.tips_database 
                if tip.category == "analysis"
            ])
        
        # Default to general tips
        if not relevant_tips:
            relevant_tips = [
                tip for tip in self.tips_database 
                if tip.category == "workflow"
            ]
        
        return relevant_tips[:3]  # Limit to 3 tips
    
    def _get_relevant_docs(self, error_type: str, context: str) -> List[str]:
        """Get relevant documentation links."""
        docs = []
        
        base_url = "https://github.com/ptzajac/vibe_check"
        
        # Add context-specific documentation
        if "config" in context.lower():
            docs.append(f"{base_url}/docs/configuration.md")
        
        if "analysis" in context.lower():
            docs.append(f"{base_url}/docs/analysis.md")
        
        if "install" in context.lower():
            docs.append(f"{base_url}/docs/installation.md")
        
        # Always include general documentation
        docs.extend([
            f"{base_url}/README.md",
            f"{base_url}/issues"
        ])
        
        return docs[:3]  # Limit to 3 docs
    
    def show_welcome_message(self) -> None:
        """Show welcome message with helpful information."""
        startup_time = self.get_startup_performance()
        
        click.echo("🎉 Welcome to Vibe Check!")
        click.echo(f"⚡ Ready in {startup_time:.3f}s")
        click.echo("")
        
        # Show quick start tip
        tip = self.tips_database[0]  # Fast Analysis tip
        click.echo("🚀 Quick Start:")
        click.echo(f"   {tip.command}")
        click.echo("")
        
        click.echo("💡 Need help? Try: vibe-check --help")
        click.echo("📚 Documentation: https://github.com/ptzajac/vibe_check")
    
    def show_completion_summary(self, analysis_time: float, files_analyzed: int, issues_found: int) -> None:
        """Show analysis completion summary with insights."""
        click.echo("")
        click.echo("✅ Analysis Complete!")
        click.echo(f"⏱️  Time: {analysis_time:.2f}s")
        click.echo(f"📁 Files: {files_analyzed}")
        click.echo(f"🔍 Issues: {issues_found}")
        
        # Performance feedback
        if analysis_time < 5:
            click.echo("🚀 Excellent performance!")
        elif analysis_time < 30:
            click.echo("⚡ Good performance!")
        else:
            click.echo("💡 Consider using --vcs-mode for faster analysis")
        
        # Issue feedback
        if issues_found == 0:
            click.echo("🎯 Perfect! No issues found.")
        elif issues_found < 10:
            click.echo("👍 Great! Only a few issues to address.")
        elif issues_found < 100:
            click.echo("📝 Some issues found - good opportunity for improvement.")
        else:
            click.echo("🔧 Many issues found - consider focusing on critical ones first.")
        
        # Show relevant next steps
        click.echo("")
        click.echo("🎯 Next Steps:")
        if issues_found > 0:
            click.echo("  • Review issues and prioritize fixes")
            click.echo("  • Set up pre-commit hooks: vibe-check hooks generate-hooks")
        click.echo("  • Compare with previous runs: vibe-check compare auto")
        click.echo("  • Monitor performance: vibe-check performance summary")
    
    def validate_installation(self) -> Tuple[bool, List[str]]:
        """
        Validate installation and return status with issues.
        
        Returns:
            Tuple of (is_valid, issues_list)
        """
        issues = []
        
        # Check Python version
        if sys.version_info < (3, 8):
            issues.append("Python 3.8+ required (current: {}.{})".format(
                sys.version_info.major, sys.version_info.minor
            ))
        
        # Check core dependencies
        try:
            import click
            import yaml
            import sqlite3
        except ImportError as e:
            issues.append(f"Missing core dependency: {e}")
        
        # Check optional dependencies
        optional_deps = {
            'rich': 'Enhanced terminal output',
            'plotly': 'Visualization features',
            'flask': 'Web interface',
            'cryptography': 'Configuration encryption'
        }
        
        missing_optional = []
        for dep, description in optional_deps.items():
            try:
                __import__(dep)
            except ImportError:
                missing_optional.append(f"{dep} ({description})")
        
        if missing_optional:
            issues.append(f"Optional dependencies missing: {', '.join(missing_optional)}")
            issues.append("Install with: pip install vibe-check[full]")
        
        return len(issues) == 0, issues
    
    def get_random_tip(self, category: Optional[str] = None) -> DeveloperTip:
        """Get a random tip, optionally filtered by category."""
        import random
        
        if category:
            filtered_tips = [tip for tip in self.tips_database if tip.category == category]
            tips = filtered_tips if filtered_tips else self.tips_database
        else:
            tips = self.tips_database
        
        return random.choice(tips)


# Global developer experience manager instance
_dev_experience_manager: Optional[DeveloperExperienceManager] = None


def get_dev_experience_manager() -> DeveloperExperienceManager:
    """Get the global developer experience manager instance."""
    global _dev_experience_manager
    if _dev_experience_manager is None:
        _dev_experience_manager = DeveloperExperienceManager()
    return _dev_experience_manager


def enhance_cli_error(error: Exception, context: str = "") -> str:
    """Enhance CLI error with helpful suggestions."""
    manager = get_dev_experience_manager()
    return manager.enhance_error_message(error, context)


def show_startup_info() -> None:
    """Show startup information and tips."""
    manager = get_dev_experience_manager()
    manager.show_welcome_message()


def show_analysis_summary(analysis_time: float, files_analyzed: int, issues_found: int) -> None:
    """Show analysis completion summary."""
    manager = get_dev_experience_manager()
    manager.show_completion_summary(analysis_time, files_analyzed, issues_found)
