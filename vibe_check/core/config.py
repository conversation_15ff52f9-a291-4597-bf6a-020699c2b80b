"""
Configuration Module
=================

This module provides functions for loading, validating, and managing configuration.
"""

import yaml
import logging
from copy import deepcopy
from pathlib import Path
from typing import Dict, Any, TypeAlias, Optional, Union

from .constants import FileExtensions, ExcludePatterns

# Type aliases for config types
ConfigDict: TypeAlias = Dict[str, Any]

# Set up logger
logger = logging.getLogger("vibe_check_config")

# Import config utilities
from .utils.config_utils import (
    load_config as _load_config,
    merge_configs as _merge_configs,
)

# Import dict_utils for deep_merge


class ConfigError(Exception):
    """Exception raised for configuration errors."""
    def __init__(self, message: str) -> None:
        self.message: str = message
        super().__init__(self.message)


def load_config(config_path: Optional[Union[str, Path]] = None) -> Dict[str, Any]:
    """
    Load configuration from a YAML file.

    Args:
        config_path: Path to the configuration file, or None to use default

    Returns:
        Dictionary containing the configuration

    Raises:
        ConfigError: If the configuration file is invalid or cannot be loaded
    """
    try:
        if config_path is not None:
            path_obj = Path(config_path)
            if not path_obj.exists():
                raise ConfigError(f"Configuration file not found: {config_path}")

        config = _load_config(config_path)

        # Validate the configuration
        validate_config(config)

        return config
    except yaml.YAMLError as e:
        raise ConfigError(f"Invalid YAML in configuration file: {e}")
    except Exception as e:
        if isinstance(e, ConfigError):
            raise
        raise ConfigError(f"Error loading configuration: {e}")


def merge_configs(base_config: Optional[Dict[str, Any]],
                 override_config: Optional[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Merge two configurations, with the override config taking precedence.

    This performs a deep merge of the two dictionaries.

    Args:
        base_config: Base configuration
        override_config: Configuration to override with

    Returns:
        Merged configuration
    """
    if base_config is None:
        return deepcopy(override_config) if override_config is not None else {}

    if override_config is None:
        return deepcopy(base_config)

    return _merge_configs(base_config, override_config)


def get_default_config() -> Dict[str, Any]:
    """
    Get the default configuration.

    Returns:
        Dictionary containing the default configuration
    """
    # Try to find the default config in the package
    default_config_path = Path(__file__).parent.parent / "config" / "default_config.yaml"

    if default_config_path.exists():
        return _load_config(default_config_path)

    # If the default config doesn't exist, return a hardcoded default
    return {
        "file_extensions": FileExtensions.get_python_extensions(),
        "exclude_patterns": ExcludePatterns.get_all_patterns(),
        "analyze_docs": True,
        "tools": {
            "ruff": {
                "enabled": True,
                "args": ["--select=E,F,W,I"]
            },
            "mypy": {
                "enabled": True,
                "args": []
            },
            "bandit": {
                "enabled": True,
                "args": ["-c", "pyproject.toml"]
            },
            "complexity": {
                "enabled": True,
                "threshold": 10
            }
        }
    }


def load_preset(preset_name: str) -> Dict[str, Any]:
    """
    Load a configuration preset.

    Args:
        preset_name: Name of the preset to load

    Returns:
        Dictionary containing the preset configuration

    Raises:
        ConfigError: If the preset is invalid or cannot be loaded
    """
    try:
        # Look for the preset in the presets directory
        preset_path = Path(__file__).parent.parent / "config" / "presets" / f"{preset_name}.yaml"

        logger.debug(f"Looking for preset at: {preset_path}")

        if not preset_path.exists():
            # Try alternative locations
            alt_paths = [
                Path(__file__).parent.parent / "config" / "presets" / f"{preset_name}.yml",
                Path(__file__).parent.parent / "config" / f"{preset_name}.yaml",
                Path(__file__).parent.parent / "config" / f"{preset_name}.yml",
            ]

            for alt_path in alt_paths:
                logger.debug(f"Trying alternative path: {alt_path}")
                if alt_path.exists():
                    preset_path = alt_path
                    break
            else:
                # If we get here, none of the paths exist
                raise ConfigError(f"Preset not found: {preset_name}")

        # Load the preset
        logger.debug(f"Loading preset from: {preset_path}")
        with open(preset_path, 'r') as f:
            preset_config_raw = yaml.safe_load(f)

        if preset_config_raw is None:
            preset_config: Dict[str, Any] = {}
        elif not isinstance(preset_config_raw, dict):
            raise ConfigError(f"Preset must be a dictionary, got {type(preset_config_raw)}")
        else:
            preset_config = preset_config_raw

        # Validate the preset
        validate_config(preset_config)

        return preset_config
    except yaml.YAMLError as e:
        raise ConfigError(f"Invalid YAML in preset file: {e}")
    except Exception as e:
        if isinstance(e, ConfigError):
            raise
        raise ConfigError(f"Error loading preset: {e}")


def validate_config(config: Dict[str, Any]) -> None:
    """
    Validate a configuration.

    Args:
        config: Configuration to validate

    Raises:
        ConfigError: If the configuration is invalid
    """
    # Validate file_extensions
    if "file_extensions" in config and not isinstance(config["file_extensions"], list):
        raise ConfigError("file_extensions must be a list")

    # Validate exclude_patterns
    if "exclude_patterns" in config and not isinstance(config["exclude_patterns"], list):
        raise ConfigError("exclude_patterns must be a list")

    # Validate analyze_docs
    if "analyze_docs" in config and not isinstance(config["analyze_docs"], bool):
        raise ConfigError("analyze_docs must be a boolean")

    # Validate tools
    if "tools" in config:
        if not isinstance(config["tools"], dict):
            raise ConfigError("tools must be a dictionary")

        for tool_name, tool_config in config["tools"].items():
            if not isinstance(tool_config, dict):
                raise ConfigError(f"Tool configuration for {tool_name} must be a dictionary")

            if "enabled" in tool_config and not isinstance(tool_config["enabled"], bool):
                raise ConfigError(f"enabled for tool {tool_name} must be a boolean")

            if "args" in tool_config and not isinstance(tool_config["args"], list):
                raise ConfigError(f"args for tool {tool_name} must be a list")


def save_config(config: Dict[str, Any], config_path: Union[str, Path]) -> None:
    """
    Save configuration to a YAML file.

    Args:
        config: Configuration dictionary to save
        config_path: Path to save the configuration file

    Raises:
        ConfigError: If there's an error saving the configuration
    """
    try:
        config_path = Path(config_path)
        config_path.parent.mkdir(parents=True, exist_ok=True)

        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, sort_keys=True)

        logger.info(f"Configuration saved to {config_path}")

    except Exception as e:
        raise ConfigError(f"Failed to save configuration to {config_path}: {e}")


def get_default_config() -> Dict[str, Any]:
    """
    Get default configuration.

    Returns:
        Default configuration dictionary
    """
    return {
        "file_extensions": [FileExtensions.PYTHON],
        "exclude_patterns": ExcludePatterns.get_all_patterns(),
        "analyze_docs": True,
        "tools": {
            "unified_analyzer": {
                "enabled": True,
                "max_workers": 4,
                "use_async": True,
                "enable_caching": True
            }
        }
    }
