"""
Preset Data Models
=================

Type-safe data models for Vibe Check preset system using TypedDict for
backward compatibility and schema validation.

These models define the structure for user-defined analysis presets,
building upon the existing configuration architecture.
"""

from typing import TypedDict, Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum

from ..constants import AnalysisProfile, SeverityLevel


class PresetMetadata(TypedDict):
    """Metadata for preset identification and versioning."""
    
    name: str                           # Unique preset name
    description: str                    # Human-readable description
    version: str                        # Preset version (semantic versioning)
    author: Optional[str]               # Preset author
    created_at: str                     # ISO format timestamp
    updated_at: str                     # ISO format timestamp
    tags: Optional[List[str]]           # Searchable tags
    compatibility_version: str          # Minimum Vibe Check version required


class RuleConfiguration(TypedDict, total=False):
    """Rule-specific configuration options."""
    
    # Rule selection
    rule_ids: Optional[List[str]]           # Specific rules to include
    exclude_rules: Optional[List[str]]      # Specific rules to exclude
    categories: Optional[List[str]]         # Rule categories to include
    exclude_categories: Optional[List[str]] # Rule categories to exclude
    
    # Rule behavior
    severity_filter: Optional[str]          # Minimum severity level
    auto_fix: Optional[bool]                # Enable auto-fix where available
    
    # Rule-specific configurations
    rule_configs: Optional[Dict[str, Dict[str, Any]]]  # Per-rule configuration
    
    # VCS-specific settings
    vcs_rules_only: Optional[bool]          # Use only VCS built-in rules
    enable_framework_rules: Optional[bool]  # Enable framework-specific rules


class ExternalToolConfiguration(TypedDict, total=False):
    """External tool configuration."""
    
    # Tool selection
    tools: Optional[List[str]]              # External tools to include
    exclude_tools: Optional[List[str]]      # External tools to exclude
    
    # Tool-specific configurations
    tool_configs: Optional[Dict[str, Dict[str, Any]]]  # Per-tool configuration
    
    # Tool behavior
    parallel_execution: Optional[bool]      # Run tools in parallel
    timeout_seconds: Optional[int]          # Tool execution timeout
    fail_fast: Optional[bool]               # Stop on first tool failure


class AnalysisConfiguration(TypedDict, total=False):
    """Analysis behavior configuration."""
    
    # Analysis profile
    profile: AnalysisProfile                # Analysis profile (minimal/standard/comprehensive)
    
    # Analysis modes
    vcs_mode: Optional[bool]                # Use VCS engine
    fast_mode: Optional[bool]               # Fast mode for pre-commit
    detailed: Optional[bool]                # Detailed output
    quiet: Optional[bool]                   # Quiet mode
    
    # Output options
    no_save: Optional[bool]                 # Skip database storage
    output_formats: Optional[List[str]]     # Output formats (json, markdown, html)
    
    # Performance settings
    max_workers: Optional[int]              # Maximum parallel workers
    timeout_seconds: Optional[int]          # Analysis timeout
    
    # File filtering
    file_extensions: Optional[List[str]]    # File extensions to analyze
    exclude_patterns: Optional[List[str]]   # Patterns to exclude
    use_gitignore: Optional[bool]           # Use .gitignore patterns


class PresetDataModel(TypedDict):
    """Complete preset data structure."""
    
    metadata: PresetMetadata                # Preset metadata
    rule_config: RuleConfiguration          # Rule configuration
    tool_config: ExternalToolConfiguration  # External tool configuration
    analysis_config: AnalysisConfiguration  # Analysis configuration
    
    # Advanced features
    inheritance: Optional[str]              # Base preset name for inheritance
    overrides: Optional[Dict[str, Any]]     # Configuration overrides
    
    # Validation
    schema_version: str                     # Preset schema version


# Utility types for validation and processing
PresetDict = Dict[str, Any]
PresetValidationResult = Dict[str, Union[bool, List[str], Dict[str, Any]]]


class PresetType(Enum):
    """Types of presets for categorization."""
    
    BUILTIN = "builtin"         # Built-in system presets
    USER = "user"               # User-created presets
    SHARED = "shared"           # Imported shared presets
    TEMPLATE = "template"       # Template presets for common use cases


class PresetStatus(Enum):
    """Preset validation and compatibility status."""
    
    VALID = "valid"             # Preset is valid and compatible
    INVALID = "invalid"         # Preset has validation errors
    OUTDATED = "outdated"       # Preset needs updates for compatibility
    DEPRECATED = "deprecated"   # Preset is deprecated but still usable
