"""
Preset System Exceptions
========================

Custom exceptions for the Vibe Check preset management system.
Provides specific error types for different preset-related failures.
"""

from typing import List, Optional, Dict, Any


class PresetError(Exception):
    """Base exception for preset-related errors."""
    
    def __init__(self, message: str, preset_name: Optional[str] = None, 
                 details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.preset_name = preset_name
        self.details = details or {}
    
    def __str__(self) -> str:
        if self.preset_name:
            return f"Preset '{self.preset_name}': {super().__str__()}"
        return super().__str__()


class PresetNotFoundError(PresetError):
    """Raised when a requested preset cannot be found."""
    
    def __init__(self, preset_name: str, search_paths: Optional[List[str]] = None):
        message = f"Preset not found: {preset_name}"
        if search_paths:
            message += f" (searched: {', '.join(search_paths)})"
        
        super().__init__(message, preset_name)
        self.search_paths = search_paths or []


class PresetValidationError(PresetError):
    """Raised when preset validation fails."""
    
    def __init__(self, preset_name: str, validation_errors: List[str], 
                 warnings: Optional[List[str]] = None):
        message = f"Preset validation failed with {len(validation_errors)} error(s)"
        
        super().__init__(message, preset_name)
        self.validation_errors = validation_errors
        self.warnings = warnings or []
    
    def get_detailed_message(self) -> str:
        """Get detailed validation error message."""
        lines = [f"Preset '{self.preset_name}' validation failed:"]
        
        if self.validation_errors:
            lines.append("\nErrors:")
            for error in self.validation_errors:
                lines.append(f"  - {error}")
        
        if self.warnings:
            lines.append("\nWarnings:")
            for warning in self.warnings:
                lines.append(f"  - {warning}")
        
        return "\n".join(lines)


class PresetConflictError(PresetError):
    """Raised when preset configuration conflicts are detected."""
    
    def __init__(self, preset_name: str, conflicts: List[str]):
        message = f"Configuration conflicts detected in preset"
        
        super().__init__(message, preset_name)
        self.conflicts = conflicts
    
    def get_detailed_message(self) -> str:
        """Get detailed conflict error message."""
        lines = [f"Preset '{self.preset_name}' has configuration conflicts:"]
        
        for conflict in self.conflicts:
            lines.append(f"  - {conflict}")
        
        lines.append("\nPlease resolve these conflicts before using the preset.")
        return "\n".join(lines)


class PresetInheritanceError(PresetError):
    """Raised when preset inheritance fails."""
    
    def __init__(self, preset_name: str, base_preset: str, error_message: str):
        message = f"Failed to inherit from base preset '{base_preset}': {error_message}"
        
        super().__init__(message, preset_name)
        self.base_preset = base_preset
        self.inheritance_error = error_message


class PresetSchemaError(PresetError):
    """Raised when preset schema validation fails."""
    
    def __init__(self, preset_name: str, schema_errors: List[str]):
        message = f"Preset schema validation failed"
        
        super().__init__(message, preset_name)
        self.schema_errors = schema_errors
    
    def get_detailed_message(self) -> str:
        """Get detailed schema error message."""
        lines = [f"Preset '{self.preset_name}' schema validation failed:"]
        
        for error in self.schema_errors:
            lines.append(f"  - {error}")
        
        return "\n".join(lines)


class PresetCompatibilityError(PresetError):
    """Raised when preset is incompatible with current Vibe Check version."""
    
    def __init__(self, preset_name: str, required_version: str, current_version: str):
        message = (f"Preset requires Vibe Check version {required_version} "
                  f"but current version is {current_version}")
        
        super().__init__(message, preset_name)
        self.required_version = required_version
        self.current_version = current_version


class PresetIOError(PresetError):
    """Raised when preset file I/O operations fail."""
    
    def __init__(self, preset_name: str, operation: str, file_path: str, 
                 original_error: Exception):
        message = f"Failed to {operation} preset file: {file_path}"
        
        super().__init__(message, preset_name)
        self.operation = operation
        self.file_path = file_path
        self.original_error = original_error
