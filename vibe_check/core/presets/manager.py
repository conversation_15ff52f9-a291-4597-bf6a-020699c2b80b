"""
Preset Manager
==============

Main interface for preset management operations including CRUD operations,
validation, inheritance, and file-based storage.
"""

import json
import yaml
import asyncio
from typing import Dict, List, Optional, Any, Union, Set
from pathlib import Path
from datetime import datetime, timezone
import logging

from ..constants import DefaultPaths
from ..version import __version__
from ..utils.dict_utils import deep_merge
from .models import PresetDataModel, PresetMetadata, PresetType, PresetStatus
from .validator import PresetValidator
from .exceptions import (
    PresetError, PresetNotFoundError, PresetValidationError,
    PresetIOError, PresetInheritanceError
)

logger = logging.getLogger(__name__)


class PresetManager:
    """
    Comprehensive preset management system.
    
    Provides CRUD operations, validation, inheritance, and storage
    for Vibe Check analysis presets.
    """
    
    def __init__(self, user_presets_dir: Optional[Path] = None):
        """
        Initialize preset manager.
        
        Args:
            user_presets_dir: Custom directory for user presets
        """
        self.validator = PresetValidator()
        
        # Setup preset directories
        self.builtin_presets_dir = Path(__file__).parent.parent.parent / "config" / "presets"
        self.user_presets_dir = user_presets_dir or self._get_default_user_presets_dir()
        
        # Ensure user presets directory exists
        self.user_presets_dir.mkdir(parents=True, exist_ok=True)
        
        # Cache for loaded presets
        self._preset_cache: Dict[str, PresetDataModel] = {}
        self._cache_timestamps: Dict[str, float] = {}
    
    def _get_default_user_presets_dir(self) -> Path:
        """Get default user presets directory."""
        # Use XDG config directory or fallback to home
        config_home = Path.home() / ".config" / "vibe-check" / "presets"
        return config_home
    
    async def list_presets(self, preset_type: Optional[PresetType] = None) -> Dict[str, Dict[str, Any]]:
        """
        List all available presets with metadata.
        
        Args:
            preset_type: Filter by preset type (builtin/user/shared)
            
        Returns:
            Dictionary mapping preset names to metadata
        """
        presets = {}
        
        # List builtin presets
        if preset_type is None or preset_type == PresetType.BUILTIN:
            builtin_presets = await self._list_presets_in_directory(
                self.builtin_presets_dir, PresetType.BUILTIN
            )
            presets.update(builtin_presets)
        
        # List user presets
        if preset_type is None or preset_type == PresetType.USER:
            user_presets = await self._list_presets_in_directory(
                self.user_presets_dir, PresetType.USER
            )
            presets.update(user_presets)
        
        return presets
    
    async def _list_presets_in_directory(self, directory: Path, 
                                       preset_type: PresetType) -> Dict[str, Dict[str, Any]]:
        """List presets in a specific directory."""
        presets: Dict[str, Dict[str, Any]] = {}
        
        if not directory.exists():
            return presets
        
        for preset_file in directory.glob("*.yaml"):
            try:
                preset_name = preset_file.stem
                
                # For builtin presets, convert to new format
                if preset_type == PresetType.BUILTIN:
                    metadata = await self._extract_builtin_metadata(preset_file)
                else:
                    # Load user preset metadata
                    preset_data = await self._load_preset_file(preset_file)
                    metadata = preset_data.get("metadata", {})
                
                presets[preset_name] = {
                    "type": preset_type.value,
                    "file_path": str(preset_file),
                    "metadata": metadata,
                    "status": PresetStatus.VALID.value  # TODO: Add validation check
                }
                
            except Exception as e:
                logger.warning(f"Failed to load preset {preset_file}: {e}")
                presets[preset_file.stem] = {
                    "type": preset_type.value,
                    "file_path": str(preset_file),
                    "metadata": {},
                    "status": PresetStatus.INVALID.value,
                    "error": str(e)
                }
        
        return presets
    
    async def _extract_builtin_metadata(self, preset_file: Path) -> Dict[str, Any]:
        """Extract metadata from builtin preset file."""
        # For builtin presets, create metadata from file content
        return {
            "name": preset_file.stem,
            "description": f"Built-in {preset_file.stem} preset",
            "version": "1.0.0",
            "author": "Vibe Check",
            "created_at": datetime.now(timezone.utc).isoformat(),
            "updated_at": datetime.now(timezone.utc).isoformat(),
            "tags": ["builtin"],
            "compatibility_version": __version__
        }
    
    async def load_preset(self, preset_name: str, 
                         use_cache: bool = True) -> PresetDataModel:
        """
        Load a preset by name.
        
        Args:
            preset_name: Name of the preset to load
            use_cache: Whether to use cached version if available
            
        Returns:
            Loaded preset data
            
        Raises:
            PresetNotFoundError: If preset is not found
            PresetValidationError: If preset validation fails
        """
        # Check cache first
        if use_cache and preset_name in self._preset_cache:
            cache_time = self._cache_timestamps.get(preset_name, 0)
            if datetime.now().timestamp() - cache_time < 300:  # 5 minute cache
                return self._preset_cache[preset_name]
        
        # Find preset file
        preset_file = await self._find_preset_file(preset_name)
        if not preset_file:
            raise PresetNotFoundError(preset_name, self._get_search_paths())
        
        # Load preset data
        try:
            if preset_file.parent == self.builtin_presets_dir:
                # Convert builtin preset to new format
                preset_data = await self._convert_builtin_preset(preset_file)
            else:
                # Load user preset
                preset_data = await self._load_preset_file(preset_file)
            
            # Validate preset
            validation_result = self.validator.validate_preset(preset_data, preset_name)
            if not validation_result["valid"]:
                raise PresetValidationError(
                    preset_name, 
                    validation_result["errors"], 
                    validation_result.get("warnings", [])
                )
            
            # Handle inheritance
            if preset_data.get("inheritance"):
                preset_data = await self._resolve_inheritance(preset_data)
            
            # Cache the result
            self._preset_cache[preset_name] = preset_data
            self._cache_timestamps[preset_name] = datetime.now().timestamp()
            
            return preset_data
            
        except Exception as e:
            if isinstance(e, (PresetNotFoundError, PresetValidationError)):
                raise
            raise PresetIOError(preset_name, "load", str(preset_file), e)
    
    async def _find_preset_file(self, preset_name: str) -> Optional[Path]:
        """Find preset file in search paths."""
        search_paths = [
            self.user_presets_dir / f"{preset_name}.yaml",
            self.builtin_presets_dir / f"{preset_name}.yaml",
        ]
        
        for path in search_paths:
            if path.exists():
                return path
        
        return None
    
    def _get_search_paths(self) -> List[str]:
        """Get list of preset search paths."""
        return [
            str(self.user_presets_dir),
            str(self.builtin_presets_dir)
        ]
    
    async def _load_preset_file(self, preset_file: Path) -> PresetDataModel:
        """Load preset data from file."""
        try:
            with open(preset_file, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            
            if not isinstance(data, dict):
                raise ValueError("Preset file must contain a YAML dictionary")
            
            return data
            
        except yaml.YAMLError as e:
            raise ValueError(f"Invalid YAML in preset file: {e}")
        except Exception as e:
            raise ValueError(f"Failed to load preset file: {e}")
    
    async def _convert_builtin_preset(self, preset_file: Path) -> PresetDataModel:
        """Convert builtin preset to new format."""
        # Load legacy preset
        legacy_data = await self._load_preset_file(preset_file)
        
        # Convert to new format
        preset_data: PresetDataModel = {
            "metadata": await self._extract_builtin_metadata(preset_file),
            "rule_config": {
                "categories": None,  # Will be determined from tools
                "exclude_categories": None,
                "severity_filter": None,
                "auto_fix": False,
                "vcs_rules_only": False,
                "enable_framework_rules": True
            },
            "tool_config": {
                "tools": list(legacy_data.get("tools", {}).keys()),
                "exclude_tools": None,
                "tool_configs": legacy_data.get("tools", {}),
                "parallel_execution": legacy_data.get("performance", {}).get("parallel", True),
                "timeout_seconds": legacy_data.get("performance", {}).get("timeout", 60)
            },
            "analysis_config": {
                "profile": "standard",  # Default profile
                "vcs_mode": False,  # Legacy presets use external tools
                "fast_mode": False,
                "detailed": False,
                "quiet": False,
                "no_save": False,
                "output_formats": legacy_data.get("reporting", {}).get("formats", ["markdown"]),
                "max_workers": legacy_data.get("performance", {}).get("max_workers", 4),
                "timeout_seconds": legacy_data.get("performance", {}).get("timeout", 60),
                "file_extensions": legacy_data.get("file_extensions", [".py"]),
                "exclude_patterns": legacy_data.get("exclude_patterns", []),
                "use_gitignore": legacy_data.get("use_gitignore", True)
            },
            "inheritance": None,
            "overrides": {},
            "schema_version": self.validator.CURRENT_SCHEMA_VERSION
        }
        
        return preset_data
    
    async def _resolve_inheritance(self, preset_data: PresetDataModel) -> PresetDataModel:
        """Resolve preset inheritance with advanced conflict resolution."""
        base_preset_name = preset_data["inheritance"]
        if not base_preset_name:
            return preset_data

        try:
            # Load base preset (avoid infinite recursion)
            base_preset = await self.load_preset(base_preset_name, use_cache=True)

            # Remove inheritance from base to avoid cycles
            base_preset = base_preset.copy()
            base_preset["inheritance"] = None

            # Advanced inheritance resolution with conflict detection
            merged_preset = await self._advanced_merge_presets(base_preset, preset_data)

            # Clear inheritance after merging
            merged_preset["inheritance"] = None

            # Validate merged preset for conflicts
            conflicts = await self._detect_inheritance_conflicts(base_preset, preset_data, merged_preset)
            if conflicts:
                logger.warning(f"Inheritance conflicts detected in preset '{preset_data.get('metadata', {}).get('name', 'unknown')}': {conflicts}")

            return merged_preset

        except Exception as e:
            raise PresetInheritanceError(
                preset_data.get("metadata", {}).get("name", "unknown"),
                base_preset_name,
                str(e)
            )

    async def _advanced_merge_presets(self, base_preset: PresetDataModel,
                                    derived_preset: PresetDataModel) -> PresetDataModel:
        """Advanced preset merging with intelligent conflict resolution."""
        merged_preset = deep_merge(base_preset, derived_preset)

        # Handle special merge cases for rule configurations
        base_rule_config = base_preset.get("rule_config", {})
        derived_rule_config = derived_preset.get("rule_config", {})

        # Merge categories intelligently (union of both)
        base_categories = set(base_rule_config.get("categories", []) or [])
        derived_categories = set(derived_rule_config.get("categories", []) or [])
        if base_categories and derived_categories:
            merged_preset["rule_config"]["categories"] = list(base_categories | derived_categories)

        # Handle exclude categories (derived takes precedence but warns on conflicts)
        base_exclude = set(base_rule_config.get("exclude_categories", []) or [])
        derived_exclude = set(derived_rule_config.get("exclude_categories", []) or [])
        if base_exclude and derived_exclude:
            merged_preset["rule_config"]["exclude_categories"] = list(base_exclude | derived_exclude)

        # Merge tools intelligently (union of both)
        base_tool_config = base_preset.get("tool_config", {})
        derived_tool_config = derived_preset.get("tool_config", {})

        base_tools = set(base_tool_config.get("tools", []) or [])
        derived_tools = set(derived_tool_config.get("tools", []) or [])
        if base_tools and derived_tools:
            merged_preset["tool_config"]["tools"] = list(base_tools | derived_tools)

        # Handle tool exclusions
        base_exclude_tools = set(base_tool_config.get("exclude_tools", []) or [])
        derived_exclude_tools = set(derived_tool_config.get("exclude_tools", []) or [])
        if base_exclude_tools and derived_exclude_tools:
            merged_preset["tool_config"]["exclude_tools"] = list(base_exclude_tools | derived_exclude_tools)

        return merged_preset

    async def _detect_inheritance_conflicts(self, base_preset: PresetDataModel,
                                          derived_preset: PresetDataModel,
                                          merged_preset: PresetDataModel) -> List[str]:
        """Detect potential conflicts in preset inheritance."""
        conflicts = []

        # Check for category conflicts (included vs excluded)
        rule_config = merged_preset.get("rule_config", {})
        categories = set(rule_config.get("categories", []) or [])
        exclude_categories = set(rule_config.get("exclude_categories", []) or [])

        category_conflicts = categories & exclude_categories
        if category_conflicts:
            conflicts.append(f"Categories both included and excluded: {', '.join(category_conflicts)}")

        # Check for tool conflicts (included vs excluded)
        tool_config = merged_preset.get("tool_config", {})
        tools = set(tool_config.get("tools", []) or [])
        exclude_tools = set(tool_config.get("exclude_tools", []) or [])

        tool_conflicts = tools & exclude_tools
        if tool_conflicts:
            conflicts.append(f"Tools both included and excluded: {', '.join(tool_conflicts)}")

        # Check for analysis mode conflicts
        analysis_config = merged_preset.get("analysis_config", {})
        vcs_mode = analysis_config.get("vcs_mode", False)
        fast_mode = analysis_config.get("fast_mode", False)
        detailed = analysis_config.get("detailed", False)

        if fast_mode and detailed:
            conflicts.append("Fast mode and detailed output are conflicting options")

        if vcs_mode and tool_config.get("tools"):
            conflicts.append("VCS mode with external tools may cause performance issues")

        return conflicts

    async def save_preset(self, preset_data: PresetDataModel,
                         overwrite: bool = False) -> Path:
        """
        Save a preset to user presets directory.

        Args:
            preset_data: Preset data to save
            overwrite: Whether to overwrite existing preset

        Returns:
            Path to saved preset file

        Raises:
            PresetValidationError: If preset validation fails
            PresetError: If preset already exists and overwrite=False
            PresetIOError: If file save operation fails
        """
        # Validate preset first
        preset_name = preset_data["metadata"]["name"]
        validation_result = self.validator.validate_preset(preset_data, preset_name)

        if not validation_result["valid"]:
            errors = validation_result["errors"]
            warnings = validation_result.get("warnings", [])
            if isinstance(errors, list) and isinstance(warnings, list):
                raise PresetValidationError(preset_name, errors, warnings)

        # Check if preset already exists
        preset_file = self.user_presets_dir / f"{preset_name}.yaml"
        if preset_file.exists() and not overwrite:
            raise PresetError(f"Preset '{preset_name}' already exists. Use overwrite=True to replace it.")

        # Update timestamps
        now = datetime.now(timezone.utc).isoformat()
        if not preset_data["metadata"].get("created_at"):
            preset_data["metadata"]["created_at"] = now
        preset_data["metadata"]["updated_at"] = now

        # Ensure schema version is set
        preset_data["schema_version"] = self.validator.CURRENT_SCHEMA_VERSION

        # Save to file
        try:
            with open(preset_file, 'w', encoding='utf-8') as f:
                yaml.dump(preset_data, f, default_flow_style=False, sort_keys=False)

            # Update cache
            self._preset_cache[preset_name] = preset_data
            self._cache_timestamps[preset_name] = datetime.now().timestamp()

            logger.info(f"Saved preset '{preset_name}' to {preset_file}")
            return preset_file

        except Exception as e:
            raise PresetIOError(preset_name, "save", str(preset_file), e)

    async def delete_preset(self, preset_name: str,
                           force: bool = False) -> bool:
        """
        Delete a user preset.

        Args:
            preset_name: Name of preset to delete
            force: Force deletion without confirmation

        Returns:
            True if preset was deleted, False if not found

        Raises:
            PresetError: If trying to delete builtin preset
            PresetIOError: If file deletion fails
        """
        # Find preset file
        preset_file = await self._find_preset_file(preset_name)
        if not preset_file:
            return False

        # Prevent deletion of builtin presets
        if preset_file.parent == self.builtin_presets_dir:
            raise PresetError(f"Cannot delete builtin preset '{preset_name}'")

        # Delete file
        try:
            preset_file.unlink()

            # Remove from cache
            self._preset_cache.pop(preset_name, None)
            self._cache_timestamps.pop(preset_name, None)

            logger.info(f"Deleted preset '{preset_name}' from {preset_file}")
            return True

        except Exception as e:
            raise PresetIOError(preset_name, "delete", str(preset_file), e)

    async def validate_preset_file(self, preset_file: Path) -> Dict[str, Any]:
        """
        Validate a preset file without loading it into the system.

        Args:
            preset_file: Path to preset file

        Returns:
            Validation result dictionary
        """
        try:
            preset_data = await self._load_preset_file(preset_file)
            preset_name = preset_data.get("metadata", {}).get("name", preset_file.stem)

            return self.validator.validate_preset(preset_data, preset_name)

        except Exception as e:
            return {
                "valid": False,
                "status": PresetStatus.INVALID.value,
                "errors": [f"Failed to load preset file: {e}"],
                "warnings": [],
                "preset_name": preset_file.stem
            }

    async def export_preset(self, preset_name: str,
                           output_file: Path,
                           format: str = "yaml") -> Path:
        """
        Export a preset to a file.

        Args:
            preset_name: Name of preset to export
            output_file: Output file path
            format: Export format (yaml or json)

        Returns:
            Path to exported file

        Raises:
            PresetNotFoundError: If preset is not found
            PresetIOError: If export fails
        """
        # Load preset
        preset_data = await self.load_preset(preset_name)

        # Export to file
        try:
            output_file.parent.mkdir(parents=True, exist_ok=True)

            if format.lower() == "json":
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(preset_data, f, indent=2, default=str)
            else:  # yaml
                with open(output_file, 'w', encoding='utf-8') as f:
                    yaml.dump(preset_data, f, default_flow_style=False, sort_keys=False)

            logger.info(f"Exported preset '{preset_name}' to {output_file}")
            return output_file

        except Exception as e:
            raise PresetIOError(preset_name, "export", str(output_file), e)

    async def import_preset(self, input_file: Path,
                           overwrite: bool = False) -> str:
        """
        Import a preset from a file.

        Args:
            input_file: Input file path
            overwrite: Whether to overwrite existing preset

        Returns:
            Name of imported preset

        Raises:
            PresetIOError: If import fails
            PresetValidationError: If imported preset is invalid
        """
        try:
            # Load preset data
            if input_file.suffix.lower() == '.json':
                with open(input_file, 'r', encoding='utf-8') as f:
                    preset_data = json.load(f)
            else:  # yaml
                preset_data = await self._load_preset_file(input_file)

            # Save as user preset
            preset_file = await self.save_preset(preset_data, overwrite=overwrite)
            preset_name = str(preset_data["metadata"]["name"])

            logger.info(f"Imported preset '{preset_name}' from {input_file}")
            return preset_name

        except Exception as e:
            if isinstance(e, (PresetValidationError, PresetError)):
                raise
            raise PresetIOError("unknown", "import", str(input_file), e)

    def clear_cache(self) -> None:
        """Clear preset cache."""
        self._preset_cache.clear()
        self._cache_timestamps.clear()
        logger.debug("Cleared preset cache")
