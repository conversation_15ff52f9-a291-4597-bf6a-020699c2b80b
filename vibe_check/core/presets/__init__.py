"""
Vibe Check Preset Management System
==================================

This module provides comprehensive preset management capabilities for Vibe Check,
enabling users to create, save, load, and share custom analysis configurations.

The preset system builds upon the existing configuration architecture and provides:
- Type-safe preset definitions with schema validation
- CRUD operations for preset management
- Integration with VCS engine and CLI
- Export/import capabilities for sharing
- Preset inheritance and merging
- Validation framework for preset quality

Classes:
    PresetMetadata: Metadata for preset identification and versioning
    RuleConfiguration: Rule-specific configuration options
    ExternalToolConfiguration: External tool configuration
    AnalysisConfiguration: Analysis behavior configuration
    PresetDataModel: Complete preset data structure
    PresetManager: Main preset management interface
    PresetValidator: Validation framework for presets

Usage:
    from vibe_check.core.presets import PresetManager, PresetDataModel
    
    # Create preset manager
    manager = PresetManager()
    
    # Load existing preset
    preset = await manager.load_preset("standard")
    
    # Create new preset
    new_preset = PresetDataModel(
        metadata=PresetMetadata(name="my-preset", description="Custom preset"),
        rule_config=RuleConfiguration(categories=["style", "security"]),
        tool_config=ExternalToolConfiguration(tools=["ruff", "mypy"]),
        analysis_config=AnalysisConfiguration(vcs_mode=True, detailed=True)
    )
    
    # Save preset
    await manager.save_preset(new_preset)
"""

from .models import (
    PresetMetadata,
    RuleConfiguration,
    ExternalToolConfiguration,
    AnalysisConfiguration,
    PresetDataModel
)

from .manager import PresetManager
from .validator import PresetValidator
from .templates import PresetTemplateManager
from .dynamic_generator import DynamicPresetGenerator, ProjectAnalyzer
from .exceptions import PresetError, PresetNotFoundError, PresetValidationError

__all__ = [
    # Data models
    "PresetMetadata",
    "RuleConfiguration",
    "ExternalToolConfiguration",
    "AnalysisConfiguration",
    "PresetDataModel",

    # Management classes
    "PresetManager",
    "PresetValidator",
    "PresetTemplateManager",
    "DynamicPresetGenerator",
    "ProjectAnalyzer",

    # Exceptions
    "PresetError",
    "PresetNotFoundError",
    "PresetValidationError"
]
