"""
Dynamic Preset Generation
=========================

Context-aware preset generation based on project characteristics,
file analysis, and development patterns.
"""

import asyncio
from typing import Dict, List, Optional, Set, Tuple
from pathlib import Path
from datetime import datetime, timezone

from .models import PresetDataModel, PresetMetadata, RuleConfiguration, ExternalToolConfiguration, AnalysisConfiguration
from .templates import PresetTemplateManager
from ..constants import AnalysisProfile
from ..version import __version__


class ProjectAnalyzer:
    """Analyzes project characteristics for dynamic preset generation."""
    
    def __init__(self, project_path: Path):
        """Initialize project analyzer."""
        self.project_path = project_path
        self.template_manager = PresetTemplateManager()
    
    async def analyze_project(self) -> Dict[str, any]:
        """Analyze project to determine characteristics."""
        analysis = {
            "frameworks": await self._detect_frameworks(),
            "project_type": await self._determine_project_type(),
            "file_patterns": await self._analyze_file_patterns(),
            "dependencies": await self._analyze_dependencies(),
            "complexity": await self._assess_complexity(),
            "security_needs": await self._assess_security_needs(),
            "performance_focus": await self._assess_performance_focus()
        }
        
        return analysis
    
    async def _detect_frameworks(self) -> List[str]:
        """Detect web frameworks and libraries in use."""
        frameworks = []
        
        # Check for common framework files and patterns
        framework_indicators = {
            "django": ["manage.py", "settings.py", "urls.py", "wsgi.py"],
            "flask": ["app.py", "application.py", "run.py"],
            "fastapi": ["main.py", "app.py"],
            "jupyter": ["*.ipynb"],
            "pytest": ["conftest.py", "test_*.py", "*_test.py"],
            "sphinx": ["conf.py", "index.rst"],
            "setuptools": ["setup.py", "setup.cfg", "pyproject.toml"]
        }
        
        for framework, indicators in framework_indicators.items():
            if await self._check_indicators(indicators):
                frameworks.append(framework)
        
        return frameworks
    
    async def _check_indicators(self, indicators: List[str]) -> bool:
        """Check if any of the framework indicators exist."""
        for indicator in indicators:
            if "*" in indicator:
                # Glob pattern
                if list(self.project_path.glob(f"**/{indicator}")):
                    return True
            else:
                # Exact file
                if (self.project_path / indicator).exists():
                    return True
                # Check in subdirectories
                if list(self.project_path.glob(f"**/{indicator}")):
                    return True
        return False
    
    async def _determine_project_type(self) -> str:
        """Determine the primary project type."""
        frameworks = await self._detect_frameworks()
        
        # Priority-based classification
        if "jupyter" in frameworks:
            return "data-science"
        elif any(fw in frameworks for fw in ["django", "flask", "fastapi"]):
            return "web-application"
        elif "setuptools" in frameworks:
            return "library"
        elif "pytest" in frameworks:
            return "testing-focused"
        else:
            return "general"
    
    async def _analyze_file_patterns(self) -> Dict[str, int]:
        """Analyze file patterns and structure."""
        patterns = {
            "python_files": 0,
            "test_files": 0,
            "config_files": 0,
            "notebook_files": 0,
            "documentation_files": 0
        }
        
        for py_file in self.project_path.glob("**/*.py"):
            patterns["python_files"] += 1
            
            if "test" in py_file.name.lower() or "test" in str(py_file.parent).lower():
                patterns["test_files"] += 1
        
        patterns["notebook_files"] = len(list(self.project_path.glob("**/*.ipynb")))
        patterns["config_files"] = len(list(self.project_path.glob("**/*.{yaml,yml,json,toml,cfg,ini}")))
        patterns["documentation_files"] = len(list(self.project_path.glob("**/*.{md,rst,txt}")))
        
        return patterns
    
    async def _analyze_dependencies(self) -> Dict[str, List[str]]:
        """Analyze project dependencies."""
        dependencies = {
            "web_frameworks": [],
            "data_science": [],
            "testing": [],
            "security": [],
            "development": []
        }
        
        # Check requirements files
        req_files = ["requirements.txt", "requirements-dev.txt", "pyproject.toml", "setup.py"]
        
        for req_file in req_files:
            req_path = self.project_path / req_file
            if req_path.exists():
                deps = await self._parse_dependencies(req_path)
                dependencies = self._categorize_dependencies(deps, dependencies)
        
        return dependencies
    
    async def _parse_dependencies(self, req_file: Path) -> List[str]:
        """Parse dependencies from requirements file."""
        try:
            content = req_file.read_text(encoding='utf-8')
            
            if req_file.suffix == '.toml':
                # Basic TOML parsing for dependencies
                deps = []
                in_deps_section = False
                for line in content.split('\n'):
                    if '[tool.poetry.dependencies]' in line or '[project.dependencies]' in line:
                        in_deps_section = True
                        continue
                    elif line.startswith('[') and in_deps_section:
                        break
                    elif in_deps_section and '=' in line:
                        dep = line.split('=')[0].strip().strip('"\'')
                        if dep and not dep.startswith('#'):
                            deps.append(dep)
                return deps
            else:
                # Requirements.txt format
                deps = []
                for line in content.split('\n'):
                    line = line.strip()
                    if line and not line.startswith('#'):
                        # Extract package name (before version specifiers)
                        dep = line.split('==')[0].split('>=')[0].split('<=')[0].split('>')[0].split('<')[0].strip()
                        deps.append(dep)
                return deps
        except Exception:
            return []
    
    def _categorize_dependencies(self, deps: List[str], categories: Dict[str, List[str]]) -> Dict[str, List[str]]:
        """Categorize dependencies by type."""
        category_mapping = {
            "web_frameworks": ["django", "flask", "fastapi", "tornado", "pyramid", "bottle"],
            "data_science": ["pandas", "numpy", "scipy", "scikit-learn", "matplotlib", "seaborn", "jupyter"],
            "testing": ["pytest", "unittest2", "nose", "coverage", "tox", "mock"],
            "security": ["bandit", "safety", "cryptography", "pycryptodome"],
            "development": ["black", "isort", "mypy", "pylint", "flake8", "ruff"]
        }
        
        for dep in deps:
            dep_lower = dep.lower()
            for category, keywords in category_mapping.items():
                if any(keyword in dep_lower for keyword in keywords):
                    if dep not in categories[category]:
                        categories[category].append(dep)
        
        return categories
    
    async def _assess_complexity(self) -> str:
        """Assess project complexity level."""
        file_patterns = await self._analyze_file_patterns()
        
        total_files = file_patterns["python_files"]
        
        if total_files < 10:
            return "simple"
        elif total_files < 50:
            return "medium"
        else:
            return "complex"
    
    async def _assess_security_needs(self) -> str:
        """Assess security requirements."""
        dependencies = await self._analyze_dependencies()
        frameworks = await self._detect_frameworks()
        
        # High security needs for web applications
        if any(fw in frameworks for fw in ["django", "flask", "fastapi"]):
            return "high"
        
        # Medium security for libraries
        if "setuptools" in frameworks:
            return "medium"
        
        # Low security for data science projects
        if "jupyter" in frameworks or dependencies["data_science"]:
            return "low"
        
        return "medium"
    
    async def _assess_performance_focus(self) -> str:
        """Assess performance requirements."""
        dependencies = await self._analyze_dependencies()
        file_patterns = await self._analyze_file_patterns()
        
        # High performance focus for data science
        if dependencies["data_science"] or file_patterns["notebook_files"] > 0:
            return "high"
        
        # Medium performance for web applications
        frameworks = await self._detect_frameworks()
        if any(fw in frameworks for fw in ["django", "flask", "fastapi"]):
            return "medium"
        
        return "low"


class DynamicPresetGenerator:
    """Generates presets dynamically based on project analysis."""
    
    def __init__(self):
        """Initialize dynamic preset generator."""
        self.template_manager = PresetTemplateManager()
    
    async def generate_preset(self, project_path: Path, preset_name: str,
                            author: Optional[str] = None) -> PresetDataModel:
        """Generate a preset based on project analysis."""
        analyzer = ProjectAnalyzer(project_path)
        analysis = await analyzer.analyze_project()
        
        # Determine best template based on analysis
        template_name = self._select_template(analysis)
        
        # Get base template
        base_template = self.template_manager.get_template(template_name)
        if not base_template:
            # Fallback to standard template
            base_template = self._create_default_template()
        
        # Customize template based on analysis
        customized_preset = await self._customize_preset(base_template, analysis, preset_name, author)
        
        return customized_preset
    
    def _select_template(self, analysis: Dict[str, any]) -> str:
        """Select the best template based on project analysis."""
        project_type = analysis.get("project_type", "general")
        security_needs = analysis.get("security_needs", "medium")
        
        # Template selection logic
        if project_type == "data-science":
            return "data-science"
        elif project_type == "web-application":
            return "web-dev"
        elif project_type == "library":
            return "library-dev"
        elif security_needs == "high":
            return "security-focused"
        else:
            return "web-dev"  # Default fallback
    
    async def _customize_preset(self, base_template: PresetDataModel, 
                              analysis: Dict[str, any], preset_name: str,
                              author: Optional[str]) -> PresetDataModel:
        """Customize template based on project analysis."""
        customized = base_template.copy()
        
        # Update metadata
        now = datetime.now(timezone.utc).isoformat()
        customized["metadata"] = PresetMetadata(
            name=preset_name,
            description=f"Auto-generated preset for {analysis.get('project_type', 'general')} project",
            version="1.0.0",
            author=author or "Vibe Check Auto-Generator",
            created_at=now,
            updated_at=now,
            tags=["auto-generated", analysis.get("project_type", "general")],
            compatibility_version=__version__
        )
        
        # Customize rule configuration
        rule_config = customized["rule_config"].copy()
        
        # Adjust complexity rules based on project complexity
        complexity = analysis.get("complexity", "medium")
        if complexity == "simple":
            rule_config["severity_filter"] = "error"  # More lenient
        elif complexity == "complex":
            rule_config["severity_filter"] = "info"   # More strict
        
        # Adjust security rules based on security needs
        security_needs = analysis.get("security_needs", "medium")
        if security_needs == "high":
            if "security" not in rule_config.get("categories", []):
                rule_config["categories"] = rule_config.get("categories", []) + ["security"]
        elif security_needs == "low":
            categories = rule_config.get("categories", [])
            if "security" in categories:
                categories.remove("security")
                rule_config["categories"] = categories
        
        customized["rule_config"] = rule_config
        
        # Customize tool configuration based on dependencies
        dependencies = analysis.get("dependencies", {})
        tool_config = customized["tool_config"].copy()
        
        # Add tools based on detected dependencies
        current_tools = set(tool_config.get("tools", []))
        
        if dependencies.get("development"):
            current_tools.update(["ruff", "mypy"])
        if dependencies.get("security"):
            current_tools.update(["bandit", "safety"])
        if dependencies.get("testing"):
            current_tools.add("pytest")
        
        tool_config["tools"] = list(current_tools)
        customized["tool_config"] = tool_config
        
        # Customize analysis configuration
        analysis_config = customized["analysis_config"].copy()
        
        # Adjust performance settings based on performance focus
        performance_focus = analysis.get("performance_focus", "medium")
        if performance_focus == "high":
            analysis_config["fast_mode"] = True
            analysis_config["max_workers"] = 2
        elif performance_focus == "low":
            analysis_config["detailed"] = True
            analysis_config["max_workers"] = 4
        
        customized["analysis_config"] = analysis_config
        
        return customized
    
    def _create_default_template(self) -> PresetDataModel:
        """Create a default template when no specific template is available."""
        now = datetime.now(timezone.utc).isoformat()
        
        return {
            "metadata": PresetMetadata(
                name="default",
                description="Default preset template",
                version="1.0.0",
                author="Vibe Check",
                created_at=now,
                updated_at=now,
                tags=["default"],
                compatibility_version=__version__
            ),
            "rule_config": RuleConfiguration(
                categories=["style", "types"],
                severity_filter="warning",
                auto_fix=True,
                vcs_rules_only=True,
                enable_framework_rules=True
            ),
            "tool_config": ExternalToolConfiguration(
                tools=["ruff", "mypy"],
                parallel_execution=True,
                timeout_seconds=300
            ),
            "analysis_config": AnalysisConfiguration(
                profile=AnalysisProfile.STANDARD,
                vcs_mode=True,
                fast_mode=False,
                detailed=False,
                use_gitignore=True
            ),
            "inheritance": None,
            "overrides": {},
            "schema_version": "1.0"
        }
