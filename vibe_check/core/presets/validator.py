"""
Preset Validation Framework
===========================

Comprehensive validation system for Vibe Check presets ensuring data integrity,
compatibility, and configuration correctness.
"""

import re
from typing import List, Dict, Any, Optional, Set, Tuple
from datetime import datetime
from pathlib import Path

from ..constants import AnalysisProfile, SeverityLevel, ToolNames
from ..version import __version__
from .models import PresetDataModel, PresetValidationResult, PresetStatus
from .exceptions import PresetValidationError, PresetSchemaError, PresetCompatibilityError


class PresetValidator:
    """Comprehensive preset validation framework."""
    
    # Schema version for preset compatibility
    CURRENT_SCHEMA_VERSION = "1.0"
    
    # Valid rule categories
    VALID_CATEGORIES = {
        "style", "security", "complexity", "documentation", 
        "imports", "types", "performance", "maintainability"
    }
    
    # Valid external tools
    VALID_TOOLS = {
        "ruff", "mypy", "bandit", "pylint", "flake8",
        "black", "isort", "safety", "pydocstyle", "complexity"
    }
    
    # Valid output formats
    VALID_OUTPUT_FORMATS = {"json", "markdown", "html", "xml", "csv"}
    
    # Valid file extensions
    VALID_FILE_EXTENSIONS = {".py", ".pyx", ".pyi", ".ipynb"}
    
    def __init__(self) -> None:
        """Initialize preset validator."""
        self.errors: List[str] = []
        self.warnings: List[str] = []
    
    def validate_preset(self, preset: PresetDataModel, 
                       preset_name: Optional[str] = None) -> PresetValidationResult:
        """
        Validate a complete preset configuration.
        
        Args:
            preset: Preset data to validate
            preset_name: Optional preset name for error reporting
            
        Returns:
            Validation result with status and error details
            
        Raises:
            PresetValidationError: If validation fails
        """
        self.errors.clear()
        self.warnings.clear()
        
        # Validate schema structure
        self._validate_schema_structure(preset)
        
        # Validate metadata
        self._validate_metadata(dict(preset.get("metadata", {})))

        # Validate rule configuration
        self._validate_rule_configuration(dict(preset.get("rule_config", {})))

        # Validate tool configuration
        self._validate_tool_configuration(dict(preset.get("tool_config", {})))

        # Validate analysis configuration
        self._validate_analysis_configuration(dict(preset.get("analysis_config", {})))
        
        # Validate cross-section compatibility
        self._validate_cross_section_compatibility(preset)
        
        # Validate inheritance if specified
        inheritance = preset.get("inheritance")
        if inheritance:
            self._validate_inheritance(str(inheritance))

        # Advanced validation: dependency checking (skip for built-in presets)
        metadata = preset.get("metadata", {})
        tags = metadata.get("tags", [])
        is_builtin = tags and "builtin" in tags

        if not is_builtin:
            self._validate_dependencies(preset)
            self._validate_advanced_conflicts(preset)
            self._validate_performance_implications(preset)
        else:
            # For built-in presets, skip advanced validation
            pass

        # Determine validation status
        is_valid = len(self.errors) == 0
        status = PresetStatus.VALID if is_valid else PresetStatus.INVALID

        result: PresetValidationResult = {
            "valid": is_valid,
            "status": status.value,
            "errors": self.errors.copy(),
            "warnings": self.warnings.copy(),
            "preset_name": preset_name or preset.get("metadata", {}).get("name", "unknown")
        }

        if not is_valid and preset_name:
            raise PresetValidationError(preset_name, self.errors, self.warnings)

        return result
    
    def _validate_schema_structure(self, preset: PresetDataModel) -> None:
        """Validate basic schema structure."""
        required_sections = ["metadata", "rule_config", "tool_config", "analysis_config"]
        
        for section in required_sections:
            if section not in preset:
                self.errors.append(f"Missing required section: {section}")
        
        # Validate schema version - more lenient for built-in presets
        schema_version = preset.get("schema_version")
        metadata = preset.get("metadata", {})
        tags = metadata.get("tags", [])
        is_builtin = tags and "builtin" in tags

        if not schema_version:
            if not is_builtin:
                self.errors.append("Missing schema_version field")
            # Built-in presets can have auto-generated schema version
        elif schema_version != self.CURRENT_SCHEMA_VERSION:
            self.warnings.append(
                f"Schema version {schema_version} may be incompatible with current version {self.CURRENT_SCHEMA_VERSION}"
            )
    
    def _validate_metadata(self, metadata: Dict[str, Any]) -> None:
        """Validate preset metadata."""
        required_fields = ["name", "description", "version"]

        # For built-in presets, be more lenient with required fields
        tags = metadata.get("tags", [])
        is_builtin = tags and "builtin" in tags
        if not is_builtin:
            required_fields.append("created_at")

        for field in required_fields:
            if not metadata.get(field):
                if is_builtin and field == "created_at":
                    # Auto-generate for built-in presets
                    continue
                self.errors.append(f"Missing required metadata field: {field}")

        # Validate name format
        name = metadata.get("name", "")
        if name and not re.match(r"^[a-zA-Z0-9_-]+$", name):
            self.errors.append("Preset name must contain only alphanumeric characters, hyphens, and underscores")

        # Validate version format (semantic versioning) - more lenient
        version = metadata.get("version", "")
        if version and not re.match(r"^\d+\.\d+(\.\d+)?$", version):
            if not is_builtin:  # Only strict for user presets
                self.errors.append("Version must follow semantic versioning format (e.g., 1.0.0)")
            else:
                self.warnings.append("Built-in preset version format is non-standard")

        # Validate timestamps - more lenient for built-in presets
        for timestamp_field in ["created_at", "updated_at"]:
            timestamp = metadata.get(timestamp_field)
            if timestamp:
                try:
                    if isinstance(timestamp, str):
                        datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                except ValueError:
                    if not is_builtin:
                        self.errors.append(f"Invalid timestamp format for {timestamp_field}: {timestamp}")
                    else:
                        self.warnings.append(f"Built-in preset has non-standard timestamp format for {timestamp_field}")

        # Validate compatibility version (warning only for development)
        compat_version = metadata.get("compatibility_version")
        if compat_version and not self._is_version_compatible(compat_version):
            self.warnings.append(
                f"Preset requires Vibe Check version {compat_version} but current version is {__version__}"
            )
    
    def _validate_rule_configuration(self, rule_config: Dict[str, Any]) -> None:
        """Validate rule configuration section."""
        # Validate categories
        categories = rule_config.get("categories", [])
        if categories:
            invalid_categories = set(categories) - self.VALID_CATEGORIES
            if invalid_categories:
                self.errors.append(f"Invalid rule categories: {', '.join(invalid_categories)}")
        
        exclude_categories = rule_config.get("exclude_categories", [])
        if exclude_categories:
            invalid_exclude = set(exclude_categories) - self.VALID_CATEGORIES
            if invalid_exclude:
                self.errors.append(f"Invalid exclude categories: {', '.join(invalid_exclude)}")
        
        # Check for conflicting category selections
        if categories and exclude_categories:
            conflicts = set(categories) & set(exclude_categories)
            if conflicts:
                self.errors.append(f"Categories cannot be both included and excluded: {', '.join(conflicts)}")
        
        # Validate severity filter
        severity_filter = rule_config.get("severity_filter")
        if severity_filter:
            valid_severities = {"error", "warning", "info", "critical"}  # Common severity levels
            if severity_filter.lower() not in valid_severities:
                self.errors.append(f"Invalid severity filter: {severity_filter}")
        
        # Validate rule IDs format
        for rule_list_field in ["rule_ids", "exclude_rules"]:
            rule_list = rule_config.get(rule_list_field, [])
            if rule_list:
                for rule_id in rule_list:
                    if not re.match(r"^[A-Z]+\d+$", rule_id):
                        self.warnings.append(f"Rule ID '{rule_id}' doesn't follow standard format (e.g., S001, C002)")
    
    def _validate_tool_configuration(self, tool_config: Dict[str, Any]) -> None:
        """Validate external tool configuration."""
        # Validate tool names
        tools = tool_config.get("tools", [])
        if tools:
            invalid_tools = set(tools) - self.VALID_TOOLS
            if invalid_tools:
                self.errors.append(f"Invalid external tools: {', '.join(invalid_tools)}")
        
        exclude_tools = tool_config.get("exclude_tools", [])
        if exclude_tools:
            invalid_exclude = set(exclude_tools) - self.VALID_TOOLS
            if invalid_exclude:
                self.errors.append(f"Invalid exclude tools: {', '.join(invalid_exclude)}")
        
        # Check for conflicting tool selections
        if tools and exclude_tools:
            conflicts = set(tools) & set(exclude_tools)
            if conflicts:
                self.errors.append(f"Tools cannot be both included and excluded: {', '.join(conflicts)}")
        
        # Validate timeout
        timeout = tool_config.get("timeout_seconds")
        if timeout is not None:
            if not isinstance(timeout, int) or timeout <= 0:
                self.errors.append("Tool timeout must be a positive integer")
            elif timeout > 3600:  # 1 hour
                self.warnings.append("Tool timeout is very high (>1 hour), consider reducing for better performance")
    
    def _validate_analysis_configuration(self, analysis_config: Dict[str, Any]) -> None:
        """Validate analysis configuration section."""
        # Validate profile - more lenient for built-in presets
        profile = analysis_config.get("profile")
        if profile:
            valid_profiles = {p.value for p in AnalysisProfile}
            if isinstance(profile, str) and profile not in valid_profiles:
                self.warnings.append(f"Non-standard analysis profile: {profile}")
            # Allow profile objects for built-in presets
        
        # Validate output formats - more lenient for built-in presets
        output_formats = analysis_config.get("output_formats", [])
        if output_formats:
            invalid_formats = set(output_formats) - self.VALID_OUTPUT_FORMATS
            if invalid_formats:
                # More lenient - warning instead of error for built-in presets
                self.warnings.append(f"Non-standard output formats: {', '.join(invalid_formats)}")
        
        # Validate file extensions
        file_extensions = analysis_config.get("file_extensions", [])
        if file_extensions:
            invalid_extensions = set(file_extensions) - self.VALID_FILE_EXTENSIONS
            if invalid_extensions:
                self.warnings.append(f"Unusual file extensions: {', '.join(invalid_extensions)}")
        
        # Validate performance settings
        max_workers = analysis_config.get("max_workers")
        if max_workers is not None:
            if not isinstance(max_workers, int) or max_workers <= 0:
                self.errors.append("max_workers must be a positive integer")
            elif max_workers > 16:
                self.warnings.append("High max_workers value may cause resource contention")
    
    def _validate_cross_section_compatibility(self, preset: PresetDataModel) -> None:
        """Validate compatibility between different configuration sections."""
        rule_config = preset.get("rule_config", {})
        tool_config = preset.get("tool_config", {})
        analysis_config = preset.get("analysis_config", {})
        
        # Check VCS mode compatibility
        vcs_mode = analysis_config.get("vcs_mode", False)
        vcs_rules_only = rule_config.get("vcs_rules_only", False)
        
        if vcs_rules_only and not vcs_mode:
            self.warnings.append("vcs_rules_only is enabled but vcs_mode is disabled")
        
        # Check fast mode compatibility
        fast_mode = analysis_config.get("fast_mode", False)
        if fast_mode:
            if tool_config.get("tools"):
                self.warnings.append("External tools may slow down fast mode execution")
            
            detailed = analysis_config.get("detailed", False)
            if detailed:
                self.warnings.append("Detailed output may conflict with fast mode optimization")
    
    def _validate_inheritance(self, base_preset_name: str) -> None:
        """Validate preset inheritance configuration."""
        if not re.match(r"^[a-zA-Z0-9_-]+$", base_preset_name):
            self.errors.append("Base preset name must contain only alphanumeric characters, hyphens, and underscores")
    
    def _is_version_compatible(self, required_version: str) -> bool:
        """Check if required version is compatible with current version."""
        try:
            # Simple version comparison - in production, use packaging.version
            current_parts = [int(x) for x in __version__.split('.')]
            required_parts = [int(x) for x in required_version.split('.')]

            # Pad shorter version with zeros
            max_len = max(len(current_parts), len(required_parts))
            current_parts.extend([0] * (max_len - len(current_parts)))
            required_parts.extend([0] * (max_len - len(required_parts)))

            return current_parts >= required_parts
        except (ValueError, AttributeError):
            return False

    def _validate_dependencies(self, preset: PresetDataModel) -> None:
        """Validate preset dependencies and requirements."""
        rule_config = preset.get("rule_config", {})
        tool_config = preset.get("tool_config", {})
        analysis_config = preset.get("analysis_config", {})

        # Check VCS mode dependencies
        vcs_mode = analysis_config.get("vcs_mode", False)
        vcs_rules_only = rule_config.get("vcs_rules_only", False)

        if vcs_rules_only and not vcs_mode:
            self.errors.append("vcs_rules_only requires vcs_mode to be enabled")

        # Check tool dependencies
        tools = tool_config.get("tools", [])
        if tools:
            # Check for conflicting tools
            conflicting_pairs = [
                ("black", "autopep8"),
                ("isort", "reorder-python-imports"),
                ("mypy", "pyright")
            ]

            for tool1, tool2 in conflicting_pairs:
                if tool1 in tools and tool2 in tools:
                    self.warnings.append(f"Tools '{tool1}' and '{tool2}' may conflict")

        # Check framework rule dependencies
        enable_framework_rules = rule_config.get("enable_framework_rules", False)
        categories = rule_config.get("categories", [])

        if enable_framework_rules and categories and "security" not in categories:
            self.warnings.append("Framework rules enabled but security category not included")

    def _validate_advanced_conflicts(self, preset: PresetDataModel) -> None:
        """Validate advanced configuration conflicts."""
        rule_config = preset.get("rule_config", {})
        tool_config = preset.get("tool_config", {})
        analysis_config = preset.get("analysis_config", {})

        # Check performance vs quality trade-offs
        fast_mode = analysis_config.get("fast_mode", False)
        detailed = analysis_config.get("detailed", False)
        profile = analysis_config.get("profile", "standard")

        if fast_mode and detailed:
            self.warnings.append("Fast mode with detailed output may reduce performance benefits")

        if fast_mode and profile == "comprehensive":
            self.warnings.append("Fast mode with comprehensive profile is contradictory")

        # Check tool configuration conflicts
        parallel_execution = tool_config.get("parallel_execution", True)
        max_workers = analysis_config.get("max_workers", 4)

        if parallel_execution and max_workers == 1:
            self.warnings.append("Parallel execution enabled but max_workers set to 1")

        # Check rule configuration conflicts
        auto_fix = rule_config.get("auto_fix", False)
        severity_filter = rule_config.get("severity_filter", "warning")

        if auto_fix and severity_filter == "info":
            self.warnings.append("Auto-fix with info severity may make excessive changes")

        # Check exclude patterns vs categories
        exclude_categories = rule_config.get("exclude_categories", [])
        categories = rule_config.get("categories", [])

        if exclude_categories and not categories:
            self.warnings.append("Exclude categories specified but no categories included")

    def _validate_performance_implications(self, preset: PresetDataModel) -> None:
        """Validate performance implications of preset configuration."""
        tool_config = preset.get("tool_config", {})
        analysis_config = preset.get("analysis_config", {})

        # Check tool count vs performance
        tools = tool_config.get("tools", []) or []
        timeout_seconds = tool_config.get("timeout_seconds", 300) or 300

        if len(tools) > 5:
            self.warnings.append(f"Large number of tools ({len(tools)}) may impact performance")

        if timeout_seconds > 600:  # 10 minutes
            self.warnings.append("Very high timeout may indicate performance issues")

        # Check file extension patterns
        file_extensions = analysis_config.get("file_extensions", []) or []
        if file_extensions and ".ipynb" in file_extensions:
            max_workers = analysis_config.get("max_workers", 4) or 4
            if max_workers > 2:
                self.warnings.append("High worker count with Jupyter notebooks may cause memory issues")

        # Check output format impact
        output_formats = analysis_config.get("output_formats", []) or []
        if len(output_formats) > 3:
            self.warnings.append("Multiple output formats may increase processing time")

        # Check VCS mode vs external tools
        vcs_mode = analysis_config.get("vcs_mode", False)
        if not vcs_mode and len(tools) > 3:
            self.warnings.append("Many external tools without VCS mode may be slow")
