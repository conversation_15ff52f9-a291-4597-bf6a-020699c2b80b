"""
Preset Validation Framework
===========================

Comprehensive validation system for Vibe Check presets ensuring data integrity,
compatibility, and configuration correctness.
"""

import re
from typing import List, Dict, Any, Optional, Set, Tuple
from datetime import datetime
from pathlib import Path

from ..constants import AnalysisProfile, SeverityLevel, ToolNames
from ..version import __version__
from .models import PresetDataModel, PresetValidationResult, PresetStatus
from .exceptions import PresetValidationError, PresetSchemaError, PresetCompatibilityError


class PresetValidator:
    """Comprehensive preset validation framework."""
    
    # Schema version for preset compatibility
    CURRENT_SCHEMA_VERSION = "1.0"
    
    # Valid rule categories
    VALID_CATEGORIES = {
        "style", "security", "complexity", "documentation", 
        "imports", "types", "performance", "maintainability"
    }
    
    # Valid external tools
    VALID_TOOLS = {
        "ruff", "mypy", "bandit", "pylint", "flake8", 
        "black", "isort", "safety", "pydocstyle"
    }
    
    # Valid output formats
    VALID_OUTPUT_FORMATS = {"json", "markdown", "html", "xml", "csv"}
    
    # Valid file extensions
    VALID_FILE_EXTENSIONS = {".py", ".pyx", ".pyi", ".ipynb"}
    
    def __init__(self) -> None:
        """Initialize preset validator."""
        self.errors: List[str] = []
        self.warnings: List[str] = []
    
    def validate_preset(self, preset: PresetDataModel, 
                       preset_name: Optional[str] = None) -> PresetValidationResult:
        """
        Validate a complete preset configuration.
        
        Args:
            preset: Preset data to validate
            preset_name: Optional preset name for error reporting
            
        Returns:
            Validation result with status and error details
            
        Raises:
            PresetValidationError: If validation fails
        """
        self.errors.clear()
        self.warnings.clear()
        
        # Validate schema structure
        self._validate_schema_structure(preset)
        
        # Validate metadata
        self._validate_metadata(preset.get("metadata", {}))
        
        # Validate rule configuration
        self._validate_rule_configuration(preset.get("rule_config", {}))
        
        # Validate tool configuration
        self._validate_tool_configuration(preset.get("tool_config", {}))
        
        # Validate analysis configuration
        self._validate_analysis_configuration(preset.get("analysis_config", {}))
        
        # Validate cross-section compatibility
        self._validate_cross_section_compatibility(preset)
        
        # Validate inheritance if specified
        if preset.get("inheritance"):
            self._validate_inheritance(preset["inheritance"])
        
        # Determine validation status
        is_valid = len(self.errors) == 0
        status = PresetStatus.VALID if is_valid else PresetStatus.INVALID
        
        result: PresetValidationResult = {
            "valid": is_valid,
            "status": status.value,
            "errors": self.errors.copy(),
            "warnings": self.warnings.copy(),
            "preset_name": preset_name or preset.get("metadata", {}).get("name", "unknown")
        }
        
        if not is_valid and preset_name:
            raise PresetValidationError(preset_name, self.errors, self.warnings)
        
        return result
    
    def _validate_schema_structure(self, preset: PresetDataModel) -> None:
        """Validate basic schema structure."""
        required_sections = ["metadata", "rule_config", "tool_config", "analysis_config"]
        
        for section in required_sections:
            if section not in preset:
                self.errors.append(f"Missing required section: {section}")
        
        # Validate schema version
        schema_version = preset.get("schema_version")
        if not schema_version:
            self.errors.append("Missing schema_version field")
        elif schema_version != self.CURRENT_SCHEMA_VERSION:
            self.warnings.append(
                f"Schema version {schema_version} may be incompatible with current version {self.CURRENT_SCHEMA_VERSION}"
            )
    
    def _validate_metadata(self, metadata: Dict[str, Any]) -> None:
        """Validate preset metadata."""
        required_fields = ["name", "description", "version", "created_at"]
        
        for field in required_fields:
            if not metadata.get(field):
                self.errors.append(f"Missing required metadata field: {field}")
        
        # Validate name format
        name = metadata.get("name", "")
        if name and not re.match(r"^[a-zA-Z0-9_-]+$", name):
            self.errors.append("Preset name must contain only alphanumeric characters, hyphens, and underscores")
        
        # Validate version format (semantic versioning)
        version = metadata.get("version", "")
        if version and not re.match(r"^\d+\.\d+(\.\d+)?$", version):
            self.errors.append("Version must follow semantic versioning format (e.g., 1.0.0)")
        
        # Validate timestamps
        for timestamp_field in ["created_at", "updated_at"]:
            timestamp = metadata.get(timestamp_field)
            if timestamp:
                try:
                    datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                except ValueError:
                    self.errors.append(f"Invalid timestamp format for {timestamp_field}: {timestamp}")
        
        # Validate compatibility version (warning only for development)
        compat_version = metadata.get("compatibility_version")
        if compat_version and not self._is_version_compatible(compat_version):
            self.warnings.append(
                f"Preset requires Vibe Check version {compat_version} but current version is {__version__}"
            )
    
    def _validate_rule_configuration(self, rule_config: Dict[str, Any]) -> None:
        """Validate rule configuration section."""
        # Validate categories
        categories = rule_config.get("categories", [])
        if categories:
            invalid_categories = set(categories) - self.VALID_CATEGORIES
            if invalid_categories:
                self.errors.append(f"Invalid rule categories: {', '.join(invalid_categories)}")
        
        exclude_categories = rule_config.get("exclude_categories", [])
        if exclude_categories:
            invalid_exclude = set(exclude_categories) - self.VALID_CATEGORIES
            if invalid_exclude:
                self.errors.append(f"Invalid exclude categories: {', '.join(invalid_exclude)}")
        
        # Check for conflicting category selections
        if categories and exclude_categories:
            conflicts = set(categories) & set(exclude_categories)
            if conflicts:
                self.errors.append(f"Categories cannot be both included and excluded: {', '.join(conflicts)}")
        
        # Validate severity filter
        severity_filter = rule_config.get("severity_filter")
        if severity_filter:
            valid_severities = {level.value.lower() for level in SeverityLevel}
            if severity_filter.lower() not in valid_severities:
                self.errors.append(f"Invalid severity filter: {severity_filter}")
        
        # Validate rule IDs format
        for rule_list_field in ["rule_ids", "exclude_rules"]:
            rule_list = rule_config.get(rule_list_field, [])
            if rule_list:
                for rule_id in rule_list:
                    if not re.match(r"^[A-Z]+\d+$", rule_id):
                        self.warnings.append(f"Rule ID '{rule_id}' doesn't follow standard format (e.g., S001, C002)")
    
    def _validate_tool_configuration(self, tool_config: Dict[str, Any]) -> None:
        """Validate external tool configuration."""
        # Validate tool names
        tools = tool_config.get("tools", [])
        if tools:
            invalid_tools = set(tools) - self.VALID_TOOLS
            if invalid_tools:
                self.errors.append(f"Invalid external tools: {', '.join(invalid_tools)}")
        
        exclude_tools = tool_config.get("exclude_tools", [])
        if exclude_tools:
            invalid_exclude = set(exclude_tools) - self.VALID_TOOLS
            if invalid_exclude:
                self.errors.append(f"Invalid exclude tools: {', '.join(invalid_exclude)}")
        
        # Check for conflicting tool selections
        if tools and exclude_tools:
            conflicts = set(tools) & set(exclude_tools)
            if conflicts:
                self.errors.append(f"Tools cannot be both included and excluded: {', '.join(conflicts)}")
        
        # Validate timeout
        timeout = tool_config.get("timeout_seconds")
        if timeout is not None:
            if not isinstance(timeout, int) or timeout <= 0:
                self.errors.append("Tool timeout must be a positive integer")
            elif timeout > 3600:  # 1 hour
                self.warnings.append("Tool timeout is very high (>1 hour), consider reducing for better performance")
    
    def _validate_analysis_configuration(self, analysis_config: Dict[str, Any]) -> None:
        """Validate analysis configuration section."""
        # Validate profile
        profile = analysis_config.get("profile")
        if profile:
            valid_profiles = {p.value for p in AnalysisProfile}
            if profile not in valid_profiles:
                self.errors.append(f"Invalid analysis profile: {profile}")
        
        # Validate output formats
        output_formats = analysis_config.get("output_formats", [])
        if output_formats:
            invalid_formats = set(output_formats) - self.VALID_OUTPUT_FORMATS
            if invalid_formats:
                self.errors.append(f"Invalid output formats: {', '.join(invalid_formats)}")
        
        # Validate file extensions
        file_extensions = analysis_config.get("file_extensions", [])
        if file_extensions:
            invalid_extensions = set(file_extensions) - self.VALID_FILE_EXTENSIONS
            if invalid_extensions:
                self.warnings.append(f"Unusual file extensions: {', '.join(invalid_extensions)}")
        
        # Validate performance settings
        max_workers = analysis_config.get("max_workers")
        if max_workers is not None:
            if not isinstance(max_workers, int) or max_workers <= 0:
                self.errors.append("max_workers must be a positive integer")
            elif max_workers > 16:
                self.warnings.append("High max_workers value may cause resource contention")
    
    def _validate_cross_section_compatibility(self, preset: PresetDataModel) -> None:
        """Validate compatibility between different configuration sections."""
        rule_config = preset.get("rule_config", {})
        tool_config = preset.get("tool_config", {})
        analysis_config = preset.get("analysis_config", {})
        
        # Check VCS mode compatibility
        vcs_mode = analysis_config.get("vcs_mode", False)
        vcs_rules_only = rule_config.get("vcs_rules_only", False)
        
        if vcs_rules_only and not vcs_mode:
            self.warnings.append("vcs_rules_only is enabled but vcs_mode is disabled")
        
        # Check fast mode compatibility
        fast_mode = analysis_config.get("fast_mode", False)
        if fast_mode:
            if tool_config.get("tools"):
                self.warnings.append("External tools may slow down fast mode execution")
            
            detailed = analysis_config.get("detailed", False)
            if detailed:
                self.warnings.append("Detailed output may conflict with fast mode optimization")
    
    def _validate_inheritance(self, base_preset_name: str) -> None:
        """Validate preset inheritance configuration."""
        if not re.match(r"^[a-zA-Z0-9_-]+$", base_preset_name):
            self.errors.append("Base preset name must contain only alphanumeric characters, hyphens, and underscores")
    
    def _is_version_compatible(self, required_version: str) -> bool:
        """Check if required version is compatible with current version."""
        try:
            # Simple version comparison - in production, use packaging.version
            current_parts = [int(x) for x in __version__.split('.')]
            required_parts = [int(x) for x in required_version.split('.')]
            
            # Pad shorter version with zeros
            max_len = max(len(current_parts), len(required_parts))
            current_parts.extend([0] * (max_len - len(current_parts)))
            required_parts.extend([0] * (max_len - len(required_parts)))
            
            return current_parts >= required_parts
        except (ValueError, AttributeError):
            return False
