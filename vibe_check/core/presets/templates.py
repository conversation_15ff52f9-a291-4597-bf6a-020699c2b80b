"""
Preset Template System
======================

Built-in preset templates for common use cases, providing rapid setup
for different development scenarios and project types.
"""

from typing import Dict, List, Optional
from datetime import datetime, timezone

from .models import PresetDataModel, PresetMetadata, RuleConfiguration, ExternalToolConfiguration, AnalysisConfiguration
from ..constants import AnalysisProfile
from ..version import __version__


class PresetTemplateManager:
    """Manager for built-in preset templates."""
    
    def __init__(self):
        """Initialize template manager."""
        self._templates: Dict[str, PresetDataModel] = {}
        self._initialize_templates()
    
    def _initialize_templates(self) -> None:
        """Initialize built-in preset templates."""
        now = datetime.now(timezone.utc).isoformat()
        
        # Web Development Template
        self._templates["web-dev"] = {
            "metadata": PresetMetadata(
                name="web-dev",
                description="Optimized for web development with Flask/Django/FastAPI",
                version="1.0.0",
                author="Vibe Check",
                created_at=now,
                updated_at=now,
                tags=["web", "flask", "django", "fastapi", "template"],
                compatibility_version=__version__
            ),
            "rule_config": RuleConfiguration(
                categories=["style", "security", "performance", "maintainability"],
                exclude_categories=["complexity"],  # Web apps often have complex views
                severity_filter="warning",
                auto_fix=True,
                vcs_rules_only=False,
                enable_framework_rules=True,
                rule_configs={
                    "S001": {"max_line_length": 120},  # Longer lines for web templates
                    "S002": {"allow_trailing_whitespace": False},
                    "SEC001": {"check_sql_injection": True},
                    "SEC002": {"check_xss_vulnerabilities": True}
                }
            ),
            "tool_config": ExternalToolConfiguration(
                tools=["ruff", "mypy", "bandit", "safety"],
                exclude_tools=["pylint"],  # Can be too strict for web frameworks
                parallel_execution=True,
                timeout_seconds=300,
                tool_configs={
                    "ruff": {"select": ["E", "W", "F", "B", "S"], "ignore": ["E501"]},
                    "mypy": {"strict": False, "ignore_missing_imports": True},
                    "bandit": {"skip_tests": ["B101", "B601"]},  # Skip assert and shell usage
                    "safety": {"check_vulnerabilities": True}
                }
            ),
            "analysis_config": AnalysisConfiguration(
                profile="standard",
                vcs_mode=False,  # Use external tools for web frameworks
                fast_mode=False,
                detailed=True,
                quiet=False,
                no_save=False,
                output_formats=["markdown", "json"],
                max_workers=4,
                timeout_seconds=300,
                file_extensions=[".py", ".pyi"],
                exclude_patterns=["*/migrations/*", "*/static/*", "*/media/*"],
                use_gitignore=True
            ),
            "inheritance": None,
            "overrides": {},
            "schema_version": "1.0"
        }
        
        # Data Science Template
        self._templates["data-science"] = {
            "metadata": PresetMetadata(
                name="data-science",
                description="Optimized for data science and machine learning projects",
                version="1.0.0",
                author="Vibe Check",
                created_at=now,
                updated_at=now,
                tags=["data-science", "ml", "jupyter", "pandas", "template"],
                compatibility_version=__version__
            ),
            "rule_config": RuleConfiguration(
                categories=["style", "performance", "types", "documentation"],
                exclude_categories=["security"],  # Less critical for research code
                severity_filter="error",  # More lenient for experimental code
                auto_fix=False,  # Preserve research code structure
                vcs_rules_only=True,  # Faster analysis for large datasets
                enable_framework_rules=True,
                rule_configs={
                    "S001": {"max_line_length": 100},
                    "C001": {"max_complexity": 15},  # Higher complexity allowed
                    "DOC001": {"require_docstrings": True},
                    "PERF001": {"check_pandas_performance": True}
                }
            ),
            "tool_config": ExternalToolConfiguration(
                tools=["ruff", "mypy"],
                exclude_tools=["bandit", "safety"],  # Security less critical
                parallel_execution=True,
                timeout_seconds=600,  # Longer timeout for large files
                tool_configs={
                    "ruff": {"select": ["E", "W", "F", "PD"], "ignore": ["E402"]},  # Allow imports anywhere
                    "mypy": {"strict": False, "ignore_missing_imports": True}
                }
            ),
            "analysis_config": AnalysisConfiguration(
                profile="minimal",  # Fast analysis for iterative development
                vcs_mode=True,
                fast_mode=True,
                detailed=False,
                quiet=True,
                no_save=False,
                output_formats=["json"],
                max_workers=2,  # Conservative for memory usage
                timeout_seconds=600,
                file_extensions=[".py", ".ipynb"],
                exclude_patterns=["*/data/*", "*/models/*", "*/checkpoints/*"],
                use_gitignore=True
            ),
            "inheritance": None,
            "overrides": {},
            "schema_version": "1.0"
        }
        
        # Security-Focused Template
        self._templates["security-focused"] = {
            "metadata": PresetMetadata(
                name="security-focused",
                description="Maximum security analysis for production applications",
                version="1.0.0",
                author="Vibe Check",
                created_at=now,
                updated_at=now,
                tags=["security", "production", "enterprise", "template"],
                compatibility_version=__version__
            ),
            "rule_config": RuleConfiguration(
                categories=["security", "style", "types", "maintainability"],
                exclude_categories=None,
                severity_filter="info",  # Catch everything
                auto_fix=False,  # Manual review required
                vcs_rules_only=False,
                enable_framework_rules=True,
                rule_configs={
                    "SEC001": {"strict_mode": True},
                    "SEC002": {"check_all_vulnerabilities": True},
                    "SEC003": {"check_crypto_usage": True},
                    "S001": {"max_line_length": 88},  # Strict formatting
                    "T001": {"strict_typing": True}
                }
            ),
            "tool_config": ExternalToolConfiguration(
                tools=["ruff", "mypy", "bandit", "safety", "pydocstyle"],
                exclude_tools=None,
                parallel_execution=False,  # Sequential for thorough analysis
                timeout_seconds=900,  # Longer timeout for comprehensive checks
                tool_configs={
                    "ruff": {"select": ["ALL"], "ignore": []},  # All rules enabled
                    "mypy": {"strict": True, "warn_return_any": True},
                    "bandit": {"confidence_level": "low", "severity_level": "low"},
                    "safety": {"full_report": True, "check_vulnerabilities": True},
                    "pydocstyle": {"convention": "google"}
                }
            ),
            "analysis_config": AnalysisConfiguration(
                profile="comprehensive",
                vcs_mode=False,  # Use all available tools
                fast_mode=False,
                detailed=True,
                quiet=False,
                no_save=False,
                output_formats=["markdown", "json", "xml"],  # Multiple formats for reporting
                max_workers=1,  # Single-threaded for stability
                timeout_seconds=1800,  # 30 minutes for comprehensive analysis
                file_extensions=[".py", ".pyi"],
                exclude_patterns=["*/tests/*"],  # Focus on production code
                use_gitignore=True
            ),
            "inheritance": None,
            "overrides": {},
            "schema_version": "1.0"
        }
        
        # Library Development Template
        self._templates["library-dev"] = {
            "metadata": PresetMetadata(
                name="library-dev",
                description="Optimized for Python library and package development",
                version="1.0.0",
                author="Vibe Check",
                created_at=now,
                updated_at=now,
                tags=["library", "package", "distribution", "template"],
                compatibility_version=__version__
            ),
            "rule_config": RuleConfiguration(
                categories=["style", "types", "documentation", "maintainability"],
                exclude_categories=["performance"],  # Focus on API design
                severity_filter="warning",
                auto_fix=True,
                vcs_rules_only=False,
                enable_framework_rules=False,  # Generic library code
                rule_configs={
                    "S001": {"max_line_length": 88},  # Black standard
                    "DOC001": {"require_docstrings": True},
                    "DOC002": {"require_type_annotations": True},
                    "T001": {"strict_typing": True}
                }
            ),
            "tool_config": ExternalToolConfiguration(
                tools=["ruff", "mypy", "pydocstyle", "black", "isort"],
                exclude_tools=["bandit"],  # Less relevant for libraries
                parallel_execution=True,
                timeout_seconds=300,
                tool_configs={
                    "ruff": {"select": ["E", "W", "F", "I", "D"], "ignore": []},
                    "mypy": {"strict": True, "warn_unused_ignores": True},
                    "pydocstyle": {"convention": "google"},
                    "black": {"line_length": 88},
                    "isort": {"profile": "black"}
                }
            ),
            "analysis_config": AnalysisConfiguration(
                profile="standard",
                vcs_mode=False,
                fast_mode=False,
                detailed=True,
                quiet=False,
                no_save=False,
                output_formats=["markdown"],
                max_workers=4,
                timeout_seconds=300,
                file_extensions=[".py", ".pyi"],
                exclude_patterns=["*/tests/*", "*/docs/*", "*/examples/*"],
                use_gitignore=True
            ),
            "inheritance": None,
            "overrides": {},
            "schema_version": "1.0"
        }
    
    def get_template(self, template_name: str) -> Optional[PresetDataModel]:
        """Get a preset template by name."""
        return self._templates.get(template_name)
    
    def list_templates(self) -> Dict[str, Dict[str, str]]:
        """List all available preset templates."""
        return {
            name: {
                "description": template["metadata"]["description"],
                "tags": ", ".join(template["metadata"].get("tags", [])),
                "use_case": self._get_use_case_description(name)
            }
            for name, template in self._templates.items()
        }
    
    def _get_use_case_description(self, template_name: str) -> str:
        """Get detailed use case description for a template."""
        descriptions = {
            "web-dev": "Flask, Django, FastAPI applications with security focus",
            "data-science": "Jupyter notebooks, pandas, scikit-learn, research code",
            "security-focused": "Production applications requiring maximum security",
            "library-dev": "Python packages and libraries for distribution"
        }
        return descriptions.get(template_name, "General purpose template")
    
    def get_template_names(self) -> List[str]:
        """Get list of available template names."""
        return list(self._templates.keys())
    
    def create_preset_from_template(self, template_name: str, preset_name: str,
                                  description: Optional[str] = None,
                                  author: Optional[str] = None) -> Optional[PresetDataModel]:
        """Create a new preset based on a template."""
        template = self.get_template(template_name)
        if not template:
            return None
        
        # Create new preset based on template
        now = datetime.now(timezone.utc).isoformat()
        new_preset = template.copy()
        
        # Update metadata
        new_preset["metadata"] = PresetMetadata(
            name=preset_name,
            description=description or f"Preset based on {template_name} template",
            version="1.0.0",
            author=author or "User",
            created_at=now,
            updated_at=now,
            tags=template["metadata"].get("tags", []) + ["custom"],
            compatibility_version=__version__
        )
        
        return new_preset
