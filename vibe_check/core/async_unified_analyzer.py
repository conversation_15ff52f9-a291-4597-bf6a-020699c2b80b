"""
Async Unified Analysis Engine
============================

Enhanced async version of the unified analysis engine with full async/await patterns,
proper concurrency control, and optimized file I/O operations.

This builds upon the validated Week 2 foundation to achieve 50% performance improvement
through async optimization.
"""

import asyncio
try:
    import aiofiles
    AIOFILES_AVAILABLE = True
except ImportError:
    AIOFILES_AVAILABLE = False
    aiofiles = None
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, AsyncIterator, Callable
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor
import multiprocessing
import logging
from contextlib import asynccontextmanager

from vibe_check.core.models import FileMetrics, ProjectMetrics, DirectoryMetrics, AnalysisConfig, AnalysisResult

logger = logging.getLogger(__name__)


@dataclass
class AsyncAnalysisConfig(AnalysisConfig):
    """Enhanced async analysis configuration"""
    # Async-specific settings
    max_concurrent_files: int = 50  # Limit concurrent file operations
    file_read_chunk_size: int = 8192  # Async file read chunk size
    semaphore_timeout: float = 30.0  # Timeout for semaphore acquisition
    batch_size: int = 100  # Files to process in each batch
    enable_streaming: bool = True  # Enable streaming results
    
    # Performance tuning
    cpu_bound_threshold: int = 1000  # Lines threshold for CPU-bound processing
    io_bound_workers: int = multiprocessing.cpu_count() * 2  # I/O workers
    cpu_bound_workers: int = multiprocessing.cpu_count()  # CPU workers


class AsyncFileProcessor:
    """Async file processing with optimized I/O"""
    
    def __init__(self, config: AsyncAnalysisConfig):
        self.config = config
        self.semaphore = asyncio.Semaphore(config.max_concurrent_files)
        self.io_executor = ThreadPoolExecutor(max_workers=config.io_bound_workers)
        self.cpu_executor = ThreadPoolExecutor(max_workers=config.cpu_bound_workers)
        
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        self.io_executor.shutdown(wait=False)
        self.cpu_executor.shutdown(wait=False)
    
    async def read_file_async(self, file_path: Path) -> str:
        """Async file reading with proper error handling"""
        try:
            async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                content = await f.read()
            return content
        except UnicodeDecodeError:
            # Try with different encoding
            try:
                async with aiofiles.open(file_path, 'r', encoding='latin-1') as f:
                    content = await f.read()
                return content
            except Exception as e:
                logger.warning(f"Failed to read {file_path}: {e}")
                return ""
        except Exception as e:
            logger.warning(f"Failed to read {file_path}: {e}")
            return ""
    
    async def analyze_file_content(self, content: str, file_path: Path) -> Dict[str, Any]:
        """Analyze file content with CPU/IO optimization"""
        lines = content.split('\n')
        total_lines = len(lines)
        
        # For large files, use CPU executor
        if total_lines > self.config.cpu_bound_threshold:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(
                self.cpu_executor,
                self._analyze_content_sync,
                content, str(file_path)
            )
        else:
            # Small files can be processed directly
            return self._analyze_content_sync(content, str(file_path))
    
    def _analyze_content_sync(self, content: str, file_path: str) -> Dict[str, Any]:
        """Synchronous content analysis for CPU-bound work"""
        lines = content.split('\n')
        total_lines = len(lines)
        code_lines = len([line for line in lines if line.strip() and not line.strip().startswith('#')])
        
        # Calculate complexity
        complexity = 1  # Base complexity
        complexity += content.count('if ')
        complexity += content.count('elif ')
        complexity += content.count('while ')
        complexity += content.count('for ')
        complexity += content.count('except ')
        complexity += content.count('with ')
        complexity += content.count('and ')
        complexity += content.count('or ')
        
        # Count structures
        functions = content.count('def ')
        classes = content.count('class ')
        imports = content.count('import ') + content.count('from ')
        
        # Quality score calculation
        quality_score = 10.0
        if complexity > 20:
            quality_score -= min(3.0, (complexity - 20) * 0.1)
        if total_lines > 500:
            quality_score -= min(2.0, (total_lines - 500) * 0.001)
        if code_lines > 0:
            structure_ratio = (functions + classes) / (code_lines / 50)
            if structure_ratio > 0.5:
                quality_score += min(1.0, structure_ratio - 0.5)
        
        quality_score = max(0.0, min(10.0, quality_score))
        
        return {
            'file_path': file_path,
            'lines': total_lines,
            'code_lines': code_lines,
            'complexity': complexity,
            'functions': functions,
            'classes': classes,
            'imports': imports,
            'quality_score': quality_score,
            'issues': max(0, complexity - 15),
            'analysis_time': time.time()
        }
    
    async def process_file(self, file_path: Path) -> Optional[FileMetrics]:
        """Process single file with async I/O and concurrency control"""
        async with self.semaphore:
            try:
                # Async file reading
                content = await self.read_file_async(file_path)
                if not content:
                    return None
                
                # Analyze content
                analysis_data = await self.analyze_file_content(content, file_path)
                
                # Create FileMetrics
                return FileMetrics(
                    file_path=analysis_data['file_path'],
                    lines=analysis_data['lines'],
                    code_lines=analysis_data['code_lines'],
                    complexity=analysis_data['complexity'],
                    functions=analysis_data['functions'],
                    classes=analysis_data['classes'],
                    quality_score=analysis_data['quality_score'],
                    issues=analysis_data['issues'],
                    analysis_time=analysis_data['analysis_time']
                )
                
            except Exception as e:
                logger.error(f"Error processing {file_path}: {e}")
                return None


class AsyncUnifiedAnalysisEngine:
    """Enhanced async unified analysis engine"""
    
    def __init__(self, config: Optional[AsyncAnalysisConfig] = None):
        self.config = config or AsyncAnalysisConfig()
        self.cache = {} if self.config.enable_caching else None
        self.file_processor = None
        
    @asynccontextmanager
    async def _get_file_processor(self):
        """Context manager for file processor"""
        processor = AsyncFileProcessor(self.config)
        try:
            yield processor
        finally:
            await processor.__aexit__(None, None, None)
    
    async def discover_files_async(self, project_path: Path) -> AsyncIterator[Path]:
        """Async file discovery with streaming"""
        def _discover_files():
            """Sync file discovery for thread executor"""
            files = []
            for file_path in project_path.rglob("*.py"):
                # Skip virtual environments and cache directories
                if any(skip in str(file_path) for skip in ['.venv', 'venv', '__pycache__', '.git']):
                    continue
                
                # Skip test files if not included
                if not self.config.include_tests and ('test_' in file_path.name or '/tests/' in str(file_path)):
                    continue
                
                files.append(file_path)
            return files
        
        # Run file discovery in thread executor to avoid blocking
        loop = asyncio.get_event_loop()
        files = await loop.run_in_executor(None, _discover_files)
        
        # Stream files in batches
        for i in range(0, len(files), self.config.batch_size):
            batch = files[i:i + self.config.batch_size]
            for file_path in batch:
                yield file_path
    
    async def analyze_files_batch(self, files: List[Path]) -> List[FileMetrics]:
        """Analyze a batch of files concurrently"""
        async with self._get_file_processor() as processor:
            # Create tasks for concurrent processing
            tasks = [processor.process_file(file_path) for file_path in files]
            
            # Execute with timeout
            try:
                results = await asyncio.wait_for(
                    asyncio.gather(*tasks, return_exceptions=True),
                    timeout=self.config.analysis_timeout
                )
            except asyncio.TimeoutError:
                logger.warning(f"Batch analysis timed out for {len(files)} files")
                return []
            
            # Filter valid results
            file_metrics = []
            for result in results:
                if isinstance(result, FileMetrics):
                    file_metrics.append(result)
                elif isinstance(result, Exception):
                    logger.error(f"File analysis error: {result}")
            
            return file_metrics
    
    async def analyze_project_streaming(self, path: Union[str, Path]) -> AsyncIterator[FileMetrics]:
        """Stream analysis results as they become available"""
        project_path = Path(path)
        
        if not project_path.exists():
            raise ValueError(f"Project path does not exist: {path}")
        
        logger.info(f"Starting streaming analysis of: {project_path}")
        
        # Process files in batches
        current_batch = []
        
        async for file_path in self.discover_files_async(project_path):
            current_batch.append(file_path)
            
            # Process batch when full
            if len(current_batch) >= self.config.batch_size:
                file_metrics = await self.analyze_files_batch(current_batch)
                for metrics in file_metrics:
                    yield metrics
                current_batch = []
        
        # Process remaining files
        if current_batch:
            file_metrics = await self.analyze_files_batch(current_batch)
            for metrics in file_metrics:
                yield metrics
    
    async def analyze_project(self, path: Union[str, Path]) -> AnalysisResult:
        """Full async project analysis with enhanced performance"""
        start_time = time.time()
        project_path = Path(path)
        
        logger.info(f"Starting async analysis of: {project_path}")
        
        # Collect all file metrics
        file_metrics = []
        
        if self.config.enable_streaming:
            # Use streaming analysis for better memory efficiency
            async for metrics in self.analyze_project_streaming(project_path):
                file_metrics.append(metrics)
        else:
            # Batch processing for smaller projects
            files = []
            async for file_path in self.discover_files_async(project_path):
                files.append(file_path)
            
            file_metrics = await self.analyze_files_batch(files)
        
        # Calculate directory metrics
        directory_metrics = await self._calculate_directory_metrics(file_metrics)
        
        # Calculate project metrics
        project_metrics = self._calculate_project_metrics(file_metrics, directory_metrics)
        
        analysis_time = time.time() - start_time
        
        result = AnalysisResult(
            project_metrics=project_metrics,
            file_metrics=file_metrics,
            directory_metrics=directory_metrics,
            analysis_time=analysis_time,
            files_analyzed=len(file_metrics)
        )
        
        logger.info(f"Async analysis completed in {analysis_time:.2f}s")
        logger.info(f"Analyzed {len(file_metrics)} files, {len(directory_metrics)} directories")
        
        return result
    
    async def _calculate_directory_metrics(self, file_metrics: List[FileMetrics]) -> List[DirectoryMetrics]:
        """Calculate directory metrics asynchronously"""
        # Group files by directory
        dir_files = {}
        for fm in file_metrics:
            dir_path = Path(fm.file_path).parent
            if str(dir_path) not in dir_files:
                dir_files[str(dir_path)] = []
            dir_files[str(dir_path)].append(fm)
        
        # Calculate metrics for each directory
        directory_metrics = []
        for dir_path, files in dir_files.items():
            if len(files) == 0:
                continue
            
            total_lines = sum(fm.lines for fm in files)
            avg_complexity = sum(fm.complexity for fm in files) / len(files)
            avg_quality = sum(fm.quality_score for fm in files) / len(files)
            total_issues = sum(fm.issues for fm in files)
            
            dm = DirectoryMetrics(
                directory_path=dir_path,
                file_count=len(files),
                total_lines=total_lines,
                average_complexity=avg_complexity,
                average_quality=avg_quality,
                total_issues=total_issues,
                files=files
            )
            
            directory_metrics.append(dm)
        
        return directory_metrics
    
    def _calculate_project_metrics(self, file_metrics: List[FileMetrics], 
                                 directory_metrics: List[DirectoryMetrics]) -> ProjectMetrics:
        """Calculate project-level metrics"""
        if not file_metrics:
            return ProjectMetrics(
                total_files=0,
                total_lines=0,
                average_complexity=0,
                max_complexity=0,
                average_quality=0,
                total_issues=0,
                quality_score=0
            )
        
        total_files = len(file_metrics)
        total_lines = sum(fm.lines for fm in file_metrics)
        avg_complexity = sum(fm.complexity for fm in file_metrics) / total_files
        max_complexity = max(fm.complexity for fm in file_metrics)
        avg_quality = sum(fm.quality_score for fm in file_metrics) / total_files
        total_issues = sum(fm.issues for fm in file_metrics)
        
        # Overall quality score
        quality_score = avg_quality
        if avg_complexity > 20:
            quality_score -= 1.0
        if total_issues > total_files * 2:
            quality_score -= 1.0
        
        quality_score = max(0.0, min(10.0, quality_score))
        
        return ProjectMetrics(
            total_files=total_files,
            total_lines=total_lines,
            average_complexity=avg_complexity,
            max_complexity=max_complexity,
            average_quality=avg_quality,
            total_issues=total_issues,
            quality_score=quality_score,
            directories=directory_metrics,
            files=file_metrics
        )


# Convenience functions for backward compatibility
async def analyze_project_async(path: Union[str, Path], config: Optional[AsyncAnalysisConfig] = None) -> AnalysisResult:
    """Async project analysis function"""
    engine = AsyncUnifiedAnalysisEngine(config)
    return await engine.analyze_project(path)


def analyze_project_sync(path: Union[str, Path], config: Optional[AsyncAnalysisConfig] = None) -> AnalysisResult:
    """Synchronous wrapper for backward compatibility"""
    return asyncio.run(analyze_project_async(path, config))
