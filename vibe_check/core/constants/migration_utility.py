"""
Configuration Migration Utility
===============================

Provides utilities to migrate from the old fragmented configuration system
to the new unified configuration architecture.
"""

import logging
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import yaml

from .unified_config import UnifiedConfig, UnifiedConfigManager
from ..config.security import SecureConfigManager

logger = logging.getLogger(__name__)


class ConfigurationMigrator:
    """
    Migrates configurations from old fragmented system to unified system.
    
    Handles migration of:
    - Legacy core/config.py configurations
    - VCS-specific configurations
    - Enterprise monitoring configurations
    - Hardcoded values to centralized constants
    """
    
    def __init__(self):
        """Initialize configuration migrator."""
        self.unified_manager = UnifiedConfigManager()
        self.migration_log: List[str] = []
        
    def migrate_legacy_config(self, legacy_config_path: Path) -> Tuple[UnifiedConfig, List[str]]:
        """
        Migrate legacy configuration file to unified format.
        
        Args:
            legacy_config_path: Path to legacy configuration file
            
        Returns:
            Tuple of (unified_config, migration_warnings)
        """
        self.migration_log.clear()
        
        try:
            # Load legacy configuration
            with open(legacy_config_path, 'r') as f:
                legacy_config = yaml.safe_load(f)
            
            if not isinstance(legacy_config, dict):
                raise ValueError("Invalid legacy configuration format")
            
            # Create new unified configuration
            unified_config = UnifiedConfig()
            
            # Migrate analysis settings
            self._migrate_analysis_settings(legacy_config, unified_config)
            
            # Migrate tool settings
            self._migrate_tool_settings(legacy_config, unified_config)
            
            # Migrate output settings
            self._migrate_output_settings(legacy_config, unified_config)
            
            # Migrate performance settings
            self._migrate_performance_settings(legacy_config, unified_config)
            
            # Validate migrated configuration
            if not unified_config.validate():
                self.migration_log.extend([
                    f"Validation error: {error}" for error in unified_config.validation_errors
                ])
            
            self.migration_log.append(f"Successfully migrated configuration from {legacy_config_path}")
            return unified_config, self.migration_log.copy()
            
        except Exception as e:
            error_msg = f"Failed to migrate configuration: {e}"
            self.migration_log.append(error_msg)
            logger.error(error_msg)
            return UnifiedConfig(), self.migration_log.copy()
    
    def _migrate_analysis_settings(self, legacy_config: Dict[str, Any], unified_config: UnifiedConfig) -> None:
        """Migrate analysis-related settings."""
        # File extensions
        if 'file_extensions' in legacy_config:
            extensions = legacy_config['file_extensions']
            if isinstance(extensions, list):
                unified_config.analysis.file_extensions = extensions
                self.migration_log.append("Migrated file_extensions")
        
        # Exclude patterns
        if 'exclude_patterns' in legacy_config:
            patterns = legacy_config['exclude_patterns']
            if isinstance(patterns, list):
                unified_config.analysis.exclude_patterns = patterns
                self.migration_log.append("Migrated exclude_patterns")
        
        # Analysis flags
        if 'analyze_docs' in legacy_config:
            unified_config.analysis.analyze_documentation = legacy_config['analyze_docs']
            self.migration_log.append("Migrated analyze_docs -> analyze_documentation")
        
        # Thresholds (from various sources)
        threshold_mappings = {
            'max_line_length': 'max_line_length',
            'complexity_threshold': 'complexity_threshold',
            'max_function_length': 'max_function_length',
        }
        
        for legacy_key, unified_key in threshold_mappings.items():
            if legacy_key in legacy_config:
                setattr(unified_config.analysis, unified_key, legacy_config[legacy_key])
                self.migration_log.append(f"Migrated {legacy_key}")
    
    def _migrate_tool_settings(self, legacy_config: Dict[str, Any], unified_config: UnifiedConfig) -> None:
        """Migrate tool-related settings."""
        if 'tools' not in legacy_config:
            return
        
        tools_config = legacy_config['tools']
        if not isinstance(tools_config, dict):
            return
        
        # Migrate individual tool configurations
        for tool_name, tool_config in tools_config.items():
            if not isinstance(tool_config, dict):
                continue
            
            # Handle special cases
            if tool_name == 'unified_analyzer':
                # Migrate unified analyzer settings to performance config
                if 'max_workers' in tool_config:
                    unified_config.performance.max_workers = tool_config['max_workers']
                    self.migration_log.append("Migrated unified_analyzer.max_workers -> performance.max_workers")
                
                if 'use_async' in tool_config:
                    unified_config.performance.async_enabled = tool_config['use_async']
                    self.migration_log.append("Migrated unified_analyzer.use_async -> performance.async_enabled")
                
                if 'enable_caching' in tool_config:
                    unified_config.performance.cache_enabled = tool_config['enable_caching']
                    self.migration_log.append("Migrated unified_analyzer.enable_caching -> performance.cache_enabled")
            
            elif tool_name == 'complexity':
                # Migrate complexity tool settings
                if 'threshold' in tool_config:
                    unified_config.analysis.complexity_threshold = tool_config['threshold']
                    self.migration_log.append("Migrated complexity.threshold -> analysis.complexity_threshold")
            
            else:
                # Migrate external tool settings
                if tool_name not in unified_config.tools.external_tools:
                    from ..constants.config_schema import ExternalToolConfig
                    unified_config.tools.external_tools[tool_name] = ExternalToolConfig()
                
                tool_obj = unified_config.tools.external_tools[tool_name]
                
                if 'enabled' in tool_config:
                    tool_obj.enabled = tool_config['enabled']
                
                if 'args' in tool_config:
                    tool_obj.arguments = tool_config['args']
                
                if 'timeout' in tool_config:
                    tool_obj.timeout_seconds = tool_config['timeout']
                
                self.migration_log.append(f"Migrated external tool: {tool_name}")
    
    def _migrate_output_settings(self, legacy_config: Dict[str, Any], unified_config: UnifiedConfig) -> None:
        """Migrate output-related settings."""
        output_mappings = {
            'output_format': 'format',
            'output_dir': 'output_directory',
            'verbose': 'verbose',
            'quiet': 'quiet',
            'show_progress': 'show_progress',
        }
        
        for legacy_key, unified_key in output_mappings.items():
            if legacy_key in legacy_config:
                setattr(unified_config.output, unified_key, legacy_config[legacy_key])
                self.migration_log.append(f"Migrated {legacy_key} -> output.{unified_key}")
    
    def _migrate_performance_settings(self, legacy_config: Dict[str, Any], unified_config: UnifiedConfig) -> None:
        """Migrate performance-related settings."""
        performance_mappings = {
            'max_workers': 'max_workers',
            'timeout': 'timeout_seconds',
            'memory_limit': 'memory_limit_mb',
            'parallel_analysis': 'parallel_enabled',
        }
        
        for legacy_key, unified_key in performance_mappings.items():
            if legacy_key in legacy_config:
                setattr(unified_config.performance, unified_key, legacy_config[legacy_key])
                self.migration_log.append(f"Migrated {legacy_key} -> performance.{unified_key}")
    
    def migrate_vcs_config(self, vcs_config_path: Path) -> Tuple[UnifiedConfig, List[str]]:
        """
        Migrate VCS-specific configuration to unified format.
        
        Args:
            vcs_config_path: Path to VCS configuration file
            
        Returns:
            Tuple of (unified_config, migration_warnings)
        """
        self.migration_log.clear()
        
        try:
            # This would load VCS-specific configuration
            # For now, we'll create a placeholder implementation
            unified_config = UnifiedConfig()
            
            self.migration_log.append(f"VCS configuration migration from {vcs_config_path} - placeholder implementation")
            
            return unified_config, self.migration_log.copy()
            
        except Exception as e:
            error_msg = f"Failed to migrate VCS configuration: {e}"
            self.migration_log.append(error_msg)
            logger.error(error_msg)
            return UnifiedConfig(), self.migration_log.copy()
    
    def create_migration_report(self, migration_logs: List[str]) -> str:
        """
        Create a detailed migration report.
        
        Args:
            migration_logs: List of migration log entries
            
        Returns:
            Formatted migration report
        """
        report_lines = [
            "Configuration Migration Report",
            "=" * 40,
            "",
            f"Total migration steps: {len(migration_logs)}",
            "",
            "Migration Details:",
            "-" * 20,
        ]
        
        for i, log_entry in enumerate(migration_logs, 1):
            report_lines.append(f"{i:2d}. {log_entry}")
        
        report_lines.extend([
            "",
            "Next Steps:",
            "-" * 10,
            "1. Review the migrated configuration for accuracy",
            "2. Test the new configuration with your project",
            "3. Update any scripts or tools that reference old config paths",
            "4. Consider enabling configuration encryption for sensitive data",
            "",
            "For more information, see the unified configuration documentation."
        ])
        
        return "\n".join(report_lines)
    
    def backup_legacy_config(self, config_path: Path) -> Path:
        """
        Create a backup of legacy configuration before migration.
        
        Args:
            config_path: Path to configuration file to backup
            
        Returns:
            Path to backup file
        """
        backup_path = config_path.with_suffix(f"{config_path.suffix}.backup")
        
        try:
            import shutil
            shutil.copy2(config_path, backup_path)
            logger.info(f"Created backup: {backup_path}")
            return backup_path
            
        except Exception as e:
            logger.error(f"Failed to create backup: {e}")
            raise


def migrate_configuration_file(
    legacy_config_path: Path,
    output_path: Optional[Path] = None,
    create_backup: bool = True,
    encrypt_sensitive: bool = False,
    password: Optional[str] = None
) -> bool:
    """
    Migrate a legacy configuration file to unified format.
    
    Args:
        legacy_config_path: Path to legacy configuration file
        output_path: Path for migrated configuration (defaults to same directory)
        create_backup: Whether to create backup of original file
        encrypt_sensitive: Whether to encrypt sensitive configuration data
        password: Password for encryption (required if encrypt_sensitive is True)
        
    Returns:
        True if migration was successful
    """
    try:
        migrator = ConfigurationMigrator()
        
        # Create backup if requested
        if create_backup:
            migrator.backup_legacy_config(legacy_config_path)
        
        # Perform migration
        unified_config, migration_logs = migrator.migrate_legacy_config(legacy_config_path)
        
        # Determine output path
        if output_path is None:
            output_path = legacy_config_path.parent / "vibe-check-unified.yaml"
        
        # Save migrated configuration
        success = migrator.unified_manager.save_configuration(
            unified_config, 
            output_path, 
            password if encrypt_sensitive else None,
            encrypt_sensitive
        )
        
        if success:
            # Generate and save migration report
            report = migrator.create_migration_report(migration_logs)
            report_path = output_path.with_suffix('.migration-report.txt')
            
            with open(report_path, 'w') as f:
                f.write(report)
            
            logger.info(f"Migration completed successfully")
            logger.info(f"Migrated configuration: {output_path}")
            logger.info(f"Migration report: {report_path}")
            
            return True
        else:
            logger.error("Failed to save migrated configuration")
            return False
            
    except Exception as e:
        logger.error(f"Configuration migration failed: {e}")
        return False


def validate_migration(original_path: Path, migrated_path: Path) -> List[str]:
    """
    Validate that migration preserved important configuration values.
    
    Args:
        original_path: Path to original configuration file
        migrated_path: Path to migrated configuration file
        
    Returns:
        List of validation issues (empty if validation passed)
    """
    issues = []
    
    try:
        # Load both configurations
        with open(original_path, 'r') as f:
            original_config = yaml.safe_load(f)
        
        manager = UnifiedConfigManager()
        migrated_config = manager.load_configuration(project_config_path=migrated_path)
        
        # Check key values are preserved
        # This is a simplified validation - in practice, you'd want more comprehensive checks
        
        if 'file_extensions' in original_config:
            if original_config['file_extensions'] != migrated_config.analysis.file_extensions:
                issues.append("File extensions mismatch")
        
        if 'tools' in original_config and 'ruff' in original_config['tools']:
            ruff_config = original_config['tools']['ruff']
            if 'enabled' in ruff_config:
                migrated_ruff = migrated_config.tools.external_tools.get('ruff')
                if not migrated_ruff or migrated_ruff.enabled != ruff_config['enabled']:
                    issues.append("Ruff tool configuration mismatch")
        
        return issues
        
    except Exception as e:
        return [f"Validation failed: {e}"]
