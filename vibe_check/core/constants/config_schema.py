"""
Configuration Schema Definitions
================================

This module provides centralized configuration schemas and validation for the
Vibe Check project, ensuring consistent configuration management across all components.

Strategic Benefits:
- Centralized configuration validation
- Type safety for configuration values
- Consistent configuration structure
- Clear documentation of configuration options
- Hierarchical configuration support
"""

from typing import Final, Dict, Any, List, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path

from . import (
    AnalysisProfile, OutputFormat, SeverityLevel,
    AnalysisThresholds, PerformanceConfig, LoggingConfig
)


# =============================================================================
# CONFIGURATION ENUMS
# =============================================================================

class ConfigScope(Enum):
    """Configuration scope levels."""
    SYSTEM = "system"           # System-wide defaults
    PROJECT = "project"         # Project-specific settings
    USER = "user"              # User preferences
    RUNTIME = "runtime"         # Runtime overrides


class ConfigFormat(Enum):
    """Supported configuration file formats."""
    YAML = "yaml"
    JSON = "json"
    TOML = "toml"


# =============================================================================
# CORE CONFIGURATION SCHEMAS
# =============================================================================

@dataclass
class AnalysisConfig:
    """Analysis configuration schema."""
    
    # Analysis scope
    file_extensions: List[str] = field(default_factory=lambda: [".py"])
    exclude_patterns: List[str] = field(default_factory=list)
    include_patterns: List[str] = field(default_factory=list)
    analyze_docs: bool = True
    use_gitignore: bool = True
    
    # Analysis profile
    profile: AnalysisProfile = AnalysisProfile.STANDARD
    enabled_tools: List[str] = field(default_factory=list)
    disabled_tools: List[str] = field(default_factory=list)
    
    # Thresholds
    max_line_length: int = AnalysisThresholds.MAX_LINE_LENGTH
    complexity_threshold: int = AnalysisThresholds.CYCLOMATIC_COMPLEXITY
    cognitive_complexity_threshold: int = AnalysisThresholds.COGNITIVE_COMPLEXITY
    max_function_length: int = AnalysisThresholds.MAX_FUNCTION_LENGTH
    max_nesting_depth: int = AnalysisThresholds.MAX_NESTING_DEPTH
    max_parameters: int = AnalysisThresholds.MAX_PARAMETERS
    
    # Documentation requirements
    min_documentation_coverage: float = AnalysisThresholds.MIN_DOCUMENTATION_COVERAGE
    require_module_docstrings: bool = True
    require_class_docstrings: bool = True
    require_function_docstrings: bool = True


@dataclass
class ToolConfig:
    """Individual tool configuration schema."""
    
    enabled: bool = True
    timeout: int = AnalysisThresholds.DEFAULT_TIMEOUT_SECONDS
    args: List[str] = field(default_factory=list)
    config_file: Optional[str] = None
    custom_rules: Dict[str, Any] = field(default_factory=dict)
    severity_overrides: Dict[str, SeverityLevel] = field(default_factory=dict)


@dataclass
class ToolsConfig:
    """Tools configuration schema."""
    
    # Linting tools
    ruff: ToolConfig = field(default_factory=ToolConfig)
    pylint: ToolConfig = field(default_factory=ToolConfig)
    flake8: ToolConfig = field(default_factory=ToolConfig)
    
    # Type checking
    mypy: ToolConfig = field(default_factory=ToolConfig)
    pyright: ToolConfig = field(default_factory=ToolConfig)
    
    # Security
    bandit: ToolConfig = field(default_factory=ToolConfig)
    safety: ToolConfig = field(default_factory=ToolConfig)
    
    # Formatting (for analysis, not modification)
    black: ToolConfig = field(default_factory=ToolConfig)
    isort: ToolConfig = field(default_factory=ToolConfig)
    
    # Testing
    pytest: ToolConfig = field(default_factory=ToolConfig)
    coverage: ToolConfig = field(default_factory=ToolConfig)


@dataclass
class OutputConfig:
    """Output configuration schema."""
    
    # Output formats
    formats: List[OutputFormat] = field(default_factory=lambda: [OutputFormat.JSON])
    output_dir: str = "vibe_check_output"
    
    # Report generation
    generate_html_report: bool = True
    generate_json_report: bool = True
    generate_markdown_summary: bool = True
    include_source_code: bool = False
    
    # Visualization
    generate_dependency_graph: bool = True
    generate_complexity_heatmap: bool = True
    generate_coverage_charts: bool = True
    
    # File naming
    timestamp_reports: bool = True
    report_prefix: str = "vibe_check"


@dataclass
class PerformanceConfigSchema:
    """Performance configuration schema."""
    
    # Parallel processing
    parallel: bool = True
    max_workers: int = PerformanceConfig.DEFAULT_MAX_WORKERS
    
    # Memory management
    max_memory_usage_mb: int = PerformanceConfig.MAX_MEMORY_USAGE_MB
    memory_warning_threshold: float = PerformanceConfig.MEMORY_WARNING_THRESHOLD
    
    # Caching
    cache_enabled: bool = True
    cache_dir: str = PerformanceConfig.CACHE_EXPIRY_HOURS
    cache_expiry_hours: int = 24
    max_cache_size_mb: int = PerformanceConfig.MAX_CACHE_SIZE_MB
    
    # Timeouts
    analysis_timeout: int = AnalysisThresholds.MAX_ANALYSIS_TIME_SECONDS
    network_timeout: int = PerformanceConfig.NETWORK_TIMEOUT_SECONDS
    file_timeout: int = PerformanceConfig.FILE_OPERATION_TIMEOUT_SECONDS
    
    # Profiling
    enable_profiling: bool = False
    profile_output_file: str = "vibe_check_profile.prof"


@dataclass
class LoggingConfigSchema:
    """Logging configuration schema."""
    
    # Log levels
    level: str = LoggingConfig.DEFAULT_LEVEL
    console_level: str = LoggingConfig.DEFAULT_LEVEL
    file_level: str = LoggingConfig.DEBUG
    
    # Log formatting
    format: str = LoggingConfig.DEFAULT_FORMAT
    detailed_format: str = LoggingConfig.DETAILED_FORMAT
    use_colors: bool = True
    
    # Log files
    log_to_file: bool = False
    log_file: str = "vibe_check.log"
    max_log_size_mb: int = LoggingConfig.MAX_LOG_SIZE_MB
    max_log_files: int = LoggingConfig.MAX_LOG_FILES
    
    # Structured logging
    structured_logging: bool = False
    json_format: bool = False


@dataclass
class MonitoringConfig:
    """Monitoring and metrics configuration schema."""
    
    # Metrics collection
    enabled: bool = False
    collect_system_metrics: bool = True
    collect_performance_metrics: bool = True
    collect_quality_metrics: bool = True
    
    # Metrics export
    export_prometheus: bool = False
    prometheus_port: int = 8000
    export_json: bool = True
    metrics_file: str = "vibe_check_metrics.json"
    
    # Alerting
    enable_alerts: bool = False
    alert_thresholds: Dict[str, float] = field(default_factory=dict)


# =============================================================================
# MAIN CONFIGURATION SCHEMA
# =============================================================================

@dataclass
class VibeCheckConfig:
    """Main Vibe Check configuration schema."""
    
    # Metadata
    version: str = "1.0"
    config_format: ConfigFormat = ConfigFormat.YAML
    
    # Core configuration sections
    analysis: AnalysisConfig = field(default_factory=AnalysisConfig)
    tools: ToolsConfig = field(default_factory=ToolsConfig)
    output: OutputConfig = field(default_factory=OutputConfig)
    performance: PerformanceConfigSchema = field(default_factory=PerformanceConfigSchema)
    logging: LoggingConfigSchema = field(default_factory=LoggingConfigSchema)
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)
    
    # Advanced features
    plugins: Dict[str, Any] = field(default_factory=dict)
    integrations: Dict[str, Any] = field(default_factory=dict)
    custom_rules: Dict[str, Any] = field(default_factory=dict)


# =============================================================================
# CONFIGURATION VALIDATION
# =============================================================================

class ConfigValidator:
    """Configuration validation utilities."""
    
    @staticmethod
    def validate_file_extensions(extensions: List[str]) -> bool:
        """Validate file extensions format."""
        for ext in extensions:
            if not ext.startswith('.') or len(ext) < 2:
                return False
        return True
    
    @staticmethod
    def validate_thresholds(config: AnalysisConfig) -> List[str]:
        """Validate analysis thresholds."""
        errors = []
        
        if config.max_line_length < 50 or config.max_line_length > 200:
            errors.append("max_line_length must be between 50 and 200")
        
        if config.complexity_threshold < 1 or config.complexity_threshold > 50:
            errors.append("complexity_threshold must be between 1 and 50")
        
        if config.min_documentation_coverage < 0 or config.min_documentation_coverage > 1:
            errors.append("min_documentation_coverage must be between 0 and 1")
        
        return errors
    
    @staticmethod
    def validate_performance_config(config: PerformanceConfigSchema) -> List[str]:
        """Validate performance configuration."""
        errors = []
        
        if config.max_workers < 1 or config.max_workers > 32:
            errors.append("max_workers must be between 1 and 32")
        
        if config.max_memory_usage_mb < 100:
            errors.append("max_memory_usage_mb must be at least 100")
        
        if config.memory_warning_threshold < 0.1 or config.memory_warning_threshold > 1.0:
            errors.append("memory_warning_threshold must be between 0.1 and 1.0")
        
        return errors
    
    @classmethod
    def validate_config(cls, config: VibeCheckConfig) -> List[str]:
        """Validate complete configuration."""
        errors = []
        
        # Validate file extensions
        if not cls.validate_file_extensions(config.analysis.file_extensions):
            errors.append("Invalid file extensions format")
        
        # Validate thresholds
        errors.extend(cls.validate_thresholds(config.analysis))
        
        # Validate performance config
        errors.extend(cls.validate_performance_config(config.performance))
        
        return errors


# =============================================================================
# CONFIGURATION DEFAULTS
# =============================================================================

class ConfigDefaults:
    """Default configuration values organized by profile."""
    
    MINIMAL_PROFILE: Final[Dict[str, Any]] = {
        "analysis": {
            "profile": "minimal",
            "enabled_tools": ["ruff"],
            "analyze_docs": False
        },
        "performance": {
            "max_workers": 2,
            "cache_enabled": True
        }
    }
    
    STANDARD_PROFILE: Final[Dict[str, Any]] = {
        "analysis": {
            "profile": "standard",
            "enabled_tools": ["ruff", "mypy", "bandit"],
            "analyze_docs": True
        },
        "performance": {
            "max_workers": 4,
            "cache_enabled": True
        }
    }
    
    COMPREHENSIVE_PROFILE: Final[Dict[str, Any]] = {
        "analysis": {
            "profile": "comprehensive",
            "enabled_tools": ["ruff", "pylint", "mypy", "bandit", "safety", "coverage"],
            "analyze_docs": True
        },
        "performance": {
            "max_workers": 8,
            "cache_enabled": True
        },
        "monitoring": {
            "enabled": True
        }
    }
    
    @classmethod
    def get_profile_defaults(cls, profile: AnalysisProfile) -> Dict[str, Any]:
        """Get default configuration for a specific profile."""
        profile_map = {
            AnalysisProfile.MINIMAL: cls.MINIMAL_PROFILE,
            AnalysisProfile.STANDARD: cls.STANDARD_PROFILE,
            AnalysisProfile.COMPREHENSIVE: cls.COMPREHENSIVE_PROFILE
        }
        return profile_map.get(profile, cls.STANDARD_PROFILE)


# =============================================================================
# EXPORTS
# =============================================================================

__all__ = [
    # Enums
    "ConfigScope",
    "ConfigFormat",
    
    # Configuration schemas
    "AnalysisConfig",
    "ToolConfig", 
    "ToolsConfig",
    "OutputConfig",
    "PerformanceConfigSchema",
    "LoggingConfigSchema",
    "MonitoringConfig",
    "VibeCheckConfig",
    
    # Validation
    "ConfigValidator",
    
    # Defaults
    "ConfigDefaults"
]
