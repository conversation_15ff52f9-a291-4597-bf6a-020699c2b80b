"""
Terminology Standardization Guide
=================================

This module defines standardized terminology for the Vibe Check project to ensure
consistency across all components, documentation, and user interfaces.

Strategic Benefits:
- Eliminates naming conflicts and ambiguous terminology
- Provides canonical terms for domain concepts
- Ensures consistent user experience
- Facilitates code maintenance and collaboration
"""

from typing import Dict, Final, Optional, Protocol
from enum import Enum


# =============================================================================
# DOMAIN TERMINOLOGY STANDARDS
# =============================================================================

class AnalysisTerms:
    """Standardized terms for analysis concepts."""
    
    # Core analysis concepts
    ANALYZER: Final[str] = "analyzer"  # Use instead of: analyse, analysis_engine
    ANALYSIS: Final[str] = "analysis"  # Use instead of: analyse, analyzing
    ANALYZE: Final[str] = "analyze"    # Use instead of: analyse
    
    # Analysis components
    ENGINE: Final[str] = "engine"      # For core processing components
    PROCESSOR: Final[str] = "processor" # For data processing components
    HANDLER: Final[str] = "handler"    # For event/request handling
    MANAGER: Final[str] = "manager"    # For resource/lifecycle management
    
    # Analysis results
    RESULT: Final[str] = "result"      # Use instead of: output, outcome
    ISSUE: Final[str] = "issue"        # Use instead of: error, problem, finding
    METRIC: Final[str] = "metric"      # Use instead of: measurement, measure
    REPORT: Final[str] = "report"      # Use instead of: summary, output


class ConfigurationTerms:
    """Standardized terms for configuration concepts."""
    
    # Configuration concepts
    CONFIG: Final[str] = "config"      # Use instead of: configuration, settings, options
    PRESET: Final[str] = "preset"      # For predefined configurations
    PROFILE: Final[str] = "profile"    # For analysis profiles
    SCHEMA: Final[str] = "schema"      # For configuration validation
    
    # Configuration components
    LOADER: Final[str] = "loader"      # For loading configurations
    VALIDATOR: Final[str] = "validator" # For validating configurations
    MERGER: Final[str] = "merger"      # For merging configurations


class ComponentTerms:
    """Standardized terms for component concepts."""

    # Component types
    GENERATOR: Final[str] = "generator" # Use instead of: creator, builder, factory
    COLLECTOR: Final[str] = "collector" # For data collection
    AGGREGATOR: Final[str] = "aggregator" # For data aggregation
    FORMATTER: Final[str] = "formatter" # For output formatting

    # Component roles
    REGISTRY: Final[str] = "registry"   # For component registration
    FACTORY: Final[str] = "factory"     # For object creation (when appropriate)
    ADAPTER: Final[str] = "adapter"     # For interface adaptation
    BRIDGE: Final[str] = "bridge"       # For connecting different systems

    # Plugin system terminology
    PLUGIN: Final[str] = "plugin"       # Use singular for individual plugins
    PLUGINS: Final[str] = "plugins"     # Use plural for collections
    PLUGIN_MANAGER: Final[str] = "plugin_manager"  # Standardized manager name
    PLUGIN_REGISTRY: Final[str] = "plugin_registry"  # Standardized registry name


class ErrorTerms:
    """Standardized terms for error handling concepts."""
    
    # Error concepts
    ERROR: Final[str] = "error"         # Use instead of: exception, failure
    WARNING: Final[str] = "warning"     # For non-critical issues
    EXCEPTION: Final[str] = "exception" # For Python exceptions specifically
    
    # Error handling
    HANDLER: Final[str] = "handler"     # For error handling
    AGGREGATOR: Final[str] = "aggregator" # For error collection
    REPORTER: Final[str] = "reporter"   # For error reporting


# =============================================================================
# NAMING CONVENTION STANDARDS
# =============================================================================

class NamingConventions:
    """Standardized naming conventions."""
    
    # Class naming patterns
    ANALYZER_SUFFIX: Final[str] = "Analyzer"
    MANAGER_SUFFIX: Final[str] = "Manager"
    HANDLER_SUFFIX: Final[str] = "Handler"
    PROCESSOR_SUFFIX: Final[str] = "Processor"
    GENERATOR_SUFFIX: Final[str] = "Generator"
    ENGINE_SUFFIX: Final[str] = "Engine"
    
    # Interface naming patterns
    INTERFACE_SUFFIX: Final[str] = "Interface"
    PROTOCOL_SUFFIX: Final[str] = "Protocol"
    
    # Abstract class patterns
    ABSTRACT_PREFIX: Final[str] = "Abstract"
    BASE_PREFIX: Final[str] = "Base"
    
    # Configuration patterns
    CONFIG_SUFFIX: Final[str] = "Config"
    SETTINGS_SUFFIX: Final[str] = "Settings"
    
    @classmethod
    def get_standard_class_name(cls, base_name: str, component_type: str) -> str:
        """Generate a standard class name."""
        suffixes = {
            'analyzer': cls.ANALYZER_SUFFIX,
            'manager': cls.MANAGER_SUFFIX,
            'handler': cls.HANDLER_SUFFIX,
            'processor': cls.PROCESSOR_SUFFIX,
            'generator': cls.GENERATOR_SUFFIX,
            'engine': cls.ENGINE_SUFFIX,
            'config': cls.CONFIG_SUFFIX
        }
        
        suffix = suffixes.get(component_type.lower(), '')
        if suffix and not base_name.endswith(suffix):
            return f"{base_name}{suffix}"
        return base_name


# =============================================================================
# STANDARDIZED COMPONENT NAMES
# =============================================================================

class StandardizedNames:
    """Canonical names for common components."""
    
    # Core analysis components
    VCS_ENGINE: Final[str] = "VCSEngine"
    ANALYSIS_MANAGER: Final[str] = "AnalysisManager"
    METRICS_COLLECTOR: Final[str] = "MetricsCollector"
    REPORT_GENERATOR: Final[str] = "ReportGenerator"
    
    # Configuration components
    CONFIG_LOADER: Final[str] = "ConfigLoader"
    CONFIG_VALIDATOR: Final[str] = "ConfigValidator"
    CONFIG_MERGER: Final[str] = "ConfigMerger"
    
    # Error handling components
    ERROR_HANDLER: Final[str] = "ErrorHandler"
    ERROR_AGGREGATOR: Final[str] = "ErrorAggregator"
    ERROR_REPORTER: Final[str] = "ErrorReporter"
    
    # Tool integration components
    TOOL_RUNNER: Final[str] = "ToolRunner"
    TOOL_MANAGER: Final[str] = "ToolManager"
    PLUGIN_MANAGER: Final[str] = "PluginManager"
    
    # UI components
    CLI_HANDLER: Final[str] = "CLIHandler"
    TUI_MANAGER: Final[str] = "TUIManager"
    WEB_SERVER: Final[str] = "WebServer"
    
    # Monitoring components
    METRICS_MANAGER: Final[str] = "MetricsManager"
    DASHBOARD_MANAGER: Final[str] = "DashboardManager"
    ALERT_MANAGER: Final[str] = "AlertManager"


# =============================================================================
# USER-FACING TERMINOLOGY
# =============================================================================

class UserFacingTerms:
    """Standardized terms for user interfaces and documentation."""
    
    # Analysis concepts
    PROJECT_ANALYSIS: Final[str] = "Project Analysis"
    CODE_QUALITY: Final[str] = "Code Quality"
    QUALITY_METRICS: Final[str] = "Quality Metrics"
    ANALYSIS_REPORT: Final[str] = "Analysis Report"
    
    # Issue terminology
    CRITICAL_ISSUES: Final[str] = "Critical Issues"
    HIGH_PRIORITY: Final[str] = "High Priority"
    MEDIUM_PRIORITY: Final[str] = "Medium Priority"
    LOW_PRIORITY: Final[str] = "Low Priority"
    
    # Tool terminology
    ANALYSIS_TOOLS: Final[str] = "Analysis Tools"
    LINTING_TOOLS: Final[str] = "Linting Tools"
    TYPE_CHECKERS: Final[str] = "Type Checkers"
    SECURITY_SCANNERS: Final[str] = "Security Scanners"
    
    # Configuration terminology
    ANALYSIS_PROFILE: Final[str] = "Analysis Profile"
    CONFIGURATION_FILE: Final[str] = "Configuration File"
    PROJECT_SETTINGS: Final[str] = "Project Settings"


# =============================================================================
# DEPRECATED TERMS MAPPING
# =============================================================================

class DeprecatedTerms:
    """Mapping of deprecated terms to their standardized replacements."""
    
    REPLACEMENTS: Final[Dict[str, str]] = {
        # Analysis terms
        "analyse": "analyze",
        "analyser": "analyzer",
        "analysis_engine": "analyzer",
        
        # Configuration terms
        "configuration": "config",
        "settings": "config",
        "options": "config",
        
        # Component terms
        "creator": "generator",
        "builder": "generator",
        "factory": "generator",  # Use generator unless specifically a factory pattern
        "controller": "manager",
        "coordinator": "manager",
        
        # Error terms
        "failure": "error",
        "exception": "error",  # Use error for general concepts, exception for Python exceptions
        
        # Result terms
        "output": "result",
        "outcome": "result",
        "response": "result",
        
        # Measurement terms
        "measurement": "metric",
        "measure": "metric",
    }
    
    @classmethod
    def get_replacement(cls, deprecated_term: str) -> str:
        """Get the standardized replacement for a deprecated term."""
        return cls.REPLACEMENTS.get(deprecated_term.lower(), deprecated_term)


# =============================================================================
# VALIDATION UTILITIES
# =============================================================================

def validate_class_name(name: str, expected_type: Optional[str] = None) -> bool:
    """Validate that a class name follows naming conventions."""
    # Check PascalCase
    if not name[0].isupper():
        return False
    
    # Check for expected suffix if provided
    if expected_type:
        conventions = NamingConventions()
        expected_suffix = getattr(conventions, f"{expected_type.upper()}_SUFFIX", None)
        if expected_suffix and not name.endswith(expected_suffix):
            return False
    
    return True


def validate_function_name(name: str) -> bool:
    """Validate that a function name follows snake_case convention."""
    # Allow private functions (starting with _)
    if name.startswith('_'):
        name = name[1:]
    
    # Check snake_case pattern
    import re
    return bool(re.match(r'^[a-z][a-z0-9_]*$', name))


def suggest_standard_name(current_name: str, component_type: str) -> str:
    """Suggest a standardized name for a component."""
    # Remove common suffixes/prefixes to get base name
    base = current_name
    
    # Remove deprecated suffixes
    deprecated_suffixes = ['Engine', 'Controller', 'Coordinator', 'Creator', 'Builder']
    for suffix in deprecated_suffixes:
        if base.endswith(suffix):
            base = base[:-len(suffix)]
            break
    
    # Generate standard name
    return NamingConventions.get_standard_class_name(base, component_type)


# =============================================================================
# EXPORTS
# =============================================================================

__all__ = [
    "AnalysisTerms",
    "ConfigurationTerms", 
    "ComponentTerms",
    "ErrorTerms",
    "NamingConventions",
    "StandardizedNames",
    "UserFacingTerms",
    "DeprecatedTerms",
    "validate_class_name",
    "validate_function_name",
    "suggest_standard_name"
]
