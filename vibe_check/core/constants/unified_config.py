"""
Unified Configuration Management
===============================

Implements the unified configuration architecture based on Phase 3 audit findings,
consolidating fragmented configuration systems into a single, secure, and maintainable system.
"""

from typing import Dict, Any, Optional, List, Union, Type
from dataclasses import dataclass, field
from pathlib import Path
from enum import Enum
import logging

from .config_schema import (
    ConfigScope, ConfigFormat, AnalysisConfig,
    ToolsConfig, OutputConfig, PerformanceConfigSchema
)
from ..config.security import SecureConfigManager
from . import AnalysisThresholds, PerformanceConfig, LoggingConfig

logger = logging.getLogger(__name__)


class ConfigurationPriority(Enum):
    """Configuration source priority levels (highest to lowest)."""
    
    CLI_OVERRIDE = 1        # Command line arguments
    ENVIRONMENT = 2         # Environment variables
    PROJECT_FILE = 3        # Project-specific configuration
    USER_CONFIG = 4         # User configuration file
    SYSTEM_DEFAULT = 5      # System defaults


@dataclass
class ConfigurationSource:
    """Information about a configuration source."""
    
    source_type: ConfigurationPriority
    file_path: Optional[Path] = None
    description: str = ""
    last_modified: Optional[float] = None
    is_encrypted: bool = False


@dataclass
class UnifiedConfig:
    """
    Unified configuration container with type safety and validation.
    
    Consolidates all configuration aspects into a single, well-typed structure
    that replaces the fragmented Dict[str, Any] approach.
    """
    
    # Core analysis configuration
    analysis: AnalysisConfig = field(default_factory=AnalysisConfig)

    # External tools configuration
    tools: ToolsConfig = field(default_factory=ToolsConfig)

    # Output and reporting configuration
    output: OutputConfig = field(default_factory=OutputConfig)
    
    # Performance and resource configuration
    performance: PerformanceConfigSchema = field(default_factory=PerformanceConfigSchema)
    
    # Configuration metadata
    sources: List[ConfigurationSource] = field(default_factory=list)
    validation_errors: List[str] = field(default_factory=list)
    
    def validate(self) -> bool:
        """
        Validate the unified configuration.
        
        Returns:
            True if configuration is valid
        """
        self.validation_errors.clear()
        
        # Validate analysis configuration
        if not self._validate_analysis_config():
            return False
        
        # Validate tools configuration
        if not self._validate_tools_config():
            return False
        
        # Validate output configuration
        if not self._validate_output_config():
            return False
        
        # Validate performance configuration
        if not self._validate_performance_config():
            return False
        
        return len(self.validation_errors) == 0
    
    def _validate_analysis_config(self) -> bool:
        """Validate analysis configuration section."""
        try:
            # Check thresholds are within reasonable ranges
            if self.analysis.max_line_length < 50 or self.analysis.max_line_length > 200:
                self.validation_errors.append(
                    f"max_line_length ({self.analysis.max_line_length}) must be between 50 and 200"
                )
            
            if self.analysis.complexity_threshold < 1 or self.analysis.complexity_threshold > 50:
                self.validation_errors.append(
                    f"complexity_threshold ({self.analysis.complexity_threshold}) must be between 1 and 50"
                )
            
            return True
            
        except Exception as e:
            self.validation_errors.append(f"Analysis config validation error: {e}")
            return False
    
    def _validate_tools_config(self) -> bool:
        """Validate tools configuration section."""
        try:
            # Validate tool configurations
            for tool_name, tool_config in self.tools.external_tools.items():
                if not tool_config.enabled:
                    continue
                
                # Check timeout is reasonable
                if tool_config.timeout_seconds < 1 or tool_config.timeout_seconds > 300:
                    self.validation_errors.append(
                        f"Tool {tool_name} timeout ({tool_config.timeout_seconds}s) must be between 1 and 300"
                    )
            
            return True
            
        except Exception as e:
            self.validation_errors.append(f"Tools config validation error: {e}")
            return False
    
    def _validate_output_config(self) -> bool:
        """Validate output configuration section."""
        try:
            # Validate output paths exist or can be created
            if self.output.output_directory:
                output_path = Path(self.output.output_directory)
                if not output_path.exists():
                    try:
                        output_path.mkdir(parents=True, exist_ok=True)
                    except Exception as e:
                        self.validation_errors.append(
                            f"Cannot create output directory {output_path}: {e}"
                        )
            
            return True
            
        except Exception as e:
            self.validation_errors.append(f"Output config validation error: {e}")
            return False
    
    def _validate_performance_config(self) -> bool:
        """Validate performance configuration section."""
        try:
            # Check worker count is reasonable
            if self.performance.max_workers < 1 or self.performance.max_workers > 32:
                self.validation_errors.append(
                    f"max_workers ({self.performance.max_workers}) must be between 1 and 32"
                )
            
            # Check memory limits
            if self.performance.memory_limit_mb < 100 or self.performance.memory_limit_mb > 8192:
                self.validation_errors.append(
                    f"memory_limit_mb ({self.performance.memory_limit_mb}) must be between 100 and 8192"
                )
            
            return True
            
        except Exception as e:
            self.validation_errors.append(f"Performance config validation error: {e}")
            return False


class UnifiedConfigManager:
    """
    Unified configuration manager that consolidates all configuration systems.
    
    Replaces the fragmented configuration approach with a single, secure,
    and maintainable configuration management system.
    """
    
    def __init__(self, secure_manager: Optional[SecureConfigManager] = None):
        """Initialize unified configuration manager."""
        self.secure_manager = secure_manager or SecureConfigManager()
        self._config_cache: Optional[UnifiedConfig] = None
        self._cache_timestamp: Optional[float] = None
        
    def load_configuration(
        self, 
        cli_overrides: Optional[Dict[str, Any]] = None,
        project_config_path: Optional[Path] = None,
        user_config_path: Optional[Path] = None,
        password: Optional[str] = None
    ) -> UnifiedConfig:
        """
        Load unified configuration from all sources with proper precedence.
        
        Args:
            cli_overrides: Command line argument overrides
            project_config_path: Path to project configuration file
            user_config_path: Path to user configuration file
            password: Password for encrypted configurations
            
        Returns:
            Unified configuration object
        """
        config = UnifiedConfig()
        
        # Load configurations in reverse priority order (lowest to highest)
        
        # 1. System defaults (lowest priority)
        self._apply_system_defaults(config)
        config.sources.append(ConfigurationSource(
            source_type=ConfigurationPriority.SYSTEM_DEFAULT,
            description="Built-in system defaults"
        ))
        
        # 2. User configuration
        if user_config_path and user_config_path.exists():
            user_config = self._load_config_file(user_config_path, password)
            if user_config:
                self._merge_configuration(config, user_config)
                config.sources.append(ConfigurationSource(
                    source_type=ConfigurationPriority.USER_CONFIG,
                    file_path=user_config_path,
                    description="User configuration file",
                    last_modified=user_config_path.stat().st_mtime,
                    is_encrypted=self.secure_manager.encryption.is_encrypted_config(user_config)
                ))
        
        # 3. Project configuration
        if project_config_path and project_config_path.exists():
            project_config = self._load_config_file(project_config_path, password)
            if project_config:
                self._merge_configuration(config, project_config)
                config.sources.append(ConfigurationSource(
                    source_type=ConfigurationPriority.PROJECT_FILE,
                    file_path=project_config_path,
                    description="Project configuration file",
                    last_modified=project_config_path.stat().st_mtime,
                    is_encrypted=self.secure_manager.encryption.is_encrypted_config(project_config)
                ))
        
        # 4. Environment variables
        env_config = self._load_environment_config()
        if env_config:
            self._merge_configuration(config, env_config)
            config.sources.append(ConfigurationSource(
                source_type=ConfigurationPriority.ENVIRONMENT,
                description="Environment variables"
            ))
        
        # 5. CLI overrides (highest priority)
        if cli_overrides:
            self._apply_cli_overrides(config, cli_overrides)
            config.sources.append(ConfigurationSource(
                source_type=ConfigurationPriority.CLI_OVERRIDE,
                description="Command line arguments"
            ))
        
        # Validate final configuration
        if not config.validate():
            logger.warning(f"Configuration validation failed: {config.validation_errors}")
        
        # Cache the configuration
        import time
        self._config_cache = config
        self._cache_timestamp = time.time()
        
        logger.info(f"Loaded unified configuration from {len(config.sources)} sources")
        return config
    
    def _apply_system_defaults(self, config: UnifiedConfig) -> None:
        """Apply system default values to configuration."""
        # Analysis defaults
        config.analysis.max_line_length = AnalysisThresholds.MAX_LINE_LENGTH
        config.analysis.complexity_threshold = AnalysisThresholds.CYCLOMATIC_COMPLEXITY
        config.analysis.max_function_length = AnalysisThresholds.MAX_FUNCTION_LENGTH
        
        # Performance defaults
        config.performance.max_workers = PerformanceConfig.DEFAULT_MAX_WORKERS
        config.performance.memory_limit_mb = PerformanceConfig.MAX_MEMORY_USAGE_MB
        config.performance.timeout_seconds = PerformanceConfig.NETWORK_TIMEOUT_SECONDS
        
        # Output defaults
        config.output.format = "table"
        config.output.show_progress = True
        config.output.verbose = False
    
    def _load_config_file(self, config_path: Path, password: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Load configuration from file with security support."""
        try:
            return self.secure_manager.load_secure_config(config_path, password)
        except Exception as e:
            logger.error(f"Failed to load configuration from {config_path}: {e}")
            return None
    
    def _load_environment_config(self) -> Optional[Dict[str, Any]]:
        """Load configuration from environment variables."""
        import os
        
        env_config = {}
        
        # Map environment variables to configuration
        env_mappings = {
            'VIBE_CHECK_MAX_LINE_LENGTH': ('analysis', 'max_line_length', int),
            'VIBE_CHECK_COMPLEXITY_THRESHOLD': ('analysis', 'complexity_threshold', int),
            'VIBE_CHECK_MAX_WORKERS': ('performance', 'max_workers', int),
            'VIBE_CHECK_MEMORY_LIMIT': ('performance', 'memory_limit_mb', int),
            'VIBE_CHECK_OUTPUT_FORMAT': ('output', 'format', str),
            'VIBE_CHECK_VERBOSE': ('output', 'verbose', lambda x: x.lower() in ('true', '1', 'yes')),
        }
        
        for env_var, (section, key, converter) in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                try:
                    converted_value = converter(value)
                    if section not in env_config:
                        env_config[section] = {}
                    env_config[section][key] = converted_value
                except (ValueError, TypeError) as e:
                    logger.warning(f"Invalid environment variable {env_var}={value}: {e}")
        
        return env_config if env_config else None
    
    def _merge_configuration(self, base_config: UnifiedConfig, override_config: Dict[str, Any]) -> None:
        """Merge override configuration into base configuration."""
        # This is a simplified merge - in practice, you'd want more sophisticated merging
        for section, values in override_config.items():
            if hasattr(base_config, section) and isinstance(values, dict):
                section_obj = getattr(base_config, section)
                for key, value in values.items():
                    if hasattr(section_obj, key):
                        setattr(section_obj, key, value)
    
    def _apply_cli_overrides(self, config: UnifiedConfig, cli_overrides: Dict[str, Any]) -> None:
        """Apply CLI argument overrides to configuration."""
        # Map CLI arguments to configuration fields
        cli_mappings = {
            'max_line_length': ('analysis', 'max_line_length'),
            'complexity_threshold': ('analysis', 'complexity_threshold'),
            'max_workers': ('performance', 'max_workers'),
            'output_format': ('output', 'format'),
            'verbose': ('output', 'verbose'),
            'quiet': ('output', 'quiet'),
        }
        
        for cli_arg, (section, field) in cli_mappings.items():
            if cli_arg in cli_overrides:
                section_obj = getattr(config, section)
                setattr(section_obj, field, cli_overrides[cli_arg])
    
    def save_configuration(
        self, 
        config: UnifiedConfig, 
        config_path: Path, 
        password: Optional[str] = None,
        encrypt_sensitive: bool = True
    ) -> bool:
        """
        Save unified configuration to file.
        
        Args:
            config: Configuration to save
            config_path: Path to save configuration
            password: Password for encryption (if encrypt_sensitive is True)
            encrypt_sensitive: Whether to encrypt sensitive data
            
        Returns:
            True if saved successfully
        """
        try:
            # Convert unified config to dictionary
            config_dict = self._config_to_dict(config)
            
            # Save with security if requested
            if encrypt_sensitive and password:
                return self.secure_manager.save_secure_config(config_dict, config_path, password)
            else:
                # Save as plain YAML
                import yaml
                with open(config_path, 'w') as f:
                    yaml.dump(config_dict, f, default_flow_style=False)
                return True
                
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
            return False
    
    def _config_to_dict(self, config: UnifiedConfig) -> Dict[str, Any]:
        """Convert unified configuration to dictionary format."""
        from dataclasses import asdict
        
        # Convert dataclass to dict, excluding metadata
        config_dict = {
            'analysis': asdict(config.analysis),
            'tools': asdict(config.tools),
            'output': asdict(config.output),
            'performance': asdict(config.performance)
        }
        
        return config_dict


# Global unified configuration manager instance
_unified_config_manager: Optional[UnifiedConfigManager] = None


def get_unified_config_manager() -> UnifiedConfigManager:
    """Get the global unified configuration manager instance."""
    global _unified_config_manager
    if _unified_config_manager is None:
        _unified_config_manager = UnifiedConfigManager()
    return _unified_config_manager


def initialize_unified_config(secure_manager: Optional[SecureConfigManager] = None) -> None:
    """Initialize the global unified configuration manager."""
    global _unified_config_manager
    _unified_config_manager = UnifiedConfigManager(secure_manager)
    logger.info("Unified configuration manager initialized")
