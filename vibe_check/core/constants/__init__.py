"""
Core Constants Module
====================

This module provides centralized constants for the Vibe Check project following
the established CAW paradigm and architectural principles.

Constants Hierarchy:
- System-wide constants (this module)
- Meta-system constants (in respective meta-system directories)
- Component constants (in component directories)

Strategic Benefits:
- Eliminates hardcoded values and magic numbers
- Ensures consistency across the codebase
- Provides single source of truth for configuration values
- Enables easy maintenance and updates
- Follows established naming conventions
"""

from typing import Final
from enum import Enum
from pathlib import Path


# =============================================================================
# SYSTEM-WIDE CONSTANTS
# =============================================================================

class ProjectInfo:
    """Project metadata constants."""
    NAME: Final[str] = "Vibe Check"
    PACKAGE_NAME: Final[str] = "vibe_check"
    DESCRIPTION: Final[str] = "Advanced project analysis and code quality assessment tool"
    VERSION: Final[str] = "1.0.0"
    AUTHOR: Final[str] = "Vibe Check Development Team"


class FileExtensions:
    """Supported file extensions."""
    PYTHON: Final[str] = ".py"
    PYTHON_STUB: Final[str] = ".pyi"
    CYTHON: Final[str] = ".pyx"
    JUPYTER: Final[str] = ".ipynb"
    
    # Configuration files
    YAML: Final[str] = ".yaml"
    YML: Final[str] = ".yml"
    JSON: Final[str] = ".json"
    TOML: Final[str] = ".toml"
    
    # Documentation
    MARKDOWN: Final[str] = ".md"
    RST: Final[str] = ".rst"
    TXT: Final[str] = ".txt"
    
    @classmethod
    def get_python_extensions(cls) -> list[str]:
        """Get all Python-related file extensions."""
        return [cls.PYTHON, cls.PYTHON_STUB, cls.CYTHON]
    
    @classmethod
    def get_config_extensions(cls) -> list[str]:
        """Get all configuration file extensions."""
        return [cls.YAML, cls.YML, cls.JSON, cls.TOML]


class DefaultPaths:
    """Default directory and file paths."""
    CONFIG_DIR: Final[str] = "config"
    CACHE_DIR: Final[str] = ".vibe_check_cache"
    OUTPUT_DIR: Final[str] = "vibe_check_output"
    REPORTS_DIR: Final[str] = "reports"
    LOGS_DIR: Final[str] = "logs"
    
    # Configuration files
    DEFAULT_CONFIG: Final[str] = "default_config.yaml"
    USER_CONFIG: Final[str] = "vibe_check.yaml"
    IGNORE_FILE: Final[str] = ".vibecheck_ignore"


class ExcludePatterns:
    """Default patterns to exclude from analysis."""
    CACHE_DIRS: Final[list[str]] = [
        "**/__pycache__/**",
        "**/.pytest_cache/**",
        "**/.mypy_cache/**",
        "**/.ruff_cache/**",
        "**/node_modules/**"
    ]
    
    VIRTUAL_ENVS: Final[list[str]] = [
        "**/venv/**",
        "**/env/**",
        "**/.venv/**",
        "**/.env/**",
        "**/virtualenv/**"
    ]
    
    VERSION_CONTROL: Final[list[str]] = [
        "**/.git/**",
        "**/.svn/**",
        "**/.hg/**",
        "**/.bzr/**"
    ]
    
    BUILD_DIRS: Final[list[str]] = [
        "**/build/**",
        "**/dist/**",
        "**/*.egg-info/**",
        "**/.eggs/**",
        "**/.tox/**"
    ]
    
    COMPILED_FILES: Final[list[str]] = [
        "**/*.pyc",
        "**/*.pyo",
        "**/*.so",
        "**/*.dll",
        "**/*.dylib"
    ]
    
    @classmethod
    def get_all_patterns(cls) -> list[str]:
        """Get all default exclude patterns."""
        return (cls.CACHE_DIRS + cls.VIRTUAL_ENVS + cls.VERSION_CONTROL + 
                cls.BUILD_DIRS + cls.COMPILED_FILES)


class AnalysisThresholds:
    """Default thresholds for analysis metrics."""
    
    # Code complexity
    CYCLOMATIC_COMPLEXITY: Final[int] = 10
    COGNITIVE_COMPLEXITY: Final[int] = 15
    MAX_FUNCTION_LENGTH: Final[int] = 50
    MAX_CLASS_LENGTH: Final[int] = 500
    MAX_MODULE_LENGTH: Final[int] = 1000
    MAX_NESTING_DEPTH: Final[int] = 4
    MAX_PARAMETERS: Final[int] = 5
    MAX_METHODS_PER_CLASS: Final[int] = 20
    
    # Code style
    MAX_LINE_LENGTH: Final[int] = 111  # Modern development standard (was 88)
    MAX_LINE_LENGTH_STRICT: Final[int] = 80  # PEP 8
    MAX_LINE_LENGTH_RELAXED: Final[int] = 120
    
    # Documentation
    MIN_DOCUMENTATION_COVERAGE: Final[float] = 0.8
    MIN_DOCSTRING_LENGTH: Final[int] = 10
    
    # Performance
    MAX_ANALYSIS_TIME_SECONDS: Final[int] = 300  # 5 minutes
    MAX_FILE_SIZE_MB: Final[int] = 10
    DEFAULT_TIMEOUT_SECONDS: Final[int] = 60


class ToolNames:
    """Standardized tool names."""
    # Linting tools
    RUFF: Final[str] = "ruff"
    PYLINT: Final[str] = "pylint"
    FLAKE8: Final[str] = "flake8"
    PYCODESTYLE: Final[str] = "pycodestyle"
    PYDOCSTYLE: Final[str] = "pydocstyle"
    
    # Type checking
    MYPY: Final[str] = "mypy"
    PYRIGHT: Final[str] = "pyright"
    
    # Security
    BANDIT: Final[str] = "bandit"
    SAFETY: Final[str] = "safety"
    
    # Formatting
    BLACK: Final[str] = "black"
    ISORT: Final[str] = "isort"
    AUTOPEP8: Final[str] = "autopep8"
    
    # Testing
    PYTEST: Final[str] = "pytest"
    COVERAGE: Final[str] = "coverage"
    
    # Documentation
    SPHINX: Final[str] = "sphinx"
    MKDOCS: Final[str] = "mkdocs"
    
    @classmethod
    def get_all_tools(cls) -> list[str]:
        """Get all supported tool names."""
        return [
            cls.RUFF, cls.PYLINT, cls.FLAKE8, cls.PYCODESTYLE, cls.PYDOCSTYLE,
            cls.MYPY, cls.PYRIGHT, cls.BANDIT, cls.SAFETY, cls.BLACK, cls.ISORT,
            cls.AUTOPEP8, cls.PYTEST, cls.COVERAGE, cls.SPHINX, cls.MKDOCS
        ]


class ErrorMessages:
    """Standardized error messages."""
    
    # File system errors
    FILE_NOT_FOUND: Final[str] = "File not found: {path}"
    DIRECTORY_NOT_FOUND: Final[str] = "Directory not found: {path}"
    PERMISSION_DENIED: Final[str] = "Permission denied: {path}"
    
    # Configuration errors
    CONFIG_NOT_FOUND: Final[str] = "Configuration file not found: {path}"
    INVALID_CONFIG: Final[str] = "Invalid configuration: {error}"
    MISSING_REQUIRED_CONFIG: Final[str] = "Missing required configuration: {key}"
    
    # Tool errors
    TOOL_NOT_FOUND: Final[str] = "Tool not found: {tool}"
    TOOL_EXECUTION_FAILED: Final[str] = "Tool execution failed: {tool} - {error}"
    TOOL_TIMEOUT: Final[str] = "Tool execution timed out: {tool}"
    
    # Analysis errors
    SYNTAX_ERROR: Final[str] = "Syntax error in {file}: {error}"
    IMPORT_ERROR: Final[str] = "Import error in {file}: {error}"
    ANALYSIS_FAILED: Final[str] = "Analysis failed for {file}: {error}"
    
    # General errors
    UNEXPECTED_ERROR: Final[str] = "Unexpected error: {error}"
    OPERATION_CANCELLED: Final[str] = "Operation cancelled by user"
    INSUFFICIENT_RESOURCES: Final[str] = "Insufficient system resources"


class LoggingConfig:
    """Logging configuration constants."""
    
    # Log levels
    DEBUG: Final[str] = "DEBUG"
    INFO: Final[str] = "INFO"
    WARNING: Final[str] = "WARNING"
    ERROR: Final[str] = "ERROR"
    CRITICAL: Final[str] = "CRITICAL"
    
    # Log formats
    DETAILED_FORMAT: Final[str] = (
        "%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s"
    )
    SIMPLE_FORMAT: Final[str] = "%(levelname)s: %(message)s"
    JSON_FORMAT: Final[str] = "json"
    
    # Default settings
    DEFAULT_LEVEL: Final[str] = INFO
    DEFAULT_FORMAT: Final[str] = SIMPLE_FORMAT
    MAX_LOG_SIZE_MB: Final[int] = 10
    MAX_LOG_FILES: Final[int] = 5


class PerformanceConfig:
    """Performance-related constants."""
    
    # Parallel processing
    DEFAULT_MAX_WORKERS: Final[int] = 4
    MIN_WORKERS: Final[int] = 1
    MAX_WORKERS: Final[int] = 16
    
    # Memory limits
    MAX_MEMORY_USAGE_MB: Final[int] = 1024  # 1GB
    MEMORY_WARNING_THRESHOLD: Final[float] = 0.8  # 80%
    
    # Caching
    CACHE_EXPIRY_HOURS: Final[int] = 24
    MAX_CACHE_SIZE_MB: Final[int] = 100
    
    # Timeouts
    NETWORK_TIMEOUT_SECONDS: Final[int] = 30
    FILE_OPERATION_TIMEOUT_SECONDS: Final[int] = 10


# =============================================================================
# ENUMS FOR STANDARDIZED VALUES
# =============================================================================

class AnalysisProfile(Enum):
    """Analysis profile types."""
    MINIMAL = "minimal"
    STANDARD = "standard"
    COMPREHENSIVE = "comprehensive"
    QUALITY = "quality"
    SECURITY = "security"
    PERFORMANCE = "performance"


class OutputFormat(Enum):
    """Supported output formats."""
    JSON = "json"
    YAML = "yaml"
    HTML = "html"
    MARKDOWN = "markdown"
    TEXT = "text"
    CSV = "csv"


class SeverityLevel(Enum):
    """Issue severity levels."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


# =============================================================================
# EXPORTS
# =============================================================================

__all__ = [
    # Classes
    "ProjectInfo",
    "FileExtensions", 
    "DefaultPaths",
    "ExcludePatterns",
    "AnalysisThresholds",
    "ToolNames",
    "ErrorMessages",
    "LoggingConfig",
    "PerformanceConfig",
    
    # Enums
    "AnalysisProfile",
    "OutputFormat", 
    "SeverityLevel"
]
