"""
Vibe Check Compatibility Module
=====================

This module provides compatibility functions for running Vibe Check.
The actor system has been removed as part of Phase 0 stabilization.
All analysis now uses the simple analyzer directly.
"""

import logging
from pathlib import Path
from typing import Any, Dict, Optional

from .models import ProjectMetrics
from .models.progress_tracker import ProgressTracker
from .simple_analyzer import simple_analyze_project

# Actor system is no longer available
ACTOR_SYSTEM_AVAILABLE = False

logger = logging.getLogger("vibe_check_compatibility")


def run_analysis_simple(
    project_path: Path,
    output_dir: Path,
    config: Dict[str, Any],
    progress: Optional[ProgressTracker] = None
) -> ProjectMetrics:
    """
    Run the project analysis using the simple analyzer.

    Args:
        project_path: Path to the project to analyze
        output_dir: Directory to store output files
        config: Configuration dictionary
        progress: Optional progress tracker

    Returns:
        ProjectMetrics object with analysis results
    """
    logger.info("Running analysis using simple analyzer")

    # Run the analysis using the simple analyzer function
    try:
        metrics = simple_analyze_project(
            project_path=project_path,
            output_dir=output_dir,
            config=config
        )

        logger.info("Analysis completed successfully")
        return metrics

    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        # Return empty metrics on failure
        return ProjectMetrics()


def run_analysis(
    project_path: Path,
    output_dir: Path,
    config: Dict[str, Any],
    progress: Optional[ProgressTracker] = None
) -> ProjectMetrics:
    """
    Run the project analysis using the simple analyzer.

    Args:
        project_path: Path to the project to analyze
        output_dir: Directory to store output files
        config: Configuration dictionary
        progress: Optional progress tracker

    Returns:
        ProjectMetrics object with analysis results
    """
    # All modes now use the simple analyzer
    logger.info("Running analysis using simple analyzer")
    return run_analysis_simple(project_path, output_dir, config, progress)
