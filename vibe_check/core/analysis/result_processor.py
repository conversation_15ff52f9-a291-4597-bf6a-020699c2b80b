"""
Result Processor Module
==================

This module provides the ResultProcessor class, which is responsible for
processing tool results and updating file metrics. It abstracts the details
of result processing for use by the analysis engine.
"""

import logging
from typing import Dict, Any, List

from ..models import FileMetrics

logger = logging.getLogger("vibe_check_analysis")


class ResultProcessor:
    """
    Processes tool results and updates file metrics.
    
    This class abstracts the details of result processing for use by
    the analysis engine.
    """
    
    def process_results(self, 
                       file_metrics: FileMetrics, 
                       tool_results: Dict[str, Any]) -> None:
        """
        Process tool results and update file metrics.
        
        Args:
            file_metrics: FileMetrics object to update
            tool_results: Dictionary mapping tool names to results
        """
        for tool_name, result in tool_results.items():
            # Process issues
            issues = self.extract_issues_from_result(result)
            file_metrics.issues.extend(issues)
            
            # Process complexity
            complexity = self.extract_complexity_from_result(result)
            if complexity > 0:
                file_metrics.complexity = complexity
                
            # Process type coverage
            type_coverage = self.extract_type_coverage_from_result(result)
            if type_coverage > 0:
                file_metrics.type_coverage = type_coverage
                
            # Process docstring coverage
            docstring_coverage = self.extract_docstring_coverage_from_result(result)
            if docstring_coverage > 0:
                file_metrics.docstring_coverage = docstring_coverage
    
    def extract_issues_from_result(self, result: Any) -> List[Dict[str, Any]]:
        """
        Extract issues from a tool result.
        
        Args:
            result: Tool result object or dictionary
            
        Returns:
            List of issue dictionaries
        """
        issues = []
        
        # Handle different result formats
        if hasattr(result, 'issues') and result.issues:
            # Object with issues attribute
            for issue in result.issues:
                # Convert issue to a dictionary if it's not already
                if not isinstance(issue, dict):
                    issue_dict = {
                        'code': getattr(issue, 'rule_id', getattr(issue, 'code', '')),
                        'message': getattr(issue, 'message', str(issue)),
                        'line': getattr(issue, 'line', 0),
                        'severity': getattr(issue, 'severity', 'medium')
                    }
                    issues.append(issue_dict)
                else:
                    issues.append(issue)
        elif isinstance(result, dict) and 'issues' in result:
            # Dictionary with issues key
            for issue in result['issues']:
                # Convert issue to a dictionary if it's not already
                if not isinstance(issue, dict):
                    issue_dict = {
                        'code': getattr(issue, 'rule_id', getattr(issue, 'code', '')),
                        'message': getattr(issue, 'message', str(issue)),
                        'line': getattr(issue, 'line', 0),
                        'severity': getattr(issue, 'severity', 'medium')
                    }
                    issues.append(issue_dict)
                else:
                    issues.append(issue)
        
        return issues
    
    def extract_complexity_from_result(self, result: Any) -> int:
        """
        Extract complexity score from a tool result.
        
        Args:
            result: Tool result object or dictionary
            
        Returns:
            Complexity score as an integer
        """
        # Handle different result formats
        if hasattr(result, 'complexity') and result.complexity is not None:
            # Convert to int if needed
            return int(result.complexity) if isinstance(result.complexity, float) else result.complexity
        elif isinstance(result, dict):
            if 'complexity_score' in result:
                return int(result['complexity_score']) if isinstance(result['complexity_score'], float) else result['complexity_score']
            elif 'summary' in result and 'max_complexity' in result['summary']:
                return int(result['summary']['max_complexity']) if isinstance(result['summary']['max_complexity'], float) else result['summary']['max_complexity']
        
        # Default to 0 if no complexity found
        return 0
    
    def extract_type_coverage_from_result(self, result: Any) -> float:
        """
        Extract type coverage from a tool result.
        
        Args:
            result: Tool result object or dictionary
            
        Returns:
            Type coverage as a float (0-100)
        """
        # Handle different result formats
        if hasattr(result, 'type_coverage') and result.type_coverage is not None:
            return float(result.type_coverage)
        elif isinstance(result, dict):
            if 'type_coverage' in result:
                return float(result['type_coverage'])
            elif 'summary' in result and 'type_coverage' in result['summary']:
                return float(result['summary']['type_coverage'])
        
        # Default to 0 if no type coverage found
        return 0.0
    
    def extract_docstring_coverage_from_result(self, result: Any) -> float:
        """
        Extract docstring coverage from a tool result.
        
        Args:
            result: Tool result object or dictionary
            
        Returns:
            Docstring coverage as a float (0-100)
        """
        # Handle different result formats
        if hasattr(result, 'docstring_coverage') and result.docstring_coverage is not None:
            return float(result.docstring_coverage)
        elif isinstance(result, dict):
            if 'docstring_coverage' in result:
                return float(result['docstring_coverage'])
            elif 'summary' in result and 'docstring_coverage' in result['summary']:
                return float(result['summary']['docstring_coverage'])
        
        # Default to 0 if no docstring coverage found
        return 0.0
