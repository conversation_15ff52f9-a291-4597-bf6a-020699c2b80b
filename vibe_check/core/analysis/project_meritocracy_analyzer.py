"""
Project Meritocracy Analyzer

This module analyzes the underlying quality, structure, and architectural decisions
that make a project work effectively. It goes beyond surface-level metrics to
understand the "why" and "how" of project success.
"""

import logging
from collections import defaultdict
from pathlib import Path
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Set

logger = logging.getLogger(__name__)


@dataclass
class ArchitecturalPattern:
    """Represents an architectural pattern found in the codebase."""
    
    pattern_name: str
    confidence: float  # 0.0 to 1.0
    evidence: List[str]
    files_involved: List[str]
    description: str
    quality_impact: str  # "positive", "negative", "neutral"


@dataclass
class QualityIndicator:
    """Represents a quality indicator in the codebase."""
    
    indicator_name: str
    score: float  # 0.0 to 10.0
    evidence: List[str]
    impact_areas: List[str]
    recommendations: List[str]


@dataclass
class ProjectMeritocracyResult:
    """Results from project meritocracy analysis."""
    
    overall_score: float = 0.0
    architectural_patterns: List[ArchitecturalPattern] = field(default_factory=list)
    quality_indicators: List[QualityIndicator] = field(default_factory=list)
    strengths: List[str] = field(default_factory=list)
    weaknesses: List[str] = field(default_factory=list)
    success_factors: List[str] = field(default_factory=list)
    risk_factors: List[str] = field(default_factory=list)
    meritocracy_insights: Dict[str, Any] = field(default_factory=dict)


class ProjectMeritocracyAnalyzer:
    """
    Analyzes project meritocracy - the underlying quality and structure
    that determines why and how a project works effectively.
    """
    
    def __init__(self) -> None:
        self.result = ProjectMeritocracyResult()
        self.file_metrics: Dict[str, Dict[str, Any]] = {}
        self.import_graph: Dict[str, Set[str]] = defaultdict(set)
        self.class_hierarchy: Dict[str, List[str]] = defaultdict(list)
        self.design_patterns: Dict[str, List[str]] = defaultdict(list)
        self.abstraction_layers: Dict[str, int] = {}
    
    def analyze_project_meritocracy(self, project_metrics: Any) -> ProjectMeritocracyResult:
        """
        Analyze project meritocracy from comprehensive metrics.
        
        Args:
            project_metrics: ProjectMetrics object with analysis results
            
        Returns:
            ProjectMeritocracyResult with meritocracy analysis
        """
        self.result = ProjectMeritocracyResult()
        
        # Extract data from project metrics
        self._extract_project_data(project_metrics)
        
        # Analyze architectural patterns
        self._analyze_architectural_patterns()
        
        # Analyze quality indicators
        self._analyze_quality_indicators()
        
        # Analyze success and risk factors
        self._analyze_success_factors()
        
        # Calculate overall meritocracy score
        self._calculate_overall_score()
        
        # Generate insights
        self._generate_meritocracy_insights()
        
        return self.result
    
    def _extract_project_data(self, project_metrics: Any) -> None:
        """Extract relevant data from project metrics."""
        for file_path, file_metrics in project_metrics.files.items():
            self.file_metrics[file_path] = {
                'complexity': getattr(file_metrics, 'complexity', 0),
                'lines': getattr(file_metrics, 'lines', 0),
                'issues': getattr(file_metrics, 'issues', []),
                'imports': getattr(file_metrics, 'imports', []),
                'classes': getattr(file_metrics, 'classes', []),
                'functions': getattr(file_metrics, 'functions', [])
            }
            
            # Build import graph
            for imp in self.file_metrics[file_path]['imports']:
                self.import_graph[file_path].add(imp)
    
    def _analyze_architectural_patterns(self) -> None:
        """Analyze architectural patterns in the codebase."""
        
        # Detect layered architecture
        self._detect_layered_architecture()
        
        # Detect design patterns
        self._detect_design_patterns()
        
        # Detect separation of concerns
        self._detect_separation_of_concerns()
        
        # Detect dependency injection patterns
        self._detect_dependency_injection()
        
        # Detect modular architecture
        self._detect_modular_architecture()
    
    def _detect_layered_architecture(self) -> None:
        """Detect layered architecture patterns."""
        layers = {
            'presentation': ['ui', 'web', 'cli', 'api', 'views', 'controllers'],
            'business': ['core', 'services', 'domain', 'business', 'logic'],
            'data': ['models', 'data', 'persistence', 'repository', 'dao'],
            'infrastructure': ['infrastructure', 'config', 'utils', 'common']
        }
        
        layer_files = defaultdict(list)
        
        for file_path in self.file_metrics:
            path_parts = Path(file_path).parts
            for layer_name, keywords in layers.items():
                if any(keyword in str(file_path).lower() for keyword in keywords):
                    layer_files[layer_name].append(file_path)
                    break
        
        if len(layer_files) >= 3:
            evidence = [f"{layer}: {len(files)} files" for layer, files in layer_files.items()]
            self.result.architectural_patterns.append(ArchitecturalPattern(
                pattern_name="Layered Architecture",
                confidence=min(0.9, len(layer_files) / 4),
                evidence=evidence,
                files_involved=sum(layer_files.values(), []),
                description="Project follows layered architecture with clear separation of concerns",
                quality_impact="positive"
            ))
    
    def _detect_design_patterns(self) -> None:
        """Detect common design patterns."""
        patterns_found = []
        
        # Factory pattern detection
        factory_files = [f for f in self.file_metrics if 'factory' in f.lower()]
        if factory_files:
            patterns_found.append(("Factory Pattern", factory_files, 0.8))
        
        # Repository pattern detection
        repo_files = [f for f in self.file_metrics if 'repository' in f.lower() or 'repo' in f.lower()]
        if repo_files:
            patterns_found.append(("Repository Pattern", repo_files, 0.8))
        
        # Strategy pattern detection (multiple implementations)
        strategy_indicators = ['strategy', 'handler', 'processor', 'runner']
        strategy_files = [f for f in self.file_metrics 
                         if any(indicator in f.lower() for indicator in strategy_indicators)]
        if len(strategy_files) >= 3:
            patterns_found.append(("Strategy Pattern", strategy_files, 0.7))
        
        # Observer pattern detection
        observer_files = [f for f in self.file_metrics 
                         if 'observer' in f.lower() or 'listener' in f.lower() or 'event' in f.lower()]
        if observer_files:
            patterns_found.append(("Observer Pattern", observer_files, 0.6))
        
        for pattern_name, files, confidence in patterns_found:
            self.result.architectural_patterns.append(ArchitecturalPattern(
                pattern_name=pattern_name,
                confidence=confidence,
                evidence=[f"Found in {len(files)} files"],
                files_involved=files,
                description=f"Project uses {pattern_name} for better code organization",
                quality_impact="positive"
            ))
    
    def _detect_separation_of_concerns(self) -> None:
        """Detect separation of concerns."""
        concern_areas = {
            'configuration': ['config', 'settings'],
            'logging': ['log', 'logger'],
            'error_handling': ['error', 'exception', 'handler'],
            'testing': ['test', 'spec'],
            'documentation': ['doc', 'readme'],
            'utilities': ['util', 'helper', 'common']
        }
        
        separated_concerns = []
        for concern, keywords in concern_areas.items():
            concern_files = [f for f in self.file_metrics 
                           if any(keyword in f.lower() for keyword in keywords)]
            if concern_files:
                separated_concerns.append((concern, len(concern_files)))
        
        if len(separated_concerns) >= 4:
            self.result.architectural_patterns.append(ArchitecturalPattern(
                pattern_name="Separation of Concerns",
                confidence=min(0.9, len(separated_concerns) / 6),
                evidence=[f"{concern}: {count} files" for concern, count in separated_concerns],
                files_involved=[],
                description="Project demonstrates good separation of concerns",
                quality_impact="positive"
            ))
    
    def _detect_dependency_injection(self) -> None:
        """Detect dependency injection patterns."""
        di_indicators = ['inject', 'dependency', 'container', 'provider', 'registry']
        di_files = [f for f in self.file_metrics 
                   if any(indicator in f.lower() for indicator in di_indicators)]
        
        if di_files:
            self.result.architectural_patterns.append(ArchitecturalPattern(
                pattern_name="Dependency Injection",
                confidence=0.7,
                evidence=[f"DI-related files: {len(di_files)}"],
                files_involved=di_files,
                description="Project uses dependency injection for loose coupling",
                quality_impact="positive"
            ))
    
    def _detect_modular_architecture(self) -> None:
        """Detect modular architecture."""
        # Analyze directory structure depth and organization
        path_depths = [len(Path(f).parts) for f in self.file_metrics]
        avg_depth = sum(path_depths) / len(path_depths) if path_depths else 0
        
        # Count distinct modules/packages
        modules = set()
        for file_path in self.file_metrics:
            parts = Path(file_path).parts
            if len(parts) > 1:
                modules.add(parts[0])
        
        if len(modules) >= 3 and avg_depth >= 2:
            self.result.architectural_patterns.append(ArchitecturalPattern(
                pattern_name="Modular Architecture",
                confidence=min(0.9, len(modules) / 10),
                evidence=[f"Modules: {len(modules)}", f"Average depth: {avg_depth:.1f}"],
                files_involved=[],
                description="Project is well-organized into distinct modules",
                quality_impact="positive"
            ))
    
    def _analyze_quality_indicators(self) -> None:
        """Analyze various quality indicators."""
        
        # Code complexity distribution
        self._analyze_complexity_distribution()
        
        # Test coverage and quality
        self._analyze_test_quality()
        
        # Documentation quality
        self._analyze_documentation_quality()
        
        # Error handling maturity
        self._analyze_error_handling()
        
        # Code consistency
        self._analyze_code_consistency()
    
    def _analyze_complexity_distribution(self) -> None:
        """Analyze code complexity distribution."""
        complexities = [metrics['complexity'] for metrics in self.file_metrics.values()]
        if not complexities:
            return
        
        avg_complexity = sum(complexities) / len(complexities)
        max_complexity = max(complexities)
        high_complexity_files = [f for f, m in self.file_metrics.items() if m['complexity'] > 10]
        
        score = max(0, 10 - (avg_complexity / 2))  # Lower complexity = higher score
        
        evidence = [
            f"Average complexity: {avg_complexity:.1f}",
            f"Maximum complexity: {max_complexity}",
            f"High complexity files: {len(high_complexity_files)}"
        ]
        
        recommendations = []
        if avg_complexity > 5:
            recommendations.append("Consider refactoring complex functions")
        if len(high_complexity_files) > len(complexities) * 0.1:
            recommendations.append("Too many high-complexity files")
        
        self.result.quality_indicators.append(QualityIndicator(
            indicator_name="Complexity Management",
            score=score,
            evidence=evidence,
            impact_areas=["maintainability", "readability", "testing"],
            recommendations=recommendations
        ))
    
    def _analyze_test_quality(self) -> None:
        """Analyze test quality and coverage."""
        test_files = [f for f in self.file_metrics if 'test' in f.lower()]
        total_files = len(self.file_metrics)
        
        if total_files == 0:
            return
        
        test_ratio = len(test_files) / total_files
        score = min(10, test_ratio * 20)  # 50% test files = score of 10
        
        evidence = [
            f"Test files: {len(test_files)}",
            f"Total files: {total_files}",
            f"Test ratio: {test_ratio:.2%}"
        ]
        
        recommendations = []
        if test_ratio < 0.3:
            recommendations.append("Increase test coverage")
        if test_ratio > 0.7:
            recommendations.append("Excellent test coverage")
        
        self.result.quality_indicators.append(QualityIndicator(
            indicator_name="Test Coverage",
            score=score,
            evidence=evidence,
            impact_areas=["reliability", "maintainability", "confidence"],
            recommendations=recommendations
        ))
    
    def _analyze_documentation_quality(self) -> None:
        """Analyze documentation quality."""
        doc_files = [f for f in self.file_metrics 
                    if any(ext in f.lower() for ext in ['.md', '.rst', '.txt', 'readme', 'doc'])]
        
        has_readme = any('readme' in f.lower() for f in doc_files)
        has_docs_dir = any('doc' in f.lower() for f in self.file_metrics)
        
        score = 0
        if has_readme:
            score += 4
        if has_docs_dir:
            score += 3
        if len(doc_files) > 3:
            score += 3
        
        evidence = [
            f"Documentation files: {len(doc_files)}",
            f"Has README: {has_readme}",
            f"Has docs directory: {has_docs_dir}"
        ]
        
        self.result.quality_indicators.append(QualityIndicator(
            indicator_name="Documentation Quality",
            score=score,
            evidence=evidence,
            impact_areas=["usability", "onboarding", "maintenance"],
            recommendations=["Maintain comprehensive documentation"] if score < 7 else []
        ))
    
    def _analyze_error_handling(self) -> None:
        """Analyze error handling maturity."""
        error_files = [f for f in self.file_metrics 
                      if any(keyword in f.lower() for keyword in ['error', 'exception', 'handler'])]
        
        # Count issues related to error handling
        error_issues = 0
        for metrics in self.file_metrics.values():
            for issue in metrics['issues']:
                if any(keyword in str(issue).lower() for keyword in ['error', 'exception', 'try', 'catch']):
                    error_issues += 1
        
        score = min(10, len(error_files) * 2 + max(0, 5 - error_issues))
        
        self.result.quality_indicators.append(QualityIndicator(
            indicator_name="Error Handling",
            score=score,
            evidence=[f"Error handling files: {len(error_files)}", f"Error-related issues: {error_issues}"],
            impact_areas=["reliability", "user_experience", "debugging"],
            recommendations=["Improve error handling"] if score < 6 else []
        ))
    
    def _analyze_code_consistency(self) -> None:
        """Analyze code consistency across the project."""
        # This would analyze naming conventions, code style, etc.
        # For now, we'll use a simplified approach based on issues
        
        total_issues = sum(len(metrics['issues']) for metrics in self.file_metrics.values())
        total_lines = sum(metrics['lines'] for metrics in self.file_metrics.values())
        
        if total_lines == 0:
            return
        
        issues_per_line = total_issues / total_lines
        score = max(0, 10 - (issues_per_line * 1000))  # Fewer issues = higher score
        
        self.result.quality_indicators.append(QualityIndicator(
            indicator_name="Code Consistency",
            score=score,
            evidence=[f"Issues per 1000 lines: {issues_per_line * 1000:.1f}"],
            impact_areas=["maintainability", "readability", "team_productivity"],
            recommendations=["Address code quality issues"] if score < 7 else []
        ))
    
    def _analyze_success_factors(self) -> None:
        """Analyze factors that contribute to project success."""
        
        # Analyze strengths
        for pattern in self.result.architectural_patterns:
            if pattern.quality_impact == "positive":
                self.result.strengths.append(f"Uses {pattern.pattern_name}")
        
        for indicator in self.result.quality_indicators:
            if indicator.score >= 8:
                self.result.strengths.append(f"Excellent {indicator.indicator_name}")
        
        # Analyze weaknesses
        for indicator in self.result.quality_indicators:
            if indicator.score < 5:
                self.result.weaknesses.append(f"Poor {indicator.indicator_name}")
        
        # Success factors
        if len(self.result.architectural_patterns) >= 3:
            self.result.success_factors.append("Well-architected with multiple design patterns")
        
        if any(indicator.score >= 8 for indicator in self.result.quality_indicators):
            self.result.success_factors.append("High quality in key areas")
        
        # Risk factors
        if any(indicator.score < 4 for indicator in self.result.quality_indicators):
            self.result.risk_factors.append("Critical quality issues need attention")
        
        if len(self.result.weaknesses) > len(self.result.strengths):
            self.result.risk_factors.append("More weaknesses than strengths")
    
    def _calculate_overall_score(self) -> None:
        """Calculate overall meritocracy score."""
        if not self.result.quality_indicators:
            self.result.overall_score = 5.0
            return
        
        # Weight different aspects
        weights = {
            "Complexity Management": 0.25,
            "Test Coverage": 0.25,
            "Documentation Quality": 0.15,
            "Error Handling": 0.20,
            "Code Consistency": 0.15
        }
        
        weighted_score = 0
        total_weight = 0
        
        for indicator in self.result.quality_indicators:
            weight = weights.get(indicator.indicator_name, 0.1)
            weighted_score += indicator.score * weight
            total_weight += weight
        
        if total_weight > 0:
            self.result.overall_score = weighted_score / total_weight
        else:
            self.result.overall_score = 5.0
        
        # Bonus for architectural patterns
        pattern_bonus = min(2.0, len(self.result.architectural_patterns) * 0.5)
        self.result.overall_score = min(10.0, self.result.overall_score + pattern_bonus)
    
    def _generate_meritocracy_insights(self) -> None:
        """Generate insights about project meritocracy."""
        insights = {}
        
        # Overall assessment
        if self.result.overall_score >= 8:
            insights["assessment"] = "Excellent project with strong meritocratic qualities"
        elif self.result.overall_score >= 6:
            insights["assessment"] = "Good project with solid foundation"
        elif self.result.overall_score >= 4:
            insights["assessment"] = "Average project with room for improvement"
        else:
            insights["assessment"] = "Project needs significant improvement"
        
        # Key insights
        insights["why_it_works"] = []
        insights["why_it_might_fail"] = []
        
        for strength in self.result.strengths:
            insights["why_it_works"].append(strength)
        
        for weakness in self.result.weaknesses:
            insights["why_it_might_fail"].append(weakness)
        
        # Architectural maturity
        pattern_count = len(self.result.architectural_patterns)
        if pattern_count >= 4:
            insights["architectural_maturity"] = "High - Multiple patterns implemented"
        elif pattern_count >= 2:
            insights["architectural_maturity"] = "Medium - Some patterns present"
        else:
            insights["architectural_maturity"] = "Low - Limited architectural patterns"
        
        self.result.meritocracy_insights = insights
