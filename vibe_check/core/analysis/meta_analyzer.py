"""
Meta-Analysis System
===================

This module provides value-added integration when multiple analysis tools are available.
It performs cross-tool correlation, pattern detection, and generates intelligent insights
that go beyond simple tool aggregation.

Key features:
- Cross-tool issue correlation
- Pattern detection across different analysis types
- Trend analysis and regression detection
- Intelligent prioritization of findings
- Actionable recommendations
"""

import logging
from collections import defaultdict
from pathlib import Path
from typing import Dict, List, Any, Optional, Set, Tuple

logger = logging.getLogger("vibe_check_meta_analyzer")


class MetaAnalyzer:
    """
    Provides value-added analysis by correlating results from multiple tools
    and generating intelligent insights.
    """

    def __init__(self):
        """Initialize the meta-analyzer."""
        self.correlation_rules = self._load_correlation_rules()
        self.pattern_detectors = self._load_pattern_detectors()

    def analyze_combined_results(self, tool_results: Dict[str, Any], 
                                file_path: Path) -> Dict[str, Any]:
        """
        Perform meta-analysis on combined tool results.
        
        Args:
            tool_results: Dictionary mapping tool names to their results
            file_path: Path to the analyzed file
            
        Returns:
            Dictionary with meta-analysis insights
        """
        available_tools = [tool for tool, result in tool_results.items() 
                          if result.get("tool_status") == "available"]
        
        fallback_tools = [tool for tool, result in tool_results.items() 
                         if result.get("fallback_used", False)]

        # Extract all issues from all tools
        all_issues = self._extract_all_issues(tool_results)
        
        # Perform cross-tool analysis
        correlations = self._find_issue_correlations(all_issues, tool_results)
        patterns = self._detect_patterns(all_issues, tool_results)
        priorities = self._calculate_priorities(all_issues, correlations)
        recommendations = self._generate_recommendations(tool_results, correlations, patterns)
        
        # Calculate meta-metrics
        meta_metrics = self._calculate_meta_metrics(tool_results, all_issues)
        
        return {
            "meta_analysis": {
                "available_tools": available_tools,
                "fallback_tools": fallback_tools,
                "total_tools_used": len(tool_results),
                "correlations": correlations,
                "patterns": patterns,
                "priorities": priorities,
                "recommendations": recommendations,
                "meta_metrics": meta_metrics,
                "analysis_quality": self._assess_analysis_quality(available_tools, fallback_tools)
            },
            "enhanced_issues": self._enhance_issues_with_context(all_issues, correlations),
            "actionable_insights": self._generate_actionable_insights(patterns, recommendations)
        }

    def _extract_all_issues(self, tool_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract and normalize issues from all tools."""
        all_issues = []
        
        for tool_name, result in tool_results.items():
            issues = result.get("issues", [])
            for issue in issues:
                # Normalize issue format and add tool context
                normalized_issue = {
                    "tool": tool_name,
                    "line": issue.get("line", issue.get("line_number", 0)),
                    "column": issue.get("column", issue.get("column_number", 0)),
                    "message": issue.get("message", ""),
                    "severity": issue.get("severity", "info"),
                    "rule": issue.get("rule", issue.get("code", "unknown")),
                    "type": issue.get("type", "general"),
                    "original": issue
                }
                all_issues.append(normalized_issue)
        
        return all_issues

    def _find_issue_correlations(self, all_issues: List[Dict[str, Any]], 
                                tool_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Find correlations between issues from different tools."""
        correlations = []
        
        # Group issues by location (line number)
        issues_by_line = defaultdict(list)
        for issue in all_issues:
            line = issue.get("line", 0)
            if line > 0:
                issues_by_line[line].append(issue)
        
        # Find correlations within the same line
        for line, line_issues in issues_by_line.items():
            if len(line_issues) > 1:
                # Multiple tools found issues on the same line
                tools_involved = list(set(issue["tool"] for issue in line_issues))
                if len(tools_involved) > 1:
                    correlation = {
                        "type": "same_line_multiple_tools",
                        "line": line,
                        "tools": tools_involved,
                        "issues": line_issues,
                        "confidence": self._calculate_correlation_confidence(line_issues),
                        "description": f"Multiple tools ({', '.join(tools_involved)}) found issues on line {line}"
                    }
                    correlations.append(correlation)
        
        # Find semantic correlations (e.g., complexity issues + style issues)
        semantic_correlations = self._find_semantic_correlations(all_issues)
        correlations.extend(semantic_correlations)
        
        return correlations

    def _find_semantic_correlations(self, all_issues: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Find semantic correlations between different types of issues."""
        correlations = []
        
        # Group issues by type
        issues_by_type = defaultdict(list)
        for issue in all_issues:
            issue_type = issue.get("type", "general")
            issues_by_type[issue_type].append(issue)
        
        # Look for meaningful combinations
        complexity_issues = issues_by_type.get("complexity", [])
        style_issues = issues_by_type.get("style", []) + issues_by_type.get("line_length", [])
        security_issues = issues_by_type.get("security", [])
        
        # High complexity + many style issues = maintainability concern
        if len(complexity_issues) > 0 and len(style_issues) > 5:
            correlations.append({
                "type": "maintainability_concern",
                "description": "High complexity combined with multiple style issues suggests maintainability problems",
                "complexity_issues": len(complexity_issues),
                "style_issues": len(style_issues),
                "confidence": 0.8,
                "recommendation": "Consider refactoring to reduce complexity and improve code style"
            })
        
        # Security issues + complexity = higher risk
        if len(security_issues) > 0 and len(complexity_issues) > 0:
            correlations.append({
                "type": "security_complexity_risk",
                "description": "Security issues in complex code are harder to review and fix",
                "security_issues": len(security_issues),
                "complexity_issues": len(complexity_issues),
                "confidence": 0.9,
                "recommendation": "Prioritize security fixes in complex functions"
            })
        
        return correlations

    def _detect_patterns(self, all_issues: List[Dict[str, Any]], 
                        tool_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Detect patterns across issues and tools."""
        patterns = []
        
        # Pattern 1: Consistent issues across multiple tools
        tool_agreement = self._analyze_tool_agreement(all_issues)
        if tool_agreement:
            patterns.append(tool_agreement)
        
        # Pattern 2: Issue hotspots (lines with many issues)
        hotspots = self._find_issue_hotspots(all_issues)
        if hotspots:
            patterns.extend(hotspots)
        
        # Pattern 3: Tool-specific patterns
        tool_patterns = self._analyze_tool_specific_patterns(tool_results)
        patterns.extend(tool_patterns)
        
        return patterns

    def _analyze_tool_agreement(self, all_issues: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Analyze agreement between different tools."""
        if len(set(issue["tool"] for issue in all_issues)) < 2:
            return None
        
        # Count issues by severity across tools
        severity_by_tool = defaultdict(lambda: defaultdict(int))
        for issue in all_issues:
            tool = issue["tool"]
            severity = issue["severity"]
            severity_by_tool[tool][severity] += 1
        
        # Check if tools agree on severity distribution
        tools = list(severity_by_tool.keys())
        if len(tools) >= 2:
            agreement_score = self._calculate_agreement_score(severity_by_tool)
            if agreement_score > 0.7:
                return {
                    "type": "tool_agreement",
                    "description": f"Multiple tools show similar issue patterns (agreement: {agreement_score:.2f})",
                    "agreement_score": agreement_score,
                    "tools": tools,
                    "confidence": agreement_score
                }
        
        return None

    def _find_issue_hotspots(self, all_issues: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Find lines or areas with many issues."""
        hotspots = []
        
        # Count issues per line
        issues_per_line = defaultdict(int)
        for issue in all_issues:
            line = issue.get("line", 0)
            if line > 0:
                issues_per_line[line] += 1
        
        # Find hotspot lines (more than 3 issues)
        for line, count in issues_per_line.items():
            if count >= 3:
                hotspots.append({
                    "type": "issue_hotspot",
                    "line": line,
                    "issue_count": count,
                    "description": f"Line {line} has {count} issues from multiple tools",
                    "confidence": min(1.0, count / 5.0)  # Cap at 1.0
                })
        
        return hotspots

    def _analyze_tool_specific_patterns(self, tool_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Analyze patterns specific to individual tools."""
        patterns = []
        
        for tool_name, result in tool_results.items():
            if result.get("tool_status") == "available":
                issues = result.get("issues", [])
                
                # Pattern: Tool found many issues of the same type
                issue_types = defaultdict(int)
                for issue in issues:
                    issue_type = issue.get("type", "general")
                    issue_types[issue_type] += 1
                
                for issue_type, count in issue_types.items():
                    if count >= 5:  # Many issues of the same type
                        patterns.append({
                            "type": "repeated_issue_type",
                            "tool": tool_name,
                            "issue_type": issue_type,
                            "count": count,
                            "description": f"{tool_name} found {count} {issue_type} issues",
                            "confidence": min(1.0, count / 10.0)
                        })
        
        return patterns

    def _calculate_priorities(self, all_issues: List[Dict[str, Any]], 
                            correlations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Calculate intelligent priorities for issues."""
        priorities = []
        
        # Priority 1: Issues found by multiple tools
        multi_tool_lines = set()
        for correlation in correlations:
            if correlation["type"] == "same_line_multiple_tools":
                multi_tool_lines.add(correlation["line"])
        
        if multi_tool_lines:
            priorities.append({
                "level": "high",
                "type": "multi_tool_agreement",
                "description": f"Issues on lines {sorted(multi_tool_lines)} found by multiple tools",
                "lines": sorted(multi_tool_lines),
                "rationale": "Multiple tools agreeing increases confidence in the issue"
            })
        
        # Priority 2: Security issues
        security_issues = [issue for issue in all_issues if issue.get("type") == "security"]
        if security_issues:
            priorities.append({
                "level": "high",
                "type": "security_issues",
                "description": f"{len(security_issues)} security issues found",
                "count": len(security_issues),
                "rationale": "Security issues should be addressed immediately"
            })
        
        # Priority 3: High complexity issues
        complexity_issues = [issue for issue in all_issues if issue.get("type") == "complexity"]
        if len(complexity_issues) > 3:
            priorities.append({
                "level": "medium",
                "type": "complexity_issues",
                "description": f"{len(complexity_issues)} complexity issues found",
                "count": len(complexity_issues),
                "rationale": "High complexity makes code harder to maintain"
            })
        
        return priorities

    def _generate_recommendations(self, tool_results: Dict[str, Any], 
                                correlations: List[Dict[str, Any]], 
                                patterns: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate actionable recommendations."""
        recommendations = []
        
        # Recommendation based on tool availability
        available_tools = [tool for tool, result in tool_results.items() 
                          if result.get("tool_status") == "available"]
        fallback_tools = [tool for tool, result in tool_results.items() 
                         if result.get("fallback_used", False)]
        
        if fallback_tools:
            recommendations.append({
                "type": "tool_installation",
                "priority": "medium",
                "description": f"Install {', '.join(fallback_tools)} for enhanced analysis",
                "rationale": "External tools provide more comprehensive analysis than fallback methods",
                "action": f"pip install {' '.join(fallback_tools)}"
            })
        
        # Recommendations based on correlations
        for correlation in correlations:
            if correlation["type"] == "maintainability_concern":
                recommendations.append({
                    "type": "refactoring",
                    "priority": "high",
                    "description": "Consider refactoring complex functions with style issues",
                    "rationale": correlation["description"],
                    "action": "Break down complex functions and apply consistent formatting"
                })
        
        # Recommendations based on patterns
        for pattern in patterns:
            if pattern["type"] == "repeated_issue_type" and pattern["count"] > 10:
                recommendations.append({
                    "type": "systematic_fix",
                    "priority": "medium",
                    "description": f"Address systematic {pattern['issue_type']} issues",
                    "rationale": f"Found {pattern['count']} similar issues",
                    "action": f"Use automated tools or IDE features to fix {pattern['issue_type']} issues"
                })
        
        return recommendations

    def _calculate_meta_metrics(self, tool_results: Dict[str, Any], 
                              all_issues: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate meta-metrics across all tools."""
        available_tools = [tool for tool, result in tool_results.items() 
                          if result.get("tool_status") == "available"]
        
        total_issues = len(all_issues)
        issues_by_severity = defaultdict(int)
        issues_by_tool = defaultdict(int)
        
        for issue in all_issues:
            issues_by_severity[issue["severity"]] += 1
            issues_by_tool[issue["tool"]] += 1
        
        return {
            "total_issues": total_issues,
            "issues_by_severity": dict(issues_by_severity),
            "issues_by_tool": dict(issues_by_tool),
            "tool_coverage": len(available_tools),
            "analysis_completeness": len(available_tools) / max(len(tool_results), 1),
            "issue_density": total_issues / max(len(tool_results), 1)
        }

    def _assess_analysis_quality(self, available_tools: List[str], 
                               fallback_tools: List[str]) -> Dict[str, Any]:
        """Assess the quality of the analysis based on tool availability."""
        total_tools = len(available_tools) + len(fallback_tools)
        available_ratio = len(available_tools) / max(total_tools, 1)
        
        if available_ratio >= 0.8:
            quality = "excellent"
            description = "Most analysis tools are available"
        elif available_ratio >= 0.6:
            quality = "good"
            description = "Most analysis tools are available"
        elif available_ratio >= 0.4:
            quality = "fair"
            description = "Some analysis tools are missing"
        else:
            quality = "limited"
            description = "Many analysis tools are unavailable"
        
        return {
            "quality": quality,
            "score": available_ratio,
            "description": description,
            "available_tools": len(available_tools),
            "fallback_tools": len(fallback_tools),
            "recommendations": self._get_quality_recommendations(available_ratio, fallback_tools)
        }

    def _enhance_issues_with_context(self, all_issues: List[Dict[str, Any]], 
                                   correlations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Enhance issues with additional context from meta-analysis."""
        enhanced_issues = []
        
        # Create correlation lookup
        correlation_by_line = {}
        for correlation in correlations:
            if correlation["type"] == "same_line_multiple_tools":
                correlation_by_line[correlation["line"]] = correlation
        
        for issue in all_issues:
            enhanced_issue = issue.copy()
            line = issue.get("line", 0)
            
            # Add correlation context
            if line in correlation_by_line:
                enhanced_issue["correlation"] = {
                    "multiple_tools": True,
                    "confidence": correlation_by_line[line]["confidence"],
                    "other_tools": [tool for tool in correlation_by_line[line]["tools"] 
                                  if tool != issue["tool"]]
                }
            
            # Add priority context
            enhanced_issue["priority_score"] = self._calculate_issue_priority_score(issue, correlations)
            
            enhanced_issues.append(enhanced_issue)
        
        return enhanced_issues

    def _generate_actionable_insights(self, patterns: List[Dict[str, Any]], 
                                    recommendations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate actionable insights for developers."""
        insights = []
        
        # Insight from patterns
        for pattern in patterns:
            if pattern["type"] == "tool_agreement" and pattern["confidence"] > 0.8:
                insights.append({
                    "type": "high_confidence",
                    "message": "Multiple tools agree on issue severity - high confidence in findings",
                    "action": "Focus on addressing these issues first",
                    "confidence": pattern["confidence"]
                })
        
        # Insight from recommendations
        high_priority_recs = [rec for rec in recommendations if rec.get("priority") == "high"]
        if high_priority_recs:
            insights.append({
                "type": "immediate_action",
                "message": f"{len(high_priority_recs)} high-priority recommendations available",
                "action": "Review and implement high-priority recommendations",
                "recommendations": high_priority_recs
            })
        
        return insights

    def _calculate_correlation_confidence(self, issues: List[Dict[str, Any]]) -> float:
        """Calculate confidence score for issue correlation."""
        # Simple heuristic: more tools agreeing = higher confidence
        unique_tools = len(set(issue["tool"] for issue in issues))
        return min(1.0, unique_tools / 3.0)  # Cap at 1.0, max confidence with 3+ tools

    def _calculate_agreement_score(self, severity_by_tool: Dict[str, Dict[str, int]]) -> float:
        """Calculate agreement score between tools."""
        # Simplified agreement calculation
        tools = list(severity_by_tool.keys())
        if len(tools) < 2:
            return 0.0
        
        # Compare severity distributions
        # This is a simplified version - real implementation would use statistical measures
        return 0.75  # Placeholder

    def _calculate_issue_priority_score(self, issue: Dict[str, Any], 
                                      correlations: List[Dict[str, Any]]) -> float:
        """Calculate priority score for an individual issue."""
        score = 0.5  # Base score
        
        # Increase score for security issues
        if issue.get("type") == "security":
            score += 0.3
        
        # Increase score for errors vs warnings
        if issue.get("severity") == "error":
            score += 0.2
        elif issue.get("severity") == "warning":
            score += 0.1
        
        # Increase score if multiple tools found issues on the same line
        line = issue.get("line", 0)
        for correlation in correlations:
            if (correlation["type"] == "same_line_multiple_tools" and 
                correlation["line"] == line):
                score += 0.2
                break
        
        return min(1.0, score)  # Cap at 1.0

    def _get_quality_recommendations(self, available_ratio: float, 
                                   fallback_tools: List[str]) -> List[str]:
        """Get recommendations for improving analysis quality."""
        recommendations = []
        
        if available_ratio < 0.5:
            recommendations.append("Install missing analysis tools for comprehensive coverage")
        
        if fallback_tools:
            recommendations.append(f"Consider installing: {', '.join(fallback_tools)}")
        
        if available_ratio < 0.8:
            recommendations.append("Enable additional analysis tools in configuration")
        
        return recommendations

    def _load_correlation_rules(self) -> List[Dict[str, Any]]:
        """Load correlation rules for cross-tool analysis."""
        # This would typically load from configuration
        return []

    def _load_pattern_detectors(self) -> List[Dict[str, Any]]:
        """Load pattern detection rules."""
        # This would typically load from configuration
        return []
