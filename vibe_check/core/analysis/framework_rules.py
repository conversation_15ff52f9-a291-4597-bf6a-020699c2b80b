"""
Framework-Specific Semantic Rules

This module contains semantic analysis rules specific to popular Python frameworks.
These rules check for framework-specific best practices and anti-patterns.
"""

import ast
import logging
from pathlib import Path
from typing import List, Dict, Any

from .python_semantic_analyzer import SemanticRule, SemanticIssue, SemanticContext
from .framework_detector import FrameworkDetector, FrameworkDetection

logger = logging.getLogger(__name__)


class DjangoModelRule(SemanticRule):
    """Rule for Django model best practices."""
    
    def __init__(self):
        super().__init__(
            rule_id="django_model_best_practices",
            description="Checks Django model implementations for best practices",
            severity="warning"
        )
    
    def check(self, node: ast.AST, context: SemanticContext) -> List[SemanticIssue]:
        """Check Django model best practices."""
        issues = []
        
        if isinstance(node, ast.ClassDef):
            # Check if this is likely a Django model
            is_model = any(
                isinstance(base, ast.Name) and base.id == "Model"
                for base in node.bases
            ) or any(
                isinstance(base, ast.Attribute) and base.attr == "Model"
                for base in node.bases
            )
            
            if is_model:
                # Check for __str__ method
                has_str_method = any(
                    isinstance(child, ast.FunctionDef) and child.name == "__str__"
                    for child in node.body
                )
                
                if not has_str_method:
                    issues.append(SemanticIssue(
                        rule_id=self.rule_id,
                        severity=self.severity,
                        message=f"Django model '{node.name}' should implement __str__ method",
                        line_number=node.lineno,
                        column_number=node.col_offset,
                        suggestion="Add def __str__(self): return self.field_name"
                    ))
                
                # Check for Meta class
                has_meta = any(
                    isinstance(child, ast.ClassDef) and child.name == "Meta"
                    for child in node.body
                )
                
                # Count fields
                field_count = sum(
                    1 for child in node.body
                    if isinstance(child, ast.Assign) and
                    any(isinstance(target, ast.Name) for target in child.targets)
                )
                
                if field_count > 10 and not has_meta:
                    issues.append(SemanticIssue(
                        rule_id=self.rule_id,
                        severity="info",
                        message=f"Django model '{node.name}' with {field_count} fields should consider Meta class for ordering",
                        line_number=node.lineno,
                        column_number=node.col_offset,
                        suggestion="Add Meta class with ordering or other options"
                    ))
        
        return issues


class FlaskRouteRule(SemanticRule):
    """Rule for Flask route best practices."""
    
    def __init__(self):
        super().__init__(
            rule_id="flask_route_best_practices",
            description="Checks Flask route implementations for best practices",
            severity="warning"
        )
    
    def check(self, node: ast.AST, context: SemanticContext) -> List[SemanticIssue]:
        """Check Flask route best practices."""
        issues = []
        
        if isinstance(node, ast.FunctionDef):
            # Check if function has Flask route decorator
            has_route_decorator = any(
                self._is_route_decorator(decorator)
                for decorator in node.decorator_list
            )
            
            if has_route_decorator:
                # Check for proper HTTP methods specification
                route_decorators = [
                    decorator for decorator in node.decorator_list
                    if self._is_route_decorator(decorator)
                ]
                
                for decorator in route_decorators:
                    if isinstance(decorator, ast.Call):
                        # Check if methods are specified for non-GET routes
                        has_methods = any(
                            isinstance(keyword, ast.keyword) and keyword.arg == "methods"
                            for keyword in decorator.keywords
                        )
                        
                        # If function name suggests POST/PUT/DELETE but no methods specified
                        if (any(verb in node.name.lower() for verb in ['post', 'put', 'delete', 'create', 'update']) 
                            and not has_methods):
                            issues.append(SemanticIssue(
                                rule_id=self.rule_id,
                                severity=self.severity,
                                message=f"Flask route '{node.name}' should specify HTTP methods",
                                line_number=node.lineno,
                                column_number=node.col_offset,
                                suggestion="Add methods=['POST'] or appropriate HTTP methods"
                            ))
                
                # Check for proper error handling
                has_try_except = any(
                    isinstance(child, ast.Try)
                    for child in ast.walk(node)
                )
                
                if not has_try_except and len(node.body) > 5:  # Complex route without error handling
                    issues.append(SemanticIssue(
                        rule_id=self.rule_id,
                        severity="info",
                        message=f"Complex Flask route '{node.name}' should include error handling",
                        line_number=node.lineno,
                        column_number=node.col_offset,
                        suggestion="Add try-except blocks for proper error handling"
                    ))
        
        return issues
    
    def _is_route_decorator(self, decorator: ast.AST) -> bool:
        """Check if decorator is a Flask route decorator."""
        if isinstance(decorator, ast.Call):
            if isinstance(decorator.func, ast.Attribute):
                return decorator.func.attr == "route"
        elif isinstance(decorator, ast.Attribute):
            return decorator.attr == "route"
        return False


class FastAPIEndpointRule(SemanticRule):
    """Rule for FastAPI endpoint best practices."""
    
    def __init__(self):
        super().__init__(
            rule_id="fastapi_endpoint_best_practices",
            description="Checks FastAPI endpoint implementations for best practices",
            severity="warning"
        )
    
    def check(self, node: ast.AST, context: SemanticContext) -> List[SemanticIssue]:
        """Check FastAPI endpoint best practices."""
        issues = []
        
        if isinstance(node, ast.FunctionDef):
            # Check if function has FastAPI route decorator
            has_fastapi_decorator = any(
                self._is_fastapi_decorator(decorator)
                for decorator in node.decorator_list
            )
            
            if has_fastapi_decorator:
                # Check for proper type annotations
                if not node.returns:
                    issues.append(SemanticIssue(
                        rule_id=self.rule_id,
                        severity=self.severity,
                        message=f"FastAPI endpoint '{node.name}' should have return type annotation",
                        line_number=node.lineno,
                        column_number=node.col_offset,
                        suggestion="Add return type annotation for better API documentation"
                    ))
                
                # Check for async/await usage
                is_async = isinstance(node, ast.AsyncFunctionDef)
                has_await = any(
                    isinstance(child, ast.Await)
                    for child in ast.walk(node)
                )
                
                if not is_async and node.name.startswith(('get_', 'post_', 'put_', 'delete_')):
                    issues.append(SemanticIssue(
                        rule_id=self.rule_id,
                        severity="info",
                        message=f"FastAPI endpoint '{node.name}' should consider using async",
                        line_number=node.lineno,
                        column_number=node.col_offset,
                        suggestion="Use async def for better performance with FastAPI"
                    ))
                
                # Check for proper status code handling
                has_status_import = "status" in str(context.imports)
                if not has_status_import:
                    issues.append(SemanticIssue(
                        rule_id=self.rule_id,
                        severity="info",
                        message="Consider importing fastapi.status for proper HTTP status codes",
                        line_number=node.lineno,
                        column_number=node.col_offset,
                        suggestion="from fastapi import status"
                    ))
        
        return issues
    
    def _is_fastapi_decorator(self, decorator: ast.AST) -> bool:
        """Check if decorator is a FastAPI route decorator."""
        if isinstance(decorator, ast.Call):
            if isinstance(decorator.func, ast.Attribute):
                return decorator.func.attr in ['get', 'post', 'put', 'delete', 'patch']
        elif isinstance(decorator, ast.Attribute):
            return decorator.attr in ['get', 'post', 'put', 'delete', 'patch']
        return False


class PytestTestRule(SemanticRule):
    """Rule for pytest test best practices."""
    
    def __init__(self):
        super().__init__(
            rule_id="pytest_test_best_practices",
            description="Checks pytest test implementations for best practices",
            severity="info"
        )
    
    def check(self, node: ast.AST, context: SemanticContext) -> List[SemanticIssue]:
        """Check pytest test best practices."""
        issues = []
        
        if isinstance(node, ast.FunctionDef) and node.name.startswith('test_'):
            # Check for proper assertions
            has_assert = any(
                isinstance(child, ast.Assert)
                for child in ast.walk(node)
            )
            
            if not has_assert:
                issues.append(SemanticIssue(
                    rule_id=self.rule_id,
                    severity=self.severity,
                    message=f"Test function '{node.name}' should contain assertions",
                    line_number=node.lineno,
                    column_number=node.col_offset,
                    suggestion="Add assert statements to verify expected behavior"
                ))
            
            # Check for descriptive test names
            if len(node.name) < 10:  # Very short test name
                issues.append(SemanticIssue(
                    rule_id=self.rule_id,
                    severity="info",
                    message=f"Test function '{node.name}' should have more descriptive name",
                    line_number=node.lineno,
                    column_number=node.col_offset,
                    suggestion="Use descriptive names like test_user_can_login_with_valid_credentials"
                ))
            
            # Check for fixture usage in complex tests
            if len(node.body) > 10:  # Complex test
                has_fixtures = any(
                    arg.arg != 'self' for arg in node.args.args
                )
                
                if not has_fixtures:
                    issues.append(SemanticIssue(
                        rule_id=self.rule_id,
                        severity="info",
                        message=f"Complex test '{node.name}' should consider using fixtures",
                        line_number=node.lineno,
                        column_number=node.col_offset,
                        suggestion="Break down setup code into reusable fixtures"
                    ))
        
        return issues


class PandasDataFrameRule(SemanticRule):
    """Rule for pandas DataFrame best practices."""
    
    def __init__(self):
        super().__init__(
            rule_id="pandas_dataframe_best_practices",
            description="Checks pandas DataFrame usage for best practices",
            severity="warning"
        )
    
    def check(self, node: ast.AST, context: SemanticContext) -> List[SemanticIssue]:
        """Check pandas DataFrame best practices."""
        issues = []
        
        # Check for chained operations without assignment
        if isinstance(node, ast.Expr) and isinstance(node.value, ast.Call):
            call_chain = self._get_call_chain(node.value)
            
            # Look for pandas method chains
            pandas_methods = ['dropna', 'fillna', 'groupby', 'sort_values', 'reset_index']
            if any(method in call_chain for method in pandas_methods):
                if len(call_chain) > 3:  # Long chain
                    issues.append(SemanticIssue(
                        rule_id=self.rule_id,
                        severity="info",
                        message="Long pandas method chain should be broken down or assigned",
                        line_number=node.lineno,
                        column_number=node.col_offset,
                        suggestion="Break chain into multiple steps or use intermediate variables"
                    ))
        
        # Check for iterrows usage (anti-pattern)
        if isinstance(node, ast.Call):
            if isinstance(node.func, ast.Attribute) and node.func.attr == "iterrows":
                issues.append(SemanticIssue(
                    rule_id=self.rule_id,
                    severity="warning",
                    message="Using iterrows() is inefficient, consider vectorized operations",
                    line_number=node.lineno,
                    column_number=node.col_offset,
                    suggestion="Use vectorized operations, apply(), or itertuples() instead"
                ))
        
        return issues
    
    def _get_call_chain(self, node: ast.Call) -> List[str]:
        """Extract method names from a call chain."""
        methods = []
        current = node
        
        while isinstance(current, ast.Call):
            if isinstance(current.func, ast.Attribute):
                methods.append(current.func.attr)
                current = current.func.value
            else:
                break
        
        return methods


def create_framework_aware_registry(detected_frameworks: List[FrameworkDetection]) -> 'SemanticRuleRegistry':
    """Create a semantic rule registry with framework-specific rules."""
    from .semantic_rules import create_enhanced_rule_registry
    
    registry = create_enhanced_rule_registry()
    
    # Add framework-specific rules based on detected frameworks
    framework_names = [detection.framework.name.lower() for detection in detected_frameworks]
    
    if "django" in framework_names:
        registry.register_rule(
            DjangoModelRule(),
            [ast.ClassDef]
        )
    
    if "flask" in framework_names:
        registry.register_rule(
            FlaskRouteRule(),
            [ast.FunctionDef]
        )
    
    if "fastapi" in framework_names:
        registry.register_rule(
            FastAPIEndpointRule(),
            [ast.FunctionDef, ast.AsyncFunctionDef]
        )
    
    if "pytest" in framework_names:
        registry.register_rule(
            PytestTestRule(),
            [ast.FunctionDef]
        )
    
    if "pandas" in framework_names:
        registry.register_rule(
            PandasDataFrameRule(),
            [ast.Call, ast.Expr]
        )
    
    return registry


class FrameworkSpecificAnalyzer:
    """
    Analyzer that combines framework detection with framework-specific rules.
    """
    
    def __init__(self):
        self.framework_detector = FrameworkDetector()
    
    def analyze_project_with_frameworks(self, project_files: Dict[str, str]) -> Dict[str, Any]:
        """
        Analyze a project with framework-specific rules.
        
        Args:
            project_files: Dictionary mapping file paths to source code
            
        Returns:
            Comprehensive analysis including framework detection and specific rules
        """
        # Detect frameworks first
        framework_result = self.framework_detector.detect_frameworks(project_files)
        
        # Create framework-aware rule registry
        registry = create_framework_aware_registry(framework_result.detected_frameworks)
        
        # Analyze each file with framework-specific rules
        from .python_semantic_analyzer import PythonSemanticAnalyzer
        
        analyzer = PythonSemanticAnalyzer(registry)
        semantic_results = {}
        
        for file_path, source_code in project_files.items():
            if file_path.endswith('.py'):
                result = analyzer.analyze_source(source_code, Path(file_path))
                semantic_results[file_path] = result
        
        return {
            'framework_analysis': framework_result,
            'semantic_analysis': semantic_results,
            'framework_specific_issues': self._extract_framework_issues(semantic_results),
            'recommendations': self._generate_framework_recommendations(framework_result, semantic_results)
        }
    
    def _extract_framework_issues(self, semantic_results: Dict[str, Any]) -> Dict[str, List[Any]]:
        """Extract framework-specific issues from semantic analysis."""
        framework_issues = {}
        
        for file_path, result in semantic_results.items():
            file_framework_issues = [
                issue for issue in result.issues
                if any(framework in issue.rule_id for framework in 
                      ['django', 'flask', 'fastapi', 'pytest', 'pandas'])
            ]
            
            if file_framework_issues:
                framework_issues[file_path] = file_framework_issues
        
        return framework_issues
    
    def _generate_framework_recommendations(self, framework_result: Any, semantic_results: Dict[str, Any]) -> List[str]:
        """Generate comprehensive recommendations based on framework analysis."""
        recommendations = []
        
        # Add framework detection recommendations
        recommendations.extend(framework_result.recommendations)
        
        # Add framework-specific issue recommendations
        framework_issue_counts = {}
        for result in semantic_results.values():
            for issue in result.issues:
                for framework in ['django', 'flask', 'fastapi', 'pytest', 'pandas']:
                    if framework in issue.rule_id:
                        framework_issue_counts[framework] = framework_issue_counts.get(framework, 0) + 1
        
        for framework, count in framework_issue_counts.items():
            if count > 5:
                recommendations.append(f"Address {count} {framework} best practice violations")
        
        return recommendations
