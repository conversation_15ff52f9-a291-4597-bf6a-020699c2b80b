"""
Semantic Analysis Rules for Python Code

This module contains specific semantic rules that can be applied during AST analysis.
"""

import ast
import logging
from typing import List

from .python_semantic_analyzer import SemanticRule, SemanticIssue, SemanticContext

logger = logging.getLogger(__name__)


class UnusedImportRule(SemanticRule):
    """Rule to detect unused imports."""
    
    def __init__(self):
        super().__init__(
            rule_id="unused_import",
            description="Detects imported modules that are not used",
            severity="warning"
        )
    
    def check(self, node: ast.AST, context: SemanticContext) -> List[SemanticIssue]:
        """Check for unused imports."""
        issues = []
        
        if isinstance(node, (ast.Import, ast.ImportFrom)):
            # This is a simplified check - a full implementation would track usage
            # For now, we'll just demonstrate the structure
            pass
        
        return issues


class FunctionComplexityRule(SemanticRule):
    """Rule to detect overly complex functions."""
    
    def __init__(self, max_complexity: int = 10):
        super().__init__(
            rule_id="function_complexity",
            description=f"Detects functions with complexity > {max_complexity}",
            severity="warning"
        )
        self.max_complexity = max_complexity
    
    def check(self, node: ast.AST, context: SemanticContext) -> List[SemanticIssue]:
        """Check function complexity."""
        issues = []
        
        if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
            complexity = self._calculate_complexity(node)
            
            if complexity > self.max_complexity:
                issues.append(SemanticIssue(
                    rule_id=self.rule_id,
                    severity=self.severity,
                    message=f"Function '{node.name}' has complexity {complexity}, "
                           f"consider refactoring (max: {self.max_complexity})",
                    line_number=node.lineno,
                    column_number=node.col_offset,
                    suggestion="Break down the function into smaller, more focused functions"
                ))
        
        return issues
    
    def _calculate_complexity(self, node: ast.FunctionDef) -> int:
        """Calculate cyclomatic complexity of a function."""
        complexity = 1  # Base complexity
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(child, ast.ExceptHandler):
                complexity += 1
            elif isinstance(child, (ast.And, ast.Or)):
                complexity += 1
        
        return complexity


class MutableDefaultArgumentRule(SemanticRule):
    """Rule to detect mutable default arguments."""
    
    def __init__(self):
        super().__init__(
            rule_id="mutable_default_argument",
            description="Detects mutable default arguments in function definitions",
            severity="warning"
        )
    
    def check(self, node: ast.AST, context: SemanticContext) -> List[SemanticIssue]:
        """Check for mutable default arguments."""
        issues = []
        
        if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
            for default in node.args.defaults:
                if self._is_mutable_default(default):
                    issues.append(SemanticIssue(
                        rule_id=self.rule_id,
                        severity=self.severity,
                        message=f"Function '{node.name}' has mutable default argument",
                        line_number=node.lineno,
                        column_number=node.col_offset,
                        suggestion="Use None as default and create the mutable object inside the function"
                    ))
        
        return issues
    
    def _is_mutable_default(self, node: ast.AST) -> bool:
        """Check if a default argument is mutable."""
        return isinstance(node, (ast.List, ast.Dict, ast.Set))


class ClassNamingRule(SemanticRule):
    """Rule to check class naming conventions."""
    
    def __init__(self):
        super().__init__(
            rule_id="class_naming",
            description="Checks that class names follow PascalCase convention",
            severity="info"
        )
    
    def check(self, node: ast.AST, context: SemanticContext) -> List[SemanticIssue]:
        """Check class naming conventions."""
        issues = []
        
        if isinstance(node, ast.ClassDef):
            if not self._is_pascal_case(node.name):
                issues.append(SemanticIssue(
                    rule_id=self.rule_id,
                    severity=self.severity,
                    message=f"Class '{node.name}' should use PascalCase naming",
                    line_number=node.lineno,
                    column_number=node.col_offset,
                    suggestion="Use PascalCase for class names (e.g., MyClass)"
                ))
        
        return issues
    
    def _is_pascal_case(self, name: str) -> bool:
        """Check if a name follows PascalCase convention."""
        return name[0].isupper() and '_' not in name


class FunctionNamingRule(SemanticRule):
    """Rule to check function naming conventions."""
    
    def __init__(self):
        super().__init__(
            rule_id="function_naming",
            description="Checks that function names follow snake_case convention",
            severity="info"
        )
    
    def check(self, node: ast.AST, context: SemanticContext) -> List[SemanticIssue]:
        """Check function naming conventions."""
        issues = []
        
        if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
            if not self._is_snake_case(node.name):
                issues.append(SemanticIssue(
                    rule_id=self.rule_id,
                    severity=self.severity,
                    message=f"Function '{node.name}' should use snake_case naming",
                    line_number=node.lineno,
                    column_number=node.col_offset,
                    suggestion="Use snake_case for function names (e.g., my_function)"
                ))
        
        return issues
    
    def _is_snake_case(self, name: str) -> bool:
        """Check if a name follows snake_case convention."""
        # Allow special methods like __init__
        if name.startswith('__') and name.endswith('__'):
            return True
        
        return name.islower() and ' ' not in name


class TooManyArgumentsRule(SemanticRule):
    """Rule to detect functions with too many arguments."""
    
    def __init__(self, max_args: int = 5):
        super().__init__(
            rule_id="too_many_arguments",
            description=f"Detects functions with more than {max_args} arguments",
            severity="warning"
        )
        self.max_args = max_args
    
    def check(self, node: ast.AST, context: SemanticContext) -> List[SemanticIssue]:
        """Check for functions with too many arguments."""
        issues = []
        
        if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
            arg_count = len(node.args.args)
            
            # Don't count 'self' for methods
            if arg_count > 0 and context.current_scope.startswith('class:'):
                arg_count -= 1
            
            if arg_count > self.max_args:
                issues.append(SemanticIssue(
                    rule_id=self.rule_id,
                    severity=self.severity,
                    message=f"Function '{node.name}' has {arg_count} arguments, "
                           f"consider reducing (max: {self.max_args})",
                    line_number=node.lineno,
                    column_number=node.col_offset,
                    suggestion="Consider using a configuration object or breaking down the function"
                ))
        
        return issues


class EmptyExceptRule(SemanticRule):
    """Rule to detect empty except blocks."""
    
    def __init__(self):
        super().__init__(
            rule_id="empty_except",
            description="Detects empty except blocks that silently ignore exceptions",
            severity="error"
        )
    
    def check(self, node: ast.AST, context: SemanticContext) -> List[SemanticIssue]:
        """Check for empty except blocks."""
        issues = []
        
        if isinstance(node, ast.ExceptHandler):
            # Check if the except block is empty or only contains pass
            if not node.body or (len(node.body) == 1 and isinstance(node.body[0], ast.Pass)):
                issues.append(SemanticIssue(
                    rule_id=self.rule_id,
                    severity=self.severity,
                    message="Empty except block silently ignores exceptions",
                    line_number=node.lineno,
                    column_number=node.col_offset,
                    suggestion="Add proper exception handling or at least log the exception"
                ))
        
        return issues


class ArchitecturalSmellRule(SemanticRule):
    """Rule to detect architectural smells and anti-patterns."""

    def __init__(self):
        super().__init__(
            rule_id="architectural_smell",
            description="Detects architectural smells and anti-patterns",
            severity="warning"
        )

    def check(self, node: ast.AST, context: SemanticContext) -> List[SemanticIssue]:
        """Check for architectural smells."""
        issues = []

        if isinstance(node, ast.ClassDef):
            # God class detection (too many methods)
            method_count = sum(1 for child in node.body if isinstance(child, (ast.FunctionDef, ast.AsyncFunctionDef)))
            if method_count > 20:
                issues.append(SemanticIssue(
                    rule_id=self.rule_id,
                    severity="error",
                    message=f"God class detected: '{node.name}' has {method_count} methods",
                    line_number=node.lineno,
                    column_number=node.col_offset,
                    suggestion="Consider breaking this class into smaller, more focused classes"
                ))

            # Data class without methods (anemic domain model)
            if method_count == 0 and len(node.body) > 1:
                issues.append(SemanticIssue(
                    rule_id=self.rule_id,
                    severity="info",
                    message=f"Potential anemic model: '{node.name}' has only data, no behavior",
                    line_number=node.lineno,
                    column_number=node.col_offset,
                    suggestion="Consider adding behavior methods to this class"
                ))

        return issues


class CohesionAnalysisRule(SemanticRule):
    """Rule to analyze class and module cohesion."""

    def __init__(self):
        super().__init__(
            rule_id="cohesion_analysis",
            description="Analyzes cohesion of classes and modules",
            severity="info"
        )

    def check(self, node: ast.AST, context: SemanticContext) -> List[SemanticIssue]:
        """Check cohesion metrics."""
        issues = []

        if isinstance(node, ast.ClassDef):
            # Analyze method cohesion (simplified)
            methods = [child for child in node.body if isinstance(child, (ast.FunctionDef, ast.AsyncFunctionDef))]
            attributes = [child for child in node.body if isinstance(child, ast.Assign)]

            if len(methods) > 5 and len(attributes) > 10:
                issues.append(SemanticIssue(
                    rule_id=self.rule_id,
                    severity="warning",
                    message=f"Low cohesion suspected in '{node.name}': {len(methods)} methods, {len(attributes)} attributes",
                    line_number=node.lineno,
                    column_number=node.col_offset,
                    suggestion="Consider if this class has too many responsibilities"
                ))

        return issues


class DesignPatternDetectionRule(SemanticRule):
    """Rule to detect and validate design patterns."""

    def __init__(self):
        super().__init__(
            rule_id="design_pattern_detection",
            description="Detects and validates design pattern implementations",
            severity="info"
        )

    def check(self, node: ast.AST, context: SemanticContext) -> List[SemanticIssue]:
        """Check for design pattern implementations."""
        issues = []

        if isinstance(node, ast.ClassDef):
            class_name = node.name.lower()

            # Singleton pattern detection
            if 'singleton' in class_name:
                has_instance_method = any(
                    isinstance(child, ast.FunctionDef) and child.name in ['get_instance', 'instance']
                    for child in node.body
                )
                if not has_instance_method:
                    issues.append(SemanticIssue(
                        rule_id=self.rule_id,
                        severity="warning",
                        message=f"Singleton class '{node.name}' missing instance method",
                        line_number=node.lineno,
                        column_number=node.col_offset,
                        suggestion="Implement get_instance() or instance() method"
                    ))

            # Factory pattern detection
            if 'factory' in class_name:
                has_create_method = any(
                    isinstance(child, ast.FunctionDef) and 'create' in child.name.lower()
                    for child in node.body
                )
                if not has_create_method:
                    issues.append(SemanticIssue(
                        rule_id=self.rule_id,
                        severity="info",
                        message=f"Factory class '{node.name}' should have create methods",
                        line_number=node.lineno,
                        column_number=node.col_offset,
                        suggestion="Add create_* methods to factory class"
                    ))

        return issues


def create_default_rule_registry():
    """Create a rule registry with default semantic rules."""
    from .python_semantic_analyzer import SemanticRuleRegistry

    registry = SemanticRuleRegistry()

    # Register basic rules
    registry.register_rule(
        UnusedImportRule(),
        [ast.Import, ast.ImportFrom]
    )

    registry.register_rule(
        FunctionComplexityRule(),
        [ast.FunctionDef, ast.AsyncFunctionDef]
    )

    registry.register_rule(
        MutableDefaultArgumentRule(),
        [ast.FunctionDef, ast.AsyncFunctionDef]
    )

    registry.register_rule(
        ClassNamingRule(),
        [ast.ClassDef]
    )

    registry.register_rule(
        FunctionNamingRule(),
        [ast.FunctionDef, ast.AsyncFunctionDef]
    )

    registry.register_rule(
        TooManyArgumentsRule(),
        [ast.FunctionDef, ast.AsyncFunctionDef]
    )

    registry.register_rule(
        EmptyExceptRule(),
        [ast.ExceptHandler]
    )

    # Register advanced architectural rules
    registry.register_rule(
        ArchitecturalSmellRule(),
        [ast.ClassDef]
    )

    registry.register_rule(
        CohesionAnalysisRule(),
        [ast.ClassDef]
    )

    registry.register_rule(
        DesignPatternDetectionRule(),
        [ast.ClassDef]
    )

    return registry


def create_enhanced_rule_registry():
    """Create an enhanced rule registry with advanced semantic rules."""
    registry = create_default_rule_registry()

    # Add type analysis rules
    from .type_analyzer import MissingTypeAnnotationRule, InconsistentTypeUsageRule

    registry.register_rule(
        MissingTypeAnnotationRule(),
        [ast.FunctionDef, ast.AsyncFunctionDef]
    )

    registry.register_rule(
        InconsistentTypeUsageRule(),
        [ast.FunctionDef, ast.AsyncFunctionDef]
    )

    return registry
