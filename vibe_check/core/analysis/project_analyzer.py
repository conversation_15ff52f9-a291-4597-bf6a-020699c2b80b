"""
Project Analyzer Module
==================

This module provides the ProjectAnalyzer class, which is responsible for
analyzing an entire project. It abstracts the details of project analysis
for use by the analysis engine.
"""

import logging
import os
import time
from pathlib import Path
from typing import Dict, Any, Union, Optional

from ..models.project_metrics import ProjectMetrics
from ..utils.file_utils import find_python_files
from .file_analyzer import FileAnalyzer
from .metrics_aggregator import MetricsAggregator
from .framework_rules import FrameworkSpecificAnalyzer
from .project_meritocracy_analyzer import ProjectMeritocracyAnalyzer
from .semantic_output_formatter import SemanticOutputFormatter
from .performance_optimizer import PerformanceOptimizer

logger = logging.getLogger("vibe_check_analysis")


class ProjectAnalyzer:
    """
    Analyzes an entire project.
    
    This class abstracts the details of project analysis for use by
    the analysis engine.
    """
    
    def __init__(self,
                 project_path: Union[str, Path],
                 config: Optional[Dict[str, Any]] = None,
                 output_dir: Optional[Union[str, Path]] = None,
                 enable_semantic_analysis: bool = True):
        """
        Initialize the project analyzer.

        Args:
            project_path: Path to the project to analyze
            config: Optional configuration dictionary
            output_dir: Optional directory to write output files to
            enable_semantic_analysis: Whether to enable enhanced semantic analysis
        """
        self.project_path = Path(project_path).absolute()
        self.config = config or {}
        self.output_dir = Path(output_dir) if output_dir else None
        self.enable_semantic_analysis = enable_semantic_analysis

        # Create output directory if specified
        if self.output_dir:
            os.makedirs(self.output_dir, exist_ok=True)

        # Initialize file analyzer
        self.file_analyzer = FileAnalyzer(
            project_path=self.project_path,
            tools_config=self.config,
            enable_semantic_analysis=enable_semantic_analysis
        )

        # Initialize metrics aggregator
        self.metrics_aggregator = MetricsAggregator()

        # Initialize enhanced semantic analyzers
        if enable_semantic_analysis:
            self.framework_analyzer = FrameworkSpecificAnalyzer()
            self.meritocracy_analyzer = ProjectMeritocracyAnalyzer()
            self.output_formatter = SemanticOutputFormatter()
            self.performance_optimizer = PerformanceOptimizer(self.project_path)
        else:
            self.framework_analyzer = None
            self.meritocracy_analyzer = None
            self.output_formatter = None
            self.performance_optimizer = None
        
    async def analyze_project(self) -> ProjectMetrics:
        """
        Analyze the project.
        
        Returns:
            ProjectMetrics object with analysis results
        """
        logger.info(f"Starting analysis of project: {self.project_path}")
        start_time = time.time()
        
        # Initialize project metrics
        metrics = ProjectMetrics(
            project_path=str(self.project_path),
            files={},
            directories={}
        )
        
        # Find all Python files in the project
        python_files = find_python_files(self.project_path)
        logger.info(f"Found {len(python_files)} Python files")
        
        # Process each file
        for file_path in python_files:
            try:
                relative_path = file_path.relative_to(self.project_path)
            except ValueError:
                # Handle symlink issues on macOS where paths might resolve differently
                try:
                    relative_path = file_path.resolve().relative_to(self.project_path.resolve())
                except ValueError:
                    relative_path = Path(file_path.name)
            logger.info(f"Analyzing file: {relative_path}")
            
            # Analyze the file
            file_metrics = await self.file_analyzer.analyze_file(file_path)
            
            # Add file metrics to project metrics
            try:
                relative_path = file_path.relative_to(self.project_path)
            except ValueError:
                # Handle symlink issues on macOS where paths might resolve differently
                try:
                    relative_path = file_path.resolve().relative_to(self.project_path.resolve())
                except ValueError:
                    relative_path = Path(file_path.name)
            metrics.files[str(relative_path)] = file_metrics
        
        # Aggregate metrics
        self.metrics_aggregator.aggregate_metrics(metrics)

        # Perform enhanced semantic analysis if enabled
        if self.enable_semantic_analysis and self.framework_analyzer:
            logger.info("Performing enhanced semantic analysis...")
            semantic_start = time.time()

            # Collect source code for semantic analysis
            project_files = {}
            for file_path in python_files:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        relative_path = file_path.relative_to(self.project_path)
                        project_files[str(relative_path)] = f.read()
                except Exception as e:
                    logger.warning(f"Could not read {file_path} for semantic analysis: {e}")

            # Perform framework-specific analysis
            framework_analysis = self.framework_analyzer.analyze_project_with_frameworks(project_files)

            # Perform meritocracy analysis
            if self.meritocracy_analyzer:
                meritocracy_result = self.meritocracy_analyzer.analyze_project_meritocracy(metrics)

                # Add semantic analysis results to metrics
                if not hasattr(metrics, 'semantic_analysis'):
                    metrics.semantic_analysis = {}

                metrics.semantic_analysis = {
                    'framework_analysis': framework_analysis['framework_analysis'],
                    'semantic_results': framework_analysis['semantic_analysis'],
                    'framework_issues': framework_analysis['framework_specific_issues'],
                    'meritocracy_result': meritocracy_result,
                    'recommendations': framework_analysis['recommendations']
                }

                # Generate comprehensive report if output formatter is available
                if self.output_formatter:
                    comprehensive_report = self.output_formatter.format_comprehensive_report(
                        framework_analysis['semantic_analysis'],
                        meritocracy_result,
                        {}  # Type analysis results would go here
                    )
                    metrics.semantic_analysis['comprehensive_report'] = comprehensive_report

            semantic_end = time.time()
            logger.info(f"Semantic analysis completed in {semantic_end - semantic_start:.2f} seconds")

        # Log completion
        end_time = time.time()
        logger.info(f"Analysis completed in {end_time - start_time:.2f} seconds")
        logger.info(f"Total files: {metrics.total_file_count}")
        logger.info(f"Total lines: {metrics.total_line_count}")
        logger.info(f"Average complexity: {metrics.avg_complexity:.2f}")
        logger.info(f"Total issues: {metrics.issue_count}")

        if self.enable_semantic_analysis and hasattr(metrics, 'semantic_analysis'):
            semantic_data = metrics.semantic_analysis
            if 'meritocracy_result' in semantic_data:
                meritocracy = semantic_data['meritocracy_result']
                logger.info(f"Project meritocracy score: {meritocracy.overall_score:.1f}/10")
                logger.info(f"Architectural patterns detected: {len(meritocracy.architectural_patterns)}")
                logger.info(f"Framework-specific issues: {len(semantic_data.get('framework_issues', {}))}")

        return metrics
