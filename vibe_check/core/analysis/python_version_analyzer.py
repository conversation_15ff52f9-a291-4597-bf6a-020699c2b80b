"""
Python Version Compatibility Analyzer

This module analyzes Python code for version compatibility issues and
provides recommendations for supporting multiple Python versions.
"""

import ast
import logging
from pathlib import Path
from dataclasses import dataclass, field

from .python_semantic_analyzer import SemanticRule, SemanticIssue, SemanticContext

logger = logging.getLogger(__name__)


@dataclass
class VersionFeature:
    """Represents a Python feature and its version requirements."""
    
    feature_name: str
    min_version: Tuple[int, int]  # (major, minor)
    description: str
    alternative: Optional[str] = None


@dataclass
class CompatibilityIssue:
    """Represents a version compatibility issue."""
    
    feature: VersionFeature
    line_number: int
    column_number: int
    code_snippet: str
    severity: str = "warning"


@dataclass
class VersionCompatibilityResult:
    """Results from version compatibility analysis."""
    
    min_python_version: Tuple[int, int] = (3, 6)
    max_python_version: Tuple[int, int] = (3, 12)
    compatibility_issues: List[CompatibilityIssue] = field(default_factory=list)
    features_used: List[VersionFeature] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    supported_versions: List[str] = field(default_factory=list)


class PythonVersionAnalyzer(ast.NodeVisitor):
    """
    Analyzes Python code for version compatibility.
    """
    
    def __init__(self, target_versions: Optional[List[Tuple[int, int]]] = None):
        self.target_versions = target_versions or [(3, 8), (3, 9), (3, 10), (3, 11), (3, 12)]
        self.result = VersionCompatibilityResult()
        self.features_db = self._build_features_database()
        self.current_line = 1
    
    def analyze_source(self, source_code: str, file_path: Path) -> VersionCompatibilityResult:
        """
        Analyze Python source code for version compatibility.
        
        Args:
            source_code: Python source code to analyze
            file_path: Path to the source file
            
        Returns:
            VersionCompatibilityResult with compatibility analysis
        """
        try:
            tree = ast.parse(source_code, filename=str(file_path))
            self.result = VersionCompatibilityResult()
            self.visit(tree)
            self._calculate_version_requirements()
            self._generate_recommendations()
            return self.result
            
        except SyntaxError as e:
            logger.warning(f"Syntax error in {file_path}: {e}")
            return VersionCompatibilityResult()
        except Exception as e:
            logger.error(f"Error analyzing version compatibility in {file_path}: {e}")
            return VersionCompatibilityResult()
    
    def _build_features_database(self) -> Dict[str, VersionFeature]:
        """Build database of Python features and their version requirements."""
        return {
            # Python 3.8 features
            "walrus_operator": VersionFeature(
                "Assignment Expressions (:=)",
                (3, 8),
                "Walrus operator for assignment in expressions",
                "Use regular assignment statements"
            ),
            "positional_only": VersionFeature(
                "Positional-only parameters",
                (3, 8),
                "Function parameters that can only be passed positionally",
                "Use regular parameters with documentation"
            ),
            
            # Python 3.9 features
            "dict_union": VersionFeature(
                "Dictionary union operators",
                (3, 9),
                "Dict union (|) and update (|=) operators",
                "Use dict.update() or {**dict1, **dict2}"
            ),
            "generic_types": VersionFeature(
                "Generic types (list[int])",
                (3, 9),
                "Built-in generic types without typing module",
                "Use typing.List[int] instead of list[int]"
            ),
            
            # Python 3.10 features
            "match_statement": VersionFeature(
                "Match statements",
                (3, 10),
                "Structural pattern matching with match/case",
                "Use if/elif chains or dictionary dispatch"
            ),
            "union_types": VersionFeature(
                "Union types (X | Y)",
                (3, 10),
                "Union types using | operator",
                "Use typing.Union[X, Y]"
            ),
            
            # Python 3.11 features
            "exception_groups": VersionFeature(
                "Exception Groups",
                (3, 11),
                "ExceptionGroup and except* syntax",
                "Use traditional exception handling"
            ),
            
            # Python 3.12 features
            "f_string_debug": VersionFeature(
                "Enhanced f-string features",
                (3, 12),
                "Advanced f-string formatting",
                "Use older f-string syntax"
            )
        }
    
    def visit_NamedExpr(self, node: ast.NamedExpr) -> None:
        """Visit walrus operator (assignment expressions)."""
        feature = self.features_db["walrus_operator"]
        self.result.features_used.append(feature)
        self.result.compatibility_issues.append(CompatibilityIssue(
            feature=feature,
            line_number=node.lineno,
            column_number=node.col_offset,
            code_snippet=f"Assignment expression at line {node.lineno}"
        ))
        self.generic_visit(node)
    
    def visit_FunctionDef(self, node: ast.FunctionDef) -> None:
        """Visit function definitions to check for positional-only parameters."""
        if node.args.posonlyargs:
            feature = self.features_db["positional_only"]
            self.result.features_used.append(feature)
            self.result.compatibility_issues.append(CompatibilityIssue(
                feature=feature,
                line_number=node.lineno,
                column_number=node.col_offset,
                code_snippet=f"Function '{node.name}' uses positional-only parameters"
            ))
        self.generic_visit(node)
    
    def visit_BinOp(self, node: ast.BinOp) -> None:
        """Visit binary operations to check for dict union operators."""
        if isinstance(node.op, ast.BitOr):
            # Check if this might be a dict union (simplified check)
            if hasattr(node.left, 'id') or hasattr(node.right, 'id'):
                # This could be a union type or dict union
                # More sophisticated analysis would be needed to distinguish
                pass
        self.generic_visit(node)
    
    def visit_Subscript(self, node: ast.Subscript) -> None:
        """Visit subscripts to check for generic types."""
        if isinstance(node.value, ast.Name):
            # Check for built-in generic types
            builtin_generics = ['list', 'dict', 'set', 'tuple', 'frozenset']
            if node.value.id in builtin_generics:
                feature = self.features_db["generic_types"]
                self.result.features_used.append(feature)
                self.result.compatibility_issues.append(CompatibilityIssue(
                    feature=feature,
                    line_number=node.lineno,
                    column_number=node.col_offset,
                    code_snippet=f"Generic type '{node.value.id}[...]' at line {node.lineno}"
                ))
        self.generic_visit(node)
    
    def visit_Match(self, node: ast.Match) -> None:
        """Visit match statements (Python 3.10+)."""
        feature = self.features_db["match_statement"]
        self.result.features_used.append(feature)
        self.result.compatibility_issues.append(CompatibilityIssue(
            feature=feature,
            line_number=node.lineno,
            column_number=node.col_offset,
            code_snippet=f"Match statement at line {node.lineno}",
            severity="error"  # Match statements are a major compatibility issue
        ))
        self.generic_visit(node)
    
    def _calculate_version_requirements(self) -> None:
        """Calculate minimum and maximum Python version requirements."""
        if not self.result.features_used:
            self.result.min_python_version = (3, 6)  # Conservative default
            return
        
        # Find the highest minimum version requirement
        min_versions = [feature.min_version for feature in self.result.features_used]
        self.result.min_python_version = max(min_versions) if min_versions else (3, 6)
        
        # Calculate supported versions
        min_major, min_minor = self.result.min_python_version
        self.result.supported_versions = [
            f"{major}.{minor}"
            for major, minor in self.target_versions
            if (major, minor) >= (min_major, min_minor)
        ]
    
    def _generate_recommendations(self) -> None:
        """Generate version compatibility recommendations."""
        recommendations = []
        
        if self.result.min_python_version > (3, 8):
            recommendations.append(
                f"Code requires Python {self.result.min_python_version[0]}.{self.result.min_python_version[1]}+ "
                "due to modern language features"
            )
        
        if len(self.result.compatibility_issues) > 0:
            recommendations.append(
                "Consider providing fallback implementations for older Python versions"
            )
        
        # Group issues by severity
        error_issues = [issue for issue in self.result.compatibility_issues if issue.severity == "error"]
        if error_issues:
            recommendations.append(
                f"Found {len(error_issues)} critical compatibility issues that prevent "
                "running on older Python versions"
            )
        
        # Specific feature recommendations
        features_used = {feature.feature_name for feature in self.result.features_used}
        
        if "Assignment Expressions (:=)" in features_used:
            recommendations.append(
                "Walrus operator (:=) requires Python 3.8+. Consider using regular assignments for broader compatibility"
            )
        
        if "Match statements" in features_used:
            recommendations.append(
                "Match statements require Python 3.10+. Consider using if/elif chains for broader compatibility"
            )
        
        if "Generic types (list[int])" in features_used:
            recommendations.append(
                "Built-in generic types require Python 3.9+. Use typing.List[int] for broader compatibility"
            )
        
        self.result.recommendations = recommendations


class PythonVersionCompatibilityRule(SemanticRule):
    """Semantic rule for Python version compatibility."""
    
    def __init__(self, target_versions: Optional[List[Tuple[int, int]]] = None):
        super().__init__(
            rule_id="python_version_compatibility",
            description="Checks Python version compatibility",
            severity="warning"
        )
        self.analyzer = PythonVersionAnalyzer(target_versions)
    
    def check(self, node: ast.AST, context: SemanticContext) -> List[SemanticIssue]:
        """Check for version compatibility issues."""
        issues = []
        
        # This rule works at the module level, so we only process Module nodes
        if isinstance(node, ast.Module):
            try:
                # Get the source code (this is a simplified approach)
                # In a real implementation, we'd need access to the original source
                result = self.analyzer.result
                
                for compat_issue in result.compatibility_issues:
                    issues.append(SemanticIssue(
                        rule_id=self.rule_id,
                        severity=compat_issue.severity,
                        message=f"Version compatibility: {compat_issue.feature.description}",
                        line_number=compat_issue.line_number,
                        column_number=compat_issue.column_number,
                        suggestion=compat_issue.feature.alternative
                    ))
            except Exception as e:
                logger.warning(f"Error in version compatibility check: {e}")
        
        return issues


def analyze_project_python_compatibility(
    project_files: Dict[str, str],
    target_versions: Optional[List[Tuple[int, int]]] = None
) -> Dict[str, VersionCompatibilityResult]:
    """
    Analyze Python version compatibility for an entire project.
    
    Args:
        project_files: Dictionary mapping file paths to source code
        target_versions: List of Python versions to target
        
    Returns:
        Dictionary mapping file paths to compatibility results
    """
    analyzer = PythonVersionAnalyzer(target_versions)
    results = {}
    
    for file_path, source_code in project_files.items():
        if file_path.endswith('.py'):
            results[file_path] = analyzer.analyze_source(source_code, Path(file_path))
    
    return results


def get_project_version_summary(results: Dict[str, VersionCompatibilityResult]) -> Dict[str, Any]:
    """
    Get a summary of version compatibility for the entire project.
    
    Args:
        results: Version compatibility results per file
        
    Returns:
        Project-wide compatibility summary
    """
    if not results:
        return {"min_version": "3.6", "issues": 0, "recommendations": []}
    
    # Find the highest minimum version across all files
    min_versions = []
    all_issues = []
    all_recommendations = set()
    
    for result in results.values():
        min_versions.append(result.min_python_version)
        all_issues.extend(result.compatibility_issues)
        all_recommendations.update(result.recommendations)
    
    project_min_version = max(min_versions) if min_versions else (3, 6)
    
    return {
        "min_version": f"{project_min_version[0]}.{project_min_version[1]}",
        "issues": len(all_issues),
        "critical_issues": len([issue for issue in all_issues if issue.severity == "error"]),
        "recommendations": list(all_recommendations),
        "files_analyzed": len(results),
        "compatible_versions": [
            f"{major}.{minor}"
            for major, minor in [(3, 8), (3, 9), (3, 10), (3, 11), (3, 12)]
            if (major, minor) >= project_min_version
        ]
    }
