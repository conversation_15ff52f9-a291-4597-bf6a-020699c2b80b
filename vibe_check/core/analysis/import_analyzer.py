"""
Advanced Import Analysis Module for Vibe Check

This module provides specialized Python import analysis capabilities that go beyond
basic static analysis tools. It focuses on:

1. Circular import detection with detailed cycle visualization
2. Import dependency mapping and analysis
3. Import optimization suggestions
4. Unused import detection and cleanup recommendations
5. Import complexity analysis
6. Module coupling analysis through imports

This is a key differentiator for Vibe Check as a specialized Python analysis platform.
"""

import ast
import os
from pathlib import Path
from dataclasses import dataclass, field
import networkx as nx
from typing import List, Dict, Set, Optional, Any

from ..utils.fs_utils import find_python_files


@dataclass
class ImportInfo:
    """Information about a single import statement."""
    module: str
    alias: Optional[str] = None
    from_module: Optional[str] = None
    line_number: int = 0
    is_relative: bool = False
    is_star_import: bool = False
    imported_names: List[str] = field(default_factory=list)


@dataclass
class CircularDependency:
    """Information about a circular dependency."""
    cycle: List[str]
    severity: str  # 'low', 'medium', 'high'
    description: str
    suggestions: List[str] = field(default_factory=list)


@dataclass
class ImportAnalysisResult:
    """Results of import analysis for a project."""
    file_imports: Dict[str, List[ImportInfo]]
    dependency_graph: nx.DiGraph
    circular_dependencies: List[CircularDependency]
    unused_imports: Dict[str, List[ImportInfo]]
    optimization_suggestions: Dict[str, List[str]]
    coupling_metrics: Dict[str, Dict[str, Any]]
    import_complexity_scores: Dict[str, float]


class ImportAnalyzer:
    """Advanced import analyzer for Python projects."""
    
    def __init__(self, project_path: str):
        """Initialize the import analyzer.
        
        Args:
            project_path: Path to the Python project to analyze
        """
        self.project_path = Path(project_path)
        self.file_imports: Dict[str, List[ImportInfo]] = {}
        self.dependency_graph = nx.DiGraph()
        
    def analyze_project(self) -> ImportAnalysisResult:
        """Perform comprehensive import analysis on the project.
        
        Returns:
            ImportAnalysisResult containing all analysis results
        """
        # Step 1: Extract imports from all Python files
        self._extract_imports()
        
        # Step 2: Build dependency graph
        self._build_dependency_graph()
        
        # Step 3: Detect circular dependencies
        circular_deps = self._detect_circular_dependencies()
        
        # Step 4: Find unused imports
        unused_imports = self._find_unused_imports()
        
        # Step 5: Generate optimization suggestions
        optimization_suggestions = self._generate_optimization_suggestions()
        
        # Step 6: Calculate coupling metrics
        coupling_metrics = self._calculate_coupling_metrics()
        
        # Step 7: Calculate import complexity scores
        complexity_scores = self._calculate_import_complexity()
        
        return ImportAnalysisResult(
            file_imports=self.file_imports,
            dependency_graph=self.dependency_graph,
            circular_dependencies=circular_deps,
            unused_imports=unused_imports,
            optimization_suggestions=optimization_suggestions,
            coupling_metrics=coupling_metrics,
            import_complexity_scores=complexity_scores
        )
    
    def _extract_imports(self) -> None:
        """Extract import information from all Python files."""
        python_files = find_python_files(str(self.project_path))
        
        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                imports = self._extract_imports_from_ast(tree)
                
                # Convert absolute path to relative path
                rel_path = os.path.relpath(file_path, self.project_path)
                self.file_imports[rel_path] = imports
                
            except (SyntaxError, UnicodeDecodeError) as e:
                # Skip files that can't be parsed
                continue
    
    def _extract_imports_from_ast(self, tree: ast.AST) -> List[ImportInfo]:
        """Extract import information from an AST.
        
        Args:
            tree: The AST to analyze
            
        Returns:
            List of ImportInfo objects
        """
        imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(ImportInfo(
                        module=alias.name,
                        alias=alias.asname,
                        line_number=node.lineno,
                        is_relative=False
                    ))
            
            elif isinstance(node, ast.ImportFrom):
                if node.module is None:
                    continue
                    
                is_relative = node.level > 0
                
                for alias in node.names:
                    imports.append(ImportInfo(
                        module=alias.name,
                        alias=alias.asname,
                        from_module=node.module,
                        line_number=node.lineno,
                        is_relative=is_relative,
                        is_star_import=(alias.name == '*'),
                        imported_names=[alias.name] if alias.name != '*' else []
                    ))
        
        return imports
    
    def _build_dependency_graph(self) -> None:
        """Build a dependency graph from the extracted imports."""
        for file_path, imports in self.file_imports.items():
            # Add the file as a node
            self.dependency_graph.add_node(file_path)
            
            for import_info in imports:
                # Determine the target module
                if import_info.from_module:
                    target_module = import_info.from_module
                else:
                    target_module = import_info.module
                
                # Try to resolve to a file in the project
                resolved_file = self._resolve_module_to_file(target_module, file_path)
                
                if resolved_file and resolved_file in self.file_imports:
                    # Add edge from current file to imported file
                    self.dependency_graph.add_edge(file_path, resolved_file)
    
    def _resolve_module_to_file(self, module: str, current_file: str) -> Optional[str]:
        """Resolve a module name to a file path within the project.
        
        Args:
            module: The module name to resolve
            current_file: The file making the import
            
        Returns:
            Relative file path if found, None otherwise
        """
        # This is a simplified resolution - could be enhanced
        # to handle more complex Python import resolution rules
        
        # Handle relative imports
        if module.startswith('.'):
            current_dir = os.path.dirname(current_file)
            # Simplified relative import handling
            module = module.lstrip('.')
            if module:
                potential_path = os.path.join(current_dir, module.replace('.', '/') + '.py')
            else:
                potential_path = os.path.join(current_dir, '__init__.py')
        else:
            # Handle absolute imports within the project
            potential_path = module.replace('.', '/') + '.py'
        
        # Check if the file exists in our analyzed files
        if potential_path in self.file_imports:
            return potential_path
        
        # Check for package __init__.py
        init_path = module.replace('.', '/') + '/__init__.py'
        if init_path in self.file_imports:
            return init_path
        
        return None
    
    def _detect_circular_dependencies(self) -> List[CircularDependency]:
        """Detect circular dependencies in the import graph.
        
        Returns:
            List of CircularDependency objects
        """
        circular_deps = []
        
        try:
            # Find all strongly connected components with more than one node
            sccs = list(nx.strongly_connected_components(self.dependency_graph))
            
            for scc in sccs:
                if len(scc) > 1:
                    # This is a circular dependency
                    cycle = list(scc)
                    severity = self._assess_cycle_severity(cycle)
                    description = f"Circular dependency involving {len(cycle)} modules"
                    suggestions = self._generate_cycle_suggestions(cycle)
                    
                    circular_deps.append(CircularDependency(
                        cycle=cycle,
                        severity=severity,
                        description=description,
                        suggestions=suggestions
                    ))
        
        except Exception:
            # If graph analysis fails, continue without circular dependency detection
            pass
        
        return circular_deps
    
    def _assess_cycle_severity(self, cycle: List[str]) -> str:
        """Assess the severity of a circular dependency.
        
        Args:
            cycle: List of files in the cycle
            
        Returns:
            Severity level: 'low', 'medium', or 'high'
        """
        if len(cycle) == 2:
            return 'high'  # Direct circular dependency
        elif len(cycle) <= 4:
            return 'medium'  # Short cycle
        else:
            return 'low'  # Long cycle, less problematic
    
    def _generate_cycle_suggestions(self, cycle: List[str]) -> List[str]:
        """Generate suggestions for resolving a circular dependency.
        
        Args:
            cycle: List of files in the cycle
            
        Returns:
            List of suggestion strings
        """
        suggestions = [
            "Consider extracting common functionality into a separate module",
            "Use dependency injection to break the circular dependency",
            "Move imports inside functions to delay the dependency",
            "Restructure the code to create a clearer hierarchy"
        ]
        
        if len(cycle) == 2:
            suggestions.insert(0, "Consider merging these two modules if they are tightly coupled")
        
        return suggestions

    def _find_unused_imports(self) -> Dict[str, List[ImportInfo]]:
        """Find potentially unused imports in each file.

        Returns:
            Dictionary mapping file paths to lists of unused imports
        """
        unused_imports = {}

        for file_path, imports in self.file_imports.items():
            try:
                with open(self.project_path / file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                unused = []
                for import_info in imports:
                    if self._is_import_unused(import_info, content):
                        unused.append(import_info)

                if unused:
                    unused_imports[file_path] = unused

            except (IOError, UnicodeDecodeError):
                continue

        return unused_imports

    def _is_import_unused(self, import_info: ImportInfo, content: str) -> bool:
        """Check if an import appears to be unused.

        Args:
            import_info: The import to check
            content: The file content

        Returns:
            True if the import appears unused
        """
        # This is a simplified check - could be enhanced with more sophisticated analysis
        if import_info.is_star_import:
            return False  # Can't easily detect star import usage

        # Determine what name to look for
        if import_info.alias:
            name_to_find = import_info.alias
        elif import_info.from_module:
            name_to_find = import_info.module
        else:
            name_to_find = import_info.module.split('.')[0]

        # Simple text search (could be improved with AST analysis)
        lines = content.split('\n')
        import_line = import_info.line_number - 1

        for i, line in enumerate(lines):
            if i == import_line:
                continue  # Skip the import line itself

            if name_to_find in line:
                return False  # Found usage

        return True  # No usage found

    def _generate_optimization_suggestions(self) -> Dict[str, List[str]]:
        """Generate import optimization suggestions for each file.

        Returns:
            Dictionary mapping file paths to lists of suggestions
        """
        suggestions = {}

        for file_path, imports in self.file_imports.items():
            file_suggestions = []

            # Check for star imports
            star_imports = [imp for imp in imports if imp.is_star_import]
            if star_imports:
                file_suggestions.append(
                    f"Avoid star imports ({len(star_imports)} found). "
                    "Import specific names instead for better clarity."
                )

            # Check for too many imports from same module
            from_modules = defaultdict(list)
            for imp in imports:
                if imp.from_module:
                    from_modules[imp.from_module].append(imp)

            for module, module_imports in from_modules.items():
                if len(module_imports) > 5:
                    file_suggestions.append(
                        f"Consider importing {module} directly instead of "
                        f"{len(module_imports)} individual items."
                    )

            # Check for long import lines
            long_imports = [imp for imp in imports if len(imp.module) > 50]
            if long_imports:
                file_suggestions.append(
                    "Consider using shorter aliases for long module names."
                )

            if file_suggestions:
                suggestions[file_path] = file_suggestions

        return suggestions

    def _calculate_coupling_metrics(self) -> Dict[str, Dict[str, Any]]:
        """Calculate coupling metrics based on imports.

        Returns:
            Dictionary with coupling metrics for each file
        """
        coupling_metrics = {}

        for file_path in self.file_imports:
            # Calculate various coupling metrics
            imports = self.file_imports[file_path]

            # Afferent coupling (Ca) - how many modules depend on this one
            afferent = len([f for f in self.file_imports
                           if self.dependency_graph.has_edge(f, file_path)])

            # Efferent coupling (Ce) - how many modules this one depends on
            efferent = len([f for f in self.file_imports
                           if self.dependency_graph.has_edge(file_path, f)])

            # Instability (I) = Ce / (Ca + Ce)
            total_coupling = afferent + efferent
            instability = efferent / total_coupling if total_coupling > 0 else 0

            coupling_metrics[file_path] = {
                'afferent_coupling': afferent,
                'efferent_coupling': efferent,
                'instability': instability,
                'total_imports': len(imports),
                'external_imports': len([imp for imp in imports
                                       if not self._is_internal_import(imp, file_path)]),
                'internal_imports': len([imp for imp in imports
                                       if self._is_internal_import(imp, file_path)])
            }

        return coupling_metrics

    def _is_internal_import(self, import_info: ImportInfo, current_file: str) -> bool:
        """Check if an import is internal to the project.

        Args:
            import_info: The import to check
            current_file: The file making the import

        Returns:
            True if the import is internal to the project
        """
        if import_info.from_module:
            target_module = import_info.from_module
        else:
            target_module = import_info.module

        resolved_file = self._resolve_module_to_file(target_module, current_file)
        return resolved_file is not None

    def _calculate_import_complexity(self) -> Dict[str, float]:
        """Calculate import complexity scores for each file.

        Returns:
            Dictionary mapping file paths to complexity scores
        """
        complexity_scores = {}

        for file_path, imports in self.file_imports.items():
            score = 0.0

            for import_info in imports:
                # Base complexity
                score += 1.0

                # Star imports add complexity
                if import_info.is_star_import:
                    score += 2.0

                # Relative imports add some complexity
                if import_info.is_relative:
                    score += 0.5

                # Long module names add complexity
                module_name = import_info.from_module or import_info.module
                if len(module_name) > 30:
                    score += 1.0

                # Multiple imports from same module reduce per-import complexity
                same_module_count = len([imp for imp in imports
                                       if (imp.from_module or imp.module) == module_name])
                if same_module_count > 1:
                    score -= 0.2

            complexity_scores[file_path] = score

        return complexity_scores
