"""
Import Analysis Visualization Module

This module provides visualization capabilities for import analysis results.
This is now a facade that imports from the specialized visualization modules.
"""

from pathlib import Path
from typing import Dict

from .import_analyzer import ImportAnalysisResult
from .visualization import (
    generate_dependency_graph,
    generate_circular_dependencies_chart,
    generate_coupling_heatmap,
    generate_complexity_chart,
    generate_interactive_dashboard
)


class ImportVisualizer:
    """Visualizer for import analysis results."""

    def __init__(self, analysis_result: ImportAnalysisResult, output_dir: str):
        """Initialize the visualizer.

        Args:
            analysis_result: Results from import analysis
            output_dir: Directory to save visualizations
        """
        self.analysis_result = analysis_result
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

    def generate_all_visualizations(self) -> Dict[str, str]:
        """Generate all available visualizations.

        Returns:
            Dictionary mapping visualization names to file paths
        """
        visualizations = {}

        # Generate dependency graph
        dep_graph_path = generate_dependency_graph(self.analysis_result, self.output_dir)
        if dep_graph_path:
            visualizations['dependency_graph'] = dep_graph_path

        # Generate circular dependency visualization
        circular_deps_path = generate_circular_dependencies_chart(self.analysis_result, self.output_dir)
        if circular_deps_path:
            visualizations['circular_dependencies'] = circular_deps_path

        # Generate coupling heatmap
        coupling_heatmap_path = generate_coupling_heatmap(self.analysis_result, self.output_dir)
        if coupling_heatmap_path:
            visualizations['coupling_heatmap'] = coupling_heatmap_path

        # Generate complexity chart
        complexity_chart_path = generate_complexity_chart(self.analysis_result, self.output_dir)
        if complexity_chart_path:
            visualizations['complexity_chart'] = complexity_chart_path

        # Generate interactive dashboard
        dashboard_path = generate_interactive_dashboard(self.analysis_result, self.output_dir)
        if dashboard_path:
            visualizations['interactive_dashboard'] = dashboard_path

        return visualizations
