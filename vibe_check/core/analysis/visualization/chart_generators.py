"""
Chart Generators for Import Analysis
===================================

This module provides chart generation functions for import analysis visualizations.
"""

import logging
import os
from pathlib import Path
from typing import Optional
import networkx as nx

logger = logging.getLogger(__name__)

try:
    import matplotlib.pyplot as plt
    import matplotlib.patches as mpatches
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False

from ..import_analyzer import ImportAnalysisResult


def generate_dependency_graph(analysis_result: ImportAnalysisResult, output_dir: Path) -> Optional[str]:
    """Generate a dependency graph visualization.
    
    Args:
        analysis_result: Results from import analysis
        output_dir: Directory to save visualizations
        
    Returns:
        Path to the generated visualization file
    """
    if not HAS_MATPLOTLIB:
        return None
    
    try:
        plt.figure(figsize=(16, 12))
        
        # Use spring layout for better visualization
        pos = nx.spring_layout(analysis_result.dependency_graph, k=3, iterations=50)
        
        # Draw nodes
        nx.draw_networkx_nodes(
            analysis_result.dependency_graph, 
            pos, 
            node_color='lightblue',
            node_size=1000,
            alpha=0.7
        )
        
        # Draw edges
        nx.draw_networkx_edges(
            analysis_result.dependency_graph,
            pos,
            edge_color='gray',
            arrows=True,
            arrowsize=20,
            alpha=0.6
        )
        
        # Draw labels (shortened for readability)
        labels = {node: os.path.basename(node) for node in analysis_result.dependency_graph.nodes()}
        nx.draw_networkx_labels(
            analysis_result.dependency_graph,
            pos,
            labels,
            font_size=8
        )
        
        plt.title("Import Dependency Graph", fontsize=16, fontweight='bold')
        plt.axis('off')
        plt.tight_layout()
        
        output_path = output_dir / "dependency_graph.png"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return str(output_path)
        
    except Exception as e:
        logger.error(f"Error generating dependency graph: {e}")
        return None


def generate_circular_dependencies_chart(analysis_result: ImportAnalysisResult, output_dir: Path) -> Optional[str]:
    """Generate a visualization of circular dependencies.
    
    Args:
        analysis_result: Results from import analysis
        output_dir: Directory to save visualizations
        
    Returns:
        Path to the generated visualization file
    """
    if not HAS_MATPLOTLIB or not analysis_result.circular_dependencies:
        return None
    
    try:
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
        
        # Chart 1: Circular dependencies by severity
        severities = [cd.severity for cd in analysis_result.circular_dependencies]
        severity_counts = {sev: severities.count(sev) for sev in set(severities)}
        
        colors = {'high': 'red', 'medium': 'orange', 'low': 'yellow'}
        ax1.pie(
            severity_counts.values(),
            labels=severity_counts.keys(),
            colors=[colors.get(sev, 'gray') for sev in severity_counts.keys()],
            autopct='%1.1f%%',
            startangle=90
        )
        ax1.set_title("Circular Dependencies by Severity")
        
        # Chart 2: Cycle sizes
        cycle_sizes = [len(cd.cycle) for cd in analysis_result.circular_dependencies]
        ax2.hist(cycle_sizes, bins=range(2, max(cycle_sizes) + 2), alpha=0.7, color='skyblue')
        ax2.set_xlabel("Cycle Size (Number of Modules)")
        ax2.set_ylabel("Number of Cycles")
        ax2.set_title("Distribution of Circular Dependency Sizes")
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        output_path = output_dir / "circular_dependencies.png"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return str(output_path)
        
    except Exception as e:
        logger.error(f"Error generating circular dependencies chart: {e}")
        return None


def generate_coupling_heatmap(analysis_result: ImportAnalysisResult, output_dir: Path) -> Optional[str]:
    """Generate a coupling metrics heatmap.
    
    Args:
        analysis_result: Results from import analysis
        output_dir: Directory to save visualizations
        
    Returns:
        Path to the generated visualization file
    """
    if not HAS_MATPLOTLIB:
        return None
    
    try:
        coupling_data = analysis_result.coupling_metrics
        if not coupling_data:
            return None
        
        # Prepare data for heatmap
        files = list(coupling_data.keys())
        metrics = ['afferent_coupling', 'efferent_coupling', 'instability']
        
        data_matrix = []
        for metric in metrics:
            row = [coupling_data[file][metric] for file in files]
            data_matrix.append(row)
        
        fig, ax = plt.subplots(figsize=(max(12, len(files) * 0.5), 6))
        
        im = ax.imshow(data_matrix, cmap='YlOrRd', aspect='auto')
        
        # Set ticks and labels
        ax.set_xticks(range(len(files)))
        ax.set_xticklabels([os.path.basename(f) for f in files], rotation=45, ha='right')
        ax.set_yticks(range(len(metrics)))
        ax.set_yticklabels(metrics)
        
        # Add colorbar
        plt.colorbar(im, ax=ax)
        
        # Add text annotations
        for i in range(len(metrics)):
            for j in range(len(files)):
                text = ax.text(j, i, f'{data_matrix[i][j]:.2f}',
                             ha="center", va="center", color="black", fontsize=8)
        
        ax.set_title("Module Coupling Metrics Heatmap")
        plt.tight_layout()
        
        output_path = output_dir / "coupling_heatmap.png"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return str(output_path)
        
    except Exception as e:
        logger.error(f"Error generating coupling heatmap: {e}")
        return None


def generate_complexity_chart(analysis_result: ImportAnalysisResult, output_dir: Path) -> Optional[str]:
    """Generate an import complexity chart.
    
    Args:
        analysis_result: Results from import analysis
        output_dir: Directory to save visualizations
        
    Returns:
        Path to the generated visualization file
    """
    if not HAS_MATPLOTLIB:
        return None
    
    try:
        complexity_data = analysis_result.import_complexity_scores
        if not complexity_data:
            return None
        
        files = list(complexity_data.keys())
        scores = list(complexity_data.values())
        
        plt.figure(figsize=(max(12, len(files) * 0.3), 8))
        
        # Create bar chart
        bars = plt.bar(range(len(files)), scores, color='steelblue', alpha=0.7)
        
        # Color bars based on complexity level
        for i, (bar, score) in enumerate(zip(bars, scores)):
            if score > 20:
                bar.set_color('red')
            elif score > 10:
                bar.set_color('orange')
            else:
                bar.set_color('green')
        
        plt.xlabel("Files")
        plt.ylabel("Import Complexity Score")
        plt.title("Import Complexity by File")
        plt.xticks(range(len(files)), [os.path.basename(f) for f in files], 
                  rotation=45, ha='right')
        plt.grid(True, alpha=0.3)
        
        # Add legend
        red_patch = mpatches.Patch(color='red', label='High Complexity (>20)')
        orange_patch = mpatches.Patch(color='orange', label='Medium Complexity (10-20)')
        green_patch = mpatches.Patch(color='green', label='Low Complexity (<10)')
        plt.legend(handles=[red_patch, orange_patch, green_patch])
        
        plt.tight_layout()
        
        output_path = output_dir / "import_complexity.png"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return str(output_path)
        
    except Exception as e:
        logger.error(f"Error generating complexity chart: {e}")
        return None
