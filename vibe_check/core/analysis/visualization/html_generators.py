"""
HTML Generators for Import Analysis
===================================

This module provides HTML generation functions for import analysis reports.
"""

import os
from ..import_analyzer import ImportAnalysisResult


def create_static_html_dashboard(analysis_result: ImportAnalysisResult) -> str:
    """Create a static HTML dashboard with analysis results.

    Args:
        analysis_result: Results from import analysis

    Returns:
        HTML content as string
    """
    html = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Import Analysis Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        h1, h2 { color: #333; }
        .metric-card { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
        .warning { border-left-color: #ffc107; }
        .error { border-left-color: #dc3545; }
        .success { border-left-color: #28a745; }
        .file-list { max-height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px; }
        .cycle { background: #fff3cd; padding: 10px; margin: 5px 0; border-radius: 5px; border-left: 4px solid #ffc107; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        .complexity-high { color: #dc3545; font-weight: bold; }
        .complexity-medium { color: #ffc107; font-weight: bold; }
        .complexity-low { color: #28a745; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Import Analysis Report</h1>

        <div class="metric-card">
            <h2>Summary</h2>
            <p><strong>Total Files Analyzed:</strong> {total_files}</p>
            <p><strong>Total Import Statements:</strong> {total_imports}</p>
            <p><strong>Circular Dependencies Found:</strong> {circular_deps_count}</p>
            <p><strong>Files with Unused Imports:</strong> {unused_imports_count}</p>
        </div>

        {circular_deps_section}

        {unused_imports_section}

        {optimization_section}

        {complexity_section}

        {coupling_section}
    </div>
</body>
</html>
    """

    # Calculate summary statistics
    total_files = len(analysis_result.file_imports)
    total_imports = sum(len(imports) for imports in analysis_result.file_imports.values())
    circular_deps_count = len(analysis_result.circular_dependencies)
    unused_imports_count = len(analysis_result.unused_imports)

    # Generate sections
    circular_deps_section = generate_circular_deps_html(analysis_result)
    unused_imports_section = generate_unused_imports_html(analysis_result)
    optimization_section = generate_optimization_html(analysis_result)
    complexity_section = generate_complexity_html(analysis_result)
    coupling_section = generate_coupling_html(analysis_result)

    return html.format(
        total_files=total_files,
        total_imports=total_imports,
        circular_deps_count=circular_deps_count,
        unused_imports_count=unused_imports_count,
        circular_deps_section=circular_deps_section,
        unused_imports_section=unused_imports_section,
        optimization_section=optimization_section,
        complexity_section=complexity_section,
        coupling_section=coupling_section
    )


def generate_circular_deps_html(analysis_result: ImportAnalysisResult) -> str:
    """Generate HTML for circular dependencies section.
    
    Args:
        analysis_result: Results from import analysis
        
    Returns:
        HTML content for circular dependencies section
    """
    if not analysis_result.circular_dependencies:
        return '<div class="metric-card success"><h2>Circular Dependencies</h2><p>No circular dependencies found! 🎉</p></div>'

    html = '<div class="metric-card error"><h2>Circular Dependencies</h2>'

    for i, cd in enumerate(analysis_result.circular_dependencies):
        severity_class = f"cycle-{cd.severity}"
        html += f'''
        <div class="cycle">
            <h3>Cycle {i+1} (Severity: {cd.severity.upper()})</h3>
            <p><strong>Modules:</strong> {" → ".join([os.path.basename(f) for f in cd.cycle])}</p>
            <p><strong>Description:</strong> {cd.description}</p>
            <p><strong>Suggestions:</strong></p>
            <ul>
                {"".join(f"<li>{suggestion}</li>" for suggestion in cd.suggestions)}
            </ul>
        </div>
        '''

    html += '</div>'
    return html


def generate_unused_imports_html(analysis_result: ImportAnalysisResult) -> str:
    """Generate HTML for unused imports section.
    
    Args:
        analysis_result: Results from import analysis
        
    Returns:
        HTML content for unused imports section
    """
    if not analysis_result.unused_imports:
        return '<div class="metric-card success"><h2>Unused Imports</h2><p>No unused imports detected! 🎉</p></div>'

    html = '<div class="metric-card warning"><h2>Unused Imports</h2>'

    for file_path, unused in analysis_result.unused_imports.items():
        html += f'<h3>{os.path.basename(file_path)}</h3><ul>'
        for import_info in unused:
            if import_info.from_module:
                import_text = f"from {import_info.from_module} import {import_info.module}"
            else:
                import_text = f"import {import_info.module}"
            html += f'<li>Line {import_info.line_number}: {import_text}</li>'
        html += '</ul>'

    html += '</div>'
    return html


def generate_optimization_html(analysis_result: ImportAnalysisResult) -> str:
    """Generate HTML for optimization suggestions section.
    
    Args:
        analysis_result: Results from import analysis
        
    Returns:
        HTML content for optimization suggestions section
    """
    if not analysis_result.optimization_suggestions:
        return '<div class="metric-card success"><h2>Optimization Suggestions</h2><p>No optimization suggestions at this time.</p></div>'

    html = '<div class="metric-card"><h2>Optimization Suggestions</h2>'

    for file_path, suggestions in analysis_result.optimization_suggestions.items():
        html += f'<h3>{os.path.basename(file_path)}</h3><ul>'
        for suggestion in suggestions:
            html += f'<li>{suggestion}</li>'
        html += '</ul>'

    html += '</div>'
    return html


def generate_complexity_html(analysis_result: ImportAnalysisResult) -> str:
    """Generate HTML for complexity metrics section.
    
    Args:
        analysis_result: Results from import analysis
        
    Returns:
        HTML content for complexity metrics section
    """
    if not analysis_result.import_complexity_scores:
        return '<div class="metric-card"><h2>Import Complexity</h2><p>No complexity data available.</p></div>'

    html = '<div class="metric-card"><h2>Import Complexity</h2>'
    html += '<table><tr><th>File</th><th>Complexity Score</th><th>Level</th></tr>'

    for file_path, score in sorted(analysis_result.import_complexity_scores.items(),
                                 key=lambda x: x[1], reverse=True):
        if score > 20:
            level = "High"
            css_class = "complexity-high"
        elif score > 10:
            level = "Medium"
            css_class = "complexity-medium"
        else:
            level = "Low"
            css_class = "complexity-low"

        html += f'<tr><td>{os.path.basename(file_path)}</td><td class="{css_class}">{score:.1f}</td><td class="{css_class}">{level}</td></tr>'

    html += '</table></div>'
    return html


def generate_coupling_html(analysis_result: ImportAnalysisResult) -> str:
    """Generate HTML for coupling metrics section.
    
    Args:
        analysis_result: Results from import analysis
        
    Returns:
        HTML content for coupling metrics section
    """
    if not analysis_result.coupling_metrics:
        return '<div class="metric-card"><h2>Coupling Metrics</h2><p>No coupling data available.</p></div>'

    html = '<div class="metric-card"><h2>Coupling Metrics</h2>'
    html += '<table><tr><th>File</th><th>Afferent</th><th>Efferent</th><th>Instability</th><th>Total Imports</th></tr>'

    for file_path, metrics in analysis_result.coupling_metrics.items():
        html += f'''<tr>
            <td>{os.path.basename(file_path)}</td>
            <td>{metrics["afferent_coupling"]}</td>
            <td>{metrics["efferent_coupling"]}</td>
            <td>{metrics["instability"]:.2f}</td>
            <td>{metrics["total_imports"]}</td>
        </tr>'''

    html += '</table></div>'
    return html
