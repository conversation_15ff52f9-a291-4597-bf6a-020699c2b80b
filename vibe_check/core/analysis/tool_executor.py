"""
Tool Executor Module
================

This module provides the ToolExecutor class, which is responsible for
executing analysis tools on files. It abstracts the details of tool execution
for use by the analysis engine.
"""

import logging
from pathlib import Path
from typing import Dict, Any, Optional, Union

from ...tools.runners.tool_registry import get_runner_for_tool, list_available_tools

logger = logging.getLogger("vibe_check_analysis")


class ToolExecutor:
    """
    Executes analysis tools on files.
    
    This class abstracts the details of tool execution for use by
    the analysis engine.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the tool executor.
        
        Args:
            config: Optional configuration for tools
        """
        self.config = config or {}
        self.available_tools = list_available_tools()
        logger.debug(f"Available tools: {', '.join(self.available_tools)}")
        
    async def run_tools(self,
                       file_path: Union[str, Path],
                       content: str) -> Dict[str, Any]:
        """
        Run all enabled tools on a file and perform meta-analysis.

        Args:
            file_path: Path to the file to analyze
            content: File content

        Returns:
            Dictionary mapping tool names to results, plus meta-analysis
        """
        results = {}

        for tool_name in self.available_tools:
            # Skip tools that are not enabled in the config
            tool_config = self.config.get("tools", {}).get(tool_name, {})
            if not tool_config.get("enabled", False):
                logger.debug(f"Skipping disabled tool: {tool_name}")
                continue

            # Run the tool
            logger.info(f"Running {tool_name} on {file_path}")
            result = await self.run_tool(tool_name, file_path, content, tool_config)

            if result:
                results[tool_name] = result

        # Perform meta-analysis if multiple tools were used
        if len(results) > 0:
            from .meta_analyzer import MetaAnalyzer
            meta_analyzer = MetaAnalyzer()
            meta_results = meta_analyzer.analyze_combined_results(results, Path(file_path))
            results["_meta_analysis"] = meta_results

        return results
    
    async def run_tool(self, 
                      tool_name: str, 
                      file_path: Union[str, Path], 
                      content: str,
                      tool_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Run a specific tool on a file.
        
        Args:
            tool_name: Name of the tool to run
            file_path: Path to the file to analyze
            content: File content
            tool_config: Optional configuration for the tool
            
        Returns:
            Dictionary containing analysis results
        """
        try:
            # Get the tool runner
            runner = get_runner_for_tool(tool_name, tool_config)
            if not runner:
                logger.warning(f"No runner found for tool: {tool_name}")
                return {"error": f"No runner found for tool: {tool_name}", "issues": []}

            # Check if tool is available on the system
            if hasattr(runner, 'is_available') and not runner.is_available():
                logger.info(f"Tool '{tool_name}' not available, using fallback analysis")
                if hasattr(runner, 'get_fallback_analysis'):
                    result = runner.get_fallback_analysis(file_path, content)
                    result["tool"] = tool_name
                    return result
                else:
                    return {
                        "tool": tool_name,
                        "tool_status": "unavailable",
                        "fallback_used": False,
                        "message": f"{tool_name} is not available and no fallback provided",
                        "issues": [],
                        "summary": {"total": 0, "by_type": {}}
                    }

            # Run the tool
            if hasattr(runner, "run_with_args") and tool_config and "args" in tool_config:
                # Run the tool with arguments
                result = await runner.run_with_args(
                    file_path=file_path,
                    content=content,
                    args=tool_config.get("args", [])
                )
            else:
                # Run the tool without arguments
                result = await runner.run(
                    file_path=file_path,
                    content=content
                )

            # Mark tool as available
            result["tool"] = tool_name
            result["tool_status"] = "available"
            return result
        except Exception as e:
            logger.error(f"Error running tool {tool_name} on {file_path}: {e}")
            import traceback
            logger.debug(traceback.format_exc())
            return {"error": str(e), "issues": []}
