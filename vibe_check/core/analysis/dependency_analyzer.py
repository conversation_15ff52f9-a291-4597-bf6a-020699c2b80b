"""
Dependency Analysis Engine

This module analyzes import dependencies, detects circular imports,
and maps architectural relationships in Python projects.
"""

import ast
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
import networkx as nx

logger = logging.getLogger(__name__)


@dataclass
class ImportInfo:
    """Information about an import statement."""
    
    module_name: str
    import_type: str  # 'import', 'from_import'
    imported_names: List[str]
    line_number: int
    is_relative: bool = False
    level: int = 0  # For relative imports


@dataclass
class DependencyNode:
    """Node in the dependency graph."""
    
    module_path: str
    imports: List[ImportInfo] = field(default_factory=list)
    imported_by: Set[str] = field(default_factory=set)
    file_path: Optional[Path] = None
    is_external: bool = False


@dataclass
class CircularDependency:
    """Represents a circular dependency."""
    
    cycle: List[str]
    severity: str  # 'error', 'warning', 'info'
    description: str
    suggestions: List[str] = field(default_factory=list)


@dataclass
class DependencyAnalysisResult:
    """Results from dependency analysis."""
    
    dependency_graph: Dict[str, DependencyNode] = field(default_factory=dict)
    circular_dependencies: List[CircularDependency] = field(default_factory=list)
    external_dependencies: Set[str] = field(default_factory=set)
    internal_dependencies: Set[str] = field(default_factory=set)
    dependency_metrics: Dict[str, Any] = field(default_factory=dict)
    architectural_violations: List[str] = field(default_factory=list)


class ImportVisitor(ast.NodeVisitor):
    """AST visitor to extract import information."""
    
    def __init__(self, file_path: Path):
        self.file_path = file_path
        self.imports: List[ImportInfo] = []
    
    def visit_Import(self, node: ast.Import) -> None:
        """Visit import statements."""
        for alias in node.names:
            self.imports.append(ImportInfo(
                module_name=alias.name,
                import_type='import',
                imported_names=[alias.asname or alias.name],
                line_number=node.lineno,
                is_relative=False
            ))
        self.generic_visit(node)
    
    def visit_ImportFrom(self, node: ast.ImportFrom) -> None:
        """Visit from-import statements."""
        if node.module:
            imported_names = [alias.asname or alias.name for alias in node.names]
            self.imports.append(ImportInfo(
                module_name=node.module,
                import_type='from_import',
                imported_names=imported_names,
                line_number=node.lineno,
                is_relative=node.level > 0,
                level=node.level
            ))
        self.generic_visit(node)


class DependencyAnalyzer:
    """
    Analyzes import dependencies and detects circular imports.
    """
    
    def __init__(self, project_path: Path):
        self.project_path = project_path
        self.dependency_graph: Dict[str, DependencyNode] = {}
        self.nx_graph = nx.DiGraph()
        
    def analyze_dependencies(self, project_files: Dict[str, str]) -> DependencyAnalysisResult:
        """
        Analyze dependencies in the project.
        
        Args:
            project_files: Dictionary mapping file paths to source code
            
        Returns:
            DependencyAnalysisResult with analysis results
        """
        # Extract imports from all files
        self._extract_imports(project_files)
        
        # Build dependency graph
        self._build_dependency_graph()
        
        # Detect circular dependencies
        circular_deps = self._detect_circular_dependencies()
        
        # Calculate metrics
        metrics = self._calculate_dependency_metrics()
        
        # Detect architectural violations
        violations = self._detect_architectural_violations()
        
        # Classify dependencies
        external_deps, internal_deps = self._classify_dependencies()
        
        return DependencyAnalysisResult(
            dependency_graph=self.dependency_graph,
            circular_dependencies=circular_deps,
            external_dependencies=external_deps,
            internal_dependencies=internal_deps,
            dependency_metrics=metrics,
            architectural_violations=violations
        )
    
    def _extract_imports(self, project_files: Dict[str, str]) -> None:
        """Extract import information from all files."""
        for file_path, source_code in project_files.items():
            if not file_path.endswith('.py'):
                continue
                
            try:
                tree = ast.parse(source_code, filename=file_path)
                visitor = ImportVisitor(Path(file_path))
                visitor.visit(tree)
                
                # Create or update dependency node
                module_name = self._file_path_to_module(file_path)
                if module_name not in self.dependency_graph:
                    self.dependency_graph[module_name] = DependencyNode(
                        module_path=module_name,
                        file_path=Path(file_path)
                    )
                
                self.dependency_graph[module_name].imports = visitor.imports
                
            except SyntaxError as e:
                logger.warning(f"Syntax error in {file_path}: {e}")
            except Exception as e:
                logger.error(f"Error analyzing imports in {file_path}: {e}")
    
    def _file_path_to_module(self, file_path: str) -> str:
        """Convert file path to module name."""
        # Remove .py extension and convert path separators to dots
        module_path = file_path.replace('.py', '').replace('/', '.').replace('\\', '.')
        
        # Remove leading dots
        while module_path.startswith('.'):
            module_path = module_path[1:]
        
        return module_path
    
    def _build_dependency_graph(self) -> None:
        """Build the dependency graph."""
        # Create a copy of the items to avoid modification during iteration
        modules_to_process = list(self.dependency_graph.items())

        for module_name, node in modules_to_process:
            for import_info in node.imports:
                target_module = self._resolve_import(import_info, module_name)

                if target_module:
                    # Add edge to NetworkX graph
                    self.nx_graph.add_edge(module_name, target_module)

                    # Update imported_by relationships
                    if target_module not in self.dependency_graph:
                        self.dependency_graph[target_module] = DependencyNode(
                            module_path=target_module,
                            is_external=self._is_external_module(target_module)
                        )

                    self.dependency_graph[target_module].imported_by.add(module_name)
    
    def _resolve_import(self, import_info: ImportInfo, current_module: str) -> Optional[str]:
        """Resolve import to actual module name."""
        if import_info.is_relative:
            # Handle relative imports
            current_parts = current_module.split('.')
            if import_info.level >= len(current_parts):
                return None  # Invalid relative import
            
            base_parts = current_parts[:-import_info.level] if import_info.level > 0 else current_parts
            if import_info.module_name:
                return '.'.join(base_parts + [import_info.module_name])
            else:
                return '.'.join(base_parts)
        else:
            return import_info.module_name
    
    def _is_external_module(self, module_name: str) -> bool:
        """Check if module is external (not part of the project)."""
        # Standard library modules
        stdlib_modules = {
            'os', 'sys', 'json', 'time', 'datetime', 'pathlib', 'collections',
            'typing', 'asyncio', 'logging', 'unittest', 'pytest', 'abc'
        }
        
        if module_name.split('.')[0] in stdlib_modules:
            return True
        
        # Check if it's a known third-party package
        third_party_modules = {
            'django', 'flask', 'fastapi', 'requests', 'pandas', 'numpy',
            'sqlalchemy', 'celery', 'redis', 'boto3', 'click'
        }
        
        if module_name.split('.')[0] in third_party_modules:
            return True
        
        # If it's not in our project files, consider it external
        return module_name not in self.dependency_graph
    
    def _detect_circular_dependencies(self) -> List[CircularDependency]:
        """Detect circular dependencies in the graph."""
        circular_deps = []
        
        try:
            # Find all strongly connected components with more than one node
            cycles = list(nx.simple_cycles(self.nx_graph))
            
            for cycle in cycles:
                if len(cycle) > 1:
                    severity = self._determine_cycle_severity(cycle)
                    description = f"Circular dependency: {' -> '.join(cycle)} -> {cycle[0]}"
                    suggestions = self._generate_cycle_suggestions(cycle)
                    
                    circular_deps.append(CircularDependency(
                        cycle=cycle,
                        severity=severity,
                        description=description,
                        suggestions=suggestions
                    ))
        
        except Exception as e:
            logger.warning(f"Error detecting circular dependencies: {e}")
        
        return circular_deps
    
    def _determine_cycle_severity(self, cycle: List[str]) -> str:
        """Determine the severity of a circular dependency."""
        if len(cycle) == 2:
            return "error"  # Direct circular dependency
        elif len(cycle) <= 4:
            return "warning"  # Short cycle
        else:
            return "info"  # Long cycle, might be acceptable
    
    def _generate_cycle_suggestions(self, cycle: List[str]) -> List[str]:
        """Generate suggestions for breaking circular dependencies."""
        suggestions = []
        
        if len(cycle) == 2:
            suggestions.append("Consider extracting common functionality to a separate module")
            suggestions.append("Use dependency injection to break the circular dependency")
        else:
            suggestions.append("Refactor to create a clearer hierarchy")
            suggestions.append("Consider using interfaces or abstract base classes")
            suggestions.append("Move shared code to a common module")
        
        return suggestions
    
    def _calculate_dependency_metrics(self) -> Dict[str, Any]:
        """Calculate dependency metrics."""
        metrics = {}
        
        # Basic counts
        metrics['total_modules'] = len(self.dependency_graph)
        metrics['total_dependencies'] = self.nx_graph.number_of_edges()
        
        # Complexity metrics
        if self.nx_graph.number_of_nodes() > 0:
            metrics['average_dependencies_per_module'] = (
                self.nx_graph.number_of_edges() / self.nx_graph.number_of_nodes()
            )
            
            # Calculate in-degree and out-degree statistics
            in_degrees = [d for n, d in self.nx_graph.in_degree()]
            out_degrees = [d for n, d in self.nx_graph.out_degree()]
            
            metrics['max_incoming_dependencies'] = max(in_degrees) if in_degrees else 0
            metrics['max_outgoing_dependencies'] = max(out_degrees) if out_degrees else 0
            metrics['avg_incoming_dependencies'] = sum(in_degrees) / len(in_degrees) if in_degrees else 0
            metrics['avg_outgoing_dependencies'] = sum(out_degrees) / len(out_degrees) if out_degrees else 0
        
        # Circular dependency metrics
        metrics['circular_dependency_count'] = len(self._detect_circular_dependencies())
        
        # External vs internal dependencies
        external_count = sum(1 for node in self.dependency_graph.values() if node.is_external)
        metrics['external_dependency_count'] = external_count
        metrics['internal_dependency_count'] = len(self.dependency_graph) - external_count
        
        return metrics
    
    def _detect_architectural_violations(self) -> List[str]:
        """Detect architectural violations."""
        violations = []
        
        # Check for common architectural anti-patterns
        for module_name, node in self.dependency_graph.items():
            # Check for modules with too many dependencies
            if len(node.imports) > 20:
                violations.append(f"Module '{module_name}' has too many dependencies ({len(node.imports)})")
            
            # Check for modules that are imported by too many others
            if len(node.imported_by) > 15:
                violations.append(f"Module '{module_name}' is imported by too many modules ({len(node.imported_by)})")
        
        return violations
    
    def _classify_dependencies(self) -> Tuple[Set[str], Set[str]]:
        """Classify dependencies as external or internal."""
        external_deps = set()
        internal_deps = set()
        
        for module_name, node in self.dependency_graph.items():
            if node.is_external:
                external_deps.add(module_name)
            else:
                internal_deps.add(module_name)
        
        return external_deps, internal_deps
    
    def get_dependency_tree(self, module_name: str, max_depth: int = 3) -> Dict[str, Any]:
        """Get dependency tree for a specific module."""
        if module_name not in self.dependency_graph:
            return {}
        
        def build_tree(current_module: str, depth: int) -> Dict[str, Any]:
            if depth >= max_depth:
                return {"module": current_module, "dependencies": "..."}
            
            node = self.dependency_graph.get(current_module)
            if not node:
                return {"module": current_module, "dependencies": []}
            
            dependencies = []
            for import_info in node.imports:
                target_module = self._resolve_import(import_info, current_module)
                if target_module and target_module != current_module:  # Avoid self-references
                    dependencies.append(build_tree(target_module, depth + 1))
            
            return {
                "module": current_module,
                "dependencies": dependencies,
                "import_count": len(node.imports),
                "imported_by_count": len(node.imported_by)
            }
        
        return build_tree(module_name, 0)
    
    def get_most_connected_modules(self, limit: int = 10) -> List[Tuple[str, int]]:
        """Get modules with the most connections (in + out degree)."""
        if not self.nx_graph.nodes():
            return []
        
        connections = []
        for node in self.nx_graph.nodes():
            total_connections = self.nx_graph.in_degree(node) + self.nx_graph.out_degree(node)
            connections.append((node, total_connections))
        
        return sorted(connections, key=lambda x: x[1], reverse=True)[:limit]


class ArchitecturalAnalyzer:
    """
    Analyzes architectural patterns and layer violations.
    """

    def __init__(self, dependency_analyzer: DependencyAnalyzer):
        self.dependency_analyzer = dependency_analyzer
        self.layers = self._detect_layers()
        self.architectural_patterns = []

    def analyze_architecture(self) -> Dict[str, Any]:
        """Analyze architectural patterns and violations."""
        # Detect architectural patterns
        patterns = self._detect_architectural_patterns()

        # Detect layer violations
        violations = self._detect_layer_violations()

        # Calculate architectural metrics
        metrics = self._calculate_architectural_metrics()

        return {
            'layers': self.layers,
            'architectural_patterns': patterns,
            'layer_violations': violations,
            'architectural_metrics': metrics,
            'recommendations': self._generate_architectural_recommendations(patterns, violations)
        }

    def _detect_layers(self) -> Dict[str, List[str]]:
        """Detect architectural layers based on module names and dependencies."""
        layers = {
            'presentation': [],
            'business': [],
            'data': [],
            'infrastructure': [],
            'tests': []
        }

        for module_name in self.dependency_analyzer.dependency_graph.keys():
            module_lower = module_name.lower()

            # Classify modules into layers
            if any(keyword in module_lower for keyword in ['view', 'controller', 'handler', 'api', 'web', 'ui']):
                layers['presentation'].append(module_name)
            elif any(keyword in module_lower for keyword in ['service', 'business', 'logic', 'domain', 'core']):
                layers['business'].append(module_name)
            elif any(keyword in module_lower for keyword in ['model', 'repository', 'dao', 'database', 'db']):
                layers['data'].append(module_name)
            elif any(keyword in module_lower for keyword in ['config', 'util', 'helper', 'infrastructure', 'common']):
                layers['infrastructure'].append(module_name)
            elif any(keyword in module_lower for keyword in ['test', 'spec']):
                layers['tests'].append(module_name)
            else:
                # Default classification based on dependencies
                node = self.dependency_analyzer.dependency_graph[module_name]
                if len(node.imports) > len(node.imported_by):
                    layers['presentation'].append(module_name)
                else:
                    layers['business'].append(module_name)

        return layers

    def _detect_architectural_patterns(self) -> List[Dict[str, Any]]:
        """Detect common architectural patterns."""
        patterns = []

        # MVC Pattern
        mvc_score = self._detect_mvc_pattern()
        if mvc_score > 0.3:
            patterns.append({
                'name': 'Model-View-Controller (MVC)',
                'confidence': mvc_score,
                'description': 'Separation of concerns with models, views, and controllers'
            })

        # Repository Pattern
        repo_score = self._detect_repository_pattern()
        if repo_score > 0.3:
            patterns.append({
                'name': 'Repository Pattern',
                'confidence': repo_score,
                'description': 'Data access abstraction layer'
            })

        # Service Layer Pattern
        service_score = self._detect_service_layer_pattern()
        if service_score > 0.3:
            patterns.append({
                'name': 'Service Layer Pattern',
                'confidence': service_score,
                'description': 'Business logic encapsulation in service classes'
            })

        # Layered Architecture
        layered_score = self._detect_layered_architecture()
        if layered_score > 0.3:
            patterns.append({
                'name': 'Layered Architecture',
                'confidence': layered_score,
                'description': 'Clear separation between presentation, business, and data layers'
            })

        return patterns

    def _detect_mvc_pattern(self) -> float:
        """Detect MVC pattern implementation."""
        score = 0.0
        total_modules = len(self.dependency_analyzer.dependency_graph)

        if total_modules == 0:
            return 0.0

        # Check for model-like modules
        model_modules = [m for m in self.dependency_analyzer.dependency_graph.keys()
                        if any(keyword in m.lower() for keyword in ['model', 'entity', 'domain'])]

        # Check for view-like modules
        view_modules = [m for m in self.dependency_analyzer.dependency_graph.keys()
                       if any(keyword in m.lower() for keyword in ['view', 'template', 'ui', 'frontend'])]

        # Check for controller-like modules
        controller_modules = [m for m in self.dependency_analyzer.dependency_graph.keys()
                             if any(keyword in m.lower() for keyword in ['controller', 'handler', 'api', 'endpoint'])]

        # Calculate score based on presence of MVC components
        if model_modules:
            score += 0.4
        if view_modules:
            score += 0.3
        if controller_modules:
            score += 0.3

        return min(score, 1.0)

    def _detect_repository_pattern(self) -> float:
        """Detect Repository pattern implementation."""
        score = 0.0

        # Look for repository-like modules
        repo_modules = [m for m in self.dependency_analyzer.dependency_graph.keys()
                       if any(keyword in m.lower() for keyword in ['repository', 'repo', 'dao', 'data_access'])]

        if repo_modules:
            score += 0.5

            # Check if repositories depend on models
            for repo_module in repo_modules:
                node = self.dependency_analyzer.dependency_graph[repo_module]
                model_imports = [imp for imp in node.imports
                               if any(keyword in imp.module_name.lower() for keyword in ['model', 'entity'])]
                if model_imports:
                    score += 0.3
                    break

        return min(score, 1.0)

    def _detect_service_layer_pattern(self) -> float:
        """Detect Service Layer pattern implementation."""
        score = 0.0

        # Look for service-like modules
        service_modules = [m for m in self.dependency_analyzer.dependency_graph.keys()
                          if any(keyword in m.lower() for keyword in ['service', 'business', 'logic', 'use_case'])]

        if service_modules:
            score += 0.6

            # Check if services are used by controllers/handlers
            for service_module in service_modules:
                node = self.dependency_analyzer.dependency_graph[service_module]
                if any(any(keyword in importer.lower() for keyword in ['controller', 'handler', 'api'])
                      for importer in node.imported_by):
                    score += 0.4
                    break

        return min(score, 1.0)

    def _detect_layered_architecture(self) -> float:
        """Detect layered architecture implementation."""
        score = 0.0

        # Check if we have clear layer separation
        layer_counts = {layer: len(modules) for layer, modules in self.layers.items() if modules}

        if len(layer_counts) >= 3:  # At least 3 distinct layers
            score += 0.5

            # Check for proper dependency direction (top-down)
            violations = len(self._detect_layer_violations())
            total_dependencies = self.dependency_analyzer.nx_graph.number_of_edges()

            if total_dependencies > 0:
                violation_ratio = violations / total_dependencies
                score += max(0, 0.5 * (1 - violation_ratio))

        return min(score, 1.0)

    def _detect_layer_violations(self) -> List[str]:
        """Detect violations of layered architecture principles."""
        violations = []

        # Define layer hierarchy (higher layers can depend on lower layers)
        layer_hierarchy = {
            'presentation': 4,
            'business': 3,
            'data': 2,
            'infrastructure': 1,
            'tests': 5  # Tests can depend on anything
        }

        # Check each dependency for layer violations
        for source, target in self.dependency_analyzer.nx_graph.edges():
            source_layer = self._get_module_layer(source)
            target_layer = self._get_module_layer(target)

            if source_layer and target_layer and source_layer != 'tests':
                source_level = layer_hierarchy.get(source_layer, 0)
                target_level = layer_hierarchy.get(target_layer, 0)

                # Violation if lower layer depends on higher layer
                if source_level < target_level:
                    violations.append(
                        f"Layer violation: {source_layer} layer module '{source}' "
                        f"depends on {target_layer} layer module '{target}'"
                    )

        return violations

    def _get_module_layer(self, module_name: str) -> Optional[str]:
        """Get the layer of a module."""
        for layer, modules in self.layers.items():
            if module_name in modules:
                return layer
        return None

    def _calculate_architectural_metrics(self) -> Dict[str, Any]:
        """Calculate architectural quality metrics."""
        metrics = {}

        # Layer distribution
        layer_distribution = {layer: len(modules) for layer, modules in self.layers.items()}
        metrics['layer_distribution'] = layer_distribution

        # Coupling metrics
        total_modules = len(self.dependency_analyzer.dependency_graph)
        if total_modules > 0:
            # Afferent coupling (incoming dependencies)
            afferent_couplings = []
            # Efferent coupling (outgoing dependencies)
            efferent_couplings = []

            for module_name, node in self.dependency_analyzer.dependency_graph.items():
                if not node.is_external:
                    afferent_couplings.append(len(node.imported_by))
                    efferent_couplings.append(len(node.imports))

            if afferent_couplings:
                metrics['average_afferent_coupling'] = sum(afferent_couplings) / len(afferent_couplings)
                metrics['max_afferent_coupling'] = max(afferent_couplings)

            if efferent_couplings:
                metrics['average_efferent_coupling'] = sum(efferent_couplings) / len(efferent_couplings)
                metrics['max_efferent_coupling'] = max(efferent_couplings)

        # Architectural pattern coverage
        patterns = self._detect_architectural_patterns()
        metrics['pattern_count'] = len(patterns)
        metrics['pattern_coverage'] = sum(p['confidence'] for p in patterns) / max(1, len(patterns))

        return metrics

    def _generate_architectural_recommendations(self, patterns: List[Dict[str, Any]],
                                              violations: List[str]) -> List[str]:
        """Generate architectural improvement recommendations."""
        recommendations = []

        # Pattern-based recommendations
        pattern_names = [p['name'] for p in patterns]

        if 'Repository Pattern' not in pattern_names:
            recommendations.append("Consider implementing Repository pattern for better data access abstraction")

        if 'Service Layer Pattern' not in pattern_names:
            recommendations.append("Consider implementing Service Layer pattern to encapsulate business logic")

        if len(patterns) == 0:
            recommendations.append("Consider adopting established architectural patterns for better code organization")

        # Layer violation recommendations
        if violations:
            recommendations.append(f"Fix {len(violations)} layer violations to improve architectural integrity")
            recommendations.append("Review dependency directions to ensure proper layering")

        # Coupling recommendations
        metrics = self._calculate_architectural_metrics()
        if metrics.get('max_afferent_coupling', 0) > 10:
            recommendations.append("Some modules have high afferent coupling - consider breaking them down")

        if metrics.get('max_efferent_coupling', 0) > 15:
            recommendations.append("Some modules have high efferent coupling - consider reducing dependencies")

        return recommendations
