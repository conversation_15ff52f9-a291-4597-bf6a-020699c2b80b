"""
Standalone Code Quality Analyzer
===============================

This module provides comprehensive code analysis capabilities using only Python's
built-in libraries, ensuring Vibe Check provides substantial value even without
external tools like ruff, mypy, bandit, etc.

The standalone analyzer focuses on:
- Code quality metrics
- Style and formatting issues
- Basic security patterns
- Complexity analysis
- Documentation coverage
- Import analysis
"""

import ast
import re
import logging
from pathlib import Path

from ..error_handling.error_aggregator import get_error_aggregator
from typing import Any, Dict, List, Union

logger = logging.getLogger("vibe_check_standalone")


class StandaloneCodeAnalyzer:
    """
    Comprehensive standalone code analyzer that provides substantial value
    without requiring external tools.
    """

    def __init__(self):
        """Initialize the standalone analyzer."""
        self.security_patterns = self._load_security_patterns()
        self.style_rules = self._load_style_rules()

    def analyze_file(self, file_path: Union[str, Path], content: str) -> Dict[str, Any]:
        """
        Perform comprehensive analysis of a Python file.
        
        Args:
            file_path: Path to the file being analyzed
            content: File content as string
            
        Returns:
            Dictionary with analysis results
        """
        file_path = Path(file_path)
        
        try:
            tree = ast.parse(content, filename=str(file_path))
        except SyntaxError as e:
            # Add to error aggregator
            aggregator = get_error_aggregator()
            aggregator.add_syntax_error(
                str(e.msg),
                file_path,
                e.lineno
            )

            return {
                "syntax_errors": [{
                    "line": e.lineno or 1,
                    "column": e.offset or 0,
                    "message": str(e.msg),
                    "severity": "error",
                    "rule": "E999"
                }],
                "analysis_successful": False,
                "analyzer": "vibe_check_standalone"
            }

        issues = []
        
        # 1. Line-based analysis
        line_issues = self._analyze_lines(content)
        issues.extend(line_issues)
        
        # 2. AST-based analysis
        ast_issues = self._analyze_ast(tree, content)
        issues.extend(ast_issues)
        
        # 3. Security pattern analysis
        security_issues = self._analyze_security(content, tree)
        issues.extend(security_issues)
        
        # 4. Import analysis
        import_issues = self._analyze_imports(tree)
        issues.extend(import_issues)
        
        # 5. Calculate metrics
        metrics = self._calculate_metrics(tree, content)
        
        return {
            "issues": issues,
            "metrics": metrics,
            "analysis_successful": True,
            "analyzer": "vibe_check_standalone",
            "tool_status": "standalone"
        }

    def _analyze_lines(self, content: str) -> List[Dict[str, Any]]:
        """Analyze content line by line for style and formatting issues."""
        issues = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            # Line length (PEP 8)
            if len(line) > 88:
                issues.append({
                    "line": i,
                    "column": 89,
                    "message": f"Line too long ({len(line)} > 88 characters)",
                    "severity": "style",
                    "rule": "E501",
                    "type": "line_length"
                })
            
            # Trailing whitespace
            if line.rstrip() != line:
                issues.append({
                    "line": i,
                    "column": len(line.rstrip()) + 1,
                    "message": "Trailing whitespace",
                    "severity": "style",
                    "rule": "W291",
                    "type": "whitespace"
                })
            
            # Mixed tabs and spaces
            if '\t' in line and '    ' in line:
                issues.append({
                    "line": i,
                    "column": 1,
                    "message": "Mixed tabs and spaces",
                    "severity": "style",
                    "rule": "E101",
                    "type": "indentation"
                })
            
            # Multiple statements on one line
            if ';' in line and not line.strip().startswith('#'):
                issues.append({
                    "line": i,
                    "column": line.find(';') + 1,
                    "message": "Multiple statements on one line",
                    "severity": "style",
                    "rule": "E702",
                    "type": "statement"
                })
        
        return issues

    def _analyze_ast(self, tree: ast.AST, content: str) -> List[Dict[str, Any]]:
        """Analyze AST for code quality issues."""
        issues = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                issues.extend(self._analyze_function(node, content))
            elif isinstance(node, ast.ClassDef):
                issues.extend(self._analyze_class(node))
            elif isinstance(node, ast.Name):
                issues.extend(self._analyze_name(node))
        
        return issues

    def _analyze_function(self, node: ast.FunctionDef, content: str) -> List[Dict[str, Any]]:
        """Analyze function for quality issues."""
        issues = []
        
        # Function complexity
        complexity = self._calculate_complexity(node)
        if complexity > 10:
            issues.append({
                "line": node.lineno,
                "column": node.col_offset,
                "message": f"Function '{node.name}' is too complex ({complexity})",
                "severity": "warning",
                "rule": "C901",
                "type": "complexity"
            })
        
        # Function length
        func_length = (getattr(node, 'end_lineno', node.lineno) - node.lineno + 1)
        if func_length > 50:
            issues.append({
                "line": node.lineno,
                "column": node.col_offset,
                "message": f"Function '{node.name}' is too long ({func_length} lines)",
                "severity": "warning",
                "rule": "C901",
                "type": "length"
            })
        
        # Missing docstring
        if not ast.get_docstring(node):
            issues.append({
                "line": node.lineno,
                "column": node.col_offset,
                "message": f"Function '{node.name}' missing docstring",
                "severity": "info",
                "rule": "D100",
                "type": "documentation"
            })
        
        # Too many arguments
        if len(node.args.args) > 5:
            issues.append({
                "line": node.lineno,
                "column": node.col_offset,
                "message": f"Function '{node.name}' has too many arguments ({len(node.args.args)})",
                "severity": "warning",
                "rule": "R0913",
                "type": "arguments"
            })
        
        return issues

    def _analyze_class(self, node: ast.ClassDef) -> List[Dict[str, Any]]:
        """Analyze class for quality issues."""
        issues = []
        
        # Missing docstring
        if not ast.get_docstring(node):
            issues.append({
                "line": node.lineno,
                "column": node.col_offset,
                "message": f"Class '{node.name}' missing docstring",
                "severity": "info",
                "rule": "D101",
                "type": "documentation"
            })
        
        # Too many methods (simplified check)
        methods = [n for n in node.body if isinstance(n, ast.FunctionDef)]
        if len(methods) > 20:
            issues.append({
                "line": node.lineno,
                "column": node.col_offset,
                "message": f"Class '{node.name}' has too many methods ({len(methods)})",
                "severity": "warning",
                "rule": "R0904",
                "type": "methods"
            })
        
        return issues

    def _analyze_name(self, node: ast.Name) -> List[Dict[str, Any]]:
        """Analyze variable names for style issues."""
        issues = []
        
        # Check naming conventions (simplified)
        if isinstance(node.ctx, ast.Store):
            name = node.id
            
            # Constants should be UPPER_CASE
            if name.isupper() and '_' in name:
                pass  # Good constant name
            elif name.isupper() and len(name) > 1:
                # Might be a constant without underscores
                pass
            elif not name.islower() and '_' not in name and not name.startswith('_'):
                # Variable should be snake_case
                if any(c.isupper() for c in name):
                    issues.append({
                        "line": node.lineno,
                        "column": node.col_offset,
                        "message": f"Variable '{name}' should use snake_case",
                        "severity": "style",
                        "rule": "N806",
                        "type": "naming"
                    })
        
        return issues

    def _analyze_security(self, content: str, tree: ast.AST) -> List[Dict[str, Any]]:
        """Analyze for basic security issues."""
        issues = []
        
        # Check for dangerous function calls
        dangerous_calls = {
            'eval': 'Use of eval() is dangerous',
            'exec': 'Use of exec() is dangerous',
            'compile': 'Use of compile() can be dangerous',
            '__import__': 'Dynamic imports can be dangerous'
        }
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Call):
                if isinstance(node.func, ast.Name):
                    func_name = node.func.id
                    if func_name in dangerous_calls:
                        issues.append({
                            "line": node.lineno,
                            "column": node.col_offset,
                            "message": dangerous_calls[func_name],
                            "severity": "warning",
                            "rule": "S102",
                            "type": "security"
                        })
        
        # Check for hardcoded passwords/secrets (basic patterns)
        secret_patterns = [
            r'password\s*=\s*["\'][^"\']+["\']',
            r'secret\s*=\s*["\'][^"\']+["\']',
            r'api_key\s*=\s*["\'][^"\']+["\']',
            r'token\s*=\s*["\'][^"\']+["\']'
        ]
        
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            for pattern in secret_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    issues.append({
                        "line": i,
                        "column": 1,
                        "message": "Possible hardcoded secret",
                        "severity": "warning",
                        "rule": "S105",
                        "type": "security"
                    })
        
        return issues

    def _analyze_imports(self, tree: ast.AST) -> List[Dict[str, Any]]:
        """Analyze import statements."""
        issues = []
        imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    imports.append(node.module)
        
        # Check for unused imports (very basic check)
        # This is simplified - real unused import detection is complex
        
        return issues

    def _calculate_complexity(self, node: ast.FunctionDef) -> int:
        """Calculate cyclomatic complexity for a function."""
        complexity = 1  # Base complexity
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(child, ast.ExceptHandler):
                complexity += 1
            elif isinstance(child, (ast.And, ast.Or)):
                complexity += 1
            elif isinstance(child, ast.BoolOp):
                complexity += len(child.values) - 1
        
        return complexity

    def _calculate_metrics(self, tree: ast.AST, content: str) -> Dict[str, Any]:
        """Calculate comprehensive code metrics."""
        lines = content.split('\n')
        
        # Count different elements
        functions = [n for n in ast.walk(tree) if isinstance(n, ast.FunctionDef)]
        classes = [n for n in ast.walk(tree) if isinstance(n, ast.ClassDef)]
        imports = [n for n in ast.walk(tree) if isinstance(n, (ast.Import, ast.ImportFrom))]
        
        # Calculate complexity metrics
        total_complexity = sum(self._calculate_complexity(f) for f in functions)
        avg_complexity = total_complexity / len(functions) if functions else 0
        
        return {
            "total_lines": len(lines),
            "code_lines": len([line for line in lines if line.strip() and not line.strip().startswith('#')]),
            "comment_lines": len([line for line in lines if line.strip().startswith('#')]),
            "blank_lines": len([line for line in lines if not line.strip()]),
            "function_count": len(functions),
            "class_count": len(classes),
            "import_count": len(imports),
            "complexity": {
                "total": total_complexity,
                "average": avg_complexity,
                "max": max((self._calculate_complexity(f) for f in functions), default=0)
            },
            "maintainability_index": self._calculate_maintainability_index(tree, content)
        }

    def _calculate_maintainability_index(self, tree: ast.AST, content: str) -> float:
        """Calculate a simplified maintainability index."""
        # Simplified version of the maintainability index
        # Real calculation involves Halstead metrics and cyclomatic complexity
        
        lines = content.split('\n')
        code_lines = len([line for line in lines if line.strip() and not line.strip().startswith('#')])
        comment_lines = len([line for line in lines if line.strip().startswith('#')])
        
        if code_lines == 0:
            return 100.0
        
        # Simple heuristic based on code/comment ratio and complexity
        comment_ratio = comment_lines / code_lines if code_lines > 0 else 0
        functions = [n for n in ast.walk(tree) if isinstance(n, ast.FunctionDef)]
        avg_complexity = sum(self._calculate_complexity(f) for f in functions) / len(functions) if functions else 1
        
        # Simplified maintainability index (0-100 scale)
        index = max(0, min(100, 100 - (avg_complexity * 5) + (comment_ratio * 10)))
        return round(index, 2)

    def _load_security_patterns(self) -> List[str]:
        """Load security patterns for analysis."""
        return [
            r'eval\s*\(',
            r'exec\s*\(',
            r'__import__\s*\(',
            r'password\s*=',
            r'secret\s*=',
            r'api_key\s*='
        ]

    def _load_style_rules(self) -> Dict[str, str]:
        """Load style rules for analysis."""
        return {
            "line_length": "Lines should not exceed 88 characters",
            "trailing_whitespace": "Lines should not have trailing whitespace",
            "mixed_indentation": "Do not mix tabs and spaces",
            "multiple_statements": "Do not put multiple statements on one line"
        }
