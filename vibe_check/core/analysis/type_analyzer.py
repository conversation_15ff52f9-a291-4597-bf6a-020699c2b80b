"""
Type System Analyzer

This module provides type annotation analysis capabilities for Python code.
It analyzes type hints, type consistency, and type coverage.
"""

import ast
import logging
from pathlib import Path
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Union

from .python_semantic_analyzer import <PERSON>mantic<PERSON><PERSON>, SemanticIssue, SemanticContext

logger = logging.getLogger(__name__)


@dataclass
class TypeInfo:
    """Information about a type annotation."""
    
    annotation: str
    line_number: int
    column_number: int
    is_generic: bool = False
    is_union: bool = False
    is_optional: bool = False
    complexity: int = 1


@dataclass
class TypeAnalysisResult:
    """Results from type analysis."""
    
    total_functions: int = 0
    typed_functions: int = 0
    total_parameters: int = 0
    typed_parameters: int = 0
    total_returns: int = 0
    typed_returns: int = 0
    type_annotations: List[TypeInfo] = field(default_factory=list)
    type_issues: List[SemanticIssue] = field(default_factory=list)
    
    @property
    def function_type_coverage(self) -> float:
        """Calculate function type coverage percentage."""
        if self.total_functions == 0:
            return 100.0
        return (self.typed_functions / self.total_functions) * 100
    
    @property
    def parameter_type_coverage(self) -> float:
        """Calculate parameter type coverage percentage."""
        if self.total_parameters == 0:
            return 100.0
        return (self.typed_parameters / self.total_parameters) * 100
    
    @property
    def return_type_coverage(self) -> float:
        """Calculate return type coverage percentage."""
        if self.total_returns == 0:
            return 100.0
        return (self.typed_returns / self.total_returns) * 100


class TypeAnalyzer(ast.NodeVisitor):
    """
    Analyzer for Python type annotations and type system usage.
    """
    
    def __init__(self):
        self.result = TypeAnalysisResult()
        self.current_function: Optional[str] = None
        self.scope_stack: List[str] = []
    
    def analyze_source(self, source_code: str, file_path: Path) -> TypeAnalysisResult:
        """
        Analyze Python source code for type annotations.
        
        Args:
            source_code: Python source code to analyze
            file_path: Path to the source file
            
        Returns:
            TypeAnalysisResult containing type analysis metrics
        """
        try:
            tree = ast.parse(source_code, filename=str(file_path))
            self.result = TypeAnalysisResult()
            self.visit(tree)
            return self.result
            
        except SyntaxError as e:
            logger.warning(f"Syntax error in {file_path}: {e}")
            return TypeAnalysisResult()
        except Exception as e:
            logger.error(f"Error analyzing types in {file_path}: {e}")
            return TypeAnalysisResult()
    
    def visit_FunctionDef(self, node: ast.FunctionDef) -> None:
        """Visit function definitions to analyze type annotations."""
        self.result.total_functions += 1
        self.current_function = node.name
        
        # Check if function has return type annotation
        if node.returns:
            self.result.typed_returns += 1
            self._analyze_type_annotation(node.returns, node.lineno, node.col_offset)
        
        self.result.total_returns += 1
        
        # Analyze function parameters
        self._analyze_function_args(node.args, node.lineno)
        
        # Check if function is fully typed
        has_return_type = node.returns is not None
        all_params_typed = self._all_parameters_typed(node.args)
        
        if has_return_type and all_params_typed:
            self.result.typed_functions += 1
        
        self.generic_visit(node)
        self.current_function = None
    
    def visit_AsyncFunctionDef(self, node: ast.AsyncFunctionDef) -> None:
        """Visit async function definitions."""
        # Same logic as regular functions
        self.result.total_functions += 1
        self.current_function = node.name
        
        if node.returns:
            self.result.typed_returns += 1
            self._analyze_type_annotation(node.returns, node.lineno, node.col_offset)
        
        self.result.total_returns += 1
        
        self._analyze_function_args(node.args, node.lineno)
        
        has_return_type = node.returns is not None
        all_params_typed = self._all_parameters_typed(node.args)
        
        if has_return_type and all_params_typed:
            self.result.typed_functions += 1
        
        self.generic_visit(node)
        self.current_function = None
    
    def _analyze_function_args(self, args: ast.arguments, line_number: int) -> None:
        """Analyze function arguments for type annotations."""
        # Regular arguments
        for arg in args.args:
            self.result.total_parameters += 1
            if arg.annotation:
                self.result.typed_parameters += 1
                self._analyze_type_annotation(arg.annotation, line_number, arg.col_offset)
        
        # Positional-only arguments (Python 3.8+)
        for arg in args.posonlyargs:
            self.result.total_parameters += 1
            if arg.annotation:
                self.result.typed_parameters += 1
                self._analyze_type_annotation(arg.annotation, line_number, arg.col_offset)
        
        # Keyword-only arguments
        for arg in args.kwonlyargs:
            self.result.total_parameters += 1
            if arg.annotation:
                self.result.typed_parameters += 1
                self._analyze_type_annotation(arg.annotation, line_number, arg.col_offset)
        
        # *args
        if args.vararg and args.vararg.annotation:
            self.result.total_parameters += 1
            self.result.typed_parameters += 1
            self._analyze_type_annotation(args.vararg.annotation, line_number, args.vararg.col_offset)
        elif args.vararg:
            self.result.total_parameters += 1
        
        # **kwargs
        if args.kwarg and args.kwarg.annotation:
            self.result.total_parameters += 1
            self.result.typed_parameters += 1
            self._analyze_type_annotation(args.kwarg.annotation, line_number, args.kwarg.col_offset)
        elif args.kwarg:
            self.result.total_parameters += 1
    
    def _all_parameters_typed(self, args: ast.arguments) -> bool:
        """Check if all parameters have type annotations."""
        # Check regular args (skip 'self' for methods)
        start_idx = 1 if args.args and args.args[0].arg == 'self' else 0
        for arg in args.args[start_idx:]:
            if not arg.annotation:
                return False
        
        # Check positional-only args
        for arg in args.posonlyargs:
            if not arg.annotation:
                return False
        
        # Check keyword-only args
        for arg in args.kwonlyargs:
            if not arg.annotation:
                return False
        
        # Check *args and **kwargs
        if args.vararg and not args.vararg.annotation:
            return False
        if args.kwarg and not args.kwarg.annotation:
            return False
        
        return True
    
    def _analyze_type_annotation(self, annotation: ast.AST, line_number: int, column_number: int) -> None:
        """Analyze a type annotation for complexity and patterns."""
        annotation_str = self._ast_to_string(annotation)
        
        type_info = TypeInfo(
            annotation=annotation_str,
            line_number=line_number,
            column_number=column_number,
            is_generic=self._is_generic_type(annotation),
            is_union=self._is_union_type(annotation),
            is_optional=self._is_optional_type(annotation),
            complexity=self._calculate_type_complexity(annotation)
        )
        
        self.result.type_annotations.append(type_info)
    
    def _ast_to_string(self, node: ast.AST) -> str:
        """Convert AST node to string representation."""
        try:
            import astor
            return astor.to_source(node).strip()
        except ImportError:
            # Fallback to basic representation
            if isinstance(node, ast.Name):
                return node.id
            elif isinstance(node, ast.Constant):
                return str(node.value)
            elif isinstance(node, ast.Attribute):
                return f"{self._ast_to_string(node.value)}.{node.attr}"
            else:
                return str(type(node).__name__)
    
    def _is_generic_type(self, annotation: ast.AST) -> bool:
        """Check if annotation is a generic type."""
        if isinstance(annotation, ast.Subscript):
            return True
        return False
    
    def _is_union_type(self, annotation: ast.AST) -> bool:
        """Check if annotation is a Union type."""
        if isinstance(annotation, ast.Subscript):
            value_str = self._ast_to_string(annotation.value)
            return 'Union' in value_str
        elif isinstance(annotation, ast.BinOp) and isinstance(annotation.op, ast.BitOr):
            # Python 3.10+ union syntax (X | Y)
            return True
        return False
    
    def _is_optional_type(self, annotation: ast.AST) -> bool:
        """Check if annotation is Optional."""
        if isinstance(annotation, ast.Subscript):
            value_str = self._ast_to_string(annotation.value)
            return 'Optional' in value_str
        elif isinstance(annotation, ast.BinOp) and isinstance(annotation.op, ast.BitOr):
            # Check for X | None pattern
            right_str = self._ast_to_string(annotation.right)
            return 'None' in right_str
        return False
    
    def _calculate_type_complexity(self, annotation: ast.AST) -> int:
        """Calculate complexity score for a type annotation."""
        complexity = 1
        
        if isinstance(annotation, ast.Subscript):
            complexity += 1
            # Add complexity for nested generics
            if hasattr(annotation, 'slice'):
                if isinstance(annotation.slice, ast.Tuple):
                    complexity += len(annotation.slice.elts)
                else:
                    complexity += self._calculate_type_complexity(annotation.slice)
        elif isinstance(annotation, ast.BinOp):
            complexity += 1
            complexity += self._calculate_type_complexity(annotation.left)
            complexity += self._calculate_type_complexity(annotation.right)
        
        return complexity


# Type-related semantic rules

class MissingTypeAnnotationRule(SemanticRule):
    """Rule to detect missing type annotations."""
    
    def __init__(self, require_return_types: bool = True, require_param_types: bool = True):
        super().__init__(
            rule_id="missing_type_annotation",
            description="Detects functions missing type annotations",
            severity="info"
        )
        self.require_return_types = require_return_types
        self.require_param_types = require_param_types
    
    def check(self, node: ast.AST, context: SemanticContext) -> List[SemanticIssue]:
        """Check for missing type annotations."""
        issues = []
        
        if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
            # Skip special methods and private methods for now
            if node.name.startswith('__') and node.name.endswith('__'):
                return issues
            
            # Check return type
            if self.require_return_types and not node.returns:
                issues.append(SemanticIssue(
                    rule_id=self.rule_id,
                    severity=self.severity,
                    message=f"Function '{node.name}' missing return type annotation",
                    line_number=node.lineno,
                    column_number=node.col_offset,
                    suggestion="Add return type annotation: def func() -> ReturnType:"
                ))
            
            # Check parameter types
            if self.require_param_types:
                missing_params = []
                
                # Skip 'self' parameter for methods
                start_idx = 1 if (node.args.args and 
                                node.args.args[0].arg == 'self' and 
                                context.current_scope.startswith('class:')) else 0
                
                for arg in node.args.args[start_idx:]:
                    if not arg.annotation:
                        missing_params.append(arg.arg)
                
                if missing_params:
                    issues.append(SemanticIssue(
                        rule_id=self.rule_id,
                        severity=self.severity,
                        message=f"Function '{node.name}' has parameters without type annotations: {', '.join(missing_params)}",
                        line_number=node.lineno,
                        column_number=node.col_offset,
                        suggestion="Add type annotations: def func(param: ParamType):"
                    ))
        
        return issues


class InconsistentTypeUsageRule(SemanticRule):
    """Rule to detect inconsistent type usage patterns."""
    
    def __init__(self):
        super().__init__(
            rule_id="inconsistent_type_usage",
            description="Detects inconsistent type annotation patterns",
            severity="warning"
        )
    
    def check(self, node: ast.AST, context: SemanticContext) -> List[SemanticIssue]:
        """Check for inconsistent type usage."""
        issues = []
        
        # This would require more sophisticated analysis across the entire file
        # For now, we'll implement basic checks
        
        return issues
