"""
File: vibe_check/core/persistence/trend_analysis.py
Purpose: Historical progress tracking and trend analysis for analysis runs
Related Files:
    - vibe_check/core/persistence/storage.py - Result storage and retrieval
    - vibe_check/core/persistence/models.py - Data models
Dependencies: dataclasses, typing, datetime, statistics

Implements historical comparison and trend tracking capabilities for project health monitoring.
"""

from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from enum import Enum
import statistics
from .models import AnalysisRun, AnalysisMode
from .storage import ResultStorage


class TrendDirection(Enum):
    """Trend direction enumeration."""
    IMPROVING = "improving"
    DEGRADING = "degrading"
    STABLE = "stable"
    VOLATILE = "volatile"


@dataclass
class RunDifferential:
    """Represents the difference between two analysis runs."""
    
    current_run_id: int
    previous_run_id: int
    
    # Issue changes
    issues_added: int = 0
    issues_resolved: int = 0
    net_issue_change: int = 0
    
    # Metric changes
    complexity_change: float = 0.0
    type_coverage_change: float = 0.0
    meritocracy_change: float = 0.0
    
    # Performance changes
    analysis_time_change: float = 0.0
    file_count_change: int = 0
    
    # Calculated at creation
    created_at: datetime = field(default_factory=datetime.now)
    
    def get_overall_trend(self) -> TrendDirection:
        """Determine overall trend direction based on changes."""
        positive_indicators = 0
        negative_indicators = 0
        
        # Issue trend (fewer issues is better)
        if self.net_issue_change < 0:
            positive_indicators += 1
        elif self.net_issue_change > 0:
            negative_indicators += 1
        
        # Complexity trend (lower complexity is better)
        if self.complexity_change < 0:
            positive_indicators += 1
        elif self.complexity_change > 0:
            negative_indicators += 1
        
        # Type coverage trend (higher coverage is better)
        if self.type_coverage_change > 0:
            positive_indicators += 1
        elif self.type_coverage_change < 0:
            negative_indicators += 1
        
        # Meritocracy trend (higher score is better)
        if self.meritocracy_change > 0:
            positive_indicators += 1
        elif self.meritocracy_change < 0:
            negative_indicators += 1
        
        if positive_indicators > negative_indicators:
            return TrendDirection.IMPROVING
        elif negative_indicators > positive_indicators:
            return TrendDirection.DEGRADING
        else:
            return TrendDirection.STABLE


@dataclass
class TrendData:
    """Represents trend data for a specific metric over time."""
    
    metric_name: str
    project_path: str
    timeframe_days: int
    
    values: List[float] = field(default_factory=list)
    timestamps: List[datetime] = field(default_factory=list)
    
    trend_direction: TrendDirection = TrendDirection.STABLE
    trend_strength: float = 0.0  # 0.0 to 1.0
    
    # Statistical measures
    mean_value: float = 0.0
    median_value: float = 0.0
    std_deviation: float = 0.0
    min_value: float = 0.0
    max_value: float = 0.0
    
    def calculate_statistics(self) -> None:
        """Calculate statistical measures for the trend data."""
        if not self.values:
            return
        
        self.mean_value = statistics.mean(self.values)
        self.median_value = statistics.median(self.values)
        self.min_value = min(self.values)
        self.max_value = max(self.values)
        
        if len(self.values) > 1:
            self.std_deviation = statistics.stdev(self.values)
            
            # Calculate trend direction and strength using linear regression
            self._calculate_trend()
    
    def _calculate_trend(self) -> None:
        """Calculate trend direction and strength using simple linear regression."""
        if len(self.values) < 2:
            return
        
        n = len(self.values)
        x_values = list(range(n))  # Time points
        y_values = self.values
        
        # Calculate slope using least squares
        x_mean = statistics.mean(x_values)
        y_mean = statistics.mean(y_values)
        
        numerator = sum((x - x_mean) * (y - y_mean) for x, y in zip(x_values, y_values))
        denominator = sum((x - x_mean) ** 2 for x in x_values)
        
        if denominator == 0:
            slope = 0
        else:
            slope = numerator / denominator
        
        # Determine trend direction
        if abs(slope) < 0.01:  # Threshold for stability
            self.trend_direction = TrendDirection.STABLE
        elif slope > 0:
            self.trend_direction = TrendDirection.IMPROVING
        else:
            self.trend_direction = TrendDirection.DEGRADING
        
        # Calculate trend strength (normalized)
        value_range = self.max_value - self.min_value
        if value_range > 0:
            self.trend_strength = min(abs(slope * n) / value_range, 1.0)
        else:
            self.trend_strength = 0.0


@dataclass
class ImprovementPattern:
    """Represents an identified improvement pattern in analysis runs."""
    
    pattern_type: str  # e.g., "consistent_improvement", "recent_improvement"
    description: str
    confidence: float  # 0.0 to 1.0
    
    affected_metrics: List[str] = field(default_factory=list)
    time_period: Tuple[datetime, datetime] = field(default_factory=lambda: (datetime.now(), datetime.now()))
    
    # Supporting data
    runs_analyzed: int = 0
    improvement_magnitude: float = 0.0


@dataclass
class RegressionPattern:
    """Represents an identified regression pattern in analysis runs."""
    
    pattern_type: str  # e.g., "sudden_regression", "gradual_decline"
    description: str
    severity: str  # "low", "medium", "high", "critical"
    
    affected_metrics: List[str] = field(default_factory=list)
    time_period: Tuple[datetime, datetime] = field(default_factory=lambda: (datetime.now(), datetime.now()))
    
    # Supporting data
    runs_analyzed: int = 0
    regression_magnitude: float = 0.0


class TrendAnalysis:
    """Provides historical progress tracking and trend analysis capabilities."""
    
    def __init__(self, storage: Optional[ResultStorage] = None):
        """Initialize trend analysis.
        
        Args:
            storage: Result storage instance. If None, creates default.
        """
        self.storage = storage or ResultStorage()
    
    def calculate_run_differential(self, current_run: AnalysisRun, previous_run: AnalysisRun) -> RunDifferential:
        """Calculate the differential between two analysis runs.
        
        Args:
            current_run: The more recent analysis run
            previous_run: The earlier analysis run for comparison
            
        Returns:
            RunDifferential object with calculated changes
        """
        differential = RunDifferential(
            current_run_id=current_run.run_id or 0,
            previous_run_id=previous_run.run_id or 0
        )
        
        # Calculate issue changes
        differential.net_issue_change = current_run.total_issues - previous_run.total_issues
        differential.issues_added = max(0, differential.net_issue_change)
        differential.issues_resolved = max(0, -differential.net_issue_change)
        
        # Calculate metric changes
        differential.complexity_change = current_run.average_complexity - previous_run.average_complexity
        differential.type_coverage_change = current_run.overall_type_coverage - previous_run.overall_type_coverage
        differential.meritocracy_change = current_run.meritocracy_score - previous_run.meritocracy_score
        
        # Calculate performance changes
        differential.analysis_time_change = current_run.analysis_duration - previous_run.analysis_duration
        differential.file_count_change = current_run.total_files - previous_run.total_files
        
        return differential
    
    def track_metric_trends(self, project_path: str, metric: str, timeframe_days: int = 30) -> TrendData:
        """Track trends for a specific metric over time.
        
        Args:
            project_path: Path to the project to analyze
            metric: Name of the metric to track
            timeframe_days: Number of days to look back
            
        Returns:
            TrendData object with trend analysis
        """
        # Get runs within timeframe
        cutoff_date = datetime.now() - timedelta(days=timeframe_days)
        runs = self.storage.find_runs_by_project(project_path, limit=100)
        
        # Filter by timeframe
        recent_runs = [run for run in runs if run.start_time >= cutoff_date]
        recent_runs.sort(key=lambda r: r.start_time)
        
        # Extract metric values
        trend_data = TrendData(
            metric_name=metric,
            project_path=project_path,
            timeframe_days=timeframe_days
        )
        
        for run in recent_runs:
            value = self._extract_metric_value(run, metric)
            if value is not None:
                trend_data.values.append(value)
                trend_data.timestamps.append(run.start_time)
        
        # Calculate statistics and trends
        trend_data.calculate_statistics()
        
        return trend_data
    
    def _extract_metric_value(self, run: AnalysisRun, metric: str) -> Optional[float]:
        """Extract a specific metric value from an analysis run."""
        metric_map = {
            'total_issues': float(run.total_issues),
            'average_complexity': run.average_complexity,
            'type_coverage': run.overall_type_coverage,
            'meritocracy_score': run.meritocracy_score,
            'analysis_duration': run.analysis_duration,
            'total_files': float(run.total_files),
            'total_lines': float(run.total_lines)
        }
        
        return metric_map.get(metric)
    
    def identify_improvement_patterns(self, runs: List[AnalysisRun]) -> List[ImprovementPattern]:
        """Identify improvement patterns in a series of analysis runs.
        
        Args:
            runs: List of analysis runs to analyze (should be chronologically ordered)
            
        Returns:
            List of identified improvement patterns
        """
        patterns = []
        
        if len(runs) < 3:
            return patterns
        
        # Sort runs by time
        sorted_runs = sorted(runs, key=lambda r: r.start_time)
        
        # Check for consistent improvement in meritocracy score
        meritocracy_scores = [run.meritocracy_score for run in sorted_runs]
        if self._is_consistently_improving(meritocracy_scores):
            patterns.append(ImprovementPattern(
                pattern_type="consistent_improvement",
                description="Consistent improvement in overall project quality",
                confidence=0.8,
                affected_metrics=["meritocracy_score"],
                time_period=(sorted_runs[0].start_time, sorted_runs[-1].start_time),
                runs_analyzed=len(sorted_runs),
                improvement_magnitude=meritocracy_scores[-1] - meritocracy_scores[0]
            ))
        
        # Check for issue reduction trend
        issue_counts = [run.total_issues for run in sorted_runs]
        if self._is_consistently_decreasing(issue_counts):
            patterns.append(ImprovementPattern(
                pattern_type="issue_reduction",
                description="Consistent reduction in total issues",
                confidence=0.7,
                affected_metrics=["total_issues"],
                time_period=(sorted_runs[0].start_time, sorted_runs[-1].start_time),
                runs_analyzed=len(sorted_runs),
                improvement_magnitude=float(issue_counts[0] - issue_counts[-1])
            ))
        
        return patterns
    
    def detect_regression_patterns(self, runs: List[AnalysisRun]) -> List[RegressionPattern]:
        """Detect regression patterns in a series of analysis runs.
        
        Args:
            runs: List of analysis runs to analyze (should be chronologically ordered)
            
        Returns:
            List of identified regression patterns
        """
        patterns = []
        
        if len(runs) < 3:
            return patterns
        
        # Sort runs by time
        sorted_runs = sorted(runs, key=lambda r: r.start_time)
        
        # Check for sudden increase in issues
        issue_counts = [run.total_issues for run in sorted_runs]
        if self._has_sudden_increase(issue_counts):
            patterns.append(RegressionPattern(
                pattern_type="sudden_regression",
                description="Sudden increase in total issues detected",
                severity="high",
                affected_metrics=["total_issues"],
                time_period=(sorted_runs[-2].start_time, sorted_runs[-1].start_time),
                runs_analyzed=len(sorted_runs),
                regression_magnitude=float(issue_counts[-1] - issue_counts[-2])
            ))
        
        # Check for complexity increase
        complexity_scores = [run.average_complexity for run in sorted_runs]
        if self._is_consistently_increasing(complexity_scores):
            patterns.append(RegressionPattern(
                pattern_type="complexity_increase",
                description="Consistent increase in code complexity",
                severity="medium",
                affected_metrics=["average_complexity"],
                time_period=(sorted_runs[0].start_time, sorted_runs[-1].start_time),
                runs_analyzed=len(sorted_runs),
                regression_magnitude=complexity_scores[-1] - complexity_scores[0]
            ))
        
        return patterns
    
    def _is_consistently_improving(self, values: List[float], threshold: float = 0.1) -> bool:
        """Check if values show consistent improvement (increase)."""
        if len(values) < 3:
            return False
        
        improvements = 0
        total_comparisons = len(values) - 1
        
        for i in range(1, len(values)):
            if values[i] > values[i-1] + threshold:
                improvements += 1
        
        return improvements >= total_comparisons * 0.7  # 70% of comparisons show improvement
    
    def _is_consistently_decreasing(self, values: List[float], threshold: float = 1.0) -> bool:
        """Check if values show consistent decrease."""
        if len(values) < 3:
            return False
        
        decreases = 0
        total_comparisons = len(values) - 1
        
        for i in range(1, len(values)):
            if values[i] < values[i-1] - threshold:
                decreases += 1
        
        return decreases >= total_comparisons * 0.7  # 70% of comparisons show decrease
    
    def _is_consistently_increasing(self, values: List[float], threshold: float = 0.5) -> bool:
        """Check if values show consistent increase."""
        if len(values) < 3:
            return False
        
        increases = 0
        total_comparisons = len(values) - 1
        
        for i in range(1, len(values)):
            if values[i] > values[i-1] + threshold:
                increases += 1
        
        return increases >= total_comparisons * 0.7  # 70% of comparisons show increase
    
    def _has_sudden_increase(self, values: List[float], multiplier: float = 1.5) -> bool:
        """Check if the latest value shows a sudden increase."""
        if len(values) < 2:
            return False
        
        return values[-1] > values[-2] * multiplier
