"""
File: vibe_check/core/persistence/comparison.py
Purpose: Detailed comparison functionality for VCS vs Plugin analysis results
Related Files:
    - vibe_check/core/persistence/storage.py - Result storage and retrieval
    - vibe_check/core/persistence/models.py - Data models
Dependencies: dataclasses, typing, collections

Provides detailed comparison analysis between VCS mode and plugin mode results.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Set, Any, Optional, Tuple
from collections import defaultdict
from .models import AnalysisRun, FileResult, Issue, IssueSeverity
from .storage import ResultStorage


@dataclass
class IssueComparison:
    """Comparison of issues between two analysis runs."""
    
    # Issues found in both runs
    shared_issues: List[Tuple[Issue, Issue]] = field(default_factory=list)
    
    # Issues found only in VCS run
    vcs_only_issues: List[Issue] = field(default_factory=list)
    
    # Issues found only in plugin run
    plugin_only_issues: List[Issue] = field(default_factory=list)
    
    # Summary statistics
    total_vcs_issues: int = 0
    total_plugin_issues: int = 0
    shared_count: int = 0
    
    def calculate_coverage_metrics(self) -> Dict[str, float]:
        """Calculate coverage metrics for the comparison."""
        if self.total_plugin_issues == 0:
            return {
                'vcs_coverage_percent': 0.0,
                'plugin_advantage_percent': 0.0,
                'overlap_percent': 0.0
            }
        
        vcs_coverage = (self.shared_count / self.total_plugin_issues) * 100
        plugin_advantage = (len(self.plugin_only_issues) / self.total_plugin_issues) * 100
        overlap = (self.shared_count / max(self.total_vcs_issues, self.total_plugin_issues)) * 100
        
        return {
            'vcs_coverage_percent': vcs_coverage,
            'plugin_advantage_percent': plugin_advantage,
            'overlap_percent': overlap
        }


@dataclass
class FileComparison:
    """Comparison of analysis results for a single file."""
    
    file_path: str
    vcs_result: Optional[FileResult] = None
    plugin_result: Optional[FileResult] = None
    issue_comparison: Optional[IssueComparison] = None
    
    def has_both_results(self) -> bool:
        """Check if both VCS and plugin results exist for this file."""
        return self.vcs_result is not None and self.plugin_result is not None
    
    def get_metrics_diff(self) -> Dict[str, float]:
        """Get difference in metrics between VCS and plugin results."""
        if not self.has_both_results():
            return {}
        
        return {
            'complexity_diff': self.plugin_result.complexity_score - self.vcs_result.complexity_score,
            'type_coverage_diff': self.plugin_result.type_coverage - self.vcs_result.type_coverage,
            'analysis_time_diff': self.plugin_result.analysis_time - self.vcs_result.analysis_time,
            'issue_count_diff': len(self.plugin_result.issues) - len(self.vcs_result.issues)
        }


@dataclass
class RunComparison:
    """Complete comparison between VCS and plugin analysis runs."""
    
    vcs_run: AnalysisRun
    plugin_run: AnalysisRun
    file_comparisons: List[FileComparison] = field(default_factory=list)
    overall_issue_comparison: Optional[IssueComparison] = None
    
    def get_summary_metrics(self) -> Dict[str, Any]:
        """Get summary metrics for the comparison."""
        metrics = {
            'vcs_total_issues': self.vcs_run.total_issues,
            'plugin_total_issues': self.plugin_run.total_issues,
            'vcs_total_files': self.vcs_run.total_files,
            'plugin_total_files': self.plugin_run.total_files,
            'vcs_analysis_time': self.vcs_run.analysis_duration,
            'plugin_analysis_time': self.plugin_run.analysis_duration,
            'vcs_success': self.vcs_run.success,
            'plugin_success': self.plugin_run.success
        }
        
        # Add coverage metrics if available
        if self.overall_issue_comparison:
            metrics.update(self.overall_issue_comparison.calculate_coverage_metrics())
        
        # Performance comparison
        if self.vcs_run.analysis_duration > 0 and self.plugin_run.analysis_duration > 0:
            speed_ratio = self.plugin_run.analysis_duration / self.vcs_run.analysis_duration
            metrics['speed_ratio'] = speed_ratio
            metrics['vcs_faster'] = speed_ratio > 1
        
        return metrics


class ComparisonEngine:
    """Engine for comparing VCS and plugin analysis results."""
    
    def __init__(self, storage: Optional[ResultStorage] = None):
        """Initialize comparison engine.
        
        Args:
            storage: Result storage instance. If None, creates default.
        """
        self.storage = storage or ResultStorage()
    
    def compare_runs(self, vcs_run_id: int, plugin_run_id: int) -> RunComparison:
        """Compare two analysis runs.
        
        Args:
            vcs_run_id: ID of the VCS analysis run
            plugin_run_id: ID of the plugin analysis run
            
        Returns:
            Detailed comparison of the two runs
        """
        # Load the analysis runs
        vcs_run = self.storage.get_analysis_run(vcs_run_id)
        plugin_run = self.storage.get_analysis_run(plugin_run_id)
        
        if not vcs_run or not plugin_run:
            raise ValueError("One or both analysis runs not found")
        
        # Create comparison object
        comparison = RunComparison(vcs_run=vcs_run, plugin_run=plugin_run)
        
        # Compare files
        comparison.file_comparisons = self._compare_files(vcs_run, plugin_run)
        
        # Compare overall issues
        comparison.overall_issue_comparison = self._compare_all_issues(vcs_run, plugin_run)
        
        return comparison
    
    def _compare_files(self, vcs_run: AnalysisRun, plugin_run: AnalysisRun) -> List[FileComparison]:
        """Compare file results between two runs."""
        # Create file maps for easy lookup
        vcs_files = {fr.relative_path: fr for fr in vcs_run.file_results}
        plugin_files = {fr.relative_path: fr for fr in plugin_run.file_results}
        
        # Get all unique file paths
        all_files = set(vcs_files.keys()) | set(plugin_files.keys())
        
        file_comparisons = []
        for file_path in sorted(all_files):
            vcs_result = vcs_files.get(file_path)
            plugin_result = plugin_files.get(file_path)
            
            file_comparison = FileComparison(
                file_path=file_path,
                vcs_result=vcs_result,
                plugin_result=plugin_result
            )
            
            # Compare issues if both results exist
            if vcs_result and plugin_result:
                file_comparison.issue_comparison = self._compare_issues(
                    vcs_result.issues, plugin_result.issues
                )
            
            file_comparisons.append(file_comparison)
        
        return file_comparisons
    
    def _compare_all_issues(self, vcs_run: AnalysisRun, plugin_run: AnalysisRun) -> IssueComparison:
        """Compare all issues between two runs."""
        # Collect all issues from both runs
        vcs_issues = []
        plugin_issues = []
        
        for file_result in vcs_run.file_results:
            vcs_issues.extend(file_result.issues)
        
        for file_result in plugin_run.file_results:
            plugin_issues.extend(file_result.issues)
        
        return self._compare_issues(vcs_issues, plugin_issues)
    
    def _compare_issues(self, vcs_issues: List[Issue], plugin_issues: List[Issue]) -> IssueComparison:
        """Compare two lists of issues."""
        comparison = IssueComparison()
        comparison.total_vcs_issues = len(vcs_issues)
        comparison.total_plugin_issues = len(plugin_issues)
        
        # Create issue signatures for matching
        vcs_signatures = {}
        for issue in vcs_issues:
            signature = self._create_issue_signature(issue)
            vcs_signatures[signature] = issue
        
        plugin_signatures = {}
        for issue in plugin_issues:
            signature = self._create_issue_signature(issue)
            plugin_signatures[signature] = issue
        
        # Find shared issues
        shared_signatures = set(vcs_signatures.keys()) & set(plugin_signatures.keys())
        for signature in shared_signatures:
            vcs_issue = vcs_signatures[signature]
            plugin_issue = plugin_signatures[signature]
            comparison.shared_issues.append((vcs_issue, plugin_issue))
        
        comparison.shared_count = len(shared_signatures)
        
        # Find VCS-only issues
        vcs_only_signatures = set(vcs_signatures.keys()) - set(plugin_signatures.keys())
        for signature in vcs_only_signatures:
            comparison.vcs_only_issues.append(vcs_signatures[signature])
        
        # Find plugin-only issues
        plugin_only_signatures = set(plugin_signatures.keys()) - set(vcs_signatures.keys())
        for signature in plugin_only_signatures:
            comparison.plugin_only_issues.append(plugin_signatures[signature])
        
        return comparison
    
    def _create_issue_signature(self, issue: Issue) -> str:
        """Create a unique signature for an issue to enable matching."""
        # Use rule_id, line_number, and first 50 chars of message
        message_part = issue.message[:50] if issue.message else ""
        return f"{issue.rule_id}:{issue.line_number}:{message_part}"
    
    def generate_comparison_report(self, comparison: RunComparison) -> Dict[str, Any]:
        """Generate a detailed comparison report."""
        report = {
            'summary': comparison.get_summary_metrics(),
            'run_details': {
                'vcs_run': {
                    'run_id': comparison.vcs_run.run_id,
                    'start_time': comparison.vcs_run.start_time.isoformat(),
                    'success': comparison.vcs_run.success,
                    'error_message': comparison.vcs_run.error_message
                },
                'plugin_run': {
                    'run_id': comparison.plugin_run.run_id,
                    'start_time': comparison.plugin_run.start_time.isoformat(),
                    'success': comparison.plugin_run.success,
                    'error_message': comparison.plugin_run.error_message
                }
            },
            'file_analysis': [],
            'issue_analysis': {}
        }
        
        # File-level analysis
        for file_comp in comparison.file_comparisons:
            file_data = {
                'file_path': file_comp.file_path,
                'has_both_results': file_comp.has_both_results(),
                'metrics_diff': file_comp.get_metrics_diff() if file_comp.has_both_results() else None
            }
            
            if file_comp.issue_comparison:
                file_data['issue_comparison'] = {
                    'vcs_issues': len(file_comp.issue_comparison.vcs_only_issues),
                    'plugin_issues': len(file_comp.issue_comparison.plugin_only_issues),
                    'shared_issues': file_comp.issue_comparison.shared_count
                }
            
            report['file_analysis'].append(file_data)
        
        # Overall issue analysis
        if comparison.overall_issue_comparison:
            ic = comparison.overall_issue_comparison
            report['issue_analysis'] = {
                'coverage_metrics': ic.calculate_coverage_metrics(),
                'vcs_only_count': len(ic.vcs_only_issues),
                'plugin_only_count': len(ic.plugin_only_issues),
                'shared_count': ic.shared_count,
                'enhancement_opportunities': self._analyze_enhancement_opportunities(ic)
            }
        
        return report
    
    def _analyze_enhancement_opportunities(self, issue_comparison: IssueComparison) -> Dict[str, Any]:
        """Analyze enhancement opportunities for VCS based on plugin-only issues."""
        opportunities = {
            'by_tool': defaultdict(int),
            'by_category': defaultdict(int),
            'by_severity': defaultdict(int),
            'top_rules': defaultdict(int)
        }
        
        for issue in issue_comparison.plugin_only_issues:
            opportunities['by_tool'][issue.tool] += 1
            opportunities['by_category'][issue.category] += 1
            opportunities['by_severity'][issue.severity.value] += 1
            opportunities['top_rules'][issue.rule_id] += 1
        
        # Convert to regular dicts and sort
        return {
            'by_tool': dict(sorted(opportunities['by_tool'].items(), key=lambda x: x[1], reverse=True)),
            'by_category': dict(sorted(opportunities['by_category'].items(), key=lambda x: x[1], reverse=True)),
            'by_severity': dict(sorted(opportunities['by_severity'].items(), key=lambda x: x[1], reverse=True)),
            'top_rules': dict(sorted(opportunities['top_rules'].items(), key=lambda x: x[1], reverse=True)[:10])
        }
