"""
File: vibe_check/core/persistence/storage.py
Purpose: Storage operations for analysis results
Related Files:
    - vibe_check/core/persistence/database.py - Database management
    - vibe_check/core/persistence/models.py - Data models
Dependencies: json, logging, datetime

Handles saving and retrieving analysis results from SQLite database.
"""

import json
import logging
from datetime import datetime
from typing import List, Optional, Dict, Any
from .database import DatabaseManager
from .models import AnalysisRun, FileResult, Issue, AnalysisMode, IssueSeverity

logger = logging.getLogger(__name__)


class ResultStorage:
    """Handles storage and retrieval of analysis results."""
    
    def __init__(self, db_manager: Optional[DatabaseManager] = None):
        """Initialize result storage.
        
        Args:
            db_manager: Database manager instance. If None, creates default.
        """
        self.db = db_manager or DatabaseManager()
    
    def save_analysis_run(self, analysis_run: AnalysisRun) -> int:
        """Save an analysis run to the database.
        
        Args:
            analysis_run: The analysis run to save
            
        Returns:
            The run_id of the saved analysis run
        """
        # Calculate final metrics
        analysis_run.calculate_metrics()
        
        # Insert analysis run
        run_query = """
            INSERT INTO analysis_runs (
                project_path, analysis_mode, profile, start_time, end_time,
                analysis_duration, total_files, total_lines, total_issues,
                average_complexity, overall_type_coverage, meritocracy_score,
                tools_used, success, error_message, config_used
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        run_params = (
            analysis_run.project_path,
            analysis_run.analysis_mode.value,
            analysis_run.profile,
            analysis_run.start_time.isoformat(),
            analysis_run.end_time.isoformat() if analysis_run.end_time else None,
            analysis_run.analysis_duration,
            analysis_run.total_files,
            analysis_run.total_lines,
            analysis_run.total_issues,
            analysis_run.average_complexity,
            analysis_run.overall_type_coverage,
            analysis_run.meritocracy_score,
            json.dumps(analysis_run.tools_used),
            analysis_run.success,
            analysis_run.error_message,
            json.dumps(analysis_run.config_used)
        )
        
        run_id = self.db.execute_insert(run_query, run_params)
        analysis_run.run_id = run_id
        
        # Save file results
        for file_result in analysis_run.file_results:
            self._save_file_result(file_result, run_id)
        
        logger.info(f"Saved analysis run {run_id} with {len(analysis_run.file_results)} files")
        return run_id
    
    def _save_file_result(self, file_result: FileResult, run_id: int) -> int:
        """Save a file result to the database.
        
        Args:
            file_result: The file result to save
            run_id: The analysis run ID this file belongs to
            
        Returns:
            The file_result_id of the saved file result
        """
        file_query = """
            INSERT INTO file_results (
                analysis_run_id, file_path, relative_path, line_count,
                complexity_score, type_coverage, analysis_time, tools_used
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        file_params = (
            run_id,
            file_result.file_path,
            file_result.relative_path,
            file_result.line_count,
            file_result.complexity_score,
            file_result.type_coverage,
            file_result.analysis_time,
            json.dumps(file_result.tools_used)
        )
        
        file_result_id = self.db.execute_insert(file_query, file_params)
        file_result.file_result_id = file_result_id
        file_result.analysis_run_id = run_id
        
        # Save issues for this file
        for issue in file_result.issues:
            self._save_issue(issue, file_result_id)
        
        return file_result_id
    
    def _save_issue(self, issue: Issue, file_result_id: int) -> int:
        """Save an issue to the database.
        
        Args:
            issue: The issue to save
            file_result_id: The file result ID this issue belongs to
            
        Returns:
            The issue_id of the saved issue
        """
        issue_query = """
            INSERT INTO issues (
                file_result_id, rule_id, message, severity, line_number,
                column_number, tool, category, suggestion, more_info, confidence
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        issue_params = (
            file_result_id,
            issue.rule_id,
            issue.message,
            issue.severity.value,
            issue.line_number,
            issue.column_number,
            issue.tool,
            issue.category,
            issue.suggestion,
            issue.more_info,
            issue.confidence
        )
        
        issue_id = self.db.execute_insert(issue_query, issue_params)
        issue.issue_id = issue_id
        issue.file_result_id = file_result_id

        return issue_id

    def get_analysis_run(self, run_id: int) -> Optional[AnalysisRun]:
        """Retrieve an analysis run by ID.

        Args:
            run_id: The analysis run ID

        Returns:
            The analysis run or None if not found
        """
        run_query = """
            SELECT * FROM analysis_runs WHERE run_id = ?
        """

        rows = self.db.execute_query(run_query, (run_id,))
        if not rows:
            return None

        row = rows[0]

        # Create analysis run object
        analysis_run = AnalysisRun(
            project_path=row['project_path'],
            analysis_mode=AnalysisMode(row['analysis_mode']),
            profile=row['profile'],
            start_time=datetime.fromisoformat(row['start_time']),
            end_time=datetime.fromisoformat(row['end_time']) if row['end_time'] else None,
            analysis_duration=row['analysis_duration'],
            total_files=row['total_files'],
            total_lines=row['total_lines'],
            total_issues=row['total_issues'],
            average_complexity=row['average_complexity'],
            overall_type_coverage=row['overall_type_coverage'],
            meritocracy_score=row['meritocracy_score'],
            tools_used=json.loads(row['tools_used']) if row['tools_used'] else [],
            success=bool(row['success']),
            error_message=row['error_message'],
            config_used=json.loads(row['config_used']) if row['config_used'] else {},
            run_id=row['run_id']
        )

        # Load file results
        analysis_run.file_results = self._get_file_results(run_id)

        return analysis_run

    def _get_file_results(self, run_id: int) -> List[FileResult]:
        """Get file results for an analysis run.

        Args:
            run_id: The analysis run ID

        Returns:
            List of file results
        """
        file_query = """
            SELECT * FROM file_results WHERE analysis_run_id = ?
            ORDER BY relative_path
        """

        rows = self.db.execute_query(file_query, (run_id,))
        file_results = []

        for row in rows:
            file_result = FileResult(
                file_path=row['file_path'],
                relative_path=row['relative_path'],
                line_count=row['line_count'],
                complexity_score=row['complexity_score'],
                type_coverage=row['type_coverage'],
                analysis_time=row['analysis_time'],
                tools_used=json.loads(row['tools_used']) if row['tools_used'] else [],
                file_result_id=row['file_result_id'],
                analysis_run_id=row['analysis_run_id']
            )

            # Load issues for this file
            file_result.issues = self._get_issues(row['file_result_id'])
            file_results.append(file_result)

        return file_results

    def _get_issues(self, file_result_id: int) -> List[Issue]:
        """Get issues for a file result.

        Args:
            file_result_id: The file result ID

        Returns:
            List of issues
        """
        issue_query = """
            SELECT * FROM issues WHERE file_result_id = ?
            ORDER BY line_number, column_number
        """

        rows = self.db.execute_query(issue_query, (file_result_id,))
        issues = []

        for row in rows:
            issue = Issue(
                rule_id=row['rule_id'],
                message=row['message'],
                severity=IssueSeverity(row['severity']),
                file_path="",  # Will be set by file result
                line_number=row['line_number'],
                column_number=row['column_number'],
                tool=row['tool'],
                category=row['category'],
                suggestion=row['suggestion'],
                more_info=row['more_info'],
                confidence=row['confidence'],
                issue_id=row['issue_id'],
                file_result_id=row['file_result_id']
            )
            issues.append(issue)

        return issues

    def get_recent_runs(self, limit: int = 10) -> List[AnalysisRun]:
        """Get recent analysis runs.

        Args:
            limit: Maximum number of runs to return

        Returns:
            List of recent analysis runs (without file details)
        """
        run_query = """
            SELECT * FROM analysis_runs
            ORDER BY start_time DESC
            LIMIT ?
        """

        rows = self.db.execute_query(run_query, (limit,))
        runs = []

        for row in rows:
            analysis_run = AnalysisRun(
                project_path=row['project_path'],
                analysis_mode=AnalysisMode(row['analysis_mode']),
                profile=row['profile'],
                start_time=datetime.fromisoformat(row['start_time']),
                end_time=datetime.fromisoformat(row['end_time']) if row['end_time'] else None,
                analysis_duration=row['analysis_duration'],
                total_files=row['total_files'],
                total_lines=row['total_lines'],
                total_issues=row['total_issues'],
                average_complexity=row['average_complexity'],
                overall_type_coverage=row['overall_type_coverage'],
                meritocracy_score=row['meritocracy_score'],
                tools_used=json.loads(row['tools_used']) if row['tools_used'] else [],
                success=bool(row['success']),
                error_message=row['error_message'],
                config_used=json.loads(row['config_used']) if row['config_used'] else {},
                run_id=row['run_id']
            )
            runs.append(analysis_run)

        return runs

    def find_runs_by_project(self, project_path: str, limit: int = 20) -> List[AnalysisRun]:
        """Find analysis runs for a specific project.

        Args:
            project_path: The project path to search for
            limit: Maximum number of runs to return

        Returns:
            List of analysis runs for the project
        """
        run_query = """
            SELECT * FROM analysis_runs
            WHERE project_path = ?
            ORDER BY start_time DESC
            LIMIT ?
        """

        rows = self.db.execute_query(run_query, (project_path, limit))
        runs = []

        for row in rows:
            analysis_run = AnalysisRun(
                project_path=row['project_path'],
                analysis_mode=AnalysisMode(row['analysis_mode']),
                profile=row['profile'],
                start_time=datetime.fromisoformat(row['start_time']),
                end_time=datetime.fromisoformat(row['end_time']) if row['end_time'] else None,
                analysis_duration=row['analysis_duration'],
                total_files=row['total_files'],
                total_lines=row['total_lines'],
                total_issues=row['total_issues'],
                average_complexity=row['average_complexity'],
                overall_type_coverage=row['overall_type_coverage'],
                meritocracy_score=row['meritocracy_score'],
                tools_used=json.loads(row['tools_used']) if row['tools_used'] else [],
                success=bool(row['success']),
                error_message=row['error_message'],
                config_used=json.loads(row['config_used']) if row['config_used'] else {},
                run_id=row['run_id']
            )
            runs.append(analysis_run)

        return runs

    def find_comparison_pairs(self, project_path: str) -> List[Dict[str, Any]]:
        """Find VCS/Plugin analysis pairs for comparison.

        Args:
            project_path: The project path to search for

        Returns:
            List of dictionaries with 'vcs_run' and 'plugin_run' keys
        """
        # Get all runs for this project, grouped by mode
        vcs_runs = []
        plugin_runs = []

        all_runs = self.find_runs_by_project(project_path)

        for run in all_runs:
            if run.analysis_mode == AnalysisMode.VCS:
                vcs_runs.append(run)
            else:
                plugin_runs.append(run)

        # Find pairs based on proximity in time
        pairs = []
        for vcs_run in vcs_runs:
            # Find the closest plugin run in time
            closest_plugin = None
            min_time_diff = float('inf')

            for plugin_run in plugin_runs:
                time_diff = abs((vcs_run.start_time - plugin_run.start_time).total_seconds())
                if time_diff < min_time_diff:
                    min_time_diff = time_diff
                    closest_plugin = plugin_run

            if closest_plugin and min_time_diff < 3600:  # Within 1 hour
                pairs.append({
                    'vcs_run': vcs_run,
                    'plugin_run': closest_plugin,
                    'time_diff_seconds': min_time_diff
                })

        return pairs
