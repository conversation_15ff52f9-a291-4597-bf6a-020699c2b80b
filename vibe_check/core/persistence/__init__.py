"""
File: vibe_check/core/persistence/__init__.py
Purpose: Data persistence module for storing and retrieving analysis results
Related Files: 
    - vibe_check/core/persistence/database.py - Database connection and schema
    - vibe_check/core/persistence/models.py - Data models for analysis results
    - vibe_check/core/persistence/storage.py - Result storage and retrieval
Dependencies: sqlite3, dataclasses, typing

This module provides SQLite-based persistence for analysis results, enabling
detailed comparison between VCS mode and plugin mode runs.
"""

from .database import DatabaseManager
from .models import AnalysisRun, FileResult, Issue
from .storage import ResultStorage

__all__ = [
    'DatabaseManager',
    'AnalysisRun', 
    'FileResult',
    'Issue',
    'ResultStorage'
]
