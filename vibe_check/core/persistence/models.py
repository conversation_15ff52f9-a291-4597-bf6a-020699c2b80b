"""
File: vibe_check/core/persistence/models.py
Purpose: Data models for analysis result persistence
Related Files:
    - vibe_check/core/persistence/database.py - Database schema implementation
    - vibe_check/core/persistence/storage.py - Storage operations
Dependencies: dataclasses, typing, datetime, enum

Defines the data structures for storing analysis results in SQLite database.
"""

from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any
import json


class AnalysisMode(Enum):
    """Analysis mode enumeration."""
    PLUGIN = "plugin"
    VCS = "vcs"


class IssueSeverity(Enum):
    """Issue severity levels."""
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"
    HINT = "hint"
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"


@dataclass
class Issue:
    """Represents a single code issue found during analysis."""
    
    # Core issue identification
    rule_id: str
    message: str
    severity: IssueSeverity
    
    # Location information
    file_path: str
    line_number: int
    column_number: int = 0
    
    # Tool and category information
    tool: str = ""
    category: str = ""
    
    # Additional metadata
    suggestion: str = ""
    more_info: str = ""
    confidence: str = ""
    
    # Internal tracking
    issue_id: Optional[int] = None
    file_result_id: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert issue to dictionary for JSON serialization."""
        return {
            'rule_id': self.rule_id,
            'message': self.message,
            'severity': self.severity.value,
            'file_path': self.file_path,
            'line_number': self.line_number,
            'column_number': self.column_number,
            'tool': self.tool,
            'category': self.category,
            'suggestion': self.suggestion,
            'more_info': self.more_info,
            'confidence': self.confidence
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Issue':
        """Create issue from dictionary."""
        return cls(
            rule_id=data.get('rule_id', ''),
            message=data.get('message', ''),
            severity=IssueSeverity(data.get('severity', 'info')),
            file_path=data.get('file_path', ''),
            line_number=data.get('line_number', 0),
            column_number=data.get('column_number', 0),
            tool=data.get('tool', ''),
            category=data.get('category', ''),
            suggestion=data.get('suggestion', ''),
            more_info=data.get('more_info', ''),
            confidence=data.get('confidence', '')
        )


@dataclass
class FileResult:
    """Represents analysis results for a single file."""
    
    # File identification
    file_path: str
    relative_path: str
    
    # File metrics
    line_count: int = 0
    complexity_score: float = 0.0
    type_coverage: float = 0.0
    
    # Issues found in this file
    issues: List[Issue] = field(default_factory=list)
    
    # Analysis metadata
    analysis_time: float = 0.0
    tools_used: List[str] = field(default_factory=list)
    
    # Internal tracking
    file_result_id: Optional[int] = None
    analysis_run_id: Optional[int] = None
    
    def add_issue(self, issue: Issue) -> None:
        """Add an issue to this file result."""
        issue.file_path = self.file_path
        self.issues.append(issue)
    
    def get_issues_by_severity(self, severity: IssueSeverity) -> List[Issue]:
        """Get all issues of a specific severity."""
        return [issue for issue in self.issues if issue.severity == severity]
    
    def get_issues_by_tool(self, tool: str) -> List[Issue]:
        """Get all issues found by a specific tool."""
        return [issue for issue in self.issues if issue.tool == tool]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert file result to dictionary for JSON serialization."""
        return {
            'file_path': self.file_path,
            'relative_path': self.relative_path,
            'line_count': self.line_count,
            'complexity_score': self.complexity_score,
            'type_coverage': self.type_coverage,
            'issues': [issue.to_dict() for issue in self.issues],
            'analysis_time': self.analysis_time,
            'tools_used': self.tools_used
        }


@dataclass
class AnalysisRun:
    """Represents a complete analysis run with all results."""
    
    # Run identification
    project_path: str
    analysis_mode: AnalysisMode
    profile: str
    
    # Timing information
    start_time: datetime
    end_time: Optional[datetime] = None
    analysis_duration: float = 0.0
    
    # Results
    file_results: List[FileResult] = field(default_factory=list)
    total_files: int = 0
    total_lines: int = 0
    total_issues: int = 0
    
    # Performance metrics
    average_complexity: float = 0.0
    overall_type_coverage: float = 0.0
    meritocracy_score: float = 0.0
    
    # Tool information
    tools_used: List[str] = field(default_factory=list)
    
    # Status and error information
    success: bool = True
    error_message: str = ""
    
    # Configuration
    config_used: Dict[str, Any] = field(default_factory=dict)
    
    # Internal tracking
    run_id: Optional[int] = None
    
    def add_file_result(self, file_result: FileResult) -> None:
        """Add a file result to this analysis run."""
        file_result.analysis_run_id = self.run_id
        self.file_results.append(file_result)
        self.total_files += 1
        self.total_lines += file_result.line_count
        self.total_issues += len(file_result.issues)
    
    def get_issues_by_severity(self, severity: IssueSeverity) -> List[Issue]:
        """Get all issues of a specific severity across all files."""
        issues = []
        for file_result in self.file_results:
            issues.extend(file_result.get_issues_by_severity(severity))
        return issues
    
    def get_severity_breakdown(self) -> Dict[str, int]:
        """Get count of issues by severity level."""
        breakdown = {}
        for file_result in self.file_results:
            for issue in file_result.issues:
                severity = issue.severity.value
                breakdown[severity] = breakdown.get(severity, 0) + 1
        return breakdown
    
    def calculate_metrics(self) -> None:
        """Calculate aggregate metrics from file results."""
        if not self.file_results:
            return
        
        # Calculate averages
        total_complexity = sum(fr.complexity_score for fr in self.file_results)
        total_type_coverage = sum(fr.type_coverage for fr in self.file_results)
        
        self.average_complexity = total_complexity / len(self.file_results)
        self.overall_type_coverage = total_type_coverage / len(self.file_results)
        
        # Calculate meritocracy score (simplified)
        if self.total_lines > 0:
            issue_density = self.total_issues / self.total_lines
            self.meritocracy_score = max(0, 10 - (issue_density * 100))
        
        # Collect all tools used
        all_tools = set()
        for file_result in self.file_results:
            all_tools.update(file_result.tools_used)
        self.tools_used = list(all_tools)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert analysis run to dictionary for JSON serialization."""
        return {
            'project_path': self.project_path,
            'analysis_mode': self.analysis_mode.value,
            'profile': self.profile,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'analysis_duration': self.analysis_duration,
            'file_results': [fr.to_dict() for fr in self.file_results],
            'total_files': self.total_files,
            'total_lines': self.total_lines,
            'total_issues': self.total_issues,
            'average_complexity': self.average_complexity,
            'overall_type_coverage': self.overall_type_coverage,
            'meritocracy_score': self.meritocracy_score,
            'tools_used': self.tools_used,
            'success': self.success,
            'error_message': self.error_message,
            'config_used': self.config_used
        }
