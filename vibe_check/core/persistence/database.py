"""
File: vibe_check/core/persistence/database.py
Purpose: SQLite database management for analysis result persistence
Related Files:
    - vibe_check/core/persistence/models.py - Data models
    - vibe_check/core/persistence/storage.py - Storage operations
Dependencies: sqlite3, pathlib, logging

Manages SQLite database connection, schema creation, and basic operations.
"""

import sqlite3
import logging
from pathlib import Path
from typing import Optional, Dict, Any
from contextlib import contextmanager

logger = logging.getLogger(__name__)


class DatabaseManager:
    """Manages SQLite database for analysis result persistence."""
    
    def __init__(self, db_path: Optional[Path] = None):
        """Initialize database manager.
        
        Args:
            db_path: Path to SQLite database file. If None, uses default location.
        """
        if db_path is None:
            # Default to user's home directory
            home_dir = Path.home()
            vibe_check_dir = home_dir / ".vibe_check"
            vibe_check_dir.mkdir(exist_ok=True)
            db_path = vibe_check_dir / "analysis_results.db"
        
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Initialize database schema
        self._initialize_schema()
    
    def _initialize_schema(self) -> None:
        """Create database tables if they don't exist."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Analysis runs table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS analysis_runs (
                    run_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    project_path TEXT NOT NULL,
                    analysis_mode TEXT NOT NULL,
                    profile TEXT NOT NULL,
                    start_time TEXT NOT NULL,
                    end_time TEXT,
                    analysis_duration REAL DEFAULT 0.0,
                    total_files INTEGER DEFAULT 0,
                    total_lines INTEGER DEFAULT 0,
                    total_issues INTEGER DEFAULT 0,
                    average_complexity REAL DEFAULT 0.0,
                    overall_type_coverage REAL DEFAULT 0.0,
                    meritocracy_score REAL DEFAULT 0.0,
                    tools_used TEXT DEFAULT '',
                    success BOOLEAN DEFAULT 1,
                    error_message TEXT DEFAULT '',
                    config_used TEXT DEFAULT '{}',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # File results table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS file_results (
                    file_result_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    analysis_run_id INTEGER NOT NULL,
                    file_path TEXT NOT NULL,
                    relative_path TEXT NOT NULL,
                    line_count INTEGER DEFAULT 0,
                    complexity_score REAL DEFAULT 0.0,
                    type_coverage REAL DEFAULT 0.0,
                    analysis_time REAL DEFAULT 0.0,
                    tools_used TEXT DEFAULT '',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (analysis_run_id) REFERENCES analysis_runs (run_id)
                        ON DELETE CASCADE
                )
            """)
            
            # Issues table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS issues (
                    issue_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    file_result_id INTEGER NOT NULL,
                    rule_id TEXT NOT NULL,
                    message TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    line_number INTEGER NOT NULL,
                    column_number INTEGER DEFAULT 0,
                    tool TEXT DEFAULT '',
                    category TEXT DEFAULT '',
                    suggestion TEXT DEFAULT '',
                    more_info TEXT DEFAULT '',
                    confidence TEXT DEFAULT '',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (file_result_id) REFERENCES file_results (file_result_id)
                        ON DELETE CASCADE
                )
            """)
            
            # Create indexes for better query performance
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_analysis_runs_mode_time 
                ON analysis_runs (analysis_mode, start_time)
            """)
            
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_file_results_run_id 
                ON file_results (analysis_run_id)
            """)
            
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_issues_file_result_id 
                ON issues (file_result_id)
            """)
            
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_issues_severity_tool 
                ON issues (severity, tool)
            """)
            
            conn.commit()
            logger.info(f"Database schema initialized at {self.db_path}")
    
    @contextmanager
    def get_connection(self):
        """Get database connection with automatic cleanup."""
        conn = None
        try:
            conn = sqlite3.connect(str(self.db_path))
            conn.row_factory = sqlite3.Row  # Enable column access by name
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Database error: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def execute_query(self, query: str, params: tuple = ()) -> list:
        """Execute a SELECT query and return results."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            return cursor.fetchall()
    
    def execute_update(self, query: str, params: tuple = ()) -> int:
        """Execute an INSERT/UPDATE/DELETE query and return affected rows."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            conn.commit()
            return cursor.rowcount
    
    def execute_insert(self, query: str, params: tuple = ()) -> int:
        """Execute an INSERT query and return the new row ID."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            conn.commit()
            return cursor.lastrowid
    
    def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics."""
        stats = {}
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Count records in each table
            cursor.execute("SELECT COUNT(*) FROM analysis_runs")
            stats['total_runs'] = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM file_results")
            stats['total_file_results'] = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM issues")
            stats['total_issues'] = cursor.fetchone()[0]
            
            # Get mode breakdown
            cursor.execute("""
                SELECT analysis_mode, COUNT(*) 
                FROM analysis_runs 
                GROUP BY analysis_mode
            """)
            stats['runs_by_mode'] = dict(cursor.fetchall())
            
            # Get recent runs
            cursor.execute("""
                SELECT run_id, project_path, analysis_mode, start_time, success
                FROM analysis_runs 
                ORDER BY start_time DESC 
                LIMIT 5
            """)
            stats['recent_runs'] = [dict(row) for row in cursor.fetchall()]
            
            # Database file size
            stats['db_size_bytes'] = self.db_path.stat().st_size
            stats['db_path'] = str(self.db_path)
        
        return stats
    
    def cleanup_old_runs(self, keep_days: int = 30) -> int:
        """Remove analysis runs older than specified days.
        
        Args:
            keep_days: Number of days to keep (default: 30)
            
        Returns:
            Number of runs deleted
        """
        query = """
            DELETE FROM analysis_runs 
            WHERE start_time < datetime('now', '-{} days')
        """.format(keep_days)
        
        deleted_count = self.execute_update(query)
        logger.info(f"Cleaned up {deleted_count} old analysis runs")
        return deleted_count
    
    def vacuum_database(self) -> None:
        """Optimize database by reclaiming space."""
        with self.get_connection() as conn:
            conn.execute("VACUUM")
            logger.info("Database vacuumed successfully")
