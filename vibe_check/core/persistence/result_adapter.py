"""
File: vibe_check/core/persistence/result_adapter.py
Purpose: Adapter for converting analysis results to persistence models
Related Files:
    - vibe_check/core/persistence/models.py - Data models
    - vibe_check/cli/commands.py - Analysis commands
Dependencies: datetime, pathlib, logging

Converts analysis results from different engines to standardized persistence models.
"""

import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

from .models import AnalysisRun, FileResult, Issue, AnalysisMode, IssueSeverity

logger = logging.getLogger(__name__)


class ResultAdapter:
    """Adapts analysis results to persistence models."""
    
    @staticmethod
    def convert_analysis_results(
        results: Any,  # Can be Dict or ProjectMetrics object
        project_path: str,
        profile: str,
        start_time: datetime,
        end_time: Optional[datetime] = None
    ) -> AnalysisRun:
        """Convert analysis results to AnalysisRun model.

        Args:
            results: Raw analysis results from analyze_command (Dict or ProjectMetrics)
            project_path: Path to the analyzed project
            profile: Analysis profile used
            start_time: When analysis started
            end_time: When analysis ended (if None, uses current time)

        Returns:
            AnalysisRun model with converted data
        """
        if end_time is None:
            end_time = datetime.now()

        # Convert results to dictionary if it's a ProjectMetrics object
        if not isinstance(results, dict):
            try:
                if hasattr(results, 'to_dict') and callable(getattr(results, 'to_dict')):
                    results = results.to_dict()
                elif hasattr(results, 'files') and isinstance(getattr(results, 'files', None), dict):
                    # This is a ProjectMetrics object - convert to expected format
                    logger.info(f"Converting ProjectMetrics object with {len(results.files)} files")
                    files_list = []
                    for file_path, file_metrics in results.files.items():
                        logger.debug(f"Converting file: {file_path} with {len(getattr(file_metrics, 'issues', []))} issues")
                        file_dict = {
                            'file_path': file_path,
                            'path': file_path,
                            'line_count': getattr(file_metrics, 'lines', 0),
                            'lines': getattr(file_metrics, 'lines', 0),
                            'complexity': getattr(file_metrics, 'complexity', 0.0),
                            'complexity_score': getattr(file_metrics, 'complexity', 0.0),
                            'type_coverage': getattr(file_metrics, 'type_coverage', 0.0),
                            'analysis_time': 0.0,  # Not available in ProjectMetrics
                            'tools_used': ['ruff', 'mypy', 'bandit', 'complexity'],
                            'issues': getattr(file_metrics, 'issues', [])
                        }
                        files_list.append(file_dict)

                    logger.info(f"Converted {len(files_list)} files for persistence")
                    results = {
                        'total_files_analyzed': getattr(results, 'total_file_count', 0),
                        'total_lines': getattr(results, 'total_line_count', 0),
                        'total_issues_found': getattr(results, 'issue_count', 0),
                        'average_complexity': getattr(results, 'avg_complexity', 0.0),
                        'meritocracy_score': 0.0,  # Not available in ProjectMetrics
                        'files': files_list,
                        'vcs_mode': False  # Plugin mode
                    }
                elif hasattr(results, '__dict__'):
                    results = results.__dict__
                else:
                    # Fallback: create a basic dict structure
                    results = {
                        'total_files_analyzed': getattr(results, 'total_files', 0),
                        'total_lines': getattr(results, 'total_lines', 0),
                        'total_issues_found': getattr(results, 'total_issues', 0),
                        'average_complexity': getattr(results, 'average_complexity', 0.0),
                        'meritocracy_score': getattr(results, 'meritocracy_score', 0.0),
                        'files': getattr(results, 'files', {}),
                        'vcs_mode': False  # Default assumption
                    }
            except Exception as e:
                logger.warning(f"Failed to convert results object to dict: {e}")
                results = {'error': f'Failed to convert results: {e}'}

        # Determine analysis mode
        vcs_mode = results.get('vcs_mode', False)
        analysis_mode = AnalysisMode.VCS if vcs_mode else AnalysisMode.PLUGIN
        
        # Calculate duration
        duration = (end_time - start_time).total_seconds()
        
        # Check if analysis was successful
        success = 'error' not in results
        error_message = results.get('error', '') if not success else ''
        
        # Create analysis run
        analysis_run = AnalysisRun(
            project_path=project_path,
            analysis_mode=analysis_mode,
            profile=profile,
            start_time=start_time,
            end_time=end_time,
            analysis_duration=duration,
            success=success,
            error_message=error_message
        )
        
        # If analysis failed, return early
        if not success:
            return analysis_run
        
        # Extract metrics
        analysis_run.total_files = results.get('total_files_analyzed', 0)
        analysis_run.total_lines = results.get('total_lines', 0)
        analysis_run.total_issues = results.get('total_issues_found', 0)
        analysis_run.average_complexity = results.get('average_complexity', 0.0)
        analysis_run.overall_type_coverage = results.get('type_coverage', 0.0)
        analysis_run.meritocracy_score = results.get('meritocracy_score', 0.0)
        
        # Extract tools used
        if vcs_mode:
            analysis_run.tools_used = ['vcs_engine']
        else:
            # For plugin mode, extract tools from results
            tools = set()
            if 'files' in results:
                for file_data in results['files']:
                    if isinstance(file_data, dict) and 'tools_used' in file_data:
                        tools.update(file_data['tools_used'])
            analysis_run.tools_used = list(tools) if tools else ['ruff', 'mypy', 'bandit', 'complexity']
        
        # Convert file results - handle both 'files' and 'file_metrics' keys
        files_data = results.get('files', results.get('file_metrics', {}))
        if files_data:
            file_results = ResultAdapter._convert_file_results(
                files_data, project_path
            )
            # Add each file result to properly update counters
            for file_result in file_results:
                analysis_run.add_file_result(file_result)
        
        return analysis_run
    
    @staticmethod
    def _convert_file_results(files_data: Any, project_path: str) -> List[FileResult]:
        """Convert file results data to FileResult models."""
        file_results = []
        project_root = Path(project_path)
        
        if isinstance(files_data, dict):
            # Handle dictionary format (file_path -> file_data)
            for file_path, file_data in files_data.items():
                file_result = ResultAdapter._convert_single_file_result(
                    file_path, file_data, project_root
                )
                if file_result:
                    file_results.append(file_result)
        
        elif isinstance(files_data, list):
            # Handle list format (VCS mode and plugin mode)
            for file_data in files_data:
                if isinstance(file_data, dict):
                    # VCS mode uses 'file', plugin mode uses 'file_path' or 'path'
                    file_path = file_data.get('file_path', file_data.get('path', file_data.get('file', '')))
                    file_result = ResultAdapter._convert_single_file_result(
                        file_path, file_data, project_root
                    )
                    if file_result:
                        file_results.append(file_result)
        
        return file_results
    
    @staticmethod
    def _convert_single_file_result(
        file_path: str, 
        file_data: Dict[str, Any], 
        project_root: Path
    ) -> Optional[FileResult]:
        """Convert a single file's results to FileResult model."""
        if not file_path:
            return None
        
        try:
            # Calculate relative path
            abs_path = Path(file_path)
            if abs_path.is_absolute():
                try:
                    relative_path = str(abs_path.relative_to(project_root))
                except ValueError:
                    relative_path = abs_path.name
            else:
                relative_path = file_path
            
            # Create file result - handle both VCS mode and plugin mode field names
            file_result = FileResult(
                file_path=str(abs_path),
                relative_path=relative_path,
                line_count=file_data.get('line_count', file_data.get('lines', 0)),  # VCS mode uses 'lines'
                complexity_score=file_data.get('complexity', file_data.get('complexity_score', 0.0)),
                type_coverage=file_data.get('type_coverage', 0.0),
                analysis_time=file_data.get('analysis_time', 0.0),
                tools_used=file_data.get('tools_used', ['vcs_engine'] if file_data.get('success') else [])
            )
            
            # Convert issues - handle both VCS mode and plugin mode
            # VCS mode stores issues count in 'issues' and detailed issues in 'issues_detail'
            # Plugin mode stores issues list directly in 'issues'
            issues_data = file_data.get('issues_detail', [])
            if not issues_data:
                # Fallback to 'issues' if it's a list (plugin mode)
                issues_field = file_data.get('issues', [])
                if isinstance(issues_field, list):
                    issues_data = issues_field

            if issues_data and isinstance(issues_data, list):
                file_result.issues = ResultAdapter._convert_issues(
                    issues_data, file_path
                )

            
            return file_result
            
        except Exception as e:
            logger.warning(f"Failed to convert file result for {file_path}: {e}")
            return None
    
    @staticmethod
    def _convert_issues(issues_data: List[Dict[str, Any]], file_path: str) -> List[Issue]:
        """Convert issues data to Issue models."""
        issues = []
        
        for issue_data in issues_data:
            try:
                issue = ResultAdapter._convert_single_issue(issue_data, file_path)
                if issue:
                    issues.append(issue)
            except Exception as e:
                logger.warning(f"Failed to convert issue in {file_path}: {e}")
                continue
        
        return issues
    
    @staticmethod
    def _convert_single_issue(issue_data: Dict[str, Any], file_path: str) -> Optional[Issue]:
        """Convert a single issue to Issue model."""
        try:
            # Extract rule ID
            rule_id = (
                issue_data.get('rule_id') or 
                issue_data.get('code') or 
                issue_data.get('rule') or 
                'unknown'
            )
            
            # Extract message
            message = issue_data.get('message', 'No message provided')
            
            # Convert severity
            severity_str = issue_data.get('severity', 'info').lower()
            try:
                severity = IssueSeverity(severity_str)
            except ValueError:
                # Map common severity variations
                severity_map = {
                    'low': IssueSeverity.LOW,
                    'medium': IssueSeverity.MEDIUM,
                    'high': IssueSeverity.HIGH,
                    'warn': IssueSeverity.WARNING,
                    'err': IssueSeverity.ERROR
                }
                severity = severity_map.get(severity_str, IssueSeverity.INFO)
            
            # Extract location
            line_number = issue_data.get('line', issue_data.get('line_number', 0))
            column_number = issue_data.get('column', issue_data.get('column_number', 0))
            
            # Extract tool and category
            tool = issue_data.get('tool', issue_data.get('file', ''))
            category = issue_data.get('category', issue_data.get('type', ''))
            
            # Create issue
            issue = Issue(
                rule_id=rule_id,
                message=message,
                severity=severity,
                file_path=file_path,
                line_number=line_number,
                column_number=column_number,
                tool=tool,
                category=category,
                suggestion=issue_data.get('suggestion', ''),
                more_info=issue_data.get('more_info', ''),
                confidence=issue_data.get('confidence', '')
            )
            
            return issue
            
        except Exception as e:
            logger.warning(f"Failed to convert issue: {e}")
            return None
    
    @staticmethod
    def extract_sample_issues_from_results(results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract sample issues from analysis results for debugging."""
        sample_issues = []
        
        # Check for sample issues in project metrics
        if 'sample_issues' in results:
            sample_issues.extend(results['sample_issues'])
        
        # Extract from files data
        if 'files' in results:
            files_data = results['files']
            
            if isinstance(files_data, dict):
                for file_data in files_data.values():
                    if isinstance(file_data, dict) and 'issues' in file_data:
                        sample_issues.extend(file_data['issues'][:3])  # First 3 issues per file
            
            elif isinstance(files_data, list):
                for file_data in files_data:
                    if isinstance(file_data, dict) and 'issues' in file_data:
                        sample_issues.extend(file_data['issues'][:3])  # First 3 issues per file
        
        return sample_issues[:10]  # Return first 10 sample issues
