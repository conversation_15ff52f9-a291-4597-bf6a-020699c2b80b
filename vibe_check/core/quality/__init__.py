"""
Quality Assurance Module
========================

This module provides comprehensive quality assurance tools and automated enforcement
systems for the Vibe Check project.

Components:
- Automated enforcement system for pre-commit hooks
- Quality validation and compliance checking
- Standards enforcement and regression prevention

Strategic Benefits:
- Maintains code quality standards automatically
- Prevents regression of systematic improvements
- Provides immediate feedback to developers
- Ensures consistency across development team
"""

from .automated_enforcement import (
    AutomatedEnforcementEngine,
    QualityViolation,
    ViolationType,
    ConstantsValidator,
    TerminologyValidator,
    ConfigSchemaValidator
)

__all__ = [
    "AutomatedEnforcementEngine",
    "QualityViolation", 
    "ViolationType",
    "ConstantsValidator",
    "TerminologyValidator",
    "ConfigSchemaValidator"
]
