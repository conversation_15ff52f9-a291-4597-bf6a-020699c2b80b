"""
Quality Assurance Module
========================

This module provides comprehensive quality assurance tools and automated enforcement
systems for the Vibe Check project.

Components:
- Modular validator architecture with auto-discovery
- Automated enforcement system for pre-commit hooks
- Quality validation and compliance checking
- Standards enforcement and regression prevention

Strategic Benefits:
- Maintains code quality standards automatically
- Prevents regression of systematic improvements
- Provides immediate feedback to developers
- Ensures consistency across development team
- Supports extensible validator architecture
"""

# Import from new modular validators
from .validators import (
    BaseValidator,
    QualityViolation,
    ViolationType,
    ConstantsValidator,
    TerminologyValidator,
    ConfigSchemaValidator,
    ValidatorRegistry,
    get_registry,
    register_validator,
    get_validator,
    get_all_validators,
    discover_validators,

    # Quality framework
    ValidatorQualityFramework,
    QualityMetrics,
    ValidationResult,
    TestCase,
    TestCaseManager,
    QualityGates,
    PerformanceMonitor,
    QualityMetricType
)

# Import legacy automated enforcement for backward compatibility
from .automated_enforcement import AutomatedEnforcementEngine

__all__ = [
    # Core validator architecture
    "BaseValidator",
    "QualityViolation",
    "ViolationType",

    # Concrete validators
    "ConstantsValidator",
    "TerminologyValidator",
    "ConfigSchemaValidator",

    # Registry system
    "ValidatorRegistry",
    "get_registry",
    "register_validator",
    "get_validator",
    "get_all_validators",
    "discover_validators",

    # Quality framework
    "ValidatorQualityFramework",
    "QualityMetrics",
    "ValidationResult",
    "TestCase",
    "TestCaseManager",
    "QualityGates",
    "PerformanceMonitor",
    "QualityMetricType",

    # Legacy compatibility
    "AutomatedEnforcementEngine"
]
