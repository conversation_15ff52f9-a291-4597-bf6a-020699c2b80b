"""
Automated Enforcement System
============================

This module provides comprehensive automated enforcement of code quality standards
through pre-commit hooks and validation systems.

Features:
- Constants usage validation
- Terminology compliance checking
- Configuration schema validation
- Import statement validation
- Code style enforcement
- Documentation standards checking

Strategic Benefits:
- Prevents regression of systematic improvements
- Ensures consistency across development team
- Automates quality gate enforcement
- Provides immediate feedback to developers
"""

import ast
import re
import json
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from vibe_check.core.constants import (
    ToolNames, FileExtensions, ErrorMessages, AnalysisThresholds
)
from vibe_check.core.constants.terminology import (
    AnalysisTerms, ConfigurationTerms, ComponentTerms
)


class ViolationType(Enum):
    """Types of quality violations."""
    CONSTANTS_USAGE = "constants_usage"
    TERMINOLOGY = "terminology"
    CONFIG_SCHEMA = "config_schema"
    IMPORT_STYLE = "import_style"
    DOCUMENTATION = "documentation"
    CODE_STYLE = "code_style"


@dataclass
class QualityViolation:
    """Represents a quality violation found during enforcement."""
    file_path: str
    line_number: int
    violation_type: ViolationType
    message: str
    suggestion: Optional[str] = None
    severity: str = "warning"


class ConstantsValidator:
    """Validates proper usage of centralized constants."""
    
    def __init__(self):
        self.hardcoded_patterns = {
            # Tool names
            r'"ruff"': f'Use ToolNames.RUFF instead of hardcoded "ruff"',
            r'"mypy"': f'Use ToolNames.MYPY instead of hardcoded "mypy"',
            r'"bandit"': f'Use ToolNames.BANDIT instead of hardcoded "bandit"',
            r'"pylint"': f'Use ToolNames.PYLINT instead of hardcoded "pylint"',
            r'"black"': f'Use ToolNames.BLACK instead of hardcoded "black"',
            r'"isort"': f'Use ToolNames.ISORT instead of hardcoded "isort"',
            
            # File extensions
            r'\.py"': f'Use FileExtensions.PYTHON instead of hardcoded ".py"',
            r'\.yaml"': f'Use FileExtensions.YAML instead of hardcoded ".yaml"',
            r'\.json"': f'Use FileExtensions.JSON instead of hardcoded ".json"',
            
            # Common thresholds
            r'\b10\b.*complexity': f'Use AnalysisThresholds.CYCLOMATIC_COMPLEXITY',
            r'\b88\b.*line.*length': f'Use AnalysisThresholds.MAX_LINE_LENGTH',
            r'\b50\b.*function.*length': f'Use AnalysisThresholds.MAX_FUNCTION_LENGTH',
        }
        
        self.allowed_files = {
            'constants', 'test_', 'conftest.py', '__init__.py'
        }
    
    def validate_file(self, file_path: Path) -> List[QualityViolation]:
        """Validate constants usage in a file."""
        violations = []
        
        # Skip files that are allowed to have hardcoded values
        if any(pattern in str(file_path).lower() for pattern in self.allowed_files):
            return violations
        
        try:
            content = file_path.read_text(encoding='utf-8')
            lines = content.split('\n')
            
            for line_num, line in enumerate(lines, 1):
                for pattern, message in self.hardcoded_patterns.items():
                    if re.search(pattern, line):
                        violations.append(QualityViolation(
                            file_path=str(file_path),
                            line_number=line_num,
                            violation_type=ViolationType.CONSTANTS_USAGE,
                            message=message,
                            suggestion=f"Import from vibe_check.core.constants and use the constant",
                            severity="warning"
                        ))
        
        except Exception as e:
            # Skip files that can't be read
            pass
        
        return violations


class TerminologyValidator:
    """Validates terminology consistency."""
    
    def __init__(self):
        self.deprecated_terms = {
            # British vs American spelling
            r'\banalyse\b': 'analyze',
            r'\banalyser\b': 'analyzer',
            r'\banalysing\b': 'analyzing',
            
            # Inconsistent terminology
            r'\bconfiguration\b': 'config',
            r'\bsettings\b': 'config',
            r'\boptions\b': 'config',
            
            # Component naming
            r'\banalysis_engine\b': 'analyzer',
            r'\bprocessing_engine\b': 'processor',
            
            # Result terminology
            r'\bfinding\b': 'issue',
            r'\bproblem\b': 'issue',
            r'\berror\b(?!.*handling|.*message)': 'issue',  # Avoid error handling contexts
        }
        
        self.context_exceptions = {
            'error': ['error_handling', 'error_message', 'exception', 'try:', 'except:'],
            'configuration': ['configuration.py', 'config_schema.py'],
        }
    
    def validate_file(self, file_path: Path) -> List[QualityViolation]:
        """Validate terminology in a file."""
        violations = []
        
        try:
            content = file_path.read_text(encoding='utf-8')
            lines = content.split('\n')
            
            for line_num, line in enumerate(lines, 1):
                line_lower = line.lower()
                
                for pattern, replacement in self.deprecated_terms.items():
                    matches = re.finditer(pattern, line, re.IGNORECASE)
                    
                    for match in matches:
                        # Check for context exceptions
                        term = match.group().lower()
                        if term in self.context_exceptions:
                            if any(exc in line_lower for exc in self.context_exceptions[term]):
                                continue
                        
                        violations.append(QualityViolation(
                            file_path=str(file_path),
                            line_number=line_num,
                            violation_type=ViolationType.TERMINOLOGY,
                            message=f'Use "{replacement}" instead of "{match.group()}"',
                            suggestion=f'Replace with standardized term from terminology guide',
                            severity="info"
                        ))
        
        except Exception:
            pass
        
        return violations


class ConfigSchemaValidator:
    """Validates configuration files against schemas."""
    
    def __init__(self):
        self.required_fields = {
            'vibe_check.yaml': ['analysis', 'output'],
            'pyproject.toml': ['tool.vibe_check'],
        }
    
    def validate_file(self, file_path: Path) -> List[QualityViolation]:
        """Validate configuration schema."""
        violations = []
        
        if file_path.suffix not in ['.yaml', '.yml', '.json', '.toml']:
            return violations
        
        try:
            if file_path.suffix in ['.yaml', '.yml']:
                with open(file_path, 'r') as f:
                    config = yaml.safe_load(f)
            elif file_path.suffix == '.json':
                with open(file_path, 'r') as f:
                    config = json.load(f)
            else:
                # Skip TOML for now (requires tomli)
                return violations
            
            # Validate required fields for known config files
            config_name = file_path.name
            if config_name in self.required_fields:
                for field in self.required_fields[config_name]:
                    if not self._has_nested_field(config, field):
                        violations.append(QualityViolation(
                            file_path=str(file_path),
                            line_number=1,
                            violation_type=ViolationType.CONFIG_SCHEMA,
                            message=f'Missing required field: {field}',
                            suggestion=f'Add required configuration field',
                            severity="error"
                        ))
        
        except Exception as e:
            violations.append(QualityViolation(
                file_path=str(file_path),
                line_number=1,
                violation_type=ViolationType.CONFIG_SCHEMA,
                message=f'Invalid configuration file: {str(e)}',
                suggestion='Fix configuration syntax',
                severity="error"
            ))
        
        return violations
    
    def _has_nested_field(self, config: Dict, field_path: str) -> bool:
        """Check if nested field exists in config."""
        parts = field_path.split('.')
        current = config
        
        for part in parts:
            if not isinstance(current, dict) or part not in current:
                return False
            current = current[part]
        
        return True


class AutomatedEnforcementEngine:
    """Main engine for automated quality enforcement."""
    
    def __init__(self):
        self.validators = {
            ViolationType.CONSTANTS_USAGE: ConstantsValidator(),
            ViolationType.TERMINOLOGY: TerminologyValidator(),
            ViolationType.CONFIG_SCHEMA: ConfigSchemaValidator(),
        }
    
    def validate_files(self, file_paths: List[Path]) -> List[QualityViolation]:
        """Validate multiple files and return all violations."""
        all_violations = []
        
        for file_path in file_paths:
            if not file_path.exists():
                continue
            
            # Run all applicable validators
            for violation_type, validator in self.validators.items():
                violations = validator.validate_file(file_path)
                all_violations.extend(violations)
        
        return all_violations
    
    def validate_project(self, project_path: Path) -> List[QualityViolation]:
        """Validate entire project."""
        python_files = list(project_path.rglob("*.py"))
        config_files = []
        
        # Find configuration files
        for pattern in ["*.yaml", "*.yml", "*.json", "*.toml"]:
            config_files.extend(project_path.rglob(pattern))
        
        all_files = python_files + config_files
        return self.validate_files(all_files)
    
    def generate_report(self, violations: List[QualityViolation]) -> str:
        """Generate a formatted report of violations."""
        if not violations:
            return "✅ No quality violations found!"
        
        report = ["🔍 AUTOMATED QUALITY ENFORCEMENT REPORT", "=" * 50, ""]
        
        # Group by violation type
        by_type = {}
        for violation in violations:
            if violation.violation_type not in by_type:
                by_type[violation.violation_type] = []
            by_type[violation.violation_type].append(violation)
        
        # Generate sections for each type
        for violation_type, type_violations in by_type.items():
            report.append(f"📋 {violation_type.value.upper()} VIOLATIONS ({len(type_violations)})")
            report.append("-" * 40)
            
            for violation in type_violations:
                severity_icon = "🔴" if violation.severity == "error" else "🟡" if violation.severity == "warning" else "🔵"
                report.append(f"{severity_icon} {violation.file_path}:{violation.line_number}")
                report.append(f"   {violation.message}")
                if violation.suggestion:
                    report.append(f"   💡 {violation.suggestion}")
                report.append("")
        
        # Summary
        error_count = sum(1 for v in violations if v.severity == "error")
        warning_count = sum(1 for v in violations if v.severity == "warning")
        info_count = sum(1 for v in violations if v.severity == "info")
        
        report.extend([
            "📊 SUMMARY",
            "-" * 20,
            f"🔴 Errors: {error_count}",
            f"🟡 Warnings: {warning_count}",
            f"🔵 Info: {info_count}",
            f"📁 Total: {len(violations)}",
        ])
        
        return "\n".join(report)
