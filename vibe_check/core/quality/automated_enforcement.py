"""
Automated Enforcement System
============================

This module provides comprehensive automated enforcement of code quality standards
through pre-commit hooks and validation systems.

Features:
- Constants usage validation
- Terminology compliance checking
- Configuration schema validation
- Import statement validation
- Code style enforcement
- Documentation standards checking

Strategic Benefits:
- Prevents regression of systematic improvements
- Ensures consistency across development team
- Automates quality gate enforcement
- Provides immediate feedback to developers
"""

import ast
import re
import json
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from vibe_check.core.constants import (
    ToolNames, FileExtensions, ErrorMessages, AnalysisThresholds
)
from vibe_check.core.constants.terminology import (
    AnalysisTerms, ConfigurationTerms, ComponentTerms
)

# Import from new modular validators
from .validators import (
    get_all_validators, QualityViolation, ViolationType
)


# ViolationType and QualityViolation are now imported from validators module
# Legacy validator classes are now imported from the modular validators

# Import the actual validator classes for backward compatibility
from .validators.constants_validator import ConstantsValidator as _ConstantsValidator
from .validators.terminology_validator import TerminologyValidator as _TerminologyValidator
from .validators.config_schema_validator import ConfigSchemaValidator as _ConfigSchemaValidator

# Create aliases for backward compatibility
ConstantsValidator = _ConstantsValidator
TerminologyValidator = _TerminologyValidator
ConfigSchemaValidator = _ConfigSchemaValidator


# Legacy validator classes removed - using modular validators from validators/ directory


# All legacy validator implementations removed


# All legacy validator implementations removed - using modular validators


class AutomatedEnforcementEngine:
    """Main engine for automated quality enforcement."""

    def __init__(self) -> None:
        """Initialize the automated enforcement engine with modular validators."""
        # Use the new modular validator system
        self.validators = get_all_validators()
    
    def validate_files(self, file_paths: List[Path]) -> List[QualityViolation]:
        """Validate multiple files and return all violations."""
        all_violations = []
        
        for file_path in file_paths:
            if not file_path.exists():
                continue
            
            # Run all applicable validators
            for violation_type, validator in self.validators.items():
                violations = validator.validate_file(file_path)
                all_violations.extend(violations)
        
        return all_violations
    
    def validate_project(self, project_path: Path) -> List[QualityViolation]:
        """Validate entire project."""
        python_files = list(project_path.rglob("*.py"))
        config_files: List[Path] = []

        # Find configuration files
        for pattern in ["*.yaml", "*.yml", "*.json", "*.toml"]:
            config_files.extend(project_path.rglob(pattern))

        all_files = python_files + config_files
        return self.validate_files(all_files)
    
    def generate_report(self, violations: List[QualityViolation]) -> str:
        """Generate a formatted report of violations."""
        if not violations:
            return "✅ No quality violations found!"
        
        report = ["🔍 AUTOMATED QUALITY ENFORCEMENT REPORT", "=" * 50, ""]

        # Group by violation type
        by_type: Dict[ViolationType, List[QualityViolation]] = {}
        for violation in violations:
            if violation.violation_type not in by_type:
                by_type[violation.violation_type] = []
            by_type[violation.violation_type].append(violation)
        
        # Generate sections for each type
        for violation_type, type_violations in by_type.items():
            report.append(f"📋 {violation_type.value.upper()} VIOLATIONS ({len(type_violations)})")
            report.append("-" * 40)
            
            for violation in type_violations:
                severity_icon = "🔴" if violation.severity == "error" else "🟡" if violation.severity == "warning" else "🔵"
                report.append(f"{severity_icon} {violation.file_path}:{violation.line_number}")
                report.append(f"   {violation.message}")
                if violation.suggestion:
                    report.append(f"   💡 {violation.suggestion}")
                report.append("")
        
        # Summary
        error_count = sum(1 for v in violations if v.severity == "error")
        warning_count = sum(1 for v in violations if v.severity == "warning")
        info_count = sum(1 for v in violations if v.severity == "info")
        
        report.extend([
            "📊 SUMMARY",
            "-" * 20,
            f"🔴 Errors: {error_count}",
            f"🟡 Warnings: {warning_count}",
            f"🔵 Info: {info_count}",
            f"📁 Total: {len(violations)}",
        ])
        
        return "\n".join(report)
