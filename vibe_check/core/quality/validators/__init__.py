"""
File: vibe_check/core/quality/validators/__init__.py
Purpose: Modular validator architecture with auto-discovery and clean imports
Related Files: base.py, constants_validator.py, terminology_validator.py, config_schema_validator.py
Dependencies: typing, pathlib, abc
"""

from typing import Dict, List, Type, Optional
from pathlib import Path
import importlib
import inspect
import logging

from .base import BaseValidator, QualityViolation, ViolationType
from .constants_validator import ConstantsValidator
from .terminology_validator import TerminologyValidator
from .config_schema_validator import ConfigSchemaValidator

logger = logging.getLogger(__name__)


class ValidatorRegistry:
    """Registry for auto-discovery and management of validators."""
    
    def __init__(self) -> None:
        """Initialize validator registry."""
        self._validators: Dict[ViolationType, Type[BaseValidator]] = {}
        self._instances: Dict[ViolationType, BaseValidator] = {}
        self._register_built_in_validators()
    
    def _register_built_in_validators(self) -> None:
        """Register built-in validators."""
        self.register(ViolationType.CONSTANTS_USAGE, ConstantsValidator)
        self.register(ViolationType.TERMINOLOGY, TerminologyValidator)
        self.register(ViolationType.CONFIG_SCHEMA, ConfigSchemaValidator)
        
        logger.debug(f"Registered {len(self._validators)} built-in validators")
    
    def register(self, violation_type: ViolationType, validator_class: Type[BaseValidator]) -> None:
        """Register a validator class for a specific violation type."""
        if not issubclass(validator_class, BaseValidator):
            raise ValueError(f"Validator class must inherit from BaseValidator: {validator_class}")
        
        self._validators[violation_type] = validator_class
        # Clear cached instance if it exists
        if violation_type in self._instances:
            del self._instances[violation_type]
        
        logger.debug(f"Registered validator: {violation_type.value} -> {validator_class.__name__}")
    
    def get_validator(self, violation_type: ViolationType) -> BaseValidator:
        """Get validator instance for a specific violation type."""
        if violation_type not in self._validators:
            raise ValueError(f"No validator registered for violation type: {violation_type}")
        
        # Use cached instance if available
        if violation_type not in self._instances:
            validator_class = self._validators[violation_type]
            self._instances[violation_type] = validator_class()
            logger.debug(f"Created validator instance: {violation_type.value}")
        
        return self._instances[violation_type]
    
    def get_all_validators(self) -> Dict[ViolationType, BaseValidator]:
        """Get all registered validator instances."""
        return {
            violation_type: self.get_validator(violation_type)
            for violation_type in self._validators.keys()
        }
    
    def discover_validators(self, package_path: Optional[Path] = None) -> int:
        """
        Discover and register validators from a package directory.
        
        Args:
            package_path: Path to search for validators (defaults to current package)
            
        Returns:
            Number of validators discovered and registered
        """
        if package_path is None:
            package_path = Path(__file__).parent
        
        discovered_count = 0
        
        for py_file in package_path.glob("*_validator.py"):
            if py_file.name == "__init__.py":
                continue
            
            try:
                # Import the module
                module_name = f"vibe_check.core.quality.validators.{py_file.stem}"
                module = importlib.import_module(module_name)
                
                # Find validator classes in the module
                for name, obj in inspect.getmembers(module, inspect.isclass):
                    if (issubclass(obj, BaseValidator) and 
                        obj != BaseValidator and 
                        hasattr(obj, 'VIOLATION_TYPE')):
                        
                        violation_type = obj.VIOLATION_TYPE
                        if violation_type is not None and violation_type not in self._validators:
                            self.register(violation_type, obj)
                            discovered_count += 1
                            logger.info(f"Auto-discovered validator: {name} for {violation_type.value}")
                
            except Exception as e:
                logger.warning(f"Failed to discover validators in {py_file}: {e}")
        
        return discovered_count
    
    def list_validators(self) -> List[Dict[str, str]]:
        """List all registered validators with their information."""
        validators_info = []

        for violation_type, validator_class in self._validators.items():
            # Extract description safely
            description = ''
            if validator_class.__doc__:
                lines = validator_class.__doc__.split('\n')
                for line in lines:
                    stripped = line.strip()
                    if stripped:
                        description = stripped
                        break

            validators_info.append({
                'violation_type': violation_type.value if violation_type else 'unknown',
                'class_name': validator_class.__name__,
                'module': validator_class.__module__,
                'description': description
            })

        return validators_info
    
    def validate_all_files(self, file_paths: List[Path]) -> List[QualityViolation]:
        """Validate files using all registered validators."""
        all_violations = []
        
        for file_path in file_paths:
            if not file_path.exists():
                continue
            
            # Run all validators
            for violation_type in self._validators.keys():
                try:
                    validator = self.get_validator(violation_type)
                    violations = validator.validate_file(file_path)
                    all_violations.extend(violations)
                except Exception as e:
                    logger.error(f"Validator {violation_type.value} failed on {file_path}: {e}")
        
        return all_violations


# Global registry instance
_registry = ValidatorRegistry()


def get_registry() -> ValidatorRegistry:
    """Get the global validator registry."""
    return _registry


def register_validator(violation_type: ViolationType, validator_class: Type[BaseValidator]) -> None:
    """Register a validator class globally."""
    _registry.register(violation_type, validator_class)


def get_validator(violation_type: ViolationType) -> BaseValidator:
    """Get a validator instance globally."""
    return _registry.get_validator(violation_type)


def get_all_validators() -> Dict[ViolationType, BaseValidator]:
    """Get all validator instances globally."""
    return _registry.get_all_validators()


def discover_validators(package_path: Optional[Path] = None) -> int:
    """Discover validators globally."""
    return _registry.discover_validators(package_path)


# Export all public interfaces
__all__ = [
    # Base classes and types
    'BaseValidator',
    'QualityViolation', 
    'ViolationType',
    
    # Concrete validators
    'ConstantsValidator',
    'TerminologyValidator',
    'ConfigSchemaValidator',
    
    # Registry system
    'ValidatorRegistry',
    'get_registry',
    'register_validator',
    'get_validator',
    'get_all_validators',
    'discover_validators'
]
