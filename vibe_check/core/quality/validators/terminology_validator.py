"""
File: vibe_check/core/quality/validators/terminology_validator.py
Purpose: Validator for terminology consistency across the codebase
Related Files: base.py, __init__.py, automated_enforcement.py
Dependencies: typing, pathlib, re
"""

import re
from typing import List, Dict, Set, Tuple
from pathlib import Path

from .base import BaseValidator, QualityViolation, ViolationType, ContentValidator
from ...constants.terminology import (
    AnalysisTerms, ConfigurationTerms, ComponentTerms, ErrorTerms,
    DeprecatedTerms, UserFacingTerms
)


class TerminologyValidator(ContentValidator):
    """Validates terminology consistency across the codebase."""
    
    VIOLATION_TYPE = ViolationType.TERMINOLOGY
    SUPPORTED_EXTENSIONS = {'.py', '.md', '.rst', '.txt', '.yaml', '.yml', '.json', '.toml'}
    
    def _initialize_validator(self) -> None:
        """Initialize terminology validation patterns."""
        # Load terminology mappings from constants
        self.preferred_terms = self.config.get('preferred_terms', {
            # Configuration terminology
            'configuration': ConfigurationTerms.CONFIG,
            'configurations': 'configs',
            'setting': ConfigurationTerms.CONFIG,
            'settings': 'configs',
            'option': ConfigurationTerms.CONFIG,
            'options': 'configs',
            'parameter': ConfigurationTerms.CONFIG,
            'parameters': 'configs',
            
            # Analysis terminology  
            'error': AnalysisTerms.ISSUE,
            'errors': 'issues',
            'warning': AnalysisTerms.ISSUE,
            'warnings': 'issues',
            'problem': AnalysisTerms.ISSUE,
            'problems': 'issues',
            'violation': AnalysisTerms.ISSUE,
            'violations': 'issues',
            'finding': AnalysisTerms.ISSUE,
            'findings': 'issues',
            
            # Component terminology
            'analyzer': 'engine',
            'analyzers': 'engines',
            'processor': 'engine',
            'processors': 'engines',
            'scanner': 'engine',
            'scanners': 'engines',
            
            # Error terminology
            'exception': ErrorTerms.ERROR,
            'exceptions': 'errors',
            'failure': ErrorTerms.ERROR,
            'failures': 'errors',
            
            # User-facing terminology
            'check': 'analysis',
            'checks': 'analyses',
            'scan': 'analysis',
            'scans': 'analyses',
            'inspection': 'analysis',
            'inspections': 'analyses',
        })
        
        # Add custom terminology from config
        if 'custom_terms' in self.config:
            self.preferred_terms.update(self.config['custom_terms'])
        
        # Deprecated terms that should not be used
        self.deprecated_terms = self.config.get('deprecated_terms', {
            'linter': 'Use "analysis engine" or "analyzer"',
            'linting': 'Use "analysis" or "code analysis"',
            'lint': 'Use "analyze" or "check"',
            'static analysis': 'Use "code analysis"',
            'code smell': 'Use "code issue" or "quality issue"',
            'bug': 'Use "issue" or "defect"',
            'bugs': 'Use "issues" or "defects"',
        })
        
        # Files to exclude from terminology checking
        self.excluded_files = self.config.get('excluded_files', [
            'test_', 'conftest', '__pycache__', '.git', '.pytest_cache',
            'node_modules', 'venv', 'env', '.env'
        ])
        
        # Case sensitivity settings
        self.case_sensitive = self.config.get('case_sensitive', False)
        
        # Context-aware checking (avoid false positives in code)
        self.check_code_context = self.config.get('check_code_context', True)
    
    def validate_file(self, file_path: Path) -> List[QualityViolation]:
        """Validate terminology consistency in a file."""
        violations = []
        
        # Skip excluded files
        if any(pattern in str(file_path).lower() for pattern in self.excluded_files):
            return violations
        
        try:
            lines = self._get_file_lines(file_path)
            
            for line_num, line in enumerate(lines, 1):
                violations.extend(self._check_line_terminology(
                    line, line_num, str(file_path), file_path.suffix
                ))
                
        except Exception as e:
            self.logger.error(f"Failed to validate terminology in {file_path}: {e}")
            violations.append(QualityViolation(
                file_path=str(file_path),
                line_number=1,
                violation_type=self.VIOLATION_TYPE,
                message=f"Failed to validate file: {e}",
                severity="error"
            ))
        
        return violations
    
    def _check_line_terminology(self, line: str, line_num: int, file_path: str, file_ext: str) -> List[QualityViolation]:
        """Check a single line for terminology violations."""
        violations = []
        
        # Skip certain contexts in code files
        if file_ext == '.py' and self.check_code_context:
            stripped_line = line.strip()
            # Skip comments, docstrings, and string literals in some contexts
            if (stripped_line.startswith('#') or 
                stripped_line.startswith('"""') or 
                stripped_line.startswith("'''") or
                'import ' in stripped_line or
                'from ' in stripped_line):
                return violations
        
        # Check for preferred terminology
        for deprecated_term, preferred_term in self.preferred_terms.items():
            violations.extend(self._find_term_violations(
                line, line_num, file_path, deprecated_term, preferred_term, 'preferred'
            ))
        
        # Check for deprecated terminology
        for deprecated_term, reason in self.deprecated_terms.items():
            violations.extend(self._find_term_violations(
                line, line_num, file_path, deprecated_term, reason, 'deprecated'
            ))
        
        return violations
    
    def _find_term_violations(self, line: str, line_num: int, file_path: str, 
                            term: str, replacement: str, violation_subtype: str) -> List[QualityViolation]:
        """Find violations of a specific term in a line."""
        violations = []
        
        # Create regex pattern for word boundaries
        flags = re.IGNORECASE if not self.case_sensitive else 0
        pattern = r'\b' + re.escape(term) + r'\b'
        
        matches = list(re.finditer(pattern, line, flags))
        
        for match in matches:
            # Additional context checking for code files
            if self._is_valid_terminology_context(line, match.start(), match.end()):
                severity = "warning" if violation_subtype == 'preferred' else "error"
                rule_id = "TERM001" if violation_subtype == 'preferred' else "TERM002"
                
                if violation_subtype == 'preferred':
                    message = f"Use '{replacement}' instead of '{term}'"
                    suggestion = f"Replace '{term}' with '{replacement}'"
                else:
                    message = f"Deprecated term '{term}': {replacement}"
                    suggestion = f"Remove or replace '{term}' - {replacement}"
                
                violations.append(QualityViolation(
                    file_path=file_path,
                    line_number=line_num,
                    violation_type=self.VIOLATION_TYPE,
                    message=message,
                    suggestion=suggestion,
                    severity=severity,
                    rule_id=rule_id,
                    context={
                        'term': term,
                        'replacement': replacement,
                        'violation_subtype': violation_subtype,
                        'line_content': line.strip(),
                        'match_position': (match.start(), match.end())
                    }
                ))
        
        return violations
    
    def _is_valid_terminology_context(self, line: str, start_pos: int, end_pos: int) -> bool:
        """Check if the terminology violation is in a valid context."""
        # Skip if inside string literals (basic check)
        before_match = line[:start_pos]
        
        # Count quotes before the match
        single_quotes = before_match.count("'") - before_match.count("\\'")
        double_quotes = before_match.count('"') - before_match.count('\\"')
        
        # If odd number of quotes, we're likely inside a string
        if single_quotes % 2 == 1 or double_quotes % 2 == 1:
            return False
        
        # Skip if it's part of a variable name or function name
        if start_pos > 0 and line[start_pos - 1].isalnum():
            return False
        
        if end_pos < len(line) and line[end_pos].isalnum():
            return False
        
        return True
    
    def validate_class_names(self, file_path: Path) -> List[QualityViolation]:
        """Validate class names follow terminology conventions."""
        violations = []
        
        if file_path.suffix != '.py':
            return violations
        
        try:
            content = self._read_file_content(file_path)
            lines = content.split('\n')
            
            for line_num, line in enumerate(lines, 1):
                # Find class definitions
                class_match = re.match(r'^\s*class\s+(\w+)', line)
                if class_match:
                    class_name = class_match.group(1)
                    violations.extend(self._validate_class_name(
                        class_name, line_num, str(file_path)
                    ))
                    
        except Exception as e:
            self.logger.error(f"Failed to validate class names in {file_path}: {e}")
        
        return violations
    
    def _validate_class_name(self, class_name: str, line_num: int, file_path: str) -> List[QualityViolation]:
        """Validate a single class name."""
        violations = []
        
        # Check for deprecated suffixes
        deprecated_suffixes = {
            'Analyzer': 'Engine',
            'Scanner': 'Engine', 
            'Processor': 'Engine',
            'Checker': 'Validator',
            'Linter': 'Engine'
        }
        
        for deprecated, preferred in deprecated_suffixes.items():
            if class_name.endswith(deprecated):
                violations.append(QualityViolation(
                    file_path=file_path,
                    line_number=line_num,
                    violation_type=self.VIOLATION_TYPE,
                    message=f"Class name '{class_name}' uses deprecated suffix '{deprecated}'",
                    suggestion=f"Consider using '{preferred}' suffix instead: {class_name.replace(deprecated, preferred)}",
                    severity="warning",
                    rule_id="TERM003",
                    context={
                        'class_name': class_name,
                        'deprecated_suffix': deprecated,
                        'preferred_suffix': preferred
                    }
                ))
        
        return violations
    
    def get_terminology_report(self, file_paths: List[Path]) -> Dict[str, any]:
        """Generate a comprehensive terminology report."""
        report = {
            'total_files': len(file_paths),
            'files_with_violations': 0,
            'total_violations': 0,
            'violation_breakdown': {
                'preferred_terms': 0,
                'deprecated_terms': 0,
                'class_names': 0
            },
            'most_common_violations': {},
            'files_by_violation_count': []
        }
        
        for file_path in file_paths:
            if not self.should_validate_file(file_path):
                continue
            
            violations = self.validate_file(file_path)
            class_violations = self.validate_class_names(file_path)
            all_violations = violations + class_violations
            
            if all_violations:
                report['files_with_violations'] += 1
                report['total_violations'] += len(all_violations)
                
                file_violation_count = len(all_violations)
                report['files_by_violation_count'].append({
                    'file': str(file_path),
                    'violations': file_violation_count
                })
                
                # Track violation types
                for violation in all_violations:
                    if violation.rule_id == 'TERM001':
                        report['violation_breakdown']['preferred_terms'] += 1
                    elif violation.rule_id == 'TERM002':
                        report['violation_breakdown']['deprecated_terms'] += 1
                    elif violation.rule_id == 'TERM003':
                        report['violation_breakdown']['class_names'] += 1
                    
                    # Track common violations
                    term = violation.context.get('term', 'unknown') if violation.context else 'unknown'
                    report['most_common_violations'][term] = report['most_common_violations'].get(term, 0) + 1
        
        # Sort files by violation count
        report['files_by_violation_count'].sort(key=lambda x: x['violations'], reverse=True)
        
        return report
