"""
File: vibe_check/core/quality/validators/config_schema_validator.py
Purpose: Validator for configuration files against schemas
Related Files: base.py, __init__.py, automated_enforcement.py
Dependencies: typing, pathlib, json, yaml, toml
"""

import json
import re
from typing import List, Dict, Set, Any, Optional, Tuple
from pathlib import Path

from .base import BaseValidator, QualityViolation, ViolationType, FileTypeValidator, ContentValidator

try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False

try:
    import toml
    TOML_AVAILABLE = True
except ImportError:
    TOML_AVAILABLE = False


class ConfigSchemaValidator(ContentValidator):
    """Validates configuration files against schemas."""
    
    VIOLATION_TYPE = ViolationType.CONFIG_SCHEMA
    SUPPORTED_EXTENSIONS = {'.yaml', '.yml', '.json', '.toml', '.ini', '.cfg'}
    
    def _initialize_validator(self) -> None:
        """Initialize configuration validation settings."""
        # Required fields for different config file types
        self.required_fields = self.config.get('required_fields', {
            'vibe_check.yaml': ['analysis', 'output'],
            'vibe-check.yaml': ['analysis', 'output'],
            'pyproject.toml': ['tool.vibe_check'],
            '.vibe_check.json': ['analysis'],
            'setup.cfg': ['vibe_check']
        })
        
        # Schema definitions for validation
        self.schemas = self.config.get('schemas', {
            'vibe_check': {
                'type': 'object',
                'properties': {
                    'analysis': {
                        'type': 'object',
                        'properties': {
                            'enabled': {'type': 'boolean'},
                            'rules': {'type': 'object'},
                            'exclude': {'type': 'array'},
                            'include': {'type': 'array'}
                        }
                    },
                    'output': {
                        'type': 'object',
                        'properties': {
                            'format': {'type': 'string', 'enum': ['json', 'yaml', 'text']},
                            'file': {'type': 'string'},
                            'verbose': {'type': 'boolean'}
                        }
                    }
                }
            }
        })
        
        # Common configuration issues to check (more precise patterns)
        self.common_issues = self.config.get('common_issues', {
            # Only flag missing quotes for strings that contain spaces or special chars
            'missing_quotes': r'^\s*[a-zA-Z_][a-zA-Z0-9_]*\s*:\s*[^"\'\[\{0-9].*\s.*[^"\'\]\}]\s*$',
            'trailing_comma': r',\s*[\]\}]\s*$',
            'duplicate_keys': None,  # Handled separately
            'invalid_yaml_syntax': None,  # Handled by parser
            'invalid_json_syntax': None,  # Handled by parser
        })
        
        # Severity levels for different issues
        self.issue_severity = self.config.get('issue_severity', {
            'missing_required_field': 'error',
            'invalid_field_type': 'error',
            'unknown_field': 'warning',
            'syntax_error': 'error',
            'duplicate_key': 'error',
            'formatting_issue': 'warning'
        })
    
    def validate_file(self, file_path: Path) -> List[QualityViolation]:
        """Validate configuration schema."""
        violations = []
        
        if not self.should_validate_file(file_path):
            return violations
        
        try:
            # Parse the configuration file
            config_data, parse_violations = self._parse_config_file(file_path)
            violations.extend(parse_violations)
            
            if config_data is not None:
                # Validate schema
                violations.extend(self._validate_schema(file_path, config_data))
                
                # Check for common issues
                violations.extend(self._check_common_issues(file_path))
                
        except Exception as e:
            self.logger.error(f"Failed to validate config schema in {file_path}: {e}")
            violations.append(QualityViolation(
                file_path=str(file_path),
                line_number=1,
                violation_type=self.VIOLATION_TYPE,
                message=f"Failed to validate configuration file: {e}",
                severity="error"
            ))
        
        return violations
    
    def _parse_config_file(self, file_path: Path) -> Tuple[Optional[Dict[str, Any]], List[QualityViolation]]:
        """Parse configuration file and return data and any parsing violations."""
        violations = []
        config_data = None
        
        try:
            content = self._read_file_content(file_path)
            
            if file_path.suffix in ['.yaml', '.yml']:
                if not YAML_AVAILABLE:
                    violations.append(QualityViolation(
                        file_path=str(file_path),
                        line_number=1,
                        violation_type=self.VIOLATION_TYPE,
                        message="YAML parsing not available - install PyYAML",
                        severity="warning",
                        rule_id="CONFIG001"
                    ))
                    return None, violations
                
                config_data = yaml.safe_load(content)
                
            elif file_path.suffix == '.json':
                config_data = json.loads(content)
                
            elif file_path.suffix == '.toml':
                if not TOML_AVAILABLE:
                    violations.append(QualityViolation(
                        file_path=str(file_path),
                        line_number=1,
                        violation_type=self.VIOLATION_TYPE,
                        message="TOML parsing not available - install toml",
                        severity="warning",
                        rule_id="CONFIG001"
                    ))
                    return None, violations
                
                config_data = toml.loads(content)
                
            elif file_path.suffix in ['.ini', '.cfg']:
                # Basic INI file validation
                config_data = self._parse_ini_file(content)
                
        except yaml.YAMLError as e:
            # Extract line number from YAML error mark
            line_number = 1
            if hasattr(e, 'problem_mark') and e.problem_mark:
                line_number = e.problem_mark.line + 1

            violations.append(QualityViolation(
                file_path=str(file_path),
                line_number=line_number,
                violation_type=self.VIOLATION_TYPE,
                message=f"YAML syntax error: {e}",
                severity="error",
                rule_id="CONFIG002"
            ))
            
        except json.JSONDecodeError as e:
            violations.append(QualityViolation(
                file_path=str(file_path),
                line_number=e.lineno,
                violation_type=self.VIOLATION_TYPE,
                message=f"JSON syntax error: {e.msg}",
                severity="error",
                rule_id="CONFIG003"
            ))
            
        except Exception as e:
            violations.append(QualityViolation(
                file_path=str(file_path),
                line_number=1,
                violation_type=self.VIOLATION_TYPE,
                message=f"Configuration parsing error: {e}",
                severity="error",
                rule_id="CONFIG004"
            ))
        
        return config_data, violations
    
    def _parse_ini_file(self, content: str) -> Dict[str, Any]:
        """Parse INI file content into dictionary."""
        import configparser
        
        parser = configparser.ConfigParser()
        parser.read_string(content)
        
        result = {}
        for section_name in parser.sections():
            result[section_name] = dict(parser[section_name])
        
        return result
    
    def _validate_schema(self, file_path: Path, config_data: Dict[str, Any]) -> List[QualityViolation]:
        """Validate configuration data against schema."""
        violations = []
        
        # Determine which schema to use based on filename
        schema_name = None
        filename = file_path.name.lower()
        
        if 'vibe_check' in filename or 'vibe-check' in filename:
            schema_name = 'vibe_check'
        elif filename == 'pyproject.toml' and 'tool' in config_data and 'vibe_check' in config_data['tool']:
            schema_name = 'vibe_check'
            config_data = config_data['tool']['vibe_check']  # Extract relevant section
        
        if schema_name and schema_name in self.schemas:
            violations.extend(self._validate_against_schema(
                file_path, config_data, self.schemas[schema_name]
            ))
        
        # Check required fields based on filename
        if filename in self.required_fields:
            violations.extend(self._check_required_fields(
                file_path, config_data, self.required_fields[filename]
            ))
        
        return violations
    
    def _validate_against_schema(self, file_path: Path, data: Dict[str, Any], schema: Dict[str, Any]) -> List[QualityViolation]:
        """Validate data against a schema definition."""
        violations = []
        
        # Basic schema validation (simplified)
        if schema.get('type') == 'object' and 'properties' in schema:
            for field_name, field_schema in schema['properties'].items():
                if field_name in data:
                    field_value = data[field_name]
                    violations.extend(self._validate_field(
                        file_path, field_name, field_value, field_schema
                    ))
        
        return violations
    
    def _validate_field(self, file_path: Path, field_name: str, value: Any, schema: Dict[str, Any]) -> List[QualityViolation]:
        """Validate a single field against its schema."""
        violations = []
        
        expected_type = schema.get('type')
        
        # Type validation
        if expected_type:
            if expected_type == 'string' and not isinstance(value, str):
                violations.append(QualityViolation(
                    file_path=str(file_path),
                    line_number=1,  # Would need line tracking for exact position
                    violation_type=self.VIOLATION_TYPE,
                    message=f"Field '{field_name}' should be a string, got {type(value).__name__}",
                    severity=self.issue_severity['invalid_field_type'],
                    rule_id="CONFIG005"
                ))
            elif expected_type == 'boolean' and not isinstance(value, bool):
                violations.append(QualityViolation(
                    file_path=str(file_path),
                    line_number=1,
                    violation_type=self.VIOLATION_TYPE,
                    message=f"Field '{field_name}' should be a boolean, got {type(value).__name__}",
                    severity=self.issue_severity['invalid_field_type'],
                    rule_id="CONFIG005"
                ))
            elif expected_type == 'array' and not isinstance(value, list):
                violations.append(QualityViolation(
                    file_path=str(file_path),
                    line_number=1,
                    violation_type=self.VIOLATION_TYPE,
                    message=f"Field '{field_name}' should be an array, got {type(value).__name__}",
                    severity=self.issue_severity['invalid_field_type'],
                    rule_id="CONFIG005"
                ))
            elif expected_type == 'object' and not isinstance(value, dict):
                violations.append(QualityViolation(
                    file_path=str(file_path),
                    line_number=1,
                    violation_type=self.VIOLATION_TYPE,
                    message=f"Field '{field_name}' should be an object, got {type(value).__name__}",
                    severity=self.issue_severity['invalid_field_type'],
                    rule_id="CONFIG005"
                ))
        
        # Enum validation
        if 'enum' in schema and value not in schema['enum']:
            violations.append(QualityViolation(
                file_path=str(file_path),
                line_number=1,
                violation_type=self.VIOLATION_TYPE,
                message=f"Field '{field_name}' value '{value}' not in allowed values: {schema['enum']}",
                severity=self.issue_severity['invalid_field_type'],
                rule_id="CONFIG006"
            ))
        
        return violations
    
    def _check_required_fields(self, file_path: Path, data: Dict[str, Any], required: List[str]) -> List[QualityViolation]:
        """Check for required fields in configuration."""
        violations = []
        
        for field in required:
            if '.' in field:
                # Nested field check (e.g., 'tool.vibe_check')
                parts = field.split('.')
                current = data
                for part in parts:
                    if isinstance(current, dict) and part in current:
                        current = current[part]
                    else:
                        violations.append(QualityViolation(
                            file_path=str(file_path),
                            line_number=1,
                            violation_type=self.VIOLATION_TYPE,
                            message=f"Required field '{field}' is missing",
                            suggestion=f"Add '{field}' section to configuration",
                            severity=self.issue_severity['missing_required_field'],
                            rule_id="CONFIG007"
                        ))
                        break
            else:
                # Top-level field check
                if field not in data:
                    violations.append(QualityViolation(
                        file_path=str(file_path),
                        line_number=1,
                        violation_type=self.VIOLATION_TYPE,
                        message=f"Required field '{field}' is missing",
                        suggestion=f"Add '{field}' to configuration",
                        severity=self.issue_severity['missing_required_field'],
                        rule_id="CONFIG007"
                    ))
        
        return violations
    
    def _check_common_issues(self, file_path: Path) -> List[QualityViolation]:
        """Check for common configuration file issues."""
        violations = []
        
        try:
            lines = self._get_file_lines(file_path)
            
            for line_num, line in enumerate(lines, 1):
                # Check for common formatting issues
                for issue_name, pattern in self.common_issues.items():
                    if pattern and re.search(pattern, line):
                        violations.append(QualityViolation(
                            file_path=str(file_path),
                            line_number=line_num,
                            violation_type=self.VIOLATION_TYPE,
                            message=f"Potential {issue_name.replace('_', ' ')}: {line.strip()}",
                            severity=self.issue_severity.get('formatting_issue', 'warning'),
                            rule_id="CONFIG008"
                        ))
                        
        except Exception as e:
            self.logger.error(f"Failed to check common issues in {file_path}: {e}")
        
        return violations
