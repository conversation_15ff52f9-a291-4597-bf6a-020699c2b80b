"""
File: vibe_check/core/quality/validators/constants_validator.py
Purpose: Validator for proper usage of centralized constants
Related Files: base.py, __init__.py, automated_enforcement.py
Dependencies: typing, pathlib, re
"""

import re
from typing import List, Dict, Set
from pathlib import Path

from .base import BaseValidator, QualityViolation, ViolationType, ContentValidator


class ConstantsValidator(ContentValidator):
    """Validates proper usage of centralized constants."""
    
    VIOLATION_TYPE = ViolationType.CONSTANTS_USAGE
    SUPPORTED_EXTENSIONS = {'.py'}
    
    def _initialize_validator(self) -> None:
        """Initialize constants validation patterns."""
        # Files that are allowed to have hardcoded values
        self.allowed_files = self.config.get('allowed_files', [
            'constants', 'config', 'settings', '__init__', 'test_', 'conftest'
        ])
        
        # Patterns for hardcoded values that should use constants
        self.hardcoded_patterns = self.config.get('hardcoded_patterns', {
            r'\.py["\']': "File extension should use FileExtensions.PYTHON",
            r'\.yaml["\']': "File extension should use FileExtensions.YAML", 
            r'\.json["\']': "File extension should use FileExtensions.JSON",
            r'\.toml["\']': "File extension should use FileExtensions.TOML",
            r'\.txt["\']': "File extension should use FileExtensions.TEXT",
            r'\.md["\']': "File extension should use FileExtensions.MARKDOWN",
            r'\.csv["\']': "File extension should use FileExtensions.CSV",
            r'\.xml["\']': "File extension should use FileExtensions.XML",
            r'\.html["\']': "File extension should use FileExtensions.HTML",
            r'\.css["\']': "File extension should use FileExtensions.CSS",
            r'\.js["\']': "File extension should use FileExtensions.JAVASCRIPT",
            r'\.ts["\']': "File extension should use FileExtensions.TYPESCRIPT",
            r'\.sql["\']': "File extension should use FileExtensions.SQL",
            r'\.log["\']': "File extension should use FileExtensions.LOG",
            r'\.ini["\']': "File extension should use FileExtensions.INI",
            r'\.cfg["\']': "File extension should use FileExtensions.CONFIG",
            
            # Tool names
            r'["\']ruff["\']': "Tool name should use ToolNames.RUFF",
            r'["\']mypy["\']': "Tool name should use ToolNames.MYPY", 
            r'["\']bandit["\']': "Tool name should use ToolNames.BANDIT",
            r'["\']pylint["\']': "Tool name should use ToolNames.PYLINT",
            r'["\']flake8["\']': "Tool name should use ToolNames.FLAKE8",
            r'["\']black["\']': "Tool name should use ToolNames.BLACK",
            r'["\']isort["\']': "Tool name should use ToolNames.ISORT",
            r'["\']pytest["\']': "Tool name should use ToolNames.PYTEST",
            r'["\']coverage["\']': "Tool name should use ToolNames.COVERAGE",
            
            # Common thresholds
            r'\b88\b.*line.*length': "Line length should use AnalysisThresholds.MAX_LINE_LENGTH",
            r'\b100\b.*line.*length': "Line length should use AnalysisThresholds.MAX_LINE_LENGTH",
            r'\b10\b.*complexity': "Complexity threshold should use AnalysisThresholds.MAX_COMPLEXITY",
            r'\b20\b.*function.*length': "Function length should use AnalysisThresholds.MAX_FUNCTION_LENGTH",
            r'\b5\b.*parameters': "Parameter count should use AnalysisThresholds.MAX_PARAMETERS",
            
            # Error messages
            r'["\']File not found["\']': "Error message should use ErrorMessages.FILE_NOT_FOUND",
            r'["\']Invalid configuration["\']': "Error message should use ErrorMessages.INVALID_CONFIG",
            r'["\']Analysis failed["\']': "Error message should use ErrorMessages.ANALYSIS_FAILED",
            r'["\']Permission denied["\']': "Error message should use ErrorMessages.PERMISSION_DENIED",
            
            # Magic numbers that should be constants
            r'\b80\b(?!.*line)': "Magic number should be defined as a constant",
            r'\b443\b': "Port number should be defined as a constant",
            r'\b8080\b': "Port number should be defined as a constant",
            r'\b3306\b': "Port number should be defined as a constant",
            r'\b5432\b': "Port number should be defined as a constant",
        })
        
        # Additional patterns from config
        if 'additional_patterns' in self.config:
            self.hardcoded_patterns.update(self.config['additional_patterns'])
    
    def validate_file(self, file_path: Path) -> List[QualityViolation]:
        """Validate constants usage in a file."""
        violations = []
        
        # Skip files that are allowed to have hardcoded values
        if any(pattern in str(file_path).lower() for pattern in self.allowed_files):
            return violations
        
        try:
            lines = self._get_file_lines(file_path)
            
            for line_num, line in enumerate(lines, 1):
                violations.extend(self._check_line_for_violations(
                    line, line_num, str(file_path)
                ))
                
        except Exception as e:
            self.logger.error(f"Failed to validate constants in {file_path}: {e}")
            violations.append(QualityViolation(
                file_path=str(file_path),
                line_number=1,
                violation_type=self.VIOLATION_TYPE,
                message=f"Failed to validate file: {e}",
                severity="error"
            ))
        
        return violations
    
    def _check_line_for_violations(self, line: str, line_num: int, file_path: str) -> List[QualityViolation]:
        """Check a single line for constants violations."""
        violations = []
        
        # Skip comments and docstrings
        stripped_line = line.strip()
        if (stripped_line.startswith('#') or 
            stripped_line.startswith('"""') or 
            stripped_line.startswith("'''")):
            return violations
        
        # Check each pattern
        for pattern, message in self.hardcoded_patterns.items():
            if re.search(pattern, line, re.IGNORECASE):
                violations.append(QualityViolation(
                    file_path=file_path,
                    line_number=line_num,
                    violation_type=self.VIOLATION_TYPE,
                    message=message,
                    suggestion="Import from vibe_check.core.constants and use the constant",
                    severity="warning",
                    rule_id="CONST001",
                    context={
                        'pattern': pattern,
                        'line_content': line.strip(),
                        'matched_text': re.search(pattern, line, re.IGNORECASE).group() if re.search(pattern, line, re.IGNORECASE) else None
                    }
                ))
        
        return violations
    
    def validate_imports(self, file_path: Path) -> List[QualityViolation]:
        """Validate that required constants are imported when used."""
        violations = []
        
        try:
            content = self._read_file_content(file_path)
            lines = content.split('\n')
            
            # Check if constants are used but not imported
            constants_used = set()
            constants_imported = set()
            
            for line in lines:
                # Check for constants usage
                if 'ToolNames.' in line:
                    constants_used.add('ToolNames')
                if 'FileExtensions.' in line:
                    constants_used.add('FileExtensions')
                if 'AnalysisThresholds.' in line:
                    constants_used.add('AnalysisThresholds')
                if 'ErrorMessages.' in line:
                    constants_used.add('ErrorMessages')
                
                # Check for imports
                if 'from vibe_check.core.constants import' in line:
                    # Extract imported constants
                    import_match = re.search(r'import\s+(.+)', line)
                    if import_match:
                        imported = import_match.group(1)
                        for const in imported.split(','):
                            constants_imported.add(const.strip())
            
            # Find missing imports
            missing_imports = constants_used - constants_imported
            
            if missing_imports:
                violations.append(QualityViolation(
                    file_path=str(file_path),
                    line_number=1,
                    violation_type=self.VIOLATION_TYPE,
                    message=f"Constants used but not imported: {', '.join(missing_imports)}",
                    suggestion=f"Add: from vibe_check.core.constants import {', '.join(missing_imports)}",
                    severity="warning",
                    rule_id="CONST002",
                    context={
                        'missing_imports': list(missing_imports),
                        'used_constants': list(constants_used),
                        'imported_constants': list(constants_imported)
                    }
                ))
                
        except Exception as e:
            self.logger.error(f"Failed to validate imports in {file_path}: {e}")
        
        return violations
    
    def get_constants_usage_report(self, file_paths: List[Path]) -> Dict[str, any]:
        """Generate a report of constants usage across files."""
        report = {
            'total_files': len(file_paths),
            'files_with_violations': 0,
            'total_violations': 0,
            'violation_types': {},
            'most_common_patterns': {},
            'files_needing_imports': []
        }
        
        for file_path in file_paths:
            if not self.should_validate_file(file_path):
                continue
            
            violations = self.validate_file(file_path)
            import_violations = self.validate_imports(file_path)
            all_violations = violations + import_violations
            
            if all_violations:
                report['files_with_violations'] += 1
                report['total_violations'] += len(all_violations)
                
                # Track violation patterns
                for violation in all_violations:
                    pattern = violation.context.get('pattern', 'unknown') if violation.context else 'unknown'
                    report['violation_types'][pattern] = report['violation_types'].get(pattern, 0) + 1
                
                # Track files needing imports
                if any(v.rule_id == 'CONST002' for v in all_violations):
                    report['files_needing_imports'].append(str(file_path))
        
        # Find most common patterns
        if report['violation_types']:
            sorted_patterns = sorted(
                report['violation_types'].items(), 
                key=lambda x: x[1], 
                reverse=True
            )
            report['most_common_patterns'] = dict(sorted_patterns[:10])
        
        return report
