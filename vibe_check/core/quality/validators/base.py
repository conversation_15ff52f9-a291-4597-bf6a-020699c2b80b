"""
File: vibe_check/core/quality/validators/base.py
Purpose: Base validator interface and common types for modular validator architecture
Related Files: __init__.py, constants_validator.py, terminology_validator.py, config_schema_validator.py
Dependencies: typing, pathlib, abc, dataclasses, enum
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any, Set
from pathlib import Path
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class ViolationType(Enum):
    """Types of quality violations."""
    CONSTANTS_USAGE = "constants_usage"
    TERMINOLOGY = "terminology"
    CONFIG_SCHEMA = "config_schema"
    IMPORT_STYLE = "import_style"
    DOCUMENTATION = "documentation"
    CODE_STYLE = "code_style"
    SECURITY = "security"
    PERFORMANCE = "performance"
    MAINTAINABILITY = "maintainability"


@dataclass
class QualityViolation:
    """Represents a quality violation found during validation."""
    file_path: str
    line_number: int
    violation_type: ViolationType
    message: str
    suggestion: Optional[str] = None
    severity: str = "warning"
    rule_id: Optional[str] = None
    context: Optional[Dict[str, Any]] = None
    
    def __post_init__(self) -> None:
        """Validate violation data after initialization."""
        if self.line_number < 1:
            raise ValueError("Line number must be positive")

        if self.severity not in {"info", "warning", "error", "critical"}:
            raise ValueError(f"Invalid severity: {self.severity}")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert violation to dictionary representation."""
        return {
            'file_path': self.file_path,
            'line_number': self.line_number,
            'violation_type': self.violation_type.value,
            'message': self.message,
            'suggestion': self.suggestion,
            'severity': self.severity,
            'rule_id': self.rule_id,
            'context': self.context or {}
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'QualityViolation':
        """Create violation from dictionary representation."""
        return cls(
            file_path=data['file_path'],
            line_number=data['line_number'],
            violation_type=ViolationType(data['violation_type']),
            message=data['message'],
            suggestion=data.get('suggestion'),
            severity=data.get('severity', 'warning'),
            rule_id=data.get('rule_id'),
            context=data.get('context')
        )


class BaseValidator(ABC):
    """
    Abstract base class for all quality validators.
    
    This class defines the interface that all validators must implement,
    providing a consistent API for validation operations.
    """
    
    # Subclasses should define this to specify their violation type
    VIOLATION_TYPE: Optional[ViolationType] = None
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize validator with optional configuration.
        
        Args:
            config: Optional configuration dictionary for the validator
        """
        self.config = config or {}
        self.logger = logging.getLogger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        self._initialize_validator()
    
    def _initialize_validator(self) -> None:
        """
        Initialize validator-specific settings.
        
        Subclasses can override this method to perform custom initialization
        based on the provided configuration.
        """
        pass
    
    @abstractmethod
    def validate_file(self, file_path: Path) -> List[QualityViolation]:
        """
        Validate a single file and return any violations found.
        
        Args:
            file_path: Path to the file to validate
            
        Returns:
            List of quality violations found in the file
            
        Raises:
            FileNotFoundError: If the file doesn't exist
            PermissionError: If the file can't be read
        """
        pass
    
    def validate_content(self, content: str, file_path: str) -> List[QualityViolation]:
        """
        Validate file content directly without reading from disk.
        
        Args:
            content: File content to validate
            file_path: Path of the file (for violation reporting)
            
        Returns:
            List of quality violations found in the content
        """
        # Default implementation - subclasses can override for efficiency
        temp_file = Path(file_path)
        if temp_file.exists():
            return self.validate_file(temp_file)
        else:
            # Create a temporary file for validation
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', suffix=temp_file.suffix, delete=False) as f:
                f.write(content)
                temp_path = Path(f.name)
            
            try:
                violations = self.validate_file(temp_path)
                # Update file paths in violations
                for violation in violations:
                    violation.file_path = file_path
                return violations
            finally:
                temp_path.unlink(missing_ok=True)
    
    def should_validate_file(self, file_path: Path) -> bool:
        """
        Determine if a file should be validated by this validator.
        
        Args:
            file_path: Path to the file to check
            
        Returns:
            True if the file should be validated, False otherwise
        """
        # Default implementation - validate all files
        # Subclasses can override to filter by file type, name patterns, etc.
        return file_path.exists() and file_path.is_file()
    
    def get_supported_extensions(self) -> Set[str]:
        """
        Get the file extensions supported by this validator.
        
        Returns:
            Set of file extensions (including the dot, e.g., {'.py', '.yaml'})
        """
        # Default implementation - support all extensions
        # Subclasses should override to specify their supported file types
        return set()
    
    def get_validator_info(self) -> Dict[str, Any]:
        """
        Get information about this validator.
        
        Returns:
            Dictionary containing validator metadata
        """
        # Extract description from docstring safely
        description = ''
        if self.__doc__:
            lines = self.__doc__.split('\n')
            # Find first non-empty line after the opening line
            for line in lines[1:]:
                stripped = line.strip()
                if stripped:
                    description = stripped
                    break

        return {
            'name': self.__class__.__name__,
            'module': self.__class__.__module__,
            'violation_type': self.VIOLATION_TYPE.value if self.VIOLATION_TYPE else None,
            'supported_extensions': list(self.get_supported_extensions()),
            'description': description,
            'config': self.config
        }
    
    def configure(self, config: Dict[str, Any]) -> None:
        """
        Update validator configuration.
        
        Args:
            config: New configuration dictionary
        """
        self.config.update(config)
        self._initialize_validator()
    
    def reset_config(self) -> None:
        """Reset validator configuration to defaults."""
        self.config.clear()
        self._initialize_validator()
    
    def __str__(self) -> str:
        """String representation of the validator."""
        return f"{self.__class__.__name__}(type={self.VIOLATION_TYPE.value if self.VIOLATION_TYPE else 'unknown'})"
    
    def __repr__(self) -> str:
        """Detailed string representation of the validator."""
        return f"{self.__class__.__name__}(violation_type={self.VIOLATION_TYPE}, config={self.config})"


class FileTypeValidator(BaseValidator):
    """
    Base class for validators that work with specific file types.
    
    This class provides common functionality for validators that only
    work with certain file extensions.
    """
    
    # Subclasses should define this to specify supported file extensions
    SUPPORTED_EXTENSIONS: Set[str] = set()
    
    def should_validate_file(self, file_path: Path) -> bool:
        """Check if file should be validated based on extension."""
        if not super().should_validate_file(file_path):
            return False
        
        if not self.SUPPORTED_EXTENSIONS:
            return True
        
        return file_path.suffix.lower() in self.SUPPORTED_EXTENSIONS
    
    def get_supported_extensions(self) -> Set[str]:
        """Get supported file extensions."""
        return self.SUPPORTED_EXTENSIONS.copy()


class ContentValidator(BaseValidator):
    """
    Base class for validators that analyze file content using patterns.
    
    This class provides common functionality for validators that search
    for patterns in file content.
    """
    
    def _read_file_content(self, file_path: Path) -> str:
        """
        Safely read file content with encoding detection.
        
        Args:
            file_path: Path to the file to read
            
        Returns:
            File content as string
            
        Raises:
            FileNotFoundError: If file doesn't exist
            PermissionError: If file can't be read
            UnicodeDecodeError: If file encoding is unsupported
        """
        try:
            return file_path.read_text(encoding='utf-8')
        except UnicodeDecodeError:
            # Try with different encodings
            for encoding in ['latin-1', 'cp1252', 'iso-8859-1']:
                try:
                    return file_path.read_text(encoding=encoding)
                except UnicodeDecodeError:
                    continue
            
            # If all encodings fail, raise the original error
            raise UnicodeDecodeError(
                'utf-8', b'', 0, 1, 
                f"Unable to decode file {file_path} with any supported encoding"
            )
    
    def _get_file_lines(self, file_path: Path) -> List[str]:
        """
        Get file content as list of lines.
        
        Args:
            file_path: Path to the file to read
            
        Returns:
            List of lines in the file
        """
        content = self._read_file_content(file_path)
        return content.split('\n')
