"""
File: vibe_check/core/quality/validators/enhanced_validator.py
Purpose: Enhanced validator with quality improvements based on framework feedback
Related Files: base.py, constants_validator.py, quality_framework.py
Dependencies: typing, pathlib, re, ast
"""

import re
import ast
from typing import List, Dict, Set, Any, Optional
from pathlib import Path

from .base import ContentValidator, QualityViolation, ViolationType
from vibe_check.core.constants import ToolNames, FileExtensions, AnalysisThresholds


class EnhancedConstantsValidator(ContentValidator):
    """Enhanced constants validator with improved accuracy and reduced false positives."""
    
    VIOLATION_TYPE = ViolationType.CONSTANTS_USAGE
    SUPPORTED_EXTENSIONS = {'.py'}
    
    def _initialize_validator(self) -> None:
        """Initialize enhanced constants validation settings."""
        # More precise patterns with context awareness
        self.hardcoded_patterns = {
            # Tool names - only in specific contexts
            r'"ruff"(?=\s*[,\]\)]|\s*$)': 'Use ToolNames.RUFF instead of hardcoded "ruff"',
            r'"mypy"(?=\s*[,\]\)]|\s*$)': 'Use ToolNames.MYPY instead of hardcoded "mypy"',
            r'"bandit"(?=\s*[,\]\)]|\s*$)': 'Use ToolNames.BANDIT instead of hardcoded "bandit"',
            r'"pylint"(?=\s*[,\]\)]|\s*$)': 'Use ToolNames.PYLINT instead of hardcoded "pylint"',
            r'"black"(?=\s*[,\]\)]|\s*$)': 'Use ToolNames.BLACK instead of hardcoded "black"',
            r'"isort"(?=\s*[,\]\)]|\s*$)': 'Use ToolNames.ISORT instead of hardcoded "isort"',
            
            # File extensions - only in file operations
            r'\.endswith\(\s*["\']\.py["\']\s*\)': 'Use FileExtensions.PYTHON instead of hardcoded ".py"',
            r'\.suffix\s*==\s*["\']\.py["\']': 'Use FileExtensions.PYTHON instead of hardcoded ".py"',
            r'glob\(["\'][^"\']*\.py["\']\)': 'Use FileExtensions.PYTHON instead of hardcoded ".py"',
            
            # Thresholds - only in configuration contexts
            r'\bmax_line_length\s*=\s*88\b': 'Use AnalysisThresholds.MAX_LINE_LENGTH instead of hardcoded 88',
            r'\bcomplexity\s*[<>=]+\s*10\b': 'Use AnalysisThresholds.CYCLOMATIC_COMPLEXITY instead of hardcoded 10',
        }
        
        # Context-aware exclusions
        self.allowed_contexts = {
            'comments': r'^\s*#',
            'docstrings': r'^\s*["\']',
            'test_files': r'test_.*\.py$|.*_test\.py$',
            'constants_files': r'constants.*\.py$',
            'config_files': r'config.*\.py$|settings.*\.py$',
            'setup_files': r'setup\.py$|conftest\.py$',
            'init_files': r'__init__\.py$'
        }
        
        # AST-based context detection
        self.ast_contexts_to_ignore = {
            'string_literals_in_tests',
            'import_statements',
            'function_names',
            'class_names',
            'variable_names'
        }
        
        # Improved file filtering
        self.excluded_directories = {
            '.git', '.venv', 'venv', '__pycache__', '.pytest_cache',
            'node_modules', '.tox', 'build', 'dist', '.mypy_cache'
        }
    
    def validate_file(self, file_path: Path) -> List[QualityViolation]:
        """Enhanced file validation with context awareness."""
        violations = []
        
        if not self.should_validate_file(file_path):
            return violations
        
        # Skip if in excluded directory
        if any(excluded in str(file_path) for excluded in self.excluded_directories):
            return violations
        
        # Skip allowed file types
        if any(re.search(pattern, str(file_path)) for pattern in self.allowed_contexts.values()):
            return violations
        
        try:
            content = self._read_file_content(file_path)
            lines = content.split('\n')
            
            # Parse AST for context awareness
            ast_context = self._get_ast_context(content)
            
            for line_num, line in enumerate(lines, 1):
                # Skip comments and docstrings
                if self._is_comment_or_docstring(line):
                    continue
                
                # Check each pattern with enhanced context
                for pattern, message in self.hardcoded_patterns.items():
                    if re.search(pattern, line):
                        # Additional context validation
                        if self._is_valid_violation(line, line_num, ast_context, file_path):
                            violations.append(QualityViolation(
                                file_path=str(file_path),
                                line_number=line_num,
                                violation_type=self.VIOLATION_TYPE,
                                message=message,
                                suggestion="Import from vibe_check.core.constants and use the constant",
                                severity="warning",
                                rule_id="CONST001",
                                context={
                                    'line_content': line.strip(),
                                    'pattern_matched': pattern,
                                    'file_type': file_path.suffix
                                }
                            ))
        
        except Exception as e:
            self.logger.error(f"Failed to validate constants in {file_path}: {e}")
        
        return violations
    
    def _get_ast_context(self, content: str) -> Dict[str, Any]:
        """Get AST context for better validation."""
        try:
            tree = ast.parse(content)
            context = {
                'imports': [],
                'function_names': [],
                'class_names': [],
                'string_literals': []
            }
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        context['imports'].append(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        context['imports'].append(node.module)
                elif isinstance(node, ast.FunctionDef):
                    context['function_names'].append(node.name)
                elif isinstance(node, ast.ClassDef):
                    context['class_names'].append(node.name)
                elif isinstance(node, ast.Str):
                    context['string_literals'].append(node.s)
            
            return context
            
        except SyntaxError:
            return {}
    
    def _is_comment_or_docstring(self, line: str) -> bool:
        """Check if line is a comment or docstring."""
        stripped = line.strip()
        return (
            stripped.startswith('#') or
            stripped.startswith('"""') or
            stripped.startswith("'''") or
            stripped.startswith('"') and stripped.endswith('"') and len(stripped) > 10 or
            stripped.startswith("'") and stripped.endswith("'") and len(stripped) > 10
        )
    
    def _is_valid_violation(self, line: str, line_num: int, ast_context: Dict[str, Any], file_path: Path) -> bool:
        """Enhanced validation to reduce false positives."""
        # Skip if already using constants
        if 'ToolNames.' in line or 'FileExtensions.' in line or 'AnalysisThresholds.' in line:
            return False
        
        # Skip if in import statement
        if re.match(r'^\s*(from|import)\s+', line):
            return False
        
        # Skip if defining a constant (assignment at module level)
        if re.match(r'^\s*[A-Z_][A-Z0-9_]*\s*=', line):
            return False
        
        # Skip if in function/method definition
        if re.match(r'^\s*def\s+', line):
            return False
        
        # Skip if in class definition
        if re.match(r'^\s*class\s+', line):
            return False
        
        # Skip if in string formatting context
        if 'f"' in line or "f'" in line or '.format(' in line or '%' in line:
            return False
        
        # Skip if in logging context
        if any(log_word in line.lower() for log_word in ['log', 'print', 'debug', 'info', 'warn', 'error']):
            return False
        
        # Skip if in test assertion
        if any(test_word in line.lower() for test_word in ['assert', 'expect', 'should']):
            return False
        
        # Skip if in exception handling
        if any(exc_word in line.lower() for exc_word in ['raise', 'except', 'try:']):
            return False
        
        # Additional context-specific checks
        if self._is_tool_name_pattern(line):
            return self._is_tool_usage_context(line)
        
        if self._is_file_extension_pattern(line):
            return self._is_file_operation_context(line)
        
        return True
    
    def _is_tool_name_pattern(self, line: str) -> bool:
        """Check if line contains tool name pattern."""
        tool_names = ['ruff', 'mypy', 'bandit', 'pylint', 'black', 'isort']
        return any(f'"{tool}"' in line for tool in tool_names)
    
    def _is_tool_usage_context(self, line: str) -> bool:
        """Check if tool name is in actual usage context."""
        usage_indicators = [
            'subprocess', 'run', 'call', 'execute', 'command',
            'tool', 'linter', 'formatter', 'checker'
        ]
        return any(indicator in line.lower() for indicator in usage_indicators)
    
    def _is_file_extension_pattern(self, line: str) -> bool:
        """Check if line contains file extension pattern."""
        return '.py"' in line or ".py'" in line
    
    def _is_file_operation_context(self, line: str) -> bool:
        """Check if file extension is in file operation context."""
        file_operations = [
            'endswith', 'suffix', 'glob', 'listdir', 'walk',
            'find', 'search', 'filter', 'match'
        ]
        return any(op in line.lower() for op in file_operations)


class EnhancedTerminologyValidator(ContentValidator):
    """Enhanced terminology validator with improved context awareness."""
    
    VIOLATION_TYPE = ViolationType.TERMINOLOGY
    SUPPORTED_EXTENSIONS = {'.py', '.md', '.rst', '.txt'}
    
    def _initialize_validator(self) -> None:
        """Initialize enhanced terminology validation."""
        # More precise terminology patterns
        self.terminology_replacements = {
            # Component terminology - only in class/function names
            r'\bclass\s+\w*Analyzer\b': 'Use "Engine" instead of "Analyzer" in class names',
            r'\bclass\s+\w*Processor\b': 'Use "Engine" instead of "Processor" in class names',
            r'\bdef\s+\w*analyze\w*\(': 'Consider using "process" or "execute" instead of "analyze"',
            
            # Configuration terminology - only in variable/parameter names
            r'\bconfiguration\s*=': 'Use "config" instead of "configuration"',
            r'\bsettings\s*=': 'Use "config" instead of "settings"',
            r'\boptions\s*=': 'Use "config" instead of "options"',
            
            # Result terminology - only in specific contexts
            r'\bfinding\b(?=\s*(=|\(|\[))': 'Use "issue" instead of "finding"',
            r'\bproblem\b(?=\s*(=|\(|\[))': 'Use "issue" instead of "problem"',
        }
        
        # Context exceptions
        self.context_exceptions = {
            'comments': r'^\s*#',
            'docstrings': r'^\s*["\']',
            'imports': r'^\s*(from|import)',
            'urls': r'https?://',
            'file_paths': r'[/\\]',
            'error_messages': r'(error|exception|traceback)',
        }
        
        # File-specific exclusions
        self.excluded_files = {
            'test_', 'conftest.py', '__init__.py', 'setup.py',
            'constants', 'config', 'settings'
        }
    
    def validate_file(self, file_path: Path) -> List[QualityViolation]:
        """Enhanced terminology validation with context awareness."""
        violations = []
        
        if not self.should_validate_file(file_path):
            return violations
        
        # Skip excluded files
        if any(pattern in str(file_path).lower() for pattern in self.excluded_files):
            return violations
        
        try:
            content = self._read_file_content(file_path)
            lines = content.split('\n')
            
            for line_num, line in enumerate(lines, 1):
                # Skip excluded contexts
                if any(re.search(pattern, line) for pattern in self.context_exceptions.values()):
                    continue
                
                # Check terminology patterns
                for pattern, message in self.terminology_replacements.items():
                    if re.search(pattern, line, re.IGNORECASE):
                        violations.append(QualityViolation(
                            file_path=str(file_path),
                            line_number=line_num,
                            violation_type=self.VIOLATION_TYPE,
                            message=message,
                            suggestion="Apply standardized terminology from style guide",
                            severity="info",
                            rule_id="TERM001",
                            context={
                                'line_content': line.strip(),
                                'pattern_matched': pattern,
                                'file_extension': file_path.suffix
                            }
                        ))
        
        except Exception as e:
            self.logger.error(f"Failed to validate terminology in {file_path}: {e}")
        
        return violations
