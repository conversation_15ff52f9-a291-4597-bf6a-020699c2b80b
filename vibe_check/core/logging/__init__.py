"""
Logging Package
===========

This package provides centralized logging functionality for the Vibe Check tool.
It implements structured logging, log correlation, and log aggregation.
"""

from .structured_logger import (
    StructuredLogger,
    LogLevel,
    StructuredLogRecord,
    JsonFormatter,
)
from .correlation import (
    CorrelationContext,
    LogCorrelator,
    get_correlator,
    create_correlation_context,
    get_correlation_context,
)

# Initialize the default logger
from .setup import setup_logging
setup_logging()

__all__ = [
    # Setup
    'setup_logging',
    'get_logger',
    
    # Contextual logging
    'ContextualLogger',
    'ContextLogger',
    
    # Structured logging
    'StructuredLogger',
    'LogLevel',
    'StructuredLogRecord',
    'JsonFormatter',
    
    # Correlation
    'CorrelationContext',
    'LogCorrelator',
    'get_correlator',
    'create_correlation_context',
    'get_correlation_context',
]
