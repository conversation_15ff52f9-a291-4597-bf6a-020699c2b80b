"""
Error Handling Handlers Module
=========================

This module provides functions for handling errors in the Vibe Check tool.
These functions can be used to format, log, and recover from errors.
"""

import logging
import traceback
from typing import TypeVar, Callable, Any, Dict, Optional

from .exceptions import (
    VibeCheckError,
    ToolError,
    FileError,
    ActorError,
    PluginError,
)

# Define type variables for the decorator
F = TypeVar('F', bound=Callable[..., Any])
ErrorHandler = Callable[[Exception, Dict[str, Any]], Any]

# Set up logging
logger = logging.getLogger("vibe_check_error_handler")


def format_error_for_user(error: Exception) -> Dict[str, Any]:
    """
    Format an error for user display.

    Args:
        error: The exception to format

    Returns:
        Formatted error information
    """
    error_info = {
        "error_type": error.__class__.__name__,
        "message": str(error),
        "details": {}
    }

    # Add specific details based on error type
    if isinstance(error, VibeCheckError):
        error_info["details"] = error.details

        if isinstance(error, ToolError):
            error_info["tool_name"] = error.tool_name
        elif isinstance(error, FileError):
            error_info["file_path"] = error.file_path
        elif isinstance(error, ActorError):
            error_info["actor_id"] = error.actor_id
        elif isinstance(error, PluginError):
            error_info["plugin_name"] = error.plugin_name

    return error_info


def log_error(error: Exception, context: Dict[str, Any]) -> None:
    """
    Log an error with context details.

    Args:
        error: The exception to log
        context: Context information about where the error occurred
    """
    # Format the error message
    error_msg = f"Error: {error.__class__.__name__}: {error}"

    # Add context information
    if "function" in context:
        error_msg += f" (in {context['function']})"

    # Log the error with appropriate level based on type
    if isinstance(error, VibeCheckError):
        logger.error(error_msg)
    else:
        logger.critical(error_msg)
        logger.debug(context.get("traceback", "No traceback available"))


def recover_from_error(error: Exception, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Attempt to recover from an error.

    Args:
        error: The exception to recover from
        context: Context information about where the error occurred

    Returns:
        Recovery information or None if recovery wasn't possible
    """
    # Log the error
    log_error(error, context)

    # Explicitly define recovery_info with type annotations
    recovery_info: Dict[str, Any] = {
        "recovered": False,
        "error": format_error_for_user(error),
        "actions_taken": []  # Initialize as a list
    }

    # Create a properly typed reference to actions_taken
    actions_taken: List[str] = recovery_info["actions_taken"]

    # Attempt to recover based on error type
    if isinstance(error, ToolError):
        # For tool errors, we can continue without this tool
        recovery_info["recovered"] = True
        actions_taken.append(f"Disabled tool: {error.tool_name}")

    elif isinstance(error, FileError):
        # For file errors, we can skip this file
        recovery_info["recovered"] = True
        actions_taken.append(f"Skipped file: {error.file_path}")

    elif isinstance(error, ActorError):
        # For actor errors, we might be able to restart the actor
        recovery_info["recovered"] = False  # Actor errors are harder to recover from
        actions_taken.append(f"Actor {error.actor_id} failed")

    elif isinstance(error, PluginError):
        # For plugin errors, we can disable the plugin
        recovery_info["recovered"] = True
        actions_taken.append(f"Disabled plugin: {error.plugin_name}")

    else:
        # For other errors, we can't recover
        recovery_info["recovered"] = False
        actions_taken.append("Unrecoverable error")

    return recovery_info if recovery_info["recovered"] else None


def handle_error(error: Exception, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Handle an error by logging it and attempting to recover.

    Args:
        error: The exception to handle
        context: Context information about where the error occurred

    Returns:
        Recovery information or None if recovery wasn't possible
    """
    # Log the error
    log_error(error, context)

    # Attempt to recover
    return recover_from_error(error, context)


# Function for backward compatibility
def handle_errors(error_handler: Optional[ErrorHandler] = None) -> Callable[[F], F]:
    """
    Decorator for handling errors in functions.

    Args:
        error_handler: Optional function to handle errors

    Returns:
        Decorated function
    """
    def decorator(func: F) -> F:
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                context: Dict[str, Any] = {
                    "function": func.__name__,
                    "args": args,
                    "kwargs": kwargs,
                    "traceback": traceback.format_exc()
                }

                # Use the provided error handler if available
                if error_handler:
                    return error_handler(e, context)

                # Otherwise, use the default error handler
                return handle_error(e, context)
        return cast(F, wrapper)
    return decorator
