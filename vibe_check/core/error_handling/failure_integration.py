"""
Failure Integration Module for VCS and Plugin Modes.

This module integrates the graceful failure handler with the analysis engines
to provide seamless error recovery and structural fixes.
"""

import asyncio
import logging
from typing import Dict, Any, Optional, Callable
from functools import wraps

from .graceful_failure_handler import <PERSON>fulF<PERSON>ureHandler, FailureCategory
from .exceptions import VibeCheckError

logger = logging.getLogger(__name__)

# Global failure handler instance
_failure_handler: Optional[GracefulFailureHandler] = None


def get_failure_handler() -> GracefulFailureHandler:
    """Get the global failure handler instance."""
    global _failure_handler
    if _failure_handler is None:
        _failure_handler = GracefulFailureHandler()
    return _failure_handler


def with_graceful_failure_handling(component: str = "unknown", operation: str = "unknown"):
    """
    Decorator to add graceful failure handling to functions.
    
    Args:
        component: Name of the component (e.g., "vcs_engine", "plugin_analyzer")
        operation: Name of the operation (e.g., "analyze", "initialize")
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                handler = get_failure_handler()
                context = {
                    "function": func.__name__,
                    "args": str(args)[:200],  # Truncate for logging
                    "kwargs": {k: str(v)[:100] for k, v in kwargs.items()}
                }
                
                recovery_result = await handler.handle_failure(
                    e, context, component, operation
                )
                
                if recovery_result:
                    logger.info(f"Recovered from failure in {component}.{operation}")
                    return recovery_result
                else:
                    logger.error(f"Failed to recover from failure in {component}.{operation}")
                    raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                handler = get_failure_handler()
                context = {
                    "function": func.__name__,
                    "args": str(args)[:200],
                    "kwargs": {k: str(v)[:100] for k, v in kwargs.items()}
                }
                
                # For sync functions, we can't do async recovery
                # Just log and re-raise
                logger.error(f"Failure in {component}.{operation}: {e}")
                raise
        
        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


class VCSFailureIntegration:
    """Integration layer for VCS engine failure handling."""
    
    def __init__(self, vcs_engine):
        self.vcs_engine = vcs_engine
        self.failure_handler = get_failure_handler()
    
    async def handle_rule_execution_failure(self, rule_id: str, error: Exception, context: Dict[str, Any]) -> bool:
        """
        Handle rule execution failures with recovery.
        
        Returns:
            True if recovery was successful, False otherwise
        """
        failure_context = {
            "rule_id": rule_id,
            "engine_mode": "vcs",
            **context
        }
        
        recovery_result = await self.failure_handler.handle_failure(
            error, failure_context, "vcs_engine", "rule_execution"
        )
        
        if recovery_result and recovery_result.get("recovery_type") == "execution_strategy_fallback":
            # Switch to individual rule execution
            logger.info("Switching VCS engine to individual rule execution mode")
            if hasattr(self.vcs_engine, 'config'):
                self.vcs_engine.config.use_shared_ast_traversal = False
            return True
        
        return False
    
    async def handle_framework_detection_failure(self, error: Exception, project_path: str) -> bool:
        """Handle framework detection failures."""
        context = {
            "project_path": project_path,
            "operation": "framework_detection"
        }
        
        recovery_result = await self.failure_handler.handle_failure(
            error, context, "vcs_engine", "framework_detection"
        )
        
        if recovery_result:
            # Continue without framework-specific rules
            logger.info("Continuing VCS analysis without framework-specific rules")
            return True
        
        return False
    
    async def handle_memory_pressure(self, current_memory_mb: float) -> Dict[str, Any]:
        """Handle memory pressure situations."""
        from .exceptions import VibeCheckError
        
        memory_error = VibeCheckError(f"High memory usage: {current_memory_mb:.1f}MB")
        context = {
            "current_memory_mb": current_memory_mb,
            "operation": "memory_management"
        }
        
        recovery_result = await self.failure_handler.handle_failure(
            memory_error, context, "vcs_engine", "memory_management"
        )
        
        if recovery_result and recovery_result.get("recovery_type") == "memory_optimization":
            return {
                "batch_size": recovery_result.get("new_batch_size", 2),
                "cleanup_performed": True,
                "continue_analysis": True
            }
        
        return {"continue_analysis": False}


class PluginFailureIntegration:
    """Integration layer for Plugin mode failure handling."""
    
    def __init__(self, plugin_analyzer):
        self.plugin_analyzer = plugin_analyzer
        self.failure_handler = get_failure_handler()
    
    async def handle_external_tool_failure(self, tool_name: str, error: Exception, context: Dict[str, Any]) -> bool:
        """
        Handle external tool failures with recovery.
        
        Returns:
            True if analysis should continue without this tool, False to abort
        """
        failure_context = {
            "tool_name": tool_name,
            "engine_mode": "plugin",
            **context
        }
        
        recovery_result = await self.failure_handler.handle_failure(
            error, failure_context, "plugin_analyzer", "external_tool_execution"
        )
        
        if recovery_result and recovery_result.get("recovery_type") == "tool_skip":
            logger.info(f"Skipping failed tool {tool_name}, continuing with remaining tools")
            return True
        
        return False
    
    async def handle_import_analysis_failure(self, error: Exception, project_path: str) -> bool:
        """Handle import analysis failures."""
        context = {
            "project_path": project_path,
            "analysis_type": "import_analysis"
        }
        
        recovery_result = await self.failure_handler.handle_failure(
            error, context, "plugin_analyzer", "import_analysis"
        )
        
        if recovery_result:
            logger.info("Continuing plugin analysis without import analysis")
            return True
        
        return False
    
    async def handle_visualization_failure(self, error: Exception, context: Dict[str, Any]) -> bool:
        """Handle visualization generation failures."""
        recovery_result = await self.failure_handler.handle_failure(
            error, context, "plugin_analyzer", "visualization_generation"
        )
        
        if recovery_result:
            logger.info("Continuing plugin analysis without visualizations")
            return True
        
        return False


class CLIFailureIntegration:
    """Integration layer for CLI failure handling."""
    
    def __init__(self):
        self.failure_handler = get_failure_handler()
    
    async def handle_analysis_mode_failure(self, mode: str, error: Exception, context: Dict[str, Any]) -> Optional[str]:
        """
        Handle analysis mode failures with fallback.
        
        Returns:
            Alternative mode to try, or None if no fallback available
        """
        failure_context = {
            "analysis_mode": mode,
            "cli_operation": "mode_selection",
            **context
        }
        
        recovery_result = await self.failure_handler.handle_failure(
            error, failure_context, "cli", "analysis_mode_selection"
        )
        
        if recovery_result:
            # Suggest fallback mode
            if mode == "vcs":
                return "plugin"
            elif mode == "plugin":
                return "vcs"
        
        return None
    
    async def handle_configuration_failure(self, error: Exception, config_path: str) -> bool:
        """Handle configuration loading failures."""
        context = {
            "config_path": config_path,
            "operation": "configuration_loading"
        }
        
        recovery_result = await self.failure_handler.handle_failure(
            error, context, "cli", "configuration_loading"
        )
        
        if recovery_result and recovery_result.get("recovery_type") == "configuration_fallback":
            logger.info("Using default configuration due to configuration file error")
            return True
        
        return False
    
    def get_user_friendly_error_message(self, error: Exception, context: Dict[str, Any]) -> str:
        """Get a user-friendly error message with recovery suggestions."""
        handler = self.failure_handler
        
        # Get recent failures for context
        recent_failures = [f for f in handler.failure_history 
                          if (f.timestamp - handler.failure_history[-1].timestamp).seconds < 300]
        
        error_msg = str(error)
        suggestions = []
        
        # Provide specific suggestions based on error type
        if "permission" in error_msg.lower():
            suggestions.append("• Check file permissions and run with appropriate privileges")
        elif "not found" in error_msg.lower():
            suggestions.append("• Verify the file or directory path exists")
            suggestions.append("• Check if external tools are installed and in PATH")
        elif "memory" in error_msg.lower():
            suggestions.append("• Try analyzing smaller directories or files")
            suggestions.append("• Close other memory-intensive applications")
        elif "timeout" in error_msg.lower():
            suggestions.append("• Try increasing timeout with --timeout option")
            suggestions.append("• Analyze smaller projects or use --fast-mode")
        
        # Add mode-specific suggestions
        if len(recent_failures) > 2:
            suggestions.append("• Try switching analysis modes (--vcs-mode or --plugin-mode)")
        
        # Build user-friendly message
        message = f"❌ Analysis failed: {error_msg}\n"
        if suggestions:
            message += "\n💡 Suggestions:\n" + "\n".join(suggestions)
        
        return message


# Convenience functions for easy integration
async def handle_vcs_failure(vcs_engine, error: Exception, context: Dict[str, Any]) -> Optional[Any]:
    """Convenience function for handling VCS failures."""
    integration = VCSFailureIntegration(vcs_engine)
    return await integration.failure_handler.handle_failure(
        error, context, "vcs_engine", "analysis"
    )


async def handle_plugin_failure(plugin_analyzer, error: Exception, context: Dict[str, Any]) -> Optional[Any]:
    """Convenience function for handling Plugin failures."""
    integration = PluginFailureIntegration(plugin_analyzer)
    return await integration.failure_handler.handle_failure(
        error, context, "plugin_analyzer", "analysis"
    )


def get_failure_statistics() -> Dict[str, Any]:
    """Get failure statistics from the global handler."""
    handler = get_failure_handler()
    return handler.get_failure_statistics()
