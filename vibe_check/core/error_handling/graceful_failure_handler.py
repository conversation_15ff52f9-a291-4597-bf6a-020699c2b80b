"""
Graceful Failure Handler for Vibe Check Analysis Modes.

This module provides comprehensive error handling, root cause diagnosis,
structural fixes, and failure prevention mechanisms for both VCS and Plugin modes.
"""

import asyncio
import logging
import traceback
import time
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable, Union
from datetime import datetime

from vibe_check.core.error_handling.exceptions import (
    VibeCheckError, ToolError, FileError, ConfigurationError
)

logger = logging.getLogger(__name__)


class FailureCategory(Enum):
    """Categories of analysis failures."""
    INITIALIZATION = "initialization"
    RULE_EXECUTION = "rule_execution"
    EXTERNAL_TOOL = "external_tool"
    FILE_ACCESS = "file_access"
    CONFIGURATION = "configuration"
    MEMORY_PRESSURE = "memory_pressure"
    TIMEOUT = "timeout"
    NETWORK = "network"
    DEPENDENCY = "dependency"
    UNKNOWN = "unknown"


class FailureSeverity(Enum):
    """Severity levels for failures."""
    CRITICAL = "critical"  # Analysis cannot continue
    HIGH = "high"         # Major functionality affected
    MEDIUM = "medium"     # Some functionality affected
    LOW = "low"          # Minor issues, analysis can continue
    INFO = "info"        # Informational, no impact


@dataclass
class FailureContext:
    """Context information about a failure."""
    timestamp: datetime
    category: FailureCategory
    severity: FailureSeverity
    component: str
    operation: str
    error_message: str
    stack_trace: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    recovery_attempted: bool = False
    recovery_successful: bool = False
    recovery_actions: List[str] = field(default_factory=list)


@dataclass
class RecoveryStrategy:
    """Strategy for recovering from a specific type of failure."""
    name: str
    description: str
    applicable_categories: List[FailureCategory]
    recovery_function: Callable
    max_attempts: int = 3
    timeout_seconds: float = 30.0
    prerequisites: List[str] = field(default_factory=list)


class GracefulFailureHandler:
    """
    Comprehensive failure handling system for Vibe Check analysis modes.
    
    Provides:
    - Root cause diagnosis
    - Automatic recovery strategies
    - Structural fixes for common failures
    - Prevention mechanisms
    - Detailed failure reporting
    """
    
    def __init__(self):
        self.failure_history: List[FailureContext] = []
        self.recovery_strategies: Dict[FailureCategory, List[RecoveryStrategy]] = {}
        self.prevention_rules: List[Callable] = []
        self.structural_fixes: Dict[str, Callable] = {}
        
        # Initialize default recovery strategies
        self._initialize_recovery_strategies()
        self._initialize_prevention_rules()
        self._initialize_structural_fixes()
    
    def _initialize_recovery_strategies(self) -> None:
        """Initialize default recovery strategies."""
        strategies = [
            RecoveryStrategy(
                name="rule_execution_fallback",
                description="Fall back to individual rule execution when hybrid analysis fails",
                applicable_categories=[FailureCategory.RULE_EXECUTION],
                recovery_function=self._recover_rule_execution_failure,
                max_attempts=1
            ),
            RecoveryStrategy(
                name="external_tool_skip",
                description="Skip failed external tool and continue with available tools",
                applicable_categories=[FailureCategory.EXTERNAL_TOOL],
                recovery_function=self._recover_external_tool_failure,
                max_attempts=1
            ),
            RecoveryStrategy(
                name="file_access_skip",
                description="Skip inaccessible files and continue analysis",
                applicable_categories=[FailureCategory.FILE_ACCESS],
                recovery_function=self._recover_file_access_failure,
                max_attempts=1
            ),
            RecoveryStrategy(
                name="memory_pressure_cleanup",
                description="Perform memory cleanup and reduce batch size",
                applicable_categories=[FailureCategory.MEMORY_PRESSURE],
                recovery_function=self._recover_memory_pressure,
                max_attempts=2
            ),
            RecoveryStrategy(
                name="configuration_defaults",
                description="Fall back to default configuration",
                applicable_categories=[FailureCategory.CONFIGURATION],
                recovery_function=self._recover_configuration_failure,
                max_attempts=1
            ),
            RecoveryStrategy(
                name="timeout_retry",
                description="Retry with increased timeout",
                applicable_categories=[FailureCategory.TIMEOUT],
                recovery_function=self._recover_timeout_failure,
                max_attempts=2
            ),
            RecoveryStrategy(
                name="dependency_graceful_degradation",
                description="Continue with reduced functionality when dependencies are missing",
                applicable_categories=[FailureCategory.DEPENDENCY],
                recovery_function=self._recover_dependency_failure,
                max_attempts=1
            )
        ]
        
        for strategy in strategies:
            for category in strategy.applicable_categories:
                if category not in self.recovery_strategies:
                    self.recovery_strategies[category] = []
                self.recovery_strategies[category].append(strategy)
    
    def _initialize_prevention_rules(self) -> None:
        """Initialize failure prevention rules."""
        self.prevention_rules = [
            self._prevent_memory_exhaustion,
            self._prevent_configuration_conflicts,
            self._prevent_file_access_issues,
            self._prevent_external_tool_failures
        ]
    
    def _initialize_structural_fixes(self) -> None:
        """Initialize structural fixes for common failure patterns."""
        self.structural_fixes = {
            "ast_node_attribute_error": self._fix_ast_node_attribute_error,
            "rule_visitor_pattern_error": self._fix_rule_visitor_pattern_error,
            "external_tool_not_found": self._fix_external_tool_not_found,
            "configuration_schema_mismatch": self._fix_configuration_schema_mismatch,
            "memory_leak_in_rule": self._fix_memory_leak_in_rule
        }
    
    async def handle_failure(self, 
                           error: Exception, 
                           context: Dict[str, Any],
                           component: str = "unknown",
                           operation: str = "unknown") -> Optional[Any]:
        """
        Handle a failure with comprehensive diagnosis and recovery.
        
        Args:
            error: The exception that occurred
            context: Context information about the failure
            component: Component where the failure occurred
            operation: Operation being performed when failure occurred
            
        Returns:
            Recovery result if successful, None if recovery failed
        """
        # Diagnose the failure
        failure_context = await self._diagnose_failure(
            error, context, component, operation
        )
        
        # Record the failure
        self.failure_history.append(failure_context)
        
        # Log the failure
        self._log_failure(failure_context)
        
        # Attempt recovery
        recovery_result = await self._attempt_recovery(failure_context)
        
        # Apply structural fixes if needed
        await self._apply_structural_fixes(failure_context)
        
        # Update prevention rules based on failure patterns
        self._update_prevention_rules(failure_context)
        
        return recovery_result
    
    async def _diagnose_failure(self, 
                               error: Exception, 
                               context: Dict[str, Any],
                               component: str,
                               operation: str) -> FailureContext:
        """Diagnose the root cause of a failure."""
        # Categorize the failure
        category = self._categorize_failure(error, context)
        
        # Determine severity
        severity = self._determine_severity(error, category, context)
        
        # Extract metadata
        metadata = self._extract_failure_metadata(error, context)
        
        return FailureContext(
            timestamp=datetime.now(),
            category=category,
            severity=severity,
            component=component,
            operation=operation,
            error_message=str(error),
            stack_trace=traceback.format_exc(),
            metadata=metadata
        )
    
    def _categorize_failure(self, error: Exception, context: Dict[str, Any]) -> FailureCategory:
        """Categorize the type of failure."""
        error_msg = str(error).lower()
        
        if isinstance(error, ToolError):
            return FailureCategory.EXTERNAL_TOOL
        elif isinstance(error, FileError):
            return FailureCategory.FILE_ACCESS
        elif isinstance(error, ConfigurationError):
            return FailureCategory.CONFIGURATION
        elif "memory" in error_msg or "out of memory" in error_msg:
            return FailureCategory.MEMORY_PRESSURE
        elif "timeout" in error_msg or "timed out" in error_msg:
            return FailureCategory.TIMEOUT
        elif "network" in error_msg or "connection" in error_msg:
            return FailureCategory.NETWORK
        elif "import" in error_msg or "module" in error_msg:
            return FailureCategory.DEPENDENCY
        elif "rule" in error_msg or "analysis" in error_msg:
            return FailureCategory.RULE_EXECUTION
        elif "initialization" in error_msg or "startup" in error_msg:
            return FailureCategory.INITIALIZATION
        else:
            return FailureCategory.UNKNOWN
    
    def _determine_severity(self, 
                          error: Exception, 
                          category: FailureCategory, 
                          context: Dict[str, Any]) -> FailureSeverity:
        """Determine the severity of a failure."""
        # Critical failures that stop analysis
        if category in [FailureCategory.INITIALIZATION, FailureCategory.CONFIGURATION]:
            return FailureSeverity.CRITICAL
        
        # High severity for major functionality
        if category in [FailureCategory.MEMORY_PRESSURE, FailureCategory.DEPENDENCY]:
            return FailureSeverity.HIGH
        
        # Medium severity for partial functionality loss
        if category in [FailureCategory.EXTERNAL_TOOL, FailureCategory.RULE_EXECUTION]:
            return FailureSeverity.MEDIUM
        
        # Low severity for minor issues
        if category in [FailureCategory.FILE_ACCESS, FailureCategory.TIMEOUT]:
            return FailureSeverity.LOW
        
        return FailureSeverity.INFO
    
    def _extract_failure_metadata(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract relevant metadata from failure context."""
        metadata = {
            "error_type": type(error).__name__,
            "error_module": getattr(error, "__module__", "unknown"),
            "context_keys": list(context.keys()) if context else []
        }
        
        # Add specific metadata based on error type
        if hasattr(error, "tool_name"):
            metadata["tool_name"] = error.tool_name
        if hasattr(error, "file_path"):
            metadata["file_path"] = str(error.file_path)
        if hasattr(error, "rule_id"):
            metadata["rule_id"] = error.rule_id
        
        return metadata

    async def _attempt_recovery(self, failure_context: FailureContext) -> Optional[Any]:
        """Attempt to recover from a failure using available strategies."""
        strategies = self.recovery_strategies.get(failure_context.category, [])

        if not strategies:
            logger.warning(f"No recovery strategies available for {failure_context.category}")
            return None

        for strategy in strategies:
            try:
                logger.info(f"Attempting recovery strategy: {strategy.name}")

                # Check prerequisites
                if not self._check_prerequisites(strategy, failure_context):
                    logger.debug(f"Prerequisites not met for strategy {strategy.name}")
                    continue

                # Attempt recovery with timeout
                recovery_result = await asyncio.wait_for(
                    strategy.recovery_function(failure_context),
                    timeout=strategy.timeout_seconds
                )

                if recovery_result:
                    failure_context.recovery_attempted = True
                    failure_context.recovery_successful = True
                    failure_context.recovery_actions.append(strategy.name)

                    logger.info(f"Recovery successful using strategy: {strategy.name}")
                    return recovery_result

            except Exception as recovery_error:
                logger.warning(f"Recovery strategy {strategy.name} failed: {recovery_error}")
                failure_context.recovery_actions.append(f"{strategy.name}_failed")

        failure_context.recovery_attempted = True
        failure_context.recovery_successful = False
        return None

    def _check_prerequisites(self, strategy: RecoveryStrategy, context: FailureContext) -> bool:
        """Check if prerequisites for a recovery strategy are met."""
        for prerequisite in strategy.prerequisites:
            if prerequisite == "memory_available" and context.category == FailureCategory.MEMORY_PRESSURE:
                # Check if we have enough memory for recovery
                import psutil
                memory_percent = psutil.virtual_memory().percent
                if memory_percent > 90:
                    return False
            elif prerequisite == "file_system_writable":
                # Check if we can write to the file system
                try:
                    test_file = Path.cwd() / ".vibe_check_test"
                    test_file.touch()
                    test_file.unlink()
                except Exception:
                    return False

        return True

    # Recovery strategy implementations
    async def _recover_rule_execution_failure(self, context: FailureContext) -> Optional[Any]:
        """Recover from rule execution failures."""
        logger.info("Attempting rule execution recovery: falling back to individual rule execution")

        # This would be called by the VCS engine to switch execution strategies
        return {
            "recovery_type": "execution_strategy_fallback",
            "action": "switch_to_individual_rule_execution",
            "message": "Hybrid analysis failed, falling back to individual rule execution"
        }

    async def _recover_external_tool_failure(self, context: FailureContext) -> Optional[Any]:
        """Recover from external tool failures."""
        tool_name = context.metadata.get("tool_name", "unknown")
        logger.info(f"Attempting external tool recovery: skipping {tool_name}")

        return {
            "recovery_type": "tool_skip",
            "action": f"skip_tool_{tool_name}",
            "message": f"External tool {tool_name} failed, continuing with remaining tools"
        }

    async def _recover_file_access_failure(self, context: FailureContext) -> Optional[Any]:
        """Recover from file access failures."""
        file_path = context.metadata.get("file_path", "unknown")
        logger.info(f"Attempting file access recovery: skipping {file_path}")

        return {
            "recovery_type": "file_skip",
            "action": f"skip_file",
            "file_path": file_path,
            "message": f"File {file_path} is inaccessible, skipping and continuing analysis"
        }

    async def _recover_memory_pressure(self, context: FailureContext) -> Optional[Any]:
        """Recover from memory pressure situations."""
        logger.info("Attempting memory pressure recovery: performing cleanup")

        # Trigger garbage collection
        import gc
        gc.collect()

        # Reduce batch sizes for future operations
        return {
            "recovery_type": "memory_optimization",
            "action": "reduce_batch_size",
            "new_batch_size": 2,  # Reduce from default
            "message": "Memory pressure detected, reducing batch size and performing cleanup"
        }

    async def _recover_configuration_failure(self, context: FailureContext) -> Optional[Any]:
        """Recover from configuration failures."""
        logger.info("Attempting configuration recovery: using default configuration")

        return {
            "recovery_type": "configuration_fallback",
            "action": "use_default_config",
            "message": "Configuration error detected, falling back to default configuration"
        }

    async def _recover_timeout_failure(self, context: FailureContext) -> Optional[Any]:
        """Recover from timeout failures."""
        logger.info("Attempting timeout recovery: increasing timeout and retrying")

        return {
            "recovery_type": "timeout_increase",
            "action": "increase_timeout",
            "new_timeout": 60.0,  # Increase timeout
            "message": "Timeout detected, increasing timeout and retrying operation"
        }

    async def _recover_dependency_failure(self, context: FailureContext) -> Optional[Any]:
        """Recover from dependency failures."""
        logger.info("Attempting dependency recovery: graceful degradation")

        return {
            "recovery_type": "graceful_degradation",
            "action": "disable_dependent_features",
            "message": "Dependency missing, disabling dependent features and continuing"
        }

    # Prevention rule implementations
    def _prevent_memory_exhaustion(self, context: Dict[str, Any]) -> bool:
        """Prevent memory exhaustion by monitoring usage."""
        try:
            import psutil  # type: ignore
            memory_percent = psutil.virtual_memory().percent
            if memory_percent > 85:
                logger.warning(f"High memory usage detected: {memory_percent:.1f}%")
                return False
        except ImportError:
            pass
        return True

    def _prevent_configuration_conflicts(self, context: Dict[str, Any]) -> bool:
        """Prevent configuration conflicts."""
        # Check for common configuration conflicts
        config = context.get("config", {})
        if isinstance(config, dict):
            # Check for conflicting settings
            if config.get("vcs_mode") and config.get("plugin_mode"):
                logger.warning("Conflicting analysis modes detected")
                return False
        return True

    def _prevent_file_access_issues(self, context: Dict[str, Any]) -> bool:
        """Prevent file access issues."""
        file_path = context.get("file_path")
        if file_path:
            path = Path(file_path)
            if not path.exists():
                logger.warning(f"File does not exist: {file_path}")
                return False
            if not path.is_file():
                logger.warning(f"Path is not a file: {file_path}")
                return False
        return True

    def _prevent_external_tool_failures(self, context: Dict[str, Any]) -> bool:
        """Prevent external tool failures."""
        tool_name = context.get("tool_name")
        if tool_name:
            # Check if tool is available
            import shutil
            if not shutil.which(tool_name):
                logger.warning(f"External tool not found: {tool_name}")
                return False
        return True

    # Structural fix implementations
    async def _fix_ast_node_attribute_error(self, context: FailureContext) -> None:
        """Fix AST node attribute errors."""
        logger.info("Applying structural fix for AST node attribute error")
        # This would implement fixes for common AST node attribute issues
        # For example, adding proper type checking before accessing node attributes

    async def _fix_rule_visitor_pattern_error(self, context: FailureContext) -> None:
        """Fix rule visitor pattern errors."""
        logger.info("Applying structural fix for rule visitor pattern error")
        # This would implement fixes for visitor pattern issues
        # For example, ensuring visitor classes have proper rule references

    async def _fix_external_tool_not_found(self, context: FailureContext) -> None:
        """Fix external tool not found errors."""
        logger.info("Applying structural fix for external tool not found")
        # This would implement fixes for missing external tools
        # For example, providing installation guidance or alternative tools

    async def _fix_configuration_schema_mismatch(self, context: FailureContext) -> None:
        """Fix configuration schema mismatch errors."""
        logger.info("Applying structural fix for configuration schema mismatch")
        # This would implement fixes for configuration issues
        # For example, migrating old configuration formats

    async def _fix_memory_leak_in_rule(self, context: FailureContext) -> None:
        """Fix memory leaks in rules."""
        logger.info("Applying structural fix for memory leak in rule")
        # This would implement fixes for memory leaks
        # For example, ensuring proper cleanup in rule execution

    async def _apply_structural_fixes(self, context: FailureContext) -> None:
        """Apply structural fixes based on failure patterns."""
        error_msg = context.error_message.lower()

        # Identify structural issues and apply fixes
        if "attribute" in error_msg and "ast" in error_msg:
            await self._fix_ast_node_attribute_error(context)
        elif "visitor" in error_msg and "rule" in error_msg:
            await self._fix_rule_visitor_pattern_error(context)
        elif "not found" in error_msg and "tool" in error_msg:
            await self._fix_external_tool_not_found(context)
        elif "schema" in error_msg or "configuration" in error_msg:
            await self._fix_configuration_schema_mismatch(context)
        elif "memory" in error_msg and "rule" in error_msg:
            await self._fix_memory_leak_in_rule(context)

    def _update_prevention_rules(self, context: FailureContext) -> None:
        """Update prevention rules based on failure patterns."""
        # Analyze failure patterns and update prevention rules
        if len(self.failure_history) >= 5:
            recent_failures = self.failure_history[-5:]
            categories = [f.category for f in recent_failures]

            # If we see repeated failures of the same category, strengthen prevention
            from collections import Counter
            category_counts = Counter(categories)

            for category, count in category_counts.items():
                if count >= 3:
                    logger.warning(f"Repeated failures in category {category}, strengthening prevention")
                    # This would implement adaptive prevention strengthening

    def _log_failure(self, context: FailureContext) -> None:
        """Log failure information with appropriate severity."""
        log_msg = (
            f"Failure in {context.component}.{context.operation}: "
            f"{context.error_message} (Category: {context.category.value}, "
            f"Severity: {context.severity.value})"
        )

        if context.severity == FailureSeverity.CRITICAL:
            logger.critical(log_msg)
        elif context.severity == FailureSeverity.HIGH:
            logger.error(log_msg)
        elif context.severity == FailureSeverity.MEDIUM:
            logger.warning(log_msg)
        elif context.severity == FailureSeverity.LOW:
            logger.info(log_msg)
        else:
            logger.debug(log_msg)

    def get_failure_statistics(self) -> Dict[str, Any]:
        """Get statistics about failures and recoveries."""
        if not self.failure_history:
            return {"total_failures": 0}

        total_failures = len(self.failure_history)
        successful_recoveries = sum(1 for f in self.failure_history if f.recovery_successful)

        # Category breakdown
        from collections import Counter
        categories = Counter(f.category.value for f in self.failure_history)
        severities = Counter(f.severity.value for f in self.failure_history)

        return {
            "total_failures": total_failures,
            "successful_recoveries": successful_recoveries,
            "recovery_rate": successful_recoveries / total_failures if total_failures > 0 else 0,
            "failure_categories": dict(categories),
            "failure_severities": dict(severities),
            "recent_failures": len([f for f in self.failure_history
                                  if (datetime.now() - f.timestamp).seconds < 3600])
        }
