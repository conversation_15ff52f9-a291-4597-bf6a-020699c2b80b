"""
Error Manager Module
================

This module provides the ErrorManager class, which is responsible for
managing error handling across the system. It provides a centralized
way to register error handlers and handle errors.
"""

import logging
import traceback

from .handlers import handle_error as default_handler

# Set up logging
logger = logging.getLogger("vibe_check_error_manager")

# Type definitions
E = TypeVar('E', bound=Exception)
ErrorHandler = Callable[[Exception, Dict[str, Any]], Any]

# Global error manager instance
_error_manager = None


class ErrorManager:
    """
    Manages error handling across the system.
    
    This class provides a centralized way to register error handlers
    and handle errors. It allows for different handlers to be registered
    for different types of errors.
    """
    
    def __init__(self) -> None:
        """Initialize the error manager."""
        self.handlers: Dict[Type[Exception], ErrorHandler] = {}
        self.fallback_handler: ErrorHandler = default_handler
        
    def register_handler(self, 
                        error_type: Type[E], 
                        handler: <PERSON>rror<PERSON><PERSON><PERSON>) -> None:
        """
        Register a handler for a specific error type.
        
        Args:
            error_type: Type of error to handle
            handler: Function to handle the error
        """
        self.handlers[error_type] = handler
        logger.debug(f"Registered handler for {error_type.__name__}")
        
    def set_fallback_handler(self, handler: ErrorHandler) -> None:
        """
        Set the fallback handler for unhandled error types.
        
        Args:
            handler: Function to handle unhandled errors
        """
        self.fallback_handler = handler
        logger.debug("Set fallback handler")
        
    def handle_error(self, 
                    error: Exception, 
                    context: Optional[Dict[str, Any]] = None) -> Any:
        """
        Handle an error with the appropriate handler.
        
        Args:
            error: The exception to handle
            context: Optional context information about where the error occurred
            
        Returns:
            Result of the error handler
        """
        context = context or {}
        
        # Find the most specific handler
        for error_type, handler in self.handlers.items():
            if isinstance(error, error_type):
                try:
                    return handler(error, context)
                except Exception as handler_error:
                    logger.error(f"Error handler failed: {handler_error}")
                    logger.debug(traceback.format_exc())
                    # Fall back to default handler
                    break
        
        # Use fallback handler if no specific handler found
        try:
            return self.fallback_handler(error, context)
        except Exception as handler_error:
            logger.error(f"Fallback handler failed: {handler_error}")
            logger.debug(traceback.format_exc())
            # Return a basic error response
            return {
                "error": str(error),
                "error_type": error.__class__.__name__,
                "handled": False
            }


def get_error_manager() -> ErrorManager:
    """
    Get the global error manager instance.
    
    Returns:
        Global error manager instance
    """
    global _error_manager
    if _error_manager is None:
        _error_manager = ErrorManager()
    return _error_manager
