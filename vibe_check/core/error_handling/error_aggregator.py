"""
Error Aggregator Module
======================

This module provides centralized error aggregation and categorization for analysis results.
It collects errors from various analysis stages and provides structured error reporting.
"""

import logging
from collections import defaultdict
from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, List, Optional, Any, Set
from pathlib import Path

from ..constants import ErrorMessages, SeverityLevel
from ..constants.terminology import ErrorTerms

logger = logging.getLogger(__name__)


class ErrorCategory(Enum):
    """Categories of errors that can occur during analysis."""
    SYNTAX_ERROR = "syntax_error"
    IMPORT_ERROR = "import_error"
    TOOL_ERROR = "tool_error"
    FILE_ERROR = "file_error"
    CONFIGURATION_ERROR = "configuration_error"
    DEPENDENCY_ERROR = "dependency_error"
    TIMEOUT_ERROR = "timeout_error"
    PERMISSION_ERROR = "permission_error"
    UNKNOWN_ERROR = "unknown_error"

    def display_name(self) -> str:
        """Get human-readable display name for the category."""
        return self.value.replace('_', ' ').title()

    @classmethod
    def get_display_mapping(cls) -> Dict[str, str]:
        """Get mapping of enum values to display names."""
        return {category.value: category.display_name() for category in cls}


class ErrorSeverity(Enum):
    """Severity levels for errors."""
    CRITICAL = "critical"  # Prevents analysis from running
    HIGH = "high"         # Significant impact on analysis quality
    MEDIUM = "medium"     # Moderate impact, some features unavailable
    LOW = "low"          # Minor impact, warnings only

    def display_name(self) -> str:
        """Get human-readable display name for the severity."""
        return self.value.title()

    def get_emoji(self) -> str:
        """Get emoji representation for the severity."""
        emoji_map = {
            ErrorSeverity.CRITICAL: "🚨",
            ErrorSeverity.HIGH: "⚠️",
            ErrorSeverity.MEDIUM: "⚡",
            ErrorSeverity.LOW: "ℹ️"
        }
        return emoji_map.get(self, "❓")


@dataclass
class AnalysisError:
    """Represents a single error encountered during analysis."""
    category: ErrorCategory
    severity: ErrorSeverity
    message: str
    file_path: Optional[Path] = None
    line_number: Optional[int] = None
    tool_name: Optional[str] = None
    context: Dict[str, Any] = field(default_factory=dict)
    count: int = 1
    
    def __str__(self) -> str:
        """String representation of the error."""
        parts = [f"[{self.severity.value.upper()}]"]
        if self.tool_name:
            parts.append(f"({self.tool_name})")
        if self.file_path:
            location = str(self.file_path)
            if self.line_number:
                location += f":{self.line_number}"
            parts.append(location)
        parts.append(self.message)
        if self.count > 1:
            parts.append(f"(×{self.count})")
        return " ".join(parts)


class ErrorAggregator:
    """Aggregates and categorizes errors from analysis results."""
    
    def __init__(self) -> None:
        self.errors: List[AnalysisError] = []
        self.error_counts: Dict[ErrorCategory, int] = defaultdict(int)
        self.severity_counts: Dict[ErrorSeverity, int] = defaultdict(int)
        self.tool_errors: Dict[str, List[AnalysisError]] = defaultdict(list)
        self.file_errors: Dict[Path, List[AnalysisError]] = defaultdict(list)
        
    def add_error(self, 
                  category: ErrorCategory,
                  severity: ErrorSeverity,
                  message: str,
                  file_path: Optional[Path] = None,
                  line_number: Optional[int] = None,
                  tool_name: Optional[str] = None,
                  context: Optional[Dict[str, Any]] = None) -> None:
        """Add an error to the aggregator."""
        error = AnalysisError(
            category=category,
            severity=severity,
            message=message,
            file_path=file_path,
            line_number=line_number,
            tool_name=tool_name,
            context=context or {}
        )
        
        # Check for duplicate errors and increment count
        for existing_error in self.errors:
            if (existing_error.category == error.category and
                existing_error.message == error.message and
                existing_error.file_path == error.file_path and
                existing_error.tool_name == error.tool_name):
                existing_error.count += 1
                return
        
        # Add new error
        self.errors.append(error)
        self.error_counts[category] += 1
        self.severity_counts[severity] += 1
        
        if tool_name:
            self.tool_errors[tool_name].append(error)
        if file_path:
            self.file_errors[file_path].append(error)
    
    def add_syntax_error(self, message: str, file_path: Path, line_number: Optional[int] = None) -> None:
        """Add a syntax error."""
        self.add_error(
            ErrorCategory.SYNTAX_ERROR,
            ErrorSeverity.HIGH,
            message,
            file_path=file_path,
            line_number=line_number
        )
    
    def add_import_error(self, message: str, file_path: Optional[Path] = None, tool_name: Optional[str] = None) -> None:
        """Add an import error."""
        self.add_error(
            ErrorCategory.IMPORT_ERROR,
            ErrorSeverity.MEDIUM,
            message,
            file_path=file_path,
            tool_name=tool_name
        )
    
    def add_tool_error(self, tool_name: str, message: str, severity: ErrorSeverity = ErrorSeverity.MEDIUM) -> None:
        """Add a tool-specific error."""
        self.add_error(
            ErrorCategory.TOOL_ERROR,
            severity,
            message,
            tool_name=tool_name
        )
    
    def add_file_error(self, file_path: Path, message: str, severity: ErrorSeverity = ErrorSeverity.LOW) -> None:
        """Add a file-specific error."""
        self.add_error(
            ErrorCategory.FILE_ERROR,
            severity,
            message,
            file_path=file_path
        )
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get a summary of all errors."""
        return {
            "total_errors": len(self.errors),
            "by_category": dict(self.error_counts),
            "by_severity": dict(self.severity_counts),
            "by_tool": {tool: len(errors) for tool, errors in self.tool_errors.items()},
            "affected_files": len(self.file_errors),
            "critical_errors": self.severity_counts[ErrorSeverity.CRITICAL],
            "high_errors": self.severity_counts[ErrorSeverity.HIGH]
        }
    
    def get_errors_by_severity(self, severity: ErrorSeverity) -> List[AnalysisError]:
        """Get all errors of a specific severity."""
        return [error for error in self.errors if error.severity == severity]
    
    def get_errors_by_category(self, category: ErrorCategory) -> List[AnalysisError]:
        """Get all errors of a specific category."""
        return [error for error in self.errors if error.category == category]
    
    def get_tool_errors(self, tool_name: str) -> List[AnalysisError]:
        """Get all errors for a specific tool."""
        return self.tool_errors.get(tool_name, [])
    
    def has_critical_errors(self) -> bool:
        """Check if there are any critical errors."""
        return self.severity_counts[ErrorSeverity.CRITICAL] > 0
    
    def has_blocking_errors(self) -> bool:
        """Check if there are errors that would block analysis."""
        return (self.severity_counts[ErrorSeverity.CRITICAL] > 0 or
                self.severity_counts[ErrorSeverity.HIGH] > 5)  # Threshold for too many high-severity errors
    
    def get_actionable_suggestions(self) -> List[str]:
        """Get actionable suggestions based on error patterns."""
        suggestions = []
        
        # Syntax error suggestions
        if self.error_counts[ErrorCategory.SYNTAX_ERROR] > 0:
            suggestions.append("Fix syntax errors in Python files before running analysis")
            suggestions.append("Consider using a Python linter to identify syntax issues")
        
        # Import error suggestions
        if self.error_counts[ErrorCategory.IMPORT_ERROR] > 0:
            suggestions.append("Check that all required dependencies are installed")
            suggestions.append("Verify Python path and module imports")
        
        # Tool error suggestions
        if self.error_counts[ErrorCategory.TOOL_ERROR] > 0:
            suggestions.append("Ensure all analysis tools are properly installed")
            suggestions.append("Try running with --profile minimal to use fewer tools")
        
        # File error suggestions
        if self.error_counts[ErrorCategory.FILE_ERROR] > 0:
            suggestions.append("Check file permissions and accessibility")
            suggestions.append("Verify that all files in the project are readable")
        
        # General suggestions
        if self.has_critical_errors():
            suggestions.append("Address critical errors before proceeding with analysis")
        
        if len(self.errors) > 20:
            suggestions.append("Consider fixing the most common errors first")
            suggestions.append("Use --debug flag for detailed error information")
        
        return suggestions
    
    def clear(self) -> None:
        """Clear all aggregated errors."""
        self.errors.clear()
        self.error_counts.clear()
        self.severity_counts.clear()
        self.tool_errors.clear()
        self.file_errors.clear()


# Global error aggregator instance
_global_aggregator = ErrorAggregator()


def get_error_aggregator() -> ErrorAggregator:
    """Get the global error aggregator instance."""
    return _global_aggregator


def reset_error_aggregator() -> None:
    """Reset the global error aggregator."""
    _global_aggregator.clear()


def collect_errors_from_analysis_results(results: Any) -> None:
    """
    Collect errors from analysis results and add them to the global aggregator.

    Args:
        results: Analysis results from various tools and stages
    """
    aggregator = get_error_aggregator()

    # Handle different result formats
    if isinstance(results, dict):
        # Check for direct error in results
        if "error" in results:
            aggregator.add_error(
                ErrorCategory.UNKNOWN_ERROR,
                ErrorSeverity.HIGH,
                results["error"]
            )

        # Check for tool-specific errors
        if "tools" in results:
            for tool_name, tool_result in results["tools"].items():
                if isinstance(tool_result, dict) and "error" in tool_result:
                    aggregator.add_tool_error(
                        tool_name,
                        tool_result["error"],
                        ErrorSeverity.MEDIUM
                    )

        # Check for file-specific errors
        if "files" in results:
            files_data = results["files"]

            # Handle both dictionary and list formats
            if isinstance(files_data, dict):
                # Plugin mode format: {"file_path": file_result}
                for file_path, file_result in files_data.items():
                    if isinstance(file_result, dict) and "error" in file_result:
                        aggregator.add_file_error(
                            Path(file_path),
                            file_result["error"],
                            ErrorSeverity.LOW
                        )
            elif isinstance(files_data, list):
                # VCS mode format: [file_result_objects]
                for file_result in files_data:
                    if hasattr(file_result, 'error') and file_result.error:
                        file_path = getattr(file_result, 'path', 'unknown')
                        aggregator.add_file_error(
                            Path(file_path),
                            str(file_result.error),
                            ErrorSeverity.LOW
                        )

    # Handle ProjectMetrics object
    elif hasattr(results, 'files'):
        for file_path, file_metrics in results.files.items():
            if hasattr(file_metrics, 'errors') and file_metrics.errors:
                for error in file_metrics.errors:
                    aggregator.add_file_error(
                        Path(file_path),
                        str(error),
                        ErrorSeverity.LOW
                    )


def collect_syntax_errors_from_logs() -> None:
    """
    Collect syntax errors from recent log entries.
    This function scans recent log entries for syntax error patterns.
    """
    # This is a simplified implementation - in a real system,
    # you might want to integrate with the logging system more directly
    import logging

    # Get recent log records (this is a simplified approach)
    # In practice, you'd want to integrate this more directly with the logging system
    pass
