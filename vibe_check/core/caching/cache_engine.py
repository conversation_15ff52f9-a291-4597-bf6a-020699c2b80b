"""
Multi-Level Cache Engine
========================

Comprehensive caching system with LRU memory cache, disk cache, and intelligent
cache invalidation for optimal performance in the async Vibe Check system.
"""

import asyncio
import hashlib
import json
import pickle
import time
from abc import ABC, abstractmethod
from collections import OrderedDict
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Tuple, Callable
from dataclasses import dataclass, field
import threading
import logging
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)


@dataclass
class CacheConfig:
    """Configuration for multi-level caching"""
    # Memory cache settings
    memory_cache_size: int = 1000  # Number of items
    memory_cache_ttl: float = 3600.0  # 1 hour TTL
    
    # Disk cache settings
    disk_cache_enabled: bool = True
    disk_cache_dir: Path = field(default_factory=lambda: Path(".vibe_check_cache"))
    disk_cache_size_mb: int = 500  # 500MB disk cache
    disk_cache_ttl: float = 86400.0  # 24 hours TTL
    
    # Performance settings
    async_write_enabled: bool = True
    compression_enabled: bool = True
    cache_stats_enabled: bool = True
    
    # Invalidation settings
    auto_invalidation: bool = True
    file_watch_enabled: bool = True
    dependency_tracking: bool = True


@dataclass
class CacheEntry:
    """Cache entry with metadata"""
    key: str
    value: Any
    created_at: float
    accessed_at: float
    ttl: float
    size_bytes: int = 0
    access_count: int = 0
    dependencies: List[str] = field(default_factory=list)
    
    @property
    def is_expired(self) -> bool:
        """Check if entry is expired"""
        return time.time() > (self.created_at + self.ttl)
    
    @property
    def age(self) -> float:
        """Get age in seconds"""
        return time.time() - self.created_at
    
    def touch(self):
        """Update access time and count"""
        self.accessed_at = time.time()
        self.access_count += 1


@dataclass
class CacheStats:
    """Cache statistics"""
    hits: int = 0
    misses: int = 0
    evictions: int = 0
    disk_reads: int = 0
    disk_writes: int = 0
    memory_usage_bytes: int = 0
    disk_usage_bytes: int = 0
    
    @property
    def hit_ratio(self) -> float:
        """Calculate hit ratio"""
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'hits': self.hits,
            'misses': self.misses,
            'evictions': self.evictions,
            'disk_reads': self.disk_reads,
            'disk_writes': self.disk_writes,
            'memory_usage_bytes': self.memory_usage_bytes,
            'disk_usage_bytes': self.disk_usage_bytes,
            'hit_ratio': self.hit_ratio
        }


class CacheInterface(ABC):
    """Abstract cache interface"""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        pass
    
    @abstractmethod
    async def set(self, key: str, value: Any, ttl: Optional[float] = None) -> bool:
        """Set value in cache"""
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        """Delete value from cache"""
        pass
    
    @abstractmethod
    async def clear(self) -> bool:
        """Clear all cache entries"""
        pass
    
    @abstractmethod
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache"""
        pass


class LRUMemoryCache(CacheInterface):
    """LRU memory cache implementation"""
    
    def __init__(self, config: CacheConfig):
        self.config = config
        self.cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self.lock = threading.RLock()
        self.stats = CacheStats()
        
    async def get(self, key: str) -> Optional[Any]:
        """Get value from memory cache"""
        with self.lock:
            if key in self.cache:
                entry = self.cache[key]
                
                # Check expiration
                if entry.is_expired:
                    del self.cache[key]
                    self.stats.misses += 1
                    return None
                
                # Move to end (most recently used)
                self.cache.move_to_end(key)
                entry.touch()
                
                self.stats.hits += 1
                return entry.value
            
            self.stats.misses += 1
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[float] = None) -> bool:
        """Set value in memory cache"""
        with self.lock:
            ttl = ttl or self.config.memory_cache_ttl
            
            # Calculate size (approximate)
            try:
                size_bytes = len(pickle.dumps(value))
            except Exception:
                size_bytes = 1024  # Default estimate
            
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=time.time(),
                accessed_at=time.time(),
                ttl=ttl,
                size_bytes=size_bytes
            )
            
            # Remove existing entry if present
            if key in self.cache:
                del self.cache[key]
            
            # Add new entry
            self.cache[key] = entry
            
            # Evict if over capacity
            while len(self.cache) > self.config.memory_cache_size:
                oldest_key = next(iter(self.cache))
                del self.cache[oldest_key]
                self.stats.evictions += 1
            
            # Update memory usage
            self.stats.memory_usage_bytes = sum(
                entry.size_bytes for entry in self.cache.values()
            )
            
            return True
    
    async def delete(self, key: str) -> bool:
        """Delete value from memory cache"""
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                self.stats.memory_usage_bytes = sum(
                    entry.size_bytes for entry in self.cache.values()
                )
                return True
            return False
    
    async def clear(self) -> bool:
        """Clear all memory cache entries"""
        with self.lock:
            self.cache.clear()
            self.stats.memory_usage_bytes = 0
            return True
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in memory cache"""
        with self.lock:
            if key in self.cache:
                entry = self.cache[key]
                if entry.is_expired:
                    del self.cache[key]
                    return False
                return True
            return False
    
    def get_stats(self) -> CacheStats:
        """Get cache statistics"""
        return self.stats


class DiskCache(CacheInterface):
    """Disk-based cache implementation"""
    
    def __init__(self, config: CacheConfig):
        self.config = config
        self.cache_dir = config.disk_cache_dir
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.executor = ThreadPoolExecutor(max_workers=4)
        self.stats = CacheStats()
        
    def _get_cache_path(self, key: str) -> Path:
        """Get cache file path for key"""
        key_hash = hashlib.sha256(key.encode()).hexdigest()
        return self.cache_dir / f"{key_hash}.cache"
    
    def _get_metadata_path(self, key: str) -> Path:
        """Get metadata file path for key"""
        key_hash = hashlib.sha256(key.encode()).hexdigest()
        return self.cache_dir / f"{key_hash}.meta"
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from disk cache"""
        if not self.config.disk_cache_enabled:
            return None
        
        cache_path = self._get_cache_path(key)
        meta_path = self._get_metadata_path(key)
        
        if not cache_path.exists() or not meta_path.exists():
            self.stats.misses += 1
            return None
        
        try:
            # Read metadata
            loop = asyncio.get_event_loop()
            metadata = await loop.run_in_executor(
                self.executor, self._read_metadata_sync, meta_path
            )
            
            # Check expiration
            if time.time() > (metadata['created_at'] + metadata['ttl']):
                await self.delete(key)
                self.stats.misses += 1
                return None
            
            # Read value
            value = await loop.run_in_executor(
                self.executor, self._read_value_sync, cache_path
            )
            
            # Update access time
            metadata['accessed_at'] = time.time()
            metadata['access_count'] += 1
            await loop.run_in_executor(
                self.executor, self._write_metadata_sync, meta_path, metadata
            )
            
            self.stats.hits += 1
            self.stats.disk_reads += 1
            return value
            
        except Exception as e:
            logger.warning(f"Error reading from disk cache: {e}")
            self.stats.misses += 1
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[float] = None) -> bool:
        """Set value in disk cache"""
        if not self.config.disk_cache_enabled:
            return False
        
        ttl = ttl or self.config.disk_cache_ttl
        cache_path = self._get_cache_path(key)
        meta_path = self._get_metadata_path(key)
        
        try:
            # Prepare metadata
            metadata = {
                'key': key,
                'created_at': time.time(),
                'accessed_at': time.time(),
                'ttl': ttl,
                'access_count': 1
            }
            
            # Write to disk asynchronously
            loop = asyncio.get_event_loop()
            
            if self.config.async_write_enabled:
                # Async write
                await asyncio.gather(
                    loop.run_in_executor(
                        self.executor, self._write_value_sync, cache_path, value
                    ),
                    loop.run_in_executor(
                        self.executor, self._write_metadata_sync, meta_path, metadata
                    )
                )
            else:
                # Sync write
                await loop.run_in_executor(
                    self.executor, self._write_value_sync, cache_path, value
                )
                await loop.run_in_executor(
                    self.executor, self._write_metadata_sync, meta_path, metadata
                )
            
            self.stats.disk_writes += 1
            return True
            
        except Exception as e:
            logger.error(f"Error writing to disk cache: {e}")
            return False
    
    def _read_metadata_sync(self, meta_path: Path) -> Dict[str, Any]:
        """Synchronously read metadata"""
        with open(meta_path, 'r') as f:
            return json.load(f)
    
    def _write_metadata_sync(self, meta_path: Path, metadata: Dict[str, Any]):
        """Synchronously write metadata"""
        with open(meta_path, 'w') as f:
            json.dump(metadata, f)
    
    def _read_value_sync(self, cache_path: Path) -> Any:
        """Synchronously read value"""
        with open(cache_path, 'rb') as f:
            if self.config.compression_enabled:
                import gzip
                return pickle.loads(gzip.decompress(f.read()))
            else:
                return pickle.load(f)
    
    def _write_value_sync(self, cache_path: Path, value: Any):
        """Synchronously write value"""
        with open(cache_path, 'wb') as f:
            if self.config.compression_enabled:
                import gzip
                compressed_data = gzip.compress(pickle.dumps(value))
                f.write(compressed_data)
            else:
                pickle.dump(value, f)
    
    async def delete(self, key: str) -> bool:
        """Delete value from disk cache"""
        cache_path = self._get_cache_path(key)
        meta_path = self._get_metadata_path(key)
        
        try:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                self.executor, self._delete_files_sync, cache_path, meta_path
            )
            return True
        except Exception as e:
            logger.warning(f"Error deleting from disk cache: {e}")
            return False
    
    def _delete_files_sync(self, cache_path: Path, meta_path: Path):
        """Synchronously delete files"""
        cache_path.unlink(missing_ok=True)
        meta_path.unlink(missing_ok=True)
    
    async def clear(self) -> bool:
        """Clear all disk cache entries"""
        try:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(self.executor, self._clear_sync)
            return True
        except Exception as e:
            logger.error(f"Error clearing disk cache: {e}")
            return False
    
    def _clear_sync(self):
        """Synchronously clear cache directory"""
        import shutil
        if self.cache_dir.exists():
            shutil.rmtree(self.cache_dir)
            self.cache_dir.mkdir(parents=True, exist_ok=True)
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in disk cache"""
        cache_path = self._get_cache_path(key)
        meta_path = self._get_metadata_path(key)
        
        if not cache_path.exists() or not meta_path.exists():
            return False
        
        try:
            loop = asyncio.get_event_loop()
            metadata = await loop.run_in_executor(
                self.executor, self._read_metadata_sync, meta_path
            )
            
            # Check expiration
            if time.time() > (metadata['created_at'] + metadata['ttl']):
                await self.delete(key)
                return False
            
            return True
        except Exception:
            return False
    
    def cleanup(self):
        """Cleanup resources"""
        self.executor.shutdown(wait=False)


class MultiLevelCache(CacheInterface):
    """Multi-level cache combining memory and disk caches"""

    def __init__(self, config: CacheConfig):
        self.config = config
        self.memory_cache = LRUMemoryCache(config)
        self.disk_cache = DiskCache(config) if config.disk_cache_enabled else None
        self.combined_stats = CacheStats()

    async def get(self, key: str) -> Optional[Any]:
        """Get value from multi-level cache (memory first, then disk)"""
        # Try memory cache first
        value = await self.memory_cache.get(key)
        if value is not None:
            self.combined_stats.hits += 1
            return value

        # Try disk cache if enabled
        if self.disk_cache:
            value = await self.disk_cache.get(key)
            if value is not None:
                # Promote to memory cache
                await self.memory_cache.set(key, value)
                self.combined_stats.hits += 1
                return value

        self.combined_stats.misses += 1
        return None

    async def set(self, key: str, value: Any, ttl: Optional[float] = None) -> bool:
        """Set value in multi-level cache"""
        # Set in memory cache
        memory_success = await self.memory_cache.set(key, value, ttl)

        # Set in disk cache if enabled
        disk_success = True
        if self.disk_cache:
            disk_success = await self.disk_cache.set(key, value, ttl)

        return memory_success and disk_success

    async def delete(self, key: str) -> bool:
        """Delete value from multi-level cache"""
        memory_success = await self.memory_cache.delete(key)

        disk_success = True
        if self.disk_cache:
            disk_success = await self.disk_cache.delete(key)

        return memory_success or disk_success

    async def clear(self) -> bool:
        """Clear all cache levels"""
        memory_success = await self.memory_cache.clear()

        disk_success = True
        if self.disk_cache:
            disk_success = await self.disk_cache.clear()

        return memory_success and disk_success

    async def exists(self, key: str) -> bool:
        """Check if key exists in any cache level"""
        if await self.memory_cache.exists(key):
            return True

        if self.disk_cache and await self.disk_cache.exists(key):
            return True

        return False

    def get_combined_stats(self) -> Dict[str, Any]:
        """Get combined statistics from all cache levels"""
        memory_stats = self.memory_cache.get_stats()
        disk_stats = self.disk_cache.stats if self.disk_cache else CacheStats()

        return {
            'memory': memory_stats.to_dict(),
            'disk': disk_stats.to_dict(),
            'combined': {
                'total_hits': memory_stats.hits + disk_stats.hits,
                'total_misses': memory_stats.misses + disk_stats.misses,
                'total_hit_ratio': (memory_stats.hits + disk_stats.hits) /
                                 max(1, memory_stats.hits + disk_stats.hits + memory_stats.misses + disk_stats.misses),
                'memory_usage_mb': memory_stats.memory_usage_bytes / (1024 * 1024),
                'disk_usage_mb': disk_stats.disk_usage_bytes / (1024 * 1024)
            }
        }

    def cleanup(self):
        """Cleanup all cache resources"""
        if self.disk_cache:
            self.disk_cache.cleanup()
