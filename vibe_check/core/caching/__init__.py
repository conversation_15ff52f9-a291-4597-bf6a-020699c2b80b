"""
Multi-Level Caching System
==========================

Comprehensive caching system for Vibe Check with LRU memory cache,
disk cache, intelligent invalidation, and seamless integration with
the async analysis engine.

This module provides:
- Multi-level caching (memory + disk)
- Intelligent cache invalidation with dependency tracking
- File system watching for automatic invalidation
- Cached analysis engine with optimal performance
- Comprehensive cache statistics and monitoring
"""

from .cache_engine import (
    CacheConfig,
    CacheEntry,
    CacheStats,
    CacheInterface,
    LRUMemoryCache,
    DiskCache,
    MultiLevelCache,
)

from .cache_invalidation import (
    InvalidationRule,
    DependencyGraph,
    CacheInvalidator,
    SmartCacheManager,
)

from .cached_analyzer import (
    CachedAnalysisConfig,
    CachedAsyncAnalysisEngine,
    analyze_project_cached,
    analyze_project_cached_sync,
)

__all__ = [
    # Cache Engine
    'CacheConfig',
    'CacheEntry',
    'CacheStats',
    'CacheInterface',
    'LRUMemoryCache',
    'DiskCache',
    'MultiLevelCache',
    
    # Cache Invalidation
    'InvalidationRule',
    'DependencyGraph',
    'CacheInvalidator',
    'SmartCacheManager',
    
    # Cached Analysis
    'CachedAnalysisConfig',
    'CachedAsyncAnalysisEngine',
    'analyze_project_cached',
    'analyze_project_cached_sync',
]


def create_default_cache(cache_dir: str = ".vibe_check_cache") -> MultiLevelCache:
    """Create a default multi-level cache configuration"""
    from pathlib import Path
    
    config = CacheConfig(
        memory_cache_size=1000,
        memory_cache_ttl=3600.0,  # 1 hour
        disk_cache_enabled=True,
        disk_cache_dir=Path(cache_dir),
        disk_cache_size_mb=500,
        disk_cache_ttl=86400.0,  # 24 hours
        async_write_enabled=True,
        compression_enabled=True,
        cache_stats_enabled=True,
        auto_invalidation=True,
        file_watch_enabled=True,
        dependency_tracking=True
    )
    
    return MultiLevelCache(config)


def create_smart_cache_manager(cache_dir: str = ".vibe_check_cache") -> SmartCacheManager:
    """Create a smart cache manager with default configuration"""
    cache = create_default_cache(cache_dir)
    
    config = CacheConfig(
        memory_cache_size=1000,
        memory_cache_ttl=3600.0,
        disk_cache_enabled=True,
        disk_cache_dir=Path(cache_dir),
        auto_invalidation=True,
        file_watch_enabled=True,
        dependency_tracking=True
    )
    
    return SmartCacheManager(cache, config)


def create_cached_analysis_engine(cache_dir: str = ".vibe_check_cache") -> CachedAsyncAnalysisEngine:
    """Create a cached analysis engine with default configuration"""
    config = CachedAnalysisConfig(
        # Analysis settings
        max_workers=4,
        max_concurrent_files=50,
        enable_streaming=True,
        
        # Cache settings
        enable_file_cache=True,
        enable_project_cache=True,
        enable_incremental_cache=True,
        
        # Cache TTL
        file_cache_ttl=3600.0,  # 1 hour
        project_cache_ttl=1800.0,  # 30 minutes
        incremental_cache_ttl=7200.0,  # 2 hours
        
        # Invalidation
        auto_invalidate_on_change=True,
        watch_project_files=True,
        dependency_tracking=True,
        
        # Performance
        cache_compression=True,
        cache_async_writes=True
    )
    
    return CachedAsyncAnalysisEngine(config)
