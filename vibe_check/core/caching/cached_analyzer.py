"""
Cached Analysis Engine
=====================

Integration of the multi-level caching system with the async analysis engine
for optimal performance and intelligent cache management.
"""

import asyncio
import hashlib
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
import logging

from vibe_check.core.async_unified_analyzer import AsyncUnifiedAnalysisEngine, AsyncAnalysisConfig
from vibe_check.core.models import AnalysisResult, FileMetrics, ProjectMetrics
from .cache_engine import MultiLevelCache, CacheConfig
from .cache_invalidation import SmartCacheManager

logger = logging.getLogger(__name__)


@dataclass
class CachedAnalysisConfig(AsyncAnalysisConfig):
    """Configuration for cached analysis"""
    # Cache settings
    enable_file_cache: bool = True
    enable_project_cache: bool = True
    enable_incremental_cache: bool = True
    
    # Cache TTL settings
    file_cache_ttl: float = 3600.0  # 1 hour for file analysis
    project_cache_ttl: float = 1800.0  # 30 minutes for project analysis
    incremental_cache_ttl: float = 7200.0  # 2 hours for incremental results
    
    # Cache invalidation
    auto_invalidate_on_change: bool = True
    watch_project_files: bool = True
    dependency_tracking: bool = True
    
    # Performance tuning
    cache_compression: bool = True
    cache_async_writes: bool = True


class CachedAsyncAnalysisEngine:
    """Async analysis engine with intelligent caching"""
    
    def __init__(self, config: Optional[CachedAnalysisConfig] = None):
        self.config = config or CachedAnalysisConfig()
        
        # Initialize base analysis engine
        self.analysis_engine = AsyncUnifiedAnalysisEngine(self.config)
        
        # Initialize caching system
        cache_config = CacheConfig(
            memory_cache_size=1000,
            memory_cache_ttl=self.config.file_cache_ttl,
            disk_cache_enabled=True,
            disk_cache_ttl=self.config.project_cache_ttl,
            compression_enabled=self.config.cache_compression,
            async_write_enabled=self.config.cache_async_writes,
            auto_invalidation=self.config.auto_invalidate_on_change,
            file_watch_enabled=self.config.watch_project_files,
            dependency_tracking=self.config.dependency_tracking
        )
        
        self.cache = MultiLevelCache(cache_config)
        self.cache_manager = SmartCacheManager(self.cache, cache_config)
        
        # Cache statistics
        self.cache_hits = 0
        self.cache_misses = 0
        self.cache_saves = 0
        
    def _get_file_cache_key(self, file_path: Path) -> str:
        """Generate cache key for file analysis"""
        # Include file path and modification time for cache invalidation
        try:
            mtime = file_path.stat().st_mtime
            content_hash = f"{file_path}:{mtime}"
        except OSError:
            content_hash = str(file_path)
        
        return f"file_analysis:{hashlib.sha256(content_hash.encode()).hexdigest()}"
    
    def _get_project_cache_key(self, project_path: Path) -> str:
        """Generate cache key for project analysis"""
        # Create a hash based on project path and configuration
        config_hash = hashlib.sha256(str(self.config.__dict__).encode()).hexdigest()[:16]
        path_hash = hashlib.sha256(str(project_path).encode()).hexdigest()[:16]
        return f"project_analysis:{path_hash}:{config_hash}"
    
    def _get_incremental_cache_key(self, project_path: Path, file_subset: List[Path]) -> str:
        """Generate cache key for incremental analysis"""
        # Hash the subset of files for incremental caching
        file_list = sorted(str(f) for f in file_subset)
        files_hash = hashlib.sha256(":".join(file_list).encode()).hexdigest()[:16]
        path_hash = hashlib.sha256(str(project_path).encode()).hexdigest()[:16]
        return f"incremental_analysis:{path_hash}:{files_hash}"
    
    async def analyze_file_cached(self, file_path: Path) -> Optional[FileMetrics]:
        """Analyze single file with caching"""
        if not self.config.enable_file_cache:
            # Direct analysis without caching
            async with self.analysis_engine._get_file_processor() as processor:
                return await processor.process_file(file_path)
        
        cache_key = self._get_file_cache_key(file_path)
        
        # Try to get from cache
        cached_result = await self.cache_manager.get(cache_key)
        if cached_result is not None:
            self.cache_hits += 1
            logger.debug(f"Cache hit for file: {file_path}")
            return cached_result
        
        # Cache miss - perform analysis
        self.cache_misses += 1
        logger.debug(f"Cache miss for file: {file_path}")
        
        async with self.analysis_engine._get_file_processor() as processor:
            result = await processor.process_file(file_path)
        
        # Cache the result
        if result is not None:
            dependencies = [str(file_path)]  # File depends on itself
            await self.cache_manager.set(
                cache_key, 
                result, 
                ttl=self.config.file_cache_ttl,
                dependencies=dependencies
            )
            self.cache_saves += 1
        
        return result
    
    async def analyze_project_cached(self, path: Union[str, Path]) -> AnalysisResult:
        """Analyze project with intelligent caching"""
        project_path = Path(path)
        
        if not self.config.enable_project_cache:
            # Direct analysis without project-level caching
            return await self._analyze_project_direct(project_path)
        
        cache_key = self._get_project_cache_key(project_path)
        
        # Try to get from cache
        cached_result = await self.cache_manager.get(cache_key)
        if cached_result is not None:
            self.cache_hits += 1
            logger.info(f"Cache hit for project: {project_path}")
            return cached_result
        
        # Cache miss - perform analysis
        self.cache_misses += 1
        logger.info(f"Cache miss for project: {project_path}")
        
        result = await self._analyze_project_direct(project_path)
        
        # Cache the result
        if result.files_analyzed > 0:
            # Project depends on all analyzed files
            dependencies = [fm.file_path for fm in result.file_metrics]
            await self.cache_manager.set(
                cache_key,
                result,
                ttl=self.config.project_cache_ttl,
                dependencies=dependencies
            )
            self.cache_saves += 1
            
            # Setup file watching for auto-invalidation
            if self.config.watch_project_files:
                self.cache_manager.add_watch_path(project_path)
        
        return result
    
    async def _analyze_project_direct(self, project_path: Path) -> AnalysisResult:
        """Direct project analysis with file-level caching"""
        start_time = time.time()
        
        logger.info(f"Starting cached analysis of: {project_path}")
        
        # Discover files
        files = []
        async for file_path in self.analysis_engine.discover_files_async(project_path):
            files.append(file_path)
        
        # Analyze files with caching
        file_metrics = []
        
        if self.config.enable_file_cache:
            # Use cached file analysis
            tasks = [self.analyze_file_cached(file_path) for file_path in files]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for result in results:
                if isinstance(result, FileMetrics):
                    file_metrics.append(result)
                elif isinstance(result, Exception):
                    logger.error(f"File analysis error: {result}")
        else:
            # Use direct analysis
            file_metrics = await self.analysis_engine.analyze_files_batch(files)
        
        # Calculate directory and project metrics
        directory_metrics = await self.analysis_engine._calculate_directory_metrics(file_metrics)
        project_metrics = self.analysis_engine._calculate_project_metrics(file_metrics, directory_metrics)
        
        analysis_time = time.time() - start_time
        
        result = AnalysisResult(
            project_metrics=project_metrics,
            file_metrics=file_metrics,
            directory_metrics=directory_metrics,
            analysis_time=analysis_time,
            files_analyzed=len(file_metrics)
        )
        
        logger.info(f"Cached analysis completed in {analysis_time:.2f}s")
        logger.info(f"Cache stats - Hits: {self.cache_hits}, Misses: {self.cache_misses}, Saves: {self.cache_saves}")
        
        return result
    
    async def analyze_incremental(self, project_path: Path, changed_files: List[Path]) -> AnalysisResult:
        """Perform incremental analysis on changed files only"""
        if not self.config.enable_incremental_cache:
            # Fall back to full analysis
            return await self.analyze_project_cached(project_path)
        
        cache_key = self._get_incremental_cache_key(project_path, changed_files)
        
        # Try to get from cache
        cached_result = await self.cache_manager.get(cache_key)
        if cached_result is not None:
            self.cache_hits += 1
            logger.info(f"Incremental cache hit for {len(changed_files)} files")
            return cached_result
        
        # Cache miss - analyze changed files
        self.cache_misses += 1
        logger.info(f"Incremental analysis of {len(changed_files)} changed files")
        
        # Analyze only changed files
        file_metrics = []
        for file_path in changed_files:
            metrics = await self.analyze_file_cached(file_path)
            if metrics:
                file_metrics.append(metrics)
        
        # For incremental analysis, we'd need to merge with existing results
        # This is a simplified version that just analyzes the changed files
        directory_metrics = await self.analysis_engine._calculate_directory_metrics(file_metrics)
        project_metrics = self.analysis_engine._calculate_project_metrics(file_metrics, directory_metrics)
        
        result = AnalysisResult(
            project_metrics=project_metrics,
            file_metrics=file_metrics,
            directory_metrics=directory_metrics,
            analysis_time=0.0,  # Would be calculated properly
            files_analyzed=len(file_metrics)
        )
        
        # Cache incremental result
        dependencies = [str(f) for f in changed_files]
        await self.cache_manager.set(
            cache_key,
            result,
            ttl=self.config.incremental_cache_ttl,
            dependencies=dependencies
        )
        self.cache_saves += 1
        
        return result
    
    async def invalidate_file_cache(self, file_path: Path) -> int:
        """Invalidate cache for specific file"""
        cache_key = self._get_file_cache_key(file_path)
        return await self.cache_manager.invalidate(cache_key)
    
    async def invalidate_project_cache(self, project_path: Path) -> int:
        """Invalidate cache for entire project"""
        cache_key = self._get_project_cache_key(project_path)
        return await self.cache_manager.invalidate(cache_key, cascade=True)
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics"""
        manager_stats = self.cache_manager.get_stats()
        
        return {
            'engine_stats': {
                'cache_hits': self.cache_hits,
                'cache_misses': self.cache_misses,
                'cache_saves': self.cache_saves,
                'hit_ratio': self.cache_hits / max(1, self.cache_hits + self.cache_misses)
            },
            'cache_manager_stats': manager_stats
        }
    
    def cleanup(self):
        """Cleanup all resources"""
        self.cache_manager.cleanup()


# Convenience functions
async def analyze_project_cached(path: Union[str, Path], config: Optional[CachedAnalysisConfig] = None) -> AnalysisResult:
    """Cached project analysis function"""
    engine = CachedAsyncAnalysisEngine(config)
    try:
        return await engine.analyze_project_cached(path)
    finally:
        engine.cleanup()


def analyze_project_cached_sync(path: Union[str, Path], config: Optional[CachedAnalysisConfig] = None) -> AnalysisResult:
    """Synchronous wrapper for cached analysis"""
    return asyncio.run(analyze_project_cached(path, config))
