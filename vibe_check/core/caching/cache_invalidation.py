"""
Intelligent Cache Invalidation System
====================================

Advanced cache invalidation with dependency tracking, file watching,
and smart invalidation strategies for optimal cache coherence.
"""

import asyncio
import hashlib
import time
from pathlib import Path
from typing import Dict, List, Set, Optional, Callable, Any
from dataclasses import dataclass, field
from collections import defaultdict
import logging
try:
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
    WATCHDOG_AVAILABLE = True
except ImportError:
    WATCHDOG_AVAILABLE = False
    Observer = None
    FileSystemEventHandler = None

from .cache_engine import CacheInterface, CacheConfig

logger = logging.getLogger(__name__)


@dataclass
class InvalidationRule:
    """Cache invalidation rule"""
    pattern: str  # File pattern or dependency pattern
    cache_keys: List[str]  # Cache keys to invalidate
    ttl_override: Optional[float] = None  # Override TTL for matched keys
    cascade: bool = True  # Whether to cascade invalidation
    
    def matches(self, path: str) -> bool:
        """Check if path matches this rule"""
        import fnmatch
        return fnmatch.fnmatch(path, self.pattern)


@dataclass
class DependencyGraph:
    """Dependency tracking graph"""
    dependencies: Dict[str, Set[str]] = field(default_factory=lambda: defaultdict(set))
    dependents: Dict[str, Set[str]] = field(default_factory=lambda: defaultdict(set))
    
    def add_dependency(self, cache_key: str, dependency: str):
        """Add dependency relationship"""
        self.dependencies[cache_key].add(dependency)
        self.dependents[dependency].add(cache_key)
    
    def remove_dependency(self, cache_key: str, dependency: str):
        """Remove dependency relationship"""
        self.dependencies[cache_key].discard(dependency)
        self.dependents[dependency].discard(cache_key)
    
    def get_dependents(self, dependency: str) -> Set[str]:
        """Get all cache keys that depend on this dependency"""
        return self.dependents.get(dependency, set())
    
    def get_dependencies(self, cache_key: str) -> Set[str]:
        """Get all dependencies for this cache key"""
        return self.dependencies.get(cache_key, set())
    
    def get_transitive_dependents(self, dependency: str) -> Set[str]:
        """Get all transitive dependents (recursive)"""
        visited = set()
        to_visit = {dependency}
        dependents = set()
        
        while to_visit:
            current = to_visit.pop()
            if current in visited:
                continue
            
            visited.add(current)
            current_dependents = self.dependents.get(current, set())
            dependents.update(current_dependents)
            to_visit.update(current_dependents)
        
        return dependents


if WATCHDOG_AVAILABLE:
    class FileWatcher(FileSystemEventHandler):
        """File system watcher for cache invalidation"""

        def __init__(self, invalidator: 'CacheInvalidator'):
            self.invalidator = invalidator
else:
    class FileWatcher:
        """Dummy file watcher when watchdog is not available"""

        def __init__(self, invalidator: 'CacheInvalidator'):
            self.invalidator = invalidator
        
    def on_modified(self, event):
        """Handle file modification events"""
        if not event.is_directory:
            asyncio.create_task(
                self.invalidator.handle_file_change(event.src_path, 'modified')
            )
    
    def on_created(self, event):
        """Handle file creation events"""
        if not event.is_directory:
            asyncio.create_task(
                self.invalidator.handle_file_change(event.src_path, 'created')
            )
    
    def on_deleted(self, event):
        """Handle file deletion events"""
        if not event.is_directory:
            asyncio.create_task(
                self.invalidator.handle_file_change(event.src_path, 'deleted')
            )


class CacheInvalidator:
    """Intelligent cache invalidation manager"""
    
    def __init__(self, cache: CacheInterface, config: CacheConfig):
        self.cache = cache
        self.config = config
        self.dependency_graph = DependencyGraph()
        self.invalidation_rules: List[InvalidationRule] = []
        self.file_observer: Optional[Observer] = None
        self.file_watcher: Optional[FileWatcher] = None
        self.watch_paths: Set[Path] = set()
        
        # Statistics
        self.invalidations_count = 0
        self.cascade_invalidations_count = 0
        self.file_change_events = 0
        
        if config.file_watch_enabled:
            self._setup_file_watching()
    
    def _setup_file_watching(self):
        """Setup file system watching"""
        if not WATCHDOG_AVAILABLE:
            logger.warning("watchdog not available, file watching disabled")
            self.config.file_watch_enabled = False
            return

        try:
            self.file_observer = Observer()
            self.file_watcher = FileWatcher(self)
        except Exception as e:
            logger.warning(f"Failed to setup file watching: {e}")
            self.config.file_watch_enabled = False
    
    def add_watch_path(self, path: Path):
        """Add path to file watching"""
        if not self.config.file_watch_enabled or not self.file_observer:
            return
        
        if path not in self.watch_paths:
            self.watch_paths.add(path)
            self.file_observer.schedule(
                self.file_watcher, str(path), recursive=True
            )
            
            if not self.file_observer.is_alive():
                self.file_observer.start()
    
    def remove_watch_path(self, path: Path):
        """Remove path from file watching"""
        if path in self.watch_paths:
            self.watch_paths.discard(path)
            # Note: watchdog doesn't have easy way to remove specific watches
            # Would need to restart observer with remaining paths
    
    def add_invalidation_rule(self, rule: InvalidationRule):
        """Add invalidation rule"""
        self.invalidation_rules.append(rule)
    
    def add_dependency(self, cache_key: str, dependency: str):
        """Add dependency for cache key"""
        if self.config.dependency_tracking:
            self.dependency_graph.add_dependency(cache_key, dependency)
    
    def remove_dependency(self, cache_key: str, dependency: str):
        """Remove dependency for cache key"""
        if self.config.dependency_tracking:
            self.dependency_graph.remove_dependency(cache_key, dependency)
    
    async def invalidate_key(self, cache_key: str, cascade: bool = True) -> int:
        """Invalidate specific cache key"""
        invalidated_count = 0
        
        # Invalidate the key itself
        if await self.cache.exists(cache_key):
            await self.cache.delete(cache_key)
            invalidated_count += 1
            self.invalidations_count += 1
        
        # Cascade invalidation to dependents
        if cascade and self.config.dependency_tracking:
            dependents = self.dependency_graph.get_transitive_dependents(cache_key)
            for dependent in dependents:
                if await self.cache.exists(dependent):
                    await self.cache.delete(dependent)
                    invalidated_count += 1
                    self.cascade_invalidations_count += 1
        
        return invalidated_count
    
    async def invalidate_pattern(self, pattern: str) -> int:
        """Invalidate cache keys matching pattern"""
        # This would require enumerating cache keys, which isn't efficient
        # In practice, we'd maintain a key registry or use rule-based invalidation
        logger.warning(f"Pattern invalidation not implemented: {pattern}")
        return 0
    
    async def invalidate_dependency(self, dependency: str) -> int:
        """Invalidate all cache keys that depend on this dependency"""
        if not self.config.dependency_tracking:
            return 0
        
        dependents = self.dependency_graph.get_transitive_dependents(dependency)
        invalidated_count = 0
        
        for cache_key in dependents:
            if await self.cache.exists(cache_key):
                await self.cache.delete(cache_key)
                invalidated_count += 1
                self.invalidations_count += 1
        
        return invalidated_count
    
    async def handle_file_change(self, file_path: str, event_type: str):
        """Handle file system change event"""
        self.file_change_events += 1
        
        # Apply invalidation rules
        for rule in self.invalidation_rules:
            if rule.matches(file_path):
                for cache_key in rule.cache_keys:
                    await self.invalidate_key(cache_key, rule.cascade)
        
        # Invalidate based on file path as dependency
        if self.config.dependency_tracking:
            await self.invalidate_dependency(file_path)
    
    async def cleanup_expired(self) -> int:
        """Clean up expired cache entries (would need cache enumeration)"""
        # This would require cache enumeration capability
        # For now, we rely on lazy expiration checking
        logger.info("Expired cleanup would require cache enumeration")
        return 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get invalidation statistics"""
        return {
            'invalidations_count': self.invalidations_count,
            'cascade_invalidations_count': self.cascade_invalidations_count,
            'file_change_events': self.file_change_events,
            'dependency_count': len(self.dependency_graph.dependencies),
            'rule_count': len(self.invalidation_rules),
            'watch_paths_count': len(self.watch_paths)
        }
    
    def cleanup(self):
        """Cleanup invalidator resources"""
        if self.file_observer and self.file_observer.is_alive():
            self.file_observer.stop()
            self.file_observer.join()


class SmartCacheManager:
    """Smart cache manager with invalidation"""
    
    def __init__(self, cache: CacheInterface, config: CacheConfig):
        self.cache = cache
        self.config = config
        self.invalidator = CacheInvalidator(cache, config)
        
    async def get(self, key: str) -> Optional[Any]:
        """Get value with dependency tracking"""
        return await self.cache.get(key)
    
    async def set(self, key: str, value: Any, ttl: Optional[float] = None, 
                dependencies: Optional[List[str]] = None) -> bool:
        """Set value with dependency tracking"""
        success = await self.cache.set(key, value, ttl)
        
        # Add dependencies
        if success and dependencies and self.config.dependency_tracking:
            for dependency in dependencies:
                self.invalidator.add_dependency(key, dependency)
        
        return success
    
    async def delete(self, key: str) -> bool:
        """Delete value and clean up dependencies"""
        success = await self.cache.delete(key)
        
        # Clean up dependencies
        if success and self.config.dependency_tracking:
            dependencies = self.invalidator.dependency_graph.get_dependencies(key)
            for dependency in dependencies:
                self.invalidator.remove_dependency(key, dependency)
        
        return success
    
    async def invalidate(self, key: str, cascade: bool = True) -> int:
        """Invalidate cache key"""
        return await self.invalidator.invalidate_key(key, cascade)
    
    async def invalidate_dependency(self, dependency: str) -> int:
        """Invalidate by dependency"""
        return await self.invalidator.invalidate_dependency(dependency)
    
    def add_invalidation_rule(self, pattern: str, cache_keys: List[str], 
                            cascade: bool = True):
        """Add invalidation rule"""
        rule = InvalidationRule(
            pattern=pattern,
            cache_keys=cache_keys,
            cascade=cascade
        )
        self.invalidator.add_invalidation_rule(rule)
    
    def add_watch_path(self, path: Path):
        """Add file watch path"""
        self.invalidator.add_watch_path(path)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics"""
        cache_stats = {}
        if hasattr(self.cache, 'get_combined_stats'):
            cache_stats = self.cache.get_combined_stats()
        elif hasattr(self.cache, 'get_stats'):
            cache_stats = {'cache': self.cache.get_stats().to_dict()}
        
        invalidation_stats = self.invalidator.get_stats()
        
        return {
            'cache': cache_stats,
            'invalidation': invalidation_stats
        }
    
    def cleanup(self):
        """Cleanup all resources"""
        self.invalidator.cleanup()
        if hasattr(self.cache, 'cleanup'):
            self.cache.cleanup()
