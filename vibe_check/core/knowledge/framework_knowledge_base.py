"""
Framework Knowledge Base System

This module provides a flexible, extensible system for storing and managing
framework expertise, best practices, and rules. It supports external
configuration files, versioning, and community contributions.
"""

import json
import logging
import yaml
from pathlib import Path
from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


@dataclass
class FrameworkRule:
    """Represents a single framework rule or best practice."""
    
    id: str
    name: str
    description: str
    severity: str  # 'error', 'warning', 'info'
    category: str  # 'security', 'performance', 'maintainability', 'style'
    framework: str
    framework_version: Optional[str] = None
    
    # Rule logic
    pattern_type: str = "ast"  # 'ast', 'regex', 'import', 'file_structure'
    pattern: Dict[str, Any] = field(default_factory=dict)
    
    # Metadata
    author: str = "VibeCheck"
    created_date: str = field(default_factory=lambda: datetime.now().isoformat())
    updated_date: str = field(default_factory=lambda: datetime.now().isoformat())
    version: str = "1.0.0"
    tags: List[str] = field(default_factory=list)
    
    # Guidance
    message: str = ""
    suggestion: str = ""
    documentation_url: Optional[str] = None
    examples: List[Dict[str, str]] = field(default_factory=list)  # good/bad examples


@dataclass
class FrameworkKnowledge:
    """Complete knowledge about a framework."""
    
    name: str
    version: str
    description: str
    
    # Detection patterns
    detection: Dict[str, Any] = field(default_factory=dict)
    
    # Rules and best practices
    rules: List[FrameworkRule] = field(default_factory=list)
    
    # General recommendations
    recommendations: List[str] = field(default_factory=list)
    
    # Architecture patterns
    patterns: List[Dict[str, Any]] = field(default_factory=list)
    
    # Metadata
    maintainer: str = "VibeCheck Team"
    last_updated: str = field(default_factory=lambda: datetime.now().isoformat())
    source_url: Optional[str] = None


class FrameworkKnowledgeBase:
    """
    Manages framework knowledge from multiple sources.
    """
    
    def __init__(self, knowledge_dir: Optional[Path] = None):
        self.knowledge_dir = knowledge_dir or Path(__file__).parent / "frameworks"
        self.knowledge_dir.mkdir(parents=True, exist_ok=True)
        
        self.frameworks: Dict[str, FrameworkKnowledge] = {}
        self.rules_by_framework: Dict[str, List[FrameworkRule]] = {}
        self.rules_by_category: Dict[str, List[FrameworkRule]] = {}
        
        self._load_all_knowledge()
    
    def _load_all_knowledge(self) -> None:
        """Load all framework knowledge from various sources."""
        # Load built-in knowledge
        self._load_builtin_knowledge()
        
        # Load external knowledge files
        self._load_external_knowledge()
        
        # Load community contributions
        self._load_community_knowledge()
        
        # Index rules for fast lookup
        self._index_rules()
    
    def _load_builtin_knowledge(self) -> None:
        """Load built-in framework knowledge."""
        builtin_frameworks = [
            self._create_django_knowledge(),
            self._create_flask_knowledge(),
            self._create_fastapi_knowledge(),
            self._create_pytest_knowledge(),
            self._create_pandas_knowledge()
        ]
        
        for framework in builtin_frameworks:
            self.frameworks[framework.name.lower()] = framework
    
    def _load_external_knowledge(self) -> None:
        """Load knowledge from external YAML/JSON files."""
        for knowledge_file in self.knowledge_dir.glob("*.yaml"):
            try:
                with open(knowledge_file, 'r') as f:
                    data = yaml.safe_load(f)
                    framework = self._parse_framework_knowledge(data)
                    self.frameworks[framework.name.lower()] = framework
                    logger.info(f"Loaded framework knowledge: {framework.name}")
            except Exception as e:
                logger.warning(f"Failed to load {knowledge_file}: {e}")
        
        for knowledge_file in self.knowledge_dir.glob("*.json"):
            try:
                with open(knowledge_file, 'r') as f:
                    data = json.load(f)
                    framework = self._parse_framework_knowledge(data)
                    self.frameworks[framework.name.lower()] = framework
                    logger.info(f"Loaded framework knowledge: {framework.name}")
            except Exception as e:
                logger.warning(f"Failed to load {knowledge_file}: {e}")
    
    def _load_community_knowledge(self) -> None:
        """Load community-contributed knowledge."""
        community_dir = self.knowledge_dir / "community"
        if community_dir.exists():
            for contrib_file in community_dir.glob("*.yaml"):
                try:
                    with open(contrib_file, 'r') as f:
                        data = yaml.safe_load(f)
                        self._merge_community_contribution(data)
                        logger.info(f"Loaded community contribution: {contrib_file.name}")
                except Exception as e:
                    logger.warning(f"Failed to load community contribution {contrib_file}: {e}")
    
    def _parse_framework_knowledge(self, data: Dict[str, Any]) -> FrameworkKnowledge:
        """Parse framework knowledge from dictionary."""
        framework = FrameworkKnowledge(
            name=data['name'],
            version=data.get('version', '1.0.0'),
            description=data.get('description', ''),
            detection=data.get('detection', {}),
            recommendations=data.get('recommendations', []),
            patterns=data.get('patterns', []),
            maintainer=data.get('maintainer', 'Community'),
            source_url=data.get('source_url')
        )
        
        # Parse rules
        for rule_data in data.get('rules', []):
            rule = FrameworkRule(
                id=rule_data['id'],
                name=rule_data['name'],
                description=rule_data['description'],
                severity=rule_data.get('severity', 'warning'),
                category=rule_data.get('category', 'style'),
                framework=framework.name,
                framework_version=rule_data.get('framework_version'),
                pattern_type=rule_data.get('pattern_type', 'ast'),
                pattern=rule_data.get('pattern', {}),
                author=rule_data.get('author', 'Community'),
                version=rule_data.get('version', '1.0.0'),
                tags=rule_data.get('tags', []),
                message=rule_data.get('message', ''),
                suggestion=rule_data.get('suggestion', ''),
                documentation_url=rule_data.get('documentation_url'),
                examples=rule_data.get('examples', [])
            )
            framework.rules.append(rule)
        
        return framework
    
    def _merge_community_contribution(self, data: Dict[str, Any]) -> None:
        """Merge community contribution into existing framework knowledge."""
        framework_name = data.get('framework', '').lower()
        if framework_name not in self.frameworks:
            logger.warning(f"Unknown framework in community contribution: {framework_name}")
            return
        
        framework = self.frameworks[framework_name]
        
        # Add new rules
        for rule_data in data.get('rules', []):
            rule = FrameworkRule(
                id=f"community_{rule_data['id']}",
                name=rule_data['name'],
                description=rule_data['description'],
                severity=rule_data.get('severity', 'info'),
                category=rule_data.get('category', 'style'),
                framework=framework.name,
                pattern_type=rule_data.get('pattern_type', 'ast'),
                pattern=rule_data.get('pattern', {}),
                author=rule_data.get('author', 'Community'),
                tags=['community'] + rule_data.get('tags', []),
                message=rule_data.get('message', ''),
                suggestion=rule_data.get('suggestion', ''),
                documentation_url=rule_data.get('documentation_url'),
                examples=rule_data.get('examples', [])
            )
            framework.rules.append(rule)
        
        # Add new recommendations
        framework.recommendations.extend(data.get('recommendations', []))
    
    def _index_rules(self) -> None:
        """Index rules for fast lookup."""
        self.rules_by_framework.clear()
        self.rules_by_category.clear()
        
        for framework in self.frameworks.values():
            framework_name = framework.name.lower()
            self.rules_by_framework[framework_name] = framework.rules
            
            for rule in framework.rules:
                if rule.category not in self.rules_by_category:
                    self.rules_by_category[rule.category] = []
                self.rules_by_category[rule.category].append(rule)
    
    def get_framework_rules(self, framework_name: str) -> List[FrameworkRule]:
        """Get all rules for a specific framework."""
        return self.rules_by_framework.get(framework_name.lower(), [])
    
    def get_rules_by_category(self, category: str) -> List[FrameworkRule]:
        """Get all rules in a specific category."""
        return self.rules_by_category.get(category, [])
    
    def get_framework_knowledge(self, framework_name: str) -> Optional[FrameworkKnowledge]:
        """Get complete knowledge for a framework."""
        return self.frameworks.get(framework_name.lower())
    
    def add_framework_knowledge(self, framework: FrameworkKnowledge) -> None:
        """Add new framework knowledge."""
        self.frameworks[framework.name.lower()] = framework
        self._index_rules()
    
    def add_rule(self, framework_name: str, rule: FrameworkRule) -> None:
        """Add a new rule to a framework."""
        framework = self.frameworks.get(framework_name.lower())
        if framework:
            framework.rules.append(rule)
            self._index_rules()
        else:
            logger.warning(f"Framework not found: {framework_name}")
    
    def export_framework_knowledge(self, framework_name: str, output_path: Path) -> None:
        """Export framework knowledge to YAML file."""
        framework = self.frameworks.get(framework_name.lower())
        if not framework:
            raise ValueError(f"Framework not found: {framework_name}")
        
        # Convert to dictionary
        data = {
            'name': framework.name,
            'version': framework.version,
            'description': framework.description,
            'detection': framework.detection,
            'recommendations': framework.recommendations,
            'patterns': framework.patterns,
            'maintainer': framework.maintainer,
            'last_updated': framework.last_updated,
            'source_url': framework.source_url,
            'rules': []
        }
        
        for rule in framework.rules:
            rule_data = {
                'id': rule.id,
                'name': rule.name,
                'description': rule.description,
                'severity': rule.severity,
                'category': rule.category,
                'pattern_type': rule.pattern_type,
                'pattern': rule.pattern,
                'message': rule.message,
                'suggestion': rule.suggestion,
                'author': rule.author,
                'version': rule.version,
                'tags': rule.tags,
                'examples': rule.examples
            }
            if rule.documentation_url:
                rule_data['documentation_url'] = rule.documentation_url
            if rule.framework_version:
                rule_data['framework_version'] = rule.framework_version
            
            data['rules'].append(rule_data)
        
        with open(output_path, 'w') as f:
            yaml.dump(data, f, default_flow_style=False, sort_keys=False)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get statistics about the knowledge base."""
        total_rules = sum(len(fw.rules) for fw in self.frameworks.values())
        
        category_counts = {}
        severity_counts = {}
        
        for framework in self.frameworks.values():
            for rule in framework.rules:
                category_counts[rule.category] = category_counts.get(rule.category, 0) + 1
                severity_counts[rule.severity] = severity_counts.get(rule.severity, 0) + 1
        
        return {
            'total_frameworks': len(self.frameworks),
            'total_rules': total_rules,
            'rules_by_category': category_counts,
            'rules_by_severity': severity_counts,
            'frameworks': list(self.frameworks.keys())
        }

    def _create_django_knowledge(self) -> FrameworkKnowledge:
        """Create built-in Django knowledge."""
        django_rules = [
            FrameworkRule(
                id="django_model_str_method",
                name="Django Model __str__ Method",
                description="Django models should implement __str__ method for better admin interface",
                severity="warning",
                category="maintainability",
                framework="Django",
                pattern_type="ast",
                pattern={
                    "node_type": "ClassDef",
                    "base_classes": ["Model", "models.Model"],
                    "missing_methods": ["__str__"]
                },
                message="Django model '{class_name}' should implement __str__ method",
                suggestion="Add def __str__(self): return self.field_name",
                documentation_url="https://docs.djangoproject.com/en/stable/ref/models/instances/#str",
                examples=[
                    {
                        "bad": "class User(models.Model):\n    name = models.CharField(max_length=100)",
                        "good": "class User(models.Model):\n    name = models.CharField(max_length=100)\n    \n    def __str__(self):\n        return self.name"
                    }
                ]
            ),
            FrameworkRule(
                id="django_model_meta_class",
                name="Django Model Meta Class",
                description="Models with many fields should consider Meta class for ordering",
                severity="info",
                category="maintainability",
                framework="Django",
                pattern_type="ast",
                pattern={
                    "node_type": "ClassDef",
                    "base_classes": ["Model", "models.Model"],
                    "field_count_threshold": 10,
                    "missing_inner_classes": ["Meta"]
                },
                message="Django model '{class_name}' with {field_count} fields should consider Meta class",
                suggestion="Add Meta class with ordering or other options"
            ),
            FrameworkRule(
                id="django_csrf_protection",
                name="Django CSRF Protection",
                description="Views should use CSRF protection",
                severity="error",
                category="security",
                framework="Django",
                pattern_type="ast",
                pattern={
                    "node_type": "FunctionDef",
                    "decorators": ["@csrf_exempt"],
                    "context": "view_function"
                },
                message="Avoid using @csrf_exempt decorator without proper justification",
                suggestion="Use CSRF tokens in forms or implement proper CSRF protection"
            )
        ]

        return FrameworkKnowledge(
            name="Django",
            version="4.0+",
            description="High-level Python web framework",
            detection={
                "import_patterns": ["django", "django.db", "django.http", "django.views"],
                "file_patterns": ["models.py", "views.py", "urls.py", "settings.py"],
                "class_patterns": ["Model", "View", "Form"],
                "decorator_patterns": ["@login_required", "@csrf_exempt"]
            },
            rules=django_rules,
            recommendations=[
                "Follow Django best practices for models, views, and templates",
                "Use Django REST framework for API development",
                "Implement proper CSRF protection",
                "Use select_related() and prefetch_related() to avoid N+1 queries"
            ]
        )

    def _create_flask_knowledge(self) -> FrameworkKnowledge:
        """Create built-in Flask knowledge."""
        flask_rules = [
            FrameworkRule(
                id="flask_route_methods",
                name="Flask Route HTTP Methods",
                description="Flask routes should specify HTTP methods when needed",
                severity="warning",
                category="maintainability",
                framework="Flask",
                pattern_type="ast",
                pattern={
                    "node_type": "FunctionDef",
                    "decorators": ["@app.route", "@bp.route"],
                    "function_name_patterns": ["create_", "post_", "update_", "delete_"],
                    "missing_methods_parameter": True
                },
                message="Flask route '{function_name}' should specify HTTP methods",
                suggestion="Add methods=['POST'] or appropriate HTTP methods to @app.route()"
            ),
            FrameworkRule(
                id="flask_error_handling",
                name="Flask Error Handling",
                description="Complex Flask routes should include error handling",
                severity="info",
                category="reliability",
                framework="Flask",
                pattern_type="ast",
                pattern={
                    "node_type": "FunctionDef",
                    "decorators": ["@app.route"],
                    "complexity_threshold": 5,
                    "missing_try_except": True
                },
                message="Complex Flask route '{function_name}' should include error handling",
                suggestion="Add try-except blocks for proper error handling"
            )
        ]

        return FrameworkKnowledge(
            name="Flask",
            version="2.0+",
            description="Lightweight WSGI web application framework",
            detection={
                "import_patterns": ["flask", "flask.Flask", "flask.request"],
                "class_patterns": ["Flask"],
                "decorator_patterns": ["@app.route", "@bp.route"]
            },
            rules=flask_rules,
            recommendations=[
                "Use Blueprints for better code organization",
                "Implement proper error handling",
                "Use Flask-SQLAlchemy for database operations"
            ]
        )

    def _create_fastapi_knowledge(self) -> FrameworkKnowledge:
        """Create built-in FastAPI knowledge."""
        fastapi_rules = [
            FrameworkRule(
                id="fastapi_return_type",
                name="FastAPI Return Type Annotation",
                description="FastAPI endpoints should have return type annotations",
                severity="warning",
                category="maintainability",
                framework="FastAPI",
                pattern_type="ast",
                pattern={
                    "node_type": "FunctionDef",
                    "decorators": ["@app.get", "@app.post", "@app.put", "@app.delete"],
                    "missing_return_annotation": True
                },
                message="FastAPI endpoint '{function_name}' should have return type annotation",
                suggestion="Add return type annotation for better API documentation"
            ),
            FrameworkRule(
                id="fastapi_async_usage",
                name="FastAPI Async Usage",
                description="FastAPI endpoints should consider using async",
                severity="info",
                category="performance",
                framework="FastAPI",
                pattern_type="ast",
                pattern={
                    "node_type": "FunctionDef",
                    "decorators": ["@app.get", "@app.post"],
                    "is_async": False,
                    "function_name_patterns": ["get_", "post_", "put_", "delete_"]
                },
                message="FastAPI endpoint '{function_name}' should consider using async",
                suggestion="Use async def for better performance with FastAPI"
            )
        ]

        return FrameworkKnowledge(
            name="FastAPI",
            version="0.68+",
            description="Modern, fast web framework for building APIs",
            detection={
                "import_patterns": ["fastapi", "fastapi.FastAPI", "pydantic"],
                "class_patterns": ["FastAPI", "BaseModel"],
                "decorator_patterns": ["@app.get", "@app.post"]
            },
            rules=fastapi_rules,
            recommendations=[
                "Leverage Pydantic models for data validation",
                "Use dependency injection for better testability",
                "Implement proper status codes"
            ]
        )

    def _create_pytest_knowledge(self) -> FrameworkKnowledge:
        """Create built-in pytest knowledge."""
        pytest_rules = [
            FrameworkRule(
                id="pytest_test_assertions",
                name="Pytest Test Assertions",
                description="Test functions should contain assertions",
                severity="warning",
                category="testing",
                framework="pytest",
                pattern_type="ast",
                pattern={
                    "node_type": "FunctionDef",
                    "function_name_prefix": "test_",
                    "missing_assertions": True
                },
                message="Test function '{function_name}' should contain assertions",
                suggestion="Add assert statements to verify expected behavior"
            ),
            FrameworkRule(
                id="pytest_descriptive_names",
                name="Pytest Descriptive Names",
                description="Test functions should have descriptive names",
                severity="info",
                category="maintainability",
                framework="pytest",
                pattern_type="ast",
                pattern={
                    "node_type": "FunctionDef",
                    "function_name_prefix": "test_",
                    "name_length_threshold": 10
                },
                message="Test function '{function_name}' should have more descriptive name",
                suggestion="Use descriptive names like test_user_can_login_with_valid_credentials"
            )
        ]

        return FrameworkKnowledge(
            name="pytest",
            version="6.0+",
            description="Testing framework for Python",
            detection={
                "import_patterns": ["pytest", "pytest.fixture"],
                "file_patterns": ["test_*.py", "*_test.py"],
                "decorator_patterns": ["@pytest.fixture", "@pytest.mark"]
            },
            rules=pytest_rules,
            recommendations=[
                "Maintain good test coverage and use fixtures effectively",
                "Use parametrized tests for multiple test cases",
                "Organize tests with clear naming conventions"
            ]
        )

    def _create_pandas_knowledge(self) -> FrameworkKnowledge:
        """Create built-in pandas knowledge."""
        pandas_rules = [
            FrameworkRule(
                id="pandas_avoid_iterrows",
                name="Pandas Avoid iterrows()",
                description="Avoid using iterrows() for better performance",
                severity="warning",
                category="performance",
                framework="pandas",
                pattern_type="ast",
                pattern={
                    "node_type": "Call",
                    "function_name": "iterrows",
                    "object_type": "DataFrame"
                },
                message="Using iterrows() is inefficient, consider vectorized operations",
                suggestion="Use vectorized operations, apply(), or itertuples() instead"
            ),
            FrameworkRule(
                id="pandas_method_chaining",
                name="Pandas Method Chaining",
                description="Long pandas method chains should be broken down",
                severity="info",
                category="maintainability",
                framework="pandas",
                pattern_type="ast",
                pattern={
                    "node_type": "Call",
                    "chain_length_threshold": 4,
                    "pandas_methods": ["dropna", "fillna", "groupby", "sort_values"]
                },
                message="Long pandas method chain should be broken down or assigned",
                suggestion="Break chain into multiple steps or use intermediate variables"
            )
        ]

        return FrameworkKnowledge(
            name="pandas",
            version="1.3+",
            description="Data manipulation and analysis library",
            detection={
                "import_patterns": ["pandas", "pd"],
                "class_patterns": ["DataFrame", "Series"],
                "function_patterns": ["read_csv", "read_excel"]
            },
            rules=pandas_rules,
            recommendations=[
                "Use vectorized operations instead of loops",
                "Avoid chained assignments",
                "Use appropriate data types for memory efficiency"
            ]
        )
