"""
Knowledge-Based Rule Engine

This module executes framework rules from the knowledge base,
providing a flexible and extensible rule execution system.
"""

import ast
import re
import logging
from pathlib import Path
from dataclasses import dataclass

from .framework_knowledge_base import FrameworkKnowledgeBase, FrameworkRule
from ..analysis.python_semantic_analyzer import SemanticIssue, SemanticContext

logger = logging.getLogger(__name__)


@dataclass
class RuleExecutionContext:
    """Context for rule execution."""
    
    file_path: Path
    source_code: str
    ast_tree: ast.AST
    detected_frameworks: List[str]
    semantic_context: SemanticContext


class KnowledgeBasedRuleEngine:
    """
    Executes framework rules from the knowledge base.
    """
    
    def __init__(self, knowledge_base: Optional[FrameworkKnowledgeBase] = None):
        self.knowledge_base = knowledge_base or FrameworkKnowledgeBase()
        self.rule_executors = {
            'ast': self._execute_ast_rule,
            'regex': self._execute_regex_rule,
            'import': self._execute_import_rule,
            'file_structure': self._execute_file_structure_rule
        }
    
    def execute_rules(self, context: RuleExecutionContext) -> List[SemanticIssue]:
        """
        Execute all applicable rules for the given context.
        
        Args:
            context: Rule execution context
            
        Returns:
            List of semantic issues found
        """
        issues = []
        
        # Get rules for detected frameworks
        applicable_rules = self._get_applicable_rules(context.detected_frameworks)
        
        for rule in applicable_rules:
            try:
                rule_issues = self._execute_rule(rule, context)
                issues.extend(rule_issues)
            except Exception as e:
                logger.warning(f"Error executing rule {rule.id}: {e}")
        
        return issues
    
    def _get_applicable_rules(self, detected_frameworks: List[str]) -> List[FrameworkRule]:
        """Get all rules applicable to the detected frameworks."""
        applicable_rules = []
        
        for framework_name in detected_frameworks:
            framework_rules = self.knowledge_base.get_framework_rules(framework_name.lower())
            applicable_rules.extend(framework_rules)
        
        return applicable_rules
    
    def _execute_rule(self, rule: FrameworkRule, context: RuleExecutionContext) -> List[SemanticIssue]:
        """Execute a single rule."""
        executor = self.rule_executors.get(rule.pattern_type)
        if not executor:
            logger.warning(f"Unknown pattern type: {rule.pattern_type}")
            return []
        
        return executor(rule, context)
    
    def _execute_ast_rule(self, rule: FrameworkRule, context: RuleExecutionContext) -> List[SemanticIssue]:
        """Execute AST-based rule."""
        issues = []
        pattern = rule.pattern
        
        # Create AST visitor for this rule
        visitor = KnowledgeBasedASTVisitor(rule, context)
        visitor.visit(context.ast_tree)
        
        return visitor.issues
    
    def _execute_regex_rule(self, rule: FrameworkRule, context: RuleExecutionContext) -> List[SemanticIssue]:
        """Execute regex-based rule."""
        issues = []
        pattern = rule.pattern
        
        if 'regex' not in pattern:
            return issues
        
        regex_pattern = pattern['regex']
        file_extensions = pattern.get('file_extensions', ['.py'])
        
        # Check if file extension matches
        if file_extensions and context.file_path.suffix not in file_extensions:
            return issues
        
        try:
            matches = re.finditer(regex_pattern, context.source_code, re.MULTILINE)
            for match in matches:
                line_number = context.source_code[:match.start()].count('\n') + 1
                
                issues.append(SemanticIssue(
                    rule_id=rule.id,
                    severity=rule.severity,
                    message=rule.message,
                    line_number=line_number,
                    column_number=match.start() - context.source_code.rfind('\n', 0, match.start()),
                    suggestion=rule.suggestion
                ))
        except re.error as e:
            logger.warning(f"Invalid regex in rule {rule.id}: {e}")
        
        return issues
    
    def _execute_import_rule(self, rule: FrameworkRule, context: RuleExecutionContext) -> List[SemanticIssue]:
        """Execute import-based rule."""
        issues = []
        pattern = rule.pattern
        
        # Extract imports from AST
        imports = self._extract_imports(context.ast_tree)
        
        # Check import patterns
        required_imports = pattern.get('required_imports', [])
        forbidden_imports = pattern.get('forbidden_imports', [])
        
        for forbidden in forbidden_imports:
            if forbidden in imports:
                issues.append(SemanticIssue(
                    rule_id=rule.id,
                    severity=rule.severity,
                    message=rule.message.format(import_name=forbidden),
                    line_number=1,  # Would need more sophisticated tracking
                    column_number=0,
                    suggestion=rule.suggestion
                ))
        
        for required in required_imports:
            if required not in imports:
                issues.append(SemanticIssue(
                    rule_id=rule.id,
                    severity=rule.severity,
                    message=rule.message.format(import_name=required),
                    line_number=1,
                    column_number=0,
                    suggestion=rule.suggestion
                ))
        
        return issues
    
    def _execute_file_structure_rule(self, rule: FrameworkRule, context: RuleExecutionContext) -> List[SemanticIssue]:
        """Execute file structure-based rule."""
        issues = []
        pattern = rule.pattern
        
        # Check file naming patterns
        file_name = context.file_path.name
        required_patterns = pattern.get('required_file_patterns', [])
        forbidden_patterns = pattern.get('forbidden_file_patterns', [])
        
        for forbidden_pattern in forbidden_patterns:
            if re.match(forbidden_pattern, file_name):
                issues.append(SemanticIssue(
                    rule_id=rule.id,
                    severity=rule.severity,
                    message=rule.message.format(file_name=file_name),
                    line_number=1,
                    column_number=0,
                    suggestion=rule.suggestion
                ))
        
        return issues
    
    def _extract_imports(self, tree: ast.AST) -> Set[str]:
        """Extract all imports from AST."""
        imports = set()
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.add(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    imports.add(node.module)
                    for alias in node.names:
                        imports.add(f"{node.module}.{alias.name}")
        
        return imports


class KnowledgeBasedASTVisitor(ast.NodeVisitor):
    """AST visitor that executes knowledge-based rules."""
    
    def __init__(self, rule: FrameworkRule, context: RuleExecutionContext):
        self.rule = rule
        self.context = context
        self.pattern = rule.pattern
        self.issues: List[SemanticIssue] = []
    
    def visit_ClassDef(self, node: ast.ClassDef) -> None:
        """Visit class definitions."""
        if self.pattern.get('node_type') == 'ClassDef':
            self._check_class_rule(node)
        self.generic_visit(node)
    
    def visit_FunctionDef(self, node: ast.FunctionDef) -> None:
        """Visit function definitions."""
        if self.pattern.get('node_type') == 'FunctionDef':
            self._check_function_rule(node)
        self.generic_visit(node)
    
    def visit_Call(self, node: ast.Call) -> None:
        """Visit function calls."""
        if self.pattern.get('node_type') == 'Call':
            self._check_call_rule(node)
        self.generic_visit(node)
    
    def visit_Assign(self, node: ast.Assign) -> None:
        """Visit assignments."""
        if self.pattern.get('node_type') == 'Assign':
            self._check_assign_rule(node)
        self.generic_visit(node)
    
    def _check_class_rule(self, node: ast.ClassDef) -> None:
        """Check class-specific rules."""
        # Check base classes
        base_classes = self.pattern.get('base_classes', [])
        if base_classes:
            node_bases = [self._get_name(base) for base in node.bases]
            if not any(base in node_bases for base in base_classes):
                return  # Rule doesn't apply
        
        # Check missing methods
        missing_methods = self.pattern.get('missing_methods', [])
        if missing_methods:
            existing_methods = [child.name for child in node.body if isinstance(child, ast.FunctionDef)]
            for missing_method in missing_methods:
                if missing_method not in existing_methods:
                    self.issues.append(SemanticIssue(
                        rule_id=self.rule.id,
                        severity=self.rule.severity,
                        message=self.rule.message.format(class_name=node.name, method_name=missing_method),
                        line_number=node.lineno,
                        column_number=node.col_offset,
                        suggestion=self.rule.suggestion
                    ))
        
        # Check field count threshold
        field_count_threshold = self.pattern.get('field_count_threshold')
        if field_count_threshold:
            field_count = sum(1 for child in node.body if isinstance(child, ast.Assign))
            if field_count > field_count_threshold:
                missing_inner_classes = self.pattern.get('missing_inner_classes', [])
                existing_inner_classes = [child.name for child in node.body if isinstance(child, ast.ClassDef)]
                
                for missing_class in missing_inner_classes:
                    if missing_class not in existing_inner_classes:
                        self.issues.append(SemanticIssue(
                            rule_id=self.rule.id,
                            severity=self.rule.severity,
                            message=self.rule.message.format(class_name=node.name, field_count=field_count),
                            line_number=node.lineno,
                            column_number=node.col_offset,
                            suggestion=self.rule.suggestion
                        ))
    
    def _check_function_rule(self, node: ast.FunctionDef) -> None:
        """Check function-specific rules."""
        # Check decorators
        decorators = self.pattern.get('decorators', [])
        if decorators:
            node_decorators = [self._get_decorator_name(dec) for dec in node.decorator_list]
            if not any(dec in node_decorators for dec in decorators):
                return  # Rule doesn't apply
        
        # Check function name patterns
        name_patterns = self.pattern.get('function_name_patterns', [])
        if name_patterns:
            if not any(pattern in node.name for pattern in name_patterns):
                return  # Rule doesn't apply
        
        # Check missing return annotation
        if self.pattern.get('missing_return_annotation') and not node.returns:
            self.issues.append(SemanticIssue(
                rule_id=self.rule.id,
                severity=self.rule.severity,
                message=self.rule.message.format(function_name=node.name),
                line_number=node.lineno,
                column_number=node.col_offset,
                suggestion=self.rule.suggestion
            ))
        
        # Check async usage
        if self.pattern.get('is_async') is False and isinstance(node, ast.AsyncFunctionDef):
            return  # Rule expects non-async but got async
        
        # Check missing assertions (for test functions)
        if self.pattern.get('missing_assertions'):
            has_assertions = any(isinstance(child, ast.Assert) for child in ast.walk(node))
            if not has_assertions:
                self.issues.append(SemanticIssue(
                    rule_id=self.rule.id,
                    severity=self.rule.severity,
                    message=self.rule.message.format(function_name=node.name),
                    line_number=node.lineno,
                    column_number=node.col_offset,
                    suggestion=self.rule.suggestion
                ))
    
    def _check_call_rule(self, node: ast.Call) -> None:
        """Check function call-specific rules."""
        function_name = self._get_name(node.func)
        
        # Check specific function names
        target_function = self.pattern.get('function_name')
        if target_function and function_name != target_function:
            return  # Rule doesn't apply
        
        # Check for specific patterns (like iterrows)
        if function_name == 'iterrows':
            self.issues.append(SemanticIssue(
                rule_id=self.rule.id,
                severity=self.rule.severity,
                message=self.rule.message,
                line_number=node.lineno,
                column_number=node.col_offset,
                suggestion=self.rule.suggestion
            ))
    
    def _check_assign_rule(self, node: ast.Assign) -> None:
        """Check assignment-specific rules."""
        # Check target names
        target_name = self.pattern.get('target_name')
        if target_name:
            node_targets = [self._get_name(target) for target in node.targets if isinstance(target, ast.Name)]
            if target_name not in node_targets:
                return  # Rule doesn't apply
        
        # Check value types or specific values
        value_type = self.pattern.get('value_type')
        if value_type == 'string_literal' and not isinstance(node.value, ast.Constant):
            return
        
        # Check specific boolean values
        if self.pattern.get('value') is True and not (isinstance(node.value, ast.Constant) and node.value.value is True):
            return
        
        # If we get here, the rule applies
        self.issues.append(SemanticIssue(
            rule_id=self.rule.id,
            severity=self.rule.severity,
            message=self.rule.message,
            line_number=node.lineno,
            column_number=node.col_offset,
            suggestion=self.rule.suggestion
        ))
    
    def _get_name(self, node: ast.AST) -> str:
        """Get name from AST node."""
        if isinstance(node, ast.Name):
            return node.id
        elif isinstance(node, ast.Attribute):
            return f"{self._get_name(node.value)}.{node.attr}"
        elif isinstance(node, ast.Constant):
            return str(node.value)
        else:
            return ""
    
    def _get_decorator_name(self, node: ast.AST) -> str:
        """Get decorator name from AST node."""
        if isinstance(node, ast.Name):
            return f"@{node.id}"
        elif isinstance(node, ast.Attribute):
            return f"@{self._get_name(node)}"
        elif isinstance(node, ast.Call):
            return f"@{self._get_name(node.func)}"
        else:
            return ""
