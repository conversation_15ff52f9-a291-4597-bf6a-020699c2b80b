# Community Contribution: Django Security Best Practices
# Author: Security Expert Community
# Date: 2024-01-15
# Description: Additional security-focused rules for Django applications

framework: "django"
contributor: "Django Security Community"
contribution_type: "security_rules"
version: "1.0.0"

# Additional security-focused rules
rules:
  - id: "django_debug_production"
    name: "Django DEBUG Setting in Production"
    description: "DEBUG should be False in production environments"
    severity: "error"
    category: "security"
    pattern_type: "ast"
    pattern:
      node_type: "Assign"
      target_name: "DEBUG"
      value: true
      file_pattern: "settings.py"
    message: "DEBUG = True should not be used in production"
    suggestion: "Set DEBUG = False in production settings"
    author: "Security Community"
    tags: ["security", "production", "configuration"]
    documentation_url: "https://docs.djangoproject.com/en/stable/ref/settings/#debug"
    examples:
      - bad: "DEBUG = True  # In production settings"
        good: "DEBUG = False  # In production settings"

  - id: "django_secret_key_hardcoded"
    name: "Django Hardcoded SECRET_KEY"
    description: "SECRET_KEY should not be hardcoded in settings"
    severity: "error"
    category: "security"
    pattern_type: "ast"
    pattern:
      node_type: "Assign"
      target_name: "SECRET_KEY"
      value_type: "string_literal"
      file_pattern: "settings.py"
    message: "SECRET_KEY should not be hardcoded in settings files"
    suggestion: "Use environment variables: SECRET_KEY = os.environ.get('SECRET_KEY')"
    author: "Security Community"
    tags: ["security", "secrets", "configuration"]

  - id: "django_allowed_hosts_wildcard"
    name: "Django ALLOWED_HOSTS Wildcard"
    description: "ALLOWED_HOSTS should not use wildcards in production"
    severity: "warning"
    category: "security"
    pattern_type: "ast"
    pattern:
      node_type: "Assign"
      target_name: "ALLOWED_HOSTS"
      contains_wildcard: true
    message: "ALLOWED_HOSTS should not contain wildcards in production"
    suggestion: "Specify exact hostnames instead of using '*'"
    author: "Security Community"
    tags: ["security", "configuration", "host_validation"]

  - id: "django_sql_injection_raw"
    name: "Django Raw SQL Injection Risk"
    description: "Raw SQL queries should use parameterized queries"
    severity: "error"
    category: "security"
    pattern_type: "ast"
    pattern:
      node_type: "Call"
      function_name: "raw"
      string_formatting: true
    message: "Raw SQL with string formatting may be vulnerable to SQL injection"
    suggestion: "Use parameterized queries: Model.objects.raw('SELECT * FROM table WHERE id = %s', [user_id])"
    author: "Security Community"
    tags: ["security", "sql_injection", "database"]

  - id: "django_xss_safe_filter"
    name: "Django XSS Safe Filter Usage"
    description: "Be cautious with |safe filter in templates"
    severity: "warning"
    category: "security"
    pattern_type: "regex"
    pattern:
      regex: "\\|\\s*safe"
      file_extensions: [".html", ".txt"]
    message: "Using |safe filter may introduce XSS vulnerabilities"
    suggestion: "Ensure content is properly sanitized before using |safe"
    author: "Security Community"
    tags: ["security", "xss", "templates"]

  - id: "django_csrf_middleware"
    name: "Django CSRF Middleware"
    description: "CSRF middleware should be enabled"
    severity: "error"
    category: "security"
    pattern_type: "ast"
    pattern:
      node_type: "Assign"
      target_name: "MIDDLEWARE"
      missing_item: "django.middleware.csrf.CsrfViewMiddleware"
    message: "CSRF middleware is not enabled in MIDDLEWARE settings"
    suggestion: "Add 'django.middleware.csrf.CsrfViewMiddleware' to MIDDLEWARE"
    author: "Security Community"
    tags: ["security", "csrf", "middleware"]

# Additional security recommendations
recommendations:
  - "Use HTTPS in production with SECURE_SSL_REDIRECT = True"
  - "Enable security headers with django-security or similar packages"
  - "Regularly update Django to the latest security patches"
  - "Use Content Security Policy (CSP) headers"
  - "Implement proper authentication and authorization"
  - "Validate and sanitize all user inputs"
  - "Use Django's built-in protection against common vulnerabilities"

# Security-focused patterns
patterns:
  - name: "Security Middleware Stack"
    description: "Proper ordering and inclusion of security middleware"
    confidence_indicators:
      - "SecurityMiddleware presence"
      - "CSRF middleware presence"
      - "Authentication middleware"
  
  - name: "Secure Settings Pattern"
    description: "Environment-based configuration for security settings"
    confidence_indicators:
      - "Environment variable usage"
      - "Separate settings files"
      - "Secret management"
