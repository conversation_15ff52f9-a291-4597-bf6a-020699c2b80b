# SQLAlchemy Framework Knowledge
# This file contains framework-specific rules and best practices for SQLAlchemy

name: "SQLAlchemy"
version: "1.4+"
description: "Python SQL toolkit and Object-Relational Mapping library"
maintainer: "VibeCheck Team"
source_url: "https://docs.sqlalchemy.org/"

# Detection patterns for framework identification
detection:
  import_patterns:
    - "sqlalchemy"
    - "sqlalchemy.orm"
    - "sqlalchemy.ext"
    - "sqlalchemy.Column"
    - "sqlalchemy.Integer"
    - "sqlalchemy.String"
  class_patterns:
    - "declarative_base"
    - "Base"
    - "Session"
  function_patterns:
    - "create_engine"
    - "sessionmaker"

# Framework-specific rules and best practices
rules:
  - id: "sqlalchemy_session_management"
    name: "SQLAlchemy Session Management"
    description: "Use context managers or try/finally for session management"
    severity: "warning"
    category: "reliability"
    pattern_type: "ast"
    pattern:
      node_type: "Call"
      function_name: "Session"
      missing_context_manager: true
    message: "SQLAlchemy sessions should be managed with context managers"
    suggestion: "Use 'with session:' or try/finally blocks to ensure proper session cleanup"
    documentation_url: "https://docs.sqlalchemy.org/en/14/orm/session_basics.html"
    examples:
      - bad: |
          session = Session()
          user = session.query(User).first()
          session.commit()
        good: |
          with Session() as session:
              user = session.query(User).first()
              session.commit()

  - id: "sqlalchemy_n_plus_one"
    name: "SQLAlchemy N+1 Query Prevention"
    description: "Use eager loading to prevent N+1 query problems"
    severity: "warning"
    category: "performance"
    pattern_type: "ast"
    pattern:
      node_type: "Call"
      method_chain: ["query", "all"]
      missing_eager_loading: true
    message: "Consider using eager loading to prevent N+1 queries"
    suggestion: "Use .options(joinedload()) or .options(selectinload()) for related objects"
    documentation_url: "https://docs.sqlalchemy.org/en/14/orm/loading_relationships.html"

  - id: "sqlalchemy_bulk_operations"
    name: "SQLAlchemy Bulk Operations"
    description: "Use bulk operations for better performance with large datasets"
    severity: "info"
    category: "performance"
    pattern_type: "ast"
    pattern:
      node_type: "For"
      contains_session_add: true
      iteration_threshold: 100
    message: "Consider using bulk operations for large datasets"
    suggestion: "Use session.bulk_insert_mappings() or session.bulk_save_objects()"

  - id: "sqlalchemy_connection_pooling"
    name: "SQLAlchemy Connection Pooling"
    description: "Configure appropriate connection pooling for production"
    severity: "info"
    category: "performance"
    pattern_type: "ast"
    pattern:
      node_type: "Call"
      function_name: "create_engine"
      missing_pool_parameters: true
    message: "Consider configuring connection pooling parameters"
    suggestion: "Add pool_size, max_overflow, and pool_timeout parameters"

# General recommendations for SQLAlchemy projects
recommendations:
  - "Use Alembic for database migrations"
  - "Implement proper session management with context managers"
  - "Use eager loading to prevent N+1 query problems"
  - "Configure connection pooling for production environments"
  - "Use bulk operations for large datasets"
  - "Implement proper error handling for database operations"

# Common architectural patterns
patterns:
  - name: "Repository Pattern"
    description: "Encapsulate data access logic in repository classes"
    confidence_indicators:
      - "Repository class names"
      - "Data access methods"
      - "Session injection"
  
  - name: "Unit of Work"
    description: "Maintain a list of objects affected by a business transaction"
    confidence_indicators:
      - "Transaction management"
      - "Session scoping"
      - "Commit/rollback patterns"
