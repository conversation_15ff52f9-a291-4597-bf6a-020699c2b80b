"""
Logging Module
===========

This module provides functions and classes for logging.
"""

import logging
import sys
from pathlib import Path
from typing import Any, Dict, Optional, Union

# Default log format
DEFAULT_LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Default log level
DEFAULT_LOG_LEVEL = logging.INFO


def setup_logging(log_level: Optional[Union[int, str]] = None,
                 log_file: Optional[Union[str, Path]] = None,
                 log_format: Optional[str] = None,
                 debug: bool = False,
                 quiet: bool = False) -> logging.Logger:
    """
    Set up logging configuration.

    Args:
        log_level: Logging level (default: INFO)
        log_file: Path to log file (default: None, logs to console only)
        log_format: Log format string (default: DEFAULT_LOG_FORMAT)
        debug: Enable debug logging (overrides log_level)
        quiet: Enable quiet mode (minimal logging, overrides log_level and debug)

    Returns:
        Configured logger
    """
    # Determine log level based on flags
    if quiet:
        level = logging.WARNING
    elif debug:
        level = logging.DEBUG
    elif isinstance(log_level, str):
        level = getattr(logging, log_level.upper(), DEFAULT_LOG_LEVEL)
    else:
        level = log_level or DEFAULT_LOG_LEVEL

    # Use default log format if none provided
    format_str = log_format or DEFAULT_LOG_FORMAT

    # Configure basic logging
    logging.basicConfig(
        level=level,
        format=format_str,
        filename=log_file,
        filemode='a' if log_file else None  # type: ignore[arg-type]
    )

    # Create logger
    logger = logging.getLogger("vibe_check")
    logger.setLevel(level)

    # If no file handler is specified and we're not using basicConfig's filename,
    # add a console handler
    if not log_file and not any(isinstance(h, logging.StreamHandler) for h in logger.handlers):
        # Create formatter
        formatter = logging.Formatter(format_str)

        # Create console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

    return logger


def get_logger(name: str) -> 'ContextualLogger':
    """
    Get a logger with the specified name.

    Args:
        name: Logger name

    Returns:
        Logger instance
    """
    logger = logging.getLogger(f"vibe_check.{name}")
    return ContextualLogger(logger)


class ContextualLogger:
    """Logger that includes context information in log messages."""

    def __init__(self, logger: logging.Logger, context: Optional[Dict[str, Any]] = None):
        """
        Initialize the contextual logger.

        Args:
            logger: Base logger or logger name
            context: Optional context dictionary
        """
        if isinstance(logger, str):
            self.logger = logging.getLogger(logger)
        else:
            self.logger = logger

        self.name = self.logger.name
        self.context = context or {}

    def with_context(self, context: Dict[str, Any]) -> 'ContextualLogger':
        """
        Create a new logger with additional context.

        Args:
            context: Context dictionary to add

        Returns:
            New contextual logger with updated context
        """
        new_context = {**self.context, **context}
        return ContextualLogger(self.logger, new_context)

    def _format_message(self, msg: str) -> str:
        """
        Format a message with context information.

        Args:
            msg: Original message

        Returns:
            Formatted message with context
        """
        if not self.context:
            return msg

        context_str = " ".join(f"{k}={v}" for k, v in self.context.items())
        return f"{msg} [{context_str}]"

    def debug(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Log a debug message."""
        self.logger.debug(self._format_message(msg), *args, **kwargs)

    def info(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Log an info message."""
        self.logger.info(self._format_message(msg), *args, **kwargs)

    def warning(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Log a warning message."""
        self.logger.warning(self._format_message(msg), *args, **kwargs)

    def error(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Log an error message."""
        self.logger.error(self._format_message(msg), *args, **kwargs)

    def critical(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Log a critical message."""
        self.logger.critical(self._format_message(msg), *args, **kwargs)


class LoggingContext:
    """Context manager for logging with context."""

    def __init__(self, logger: Union[logging.Logger, ContextualLogger], context: Dict[str, Any]):
        """
        Initialize the logging context.

        Args:
            logger: Logger to use
            context: Context dictionary
        """
        self.logger = logger
        self.context = context
        self.temp_logger = None

    def __enter__(self) -> ContextualLogger:
        """
        Enter the context.

        Returns:
            Contextual logger with the specified context
        """
        if isinstance(self.logger, ContextualLogger):
            self.temp_logger = self.logger.with_context(self.context)  # type: ignore[assignment]
        else:
            self.temp_logger = ContextualLogger(self.logger, self.context)  # type: ignore[assignment]

        return self.temp_logger  # type: ignore[return-value]

    def __exit__(self, exc_type: Any, exc_val: Any, exc_tb: Any) -> None:
        """Exit the context."""
        pass


class ContextLogger:
    """Logger that includes context information in log messages."""

    def __init__(self, logger: logging.Logger, context: Optional[Dict[str, Any]] = None):
        """
        Initialize the context logger.

        Args:
            logger: Base logger
            context: Optional context dictionary
        """
        self.logger = logger
        self.context = context or {}

    def _format_message(self, msg: str) -> str:
        """
        Format a message with context information.

        Args:
            msg: Original message

        Returns:
            Formatted message with context
        """
        if not self.context:
            return msg

        context_str = " ".join(f"{k}={v}" for k, v in self.context.items())
        return f"{msg} [{context_str}]"

    def debug(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Log a debug message."""
        self.logger.debug(self._format_message(msg), *args, **kwargs)

    def info(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Log an info message."""
        self.logger.info(self._format_message(msg), *args, **kwargs)

    def warning(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Log a warning message."""
        self.logger.warning(self._format_message(msg), *args, **kwargs)

    def error(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Log an error message."""
        self.logger.error(self._format_message(msg), *args, **kwargs)

    def critical(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Log a critical message."""
        self.logger.critical(self._format_message(msg), *args, **kwargs)

    def with_context(self, **context: Any) -> 'ContextLogger':
        """
        Create a new logger with additional context.

        Args:
            **context: Context key-value pairs

        Returns:
            New context logger with updated context
        """
        new_context = {**self.context, **context}
        return ContextLogger(self.logger, new_context)


# Initialize the default logger
setup_logging()
