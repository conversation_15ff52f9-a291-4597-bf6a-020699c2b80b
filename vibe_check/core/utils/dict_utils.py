"""
Dictionary Utilities
================

This module provides utility functions for working with dictionaries.
It includes functions for merging, flattening, and manipulating dictionaries.
"""

import copy
from typing import Dict, Any, Optional, List


def deep_merge(dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
    """
    Deep merge two dictionaries.

    This function performs a deep merge of two dictionaries, where values from dict2
    override values from dict1 for the same keys. If both values are dictionaries,
    they are recursively merged.

    Args:
        dict1: First dictionary
        dict2: Second dictionary (takes precedence)

    Returns:
        Merged dictionary
    """
    # Make a copy of dict1 to avoid modifying the original
    result = copy.deepcopy(dict1)

    # Merge dict2 into the copy of dict1
    for key, value in dict2.items():
        # If both values are dictionaries, recursively merge them
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = deep_merge(result[key], value)
        else:
            # Otherwise, just override the value
            result[key] = copy.deepcopy(value)

    return result


def get_nested_value(data: Dict[str, Any], path: str, default: Optional[Any] = None) -> Any:
    """
    Get a value from a nested dictionary using a dot-separated path.

    Args:
        data: Dictionary to get the value from
        path: Dot-separated path to the value (e.g., "a.b.c")
        default: Default value to return if the path doesn't exist

    Returns:
        Value at the specified path, or the default value if the path doesn't exist
    """
    keys = path.split('.')
    result = data

    for key in keys:
        if isinstance(result, dict) and key in result:
            result = result[key]
        else:
            return default

    return result


def set_nested_value(data: Dict[str, Any], path: str, value: Any) -> Dict[str, Any]:
    """
    Set a value in a nested dictionary using a dot-separated path.

    Args:
        data: Dictionary to set the value in
        path: Dot-separated path to the value (e.g., "a.b.c")
        value: Value to set

    Returns:
        Updated dictionary
    """
    keys = path.split('.')
    result = data

    # Navigate to the parent of the target key
    for key in keys[:-1]:
        if key not in result or not isinstance(result[key], dict):
            result[key] = {}
        result = result[key]

    # Set the value
    result[keys[-1]] = value

    return data


def flatten_dict(data: Dict[str, Any], prefix: str = '', sep: str = '.') -> Dict[str, Any]:
    """
    Flatten a nested dictionary into a single-level dictionary with dot-separated keys.

    This function flattens a nested dictionary by concatenating keys with a separator.
    For example, {'a': {'b': 1}} becomes {'a.b': 1}.

    Args:
        data: Dictionary to flatten
        prefix: Prefix to add to all keys
        sep: Separator to use between keys

    Returns:
        Flattened dictionary
    """
    result = {}

    for key, value in data.items():
        new_key = f"{prefix}{sep}{key}" if prefix else key

        if isinstance(value, dict):
            # Recursively flatten the nested dictionary
            result.update(flatten_dict(value, new_key, sep))
        else:
            # Add the value to the result
            result[new_key] = value

    return result


def unflatten_dict(d: Dict[str, Any], sep: str = '.') -> Dict[str, Any]:
    """
    Unflatten a flattened dictionary.

    This function unflattens a dictionary that was flattened with flatten_dict.
    For example, {'a.b': 1} becomes {'a': {'b': 1}}.

    Args:
        d: Flattened dictionary
        sep: Separator used between keys

    Returns:
        Unflattened dictionary
    """
    result: Dict[str, Any] = {}
    for key, value in d.items():
        parts = key.split(sep)
        current: Dict[str, Any] = result
        for part in parts[:-1]:
            if part not in current:
                current[part] = {}
            current = current[part]
        current[parts[-1]] = value
    return result


def filter_dict(d: Dict[str, Any], keys: List[str]) -> Dict[str, Any]:
    """
    Filter a dictionary to include only the specified keys.

    Args:
        d: Dictionary to filter
        keys: List of keys to include

    Returns:
        Filtered dictionary
    """
    return {k: v for k, v in d.items() if k in keys}


def exclude_keys(d: Dict[str, Any], keys: List[str]) -> Dict[str, Any]:
    """
    Filter a dictionary to exclude the specified keys.

    Args:
        d: Dictionary to filter
        keys: List of keys to exclude

    Returns:
        Filtered dictionary
    """
    return {k: v for k, v in d.items() if k not in keys}


def dict_to_list(d: Dict[str, Any], key_field: str = 'key', value_field: str = 'value') -> List[Dict[str, Any]]:
    """
    Convert a dictionary to a list of dictionaries.

    This function converts a dictionary to a list of dictionaries with key and value fields.
    For example, {'a': 1, 'b': 2} becomes [{'key': 'a', 'value': 1}, {'key': 'b', 'value': 2}].

    Args:
        d: Dictionary to convert
        key_field: Name of the key field in the output dictionaries
        value_field: Name of the value field in the output dictionaries

    Returns:
        List of dictionaries
    """
    return [{key_field: k, value_field: v} for k, v in d.items()]


def list_to_dict(l: List[Dict[str, Any]], key_field: str = 'key', value_field: str = 'value') -> Dict[str, Any]:
    """
    Convert a list of dictionaries to a dictionary.

    This function converts a list of dictionaries with key and value fields to a dictionary.
    For example, [{'key': 'a', 'value': 1}, {'key': 'b', 'value': 2}] becomes {'a': 1, 'b': 2}.

    Args:
        l: List of dictionaries to convert
        key_field: Name of the key field in the input dictionaries
        value_field: Name of the value field in the input dictionaries

    Returns:
        Dictionary
    """
    return {item[key_field]: item[value_field] for item in l}
