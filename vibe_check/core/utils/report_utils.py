"""
Report Generation Utilities
=======================

This module provides utility functions for generating reports from analysis results.
It abstracts the details of report generation to be used by both the simple analyzer
and the actor-based system.
"""

import json
import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any

from vibe_check.core.models import ProjectMetrics

logger = logging.getLogger("vibe_check_report_utils")


def generate_json_report(metrics: ProjectMetrics, output_dir: Path) -> Path:
    """
    Generate a JSON report from analysis results.
    
    Args:
        metrics: ProjectMetrics object with analysis results
        output_dir: Directory to write the report to
        
    Returns:
        Path to the generated report
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate JSON report
    json_path = output_dir / "report.json"
    try:
        with open(json_path, 'w') as f:
            # Convert metrics to a dictionary
            metrics_dict = {
                "project_path": metrics.project_path,
                "files": {k: vars(v) for k, v in metrics.files.items()},
                "directories": {k: vars(v) for k, v in metrics.directories.items()},
                "issue_count": metrics.issue_count,
                "max_complexity": metrics.max_complexity,
                "issues_by_severity": metrics.issues_by_severity
            }
            json.dump(metrics_dict, f, indent=2, default=str)
        logger.info(f"JSON report saved to {json_path}")
    except Exception as e:
        logger.error(f"Error generating JSON report: {e}")
    
    return json_path


def generate_markdown_report(metrics: ProjectMetrics, output_dir: Path) -> Path:
    """
    Generate a Markdown report from analysis results.
    
    Args:
        metrics: ProjectMetrics object with analysis results
        output_dir: Directory to write the report to
        
    Returns:
        Path to the generated report
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate Markdown report
    md_path = output_dir / "report.md"
    try:
        with open(md_path, 'w') as f:
            f.write(f"# Project Analysis Report\n\n")
            f.write(f"**Project:** {metrics.project_path}\n\n")
            f.write(f"**Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write(f"## Summary\n\n")
            f.write(f"- **Total Files:** {metrics.total_file_count}\n")
            f.write(f"- **Total Directories:** {len(metrics.directories)}\n")
            f.write(f"- **Total Issues:** {metrics.issue_count}\n")
            f.write(f"- **Max Complexity:** {metrics.max_complexity}\n\n")

            f.write(f"## Files\n\n")
            f.write(f"| File | Lines | Issues | Complexity |\n")
            f.write(f"|------|-------|--------|------------|\n")
            for file_path, file_metrics in metrics.files.items():
                f.write(f"| {file_path} | {file_metrics.lines} | {len(file_metrics.issues)} | {file_metrics.complexity} |\n")

            f.write(f"\n## Issues\n\n")
            for file_path, file_metrics in metrics.files.items():
                if file_metrics.issues:
                    f.write(f"### {file_path}\n\n")
                    f.write(f"| Line | Code | Severity | Message |\n")
                    f.write(f"|------|------|----------|--------|\n")
                    for issue in file_metrics.issues:
                        line = issue.get('line', 0)
                        code = issue.get('code', '')
                        severity = issue.get('severity', 'unknown')
                        message = issue.get('message', '')
                        f.write(f"| {line} | {code} | {severity} | {message} |\n")
                    f.write(f"\n")
        logger.info(f"Markdown report saved to {md_path}")
    except Exception as e:
        logger.error(f"Error generating Markdown report: {e}")
    
    return md_path


def generate_html_report(metrics: ProjectMetrics, output_dir: Path) -> Path:
    """
    Generate an HTML report from analysis results.
    
    Args:
        metrics: ProjectMetrics object with analysis results
        output_dir: Directory to write the report to
        
    Returns:
        Path to the generated report
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate HTML report
    html_path = output_dir / "report.html"
    try:
        with open(html_path, 'w') as f:
            f.write(f"""<!DOCTYPE html>
<html>
<head>
    <title>Project Analysis Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        h1, h2, h3 {{ color: #333; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        tr:nth-child(even) {{ background-color: #f9f9f9; }}
        .severity-high {{ color: #d9534f; }}
        .severity-medium {{ color: #f0ad4e; }}
        .severity-low {{ color: #5bc0de; }}
    </style>
</head>
<body>
    <h1>Project Analysis Report</h1>
    <p><strong>Project:</strong> {metrics.project_path}</p>
    <p><strong>Date:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>

    <h2>Summary</h2>
    <ul>
        <li><strong>Total Files:</strong> {metrics.total_file_count}</li>
        <li><strong>Total Directories:</strong> {len(metrics.directories)}</li>
        <li><strong>Total Issues:</strong> {metrics.issue_count}</li>
        <li><strong>Max Complexity:</strong> {metrics.max_complexity}</li>
    </ul>

    <h2>Files</h2>
    <table>
        <tr>
            <th>File</th>
            <th>Lines</th>
            <th>Issues</th>
            <th>Complexity</th>
        </tr>
""")
            for file_path, file_metrics in metrics.files.items():
                f.write(f"""        <tr>
            <td>{file_path}</td>
            <td>{file_metrics.lines}</td>
            <td>{len(file_metrics.issues)}</td>
            <td>{file_metrics.complexity}</td>
        </tr>
""")

            f.write(f"""    </table>

    <h2>Issues</h2>
""")
            for file_path, file_metrics in metrics.files.items():
                if file_metrics.issues:
                    f.write(f"""    <h3>{file_path}</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
""")
                    for issue in file_metrics.issues:
                        line = issue.get('line', 0)
                        code = issue.get('code', '')
                        severity = issue.get('severity', 'unknown')
                        message = issue.get('message', '')
                        severity_class = f"severity-{severity.lower()}" if severity.lower() in ['high', 'medium', 'low'] else ""
                        f.write(f"""        <tr>
            <td>{line}</td>
            <td>{code}</td>
            <td class="{severity_class}">{severity}</td>
            <td>{message}</td>
        </tr>
""")
                    f.write(f"""    </table>
""")

            f.write(f"""</body>
</html>
""")
        logger.info(f"HTML report saved to {html_path}")
    except Exception as e:
        logger.error(f"Error generating HTML report: {e}")
    
    return html_path


def generate_reports(metrics: ProjectMetrics, output_dir: Path) -> Dict[str, Path]:
    """
    Generate all reports from analysis results.
    
    Args:
        metrics: ProjectMetrics object with analysis results
        output_dir: Directory to write reports to
        
    Returns:
        Dictionary mapping report types to report paths
    """
    reports = {}
    
    # Generate JSON report
    reports['json'] = generate_json_report(metrics, output_dir)
    
    # Generate Markdown report
    reports['markdown'] = generate_markdown_report(metrics, output_dir)
    
    # Generate HTML report
    reports['html'] = generate_html_report(metrics, output_dir)
    
    return reports
