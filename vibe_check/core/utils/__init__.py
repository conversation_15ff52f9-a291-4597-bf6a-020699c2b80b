"""
Core Utilities for Project Analysis
=================================

This module provides utility functions and helpers used throughout the Vibe Check tool.
These utilities include logging, file operations, async utilities, and other common functionality.

The utilities have been consolidated and organized into logical modules for better maintainability
and consistency.
"""

import logging

# Configure logging
logger = logging.getLogger("vibe_check")
logger.setLevel(logging.INFO)

# Export file system utilities
from vibe_check.core.fs_utils import (
    get_file_content, write_file, ensure_directory, ensure_dir,
    is_excluded, find_files, count_lines,
    get_file_stats, copy_file, get_relative_path, get_file_extension
)

# Export configuration utilities
from vibe_check.core.config import load_config, save_config, get_default_config, merge_configs

# Export async utilities
from vibe_check.core.utils.async_utils import (
    run_async, run_async_with_timeout, ensure_event_loop,
    with_timeout, to_thread, gather_with_concurrency
)

# Export tool utilities
from vibe_check.core.utils.tool_utils import (
    run_tool_on_file, extract_issues_from_result, extract_complexity_from_result
)

# Export reporting utilities
from vibe_check.core.utils.report_utils import (
    generate_reports, generate_json_report, generate_markdown_report, generate_html_report
)

# Export dictionary utilities
from vibe_check.core.utils.dict_utils import deep_merge

__all__ = [
    # Logging
    'logger',

    # File system utilities
    'get_file_content',
    'write_file',
    'ensure_directory',
    'ensure_dir',
    'normalize_path',
    'is_excluded',
    'list_files',
    'find_files',
    'find_python_files',
    'count_lines',
    'get_file_stats',
    'copy_file',
    'get_relative_path',
    'get_file_extension',

    # Configuration utilities
    'load_config',
    'merge_configs',
    'read_config',
    'parse_gitignore',
    'get_exclude_patterns',
    'get_available_presets',
    'load_preset',
    'apply_preset',

    # Async utilities
    'run_async',
    'run_async_with_timeout',
    'ensure_event_loop',
    'with_timeout',
    'to_thread',
    'gather_with_concurrency',

    # Tool utilities
    'run_tool_on_file',
    'extract_issues_from_result',
    'extract_complexity_from_result',

    # Reporting utilities
    'generate_reports',
    'generate_json_report',
    'generate_markdown_report',
    'generate_html_report',

    # Dictionary utilities
    'deep_merge',
]
