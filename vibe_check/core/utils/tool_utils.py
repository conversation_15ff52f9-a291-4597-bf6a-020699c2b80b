"""
Tool Execution Utilities
=====================

This module provides utility functions for executing analysis tools on files.
It abstracts the details of tool execution to be used by both the simple analyzer
and the actor-based system.
"""

import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

# from vibe_check.tools.runners.tool_registry import get_runner_for_tool  # Disabled for consolidation
from .async_utils import run_async

logger = logging.getLogger("vibe_check_tool_utils")


def run_tool_on_file(
    tool_name: str,
    file_path: Union[str, Path],
    content: str,
    tool_config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Run a specific tool on a file and return the results.
    
    This function handles both synchronous and asynchronous tool runners.
    
    Args:
        tool_name: Name of the tool to run
        file_path: Path to the file to analyze
        content: Content of the file
        tool_config: Optional configuration for the tool
        
    Returns:
        Dictionary containing analysis results
        
    Raises:
        Exception: If the tool runner cannot be found or if the tool execution fails
    """
    # Get the tool runner (disabled for consolidation)
    # runner = get_runner_for_tool(tool_name, tool_config)
    # if not runner:
    logger.warning(f"Tool runner disabled during consolidation: {tool_name}")
    return {"error": f"Tool runner disabled during consolidation: {tool_name}", "issues": []}
    
    try:
        # Check if the tool has a run_with_args method
        if hasattr(runner, "run_with_args") and tool_config and "args" in tool_config:
            # Run the tool with arguments
            result = run_async(
                runner.run_with_args,
                file_path=file_path,
                content=content,
                args=tool_config.get("args", [])
            )
        else:
            # Run the tool without arguments
            result = run_async(
                runner.run,
                file_path=file_path,
                content=content
            )
        
        return result
    except Exception as e:
        logger.error(f"Error running tool {tool_name} on {file_path}: {e}")
        import traceback
        logger.debug(traceback.format_exc())
        return {"error": str(e), "issues": []}


def extract_issues_from_result(result: Any) -> List[Dict[str, Any]]:
    """
    Extract issues from a tool result.
    
    This function handles different result formats from different tools.
    
    Args:
        result: Tool result object or dictionary
        
    Returns:
        List of issue dictionaries
    """
    issues = []
    
    # Handle different result formats
    if hasattr(result, 'issues') and result.issues:
        # Object with issues attribute
        for issue in result.issues:
            # Convert issue to a dictionary if it's not already
            if not isinstance(issue, dict):
                issue_dict = {
                    'code': getattr(issue, 'rule_id', getattr(issue, 'code', '')),
                    'message': getattr(issue, 'message', str(issue)),
                    'line': getattr(issue, 'line', 0),
                    'severity': getattr(issue, 'severity', 'medium')
                }
                issues.append(issue_dict)
            else:
                issues.append(issue)
    elif isinstance(result, dict) and 'issues' in result:
        # Dictionary with issues key
        for issue in result['issues']:
            # Convert issue to a dictionary if it's not already
            if not isinstance(issue, dict):
                issue_dict = {
                    'code': getattr(issue, 'rule_id', getattr(issue, 'code', '')),
                    'message': getattr(issue, 'message', str(issue)),
                    'line': getattr(issue, 'line', 0),
                    'severity': getattr(issue, 'severity', 'medium')
                }
                issues.append(issue_dict)
            else:
                issues.append(issue)
    
    return issues


def extract_complexity_from_result(result: Any) -> int:
    """
    Extract complexity score from a tool result.
    
    This function handles different result formats from different tools.
    
    Args:
        result: Tool result object or dictionary
        
    Returns:
        Complexity score as an integer
    """
    # Handle different result formats
    if hasattr(result, 'complexity') and result.complexity is not None:
        # Convert to int if needed
        return int(result.complexity) if isinstance(result.complexity, float) else result.complexity
    elif isinstance(result, dict):
        if 'complexity_score' in result:
            return int(result['complexity_score']) if isinstance(result['complexity_score'], float) else result['complexity_score']
        elif 'summary' in result and 'max_complexity' in result['summary']:
            return int(result['summary']['max_complexity']) if isinstance(result['summary']['max_complexity'], float) else result['summary']['max_complexity']
    
    # Default to 0 if no complexity found
    return 0
