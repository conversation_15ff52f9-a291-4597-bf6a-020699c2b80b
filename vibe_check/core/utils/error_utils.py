"""
Error Utilities Module
=====================

This module provides standardized error handling utilities for consistent
error handling patterns across the Vibe Check codebase.

Examples:
    Basic error logging with context:

    >>> from vibe_check.core.utils.error_utils import log_error_with_context, create_error_context
    >>> try:
    ...     risky_operation()
    ... except Exception as e:
    ...     context = create_error_context("risky_operation", "my_module")
    ...     log_error_with_context(e, context)

    Using the safe execution wrapper:

    >>> from vibe_check.core.utils.error_utils import safe_execute
    >>> result = safe_execute(
    ...     potentially_failing_function,
    ...     arg1, arg2,
    ...     component="my_component",
    ...     fallback_value="default",
    ...     reraise=False
    ... )

    Using the error handler decorator:

    >>> from vibe_check.core.utils.error_utils import error_handler_decorator
    >>> @error_handler_decorator(component="my_component", fallback_value=None)
    ... def my_function(x, y):
    ...     return x / y  # May raise ZeroDivisionError
"""

import logging
import traceback
from functools import wraps
from typing import Any, Callable, Dict, Optional, TypeVar

# Type variables for generic functions
F = TypeVar('F', bound=Callable[..., Any])
AsyncF = TypeVar('AsyncF', bound=Callable[..., Any])

logger = logging.getLogger("vibe_check_error_utils")


def log_error_with_context(
    error: Exception,
    context: Dict[str, Any],
    logger_instance: Optional[logging.Logger] = None,
    level: int = logging.ERROR
) -> None:
    """
    Log an error with context information in a standardized format.

    Args:
        error: The exception to log
        context: Context information about where the error occurred
        logger_instance: Optional logger instance to use (defaults to module logger)
        level: Log level to use (defaults to ERROR)
    """
    log = logger_instance or logger
    
    # Create standardized error message
    function_name = context.get("function", "unknown")
    component = context.get("component", "unknown")
    
    error_msg = f"Error in {component}.{function_name}: {error}"
    
    # Log the error
    log.log(level, error_msg)
    
    # Log additional context if available
    if context.get("args") or context.get("kwargs"):
        log.debug(f"Function arguments: args={context.get('args')}, kwargs={context.get('kwargs')}")
    
    # Log traceback at debug level
    if context.get("traceback"):
        log.debug(f"Traceback:\n{context['traceback']}")
    else:
        log.debug(f"Traceback:\n{traceback.format_exc()}")


def create_error_context(
    function_name: str,
    component: str,
    args: tuple = (),
    kwargs: Optional[Dict[str, Any]] = None,
    additional_context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Create a standardized error context dictionary.

    Args:
        function_name: Name of the function where the error occurred
        component: Name of the component/module where the error occurred
        args: Function arguments
        kwargs: Function keyword arguments
        additional_context: Additional context information

    Returns:
        Standardized error context dictionary
    """
    context = {
        "function": function_name,
        "component": component,
        "args": args,
        "kwargs": kwargs or {},
        "traceback": traceback.format_exc()
    }
    
    if additional_context:
        context.update(additional_context)
    
    return context


def handle_error_with_fallback(
    error: Exception,
    context: Dict[str, Any],
    fallback_value: Any = None,
    logger_instance: Optional[logging.Logger] = None,
    reraise: bool = False
) -> Any:
    """
    Handle an error with a standardized pattern including logging and fallback.

    Args:
        error: The exception to handle
        context: Error context information
        fallback_value: Value to return if not re-raising
        logger_instance: Optional logger instance to use
        reraise: Whether to re-raise the exception after logging

    Returns:
        Fallback value if not re-raising

    Raises:
        Exception: The original exception if reraise is True
    """
    # Log the error
    log_error_with_context(error, context, logger_instance)
    
    # Re-raise if requested
    if reraise:
        raise
    
    # Return fallback value
    return fallback_value


def safe_execute(
    func: Callable[..., Any],
    *args: Any,
    component: str = "unknown",
    fallback_value: Any = None,
    logger_instance: Optional[logging.Logger] = None,
    reraise: bool = False,
    **kwargs: Any
) -> Any:
    """
    Safely execute a function with standardized error handling.

    Args:
        func: Function to execute
        *args: Positional arguments for the function
        component: Component name for error context
        fallback_value: Value to return on error
        logger_instance: Optional logger instance
        reraise: Whether to re-raise exceptions
        **kwargs: Keyword arguments for the function

    Returns:
        Function result or fallback value

    Raises:
        Exception: If reraise is True and an exception occurs
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        context = create_error_context(
            function_name=func.__name__,
            component=component,
            args=args,
            kwargs=kwargs
        )
        return handle_error_with_fallback(
            error=e,
            context=context,
            fallback_value=fallback_value,
            logger_instance=logger_instance,
            reraise=reraise
        )


async def safe_execute_async(
    func: Callable[..., Any],
    *args: Any,
    component: str = "unknown",
    fallback_value: Any = None,
    logger_instance: Optional[logging.Logger] = None,
    reraise: bool = False,
    **kwargs: Any
) -> Any:
    """
    Safely execute an async function with standardized error handling.

    Args:
        func: Async function to execute
        *args: Positional arguments for the function
        component: Component name for error context
        fallback_value: Value to return on error
        logger_instance: Optional logger instance
        reraise: Whether to re-raise exceptions
        **kwargs: Keyword arguments for the function

    Returns:
        Function result or fallback value

    Raises:
        Exception: If reraise is True and an exception occurs
    """
    try:
        return await func(*args, **kwargs)
    except Exception as e:
        context = create_error_context(
            function_name=func.__name__,
            component=component,
            args=args,
            kwargs=kwargs
        )
        return handle_error_with_fallback(
            error=e,
            context=context,
            fallback_value=fallback_value,
            logger_instance=logger_instance,
            reraise=reraise
        )


def error_handler_decorator(
    component: str = "unknown",
    fallback_value: Any = None,
    logger_instance: Optional[logging.Logger] = None,
    reraise: bool = False
) -> Callable[[F], F]:
    """
    Decorator for standardized error handling.

    Args:
        component: Component name for error context
        fallback_value: Value to return on error
        logger_instance: Optional logger instance
        reraise: Whether to re-raise exceptions

    Returns:
        Decorated function
    """
    def decorator(func: F) -> F:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            return safe_execute(
                func,
                *args,
                component=component,
                fallback_value=fallback_value,
                logger_instance=logger_instance,
                reraise=reraise,
                **kwargs
            )
        return wrapper
    return decorator


def async_error_handler_decorator(
    component: str = "unknown",
    fallback_value: Any = None,
    logger_instance: Optional[logging.Logger] = None,
    reraise: bool = False
) -> Callable[[AsyncF], AsyncF]:
    """
    Decorator for standardized async error handling.

    Args:
        component: Component name for error context
        fallback_value: Value to return on error
        logger_instance: Optional logger instance
        reraise: Whether to re-raise exceptions

    Returns:
        Decorated async function
    """
    def decorator(func: AsyncF) -> AsyncF:
        @wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            return await safe_execute_async(
                func,
                *args,
                component=component,
                fallback_value=fallback_value,
                logger_instance=logger_instance,
                reraise=reraise,
                **kwargs
            )
        return wrapper
    return decorator
