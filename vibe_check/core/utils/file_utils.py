"""
File Utility Functions
====================

This module provides utility functions for file operations.
"""

import os
import fnmatch
from pathlib import Path
from typing import Union, List, Optional


def ensure_dir(path: Union[str, Path]) -> Path:
    """
    Ensure that a directory exists, creating it if necessary.

    Args:
        path: Directory path to ensure exists

    Returns:
        Path object of the directory
    """
    path_obj = Path(path)
    if not path_obj.exists():
        path_obj.mkdir(parents=True, exist_ok=True)
    elif not path_obj.is_dir():
        raise ValueError(f"{path} exists but is not a directory")
    return path_obj


def normalize_path(path: Union[str, Path], base_path: Optional[Union[str, Path]]= None) -> str:
    """
    Normalize a path to a standard format.

    Args:
        path: Path to normalize
        base_path: Optional base path to make the path relative to

    Returns:
        Normalized path as a string
    """
    # Convert to Path objects
    path_obj = Path(path)

    # Make the path absolute if it's not already
    if not path_obj.is_absolute() and base_path is not None:
        path_obj = Path(base_path) / path_obj

    # Resolve the path
    try:
        path_obj = path_obj.resolve()
    except (FileNotFoundError, RuntimeError):
        # If the path doesn't exist, just normalize it
        pass

    # Return the normalized path as a string with forward slashes
    return str(path_obj).replace('\\', '/')


def is_excluded(path: Union[str, Path], exclude_patterns: Optional[List[str]]= None) -> bool:
    """
    Check if a path matches any exclude pattern.

    Args:
        path: Path to check
        exclude_patterns: Patterns to exclude

    Returns:
        True if the path should be excluded, False otherwise
    """
    if not exclude_patterns:
        return False

    # Normalize the path
    norm_path = normalize_path(path)

    # Check if the path matches any pattern
    for pattern in exclude_patterns:
        if fnmatch.fnmatch(norm_path, pattern):
            return True

        # Also check the basename
        basename = os.path.basename(norm_path)
        if fnmatch.fnmatch(basename, pattern):
            return True

    return False


def list_files(directory: Union[str, Path],
              file_extensions: Optional[List[str]]= None,
              exclude_patterns: Optional[List[str]]= None,
              recursive: bool = True) -> List[str]:
    """
    List files in a directory with optional filtering.

    Args:
        directory: Directory to list files from
        file_extensions: Optional list of file extensions to include
        exclude_patterns: Optional list of patterns to exclude
        recursive: Whether to recursively list files in subdirectories

    Returns:
        List of file paths
    """
    directory = Path(directory)
    result = []

    # Ensure file extensions start with a dot
    if file_extensions:
        file_extensions = [ext if ext.startswith('.') else f'.{ext}' for ext in file_extensions]

    # Walk the directory
    if recursive:
        for root, dirs, files in os.walk(directory):
            # Skip excluded directories
            dirs[:] = [d for d in dirs if not is_excluded(os.path.join(root, d), exclude_patterns)]

            # Process files
            for file in files:
                file_path = os.path.join(root, file)

                # Skip excluded files
                if is_excluded(file_path, exclude_patterns):
                    continue

                # Filter by extension if needed
                if file_extensions and not any(file.endswith(ext) for ext in file_extensions):
                    continue

                result.append(normalize_path(file_path))
    else:
        # Only list files in the top-level directory
        for item in directory.iterdir():
            if item.is_file():
                # Skip excluded files
                if is_excluded(item, exclude_patterns):
                    continue

                # Filter by extension if needed
                if file_extensions and not any(item.name.endswith(ext) for ext in file_extensions):
                    continue

                result.append(normalize_path(item))

    return sorted(result)


def find_python_files(directory: Union[str, Path]) -> List[Path]:
    """
    Find all Python files in a directory.

    Args:
        directory: Directory to search

    Returns:
        List of Python file paths
    """
    return [Path(p) for p in list_files(directory, file_extensions=['.py'])]


def count_lines(file_path: Union[str, Path]) -> int:
    """
    Count the number of lines in a file.

    Args:
        file_path: Path to the file

    Returns:
        Number of lines
    """
    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
        return sum(1 for _ in f)
