"""
Preset Manager
===========

This module provides utilities for managing configuration presets.
"""

from pathlib import Path

from .config_utils import load_config, merge_configs
from .gitignore_utils import get_exclude_patterns


def get_available_presets() -> List[str]:
    """
    Get a list of available preset names.
    
    Returns:
        List of preset names (without the .yaml extension)
    """
    preset_dir = Path(__file__).parent.parent.parent / "config" / "presets"
    if not preset_dir.exists():
        return []
    
    presets = []
    for file in preset_dir.glob("*.yaml"):
        presets.append(file.stem)
    
    return sorted(presets)


def get_preset_path(preset_name: str) -> Optional[Path]:
    """
    Get the path to a preset configuration file.
    
    Args:
        preset_name: Name of the preset (without .yaml extension)
        
    Returns:
        Path to the preset file, or None if not found
    """
    preset_dir = Path(__file__).parent.parent.parent / "config" / "presets"
    preset_path = preset_dir / f"{preset_name}.yaml"
    
    if preset_path.exists():
        return preset_path
    
    return None


def load_preset(preset_name: str) -> Dict[str, Any]:
    """
    Load a preset configuration.
    
    Args:
        preset_name: Name of the preset (without .yaml extension)
        
    Returns:
        Configuration dictionary
        
    Raises:
        ValueError: If the preset is not found
    """
    preset_path = get_preset_path(preset_name)
    if not preset_path:
        raise ValueError(f"Preset '{preset_name}' not found")
    
    return load_config(preset_path)


def apply_preset(preset_name: str, project_path: str, 
                base_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Apply a preset configuration to a project, integrating .gitignore if specified.
    
    Args:
        preset_name: Name of the preset (without .yaml extension)
        project_path: Path to the project directory
        base_config: Optional base configuration to merge with
        
    Returns:
        Merged configuration dictionary
        
    Raises:
        ValueError: If the preset is not found
    """
    # Load the preset
    preset_config = load_preset(preset_name)
    
    # Start with base config or empty dict
    config = base_config or {}
    
    # Merge with preset
    config = merge_configs(config, preset_config)
    
    # Handle .gitignore integration if enabled
    if config.get("use_gitignore", False):
        # Get existing exclude patterns
        exclude_patterns = config.get("exclude_patterns", [])
        
        # Add patterns from .gitignore
        exclude_patterns = get_exclude_patterns(
            project_path, 
            use_gitignore=True, 
            base_patterns=exclude_patterns
        )
        
        # Update config with new patterns
        config["exclude_patterns"] = exclude_patterns
    
    return config
