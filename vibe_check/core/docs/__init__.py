"""
Documentation Package
=================

This package provides standardized documentation templates and utilities
for the Vibe Check codebase. It helps ensure consistent documentation
across the project.
"""

    MODULE_TEMPLATE,
    CLASS_TEMPLATE,
    FUNCTION_TEMPLATE,
    ASYNC_FUNCTION_TEMPLATE,
    PROPERTY_TEMPLATE,
    EXCEPTION_TEMPLATE,
    FILE_HEADER_TEMPLATE,
)

    generate_module_docstring,
    generate_class_docstring,
    generate_function_docstring,
    generate_async_function_docstring,
    generate_property_docstring,
    generate_exception_docstring,
    generate_file_header,
)

__all__ = [
    # Templates
    'MODULE_TEMPLATE',
    'CLASS_TEMPLATE',
    'FUNCTION_TEMPLATE',
    'ASYNC_FUNCTION_TEMPLATE',
    'PROPERTY_TEMPLATE',
    'EXCEPTION_TEMPLATE',
    'FILE_HEADER_TEMPLATE',
    
    # Utilities
    'generate_module_docstring',
    'generate_class_docstring',
    'generate_function_docstring',
    'generate_async_function_docstring',
    'generate_property_docstring',
    'generate_exception_docstring',
    'generate_file_header',
]
