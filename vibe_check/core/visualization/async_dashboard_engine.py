"""
Async Dashboard Engine
=====================

Enhanced async version of the dashboard engine with async rendering,
WebSocket support, and real-time data streaming capabilities.
"""

import asyncio
import aiofiles
import json
import time
from typing import Dict, List, Any, Optional, AsyncIterator, Callable
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
import logging

from .unified_charts import ChartType, ChartConfig, ChartData
from .dashboard_engine import Dashboard, DashboardPanel, PanelType, DashboardLayout, DashboardTheme

logger = logging.getLogger(__name__)


@dataclass
class AsyncRenderConfig:
    """Configuration for async rendering"""
    max_concurrent_panels: int = 20
    render_timeout: float = 30.0
    enable_streaming: bool = True
    cache_rendered_panels: bool = True
    websocket_enabled: bool = True
    real_time_updates: bool = True


class AsyncChartRenderer:
    """Async chart rendering with optimized performance"""
    
    def __init__(self, config: AsyncRenderConfig):
        self.config = config
        self.semaphore = asyncio.Semaphore(config.max_concurrent_panels)
        self.executor = ThreadPoolExecutor(max_workers=4)
        self.render_cache = {} if config.cache_rendered_panels else None
        
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        self.executor.shutdown(wait=False)
    
    async def render_chart_async(self, chart_type: ChartType, config: ChartConfig, 
                               data: ChartData, panel_id: str) -> str:
        """Async chart rendering with caching"""
        async with self.semaphore:
            # Check cache first
            cache_key = f"{panel_id}_{hash(str(data.x_values))}"
            if self.render_cache and cache_key in self.render_cache:
                return self.render_cache[cache_key]
            
            # Render chart
            loop = asyncio.get_event_loop()
            html = await loop.run_in_executor(
                self.executor,
                self._render_chart_sync,
                chart_type, config, data, panel_id
            )
            
            # Cache result
            if self.render_cache:
                self.render_cache[cache_key] = html
            
            return html
    
    def _render_chart_sync(self, chart_type: ChartType, config: ChartConfig, 
                          data: ChartData, panel_id: str) -> str:
        """Synchronous chart rendering"""
        chart_config = {
            'type': self._get_chartjs_type(chart_type),
            'data': {
                'labels': data.labels or data.x_values,
                'datasets': [{
                    'label': config.title,
                    'data': data.y_values,
                    'backgroundColor': data.colors or ['rgba(54, 162, 235, 0.2)'],
                    'borderColor': data.colors or ['rgba(54, 162, 235, 1)'],
                    'borderWidth': 1
                }]
            },
            'options': {
                'responsive': True,
                'plugins': {
                    'title': {
                        'display': bool(config.title),
                        'text': config.title
                    }
                },
                'animation': {
                    'duration': 1000 if config.animation else 0
                }
            }
        }
        
        html = f"""
        <div style="width: {config.width}px; height: {config.height}px;">
            <canvas id="{panel_id}"></canvas>
        </div>
        <script>
            const ctx_{panel_id} = document.getElementById('{panel_id}').getContext('2d');
            const chart_{panel_id} = new Chart(ctx_{panel_id}, {json.dumps(chart_config)});
        </script>
        """
        
        return html
    
    def _get_chartjs_type(self, chart_type: ChartType) -> str:
        """Map chart type to Chart.js type"""
        mapping = {
            ChartType.LINE: 'line',
            ChartType.BAR: 'bar',
            ChartType.SCATTER: 'scatter',
            ChartType.PIE: 'pie',
            ChartType.TIME_SERIES: 'line',
            ChartType.HISTOGRAM: 'bar',
        }
        return mapping.get(chart_type, 'line')


class AsyncDashboardEngine:
    """Enhanced async dashboard engine with real-time capabilities"""
    
    def __init__(self, config: Optional[AsyncRenderConfig] = None):
        self.config = config or AsyncRenderConfig()
        self.dashboards: Dict[str, Dashboard] = {}
        self.chart_renderer = None
        self.websocket_connections = set()
        self.real_time_data = {}
        
    async def __aenter__(self):
        self.chart_renderer = AsyncChartRenderer(self.config)
        await self.chart_renderer.__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.chart_renderer:
            await self.chart_renderer.__aexit__(exc_type, exc_val, exc_tb)
    
    def create_dashboard(self, dashboard_id: str, title: str, description: str = "") -> Dashboard:
        """Create a new dashboard"""
        dashboard = Dashboard(
            id=dashboard_id,
            title=title,
            description=description
        )
        
        self.dashboards[dashboard_id] = dashboard
        return dashboard
    
    def get_dashboard(self, dashboard_id: str) -> Optional[Dashboard]:
        """Get dashboard by ID"""
        return self.dashboards.get(dashboard_id)
    
    async def render_panel_async(self, panel: DashboardPanel, data: Optional[Dict[str, Any]] = None) -> str:
        """Async panel rendering"""
        if panel.panel_type == PanelType.CHART:
            return await self._render_chart_panel_async(panel, data)
        elif panel.panel_type == PanelType.METRIC:
            return await self._render_metric_panel_async(panel, data)
        elif panel.panel_type == PanelType.TEXT:
            return await self._render_text_panel_async(panel, data)
        else:
            return f"<div>Panel type {panel.panel_type.value} not implemented</div>"
    
    async def _render_chart_panel_async(self, panel: DashboardPanel, data: Optional[Dict[str, Any]] = None) -> str:
        """Async chart panel rendering"""
        chart_type_str = panel.config.get('chart_type', 'line')
        chart_type = ChartType(chart_type_str)
        
        config = ChartConfig(
            title="",  # Title handled by panel
            width=panel.position.get('width', 6) * 100,
            height=panel.position.get('height', 4) * 50,
            animation=True
        )
        
        # Prepare chart data
        if data and panel.id in data:
            panel_data = data[panel.id]
            chart_data = ChartData(
                x_values=panel_data.get('x', []),
                y_values=panel_data.get('y', []),
                labels=panel_data.get('labels', []),
                colors=panel_data.get('colors', [])
            )
        else:
            # Sample data
            chart_data = ChartData(
                x_values=['Jan', 'Feb', 'Mar', 'Apr', 'May'],
                y_values=[10, 20, 15, 25, 30]
            )
        
        return await self.chart_renderer.render_chart_async(chart_type, config, chart_data, panel.id)
    
    async def _render_metric_panel_async(self, panel: DashboardPanel, data: Optional[Dict[str, Any]] = None) -> str:
        """Async metric panel rendering"""
        metric_name = panel.config.get('metric_name', 'Unknown')
        
        # Get metric value from data
        if data and panel.id in data:
            value = data[panel.id].get('value', 0)
        else:
            value = 42  # Sample value
        
        # Format value based on type
        if isinstance(value, float):
            formatted_value = f"{value:.1f}"
        else:
            formatted_value = str(value)
        
        return f"""
        <div class="metric-value" style="font-size: 2em; text-align: center; color: #007bff;">
            {formatted_value}
        </div>
        <div style="text-align: center; color: #666; margin-top: 10px;">
            {metric_name}
        </div>
        """
    
    async def _render_text_panel_async(self, panel: DashboardPanel, data: Optional[Dict[str, Any]] = None) -> str:
        """Async text panel rendering"""
        text_content = panel.config.get('content', 'No content')
        return f"<div>{text_content}</div>"
    
    async def render_dashboard_async(self, dashboard_id: str, data: Optional[Dict[str, Any]] = None) -> str:
        """Async dashboard rendering with concurrent panel processing"""
        dashboard = self.get_dashboard(dashboard_id)
        if not dashboard:
            return "<div>Dashboard not found</div>"
        
        # Render panels concurrently
        panel_tasks = [
            self.render_panel_async(panel, data)
            for panel in dashboard.panels
        ]
        
        try:
            panel_htmls = await asyncio.wait_for(
                asyncio.gather(*panel_tasks),
                timeout=self.config.render_timeout
            )
        except asyncio.TimeoutError:
            logger.warning(f"Dashboard rendering timed out: {dashboard_id}")
            panel_htmls = ["<div>Rendering timed out</div>"] * len(dashboard.panels)
        
        # Build complete HTML
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>{dashboard.title} - Vibe Check Dashboard</title>
            <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
            <script src="https://unpkg.com/gridstack@latest/dist/gridstack-all.js"></script>
            <link rel="stylesheet" href="https://unpkg.com/gridstack@latest/dist/gridstack.min.css"/>
            <style>
                body {{
                    font-family: {dashboard.theme.fonts['family']};
                    background-color: {dashboard.theme.colors['background']};
                    color: {dashboard.theme.colors['text']};
                    margin: 0;
                    padding: 20px;
                }}
                .dashboard-header {{
                    text-align: center;
                    margin-bottom: 20px;
                    padding: 20px;
                    background: linear-gradient(135deg, {dashboard.theme.colors['primary']}, {dashboard.theme.colors['secondary']});
                    color: white;
                    border-radius: 8px;
                }}
                .grid-stack-item-content {{
                    background: white;
                    border: 1px solid #ddd;
                    border-radius: 8px;
                    padding: 15px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }}
                .panel-title {{
                    font-weight: bold;
                    margin-bottom: 10px;
                    color: {dashboard.theme.colors['primary']};
                }}
            </style>
        </head>
        <body>
            <div class="dashboard-header">
                <h1>{dashboard.title}</h1>
                {f'<p>{dashboard.description}</p>' if dashboard.description else ''}
            </div>
            
            <div class="grid-stack">
        """
        
        # Add panels with rendered content
        for panel, panel_html in zip(dashboard.panels, panel_htmls):
            html += f"""
                <div class="grid-stack-item" 
                     gs-x="{panel.position.get('x', 0)}" 
                     gs-y="{panel.position.get('y', 0)}"
                     gs-w="{panel.position.get('width', 6)}" 
                     gs-h="{panel.position.get('height', 4)}">
                    <div class="grid-stack-item-content">
                        <div class="panel-title">{panel.title}</div>
                        {panel_html}
                    </div>
                </div>
            """
        
        html += """
            </div>
            
            <script>
                // Initialize GridStack
                GridStack.init();
                
                // WebSocket connection for real-time updates
                if (typeof WebSocket !== 'undefined') {
                    const ws = new WebSocket('ws://localhost:8765/dashboard');
                    ws.onmessage = function(event) {
                        const data = JSON.parse(event.data);
                        // Handle real-time updates
                        console.log('Real-time update:', data);
                    };
                }
            </script>
        </body>
        </html>
        """
        
        return html
    
    async def save_dashboard_async(self, dashboard_id: str, file_path: Path, data: Optional[Dict[str, Any]] = None):
        """Async dashboard saving to file"""
        html = await self.render_dashboard_async(dashboard_id, data)
        
        async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
            await f.write(html)
        
        logger.info(f"Dashboard saved to: {file_path}")
    
    async def stream_dashboard_updates(self, dashboard_id: str) -> AsyncIterator[Dict[str, Any]]:
        """Stream real-time dashboard updates"""
        dashboard = self.get_dashboard(dashboard_id)
        if not dashboard:
            return
        
        while True:
            # Generate sample real-time data
            update_data = {
                'timestamp': time.time(),
                'dashboard_id': dashboard_id,
                'panels': {}
            }
            
            for panel in dashboard.panels:
                if panel.panel_type == PanelType.METRIC:
                    # Simulate real-time metric updates
                    import random
                    update_data['panels'][panel.id] = {
                        'value': random.randint(10, 100)
                    }
            
            yield update_data
            await asyncio.sleep(1)  # Update every second
    
    async def create_monitoring_dashboard_async(self, dashboard_id: str = "async_monitoring") -> Dashboard:
        """Create async monitoring dashboard"""
        dashboard = self.create_dashboard(
            dashboard_id,
            "Async Vibe Check Monitoring",
            "Real-time async monitoring dashboard"
        )
        
        # Add async-specific metrics
        from .dashboard_engine import PanelType
        
        # System metrics
        dashboard.add_panel(DashboardPanel(
            id="async_cpu_usage",
            title="Async CPU Usage",
            panel_type=PanelType.METRIC,
            position={'x': 0, 'y': 0, 'width': 3, 'height': 3},
            config={'metric_name': 'cpu_percent'},
            refresh_interval=5
        ))
        
        dashboard.add_panel(DashboardPanel(
            id="async_memory_usage",
            title="Async Memory Usage",
            panel_type=PanelType.METRIC,
            position={'x': 3, 'y': 0, 'width': 3, 'height': 3},
            config={'metric_name': 'memory_percent'},
            refresh_interval=5
        ))
        
        # Async performance metrics
        dashboard.add_panel(DashboardPanel(
            id="async_tasks_active",
            title="Active Async Tasks",
            panel_type=PanelType.METRIC,
            position={'x': 6, 'y': 0, 'width': 3, 'height': 3},
            config={'metric_name': 'active_tasks'},
            refresh_interval=1
        ))
        
        dashboard.add_panel(DashboardPanel(
            id="async_throughput",
            title="Analysis Throughput",
            panel_type=PanelType.CHART,
            position={'x': 0, 'y': 3, 'width': 12, 'height': 4},
            config={'chart_type': 'time_series'},
            refresh_interval=5
        ))
        
        return dashboard


# Convenience functions
async def render_dashboard_async(dashboard_id: str, engine: AsyncDashboardEngine, 
                               data: Optional[Dict[str, Any]] = None) -> str:
    """Async dashboard rendering function"""
    return await engine.render_dashboard_async(dashboard_id, data)


def render_dashboard_sync(dashboard_id: str, engine: AsyncDashboardEngine, 
                         data: Optional[Dict[str, Any]] = None) -> str:
    """Synchronous wrapper for backward compatibility"""
    return asyncio.run(render_dashboard_async(dashboard_id, engine, data))
