"""
Unified Dashboard Engine
=======================

Grafana-replacement dashboard engine that provides real-time monitoring
dashboards for the Vibe Check monitoring platform.
"""

import json
import time
import asyncio
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path

from .unified_charts import UnifiedChartEngine, ChartType, ChartConfig, ChartData


class PanelType(Enum):
    """Dashboard panel types"""
    CHART = "chart"
    METRIC = "metric"
    TABLE = "table"
    TEXT = "text"
    ALERT = "alert"
    LOG = "log"


@dataclass
class DashboardPanel:
    """Dashboard panel configuration"""
    id: str
    title: str
    panel_type: PanelType
    position: Dict[str, int]  # x, y, width, height
    config: Dict[str, Any] = field(default_factory=dict)
    data_source: str = ""
    query: str = ""
    refresh_interval: int = 30  # seconds


@dataclass
class DashboardLayout:
    """Dashboard layout configuration"""
    grid_size: int = 12
    row_height: int = 30
    margin: List[int] = field(default_factory=lambda: [10, 10])
    container_padding: List[int] = field(default_factory=lambda: [10, 10])


@dataclass
class DashboardTheme:
    """Dashboard theme configuration"""
    name: str
    colors: Dict[str, str] = field(default_factory=dict)
    fonts: Dict[str, str] = field(default_factory=dict)
    
    def __post_init__(self):
        if not self.colors:
            self.colors = {
                'primary': '#007bff',
                'secondary': '#6c757d',
                'success': '#28a745',
                'danger': '#dc3545',
                'warning': '#ffc107',
                'info': '#17a2b8',
                'background': '#ffffff',
                'text': '#333333'
            }
        
        if not self.fonts:
            self.fonts = {
                'family': 'Arial, sans-serif',
                'size': '14px',
                'weight': 'normal'
            }


@dataclass
class Dashboard:
    """Dashboard configuration"""
    id: str
    title: str
    description: str = ""
    panels: List[DashboardPanel] = field(default_factory=list)
    layout: DashboardLayout = field(default_factory=DashboardLayout)
    theme: DashboardTheme = field(default_factory=lambda: DashboardTheme("default"))
    refresh_interval: int = 30  # seconds
    auto_refresh: bool = True
    created_at: float = field(default_factory=time.time)
    updated_at: float = field(default_factory=time.time)
    
    def add_panel(self, panel: DashboardPanel):
        """Add panel to dashboard"""
        self.panels.append(panel)
        self.updated_at = time.time()
    
    def remove_panel(self, panel_id: str) -> bool:
        """Remove panel from dashboard"""
        for i, panel in enumerate(self.panels):
            if panel.id == panel_id:
                del self.panels[i]
                self.updated_at = time.time()
                return True
        return False
    
    def get_panel(self, panel_id: str) -> Optional[DashboardPanel]:
        """Get panel by ID"""
        for panel in self.panels:
            if panel.id == panel_id:
                return panel
        return None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'panels': [self._panel_to_dict(panel) for panel in self.panels],
            'layout': self.layout.__dict__,
            'theme': self.theme.__dict__,
            'refresh_interval': self.refresh_interval,
            'auto_refresh': self.auto_refresh,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
    
    def _panel_to_dict(self, panel: DashboardPanel) -> Dict[str, Any]:
        """Convert panel to dictionary"""
        return {
            'id': panel.id,
            'title': panel.title,
            'type': panel.panel_type.value,
            'position': panel.position,
            'config': panel.config,
            'data_source': panel.data_source,
            'query': panel.query,
            'refresh_interval': panel.refresh_interval
        }


class UnifiedDashboardEngine:
    """Main dashboard engine - Grafana replacement"""
    
    def __init__(self):
        self.dashboards: Dict[str, Dashboard] = {}
        self.chart_engine = UnifiedChartEngine()
        self.data_sources: Dict[str, Any] = {}
        self.running_dashboards: Dict[str, bool] = {}
        
    def create_dashboard(self, dashboard_id: str, title: str, description: str = "") -> Dashboard:
        """Create a new dashboard"""
        dashboard = Dashboard(
            id=dashboard_id,
            title=title,
            description=description
        )
        
        self.dashboards[dashboard_id] = dashboard
        return dashboard
    
    def get_dashboard(self, dashboard_id: str) -> Optional[Dashboard]:
        """Get dashboard by ID"""
        return self.dashboards.get(dashboard_id)
    
    def delete_dashboard(self, dashboard_id: str) -> bool:
        """Delete dashboard"""
        if dashboard_id in self.dashboards:
            del self.dashboards[dashboard_id]
            if dashboard_id in self.running_dashboards:
                del self.running_dashboards[dashboard_id]
            return True
        return False
    
    def add_chart_panel(self, dashboard_id: str, panel_id: str, title: str, 
                       chart_type: ChartType, position: Dict[str, int],
                       data_source: str = "", query: str = "") -> bool:
        """Add chart panel to dashboard"""
        dashboard = self.get_dashboard(dashboard_id)
        if not dashboard:
            return False
        
        panel = DashboardPanel(
            id=panel_id,
            title=title,
            panel_type=PanelType.CHART,
            position=position,
            config={'chart_type': chart_type.value},
            data_source=data_source,
            query=query
        )
        
        dashboard.add_panel(panel)
        return True
    
    def add_metric_panel(self, dashboard_id: str, panel_id: str, title: str,
                        position: Dict[str, int], metric_name: str,
                        data_source: str = "", query: str = "") -> bool:
        """Add metric panel to dashboard"""
        dashboard = self.get_dashboard(dashboard_id)
        if not dashboard:
            return False
        
        panel = DashboardPanel(
            id=panel_id,
            title=title,
            panel_type=PanelType.METRIC,
            position=position,
            config={'metric_name': metric_name},
            data_source=data_source,
            query=query
        )
        
        dashboard.add_panel(panel)
        return True
    
    def render_dashboard_html(self, dashboard_id: str, data: Optional[Dict[str, Any]] = None) -> str:
        """Render dashboard as HTML"""
        dashboard = self.get_dashboard(dashboard_id)
        if not dashboard:
            return "<div>Dashboard not found</div>"
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>{dashboard.title} - Vibe Check Dashboard</title>
            <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
            <script src="https://unpkg.com/gridstack@latest/dist/gridstack-all.js"></script>
            <link rel="stylesheet" href="https://unpkg.com/gridstack@latest/dist/gridstack.min.css"/>
            <style>
                body {{
                    font-family: {dashboard.theme.fonts['family']};
                    background-color: {dashboard.theme.colors['background']};
                    color: {dashboard.theme.colors['text']};
                    margin: 0;
                    padding: 20px;
                }}
                .dashboard-header {{
                    text-align: center;
                    margin-bottom: 20px;
                    padding: 20px;
                    background: linear-gradient(135deg, {dashboard.theme.colors['primary']}, {dashboard.theme.colors['secondary']});
                    color: white;
                    border-radius: 8px;
                }}
                .dashboard-title {{
                    font-size: 2em;
                    margin: 0;
                }}
                .dashboard-description {{
                    font-size: 1.1em;
                    margin: 10px 0 0 0;
                    opacity: 0.9;
                }}
                .grid-stack {{
                    background: {dashboard.theme.colors['background']};
                }}
                .grid-stack-item-content {{
                    background: white;
                    border: 1px solid #ddd;
                    border-radius: 8px;
                    padding: 15px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }}
                .panel-title {{
                    font-weight: bold;
                    margin-bottom: 10px;
                    color: {dashboard.theme.colors['primary']};
                }}
                .metric-value {{
                    font-size: 2em;
                    font-weight: bold;
                    text-align: center;
                    color: {dashboard.theme.colors['success']};
                }}
                .refresh-indicator {{
                    position: fixed;
                    top: 10px;
                    right: 10px;
                    background: {dashboard.theme.colors['info']};
                    color: white;
                    padding: 5px 10px;
                    border-radius: 4px;
                    font-size: 0.9em;
                }}
            </style>
        </head>
        <body>
            <div class="dashboard-header">
                <h1 class="dashboard-title">{dashboard.title}</h1>
                {f'<p class="dashboard-description">{dashboard.description}</p>' if dashboard.description else ''}
            </div>
            
            <div class="refresh-indicator" id="refreshIndicator">
                Auto-refresh: {dashboard.refresh_interval}s
            </div>
            
            <div class="grid-stack">
        """
        
        # Add panels
        for panel in dashboard.panels:
            panel_html = self._render_panel_html(panel, data)
            html += f"""
                <div class="grid-stack-item" 
                     gs-x="{panel.position.get('x', 0)}" 
                     gs-y="{panel.position.get('y', 0)}"
                     gs-w="{panel.position.get('width', 6)}" 
                     gs-h="{panel.position.get('height', 4)}">
                    <div class="grid-stack-item-content">
                        <div class="panel-title">{panel.title}</div>
                        {panel_html}
                    </div>
                </div>
            """
        
        html += """
            </div>
            
            <script>
                // Initialize GridStack
                GridStack.init();
                
                // Auto-refresh functionality
                let refreshInterval = """ + str(dashboard.refresh_interval * 1000) + """;
                let autoRefresh = """ + ("true" if dashboard.auto_refresh else "false") + """;
                
                if (autoRefresh) {
                    setInterval(() => {
                        location.reload();
                    }, refreshInterval);
                    
                    // Update refresh indicator
                    let countdown = """ + str(dashboard.refresh_interval) + """;
                    setInterval(() => {
                        countdown--;
                        if (countdown <= 0) countdown = """ + str(dashboard.refresh_interval) + """;
                        document.getElementById('refreshIndicator').textContent = 
                            `Auto-refresh: ${countdown}s`;
                    }, 1000);
                }
            </script>
        </body>
        </html>
        """
        
        return html
    
    def _render_panel_html(self, panel: DashboardPanel, data: Optional[Dict[str, Any]] = None) -> str:
        """Render individual panel HTML"""
        if panel.panel_type == PanelType.CHART:
            return self._render_chart_panel(panel, data)
        elif panel.panel_type == PanelType.METRIC:
            return self._render_metric_panel(panel, data)
        elif panel.panel_type == PanelType.TEXT:
            return self._render_text_panel(panel, data)
        else:
            return f"<div>Panel type {panel.panel_type.value} not implemented</div>"
    
    def _render_chart_panel(self, panel: DashboardPanel, data: Optional[Dict[str, Any]] = None) -> str:
        """Render chart panel"""
        chart_type_str = panel.config.get('chart_type', 'line')
        chart_type = ChartType(chart_type_str)
        
        chart = self.chart_engine.create_chart(
            chart_type,
            panel.id,
            ChartConfig(
                title="",  # Title is handled by panel
                width=panel.position.get('width', 6) * 100,
                height=panel.position.get('height', 4) * 50
            )
        )
        
        # Set sample data if no real data provided
        if data and panel.id in data:
            panel_data = data[panel.id]
            chart.set_data(ChartData(
                x_values=panel_data.get('x', []),
                y_values=panel_data.get('y', []),
                labels=panel_data.get('labels', [])
            ))
        else:
            # Sample data
            chart.set_data(ChartData(
                x_values=['Jan', 'Feb', 'Mar', 'Apr', 'May'],
                y_values=[10, 20, 15, 25, 30]
            ))
        
        return chart.to_html()
    
    def _render_metric_panel(self, panel: DashboardPanel, data: Optional[Dict[str, Any]] = None) -> str:
        """Render metric panel"""
        metric_name = panel.config.get('metric_name', 'Unknown')
        
        # Get metric value from data
        if data and panel.id in data:
            value = data[panel.id].get('value', 0)
        else:
            value = 42  # Sample value
        
        return f"""
        <div class="metric-value">{value}</div>
        <div style="text-align: center; color: #666; margin-top: 10px;">
            {metric_name}
        </div>
        """
    
    def _render_text_panel(self, panel: DashboardPanel, data: Optional[Dict[str, Any]] = None) -> str:
        """Render text panel"""
        text_content = panel.config.get('content', 'No content')
        return f"<div>{text_content}</div>"
    
    def create_monitoring_dashboard(self, dashboard_id: str = "monitoring") -> Dashboard:
        """Create a default monitoring dashboard"""
        dashboard = self.create_dashboard(
            dashboard_id,
            "Vibe Check Monitoring",
            "Real-time system and application monitoring"
        )
        
        # Add system metrics panels
        self.add_metric_panel(dashboard_id, "cpu_usage", "CPU Usage", 
                             {'x': 0, 'y': 0, 'width': 3, 'height': 3}, "cpu_percent")
        
        self.add_metric_panel(dashboard_id, "memory_usage", "Memory Usage", 
                             {'x': 3, 'y': 0, 'width': 3, 'height': 3}, "memory_percent")
        
        self.add_metric_panel(dashboard_id, "disk_usage", "Disk Usage", 
                             {'x': 6, 'y': 0, 'width': 3, 'height': 3}, "disk_percent")
        
        self.add_metric_panel(dashboard_id, "process_count", "Processes", 
                             {'x': 9, 'y': 0, 'width': 3, 'height': 3}, "process_count")
        
        # Add time series charts
        self.add_chart_panel(dashboard_id, "cpu_chart", "CPU Usage Over Time", 
                            ChartType.TIME_SERIES, {'x': 0, 'y': 3, 'width': 6, 'height': 4})
        
        self.add_chart_panel(dashboard_id, "memory_chart", "Memory Usage Over Time", 
                            ChartType.TIME_SERIES, {'x': 6, 'y': 3, 'width': 6, 'height': 4})
        
        return dashboard
    
    def export_dashboard(self, dashboard_id: str, format: str = "json") -> str:
        """Export dashboard configuration"""
        dashboard = self.get_dashboard(dashboard_id)
        if not dashboard:
            return ""
        
        if format == "json":
            return json.dumps(dashboard.to_dict(), indent=2)
        elif format == "html":
            return self.render_dashboard_html(dashboard_id)
        else:
            return ""
