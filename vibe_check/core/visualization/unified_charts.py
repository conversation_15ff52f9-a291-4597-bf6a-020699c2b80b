"""
Unified Chart Engine
===================

Consolidated chart system that merges functionality from ai/visualization/interactive_charts.py
and ui/visualization/charts.py into a single, powerful charting interface.
"""

import json
import time
from enum import Enum
from typing import Dict, List, Any, Optional, Union, Tuple
from dataclasses import dataclass, field
from pathlib import Path


class ChartType(Enum):
    """Supported chart types"""
    LINE = "line"
    BAR = "bar"
    SCATTER = "scatter"
    PIE = "pie"
    HEATMAP = "heatmap"
    GAUGE = "gauge"
    TREEMAP = "treemap"
    TIME_SERIES = "time_series"
    HISTOGRAM = "histogram"
    BOX_PLOT = "box_plot"


@dataclass
class ChartData:
    """Chart data structure"""
    x_values: List[Any]
    y_values: List[Any]
    labels: List[str] = field(default_factory=list)
    colors: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ChartConfig:
    """Chart configuration"""
    title: str = ""
    x_label: str = ""
    y_label: str = ""
    width: int = 800
    height: int = 600
    theme: str = "default"
    interactive: bool = True
    show_legend: bool = True
    show_grid: bool = True
    animation: bool = True


class InteractiveChart:
    """Base class for interactive charts"""
    
    def __init__(self, chart_type: ChartType, config: Optional[ChartConfig] = None):
        self.chart_type = chart_type
        self.config = config or ChartConfig()
        self.data = None
        self.chart_id = f"chart_{int(time.time() * 1000)}"
        
    def set_data(self, data: ChartData):
        """Set chart data"""
        self.data = data
        
    def update_config(self, **kwargs):
        """Update chart configuration"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
    
    def to_html(self) -> str:
        """Generate HTML representation"""
        if not self.data:
            return "<div>No data available</div>"
        
        # Generate Chart.js or Plotly.js HTML
        return self._generate_html()
    
    def to_json(self) -> str:
        """Export chart configuration as JSON"""
        return json.dumps({
            'type': self.chart_type.value,
            'config': self.config.__dict__,
            'data': {
                'x': self.data.x_values if self.data else [],
                'y': self.data.y_values if self.data else [],
                'labels': self.data.labels if self.data else [],
                'colors': self.data.colors if self.data else [],
            }
        }, indent=2)
    
    def _generate_html(self) -> str:
        """Generate HTML with Chart.js"""
        chart_js_config = self._get_chartjs_config()
        
        html = f"""
        <div style="width: {self.config.width}px; height: {self.config.height}px;">
            <canvas id="{self.chart_id}"></canvas>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
            const ctx_{self.chart_id} = document.getElementById('{self.chart_id}').getContext('2d');
            const chart_{self.chart_id} = new Chart(ctx_{self.chart_id}, {chart_js_config});
        </script>
        """
        
        return html
    
    def _get_chartjs_config(self) -> str:
        """Get Chart.js configuration"""
        if not self.data:
            return "{}"
        
        config = {
            'type': self._get_chartjs_type(),
            'data': {
                'labels': self.data.labels or self.data.x_values,
                'datasets': [{
                    'label': self.config.title,
                    'data': self.data.y_values,
                    'backgroundColor': self.data.colors or ['rgba(54, 162, 235, 0.2)'],
                    'borderColor': self.data.colors or ['rgba(54, 162, 235, 1)'],
                    'borderWidth': 1
                }]
            },
            'options': {
                'responsive': True,
                'plugins': {
                    'title': {
                        'display': bool(self.config.title),
                        'text': self.config.title
                    },
                    'legend': {
                        'display': self.config.show_legend
                    }
                },
                'scales': {
                    'x': {
                        'display': True,
                        'title': {
                            'display': bool(self.config.x_label),
                            'text': self.config.x_label
                        },
                        'grid': {
                            'display': self.config.show_grid
                        }
                    },
                    'y': {
                        'display': True,
                        'title': {
                            'display': bool(self.config.y_label),
                            'text': self.config.y_label
                        },
                        'grid': {
                            'display': self.config.show_grid
                        }
                    }
                },
                'animation': {
                    'duration': 1000 if self.config.animation else 0
                }
            }
        }
        
        return json.dumps(config)
    
    def _get_chartjs_type(self) -> str:
        """Map chart type to Chart.js type"""
        mapping = {
            ChartType.LINE: 'line',
            ChartType.BAR: 'bar',
            ChartType.SCATTER: 'scatter',
            ChartType.PIE: 'pie',
            ChartType.TIME_SERIES: 'line',
            ChartType.HISTOGRAM: 'bar',
        }
        return mapping.get(self.chart_type, 'line')


class TimeSeriesChart(InteractiveChart):
    """Time series chart for monitoring data"""
    
    def __init__(self, config: Optional[ChartConfig] = None):
        super().__init__(ChartType.TIME_SERIES, config)
        
    def add_time_point(self, timestamp: float, value: float, label: str = ""):
        """Add a time point to the chart"""
        if not self.data:
            self.data = ChartData([], [], [], [])
        
        self.data.x_values.append(timestamp)
        self.data.y_values.append(value)
        if label:
            self.data.labels.append(label)
    
    def set_time_range(self, start_time: float, end_time: float):
        """Filter data to time range"""
        if not self.data:
            return
        
        filtered_x = []
        filtered_y = []
        filtered_labels = []
        
        for i, timestamp in enumerate(self.data.x_values):
            if start_time <= timestamp <= end_time:
                filtered_x.append(timestamp)
                filtered_y.append(self.data.y_values[i])
                if i < len(self.data.labels):
                    filtered_labels.append(self.data.labels[i])
        
        self.data.x_values = filtered_x
        self.data.y_values = filtered_y
        self.data.labels = filtered_labels


class BarChart(InteractiveChart):
    """Bar chart for categorical data"""
    
    def __init__(self, config: Optional[ChartConfig] = None):
        super().__init__(ChartType.BAR, config)


class LineChart(InteractiveChart):
    """Line chart for continuous data"""
    
    def __init__(self, config: Optional[ChartConfig] = None):
        super().__init__(ChartType.LINE, config)


class HeatmapChart(InteractiveChart):
    """Heatmap chart for matrix data"""
    
    def __init__(self, config: Optional[ChartConfig] = None):
        super().__init__(ChartType.HEATMAP, config)
        
    def set_matrix_data(self, matrix: List[List[float]], x_labels: List[str], y_labels: List[str]):
        """Set matrix data for heatmap"""
        # Flatten matrix for Chart.js
        data_points = []
        for i, row in enumerate(matrix):
            for j, value in enumerate(row):
                data_points.append({
                    'x': j,
                    'y': i,
                    'v': value
                })
        
        self.data = ChartData(
            x_values=x_labels,
            y_values=y_labels,
            labels=[],
            metadata={'matrix_data': data_points}
        )


class ScatterChart(InteractiveChart):
    """Scatter plot for correlation analysis"""
    
    def __init__(self, config: Optional[ChartConfig] = None):
        super().__init__(ChartType.SCATTER, config)


class PieChart(InteractiveChart):
    """Pie chart for proportional data"""
    
    def __init__(self, config: Optional[ChartConfig] = None):
        super().__init__(ChartType.PIE, config)


class GaugeChart(InteractiveChart):
    """Gauge chart for single value display"""
    
    def __init__(self, config: Optional[ChartConfig] = None):
        super().__init__(ChartType.GAUGE, config)
        
    def set_value(self, value: float, min_val: float = 0, max_val: float = 100):
        """Set gauge value"""
        self.data = ChartData(
            x_values=[min_val, max_val],
            y_values=[value],
            metadata={'min': min_val, 'max': max_val, 'value': value}
        )


class TreemapChart(InteractiveChart):
    """Treemap chart for hierarchical data"""
    
    def __init__(self, config: Optional[ChartConfig] = None):
        super().__init__(ChartType.TREEMAP, config)


class UnifiedChartEngine:
    """Main chart engine that manages all chart types"""
    
    def __init__(self):
        self.charts: Dict[str, InteractiveChart] = {}
        self.themes = {
            'default': {'primary': '#007bff', 'secondary': '#6c757d'},
            'dark': {'primary': '#17a2b8', 'secondary': '#343a40'},
            'light': {'primary': '#28a745', 'secondary': '#f8f9fa'},
        }
    
    def create_chart(self, chart_type: ChartType, chart_id: str = None, config: Optional[ChartConfig] = None) -> InteractiveChart:
        """Create a new chart"""
        chart_classes = {
            ChartType.LINE: LineChart,
            ChartType.BAR: BarChart,
            ChartType.SCATTER: ScatterChart,
            ChartType.PIE: PieChart,
            ChartType.HEATMAP: HeatmapChart,
            ChartType.GAUGE: GaugeChart,
            ChartType.TREEMAP: TreemapChart,
            ChartType.TIME_SERIES: TimeSeriesChart,
        }
        
        chart_class = chart_classes.get(chart_type, InteractiveChart)
        chart = chart_class(config)
        
        if chart_id:
            chart.chart_id = chart_id
            self.charts[chart_id] = chart
        
        return chart
    
    def get_chart(self, chart_id: str) -> Optional[InteractiveChart]:
        """Get chart by ID"""
        return self.charts.get(chart_id)
    
    def remove_chart(self, chart_id: str) -> bool:
        """Remove chart by ID"""
        if chart_id in self.charts:
            del self.charts[chart_id]
            return True
        return False
    
    def export_all_charts(self, format: str = "html") -> Dict[str, str]:
        """Export all charts in specified format"""
        exports = {}
        
        for chart_id, chart in self.charts.items():
            if format == "html":
                exports[chart_id] = chart.to_html()
            elif format == "json":
                exports[chart_id] = chart.to_json()
        
        return exports
    
    def create_monitoring_dashboard(self, metrics_data: Dict[str, List[Tuple[float, float]]]) -> str:
        """Create a monitoring dashboard with multiple charts"""
        dashboard_html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Vibe Check Monitoring Dashboard</title>
            <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .chart-container { display: inline-block; margin: 20px; }
                .dashboard-title { text-align: center; color: #333; }
            </style>
        </head>
        <body>
            <h1 class="dashboard-title">Vibe Check Monitoring Dashboard</h1>
        """
        
        # Create charts for each metric
        for metric_name, data_points in metrics_data.items():
            timestamps = [point[0] for point in data_points]
            values = [point[1] for point in data_points]
            
            chart = self.create_chart(
                ChartType.TIME_SERIES,
                f"chart_{metric_name}",
                ChartConfig(title=metric_name.replace('_', ' ').title(), width=400, height=300)
            )
            
            chart.set_data(ChartData(timestamps, values))
            dashboard_html += f'<div class="chart-container">{chart.to_html()}</div>'
        
        dashboard_html += """
        </body>
        </html>
        """
        
        return dashboard_html
