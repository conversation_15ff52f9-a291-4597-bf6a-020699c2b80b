"""
Unified Visualization System
===========================

Consolidated visualization module that merges ai/visualization/ and ui/visualization/
into a single, comprehensive visualization interface for Vibe Check.

This module provides:
- Interactive charts and dashboards
- Data aggregation and processing
- Report generation and export
- Real-time monitoring visualizations
- Grafana-replacement dashboard engine
"""

from .unified_charts import (
    UnifiedChartEngine,
    ChartType,
    InteractiveChart,
    TimeSeriesChart,
    BarChart,
    LineChart,
    HeatmapChart,
    ScatterChart,
    <PERSON>hart,
    GaugeChart,
    TreemapChart,
)

from .dashboard_engine import (
    UnifiedDashboardEngine,
    Dashboard,
    DashboardPanel,
    DashboardLayout,
    DashboardTheme,
)

from .data_processor import (
    DataProcessor,
    MetricsAggregator,
    TimeSeriesProcessor,
    StatisticalAnalyzer,
)

from .report_generator import (
    UnifiedReportGenerator,
    ReportFormat,
    ReportTemplate,
    ExportOptions,
)

from .exporters import (
    ChartExporter,
    DashboardExporter,
    DataExporter,
    export_to_html,
    export_to_pdf,
    export_to_png,
    export_to_svg,
    export_to_json,
)

__all__ = [
    # Chart Engine
    'UnifiedChartEngine',
    'ChartType',
    'InteractiveChart',
    'TimeSeriesChart',
    'BarChart',
    'LineChart',
    'HeatmapChart',
    'ScatterChart',
    'PieChart',
    'GaugeChart',
    'TreemapChart',
    
    # Dashboard Engine
    'UnifiedDashboardEngine',
    'Dashboard',
    'DashboardPanel',
    'DashboardLayout',
    'DashboardTheme',
    
    # Data Processing
    'DataProcessor',
    'MetricsAggregator',
    'TimeSeriesProcessor',
    'StatisticalAnalyzer',
    
    # Report Generation
    'UnifiedReportGenerator',
    'ReportFormat',
    'ReportTemplate',
    'ExportOptions',
    
    # Exporters
    'ChartExporter',
    'DashboardExporter',
    'DataExporter',
    'export_to_html',
    'export_to_pdf',
    'export_to_png',
    'export_to_svg',
    'export_to_json',
]
