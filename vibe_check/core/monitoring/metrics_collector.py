"""
Metrics Collection Framework
===========================

Base classes and implementations for collecting various types of metrics.
"""

import asyncio
import time
import psutil
from typing import Dict, List, Optional, Any, Callable
from abc import ABC, abstractmethod
from dataclasses import dataclass
from pathlib import Path

from .time_series_storage import TimeSeriesStorage


@dataclass
class MetricDefinition:
    """Definition of a metric"""
    name: str
    description: str
    unit: str
    labels: Dict[str, str]
    collection_interval: float = 10.0


class MetricsCollector(ABC):
    """Base class for metrics collectors"""
    
    def __init__(self, name: str, interval: float = 10.0, enabled: bool = True):
        self.name = name
        self.interval = interval
        self.enabled = enabled
        self.running = False
        self.task: Optional[asyncio.Task] = None
        self.storage: Optional[TimeSeriesStorage] = None
        self.error_count = 0
        self.last_collection_time = 0.0
        
    @abstractmethod
    async def collect(self) -> Dict[str, float]:
        """Override this method to implement metric collection"""
        pass
    
    def get_metric_definitions(self) -> List[MetricDefinition]:
        """Get definitions of metrics this collector provides"""
        return []
    
    async def start(self, storage: TimeSeriesStorage):
        """Start collecting metrics"""
        if not self.enabled:
            return
            
        self.storage = storage
        self.running = True
        self.task = asyncio.create_task(self._collect_loop())
        print(f"Started metrics collector: {self.name}")
    
    async def stop(self):
        """Stop collecting metrics"""
        self.running = False
        if self.task:
            self.task.cancel()
            try:
                await self.task
            except asyncio.CancelledError:
                pass
        print(f"Stopped metrics collector: {self.name}")
    
    async def _collect_loop(self):
        """Main collection loop"""
        while self.running:
            try:
                start_time = time.time()
                
                # Collect metrics
                metrics = await self.collect()
                
                # Store metrics with collector label
                timestamp = time.time()
                for metric_name, value in metrics.items():
                    await self.storage.store_metric(
                        f"{self.name}_{metric_name}",
                        value,
                        {"collector": self.name},
                        timestamp
                    )
                
                self.last_collection_time = timestamp
                collection_duration = time.time() - start_time
                
                # Store collection performance metrics
                await self.storage.store_metric(
                    f"collector_duration_seconds",
                    collection_duration,
                    {"collector": self.name},
                    timestamp
                )
                
                # Wait for next collection
                sleep_time = max(0, self.interval - collection_duration)
                await asyncio.sleep(sleep_time)
                
            except Exception as e:
                self.error_count += 1
                print(f"Error in collector {self.name}: {e}")
                
                # Store error metric
                if self.storage:
                    await self.storage.store_metric(
                        f"collector_errors_total",
                        self.error_count,
                        {"collector": self.name},
                        time.time()
                    )
                
                # Back off on errors
                await asyncio.sleep(min(self.interval * 2, 60))
    
    def get_status(self) -> Dict[str, Any]:
        """Get collector status"""
        return {
            'name': self.name,
            'enabled': self.enabled,
            'running': self.running,
            'interval': self.interval,
            'error_count': self.error_count,
            'last_collection_time': self.last_collection_time
        }


class SystemMetricsCollector(MetricsCollector):
    """Collect system performance metrics"""
    
    def __init__(self, interval: float = 5.0):
        super().__init__("system", interval)
        self.boot_time = psutil.boot_time()
    
    async def collect(self) -> Dict[str, float]:
        """Collect system metrics"""
        metrics = {}
        
        # CPU metrics
        cpu_percent = psutil.cpu_percent(interval=None)
        metrics['cpu_percent'] = cpu_percent
        
        cpu_count = psutil.cpu_count()
        metrics['cpu_count'] = cpu_count
        
        # Load average (Unix-like systems)
        if hasattr(psutil, 'getloadavg'):
            load_avg = psutil.getloadavg()
            metrics['load_avg_1m'] = load_avg[0]
            metrics['load_avg_5m'] = load_avg[1]
            metrics['load_avg_15m'] = load_avg[2]
        
        # Memory metrics
        memory = psutil.virtual_memory()
        metrics['memory_total_bytes'] = memory.total
        metrics['memory_available_bytes'] = memory.available
        metrics['memory_used_bytes'] = memory.used
        metrics['memory_percent'] = memory.percent
        
        # Swap metrics
        swap = psutil.swap_memory()
        metrics['swap_total_bytes'] = swap.total
        metrics['swap_used_bytes'] = swap.used
        metrics['swap_percent'] = swap.percent
        
        # Disk metrics
        disk_usage = psutil.disk_usage('/')
        metrics['disk_total_bytes'] = disk_usage.total
        metrics['disk_used_bytes'] = disk_usage.used
        metrics['disk_free_bytes'] = disk_usage.free
        metrics['disk_percent'] = (disk_usage.used / disk_usage.total) * 100
        
        # Process metrics
        metrics['process_count'] = len(psutil.pids())
        
        # System uptime
        metrics['uptime_seconds'] = time.time() - self.boot_time
        
        return metrics
    
    def get_metric_definitions(self) -> List[MetricDefinition]:
        """Get system metric definitions"""
        return [
            MetricDefinition("cpu_percent", "CPU utilization percentage", "%", {"collector": "system"}),
            MetricDefinition("memory_percent", "Memory utilization percentage", "%", {"collector": "system"}),
            MetricDefinition("disk_percent", "Disk utilization percentage", "%", {"collector": "system"}),
            MetricDefinition("process_count", "Number of running processes", "count", {"collector": "system"}),
            MetricDefinition("uptime_seconds", "System uptime", "seconds", {"collector": "system"}),
        ]


class CodeQualityCollector(MetricsCollector):
    """Collect code quality metrics from Vibe Check analysis"""
    
    def __init__(self, project_path: str, interval: float = 60.0):
        super().__init__("code_quality", interval)
        self.project_path = Path(project_path)
        self.last_analysis_result = None
    
    async def collect(self) -> Dict[str, float]:
        """Collect code quality metrics"""
        try:
            # Import here to avoid circular imports
            from vibe_check.core.simple_analyzer import analyze_project
            
            # Run analysis
            result = await asyncio.get_event_loop().run_in_executor(
                None, analyze_project, str(self.project_path)
            )
            
            self.last_analysis_result = result
            
            # Extract metrics from analysis result
            metrics = {}
            
            if hasattr(result, 'project_metrics'):
                pm = result.project_metrics
                metrics['complexity_avg'] = getattr(pm, 'average_complexity', 0.0)
                metrics['complexity_max'] = getattr(pm, 'max_complexity', 0.0)
                metrics['issues_total'] = getattr(pm, 'total_issues', 0.0)
                metrics['files_total'] = getattr(pm, 'total_files', 0.0)
                metrics['lines_total'] = getattr(pm, 'total_lines', 0.0)
                metrics['quality_score'] = getattr(pm, 'quality_score', 0.0)
            
            # File-level aggregations
            if hasattr(result, 'file_metrics'):
                file_metrics = result.file_metrics
                if file_metrics:
                    complexities = [fm.complexity for fm in file_metrics if hasattr(fm, 'complexity')]
                    if complexities:
                        metrics['complexity_p95'] = sorted(complexities)[int(len(complexities) * 0.95)]
                        metrics['complexity_p99'] = sorted(complexities)[int(len(complexities) * 0.99)]
            
            return metrics
            
        except Exception as e:
            print(f"Error collecting code quality metrics: {e}")
            return {}
    
    def get_metric_definitions(self) -> List[MetricDefinition]:
        """Get code quality metric definitions"""
        return [
            MetricDefinition("complexity_avg", "Average code complexity", "score", {"collector": "code_quality"}),
            MetricDefinition("complexity_max", "Maximum code complexity", "score", {"collector": "code_quality"}),
            MetricDefinition("issues_total", "Total number of issues", "count", {"collector": "code_quality"}),
            MetricDefinition("files_total", "Total number of files", "count", {"collector": "code_quality"}),
            MetricDefinition("quality_score", "Overall quality score", "score", {"collector": "code_quality"}),
        ]


class ProcessMetricsCollector(MetricsCollector):
    """Collect metrics for the current Python process"""
    
    def __init__(self, interval: float = 10.0):
        super().__init__("process", interval)
        self.process = psutil.Process()
        self.start_time = time.time()
    
    async def collect(self) -> Dict[str, float]:
        """Collect process metrics"""
        metrics = {}
        
        try:
            # Memory metrics
            memory_info = self.process.memory_info()
            metrics['memory_rss_bytes'] = memory_info.rss
            metrics['memory_vms_bytes'] = memory_info.vms
            
            # CPU metrics
            metrics['cpu_percent'] = self.process.cpu_percent()
            
            # Thread count
            metrics['threads'] = self.process.num_threads()
            
            # File descriptors (Unix-like systems)
            if hasattr(self.process, 'num_fds'):
                metrics['file_descriptors'] = self.process.num_fds()
            
            # Process uptime
            metrics['uptime_seconds'] = time.time() - self.start_time
            
            # I/O counters (if available)
            if hasattr(self.process, 'io_counters'):
                io_counters = self.process.io_counters()
                metrics['io_read_bytes'] = io_counters.read_bytes
                metrics['io_write_bytes'] = io_counters.write_bytes
                metrics['io_read_count'] = io_counters.read_count
                metrics['io_write_count'] = io_counters.write_count
            
        except psutil.NoSuchProcess:
            # Process no longer exists
            pass
        except Exception as e:
            print(f"Error collecting process metrics: {e}")
        
        return metrics
    
    def get_metric_definitions(self) -> List[MetricDefinition]:
        """Get process metric definitions"""
        return [
            MetricDefinition("memory_rss_bytes", "Resident Set Size memory", "bytes", {"collector": "process"}),
            MetricDefinition("cpu_percent", "Process CPU utilization", "%", {"collector": "process"}),
            MetricDefinition("threads", "Number of threads", "count", {"collector": "process"}),
            MetricDefinition("uptime_seconds", "Process uptime", "seconds", {"collector": "process"}),
        ]


class CustomMetricsCollector(MetricsCollector):
    """Collector for custom application metrics"""
    
    def __init__(self, interval: float = 30.0):
        super().__init__("custom", interval)
        self.custom_metrics: Dict[str, float] = {}
        self.metric_callbacks: Dict[str, Callable[[], float]] = {}
    
    def add_metric(self, name: str, value: float):
        """Add a custom metric value"""
        self.custom_metrics[name] = value
    
    def add_metric_callback(self, name: str, callback: Callable[[], float]):
        """Add a callback function to generate metric values"""
        self.metric_callbacks[name] = callback
    
    async def collect(self) -> Dict[str, float]:
        """Collect custom metrics"""
        metrics = dict(self.custom_metrics)
        
        # Execute callbacks
        for name, callback in self.metric_callbacks.items():
            try:
                value = callback()
                metrics[name] = value
            except Exception as e:
                print(f"Error executing callback for metric {name}: {e}")
        
        return metrics
