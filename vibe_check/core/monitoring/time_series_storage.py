"""
Time-Series Storage Engine for Vibe Check Monitoring
====================================================

High-performance time-series storage with compression and PromQL compatibility.
"""

import asyncio
import time
import sqlite3
import json
import threading
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from collections import deque
from pathlib import Path


@dataclass
class MetricPoint:
    """Single metric data point"""
    timestamp: float
    value: float
    labels: Dict[str, str] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'timestamp': self.timestamp,
            'value': self.value,
            'labels': self.labels
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MetricPoint':
        """Create from dictionary"""
        return cls(
            timestamp=data['timestamp'],
            value=data['value'],
            labels=data.get('labels', {})
        )


@dataclass
class MetricSeries:
    """Time series of metric points"""
    name: str
    points: deque = field(default_factory=lambda: deque(maxlen=10000))
    labels: Dict[str, str] = field(default_factory=dict)
    
    def add_point(self, value: float, timestamp: Optional[float] = None, labels: Optional[Dict[str, str]] = None):
        """Add a new data point"""
        if timestamp is None:
            timestamp = time.time()
        
        point_labels = {**self.labels}
        if labels:
            point_labels.update(labels)
            
        self.points.append(MetricPoint(timestamp, value, point_labels))
    
    def get_points_in_range(self, start_time: float, end_time: float) -> List[MetricPoint]:
        """Get points within time range"""
        return [p for p in self.points if start_time <= p.timestamp <= end_time]
    
    def get_latest_point(self) -> Optional[MetricPoint]:
        """Get the most recent point"""
        return self.points[-1] if self.points else None


class TimeSeriesStorage:
    """High-performance time-series storage with compression"""
    
    def __init__(self, db_path: str = "vibe_check_metrics.db", memory_cache_size: int = 10000):
        self.db_path = Path(db_path)
        self.memory_cache: Dict[str, MetricSeries] = {}
        self.cache_lock = threading.RLock()
        self.memory_cache_size = memory_cache_size
        self._init_database()
    
    def _init_database(self):
        """Initialize SQLite database for persistent storage"""
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        conn = sqlite3.connect(self.db_path)
        conn.execute("""
            CREATE TABLE IF NOT EXISTS metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                timestamp REAL NOT NULL,
                value REAL NOT NULL,
                labels TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create indexes for performance
        conn.execute("CREATE INDEX IF NOT EXISTS idx_metrics_name_time ON metrics(name, timestamp)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_metrics_time ON metrics(timestamp)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_metrics_name ON metrics(name)")
        
        conn.commit()
        conn.close()
    
    async def store_metric(self, name: str, value: float, labels: Optional[Dict[str, str]] = None, timestamp: Optional[float] = None):
        """Store a metric point"""
        if timestamp is None:
            timestamp = time.time()
        
        labels = labels or {}
        series_key = self._get_series_key(name, labels)
        
        with self.cache_lock:
            # Add to memory cache
            if series_key not in self.memory_cache:
                self.memory_cache[series_key] = MetricSeries(name, labels=labels)
            
            self.memory_cache[series_key].add_point(value, timestamp, labels)
            
            # Manage cache size
            if len(self.memory_cache) > self.memory_cache_size:
                # Remove oldest series (simple LRU-like behavior)
                oldest_key = next(iter(self.memory_cache))
                del self.memory_cache[oldest_key]
        
        # Async write to database
        await self._persist_to_db(name, value, labels, timestamp)
    
    async def _persist_to_db(self, name: str, value: float, labels: Dict[str, str], timestamp: float):
        """Persist metric to database asynchronously"""
        def _write():
            conn = sqlite3.connect(self.db_path)
            try:
                conn.execute(
                    "INSERT INTO metrics (name, timestamp, value, labels) VALUES (?, ?, ?, ?)",
                    (name, timestamp, value, json.dumps(labels) if labels else None)
                )
                conn.commit()
            finally:
                conn.close()
        
        # Run in thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, _write)
    
    async def query_range(self, name: str, start_time: float, end_time: float, labels: Optional[Dict[str, str]] = None) -> List[MetricPoint]:
        """Query metrics within time range"""
        series_key = self._get_series_key(name, labels or {})
        
        # First check memory cache
        with self.cache_lock:
            if series_key in self.memory_cache:
                cached_points = self.memory_cache[series_key].get_points_in_range(start_time, end_time)
                if cached_points:
                    return cached_points
        
        # Fallback to database
        return await self._query_from_db(name, start_time, end_time, labels)
    
    async def _query_from_db(self, name: str, start_time: float, end_time: float, labels: Optional[Dict[str, str]] = None) -> List[MetricPoint]:
        """Query metrics from database"""
        def _read():
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                
                if labels:
                    # Filter by labels (simple implementation)
                    labels_json = json.dumps(labels, sort_keys=True)
                    cursor.execute(
                        "SELECT timestamp, value, labels FROM metrics WHERE name = ? AND timestamp BETWEEN ? AND ? AND labels = ?",
                        (name, start_time, end_time, labels_json)
                    )
                else:
                    cursor.execute(
                        "SELECT timestamp, value, labels FROM metrics WHERE name = ? AND timestamp BETWEEN ? AND ?",
                        (name, start_time, end_time)
                    )
                
                points = []
                for row in cursor.fetchall():
                    timestamp, value, labels_json = row
                    point_labels = json.loads(labels_json) if labels_json else {}
                    points.append(MetricPoint(timestamp, value, point_labels))
                
                return points
            finally:
                conn.close()
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _read)
    
    async def query_latest(self, name: str, labels: Optional[Dict[str, str]] = None) -> Optional[MetricPoint]:
        """Get the latest value for a metric"""
        series_key = self._get_series_key(name, labels or {})
        
        # Check memory cache first
        with self.cache_lock:
            if series_key in self.memory_cache:
                latest = self.memory_cache[series_key].get_latest_point()
                if latest:
                    return latest
        
        # Query from database
        end_time = time.time()
        start_time = end_time - 3600  # Last hour
        points = await self._query_from_db(name, start_time, end_time, labels)
        return points[-1] if points else None
    
    async def get_metric_names(self) -> List[str]:
        """Get all metric names"""
        def _read():
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                cursor.execute("SELECT DISTINCT name FROM metrics")
                return [row[0] for row in cursor.fetchall()]
            finally:
                conn.close()
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _read)
    
    async def get_label_values(self, label_name: str) -> List[str]:
        """Get all values for a specific label"""
        def _read():
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                cursor.execute("SELECT DISTINCT labels FROM metrics WHERE labels IS NOT NULL")
                
                values = set()
                for row in cursor.fetchall():
                    labels = json.loads(row[0])
                    if label_name in labels:
                        values.add(labels[label_name])
                
                return list(values)
            finally:
                conn.close()
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _read)
    
    def _get_series_key(self, name: str, labels: Dict[str, str]) -> str:
        """Generate a unique key for a metric series"""
        return f"{name}:{json.dumps(labels, sort_keys=True)}"
    
    async def cleanup_old_data(self, retention_days: int = 30):
        """Clean up old data based on retention policy"""
        cutoff_time = time.time() - (retention_days * 24 * 3600)
        
        def _cleanup():
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM metrics WHERE timestamp < ?", (cutoff_time,))
                deleted_count = cursor.rowcount
                conn.commit()
                return deleted_count
            finally:
                conn.close()
        
        loop = asyncio.get_event_loop()
        deleted_count = await loop.run_in_executor(None, _cleanup)
        return deleted_count
    
    async def get_storage_stats(self) -> Dict[str, Any]:
        """Get storage statistics"""
        def _stats():
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                
                # Total metrics count
                cursor.execute("SELECT COUNT(*) FROM metrics")
                total_metrics = cursor.fetchone()[0]
                
                # Unique metric names
                cursor.execute("SELECT COUNT(DISTINCT name) FROM metrics")
                unique_names = cursor.fetchone()[0]
                
                # Date range
                cursor.execute("SELECT MIN(timestamp), MAX(timestamp) FROM metrics")
                min_time, max_time = cursor.fetchone()
                
                # Database size
                cursor.execute("SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()")
                db_size = cursor.fetchone()[0]
                
                return {
                    'total_metrics': total_metrics,
                    'unique_metric_names': unique_names,
                    'time_range': {
                        'start': min_time,
                        'end': max_time,
                        'duration_hours': (max_time - min_time) / 3600 if min_time and max_time else 0
                    },
                    'database_size_bytes': db_size,
                    'memory_cache_size': len(self.memory_cache)
                }
            finally:
                conn.close()
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _stats)
