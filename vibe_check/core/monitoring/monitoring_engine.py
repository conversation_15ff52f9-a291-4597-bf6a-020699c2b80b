"""
Vibe Check Monitoring Engine
===========================

Main monitoring engine that coordinates metrics collection, storage, and querying.
This is the core component that replaces Prometheus functionality.
"""

import asyncio
import time
from typing import Dict, List, Optional, Any

from .metrics_collector import MetricsCollector, SystemMetricsCollector, CodeQualityCollector, ProcessMetricsCollector
from .query_engine import PromQLEngine, QueryResult


class VibeCheckMonitoringEngine:
    """Main monitoring engine - Prometheus replacement"""
    
    def __init__(self, storage_path: str = "vibe_check_metrics.db", config: Optional[Dict[str, Any]] = None):
        self.storage = TimeSeriesStorage(storage_path)
        self.collectors: List[MetricsCollector] = []
        self.query_engine = PromQLEngine(self.storage)
        self.running = False
        self.config = config or {}
        
        # Performance tracking
        self.start_time = time.time()
        self.query_count = 0
        self.error_count = 0
        
        # Default configuration
        self.default_config = {
            'retention_days': 30,
            'cleanup_interval_hours': 24,
            'enable_system_metrics': True,
            'enable_process_metrics': True,
            'system_metrics_interval': 5.0,
            'process_metrics_interval': 10.0,
        }
        
        # Merge with provided config
        self.config = {**self.default_config, **self.config}
    
    def add_collector(self, collector: MetricsCollector):
        """Add a metrics collector"""
        self.collectors.append(collector)
        print(f"Added collector: {collector.name}")
    
    def remove_collector(self, collector_name: str) -> bool:
        """Remove a collector by name"""
        for i, collector in enumerate(self.collectors):
            if collector.name == collector_name:
                if collector.running:
                    asyncio.create_task(collector.stop())
                del self.collectors[i]
                print(f"Removed collector: {collector_name}")
                return True
        return False
    
    def get_collector(self, name: str) -> Optional[MetricsCollector]:
        """Get a collector by name"""
        for collector in self.collectors:
            if collector.name == name:
                return collector
        return None
    
    async def start(self):
        """Start the monitoring engine"""
        if self.running:
            return
        
        self.running = True
        
        # Add default collectors if enabled
        if self.config.get('enable_system_metrics', True):
            system_collector = SystemMetricsCollector(
                interval=self.config.get('system_metrics_interval', 5.0)
            )
            self.add_collector(system_collector)
        
        if self.config.get('enable_process_metrics', True):
            process_collector = ProcessMetricsCollector(
                interval=self.config.get('process_metrics_interval', 10.0)
            )
            self.add_collector(process_collector)
        
        # Start all collectors
        for collector in self.collectors:
            await collector.start(self.storage)
        
        # Start background tasks
        asyncio.create_task(self._cleanup_task())
        asyncio.create_task(self._health_check_task())
        
        print(f"Vibe Check Monitoring Engine started with {len(self.collectors)} collectors")
    
    async def stop(self):
        """Stop the monitoring engine"""
        if not self.running:
            return
        
        self.running = False
        
        # Stop all collectors
        for collector in self.collectors:
            await collector.stop()
        
        print("Vibe Check Monitoring Engine stopped")
    
    async def _cleanup_task(self):
        """Background task for data cleanup"""
        cleanup_interval = self.config.get('cleanup_interval_hours', 24) * 3600
        retention_days = self.config.get('retention_days', 30)
        
        while self.running:
            try:
                await asyncio.sleep(cleanup_interval)
                if self.running:
                    deleted_count = await self.storage.cleanup_old_data(retention_days)
                    print(f"Cleaned up {deleted_count} old metric records")
            except Exception as e:
                print(f"Error in cleanup task: {e}")
                self.error_count += 1
    
    async def _health_check_task(self):
        """Background task for health monitoring"""
        while self.running:
            try:
                await asyncio.sleep(60)  # Check every minute
                
                if self.running:
                    # Check collector health
                    for collector in self.collectors:
                        if collector.enabled and not collector.running:
                            print(f"Restarting failed collector: {collector.name}")
                            await collector.start(self.storage)
                    
                    # Record engine health metrics
                    await self.record_metric("engine_uptime_seconds", time.time() - self.start_time)
                    await self.record_metric("engine_query_count_total", self.query_count)
                    await self.record_metric("engine_error_count_total", self.error_count)
                    await self.record_metric("engine_collectors_active", len([c for c in self.collectors if c.running]))
                    
            except Exception as e:
                print(f"Error in health check task: {e}")
                self.error_count += 1
    
    async def query(self, query: str, start_time: Optional[float] = None, end_time: Optional[float] = None) -> QueryResult:
        """Query metrics using PromQL-compatible syntax"""
        try:
            self.query_count += 1
            
            if end_time is None:
                end_time = time.time()
            if start_time is None:
                start_time = end_time - 3600  # Default to last hour
            
            result = await self.query_engine.execute_query(query, start_time, end_time)
            return result
            
        except Exception as e:
            self.error_count += 1
            print(f"Error executing query '{query}': {e}")
            raise
    
    async def instant_query(self, query: str) -> QueryResult:
        """Execute an instant query (latest values)"""
        try:
            self.query_count += 1
            result = await self.query_engine.execute_instant_query(query)
            return result
            
        except Exception as e:
            self.error_count += 1
            print(f"Error executing instant query '{query}': {e}")
            raise
    
    async def record_metric(self, name: str, value: float, labels: Optional[Dict[str, str]] = None):
        """Record a custom metric"""
        await self.storage.store_metric(name, value, labels)
    
    async def get_metric_names(self) -> List[str]:
        """Get all available metric names"""
        return await self.storage.get_metric_names()
    
    async def get_label_values(self, label_name: str) -> List[str]:
        """Get all values for a specific label"""
        return await self.storage.get_label_values(label_name)
    
    def get_collector_status(self) -> List[Dict[str, Any]]:
        """Get status of all collectors"""
        return [collector.get_status() for collector in self.collectors]
    
    async def get_engine_stats(self) -> Dict[str, Any]:
        """Get engine statistics"""
        storage_stats = await self.storage.get_storage_stats()
        
        return {
            'engine': {
                'running': self.running,
                'uptime_seconds': time.time() - self.start_time,
                'query_count': self.query_count,
                'error_count': self.error_count,
                'collectors_count': len(self.collectors),
                'collectors_active': len([c for c in self.collectors if c.running]),
            },
            'storage': storage_stats,
            'collectors': self.get_collector_status(),
            'config': self.config
        }
    
    async def add_code_quality_monitoring(self, project_path: str, interval: float = 300.0):
        """Add code quality monitoring for a project"""
        collector = CodeQualityCollector(project_path, interval)
        self.add_collector(collector)
        
        if self.running:
            await collector.start(self.storage)
        
        return collector
    
    async def export_metrics(self, format: str = "prometheus") -> str:
        """Export metrics in various formats"""
        if format == "prometheus":
            return await self._export_prometheus_format()
        elif format == "json":
            return await self._export_json_format()
        else:
            raise ValueError(f"Unsupported export format: {format}")
    
    async def _export_prometheus_format(self) -> str:
        """Export metrics in Prometheus text format"""
        lines = []
        metric_names = await self.get_metric_names()
        
        for metric_name in metric_names:
            # Get latest value
            latest_point = await self.storage.query_latest(metric_name)
            if latest_point:
                # Format as Prometheus metric
                labels_str = ""
                if latest_point.labels:
                    label_pairs = [f'{k}="{v}"' for k, v in latest_point.labels.items()]
                    labels_str = "{" + ",".join(label_pairs) + "}"
                
                lines.append(f"{metric_name}{labels_str} {latest_point.value} {int(latest_point.timestamp * 1000)}")
        
        return "\n".join(lines)
    
    async def _export_json_format(self) -> str:
        """Export metrics in JSON format"""
        import json
        
        metrics = {}
        metric_names = await self.get_metric_names()
        
        for metric_name in metric_names:
            latest_point = await self.storage.query_latest(metric_name)
            if latest_point:
                metrics[metric_name] = latest_point.to_dict()
        
        return json.dumps(metrics, indent=2)


# Convenience function for quick setup
async def create_monitoring_engine(project_path: Optional[str] = None, **config) -> VibeCheckMonitoringEngine:
    """Create and start a monitoring engine with default configuration"""
    engine = VibeCheckMonitoringEngine(config=config)
    
    # Add code quality monitoring if project path provided
    if project_path:
        await engine.add_code_quality_monitoring(project_path)
    
    await engine.start()
    return engine
