"""
PromQL-Compatible Query Engine
==============================

Query engine that supports a subset of PromQL for querying time-series data.
"""

import re
import time
from dataclasses import dataclass
from enum import Enum

from .time_series_storage import TimeSeriesStorage, MetricPoint


class QueryType(Enum):
    """Types of queries supported"""
    INSTANT = "instant"
    RANGE = "range"


@dataclass
class QueryResult:
    """Result of a query execution"""
    query_type: QueryType
    metric_name: str
    data: List[MetricPoint]
    labels: Dict[str, str]
    execution_time: float
    error: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'query_type': self.query_type.value,
            'metric_name': self.metric_name,
            'data': [point.to_dict() for point in self.data],
            'labels': self.labels,
            'execution_time': self.execution_time,
            'error': self.error
        }


class PromQLParser:
    """Simple PromQL parser for basic queries"""
    
    def __init__(self):
        # Basic patterns for PromQL parsing
        self.metric_pattern = re.compile(r'^([a-zA-Z_][a-zA-Z0-9_]*)')
        self.label_pattern = re.compile(r'\{([^}]+)\}')
        self.function_pattern = re.compile(r'^([a-zA-Z_]+)\((.+)\)')
        
    def parse_query(self, query: str) -> Dict[str, Any]:
        """Parse a PromQL query into components"""
        query = query.strip()
        
        # Check for functions
        function_match = self.function_pattern.match(query)
        if function_match:
            function_name = function_match.group(1)
            function_args = function_match.group(2)
            return {
                'type': 'function',
                'function': function_name,
                'args': function_args,
                'metric': self._extract_metric_from_args(function_args)
            }
        
        # Simple metric query
        metric_match = self.metric_pattern.match(query)
        if metric_match:
            metric_name = metric_match.group(1)
            
            # Extract labels if present
            labels = {}
            label_match = self.label_pattern.search(query)
            if label_match:
                labels = self._parse_labels(label_match.group(1))
            
            return {
                'type': 'metric',
                'metric': metric_name,
                'labels': labels
            }
        
        raise ValueError(f"Unable to parse query: {query}")
    
    def _extract_metric_from_args(self, args: str) -> str:
        """Extract metric name from function arguments"""
        # Simple extraction - just get the first word
        parts = args.split(',')
        if parts:
            metric_match = self.metric_pattern.match(parts[0].strip())
            if metric_match:
                return metric_match.group(1)
        return args.strip()
    
    def _parse_labels(self, label_string: str) -> Dict[str, str]:
        """Parse label selectors"""
        labels = {}
        
        # Split by comma and parse each label
        for label_expr in label_string.split(','):
            label_expr = label_expr.strip()
            
            # Handle different operators (=, !=, =~, !~)
            if '=' in label_expr:
                if '!=' in label_expr:
                    # Not equal - skip for now
                    continue
                elif '=~' in label_expr:
                    # Regex match - skip for now
                    continue
                elif '!~' in label_expr:
                    # Regex not match - skip for now
                    continue
                else:
                    # Simple equality
                    key, value = label_expr.split('=', 1)
                    key = key.strip()
                    value = value.strip().strip('"\'')
                    labels[key] = value
        
        return labels


class PromQLEngine:
    """PromQL-compatible query engine"""
    
    def __init__(self, storage: TimeSeriesStorage):
        self.storage = storage
        self.parser = PromQLParser()
        
        # Supported functions
        self.functions = {
            'rate': self._function_rate,
            'avg': self._function_avg,
            'sum': self._function_sum,
            'max': self._function_max,
            'min': self._function_min,
            'count': self._function_count,
        }
    
    async def execute_query(self, query: str, start_time: float, end_time: float) -> QueryResult:
        """Execute a range query"""
        start_exec_time = time.time()
        
        try:
            parsed = self.parser.parse_query(query)
            
            if parsed['type'] == 'metric':
                # Simple metric query
                data = await self.storage.query_range(
                    parsed['metric'], 
                    start_time, 
                    end_time, 
                    parsed.get('labels')
                )
                
                return QueryResult(
                    query_type=QueryType.RANGE,
                    metric_name=parsed['metric'],
                    data=data,
                    labels=parsed.get('labels', {}),
                    execution_time=time.time() - start_exec_time
                )
            
            elif parsed['type'] == 'function':
                # Function query
                function_name = parsed['function']
                if function_name in self.functions:
                    data = await self.functions[function_name](
                        parsed['metric'], 
                        start_time, 
                        end_time,
                        parsed.get('labels', {})
                    )
                    
                    return QueryResult(
                        query_type=QueryType.RANGE,
                        metric_name=f"{function_name}({parsed['metric']})",
                        data=data,
                        labels=parsed.get('labels', {}),
                        execution_time=time.time() - start_exec_time
                    )
                else:
                    raise ValueError(f"Unsupported function: {function_name}")
            
            else:
                raise ValueError(f"Unsupported query type: {parsed['type']}")
                
        except Exception as e:
            return QueryResult(
                query_type=QueryType.RANGE,
                metric_name=query,
                data=[],
                labels={},
                execution_time=time.time() - start_exec_time,
                error=str(e)
            )
    
    async def execute_instant_query(self, query: str) -> QueryResult:
        """Execute an instant query (latest value)"""
        start_exec_time = time.time()
        
        try:
            parsed = self.parser.parse_query(query)
            
            if parsed['type'] == 'metric':
                # Get latest value
                latest_point = await self.storage.query_latest(
                    parsed['metric'],
                    parsed.get('labels')
                )
                
                data = [latest_point] if latest_point else []
                
                return QueryResult(
                    query_type=QueryType.INSTANT,
                    metric_name=parsed['metric'],
                    data=data,
                    labels=parsed.get('labels', {}),
                    execution_time=time.time() - start_exec_time
                )
            
            else:
                # For functions, get recent data and apply function
                end_time = time.time()
                start_time = end_time - 300  # Last 5 minutes
                
                return await self.execute_query(query, start_time, end_time)
                
        except Exception as e:
            return QueryResult(
                query_type=QueryType.INSTANT,
                metric_name=query,
                data=[],
                labels={},
                execution_time=time.time() - start_exec_time,
                error=str(e)
            )
    
    async def _function_rate(self, metric: str, start_time: float, end_time: float, labels: Dict[str, str]) -> List[MetricPoint]:
        """Calculate rate of change"""
        data = await self.storage.query_range(metric, start_time, end_time, labels)
        
        if len(data) < 2:
            return []
        
        # Calculate rate between consecutive points
        rate_points = []
        for i in range(1, len(data)):
            prev_point = data[i-1]
            curr_point = data[i]
            
            time_diff = curr_point.timestamp - prev_point.timestamp
            value_diff = curr_point.value - prev_point.value
            
            if time_diff > 0:
                rate = value_diff / time_diff
                rate_points.append(MetricPoint(curr_point.timestamp, rate, curr_point.labels))
        
        return rate_points
    
    async def _function_avg(self, metric: str, start_time: float, end_time: float, labels: Dict[str, str]) -> List[MetricPoint]:
        """Calculate average value"""
        data = await self.storage.query_range(metric, start_time, end_time, labels)
        
        if not data:
            return []
        
        avg_value = sum(point.value for point in data) / len(data)
        avg_timestamp = (start_time + end_time) / 2
        
        return [MetricPoint(avg_timestamp, avg_value, labels)]
    
    async def _function_sum(self, metric: str, start_time: float, end_time: float, labels: Dict[str, str]) -> List[MetricPoint]:
        """Calculate sum of values"""
        data = await self.storage.query_range(metric, start_time, end_time, labels)
        
        if not data:
            return []
        
        sum_value = sum(point.value for point in data)
        sum_timestamp = (start_time + end_time) / 2
        
        return [MetricPoint(sum_timestamp, sum_value, labels)]
    
    async def _function_max(self, metric: str, start_time: float, end_time: float, labels: Dict[str, str]) -> List[MetricPoint]:
        """Calculate maximum value"""
        data = await self.storage.query_range(metric, start_time, end_time, labels)
        
        if not data:
            return []
        
        max_point = max(data, key=lambda p: p.value)
        return [max_point]
    
    async def _function_min(self, metric: str, start_time: float, end_time: float, labels: Dict[str, str]) -> List[MetricPoint]:
        """Calculate minimum value"""
        data = await self.storage.query_range(metric, start_time, end_time, labels)
        
        if not data:
            return []
        
        min_point = min(data, key=lambda p: p.value)
        return [min_point]
    
    async def _function_count(self, metric: str, start_time: float, end_time: float, labels: Dict[str, str]) -> List[MetricPoint]:
        """Count number of data points"""
        data = await self.storage.query_range(metric, start_time, end_time, labels)
        
        count_value = len(data)
        count_timestamp = (start_time + end_time) / 2
        
        return [MetricPoint(count_timestamp, count_value, labels)]
