"""
File System Utilities
==================

This module provides utility functions for working with the file system.
"""

import fnmatch
import os
import shutil
from pathlib import Path
from typing import Union, List, Optional, Iterator, Dict

from .error_handling import FileError


def get_file_content(file_path: Union[str, Path]) -> str:
    """
    Read the content of a file.

    Args:
        file_path: Path to the file

    Returns:
        Content of the file as a string

    Raises:
        FileNotFoundError: If the file does not exist
        FileError: If the file cannot be read
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        raise
    except Exception as e:
        raise FileError(f"Failed to read file {file_path}: {e}", str(file_path))


def write_file(file_path: Union[str, Path], content: str) -> None:
    """
    Write content to a file.

    Args:
        file_path: Path to the file
        content: Content to write

    Raises:
        FileError: If the file cannot be written
    """
    try:
        # Create directory if it doesn't exist
        directory = os.path.dirname(file_path)
        if directory and not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)

        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
    except Exception as e:
        raise FileError(f"Failed to write file {file_path}: {e}", str(file_path))


def is_excluded(file_path: Union[str, Path], exclude_patterns: List[str]) -> bool:
    """
    Check if a file path matches any of the exclude patterns.

    Args:
        file_path: File path to check
        exclude_patterns: List of glob patterns to exclude

    Returns:
        True if the file path matches any of the exclude patterns, False otherwise
    """
    if not exclude_patterns:
        return False

    path_str = str(file_path)

    for pattern in exclude_patterns:
        # Handle special case for directory patterns
        if "**/" in pattern:
            parts = path_str.split(os.sep)
            for i in range(len(parts)):
                subpath = os.sep.join(parts[i:])
                if fnmatch.fnmatch(subpath, pattern.replace("**/", "")):
                    return True
                subpath = os.sep.join(parts[:i+1])
                if fnmatch.fnmatch(subpath, pattern.replace("/**", "")):
                    return True

        # Direct match
        if fnmatch.fnmatch(path_str, pattern):
            return True

    return False


def find_files(directory: Union[str, Path],
              file_extensions: Optional[List[str]] = None,
              exclude_patterns: Optional[List[str]] = None) -> List[Path]:
    """
    Find files in a directory matching the given criteria.

    Args:
        directory: Directory to search
        file_extensions: List of file extensions to include
        exclude_patterns: List of glob patterns to exclude

    Returns:
        List of file paths

    Raises:
        FileNotFoundError: If the directory does not exist
        FileError: If the directory cannot be searched
    """
    try:
        directory_path = Path(directory)
        if not directory_path.exists():
            raise FileNotFoundError(f"Directory does not exist: {directory}")

        if not directory_path.is_dir():
            raise FileError(f"Not a directory: {directory}", str(directory))

        file_extensions = file_extensions or []
        exclude_patterns = exclude_patterns or []

        result = []

        for root, dirs, files in os.walk(directory_path):
            # Skip excluded directories
            dirs[:] = [d for d in dirs if not is_excluded(os.path.join(root, d), exclude_patterns)]

            for file in files:
                file_path = Path(os.path.join(root, file))

                # Skip excluded files
                if is_excluded(file_path, exclude_patterns):
                    continue

                # Check file extension
                if file_extensions and not any(str(file_path).endswith(ext) for ext in file_extensions):
                    continue

                result.append(file_path)

        return result
    except FileNotFoundError:
        raise
    except Exception as e:
        if isinstance(e, FileError):
            raise
        raise FileError(f"Failed to search directory {directory}: {e}", str(directory))


def get_file_stats(file_path: Union[str, Path]) -> Dict[str, Union[int, float]]:
    """
    Get statistics for a file.

    Args:
        file_path: Path to the file

    Returns:
        Dictionary with file statistics

    Raises:
        FileError: If the file statistics cannot be retrieved
    """
    try:
        path_obj = Path(file_path)
        stats = path_obj.stat()

        return {
            "size": stats.st_size,
            "modified": stats.st_mtime,
            "created": stats.st_ctime,
            "accessed": stats.st_atime
        }
    except Exception as e:
        raise FileError(f"Failed to get file statistics for {file_path}: {e}", str(file_path))


def ensure_directory(directory: Union[str, Path]) -> Path:
    """
    Ensure a directory exists, creating it if necessary.

    Args:
        directory: Directory path

    Returns:
        Path object for the directory

    Raises:
        FileError: If the directory cannot be created
    """
    try:
        path_obj = Path(directory)
        path_obj.mkdir(parents=True, exist_ok=True)
        return path_obj
    except Exception as e:
        raise FileError(f"Failed to create directory {directory}: {e}", str(directory))


# Alias for backward compatibility
ensure_dir = ensure_directory


def copy_file(source: Union[str, Path], destination: Union[str, Path]) -> None:
    """
    Copy a file from source to destination.

    Args:
        source: Source file path
        destination: Destination file path

    Raises:
        FileError: If the file cannot be copied
    """
    try:
        shutil.copy2(source, destination)
    except Exception as e:
        raise FileError(f"Failed to copy file from {source} to {destination}: {e}", str(source))


def get_relative_path(path: Union[str, Path], base_path: Union[str, Path]) -> str:
    """
    Get the relative path from base_path to path.

    Args:
        path: Path to get relative path for
        base_path: Base path

    Returns:
        Relative path as a string
    """
    # Normalize paths to handle trailing slashes
    path_obj = Path(os.path.normpath(str(path)))
    base_path_obj = Path(os.path.normpath(str(base_path)))

    # Get the relative path
    rel_path = path_obj.relative_to(base_path_obj)

    # Convert to string and handle the case where the path is the same
    if str(rel_path) == '.':
        return '.'

    return str(rel_path)


def get_file_extension(file_path: Union[str, Path]) -> str:
    """
    Get the file extension of a file.

    Args:
        file_path: Path to the file

    Returns:
        File extension (including the dot)
    """
    path_str = str(file_path)

    # Special case for dotfiles like .gitignore
    if os.path.basename(path_str).startswith('.') and '.' not in os.path.basename(path_str)[1:]:
        return os.path.basename(path_str)

    return os.path.splitext(path_str)[1]


def count_lines(file_path: Union[str, Path]) -> int:
    """
    Count the number of lines in a file.

    Args:
        file_path: Path to the file

    Returns:
        Number of lines

    Raises:
        FileError: If the file cannot be read
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return sum(1 for _ in f)
    except Exception as e:
        raise FileError(f"Failed to count lines in file {file_path}: {e}", str(file_path))
