"""
Pre-commit Hook Generation System
=================================

Automatically generates .pre-commit-config.yaml files with Vibe Check integration,
supporting configurable validation levels and proper exit code compliance.
"""

import yaml
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, field
from enum import Enum

logger = logging.getLogger(__name__)


class ValidationLevel(Enum):
    """Validation levels for pre-commit hooks."""
    
    MINIMAL = "minimal"      # Essential checks only (fastest)
    STANDARD = "standard"    # Balanced performance and coverage
    STRICT = "strict"        # Comprehensive analysis (slowest)


class HookMode(Enum):
    """Hook generation modes."""
    
    MERGE = "merge"         # Merge with existing configuration
    FRESH = "fresh"         # Create fresh configuration (overwrite)
    APPEND = "append"       # Append to existing configuration


@dataclass
class HookConfig:
    """Configuration for pre-commit hook generation."""
    
    # Basic settings
    validation_level: ValidationLevel = ValidationLevel.STANDARD
    mode: HookMode = HookMode.MERGE
    
    # Performance settings
    timeout_seconds: int = 30
    fail_fast: bool = True
    parallel_processing: bool = True
    
    # Rule configuration
    enabled_categories: Set[str] = field(default_factory=lambda: {
        'style', 'security', 'complexity', 'imports'
    })
    disabled_rules: Set[str] = field(default_factory=set)
    
    # File filtering
    file_patterns: List[str] = field(default_factory=lambda: [r'\.py$'])
    exclude_patterns: List[str] = field(default_factory=lambda: [
        r'^tests/', r'^migrations/', r'__pycache__/', r'\.git/'
    ])
    
    # Integration settings
    external_tools: List[str] = field(default_factory=list)
    vibe_check_version: str = "v0.3.0"
    repository_url: str = "https://github.com/ptzajac/vibe_check"
    
    # Advanced options
    backup_existing: bool = True
    dry_run: bool = False
    verbose: bool = False


class PreCommitHookGenerator:
    """
    Generates pre-commit configuration files with Vibe Check integration.
    
    Supports multiple validation levels, configuration merging, and
    integration with external tools for comprehensive code quality.
    """
    
    def __init__(self, config: Optional[HookConfig] = None):
        """Initialize hook generator with configuration."""
        self.config = config or HookConfig()
        
    def generate_hook_config(self) -> Dict[str, Any]:
        """
        Generate pre-commit hook configuration for Vibe Check.
        
        Returns:
            Dictionary containing hook configuration
        """
        # Base hook configuration
        hook_config = {
            'id': 'vibe-check',
            'name': 'Vibe Check Analysis',
            'entry': 'vibe-check analyze',
            'language': 'python',
            'files': '|'.join(self.config.file_patterns),
            'pass_filenames': True,
            'require_serial': False,
            'stages': ['commit']
        }
        
        # Add arguments based on validation level
        args = self._get_validation_args()
        if args:
            hook_config['args'] = args
        
        # Add exclude patterns
        if self.config.exclude_patterns:
            hook_config['exclude'] = '|'.join(self.config.exclude_patterns)
        
        # Add timeout
        if self.config.timeout_seconds != 30:
            hook_config['timeout'] = self.config.timeout_seconds
        
        return hook_config
    
    def generate_full_config(self, existing_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate complete pre-commit configuration file.
        
        Args:
            existing_config: Existing configuration to merge with
            
        Returns:
            Complete pre-commit configuration
        """
        # Start with existing config or create new
        if existing_config and self.config.mode == HookMode.MERGE:
            full_config = existing_config.copy()
        else:
            full_config = {
                'repos': [],
                'default_stages': ['commit'],
                'fail_fast': self.config.fail_fast
            }
        
        # Ensure repos list exists
        if 'repos' not in full_config:
            full_config['repos'] = []
        
        # Find existing Vibe Check repo or create new
        vibe_check_repo = self._find_vibe_check_repo(full_config['repos'])
        
        if vibe_check_repo:
            # Update existing repo
            self._update_existing_repo(vibe_check_repo)
        else:
            # Add new Vibe Check repo
            new_repo = self._create_vibe_check_repo()
            
            if self.config.mode == HookMode.APPEND:
                full_config['repos'].append(new_repo)
            else:
                # Insert at beginning for priority
                full_config['repos'].insert(0, new_repo)
        
        # Add external tools if specified
        if self.config.external_tools:
            self._add_external_tools(full_config)
        
        return full_config
    
    def save_config(self, config: Dict[str, Any], file_path: Path) -> bool:
        """
        Save pre-commit configuration to file.
        
        Args:
            config: Configuration to save
            file_path: Path to save configuration
            
        Returns:
            True if saved successfully
        """
        try:
            # Backup existing file if requested
            if self.config.backup_existing and file_path.exists():
                backup_path = file_path.with_suffix('.yaml.backup')
                backup_path.write_text(file_path.read_text())
                logger.info(f"Backed up existing configuration to {backup_path}")
            
            # Save configuration
            if self.config.dry_run:
                logger.info("DRY RUN: Would save configuration to {file_path}")
                if self.config.verbose:
                    print(yaml.dump(config, default_flow_style=False, sort_keys=False))
                return True
            
            with open(file_path, 'w') as f:
                yaml.dump(config, f, default_flow_style=False, sort_keys=False)
            
            logger.info(f"Saved pre-commit configuration to {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
            return False
    
    def load_existing_config(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """
        Load existing pre-commit configuration.
        
        Args:
            file_path: Path to configuration file
            
        Returns:
            Existing configuration or None if not found
        """
        try:
            if not file_path.exists():
                return None
            
            with open(file_path, 'r') as f:
                config = yaml.safe_load(f)
            
            if not isinstance(config, dict):
                logger.warning(f"Invalid configuration format in {file_path}")
                return None
            
            return config
            
        except Exception as e:
            logger.error(f"Failed to load existing configuration: {e}")
            return None
    
    def _get_validation_args(self) -> List[str]:
        """Get command line arguments based on validation level."""
        args = ['--vcs-mode', '--fast-mode']
        
        # Add validation level specific arguments
        if self.config.validation_level == ValidationLevel.MINIMAL:
            args.extend(['--critical-rules-only', '--max-issues', '10'])
        elif self.config.validation_level == ValidationLevel.STANDARD:
            args.extend(['--max-execution-time', '30'])
        elif self.config.validation_level == ValidationLevel.STRICT:
            args.extend(['--max-execution-time', '60', '--detailed'])
        
        # Add category filters
        if self.config.enabled_categories:
            categories = ','.join(self.config.enabled_categories)
            args.extend(['--categories', categories])
        
        # Add disabled rules
        if self.config.disabled_rules:
            disabled = ','.join(self.config.disabled_rules)
            args.extend(['--exclude-rules', disabled])
        
        # Add external tools
        if self.config.external_tools:
            tools = ','.join(self.config.external_tools)
            args.extend(['--tools', tools])
        
        # Performance options
        if not self.config.parallel_processing:
            args.append('--no-parallel')
        
        # Always add no-save for pre-commit (don't save to database)
        args.append('--no-save')
        
        # Add quiet mode for cleaner output
        args.append('--quiet')
        
        return args
    
    def _find_vibe_check_repo(self, repos: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Find existing Vibe Check repository in configuration."""
        for repo in repos:
            if isinstance(repo, dict) and 'repo' in repo:
                repo_url = repo['repo']
                if 'vibe_check' in repo_url or 'vibe-check' in repo_url:
                    return repo
        return None
    
    def _update_existing_repo(self, repo: Dict[str, Any]) -> None:
        """Update existing Vibe Check repository configuration."""
        # Update version
        repo['rev'] = self.config.vibe_check_version
        
        # Update hooks
        if 'hooks' not in repo:
            repo['hooks'] = []
        
        # Find existing Vibe Check hook or add new
        vibe_check_hook = None
        for hook in repo['hooks']:
            if isinstance(hook, dict) and hook.get('id') == 'vibe-check':
                vibe_check_hook = hook
                break
        
        if vibe_check_hook:
            # Update existing hook
            new_hook_config = self.generate_hook_config()
            vibe_check_hook.update(new_hook_config)
        else:
            # Add new hook
            repo['hooks'].append(self.generate_hook_config())
    
    def _create_vibe_check_repo(self) -> Dict[str, Any]:
        """Create new Vibe Check repository configuration."""
        return {
            'repo': self.config.repository_url,
            'rev': self.config.vibe_check_version,
            'hooks': [self.generate_hook_config()]
        }
    
    def _add_external_tools(self, config: Dict[str, Any]) -> None:
        """Add external tool repositories to configuration."""
        external_repos = []
        
        # Add common external tools
        tool_configs = {
            'ruff': {
                'repo': 'https://github.com/astral-sh/ruff-pre-commit',
                'rev': 'v0.1.6',
                'hooks': [{'id': 'ruff', 'args': ['--fix']}]
            },
            'mypy': {
                'repo': 'https://github.com/pre-commit/mirrors-mypy',
                'rev': 'v1.7.1',
                'hooks': [{'id': 'mypy'}]
            },
            'black': {
                'repo': 'https://github.com/psf/black',
                'rev': '23.11.0',
                'hooks': [{'id': 'black'}]
            },
            'isort': {
                'repo': 'https://github.com/pycqa/isort',
                'rev': '5.12.0',
                'hooks': [{'id': 'isort'}]
            }
        }
        
        for tool in self.config.external_tools:
            if tool in tool_configs:
                external_repos.append(tool_configs[tool])
        
        # Add external repos to configuration
        config['repos'].extend(external_repos)
    
    def estimate_execution_time(self) -> float:
        """
        Estimate hook execution time based on configuration.
        
        Returns:
            Estimated execution time in seconds
        """
        base_time = 5.0  # Base overhead
        
        # Add time based on validation level
        if self.config.validation_level == ValidationLevel.MINIMAL:
            analysis_time = 10.0
        elif self.config.validation_level == ValidationLevel.STANDARD:
            analysis_time = 20.0
        else:  # STRICT
            analysis_time = 40.0
        
        # Add time for external tools
        tool_time = len(self.config.external_tools) * 5.0
        
        # Adjust for parallel processing
        if self.config.parallel_processing:
            total_time = base_time + max(analysis_time, tool_time)
        else:
            total_time = base_time + analysis_time + tool_time
        
        return min(total_time, self.config.timeout_seconds)
    
    def validate_config(self) -> List[str]:
        """
        Validate hook configuration and return any issues.
        
        Returns:
            List of validation issues (empty if valid)
        """
        issues = []
        
        # Check timeout vs validation level
        estimated_time = self.estimate_execution_time()
        if estimated_time > self.config.timeout_seconds:
            issues.append(f"Estimated execution time ({estimated_time:.1f}s) exceeds timeout ({self.config.timeout_seconds}s)")
        
        # Check for conflicting settings
        if self.config.validation_level == ValidationLevel.MINIMAL and self.config.external_tools:
            issues.append("External tools may slow down minimal validation level")
        
        # Check file patterns
        if not self.config.file_patterns:
            issues.append("No file patterns specified - hook may not run on any files")
        
        # Check category conflicts
        if not self.config.enabled_categories:
            issues.append("No rule categories enabled - analysis may be empty")
        
        return issues


def create_hook_generator(
    validation_level: str = "standard",
    mode: str = "merge",
    external_tools: Optional[List[str]] = None,
    **kwargs
) -> PreCommitHookGenerator:
    """
    Create pre-commit hook generator with specified configuration.
    
    Args:
        validation_level: Validation level (minimal/standard/strict)
        mode: Generation mode (merge/fresh/append)
        external_tools: List of external tools to include
        **kwargs: Additional configuration options
        
    Returns:
        Configured hook generator
    """
    config = HookConfig(
        validation_level=ValidationLevel(validation_level),
        mode=HookMode(mode),
        external_tools=external_tools or [],
        **kwargs
    )
    
    return PreCommitHookGenerator(config)
