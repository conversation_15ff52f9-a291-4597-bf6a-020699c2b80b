"""
Common Utilities Module
======================

Consolidated imports and utilities used throughout Vibe Check.
"""

# Standard library imports
import os
import sys
import time
import json
import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from collections import defaultdict

# Third-party imports
import click
import yaml
import psutil

# Common type aliases
ConfigDict = Dict[str, Any]
MetricDict = Dict[str, float]
LabelDict = Dict[str, str]

# Common utility functions
def get_timestamp() -> float:
    """Get current timestamp"""
    return time.time()

def format_bytes(bytes_value: int) -> str:
    """Format bytes in human readable format"""
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if bytes_value < 1024.0:
            return f"{bytes_value:.1f}{unit}"
        bytes_value /= 1024.0
    return f"{bytes_value:.1f}PB"

def safe_json_loads(json_str: str, default: Any = None) -> Any:
    """Safely load JSON with default fallback"""
    try:
        return json.loads(json_str)
    except (json.JSONDecodeError, TypeError):
        return default

def ensure_directory(path: Path) -> Path:
    """Ensure directory exists"""
    path.mkdir(parents=True, exist_ok=True)
    return path
