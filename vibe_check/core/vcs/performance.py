"""
VCS Performance Monitoring
===========================

This module provides performance monitoring and benchmarking
capabilities for the VCS engine.
"""

import time
import psutil
import threading
from collections import defaultdict, deque
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Deque
import logging

from .models import AnalysisTarget

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetric:
    """Individual performance metric."""
    
    name: str
    value: float
    unit: str
    timestamp: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AnalysisPerformance:
    """Performance data for a single analysis."""
    
    target_path: str
    file_size: int
    execution_time: float
    memory_usage: float
    rules_executed: int
    cache_hit: bool = False
    timestamp: float = field(default_factory=time.time)


class PerformanceMonitor:
    """
    Performance monitoring system for VCS engine.
    
    Tracks execution times, memory usage, cache performance,
    and provides benchmarking capabilities.
    """
    
    def __init__(self, history_size: int = 1000):
        """
        Initialize performance monitor.
        
        Args:
            history_size: Maximum number of performance records to keep
        """
        self.history_size = history_size
        self._initialized = False
        self._monitoring_active = False
        
        # Performance data storage
        self.analysis_history: Deque[AnalysisPerformance] = deque(maxlen=history_size)
        self.metrics: Dict[str, Deque[PerformanceMetric]] = defaultdict(lambda: deque(maxlen=100))
        
        # Aggregated statistics
        self.total_analyses = 0
        self.total_execution_time = 0.0
        self.total_files_processed = 0
        self.total_bytes_processed = 0
        
        # Cache statistics
        self.cache_hits = 0
        self.cache_misses = 0
        
        # System monitoring
        self._system_monitor_thread: Optional[threading.Thread] = None
        self._stop_monitoring = threading.Event()
        
        # Benchmarks
        self.benchmarks: Dict[str, Dict[str, float]] = {
            "small_files": {"target_time": 0.1, "files": "<100 lines"},
            "medium_files": {"target_time": 1.0, "files": "100-1000 lines"},
            "large_files": {"target_time": 5.0, "files": ">1000 lines"}
        }
    
    def initialize(self) -> None:
        """Initialize the performance monitor."""
        if self._initialized:
            return
        
        # Start system monitoring
        self._start_system_monitoring()
        
        self._initialized = True
        self._monitoring_active = True
        logger.info("Performance monitor initialized")
    
    def shutdown(self) -> None:
        """Shutdown the performance monitor."""
        if not self._initialized:
            return
        
        self._monitoring_active = False
        
        # Stop system monitoring
        self._stop_system_monitoring()
        
        logger.info("Performance monitor shutdown")
    
    def track_analysis(self, target: AnalysisTarget, execution_time: float, 
                      rules_executed: int = 0, cache_hit: bool = False) -> None:
        """
        Track performance of an analysis operation.
        
        Args:
            target: Analysis target
            execution_time: Time taken for analysis
            rules_executed: Number of rules executed
            cache_hit: Whether result came from cache
        """
        if not self._monitoring_active:
            return
        
        try:
            # Get file size
            file_size = 0
            if target.path.exists():
                file_size = target.path.stat().st_size
            
            # Get current memory usage
            process = psutil.Process()
            memory_usage = process.memory_info().rss / 1024 / 1024  # MB
            
            # Create performance record
            performance = AnalysisPerformance(
                target_path=str(target.path),
                file_size=file_size,
                execution_time=execution_time,
                memory_usage=memory_usage,
                rules_executed=rules_executed,
                cache_hit=cache_hit
            )
            
            # Store in history
            self.analysis_history.append(performance)
            
            # Update aggregated statistics
            self.total_analyses += 1
            self.total_execution_time += execution_time
            self.total_files_processed += 1
            self.total_bytes_processed += file_size
            
            if cache_hit:
                self.cache_hits += 1
            else:
                self.cache_misses += 1
            
            # Add execution time metric
            self.add_metric("execution_time", execution_time, "seconds", {
                "file_size": file_size,
                "rules_executed": rules_executed,
                "cache_hit": cache_hit
            })
            
            # Add memory usage metric
            self.add_metric("memory_usage", memory_usage, "MB")
            
            logger.debug(f"Tracked analysis: {target.path} ({execution_time:.3f}s)")
            
        except Exception as e:
            logger.warning(f"Failed to track analysis performance: {e}")
    
    def add_metric(self, name: str, value: float, unit: str, 
                   metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        Add a custom performance metric.
        
        Args:
            name: Metric name
            value: Metric value
            unit: Unit of measurement
            metadata: Optional metadata
        """
        if not self._monitoring_active:
            return
        
        metric = PerformanceMetric(
            name=name,
            value=value,
            unit=unit,
            metadata=metadata or {}
        )
        
        self.metrics[name].append(metric)
    
    def get_performance_report(self) -> Dict[str, Any]:
        """
        Generate comprehensive performance report.
        
        Returns:
            Performance report with statistics and benchmarks
        """
        if self.total_analyses == 0:
            return {"status": "no_data", "analyses": 0}
        
        # Calculate basic statistics
        avg_execution_time = self.total_execution_time / self.total_analyses
        avg_file_size = self.total_bytes_processed / self.total_files_processed if self.total_files_processed > 0 else 0
        cache_hit_rate = self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0
        
        # Calculate percentiles for execution time
        execution_times = [p.execution_time for p in self.analysis_history]
        execution_times.sort()
        
        percentiles = {}
        if execution_times:
            percentiles = {
                "p50": self._percentile(execution_times, 50),
                "p90": self._percentile(execution_times, 90),
                "p95": self._percentile(execution_times, 95),
                "p99": self._percentile(execution_times, 99)
            }
        
        # Analyze performance by file size
        size_analysis = self._analyze_performance_by_size()
        
        # Check benchmark compliance
        benchmark_results = self._check_benchmarks()
        
        # Get recent performance trend
        trend = self._get_performance_trend()
        
        return {
            "summary": {
                "total_analyses": self.total_analyses,
                "total_execution_time": self.total_execution_time,
                "average_execution_time": avg_execution_time,
                "average_file_size": avg_file_size,
                "cache_hit_rate": cache_hit_rate
            },
            "execution_time_percentiles": percentiles,
            "performance_by_size": size_analysis,
            "benchmark_results": benchmark_results,
            "trend": trend,
            "system_metrics": self._get_system_metrics()
        }
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """Calculate percentile value."""
        if not data:
            return 0.0
        
        index = int(len(data) * percentile / 100)
        if index >= len(data):
            index = len(data) - 1
        
        return data[index]
    
    def _analyze_performance_by_size(self) -> Dict[str, Any]:
        """Analyze performance by file size categories."""
        small_files = []  # <100 lines
        medium_files = []  # 100-1000 lines
        large_files = []  # >1000 lines
        
        for perf in self.analysis_history:
            # Estimate lines from file size (rough approximation)
            estimated_lines = perf.file_size / 50  # ~50 chars per line average
            
            if estimated_lines < 100:
                small_files.append(perf.execution_time)
            elif estimated_lines < 1000:
                medium_files.append(perf.execution_time)
            else:
                large_files.append(perf.execution_time)
        
        def analyze_category(times: List[float], category: str) -> Dict[str, Any]:
            if not times:
                return {"count": 0, "average": 0.0, "max": 0.0}
            
            return {
                "count": len(times),
                "average": sum(times) / len(times),
                "max": max(times),
                "min": min(times)
            }
        
        return {
            "small_files": analyze_category(small_files, "small"),
            "medium_files": analyze_category(medium_files, "medium"),
            "large_files": analyze_category(large_files, "large")
        }
    
    def _check_benchmarks(self) -> Dict[str, Any]:
        """Check performance against benchmarks."""
        size_analysis = self._analyze_performance_by_size()
        
        results = {}
        
        # Check small files benchmark
        if size_analysis["small_files"]["count"] > 0:
            avg_time = size_analysis["small_files"]["average"]
            target_time = self.benchmarks["small_files"]["target_time"]
            results["small_files"] = {
                "target": target_time,
                "actual": avg_time,
                "passed": avg_time <= target_time,
                "ratio": avg_time / target_time if target_time > 0 else float('inf')
            }
        
        # Check medium files benchmark
        if size_analysis["medium_files"]["count"] > 0:
            avg_time = size_analysis["medium_files"]["average"]
            target_time = self.benchmarks["medium_files"]["target_time"]
            results["medium_files"] = {
                "target": target_time,
                "actual": avg_time,
                "passed": avg_time <= target_time,
                "ratio": avg_time / target_time if target_time > 0 else float('inf')
            }
        
        # Check large files benchmark
        if size_analysis["large_files"]["count"] > 0:
            avg_time = size_analysis["large_files"]["average"]
            target_time = self.benchmarks["large_files"]["target_time"]
            results["large_files"] = {
                "target": target_time,
                "actual": avg_time,
                "passed": avg_time <= target_time,
                "ratio": avg_time / target_time if target_time > 0 else float('inf')
            }
        
        return results
    
    def _get_performance_trend(self) -> Dict[str, Any]:
        """Analyze recent performance trend."""
        if len(self.analysis_history) < 10:
            return {"status": "insufficient_data"}
        
        # Get recent analyses (last 20% or minimum 10)
        recent_count = max(10, len(self.analysis_history) // 5)
        recent_analyses = list(self.analysis_history)[-recent_count:]
        
        # Calculate trend
        recent_avg = sum(p.execution_time for p in recent_analyses) / len(recent_analyses)
        
        # Compare with overall average
        overall_avg = self.total_execution_time / self.total_analyses
        
        trend_ratio = recent_avg / overall_avg if overall_avg > 0 else 1.0
        
        if trend_ratio < 0.9:
            trend = "improving"
        elif trend_ratio > 1.1:
            trend = "degrading"
        else:
            trend = "stable"
        
        return {
            "status": "available",
            "trend": trend,
            "recent_average": recent_avg,
            "overall_average": overall_avg,
            "trend_ratio": trend_ratio
        }
    
    def _get_system_metrics(self) -> Dict[str, Any]:
        """Get current system metrics."""
        try:
            process = psutil.Process()
            
            return {
                "cpu_percent": process.cpu_percent(),
                "memory_mb": process.memory_info().rss / 1024 / 1024,
                "memory_percent": process.memory_percent(),
                "threads": process.num_threads()
            }
        except Exception:
            return {}
    
    def _start_system_monitoring(self) -> None:
        """Start background system monitoring."""
        def monitor():
            while not self._stop_monitoring.is_set():
                try:
                    # Add system metrics
                    metrics = self._get_system_metrics()
                    for name, value in metrics.items():
                        if isinstance(value, (int, float)):
                            unit = "percent" if "percent" in name else ("MB" if "mb" in name else "count")
                            self.add_metric(f"system_{name}", value, unit)
                    
                    # Sleep for 5 seconds
                    self._stop_monitoring.wait(5)
                    
                except Exception as e:
                    logger.warning(f"System monitoring error: {e}")
                    break
        
        self._system_monitor_thread = threading.Thread(target=monitor, daemon=True)
        self._system_monitor_thread.start()
    
    def _stop_system_monitoring(self) -> None:
        """Stop background system monitoring."""
        if self._system_monitor_thread:
            self._stop_monitoring.set()
            self._system_monitor_thread.join(timeout=1)
