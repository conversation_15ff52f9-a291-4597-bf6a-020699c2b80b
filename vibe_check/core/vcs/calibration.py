"""
File: vibe_check/core/vcs/calibration.py
Purpose: Issue calibration framework for VCS engine to maintain realistic issue counts
Related Files: vibe_check/core/vcs/engine.py, vibe_check/core/vcs/rules/
Dependencies: typing, enum, dataclasses, pathlib
"""

from typing import Dict, List, Optional, Tuple, Any
from enum import Enum
from dataclasses import dataclass
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class AnalysisProfile(Enum):
    """Analysis profiles with different sensitivity levels."""
    MINIMAL = "minimal"
    STANDARD = "standard"
    STRICT = "strict"


@dataclass
class IssueTargetRange:
    """Target issue count range for a profile."""
    min_issues: int
    max_issues: int
    max_per_rule: int
    
    def is_within_range(self, count: int) -> bool:
        """Check if issue count is within target range."""
        return self.min_issues <= count <= self.max_issues


@dataclass
class RuleCalibrationConfig:
    """Calibration configuration for a specific rule."""
    rule_id: str
    enabled: bool = True
    max_issues_per_file: int = 5
    severity_threshold: str = "warning"
    priority: int = 1  # 1=high, 2=medium, 3=low


class IssueCalibrationFramework:
    """Framework for calibrating rule sensitivity to realistic levels."""
    
    # Target issue ranges by profile
    TARGET_RANGES = {
        AnalysisProfile.MINIMAL: IssueTargetRange(0, 5, 2),
        AnalysisProfile.STANDARD: IssueTargetRange(5, 15, 5),
        AnalysisProfile.STRICT: IssueTargetRange(15, 25, 10)
    }
    
    # Rule calibration by profile
    RULE_CALIBRATIONS = {
        AnalysisProfile.MINIMAL: {
            # Only critical issues
            "S001": RuleCalibrationConfig("S001", enabled=False),  # Line length disabled
            "S002": RuleCalibrationConfig("S002", enabled=True, max_issues_per_file=2),
            "S003": RuleCalibrationConfig("S003", enabled=True, max_issues_per_file=1),
            "SEC001": RuleCalibrationConfig("SEC001", enabled=True, max_issues_per_file=3),
            "SEC002": RuleCalibrationConfig("SEC002", enabled=True, max_issues_per_file=3),
            "C001": RuleCalibrationConfig("C001", enabled=True, max_issues_per_file=2),
        },
        AnalysisProfile.STANDARD: {
            # Balanced analysis
            "S001": RuleCalibrationConfig("S001", enabled=True, max_issues_per_file=3),
            "S002": RuleCalibrationConfig("S002", enabled=True, max_issues_per_file=5),
            "S003": RuleCalibrationConfig("S003", enabled=True, max_issues_per_file=3),
            "SEC001": RuleCalibrationConfig("SEC001", enabled=True, max_issues_per_file=5),
            "SEC002": RuleCalibrationConfig("SEC002", enabled=True, max_issues_per_file=5),
            "C001": RuleCalibrationConfig("C001", enabled=True, max_issues_per_file=3),
            "C002": RuleCalibrationConfig("C002", enabled=True, max_issues_per_file=2),
            "C003": RuleCalibrationConfig("C003", enabled=True, max_issues_per_file=2),
        },
        AnalysisProfile.STRICT: {
            # Comprehensive analysis
            "S001": RuleCalibrationConfig("S001", enabled=True, max_issues_per_file=10),
            "S002": RuleCalibrationConfig("S002", enabled=True, max_issues_per_file=10),
            "S003": RuleCalibrationConfig("S003", enabled=True, max_issues_per_file=5),
            "SEC001": RuleCalibrationConfig("SEC001", enabled=True, max_issues_per_file=10),
            "SEC002": RuleCalibrationConfig("SEC002", enabled=True, max_issues_per_file=10),
            "C001": RuleCalibrationConfig("C001", enabled=True, max_issues_per_file=5),
            "C002": RuleCalibrationConfig("C002", enabled=True, max_issues_per_file=5),
            "C003": RuleCalibrationConfig("C003", enabled=True, max_issues_per_file=5),
            "C004": RuleCalibrationConfig("C004", enabled=True, max_issues_per_file=3),
            "C005": RuleCalibrationConfig("C005", enabled=True, max_issues_per_file=3),
        }
    }
    
    def __init__(self, profile: AnalysisProfile = AnalysisProfile.STANDARD):
        """Initialize calibration framework with specified profile."""
        self.profile = profile
        self.target_range = self.TARGET_RANGES[profile]
        self.rule_configs = self.RULE_CALIBRATIONS.get(profile, {})
        
    def get_rule_config(self, rule_id: str) -> Optional[RuleCalibrationConfig]:
        """Get calibration configuration for a rule."""
        return self.rule_configs.get(rule_id)
    
    def is_rule_enabled(self, rule_id: str) -> bool:
        """Check if rule is enabled for current profile."""
        config = self.get_rule_config(rule_id)
        return config.enabled if config else True
    
    def get_max_issues_per_file(self, rule_id: str) -> int:
        """Get maximum issues per file for a rule."""
        config = self.get_rule_config(rule_id)
        return config.max_issues_per_file if config else 5
    
    def calibrate_issues(self, issues: List[Any], file_path: Path) -> List[Any]:
        """Calibrate issues for a file based on current profile."""
        if not issues:
            return issues
        
        # Group issues by rule
        issues_by_rule = {}
        for issue in issues:
            rule_id = getattr(issue, 'rule_id', 'UNKNOWN')
            if rule_id not in issues_by_rule:
                issues_by_rule[rule_id] = []
            issues_by_rule[rule_id].append(issue)
        
        calibrated_issues = []
        
        for rule_id, rule_issues in issues_by_rule.items():
            # Check if rule is enabled
            if not self.is_rule_enabled(rule_id):
                logger.debug(f"Rule {rule_id} disabled for profile {self.profile.value}")
                continue
            
            # Apply per-rule limits
            max_per_file = self.get_max_issues_per_file(rule_id)
            limited_issues = rule_issues[:max_per_file]
            
            if len(rule_issues) > max_per_file:
                logger.debug(
                    f"Rule {rule_id} in {file_path.name}: "
                    f"Limited from {len(rule_issues)} to {max_per_file} issues"
                )
            
            calibrated_issues.extend(limited_issues)
        
        return calibrated_issues
    
    def validate_file_issue_count(self, issue_count: int, file_path: Path) -> bool:
        """Validate if file issue count is within acceptable range."""
        if issue_count > self.target_range.max_issues:
            logger.warning(
                f"File {file_path.name} has {issue_count} issues "
                f"(exceeds {self.target_range.max_issues} for {self.profile.value} profile)"
            )
            return False
        return True
    
    def get_profile_summary(self) -> Dict[str, Any]:
        """Get summary of current profile configuration."""
        enabled_rules = [
            rule_id for rule_id, config in self.rule_configs.items() 
            if config.enabled
        ]
        
        return {
            "profile": self.profile.value,
            "target_range": {
                "min": self.target_range.min_issues,
                "max": self.target_range.max_issues,
                "max_per_rule": self.target_range.max_per_rule
            },
            "enabled_rules": enabled_rules,
            "total_rules": len(self.rule_configs),
            "enabled_count": len(enabled_rules)
        }
    
    @classmethod
    def create_from_profile_name(cls, profile_name: str) -> 'IssueCalibrationFramework':
        """Create calibration framework from profile name string."""
        try:
            profile = AnalysisProfile(profile_name.lower())
            return cls(profile)
        except ValueError:
            logger.warning(f"Unknown profile '{profile_name}', using standard")
            return cls(AnalysisProfile.STANDARD)
    
    def apply_global_limits(self, all_issues: List[Any]) -> List[Any]:
        """Apply global issue limits across all files."""
        if len(all_issues) <= self.target_range.max_issues:
            return all_issues
        
        # Prioritize issues by severity and rule priority
        prioritized_issues = sorted(
            all_issues,
            key=lambda issue: (
                self._get_severity_priority(getattr(issue, 'severity', 'info')),
                self._get_rule_priority(getattr(issue, 'rule_id', 'UNKNOWN'))
            )
        )
        
        limited_issues = prioritized_issues[:self.target_range.max_issues]
        
        logger.info(
            f"Applied global limit: {len(all_issues)} → {len(limited_issues)} issues "
            f"for {self.profile.value} profile"
        )
        
        return limited_issues
    
    def _get_severity_priority(self, severity: str) -> int:
        """Get priority value for severity (lower = higher priority)."""
        severity_priorities = {
            'error': 1,
            'warning': 2,
            'info': 3,
            'hint': 4
        }
        return severity_priorities.get(severity.lower(), 5)
    
    def _get_rule_priority(self, rule_id: str) -> int:
        """Get priority value for rule (lower = higher priority)."""
        config = self.get_rule_config(rule_id)
        return config.priority if config else 3
