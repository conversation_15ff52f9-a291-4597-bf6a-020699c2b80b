"""
File: vibe_check/core/vcs/config/rule_config.py
Purpose: Typed rule configuration system for VCS engine
Related Files: config.py, models.py, rule_loader.py
Dependencies: typing, dataclasses, abc, pydantic
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List, Union, Type, TypeVar, Generic
from enum import Enum
import logging

from vibe_check.core.constants import AnalysisThresholds

logger = logging.getLogger(__name__)

# Type variable for generic configuration
T = TypeVar('T', bound='RuleConfig')


class ConfigValidationError(Exception):
    """Raised when rule configuration validation fails."""
    
    def __init__(self, rule_id: str, field: str, value: Any, message: str):
        self.rule_id = rule_id
        self.field = field
        self.value = value
        self.message = message
        super().__init__(f"Rule {rule_id} config error in '{field}': {message}")


class ConfigFieldType(Enum):
    """Types of configuration fields."""
    INTEGER = "integer"
    FLOAT = "float"
    BOOLEAN = "boolean"
    STRING = "string"
    LIST = "list"
    DICT = "dict"
    ENUM = "enum"


@dataclass
class ConfigField:
    """Metadata for a configuration field."""
    name: str
    field_type: ConfigFieldType
    default: Any
    description: str
    min_value: Optional[Union[int, float]] = None
    max_value: Optional[Union[int, float]] = None
    allowed_values: Optional[List[Any]] = None
    required: bool = False
    validation_pattern: Optional[str] = None
    
    def validate(self, value: Any, rule_id: str) -> Any:
        """Validate and convert a configuration value."""
        if value is None:
            if self.required:
                raise ConfigValidationError(
                    rule_id, self.name, value, 
                    f"Required field '{self.name}' cannot be None"
                )
            return self.default
        
        # Type validation and conversion
        try:
            if self.field_type == ConfigFieldType.INTEGER:
                value = int(value)
                if self.min_value is not None and value < self.min_value:
                    raise ConfigValidationError(
                        rule_id, self.name, value,
                        f"Value {value} is below minimum {self.min_value}"
                    )
                if self.max_value is not None and value > self.max_value:
                    raise ConfigValidationError(
                        rule_id, self.name, value,
                        f"Value {value} is above maximum {self.max_value}"
                    )
                    
            elif self.field_type == ConfigFieldType.FLOAT:
                value = float(value)
                if self.min_value is not None and value < self.min_value:
                    raise ConfigValidationError(
                        rule_id, self.name, value,
                        f"Value {value} is below minimum {self.min_value}"
                    )
                if self.max_value is not None and value > self.max_value:
                    raise ConfigValidationError(
                        rule_id, self.name, value,
                        f"Value {value} is above maximum {self.max_value}"
                    )
                    
            elif self.field_type == ConfigFieldType.BOOLEAN:
                if isinstance(value, str):
                    value = value.lower() in ('true', '1', 'yes', 'on')
                else:
                    value = bool(value)
                    
            elif self.field_type == ConfigFieldType.STRING:
                value = str(value)
                if self.validation_pattern:
                    import re
                    if not re.match(self.validation_pattern, value):
                        raise ConfigValidationError(
                            rule_id, self.name, value,
                            f"Value '{value}' does not match pattern '{self.validation_pattern}'"
                        )
                        
            elif self.field_type == ConfigFieldType.LIST:
                if not isinstance(value, list):
                    raise ConfigValidationError(
                        rule_id, self.name, value,
                        f"Expected list, got {type(value).__name__}"
                    )
                    
            elif self.field_type == ConfigFieldType.DICT:
                if not isinstance(value, dict):
                    raise ConfigValidationError(
                        rule_id, self.name, value,
                        f"Expected dict, got {type(value).__name__}"
                    )
                    
            elif self.field_type == ConfigFieldType.ENUM:
                if self.allowed_values and value not in self.allowed_values:
                    raise ConfigValidationError(
                        rule_id, self.name, value,
                        f"Value '{value}' not in allowed values: {self.allowed_values}"
                    )
                    
        except (ValueError, TypeError) as e:
            raise ConfigValidationError(
                rule_id, self.name, value,
                f"Type conversion error: {e}"
            )
        
        return value


class RuleConfig(ABC):
    """Abstract base class for typed rule configurations."""
    
    def __init__(self, rule_id: str, raw_config: Optional[Dict[str, Any]] = None):
        """Initialize rule configuration with validation."""
        self.rule_id = rule_id
        self._raw_config = raw_config or {}
        self._validated_config: Dict[str, Any] = {}
        self._validation_errors: List[ConfigValidationError] = []
        
        # Validate and populate configuration
        self._validate_and_populate()
    
    @abstractmethod
    def get_config_fields(self) -> Dict[str, ConfigField]:
        """Return configuration field definitions."""
        pass
    
    def _validate_and_populate(self) -> None:
        """Validate raw configuration and populate typed fields."""
        fields = self.get_config_fields()
        
        for field_name, field_def in fields.items():
            try:
                raw_value = self._raw_config.get(field_name)
                validated_value = field_def.validate(raw_value, self.rule_id)
                self._validated_config[field_name] = validated_value
                
                # Set as instance attribute for easy access
                setattr(self, field_name, validated_value)
                
            except ConfigValidationError as e:
                self._validation_errors.append(e)
                # Set default value on error
                setattr(self, field_name, field_def.default)
    
    def is_valid(self) -> bool:
        """Check if configuration is valid."""
        return len(self._validation_errors) == 0
    
    def get_validation_errors(self) -> List[ConfigValidationError]:
        """Get list of validation errors."""
        return self._validation_errors.copy()
    
    def get_config_value(self, field_name: str, default: Any = None) -> Any:
        """Get validated configuration value."""
        return self._validated_config.get(field_name, default)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return self._validated_config.copy()
    
    def get_config_schema(self) -> Dict[str, Dict[str, Any]]:
        """Get configuration schema for documentation."""
        fields = self.get_config_fields()
        schema = {}
        
        for field_name, field_def in fields.items():
            schema[field_name] = {
                'type': field_def.field_type.value,
                'default': field_def.default,
                'description': field_def.description,
                'required': field_def.required,
                'min_value': field_def.min_value,
                'max_value': field_def.max_value,
                'allowed_values': field_def.allowed_values,
                'validation_pattern': field_def.validation_pattern
            }
        
        return schema


# Style Rule Configurations

class StyleRuleConfig(RuleConfig):
    """Base configuration for style rules."""

    def __init__(self, rule_id: str, raw_config: Optional[Dict[str, Any]] = None):
        super().__init__(rule_id, raw_config)
        # Will be populated by validation
        self.max_line_length: int = AnalysisThresholds.MAX_LINE_LENGTH
        self.enforce_pep8: bool = True
        self.ignore_comments: bool = False
    
    def get_config_fields(self) -> Dict[str, ConfigField]:
        return {
            'max_line_length': ConfigField(
                name='max_line_length',
                field_type=ConfigFieldType.INTEGER,
                default=AnalysisThresholds.MAX_LINE_LENGTH,
                description='Maximum allowed line length',
                min_value=50,
                max_value=200
            ),
            'enforce_pep8': ConfigField(
                name='enforce_pep8',
                field_type=ConfigFieldType.BOOLEAN,
                default=True,
                description='Enforce PEP 8 style guidelines'
            ),
            'ignore_comments': ConfigField(
                name='ignore_comments',
                field_type=ConfigFieldType.BOOLEAN,
                default=False,
                description='Ignore line length for comment-only lines'
            )
        }


class LineLengthConfig(StyleRuleConfig):
    """Configuration for line length rule (S001)."""

    def __init__(self, rule_id: str, raw_config: Optional[Dict[str, Any]] = None):
        super().__init__(rule_id, raw_config)
        # Additional fields specific to line length
        self.ignore_urls: bool = True
        self.ignore_imports: bool = False
    
    def get_config_fields(self) -> Dict[str, ConfigField]:
        base_fields = super().get_config_fields()
        base_fields.update({
            'ignore_urls': ConfigField(
                name='ignore_urls',
                field_type=ConfigFieldType.BOOLEAN,
                default=True,
                description='Ignore lines containing URLs'
            ),
            'ignore_imports': ConfigField(
                name='ignore_imports',
                field_type=ConfigFieldType.BOOLEAN,
                default=False,
                description='Ignore import statement line length'
            )
        })
        return base_fields


class NamingConventionConfig(StyleRuleConfig):
    """Configuration for naming convention rule (S004)."""

    def __init__(self, rule_id: str, raw_config: Optional[Dict[str, Any]] = None):
        super().__init__(rule_id, raw_config)
        # Naming convention settings
        self.class_name_pattern: str = r'^[A-Z][a-zA-Z0-9]*$'
        self.function_name_pattern: str = r'^[a-z_][a-z0-9_]*$'
        self.variable_name_pattern: str = r'^[a-z_][a-z0-9_]*$'
        self.constant_name_pattern: str = r'^[A-Z_][A-Z0-9_]*$'
    
    def get_config_fields(self) -> Dict[str, ConfigField]:
        base_fields = super().get_config_fields()
        base_fields.update({
            'class_name_pattern': ConfigField(
                name='class_name_pattern',
                field_type=ConfigFieldType.STRING,
                default=r'^[A-Z][a-zA-Z0-9]*$',
                description='Regex pattern for class names (PascalCase)',
                validation_pattern=r'^.+$'  # Basic non-empty validation
            ),
            'function_name_pattern': ConfigField(
                name='function_name_pattern',
                field_type=ConfigFieldType.STRING,
                default=r'^[a-z_][a-z0-9_]*$',
                description='Regex pattern for function names (snake_case)',
                validation_pattern=r'^.+$'
            ),
            'variable_name_pattern': ConfigField(
                name='variable_name_pattern',
                field_type=ConfigFieldType.STRING,
                default=r'^[a-z_][a-z0-9_]*$',
                description='Regex pattern for variable names (snake_case)',
                validation_pattern=r'^.+$'
            ),
            'constant_name_pattern': ConfigField(
                name='constant_name_pattern',
                field_type=ConfigFieldType.STRING,
                default=r'^[A-Z_][A-Z0-9_]*$',
                description='Regex pattern for constant names (UPPER_CASE)',
                validation_pattern=r'^.+$'
            )
        })
        return base_fields


# Complexity Rule Configurations

class ComplexityRuleConfig(RuleConfig):
    """Base configuration for complexity rules."""

    def __init__(self, rule_id: str, raw_config: Optional[Dict[str, Any]] = None):
        super().__init__(rule_id, raw_config)
        # Common complexity settings
        self.complexity_threshold: int = AnalysisThresholds.CYCLOMATIC_COMPLEXITY
        self.cognitive_complexity_threshold: int = AnalysisThresholds.COGNITIVE_COMPLEXITY
    
    def get_config_fields(self) -> Dict[str, ConfigField]:
        return {
            'complexity_threshold': ConfigField(
                name='complexity_threshold',
                field_type=ConfigFieldType.INTEGER,
                default=AnalysisThresholds.CYCLOMATIC_COMPLEXITY,
                description='Maximum allowed cyclomatic complexity',
                min_value=1,
                max_value=50
            ),
            'cognitive_complexity_threshold': ConfigField(
                name='cognitive_complexity_threshold',
                field_type=ConfigFieldType.INTEGER,
                default=AnalysisThresholds.COGNITIVE_COMPLEXITY,
                description='Maximum allowed cognitive complexity',
                min_value=1,
                max_value=100
            )
        }


class CyclomaticComplexityConfig(ComplexityRuleConfig):
    """Configuration for cyclomatic complexity rule (C001)."""

    def __init__(self, rule_id: str, raw_config: Optional[Dict[str, Any]] = None):
        super().__init__(rule_id, raw_config)
        # Additional settings
        self.include_nested_functions: bool = True
        self.include_lambda_functions: bool = False
    
    def get_config_fields(self) -> Dict[str, ConfigField]:
        base_fields = super().get_config_fields()
        base_fields.update({
            'include_nested_functions': ConfigField(
                name='include_nested_functions',
                field_type=ConfigFieldType.BOOLEAN,
                default=True,
                description='Include nested functions in complexity calculation'
            ),
            'include_lambda_functions': ConfigField(
                name='include_lambda_functions',
                field_type=ConfigFieldType.BOOLEAN,
                default=False,
                description='Include lambda functions in complexity calculation'
            )
        })
        return base_fields


class FunctionLengthConfig(ComplexityRuleConfig):
    """Configuration for function length rule (C002)."""

    def __init__(self, rule_id: str, raw_config: Optional[Dict[str, Any]] = None):
        super().__init__(rule_id, raw_config)
        # Function length settings
        self.max_function_lines: int = AnalysisThresholds.MAX_FUNCTION_LENGTH
        self.count_blank_lines: bool = False
        self.count_comment_lines: bool = True
    
    def get_config_fields(self) -> Dict[str, ConfigField]:
        base_fields = super().get_config_fields()
        base_fields.update({
            'max_function_lines': ConfigField(
                name='max_function_lines',
                field_type=ConfigFieldType.INTEGER,
                default=AnalysisThresholds.MAX_FUNCTION_LENGTH,
                description='Maximum allowed function length in lines',
                min_value=5,
                max_value=500
            ),
            'count_blank_lines': ConfigField(
                name='count_blank_lines',
                field_type=ConfigFieldType.BOOLEAN,
                default=False,
                description='Include blank lines in function length count'
            ),
            'count_comment_lines': ConfigField(
                name='count_comment_lines',
                field_type=ConfigFieldType.BOOLEAN,
                default=True,
                description='Include comment lines in function length count'
            )
        })
        return base_fields


# Configuration Factory and Registry

class RuleConfigRegistry:
    """Registry for rule configuration classes."""

    def __init__(self) -> None:
        self._config_classes: Dict[str, Type[RuleConfig]] = {}
        self._register_built_in_configs()

    def _register_built_in_configs(self) -> None:
        """Register built-in configuration classes."""
        # Style rules
        self.register('S001', LineLengthConfig)
        self.register('S004', NamingConventionConfig)

        # Complexity rules
        self.register('C001', CyclomaticComplexityConfig)
        self.register('C002', FunctionLengthConfig)

        # Base configurations for categories
        self.register('style_base', StyleRuleConfig)
        self.register('complexity_base', ComplexityRuleConfig)

    def register(self, rule_id: str, config_class: Type[RuleConfig]) -> None:
        """Register a configuration class for a rule."""
        self._config_classes[rule_id] = config_class
        logger.debug(f"Registered config class for rule {rule_id}: {config_class.__name__}")

    def get_config_class(self, rule_id: str) -> Optional[Type[RuleConfig]]:
        """Get configuration class for a rule."""
        return self._config_classes.get(rule_id)

    def create_config(self, rule_id: str, raw_config: Optional[Dict[str, Any]] = None) -> RuleConfig:
        """Create typed configuration instance for a rule."""
        config_class = self.get_config_class(rule_id)

        if config_class is None:
            # Fall back to base configuration based on rule category
            category_prefix = rule_id[0] if rule_id else 'S'
            category_map = {
                'S': 'style_base',
                'C': 'complexity_base'
            }

            base_config_id = category_map.get(category_prefix, 'style_base')
            config_class = self._config_classes.get(base_config_id, StyleRuleConfig)

            logger.warning(f"No specific config class for rule {rule_id}, using {config_class.__name__}")

        return config_class(rule_id, raw_config)

    def get_all_config_schemas(self) -> Dict[str, Dict[str, Any]]:
        """Get configuration schemas for all registered rules."""
        schemas = {}

        for rule_id, config_class in self._config_classes.items():
            # Create temporary instance to get schema
            temp_config = config_class(rule_id, {})
            schemas[rule_id] = temp_config.get_config_schema()

        return schemas

    def validate_all_configs(self, rule_configs: Dict[str, Dict[str, Any]]) -> Dict[str, List[ConfigValidationError]]:
        """Validate all rule configurations and return errors."""
        all_errors = {}

        for rule_id, raw_config in rule_configs.items():
            try:
                config = self.create_config(rule_id, raw_config)
                errors = config.get_validation_errors()
                if errors:
                    all_errors[rule_id] = errors
            except Exception as e:
                all_errors[rule_id] = [ConfigValidationError(
                    rule_id, 'general', raw_config, f"Configuration creation failed: {e}"
                )]

        return all_errors


# Global registry instance
_config_registry = RuleConfigRegistry()


def get_config_registry() -> RuleConfigRegistry:
    """Get the global configuration registry."""
    return _config_registry


def create_rule_config(rule_id: str, raw_config: Optional[Dict[str, Any]] = None) -> RuleConfig:
    """Create typed configuration for a rule."""
    return _config_registry.create_config(rule_id, raw_config)


def register_rule_config(rule_id: str, config_class: Type[RuleConfig]) -> None:
    """Register a configuration class for a rule."""
    _config_registry.register(rule_id, config_class)
