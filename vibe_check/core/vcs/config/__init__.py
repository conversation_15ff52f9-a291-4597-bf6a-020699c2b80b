"""
VCS Configuration Module
========================

Comprehensive configuration system for the VCS engine including:
- Typed rule configurations (RuleConfig system)
- Configuration loading and validation
- Type-safe configuration classes

Components:
- RuleConfig: Typed rule configuration system
- Configuration validation and schema management
"""

# Import typed rule configuration system
from .rule_config import (
    RuleConfig,
    ConfigValidationError,
    ConfigField,
    ConfigFieldType,

    # Base configurations
    StyleRuleConfig,
    ComplexityRuleConfig,

    # Specific rule configurations
    LineLengthConfig,
    NamingConventionConfig,
    CyclomaticComplexityConfig,
    FunctionLengthConfig,

    # Registry and factory functions
    RuleConfigRegistry,
    get_config_registry,
    create_rule_config,
    register_rule_config
)

__all__ = [
    # Typed rule configuration system
    "RuleConfig",
    "ConfigValidationError",
    "ConfigField",
    "ConfigFieldType",

    # Base configurations
    "StyleRuleConfig",
    "ComplexityRuleConfig",

    # Specific rule configurations
    "LineLengthConfig",
    "NamingConventionConfig",
    "CyclomaticComplexityConfig",
    "FunctionLengthConfig",

    # Registry and factory
    "RuleConfigRegistry",
    "get_config_registry",
    "create_rule_config",
    "register_rule_config"
]
