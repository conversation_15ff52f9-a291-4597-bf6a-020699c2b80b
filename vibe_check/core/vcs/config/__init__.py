"""
VCS Configuration Module
========================

Comprehensive configuration system for the VCS engine including:
- Typed rule configurations (RuleConfig system)
- Configuration loading and validation
- Type-safe configuration classes

Components:
- RuleConfig: Typed rule configuration system
- Configuration validation and schema management
"""

# Import main VCS configuration from the config.py file (not this directory)
import importlib.util
import sys
from pathlib import Path

# Load VCSConfig from the config.py file directly
config_file_path = Path(__file__).parent.parent / "config.py"
spec = importlib.util.spec_from_file_location("vcs_config_module", config_file_path)
config_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(config_module)

VCSConfig = config_module.VCSConfig
VCSConfigManager = config_module.VCSConfigManager

# Import typed rule configuration system
from .rule_config import (
    RuleConfig,
    Config<PERSON><PERSON><PERSON><PERSON>r,
    Config<PERSON>ield,
    ConfigFieldType,

    # Base configurations
    StyleRuleConfig,
    ComplexityRuleConfig,

    # Specific rule configurations
    LineLengthConfig,
    NamingConventionConfig,
    CyclomaticComplexityConfig,
    FunctionLengthConfig,

    # Registry and factory functions
    RuleConfigRegistry,
    get_config_registry,
    create_rule_config,
    register_rule_config
)

__all__ = [
    # Main VCS configuration
    "VCSConfig",
    "VCSConfigManager",

    # Typed rule configuration system
    "RuleConfig",
    "ConfigValidationError",
    "ConfigField",
    "ConfigFieldType",

    # Base configurations
    "StyleRuleConfig",
    "ComplexityRuleConfig",

    # Specific rule configurations
    "LineLengthConfig",
    "NamingConventionConfig",
    "CyclomaticComplexityConfig",
    "FunctionLengthConfig",

    # Registry and factory
    "RuleConfigRegistry",
    "get_config_registry",
    "create_rule_config",
    "register_rule_config"
]
