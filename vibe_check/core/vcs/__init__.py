"""
Vibe Check Standalone (VCS) Engine
==================================

This module provides the core VCS engine that enables Vibe Check to operate
in both integrated and standalone modes, providing substantial value without
external tool dependencies.

The VCS engine includes:
- VibeCheckEngine: Core analysis engine with dual-mode operation
- RuleRegistry: Extensible rule management system
- Built-in analysis rules across 6 categories
- Configuration management and caching
- Performance optimization and monitoring

Architecture Overview:
- Dual-mode operation (integrated/standalone)
- Rule-based analysis with extensible registry
- AST-based code analysis and transformation
- Multi-level caching for performance
- Plugin system for extensibility
"""


__all__ = [
    "VibeCheckEngine",
    "EngineMode",
    "RuleRegistry",
    "RuleCategory",
    "AnalysisRule",
    "AnalysisTarget",
    "AnalysisResult",
    "AnalysisContext",
    "VCSConfig",
    "VCSConfigManager"
]
