"""
File: vibe_check/core/vcs/auto_fix.py
Purpose: Auto-fix engine for VCS analysis issues
Related Files: vibe_check/core/vcs/engine.py, vibe_check/core/vcs/models.py
Dependencies: ast, pathlib, typing
"""

import ast
import re
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Callable, Optional, Any

from vibe_check.core.logging import get_logger
from .models import AnalysisIssue, AnalysisTarget

logger = get_logger(__name__)


class FixResult(Enum):
    """Result of an auto-fix operation."""
    SUCCESS = "success"
    FAILED = "failed"
    SKIPPED = "skipped"
    UNSAFE = "unsafe"


@dataclass
class FixOperation:
    """Represents a single fix operation."""
    issue: AnalysisIssue
    original_content: str
    fixed_content: str
    description: str
    safe: bool = True
    confidence: float = 1.0
    
    def apply(self, content: str) -> str:
        """Apply the fix to the content."""
        return self.fixed_content


@dataclass
class FixReport:
    """Report of auto-fix operations."""
    target: AnalysisTarget
    operations: List[FixOperation]
    successful_fixes: int = 0
    failed_fixes: int = 0
    skipped_fixes: int = 0
    unsafe_fixes: int = 0
    
    def add_result(self, operation: FixOperation, result: FixResult):
        """Add a fix result to the report."""
        if result == FixResult.SUCCESS:
            self.successful_fixes += 1
        elif result == FixResult.FAILED:
            self.failed_fixes += 1
        elif result == FixResult.SKIPPED:
            self.skipped_fixes += 1
        elif result == FixResult.UNSAFE:
            self.unsafe_fixes += 1


class AutoFixEngine:
    """Engine for automatically fixing code issues."""
    
    def __init__(self):
        self.fixers: Dict[str, Callable] = {}
        self.safe_rules = {
            # Style fixes (generally safe)
            'S002', 'S005', 'S006',  # Whitespace, blank lines, multiple statements
            # Import fixes (safe with validation)
            'I001', 'I002', 'I006',  # Unused imports, import order, grouping
            # Simple type fixes
            'T005',  # Generic type usage
        }
        self._register_built_in_fixers()
    
    def _register_built_in_fixers(self):
        """Register built-in fix functions."""
        self.fixers.update({
            'S002': self._fix_trailing_whitespace,
            'S005': self._fix_blank_lines,
            'S006': self._fix_multiple_statements,
            'I001': self._fix_unused_imports,
            'I002': self._fix_import_order,
            'T005': self._fix_generic_types,
        })
    
    def can_fix(self, issue: AnalysisIssue) -> bool:
        """Check if an issue can be auto-fixed."""
        return issue.rule_id in self.fixers and issue.auto_fixable
    
    def is_safe_fix(self, issue: AnalysisIssue) -> bool:
        """Check if a fix is considered safe."""
        return issue.rule_id in self.safe_rules
    
    def fix_issue(self, issue: AnalysisIssue, content: str, ast_tree: Optional[ast.AST] = None) -> Optional[FixOperation]:
        """Fix a single issue."""
        if not self.can_fix(issue):
            return None
        
        fixer = self.fixers.get(issue.rule_id)
        if not fixer:
            return None
        
        try:
            fixed_content = fixer(issue, content, ast_tree)
            if fixed_content and fixed_content != content:
                return FixOperation(
                    issue=issue,
                    original_content=content,
                    fixed_content=fixed_content,
                    description=f"Fixed {issue.rule_id}: {issue.message}",
                    safe=self.is_safe_fix(issue),
                    confidence=0.9 if self.is_safe_fix(issue) else 0.7
                )
        except Exception as e:
            logger.error(f"Error fixing issue {issue.rule_id}: {e}")
        
        return None
    
    def fix_issues(self, issues: List[AnalysisIssue], content: str, 
                   ast_tree: Optional[ast.AST] = None, safe_only: bool = True) -> List[FixOperation]:
        """Fix multiple issues in order of safety and line number."""
        operations = []
        current_content = content
        
        # Sort issues by safety (safe first) and line number
        sorted_issues = sorted(issues, key=lambda i: (not self.is_safe_fix(i), i.line))
        
        for issue in sorted_issues:
            if safe_only and not self.is_safe_fix(issue):
                continue
            
            operation = self.fix_issue(issue, current_content, ast_tree)
            if operation:
                operations.append(operation)
                current_content = operation.fixed_content
        
        return operations
    
    # Built-in fixer implementations
    
    def _fix_trailing_whitespace(self, issue: AnalysisIssue, content: str, ast_tree: Optional[ast.AST]) -> str:
        """Fix trailing whitespace."""
        lines = content.splitlines(keepends=True)
        fixed_lines = []
        
        for line in lines:
            fixed_lines.append(line.rstrip() + ('\n' if line.endswith('\n') else ''))
        
        return ''.join(fixed_lines)
    
    def _fix_blank_lines(self, issue: AnalysisIssue, content: str, ast_tree: Optional[ast.AST]) -> str:
        """Fix blank line issues."""
        lines = content.splitlines(keepends=True)
        
        # Remove excessive blank lines (more than 2 consecutive)
        fixed_lines = []
        blank_count = 0
        
        for line in lines:
            if line.strip() == '':
                blank_count += 1
                if blank_count <= 2:  # Allow up to 2 blank lines
                    fixed_lines.append(line)
            else:
                blank_count = 0
                fixed_lines.append(line)
        
        return ''.join(fixed_lines)
    
    def _fix_multiple_statements(self, issue: AnalysisIssue, content: str, ast_tree: Optional[ast.AST]) -> str:
        """Fix multiple statements on one line."""
        lines = content.splitlines(keepends=True)
        
        if issue.line <= len(lines):
            line_idx = issue.line - 1
            line = lines[line_idx]
            
            # Simple case: split on semicolon
            if ';' in line and not line.strip().startswith('#'):
                parts = line.split(';')
                if len(parts) > 1:
                    indent = len(line) - len(line.lstrip())
                    indent_str = ' ' * indent
                    
                    new_lines = []
                    for i, part in enumerate(parts):
                        part = part.strip()
                        if part:
                            if i == 0:
                                new_lines.append(indent_str + part + '\n')
                            else:
                                new_lines.append(indent_str + part + '\n')
                    
                    lines[line_idx:line_idx+1] = new_lines
        
        return ''.join(lines)
    
    def _fix_unused_imports(self, issue: AnalysisIssue, content: str, ast_tree: Optional[ast.AST]) -> str:
        """Fix unused imports."""
        if not ast_tree:
            return content
        
        lines = content.splitlines(keepends=True)
        
        # Find import statements to remove
        for node in ast.walk(ast_tree):
            if isinstance(node, (ast.Import, ast.ImportFrom)):
                if node.lineno == issue.line:
                    # Check if this is the unused import
                    line_idx = node.lineno - 1
                    if line_idx < len(lines):
                        # Remove the line
                        lines[line_idx] = ''
                    break
        
        return ''.join(lines)
    
    def _fix_import_order(self, issue: AnalysisIssue, content: str, ast_tree: Optional[ast.AST]) -> str:
        """Fix import order (basic implementation)."""
        if not ast_tree:
            return content
        
        lines = content.splitlines(keepends=True)
        
        # Collect all imports
        imports = []
        import_lines = set()
        
        for node in ast.walk(ast_tree):
            if isinstance(node, (ast.Import, ast.ImportFrom)):
                import_lines.add(node.lineno - 1)
                imports.append((node.lineno - 1, lines[node.lineno - 1]))
        
        if len(imports) < 2:
            return content
        
        # Sort imports: standard library, third-party, local
        def import_sort_key(import_info):
            line_idx, line = import_info
            line = line.strip()
            
            if line.startswith('from __future__'):
                return (0, line)
            elif line.startswith('import ') or line.startswith('from '):
                # Simple heuristic: assume single word modules are standard library
                module_name = line.split()[1].split('.')[0]
                if module_name in ['os', 'sys', 'json', 'datetime', 'pathlib', 're', 'typing']:
                    return (1, line)
                else:
                    return (2, line)
            return (3, line)
        
        sorted_imports = sorted(imports, key=import_sort_key)
        
        # Replace import lines
        for i, (original_idx, _) in enumerate(imports):
            _, sorted_line = sorted_imports[i]
            lines[original_idx] = sorted_line
        
        return ''.join(lines)
    
    def _fix_generic_types(self, issue: AnalysisIssue, content: str, ast_tree: Optional[ast.AST]) -> str:
        """Fix generic type usage."""
        lines = content.splitlines(keepends=True)
        
        if issue.line <= len(lines):
            line_idx = issue.line - 1
            line = lines[line_idx]
            
            # Simple replacements for common cases
            replacements = {
                r'\blist\b': 'List',
                r'\bdict\b': 'Dict',
                r'\bset\b': 'Set',
                r'\btuple\b': 'Tuple',
            }
            
            fixed_line = line
            for pattern, replacement in replacements.items():
                if re.search(pattern, fixed_line):
                    fixed_line = re.sub(pattern, replacement, fixed_line)
                    break
            
            lines[line_idx] = fixed_line
        
        return ''.join(lines)


class InteractiveFixEngine:
    """Interactive auto-fix engine with user confirmation."""
    
    def __init__(self, auto_fix_engine: AutoFixEngine):
        self.auto_fix_engine = auto_fix_engine
    
    def fix_interactively(self, issues: List[AnalysisIssue], content: str, 
                         ast_tree: Optional[ast.AST] = None) -> FixReport:
        """Fix issues interactively with user confirmation."""
        from rich.console import Console
        from rich.prompt import Confirm
        
        console = Console()
        report = FixReport(target=AnalysisTarget.from_content(content), operations=[])
        
        current_content = content
        
        for issue in issues:
            if not self.auto_fix_engine.can_fix(issue):
                continue
            
            console.print(f"\n[yellow]Issue found:[/yellow] {issue.rule_id}")
            console.print(f"Line {issue.line}: {issue.message}")
            
            if issue.fix_suggestion:
                console.print(f"[dim]Suggestion: {issue.fix_suggestion}[/dim]")
            
            operation = self.auto_fix_engine.fix_issue(issue, current_content, ast_tree)
            if operation:
                # Show preview of fix
                console.print("\n[blue]Proposed fix:[/blue]")
                lines = current_content.splitlines()
                if issue.line <= len(lines):
                    context_start = max(0, issue.line - 3)
                    context_end = min(len(lines), issue.line + 2)
                    
                    for i in range(context_start, context_end):
                        prefix = ">" if i == issue.line - 1 else " "
                        console.print(f"{prefix} {i+1:3d}: {lines[i]}")
                
                # Ask for confirmation
                if Confirm.ask("Apply this fix?"):
                    current_content = operation.fixed_content
                    report.operations.append(operation)
                    report.add_result(operation, FixResult.SUCCESS)
                    console.print("[green]Fix applied![/green]")
                else:
                    report.add_result(operation, FixResult.SKIPPED)
                    console.print("[yellow]Fix skipped[/yellow]")
        
        return report
