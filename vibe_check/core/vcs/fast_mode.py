"""
File: vibe_check/core/vcs/fast_mode.py
Purpose: Fast mode implementation for pre-commit optimization with Git integration
Related Files: vibe_check/core/vcs/engine.py, vibe_check/cli/commands.py
Dependencies: typing, pathlib, subprocess, asyncio, logging
"""

from typing import List, Set, Optional, Dict, Any, Tuple
from pathlib import Path
import subprocess
import asyncio
import logging
import time
from enum import Enum
from dataclasses import dataclass

logger = logging.getLogger(__name__)


class ChangeType(Enum):
    """Types of file changes detected by Git."""
    ADDED = "A"
    MODIFIED = "M"
    DELETED = "D"
    RENAMED = "R"
    COPIED = "C"
    UNMERGED = "U"


@dataclass
class FileChange:
    """Represents a file change detected by Git."""
    path: Path
    change_type: ChangeType
    old_path: Optional[Path] = None  # For renamed files
    
    @property
    def is_python_file(self) -> bool:
        """Check if the changed file is a Python file."""
        return self.path.suffix in {'.py', '.pyi'}
    
    @property
    def should_analyze(self) -> bool:
        """Check if this file change should be analyzed."""
        return (
            self.change_type in {ChangeType.ADDED, ChangeType.MODIFIED} and
            self.is_python_file and
            self.path.exists()
        )


class GitChangeDetector:
    """Detects file changes using Git for fast mode analysis."""
    
    def __init__(self, repository_root: Optional[Path] = None):
        """Initialize Git change detector."""
        self.repository_root = repository_root or self._find_git_root()
        
    def _find_git_root(self) -> Optional[Path]:
        """Find the Git repository root directory."""
        current = Path.cwd()
        while current != current.parent:
            if (current / '.git').exists():
                return current
            current = current.parent
        return None
    
    async def get_staged_files(self) -> List[FileChange]:
        """Get list of staged files for commit."""
        if not self.repository_root:
            logger.warning("Not in a Git repository, fast mode disabled")
            return []
        
        try:
            # Get staged files using git diff --cached
            result = await asyncio.create_subprocess_exec(
                'git', 'diff', '--cached', '--name-status',
                cwd=self.repository_root,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await result.communicate()
            
            if result.returncode != 0:
                logger.error(f"Git command failed: {stderr.decode()}")
                return []
            
            return self._parse_git_output(stdout.decode())
            
        except Exception as e:
            logger.error(f"Failed to get staged files: {e}")
            return []
    
    async def get_modified_files(self, include_untracked: bool = False) -> List[FileChange]:
        """Get list of modified files in working directory."""
        if not self.repository_root:
            logger.warning("Not in a Git repository, fast mode disabled")
            return []
        
        try:
            # Get modified files using git status
            cmd = ['git', 'status', '--porcelain']
            if include_untracked:
                cmd.append('--untracked-files=all')
            
            result = await asyncio.create_subprocess_exec(
                *cmd,
                cwd=self.repository_root,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await result.communicate()
            
            if result.returncode != 0:
                logger.error(f"Git command failed: {stderr.decode()}")
                return []
            
            return self._parse_git_status_output(stdout.decode())
            
        except Exception as e:
            logger.error(f"Failed to get modified files: {e}")
            return []
    
    def _parse_git_output(self, output: str) -> List[FileChange]:
        """Parse git diff --name-status output."""
        changes = []
        
        for line in output.strip().split('\n'):
            if not line:
                continue
            
            parts = line.split('\t')
            if len(parts) < 2:
                continue
            
            status = parts[0]
            file_path = parts[1]
            
            # Handle renamed files (R100 old_name new_name)
            if status.startswith('R') and len(parts) >= 3:
                old_path = Path(self.repository_root / parts[1])
                new_path = Path(self.repository_root / parts[2])
                change_type = ChangeType.RENAMED
                changes.append(FileChange(
                    path=new_path,
                    change_type=change_type,
                    old_path=old_path
                ))
            else:
                # Map Git status to ChangeType
                change_type_map = {
                    'A': ChangeType.ADDED,
                    'M': ChangeType.MODIFIED,
                    'D': ChangeType.DELETED,
                    'C': ChangeType.COPIED,
                    'U': ChangeType.UNMERGED
                }
                
                change_type = change_type_map.get(status[0], ChangeType.MODIFIED)
                file_path = Path(self.repository_root / file_path)
                
                changes.append(FileChange(
                    path=file_path,
                    change_type=change_type
                ))
        
        return changes
    
    def _parse_git_status_output(self, output: str) -> List[FileChange]:
        """Parse git status --porcelain output."""
        changes = []
        
        for line in output.strip().split('\n'):
            if not line or len(line) < 3:
                continue
            
            # Git status format: XY filename
            index_status = line[0]
            worktree_status = line[1]
            file_path = line[3:]  # Skip the space
            
            # Determine change type based on status
            if index_status in 'AM' or worktree_status in 'AM':
                if index_status == 'A' or worktree_status == 'A':
                    change_type = ChangeType.ADDED
                else:
                    change_type = ChangeType.MODIFIED
            elif index_status == 'D' or worktree_status == 'D':
                change_type = ChangeType.DELETED
            elif index_status == 'R' or worktree_status == 'R':
                change_type = ChangeType.RENAMED
            else:
                change_type = ChangeType.MODIFIED
            
            file_path = Path(self.repository_root / file_path)
            changes.append(FileChange(
                path=file_path,
                change_type=change_type
            ))
        
        return changes


class FastModeProcessor:
    """Processes analysis in fast mode for pre-commit optimization."""
    
    def __init__(self, git_detector: Optional[GitChangeDetector] = None):
        """Initialize fast mode processor."""
        self.git_detector = git_detector or GitChangeDetector()
        self.performance_target = 10.0  # 10 seconds for typical commits
        
    async def get_files_to_analyze(self, mode: str = "staged") -> List[Path]:
        """Get list of files to analyze in fast mode."""
        start_time = time.time()
        
        if mode == "staged":
            changes = await self.git_detector.get_staged_files()
        elif mode == "modified":
            changes = await self.git_detector.get_modified_files(include_untracked=True)
        else:
            logger.warning(f"Unknown fast mode: {mode}, using staged")
            changes = await self.git_detector.get_staged_files()
        
        # Filter to files that should be analyzed
        files_to_analyze = [
            change.path for change in changes 
            if change.should_analyze
        ]
        
        detection_time = time.time() - start_time
        logger.info(
            f"Fast mode detected {len(files_to_analyze)} files to analyze "
            f"from {len(changes)} changes in {detection_time:.3f}s"
        )
        
        return files_to_analyze
    
    async def should_use_fast_mode(self, target_path: Path) -> bool:
        """Determine if fast mode should be used for the given target."""
        # Fast mode is only useful in Git repositories
        if not self.git_detector.repository_root:
            return False
        
        # Check if target is within the Git repository
        try:
            target_path.resolve().relative_to(self.git_detector.repository_root)
            return True
        except ValueError:
            # Target is outside the Git repository
            return False
    
    def estimate_performance_benefit(self, total_files: int, changed_files: int) -> Dict[str, Any]:
        """Estimate performance benefit of using fast mode."""
        if total_files == 0:
            return {"benefit": 0, "estimated_time_saved": 0}
        
        reduction_ratio = 1 - (changed_files / total_files)
        estimated_time_saved = reduction_ratio * 100  # Percentage
        
        return {
            "total_files": total_files,
            "changed_files": changed_files,
            "reduction_ratio": reduction_ratio,
            "estimated_time_saved_percent": estimated_time_saved,
            "performance_benefit": "high" if reduction_ratio > 0.7 else "medium" if reduction_ratio > 0.3 else "low"
        }
    
    def get_fast_mode_summary(self, files_analyzed: List[Path], total_time: float) -> Dict[str, Any]:
        """Generate summary of fast mode execution."""
        return {
            "mode": "fast",
            "files_analyzed": len(files_analyzed),
            "execution_time": total_time,
            "performance_target_met": total_time <= self.performance_target,
            "average_time_per_file": total_time / len(files_analyzed) if files_analyzed else 0,
            "git_repository": str(self.git_detector.repository_root) if self.git_detector.repository_root else None
        }


async def main():
    """Test fast mode functionality."""
    detector = GitChangeDetector()
    processor = FastModeProcessor(detector)
    
    print("🚀 Testing Fast Mode Functionality")
    print("=" * 50)
    
    # Test Git repository detection
    print(f"Git repository: {detector.repository_root}")
    
    # Test staged files detection
    staged_files = await detector.get_staged_files()
    print(f"Staged files: {len(staged_files)}")
    for change in staged_files[:5]:  # Show first 5
        print(f"  {change.change_type.value}: {change.path}")
    
    # Test modified files detection
    modified_files = await detector.get_modified_files()
    print(f"Modified files: {len(modified_files)}")
    for change in modified_files[:5]:  # Show first 5
        print(f"  {change.change_type.value}: {change.path}")
    
    # Test fast mode file selection
    files_to_analyze = await processor.get_files_to_analyze("staged")
    print(f"Files to analyze in fast mode: {len(files_to_analyze)}")
    
    print("\n✅ Fast mode functionality test completed!")


if __name__ == "__main__":
    asyncio.run(main())
