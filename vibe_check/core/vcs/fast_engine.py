"""
Fast Analysis Engine
===================

Optimized analysis engine for pre-commit usage focusing on changed files
only with target execution time of <30 seconds.
"""

import asyncio
import time
import logging
from pathlib import Path
from typing import List, Optional, Dict, Any, Set
from dataclasses import dataclass, field
from enum import Enum

from .git_integration import G<PERSON><PERSON><PERSON>eDete<PERSON>, FileChange, get_git_detector
from .models import AnalysisTarget, AnalysisContext, AnalysisResult, EngineMode
from .registry import AnalysisRule

logger = logging.getLogger(__name__)


class FastModeRuleCategory(Enum):
    """Rule categories optimized for fast execution."""
    
    CRITICAL = "critical"      # Security, syntax errors (always run)
    FAST = "fast"             # Quick style checks (<100ms)
    STANDARD = "standard"     # Normal rules (100-500ms)
    SLOW = "slow"            # Complex analysis (>500ms, skip in fast mode)


@dataclass
class FastModeConfig:
    """Configuration for fast execution mode."""
    
    # Performance settings
    max_execution_time_seconds: float = 30.0
    max_files_to_analyze: int = 50
    parallel_workers: int = 4
    
    # Rule selection
    critical_rules_only: bool = False
    max_rules_per_file: int = 10
    rule_timeout_seconds: float = 5.0
    
    # Git integration
    base_reference: str = "HEAD"
    analyze_staged_only: bool = True
    include_untracked_files: bool = False
    
    # Caching
    cache_enabled: bool = True
    cache_ttl_hours: int = 24
    max_cache_size_mb: int = 100
    
    # Early termination
    fail_fast: bool = False
    max_issues_per_file: int = 5
    stop_on_critical_issue: bool = True


@dataclass
class FastAnalysisResult:
    """Result of fast analysis execution."""
    
    # Analysis results
    total_files_analyzed: int = 0
    total_issues_found: int = 0
    critical_issues_found: int = 0
    
    # Performance metrics
    execution_time_seconds: float = 0.0
    cache_hit_rate: float = 0.0
    files_per_second: float = 0.0
    
    # File-specific results
    file_results: Dict[str, AnalysisResult] = field(default_factory=dict)
    
    # Git information
    analyzed_changes: List[FileChange] = field(default_factory=list)
    base_reference: str = "HEAD"
    
    # Optimization info
    skipped_files: List[str] = field(default_factory=list)
    skipped_rules: List[str] = field(default_factory=list)
    
    @property
    def success(self) -> bool:
        """Check if analysis completed successfully within time budget."""
        return (self.execution_time_seconds <= 30.0 and 
                self.critical_issues_found == 0)


class RulePrioritizer:
    """Prioritizes rules for fast execution mode."""
    
    def __init__(self):
        """Initialize rule prioritizer."""
        self.rule_performance_cache: Dict[str, float] = {}
        self.rule_categories: Dict[str, FastModeRuleCategory] = {}
    
    def categorize_rule(self, rule: AnalysisRule) -> FastModeRuleCategory:
        """Categorize rule based on performance and importance."""
        # Check cache first
        if rule.rule_id in self.rule_categories:
            return self.rule_categories[rule.rule_id]
        
        # Categorize based on rule properties
        category = FastModeRuleCategory.STANDARD
        
        # Critical rules (security, syntax)
        if hasattr(rule, 'category') and rule.category in ['security', 'syntax']:
            category = FastModeRuleCategory.CRITICAL
        elif rule.rule_id.startswith('SEC') or rule.rule_id.startswith('SYN'):
            category = FastModeRuleCategory.CRITICAL
        
        # Fast rules (simple style checks)
        elif rule.rule_id.startswith('S00') or rule.rule_id in ['S001', 'S002', 'S003']:
            category = FastModeRuleCategory.FAST
        
        # Slow rules (complex analysis)
        elif rule.rule_id.startswith('C00') or rule.rule_id.startswith('T00'):
            # Check if we have performance data
            avg_time = self.rule_performance_cache.get(rule.rule_id, 0)
            if avg_time > 500:  # >500ms
                category = FastModeRuleCategory.SLOW
        
        # Cache the categorization
        self.rule_categories[rule.rule_id] = category
        return category
    
    def get_rules_for_fast_mode(self, all_rules: List[AnalysisRule], config: FastModeConfig) -> List[AnalysisRule]:
        """Get prioritized rules for fast execution mode."""
        prioritized_rules = []
        
        # Always include critical rules
        critical_rules = [rule for rule in all_rules 
                         if self.categorize_rule(rule) == FastModeRuleCategory.CRITICAL]
        prioritized_rules.extend(critical_rules)
        
        if config.critical_rules_only:
            return prioritized_rules
        
        # Add fast rules
        fast_rules = [rule for rule in all_rules 
                     if self.categorize_rule(rule) == FastModeRuleCategory.FAST]
        prioritized_rules.extend(fast_rules)
        
        # Add standard rules if we have time budget
        if len(prioritized_rules) < config.max_rules_per_file:
            standard_rules = [rule for rule in all_rules 
                            if self.categorize_rule(rule) == FastModeRuleCategory.STANDARD]
            
            # Sort by estimated execution time
            standard_rules.sort(key=lambda r: self.rule_performance_cache.get(r.rule_id, 100))
            
            remaining_slots = config.max_rules_per_file - len(prioritized_rules)
            prioritized_rules.extend(standard_rules[:remaining_slots])
        
        logger.info(f"Selected {len(prioritized_rules)} rules for fast mode "
                   f"({len(critical_rules)} critical, {len(fast_rules)} fast)")
        
        return prioritized_rules
    
    def estimate_execution_time(self, rules: List[AnalysisRule], file_count: int) -> float:
        """Estimate total execution time for rules and files."""
        total_time = 0.0
        
        for rule in rules:
            # Get cached performance or use default estimates
            rule_time = self.rule_performance_cache.get(rule.rule_id, 100)  # 100ms default
            total_time += rule_time * file_count
        
        # Add overhead for Git operations, caching, etc.
        overhead = 2.0  # 2 seconds overhead
        
        return (total_time / 1000.0) + overhead  # Convert ms to seconds


class FastCacheManager:
    """Optimized caching for fast execution mode."""
    
    def __init__(self, config: FastModeConfig):
        """Initialize fast cache manager."""
        self.config = config
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.cache_hits = 0
        self.cache_misses = 0
    
    def get_cache_key(self, file_path: Path, rule_ids: List[str]) -> str:
        """Generate cache key including file hash and rule versions."""
        try:
            # Get file modification time and size for quick cache key
            stat = file_path.stat()
            file_signature = f"{stat.st_mtime}_{stat.st_size}"
            
            # Include rule IDs in cache key
            rules_signature = "_".join(sorted(rule_ids))
            
            return f"{file_path}_{file_signature}_{rules_signature}"
            
        except Exception:
            # Fallback to simple path-based key
            return f"{file_path}_{'_'.join(sorted(rule_ids))}"
    
    def get_cached_result(self, file_path: Path, rule_ids: List[str]) -> Optional[AnalysisResult]:
        """Get cached analysis result for file."""
        if not self.config.cache_enabled:
            return None
        
        cache_key = self.get_cache_key(file_path, rule_ids)
        
        if cache_key in self.cache:
            cached_data = self.cache[cache_key]
            
            # Check if cache is still valid (TTL)
            cache_age_hours = (time.time() - cached_data['timestamp']) / 3600
            if cache_age_hours <= self.config.cache_ttl_hours:
                self.cache_hits += 1
                logger.debug(f"Cache hit for {file_path.name}")
                return cached_data['result']
            else:
                # Remove stale cache entry
                del self.cache[cache_key]
        
        self.cache_misses += 1
        logger.debug(f"Cache miss for {file_path.name}")
        return None
    
    def cache_result(self, file_path: Path, rule_ids: List[str], result: AnalysisResult) -> None:
        """Cache analysis result for future use."""
        if not self.config.cache_enabled:
            return
        
        cache_key = self.get_cache_key(file_path, rule_ids)
        
        self.cache[cache_key] = {
            'result': result,
            'timestamp': time.time(),
            'file_path': str(file_path),
            'rule_ids': rule_ids
        }
        
        logger.debug(f"Cached result for {file_path.name}")
    
    def get_cache_hit_rate(self) -> float:
        """Get cache hit rate for performance monitoring."""
        total_requests = self.cache_hits + self.cache_misses
        if total_requests == 0:
            return 0.0
        return self.cache_hits / total_requests


class FastAnalysisEngine:
    """
    Optimized analysis engine for pre-commit usage.
    
    Focuses on changed files only with aggressive optimizations
    to meet <30 second execution time target.
    """
    
    def __init__(self, config: Optional[FastModeConfig] = None):
        """Initialize fast analysis engine."""
        self.config = config or FastModeConfig()
        self.git_detector = get_git_detector()
        self.rule_prioritizer = RulePrioritizer()
        self.cache_manager = FastCacheManager(self.config)
        
        logger.info("Fast analysis engine initialized")
    
    async def analyze_staged_files(self, rules: List[AnalysisRule]) -> FastAnalysisResult:
        """
        Analyze staged files for pre-commit hook.
        
        Args:
            rules: Available analysis rules
            
        Returns:
            Fast analysis result
        """
        start_time = time.time()
        
        # Get staged files
        staged_changes = self.git_detector.get_staged_files({'.py'})
        
        if not staged_changes:
            logger.info("No staged Python files found")
            return FastAnalysisResult(
                execution_time_seconds=time.time() - start_time,
                base_reference="staged"
            )
        
        # Filter and prioritize files
        analyzable_files = self._filter_analyzable_files(staged_changes)
        
        # Analyze files
        result = await self._analyze_files_fast(analyzable_files, rules)
        result.execution_time_seconds = time.time() - start_time
        result.analyzed_changes = staged_changes
        result.base_reference = "staged"
        
        logger.info(f"Fast analysis completed in {result.execution_time_seconds:.2f}s")
        return result
    
    async def analyze_changes(self, rules: List[AnalysisRule], base_ref: str = "HEAD") -> FastAnalysisResult:
        """
        Analyze changed files compared to base reference.
        
        Args:
            rules: Available analysis rules
            base_ref: Base reference to compare against
            
        Returns:
            Fast analysis result
        """
        start_time = time.time()
        
        # Get changed files
        changed_files = self.git_detector.get_modified_files(base_ref, {'.py'})
        
        if not changed_files:
            logger.info(f"No changed Python files found compared to {base_ref}")
            return FastAnalysisResult(
                execution_time_seconds=time.time() - start_time,
                base_reference=base_ref
            )
        
        # Filter and prioritize files
        analyzable_files = self._filter_analyzable_files(changed_files)
        
        # Analyze files
        result = await self._analyze_files_fast(analyzable_files, rules)
        result.execution_time_seconds = time.time() - start_time
        result.analyzed_changes = changed_files
        result.base_reference = base_ref
        
        logger.info(f"Fast analysis completed in {result.execution_time_seconds:.2f}s")
        return result
    
    def _filter_analyzable_files(self, file_changes: List[FileChange]) -> List[FileChange]:
        """Filter files that should be analyzed in fast mode."""
        analyzable = []
        
        for change in file_changes:
            # Skip deleted files
            if change.change_type.value == 'D':
                continue
            
            # Skip files that don't exist
            if not change.path.exists():
                continue
            
            # Skip very large files (>1MB)
            try:
                file_size = change.path.stat().st_size
                if file_size > 1024 * 1024:  # 1MB
                    logger.warning(f"Skipping large file: {change.path} ({file_size} bytes)")
                    continue
            except Exception:
                continue
            
            # Skip binary files
            if change.is_binary:
                continue
            
            analyzable.append(change)
        
        # Limit number of files if too many
        if len(analyzable) > self.config.max_files_to_analyze:
            logger.warning(f"Too many files ({len(analyzable)}), limiting to {self.config.max_files_to_analyze}")
            analyzable = analyzable[:self.config.max_files_to_analyze]
        
        return analyzable
    
    async def _analyze_files_fast(self, file_changes: List[FileChange], rules: List[AnalysisRule]) -> FastAnalysisResult:
        """Analyze files with fast mode optimizations."""
        result = FastAnalysisResult()
        
        # Get prioritized rules for fast mode
        fast_rules = self.rule_prioritizer.get_rules_for_fast_mode(rules, self.config)
        rule_ids = [rule.rule_id for rule in fast_rules]
        
        # Check time budget
        estimated_time = self.rule_prioritizer.estimate_execution_time(fast_rules, len(file_changes))
        if estimated_time > self.config.max_execution_time_seconds:
            logger.warning(f"Estimated time ({estimated_time:.1f}s) exceeds budget ({self.config.max_execution_time_seconds}s)")
            
            # Reduce to critical rules only
            fast_rules = [rule for rule in fast_rules 
                         if self.rule_prioritizer.categorize_rule(rule) == FastModeRuleCategory.CRITICAL]
            logger.info(f"Reduced to {len(fast_rules)} critical rules")
        
        # Analyze files in parallel
        semaphore = asyncio.Semaphore(self.config.parallel_workers)
        tasks = []
        
        for change in file_changes:
            task = self._analyze_single_file_fast(change, fast_rules, semaphore)
            tasks.append(task)
        
        # Execute with timeout
        try:
            file_results = await asyncio.wait_for(
                asyncio.gather(*tasks, return_exceptions=True),
                timeout=self.config.max_execution_time_seconds
            )
            
            # Process results
            for i, file_result in enumerate(file_results):
                if isinstance(file_result, Exception):
                    logger.error(f"Error analyzing {file_changes[i].path}: {file_result}")
                    continue
                
                if file_result:
                    result.file_results[str(file_changes[i].path)] = file_result
                    result.total_issues_found += len(file_result.issues)
                    
                    # Count critical issues
                    critical_count = sum(1 for issue in file_result.issues 
                                       if hasattr(issue, 'severity') and issue.severity == 'error')
                    result.critical_issues_found += critical_count
            
            result.total_files_analyzed = len([r for r in file_results if not isinstance(r, Exception)])
            
        except asyncio.TimeoutError:
            logger.warning(f"Analysis timed out after {self.config.max_execution_time_seconds}s")
            result.total_files_analyzed = 0
        
        # Calculate performance metrics
        if result.execution_time_seconds > 0:
            result.files_per_second = result.total_files_analyzed / result.execution_time_seconds
        
        result.cache_hit_rate = self.cache_manager.get_cache_hit_rate()
        
        return result
    
    async def _analyze_single_file_fast(self, change: FileChange, rules: List[AnalysisRule], semaphore: asyncio.Semaphore) -> Optional[AnalysisResult]:
        """Analyze single file with fast mode optimizations."""
        async with semaphore:
            try:
                rule_ids = [rule.rule_id for rule in rules]
                
                # Check cache first
                cached_result = self.cache_manager.get_cached_result(change.path, rule_ids)
                if cached_result:
                    return cached_result
                
                # Create analysis target
                target = AnalysisTarget.from_file(change.path)
                context = AnalysisContext(mode=EngineMode.STANDALONE)
                
                # Run rules with timeout
                result = AnalysisResult(target=target)
                
                for rule in rules:
                    try:
                        rule_start = time.time()
                        
                        # Get file content and AST
                        content = target.get_content()
                        ast_tree = target.get_ast()
                        
                        # Run rule with timeout
                        issues = await asyncio.wait_for(
                            rule.analyze(target, content, ast_tree, context),
                            timeout=self.config.rule_timeout_seconds
                        )
                        
                        result.issues.extend(issues)
                        result.rules_executed.append(rule.rule_id)
                        
                        # Track rule performance
                        rule_time = (time.time() - rule_start) * 1000  # Convert to ms
                        self.rule_prioritizer.rule_performance_cache[rule.rule_id] = rule_time
                        
                        # Early termination if too many issues
                        if len(result.issues) >= self.config.max_issues_per_file:
                            logger.debug(f"Early termination for {change.path}: too many issues")
                            break
                        
                        # Stop on critical issue if configured
                        if self.config.stop_on_critical_issue:
                            critical_issues = [issue for issue in issues 
                                             if hasattr(issue, 'severity') and issue.severity == 'error']
                            if critical_issues:
                                logger.debug(f"Early termination for {change.path}: critical issue found")
                                break
                        
                    except asyncio.TimeoutError:
                        logger.warning(f"Rule {rule.rule_id} timed out for {change.path}")
                        continue
                    except Exception as e:
                        logger.error(f"Error running rule {rule.rule_id} on {change.path}: {e}")
                        continue
                
                # Cache the result
                self.cache_manager.cache_result(change.path, rule_ids, result)
                
                return result
                
            except Exception as e:
                logger.error(f"Error analyzing {change.path}: {e}")
                return None
