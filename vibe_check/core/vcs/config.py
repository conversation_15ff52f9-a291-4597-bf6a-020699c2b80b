"""
VCS Configuration Management
============================

This module provides configuration management for the VCS engine,
supporting multiple configuration sources and inheritance.
"""

import os
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Union
import yaml

from vibe_check.core.vcs.models import EngineMode, RuleCategory
from vibe_check.core.constants import AnalysisThresholds


@dataclass
class VCSConfig:
    """VCS engine configuration."""
    
    # Engine settings
    mode: EngineMode = EngineMode.INTEGRATED
    cache_enabled: bool = True
    cache_dir: Optional[Path] = None
    performance_mode: bool = False
    auto_fix_enabled: bool = False
    use_shared_ast_traversal: bool = True  # Enable shared AST traversal for better performance

    # File I/O optimization settings
    file_cache_size: int = 100  # Maximum number of files to cache
    file_cache_memory_mb: float = 512.0  # Maximum memory usage for file cache in MB
    
    # Rule settings
    enabled_categories: Set[RuleCategory] = field(default_factory=lambda: set(RuleCategory))
    enabled_rules: Set[str] = field(default_factory=set)
    disabled_rules: Set[str] = field(default_factory=set)
    rule_config: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    # Analysis settings
    max_line_length: int = AnalysisThresholds.MAX_LINE_LENGTH
    complexity_threshold: int = AnalysisThresholds.CYCLOMATIC_COMPLEXITY
    cognitive_complexity_threshold: int = AnalysisThresholds.COGNITIVE_COMPLEXITY
    documentation_threshold: float = AnalysisThresholds.MIN_DOCUMENTATION_COVERAGE
    
    # Performance settings
    parallel_analysis: bool = True
    max_workers: Optional[int] = None
    timeout_seconds: float = 300.0
    memory_limit_mb: Optional[int] = None
    
    # Output settings
    output_format: str = "json"
    include_metrics: bool = True
    include_suggestions: bool = True
    verbose_output: bool = False
    
    # Integration settings
    integrate_with_external_tools: bool = True
    external_tool_coordination: bool = True
    meta_analysis_enabled: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "mode": self.mode.value,
            "cache_enabled": self.cache_enabled,
            "cache_dir": str(self.cache_dir) if self.cache_dir else None,
            "performance_mode": self.performance_mode,
            "auto_fix_enabled": self.auto_fix_enabled,
            "enabled_categories": [cat.value for cat in self.enabled_categories],
            "enabled_rules": list(self.enabled_rules),
            "disabled_rules": list(self.disabled_rules),
            "rule_config": self.rule_config,
            "max_line_length": self.max_line_length,
            "complexity_threshold": self.complexity_threshold,
            "cognitive_complexity_threshold": self.cognitive_complexity_threshold,
            "documentation_threshold": self.documentation_threshold,
            "parallel_analysis": self.parallel_analysis,
            "max_workers": self.max_workers,
            "timeout_seconds": self.timeout_seconds,
            "memory_limit_mb": self.memory_limit_mb,
            "output_format": self.output_format,
            "include_metrics": self.include_metrics,
            "include_suggestions": self.include_suggestions,
            "verbose_output": self.verbose_output,
            "integrate_with_external_tools": self.integrate_with_external_tools,
            "external_tool_coordination": self.external_tool_coordination,
            "meta_analysis_enabled": self.meta_analysis_enabled
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "VCSConfig":
        """Create config from dictionary."""
        config = cls()
        
        # Engine settings
        if "mode" in data:
            config.mode = EngineMode(data["mode"])
        if "cache_enabled" in data:
            config.cache_enabled = data["cache_enabled"]
        if "cache_dir" in data and data["cache_dir"]:
            config.cache_dir = Path(data["cache_dir"])
        if "performance_mode" in data:
            config.performance_mode = data["performance_mode"]
        if "auto_fix_enabled" in data:
            config.auto_fix_enabled = data["auto_fix_enabled"]
        
        # Rule settings
        if "enabled_categories" in data:
            config.enabled_categories = {RuleCategory(cat) for cat in data["enabled_categories"]}
        if "enabled_rules" in data:
            config.enabled_rules = set(data["enabled_rules"])
        if "disabled_rules" in data:
            config.disabled_rules = set(data["disabled_rules"])
        if "rule_config" in data:
            config.rule_config = data["rule_config"]
        
        # Analysis settings
        for key in ["max_line_length", "complexity_threshold", "cognitive_complexity_threshold", "documentation_threshold"]:
            if key in data:
                setattr(config, key, data[key])
        
        # Performance settings
        for key in ["parallel_analysis", "max_workers", "timeout_seconds", "memory_limit_mb"]:
            if key in data:
                setattr(config, key, data[key])
        
        # Output settings
        for key in ["output_format", "include_metrics", "include_suggestions", "verbose_output"]:
            if key in data:
                setattr(config, key, data[key])
        
        # Integration settings
        for key in ["integrate_with_external_tools", "external_tool_coordination", "meta_analysis_enabled"]:
            if key in data:
                setattr(config, key, data[key])
        
        return config


class VCSConfigManager:
    """Manages VCS configuration from multiple sources."""
    
    def __init__(self):
        """Initialize the config manager."""
        self._config_cache: Optional[VCSConfig] = None
        self._config_sources: List[Path] = []
    
    def load_config(self, 
                   config_path: Optional[Union[str, Path]] = None,
                   cli_overrides: Optional[Dict[str, Any]] = None,
                   mode: Optional[EngineMode] = None) -> VCSConfig:
        """
        Load configuration from multiple sources with inheritance.
        
        Priority order (highest to lowest):
        1. CLI arguments/overrides
        2. Explicit config file
        3. Project config (.vibe-check.yaml)
        4. User config (~/.vibe-check/config.yaml)
        5. Environment variables (VCS_*)
        6. Default configuration
        """
        # Start with default config
        config = VCSConfig()
        
        # Apply mode if specified
        if mode:
            config.mode = mode
        
        # Load from environment variables
        env_config = self._load_from_environment()
        config = self._merge_configs(config, env_config)
        
        # Load from user config
        user_config_path = Path.home() / ".vibe-check" / "config.yaml"
        if user_config_path.exists():
            user_config = self._load_from_file(user_config_path)
            config = self._merge_configs(config, user_config)
        
        # Load from project config
        project_config_path = Path.cwd() / ".vibe-check.yaml"
        if project_config_path.exists():
            project_config = self._load_from_file(project_config_path)
            config = self._merge_configs(config, project_config)
        
        # Load from explicit config file
        if config_path:
            explicit_config = self._load_from_file(Path(config_path))
            config = self._merge_configs(config, explicit_config)
        
        # Apply CLI overrides
        if cli_overrides:
            cli_config = VCSConfig.from_dict(cli_overrides)
            config = self._merge_configs(config, cli_config)
        
        # Set default cache directory if not specified
        if not config.cache_dir:
            config.cache_dir = Path.home() / ".vibe-check" / "cache"
        
        self._config_cache = config
        return config
    
    def _load_from_file(self, config_path: Path) -> VCSConfig:
        """Load configuration from YAML file."""
        try:
            with open(config_path, 'r') as f:
                data = yaml.safe_load(f) or {}
            
            # Extract VCS-specific config
            vcs_data = data.get("vcs", data)
            return VCSConfig.from_dict(vcs_data)
        
        except Exception as e:
            # Log warning but don't fail
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Failed to load config from {config_path}: {e}")
            return VCSConfig()
    
    def _load_from_environment(self) -> VCSConfig:
        """Load configuration from environment variables."""
        env_data = {}
        
        # Map environment variables to config keys
        env_mapping = {
            "VCS_MODE": "mode",
            "VCS_CACHE_ENABLED": "cache_enabled",
            "VCS_CACHE_DIR": "cache_dir",
            "VCS_PERFORMANCE_MODE": "performance_mode",
            "VCS_AUTO_FIX": "auto_fix_enabled",
            "VCS_MAX_LINE_LENGTH": "max_line_length",
            "VCS_COMPLEXITY_THRESHOLD": "complexity_threshold",
            "VCS_PARALLEL": "parallel_analysis",
            "VCS_MAX_WORKERS": "max_workers",
            "VCS_TIMEOUT": "timeout_seconds",
            "VCS_OUTPUT_FORMAT": "output_format",
            "VCS_VERBOSE": "verbose_output"
        }
        
        for env_var, config_key in env_mapping.items():
            value = os.getenv(env_var)
            if value is not None:
                # Convert string values to appropriate types
                if config_key in ["cache_enabled", "performance_mode", "auto_fix_enabled", "parallel_analysis", "verbose_output"]:
                    env_data[config_key] = value.lower() in ("true", "1", "yes", "on")
                elif config_key in ["max_line_length", "complexity_threshold", "max_workers"]:
                    try:
                        env_data[config_key] = int(value)
                    except ValueError:
                        pass
                elif config_key == "timeout_seconds":
                    try:
                        env_data[config_key] = float(value)
                    except ValueError:
                        pass
                else:
                    env_data[config_key] = value
        
        return VCSConfig.from_dict(env_data)
    
    def _merge_configs(self, base: VCSConfig, override: VCSConfig) -> VCSConfig:
        """Merge two configurations, with override taking precedence."""
        # Create a new config based on the base
        merged_dict = base.to_dict()
        override_dict = override.to_dict()
        
        # Merge dictionaries
        for key, value in override_dict.items():
            if value is not None:
                if key in ["enabled_categories", "enabled_rules", "disabled_rules"]:
                    # For sets, use union
                    if key == "enabled_categories":
                        merged_dict[key] = list(set(merged_dict[key]) | set(value))
                    else:
                        merged_dict[key] = list(set(merged_dict[key]) | set(value))
                elif key == "rule_config":
                    # For nested dicts, merge recursively
                    merged_dict[key] = {**merged_dict[key], **value}
                else:
                    merged_dict[key] = value
        
        return VCSConfig.from_dict(merged_dict)


# Alias for backward compatibility
EngineConfig = VCSConfig
