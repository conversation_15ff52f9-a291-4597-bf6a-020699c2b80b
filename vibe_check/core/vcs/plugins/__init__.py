"""
VCS Plugin System
=================

This module provides the plugin architecture for extending VCS analysis capabilities.
"""

from .plugin_manager import PluginManager
from .plugin_interface import PluginInterface, RulePlugin, AnalyzerPlugin
from .plugin_loader import Plugin<PERSON>oader
from .plugin_registry import PluginRegistry

__all__ = [
    'PluginManager',
    'PluginInterface',
    'RulePlugin',
    'AnalyzerPlugin',
    'PluginLoader',
    'PluginRegistry'
]
