"""
File: vibe_check/core/vcs/plugins/plugin_manager.py
Purpose: Plugin manager for loading and managing VCS plugins
Related Files: vibe_check/core/vcs/plugins/plugin_loader.py
Dependencies: asyncio, pathlib, typing
"""

import asyncio
from pathlib import Path
from typing import Optional, List, Dict, Any, Type

from .plugin_interface import (
    PluginInterface, RulePlugin, AnalyzerPlugin,
    FormatterPlugin, IntegrationPlugin
)
from .plugin_loader import PluginLoader
from .plugin_registry import PluginRegistry
from vibe_check.core.vcs.models import AnalysisTarget, AnalysisResult, AnalysisContext
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class PluginManager:
    """Manages VCS plugins including loading, initialization, and lifecycle."""
    
    def __init__(self, plugin_dirs: Optional[List[Path]] = None):
        """
        Initialize plugin manager.
        
        Args:
            plugin_dirs: List of directories to search for plugins
        """
        self.plugin_dirs = plugin_dirs or []
        self.loader = PluginLoader()
        self.registry = PluginRegistry()
        self._initialized = False
        self._plugins: Dict[str, PluginInterface] = {}
        self._plugin_configs: Dict[str, Dict[str, Any]] = {}
    
    async def initialize(self, config: Dict[str, Any] = None) -> None:
        """
        Initialize the plugin manager and load plugins.
        
        Args:
            config: Plugin configuration dictionary
        """
        if self._initialized:
            logger.warning("Plugin manager already initialized")
            return
        
        config = config or {}
        self._plugin_configs = config.get('plugins', {})
        
        try:
            # Discover and load plugins
            await self._discover_plugins()
            
            # Initialize loaded plugins
            await self._initialize_plugins()
            
            self._initialized = True
            logger.info(f"Plugin manager initialized with {len(self._plugins)} plugins")
            
        except Exception as e:
            logger.error(f"Failed to initialize plugin manager: {e}")
            raise
    
    async def _discover_plugins(self) -> None:
        """Discover plugins in configured directories."""
        discovered_plugins = []
        
        # Discover from plugin directories
        for plugin_dir in self.plugin_dirs:
            if plugin_dir.exists():
                plugins = await self.loader.discover_plugins(plugin_dir)
                discovered_plugins.extend(plugins)
                logger.debug(f"Discovered {len(plugins)} plugins in {plugin_dir}")
        
        # Load discovered plugins
        for plugin_class in discovered_plugins:
            try:
                plugin_instance = plugin_class()
                plugin_name = plugin_instance.metadata.name
                
                # Check for conflicts
                if plugin_name in self._plugins:
                    logger.warning(f"Plugin name conflict: {plugin_name} - skipping duplicate")
                    continue
                
                self._plugins[plugin_name] = plugin_instance
                self.registry.register_plugin(plugin_instance)
                
                logger.debug(f"Loaded plugin: {plugin_name} v{plugin_instance.metadata.version}")
                
            except Exception as e:
                logger.error(f"Failed to load plugin {plugin_class}: {e}")
    
    async def _initialize_plugins(self) -> None:
        """Initialize all loaded plugins."""
        initialization_tasks = []
        
        for plugin_name, plugin in self._plugins.items():
            if plugin.is_enabled():
                plugin_config = self._plugin_configs.get(plugin_name, {})
                task = self._initialize_single_plugin(plugin, plugin_config)
                initialization_tasks.append(task)
        
        # Initialize plugins concurrently
        if initialization_tasks:
            results = await asyncio.gather(*initialization_tasks, return_exceptions=True)
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    plugin_name = list(self._plugins.keys())[i]
                    logger.error(f"Failed to initialize plugin {plugin_name}: {result}")
    
    async def _initialize_single_plugin(self, plugin: PluginInterface, config: Dict[str, Any]) -> None:
        """Initialize a single plugin."""
        try:
            await plugin._initialize_internal(config)
            logger.debug(f"Initialized plugin: {plugin.metadata.name}")
        except Exception as e:
            logger.error(f"Plugin initialization failed for {plugin.metadata.name}: {e}")
            plugin.disable()
            raise
    
    async def cleanup(self) -> None:
        """Cleanup all plugins."""
        if not self._initialized:
            return
        
        cleanup_tasks = []
        for plugin in self._plugins.values():
            if plugin.is_initialized():
                cleanup_tasks.append(plugin.cleanup())
        
        if cleanup_tasks:
            await asyncio.gather(*cleanup_tasks, return_exceptions=True)
        
        self._plugins.clear()
        self.registry.clear()
        self._initialized = False
        
        logger.info("Plugin manager cleaned up")
    
    def get_plugin(self, name: str) -> Optional[PluginInterface]:
        """Get a plugin by name."""
        return self._plugins.get(name)
    
    def get_plugins_by_type(self, plugin_type: Type[PluginInterface]) -> List[PluginInterface]:
        """Get all plugins of a specific type."""
        return [
            plugin for plugin in self._plugins.values()
            if isinstance(plugin, plugin_type) and plugin.is_enabled()
        ]
    
    def get_rule_plugins(self) -> List[RulePlugin]:
        """Get all rule plugins."""
        return self.get_plugins_by_type(RulePlugin)
    
    def get_analyzer_plugins(self) -> List[AnalyzerPlugin]:
        """Get all analyzer plugins."""
        return self.get_plugins_by_type(AnalyzerPlugin)
    
    def get_formatter_plugins(self) -> List[FormatterPlugin]:
        """Get all formatter plugins."""
        return self.get_plugins_by_type(FormatterPlugin)
    
    def get_integration_plugins(self) -> List[IntegrationPlugin]:
        """Get all integration plugins."""
        return self.get_plugins_by_type(IntegrationPlugin)
    
    async def run_analyzer_plugins(self, target: AnalysisTarget, 
                                 context: AnalysisContext) -> List[AnalysisResult]:
        """Run all enabled analyzer plugins on a target."""
        analyzer_plugins = self.get_analyzer_plugins()
        
        if not analyzer_plugins:
            return []
        
        # Check if target is supported by any analyzer
        supported_analyzers = []
        for analyzer in analyzer_plugins:
            supported_types = analyzer.get_supported_file_types()
            if target.path and target.path.suffix in supported_types:
                supported_analyzers.append(analyzer)
        
        if not supported_analyzers:
            return []
        
        # Run analyzers concurrently
        tasks = [
            analyzer.analyze(target, context)
            for analyzer in supported_analyzers
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out exceptions and return valid results
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                analyzer_name = supported_analyzers[i].metadata.name
                logger.error(f"Analyzer plugin {analyzer_name} failed: {result}")
            else:
                valid_results.append(result)
        
        return valid_results
    
    def format_with_plugin(self, format_name: str, results: List[AnalysisResult]) -> Optional[str]:
        """Format results using a specific formatter plugin."""
        formatter_plugins = self.get_formatter_plugins()
        
        for formatter in formatter_plugins:
            if formatter.get_format_name() == format_name:
                try:
                    return formatter.format_results(results)
                except Exception as e:
                    logger.error(f"Formatter plugin {formatter.metadata.name} failed: {e}")
                    return None
        
        return None
    
    def get_available_formats(self) -> List[str]:
        """Get list of available output formats from plugins."""
        formatter_plugins = self.get_formatter_plugins()
        return [formatter.get_format_name() for formatter in formatter_plugins]
    
    def enable_plugin(self, name: str) -> bool:
        """Enable a plugin by name."""
        plugin = self.get_plugin(name)
        if plugin:
            plugin.enable()
            logger.info(f"Enabled plugin: {name}")
            return True
        return False
    
    def disable_plugin(self, name: str) -> bool:
        """Disable a plugin by name."""
        plugin = self.get_plugin(name)
        if plugin:
            plugin.disable()
            logger.info(f"Disabled plugin: {name}")
            return True
        return False
    
    def get_plugin_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all plugins."""
        status = {}
        
        for name, plugin in self._plugins.items():
            status[name] = {
                "enabled": plugin.is_enabled(),
                "initialized": plugin.is_initialized(),
                "version": plugin.metadata.version,
                "description": plugin.metadata.description,
                "type": type(plugin).__name__
            }
        
        return status
    
    def get_plugin_statistics(self) -> Dict[str, Any]:
        """Get plugin system statistics."""
        total_plugins = len(self._plugins)
        enabled_plugins = sum(1 for p in self._plugins.values() if p.is_enabled())
        initialized_plugins = sum(1 for p in self._plugins.values() if p.is_initialized())
        
        plugin_types = {}
        for plugin in self._plugins.values():
            plugin_type = type(plugin).__name__
            plugin_types[plugin_type] = plugin_types.get(plugin_type, 0) + 1
        
        return {
            "total_plugins": total_plugins,
            "enabled_plugins": enabled_plugins,
            "initialized_plugins": initialized_plugins,
            "plugin_types": plugin_types,
            "plugin_directories": [str(d) for d in self.plugin_dirs]
        }
