"""
File: vibe_check/core/vcs/plugins/plugin_interface.py
Purpose: Plugin interface definitions for VCS extensibility
Related Files: vibe_check/core/vcs/plugins/plugin_manager.py
Dependencies: abc, typing
"""

import ast
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Type
from dataclasses import dataclass

from vibe_check.core.vcs.models import (
    AnalysisTarget, AnalysisResult, AnalysisContext, AnalysisIssue,
    RuleCategory, IssueSeverity
)
from vibe_check.core.vcs.registry import AnalysisRule


@dataclass
class PluginMetadata:
    """Metadata for a plugin."""
    name: str
    version: str
    description: str
    author: str
    license: str
    homepage: Optional[str] = None
    dependencies: List[str] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []


class PluginInterface(ABC):
    """Base interface for all VCS plugins."""
    
    def __init__(self):
        self._metadata: Optional[PluginMetadata] = None
        self._enabled = True
        self._initialized = False
    
    @property
    @abstractmethod
    def metadata(self) -> PluginMetadata:
        """Get plugin metadata."""
        pass
    
    @abstractmethod
    async def initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the plugin with configuration."""
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """Cleanup plugin resources."""
        pass
    
    def is_enabled(self) -> bool:
        """Check if plugin is enabled."""
        return self._enabled
    
    def enable(self) -> None:
        """Enable the plugin."""
        self._enabled = True
    
    def disable(self) -> None:
        """Disable the plugin."""
        self._enabled = False
    
    def is_initialized(self) -> bool:
        """Check if plugin is initialized."""
        return self._initialized
    
    async def _initialize_internal(self, config: Dict[str, Any]) -> None:
        """Internal initialization wrapper."""
        await self.initialize(config)
        self._initialized = True


class RulePlugin(PluginInterface):
    """Plugin interface for custom analysis rules."""
    
    @abstractmethod
    def get_rules(self) -> List[Type[AnalysisRule]]:
        """Get list of analysis rule classes provided by this plugin."""
        pass
    
    @abstractmethod
    def get_rule_categories(self) -> List[RuleCategory]:
        """Get list of rule categories this plugin supports."""
        pass


class AnalyzerPlugin(PluginInterface):
    """Plugin interface for custom analyzers."""
    
    @abstractmethod
    async def analyze(self, target: AnalysisTarget, context: AnalysisContext) -> AnalysisResult:
        """Perform custom analysis on the target."""
        pass
    
    @abstractmethod
    def get_supported_file_types(self) -> List[str]:
        """Get list of file extensions this analyzer supports."""
        pass


class FormatterPlugin(PluginInterface):
    """Plugin interface for custom output formatters."""
    
    @abstractmethod
    def get_format_name(self) -> str:
        """Get the name of the output format."""
        pass
    
    @abstractmethod
    def format_results(self, results: List[AnalysisResult]) -> str:
        """Format analysis results."""
        pass
    
    @abstractmethod
    def get_file_extension(self) -> str:
        """Get the file extension for this format."""
        pass


class IntegrationPlugin(PluginInterface):
    """Plugin interface for external tool integrations."""
    
    @abstractmethod
    def get_tool_name(self) -> str:
        """Get the name of the external tool."""
        pass
    
    @abstractmethod
    async def run_tool(self, target: AnalysisTarget, context: AnalysisContext) -> AnalysisResult:
        """Run the external tool and return results."""
        pass
    
    @abstractmethod
    def is_tool_available(self) -> bool:
        """Check if the external tool is available."""
        pass


# Example plugin implementations for reference

class ExampleRulePlugin(RulePlugin):
    """Example rule plugin implementation."""
    
    @property
    def metadata(self) -> PluginMetadata:
        return PluginMetadata(
            name="example-rules",
            version="1.0.0",
            description="Example custom rules plugin",
            author="VCS Team",
            license="MIT"
        )
    
    async def initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the plugin."""
        pass
    
    async def cleanup(self) -> None:
        """Cleanup plugin resources."""
        pass
    
    def get_rules(self) -> List[Type[AnalysisRule]]:
        """Get custom rules."""
        return [ExampleCustomRule]
    
    def get_rule_categories(self) -> List[RuleCategory]:
        """Get supported categories."""
        return [RuleCategory.STYLE]


class ExampleCustomRule(AnalysisRule):
    """Example custom analysis rule."""
    
    def __init__(self):
        super().__init__(
            rule_id="CUSTOM001",
            category=RuleCategory.STYLE,
            name="Example Custom Rule",
            description="An example custom rule for demonstration",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Analyze target and return issues."""
        issues = []
        
        # Example: Check for TODO comments
        lines = content.split('\n')
        for line_num, line in enumerate(lines, 1):
            if 'TODO' in line.upper():
                issues.append(self.create_issue(
                    line=line_num,
                    column=line.upper().find('TODO'),
                    message="TODO comment found - consider creating a ticket",
                    fix_suggestion="Create a proper issue tracker ticket"
                ))
        
        return issues


class ExampleAnalyzerPlugin(AnalyzerPlugin):
    """Example analyzer plugin implementation."""
    
    @property
    def metadata(self) -> PluginMetadata:
        return PluginMetadata(
            name="example-analyzer",
            version="1.0.0",
            description="Example custom analyzer plugin",
            author="VCS Team",
            license="MIT"
        )
    
    async def initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the plugin."""
        pass
    
    async def cleanup(self) -> None:
        """Cleanup plugin resources."""
        pass
    
    async def analyze(self, target: AnalysisTarget, context: AnalysisContext) -> AnalysisResult:
        """Perform custom analysis."""
        # Example: Simple line count analysis
        content = target.get_content()
        lines = content.split('\n')
        
        issues = []
        if len(lines) > 1000:
            issues.append(AnalysisIssue(
                line=1,
                column=1,
                message=f"File is very long ({len(lines)} lines) - consider splitting",
                severity=IssueSeverity.WARNING,
                rule_id="CUSTOM002",
                category=RuleCategory.COMPLEXITY,
                source="example-analyzer"
            ))
        
        return AnalysisResult(
            target=target,
            issues=issues,
            success=True
        )
    
    def get_supported_file_types(self) -> List[str]:
        """Get supported file types."""
        return ['.py', '.pyx']


class ExampleFormatterPlugin(FormatterPlugin):
    """Example formatter plugin implementation."""
    
    @property
    def metadata(self) -> PluginMetadata:
        return PluginMetadata(
            name="example-formatter",
            version="1.0.0",
            description="Example custom formatter plugin",
            author="VCS Team",
            license="MIT"
        )
    
    async def initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the plugin."""
        pass
    
    async def cleanup(self) -> None:
        """Cleanup plugin resources."""
        pass
    
    def get_format_name(self) -> str:
        """Get format name."""
        return "example"
    
    def format_results(self, results: List[AnalysisResult]) -> str:
        """Format results as example format."""
        output = ["=== Example Format Output ===\n"]
        
        total_issues = sum(len(result.issues) for result in results)
        output.append(f"Total files analyzed: {len(results)}")
        output.append(f"Total issues found: {total_issues}")
        output.append("")
        
        for result in results:
            if result.issues:
                output.append(f"File: {result.target.path}")
                for issue in result.issues:
                    output.append(f"  - {issue.rule_id}: {issue.message}")
                output.append("")
        
        return "\n".join(output)
    
    def get_file_extension(self) -> str:
        """Get file extension."""
        return ".example"
