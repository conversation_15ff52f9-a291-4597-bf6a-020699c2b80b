"""
File: vibe_check/core/vcs/plugins/plugin_registry.py
Purpose: Plugin registry for managing loaded plugins
Related Files: vibe_check/core/vcs/plugins/plugin_manager.py
Dependencies: typing
"""

from collections import defaultdict
from typing import Dict, List, Optional, Set, Type, Any

from .plugin_interface import (
    PluginInterface, RulePlugin, AnalyzerPlugin,
    FormatterPlugin, IntegrationPlugin
)
from vibe_check.core.vcs.models import RuleCategory
from vibe_check.core.vcs.registry import AnalysisRule
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class PluginRegistry:
    """Registry for managing and organizing loaded plugins."""
    
    def __init__(self):
        self._plugins: Dict[str, PluginInterface] = {}
        self._plugins_by_type: Dict[Type, List[PluginInterface]] = defaultdict(list)
        self._rule_plugins: List[RulePlugin] = []
        self._analyzer_plugins: List[AnalyzerPlugin] = []
        self._formatter_plugins: List[FormatterPlugin] = []
        self._integration_plugins: List[IntegrationPlugin] = []
        self._plugin_rules: Dict[str, List[Type[AnalysisRule]]] = {}
        self._format_registry: Dict[str, FormatterPlugin] = {}
    
    def register_plugin(self, plugin: PluginInterface) -> bool:
        """
        Register a plugin in the registry.
        
        Args:
            plugin: Plugin instance to register
            
        Returns:
            True if registration successful, False otherwise
        """
        plugin_name = plugin.metadata.name
        
        # Check for name conflicts
        if plugin_name in self._plugins:
            logger.warning(f"Plugin name conflict: {plugin_name} already registered")
            return False
        
        try:
            # Register in main registry
            self._plugins[plugin_name] = plugin
            
            # Register by type
            plugin_type = type(plugin)
            self._plugins_by_type[plugin_type].append(plugin)
            
            # Register in specific type registries
            if isinstance(plugin, RulePlugin):
                self._register_rule_plugin(plugin)
            
            if isinstance(plugin, AnalyzerPlugin):
                self._register_analyzer_plugin(plugin)
            
            if isinstance(plugin, FormatterPlugin):
                self._register_formatter_plugin(plugin)
            
            if isinstance(plugin, IntegrationPlugin):
                self._register_integration_plugin(plugin)
            
            logger.debug(f"Registered plugin: {plugin_name} ({plugin_type.__name__})")
            return True
            
        except Exception as e:
            logger.error(f"Failed to register plugin {plugin_name}: {e}")
            return False
    
    def _register_rule_plugin(self, plugin: RulePlugin) -> None:
        """Register a rule plugin."""
        self._rule_plugins.append(plugin)
        
        try:
            # Get rules from plugin
            rules = plugin.get_rules()
            self._plugin_rules[plugin.metadata.name] = rules
            
            logger.debug(f"Registered {len(rules)} rules from plugin {plugin.metadata.name}")
            
        except Exception as e:
            logger.error(f"Failed to register rules from plugin {plugin.metadata.name}: {e}")
    
    def _register_analyzer_plugin(self, plugin: AnalyzerPlugin) -> None:
        """Register an analyzer plugin."""
        self._analyzer_plugins.append(plugin)
        
        try:
            supported_types = plugin.get_supported_file_types()
            logger.debug(f"Registered analyzer plugin {plugin.metadata.name} "
                        f"for file types: {supported_types}")
            
        except Exception as e:
            logger.error(f"Failed to register analyzer plugin {plugin.metadata.name}: {e}")
    
    def _register_formatter_plugin(self, plugin: FormatterPlugin) -> None:
        """Register a formatter plugin."""
        self._formatter_plugins.append(plugin)
        
        try:
            format_name = plugin.get_format_name()
            
            # Check for format name conflicts
            if format_name in self._format_registry:
                logger.warning(f"Format name conflict: {format_name} already registered")
                return
            
            self._format_registry[format_name] = plugin
            
            logger.debug(f"Registered formatter plugin {plugin.metadata.name} "
                        f"for format: {format_name}")
            
        except Exception as e:
            logger.error(f"Failed to register formatter plugin {plugin.metadata.name}: {e}")
    
    def _register_integration_plugin(self, plugin: IntegrationPlugin) -> None:
        """Register an integration plugin."""
        self._integration_plugins.append(plugin)
        
        try:
            tool_name = plugin.get_tool_name()
            is_available = plugin.is_tool_available()
            
            logger.debug(f"Registered integration plugin {plugin.metadata.name} "
                        f"for tool: {tool_name} (available: {is_available})")
            
        except Exception as e:
            logger.error(f"Failed to register integration plugin {plugin.metadata.name}: {e}")
    
    def unregister_plugin(self, plugin_name: str) -> bool:
        """
        Unregister a plugin from the registry.
        
        Args:
            plugin_name: Name of plugin to unregister
            
        Returns:
            True if unregistration successful, False otherwise
        """
        if plugin_name not in self._plugins:
            logger.warning(f"Plugin not found for unregistration: {plugin_name}")
            return False
        
        try:
            plugin = self._plugins[plugin_name]
            
            # Remove from main registry
            del self._plugins[plugin_name]
            
            # Remove from type registry
            plugin_type = type(plugin)
            if plugin_type in self._plugins_by_type:
                self._plugins_by_type[plugin_type].remove(plugin)
            
            # Remove from specific type registries
            if isinstance(plugin, RulePlugin):
                self._rule_plugins.remove(plugin)
                if plugin_name in self._plugin_rules:
                    del self._plugin_rules[plugin_name]
            
            if isinstance(plugin, AnalyzerPlugin):
                self._analyzer_plugins.remove(plugin)
            
            if isinstance(plugin, FormatterPlugin):
                format_name = plugin.get_format_name()
                if format_name in self._format_registry:
                    del self._format_registry[format_name]
                self._formatter_plugins.remove(plugin)
            
            if isinstance(plugin, IntegrationPlugin):
                self._integration_plugins.remove(plugin)
            
            logger.debug(f"Unregistered plugin: {plugin_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to unregister plugin {plugin_name}: {e}")
            return False
    
    def get_plugin(self, name: str) -> Optional[PluginInterface]:
        """Get a plugin by name."""
        return self._plugins.get(name)
    
    def get_all_plugins(self) -> List[PluginInterface]:
        """Get all registered plugins."""
        return list(self._plugins.values())
    
    def get_plugins_by_type(self, plugin_type: Type[PluginInterface]) -> List[PluginInterface]:
        """Get all plugins of a specific type."""
        return self._plugins_by_type.get(plugin_type, [])
    
    def get_rule_plugins(self) -> List[RulePlugin]:
        """Get all rule plugins."""
        return self._rule_plugins.copy()
    
    def get_analyzer_plugins(self) -> List[AnalyzerPlugin]:
        """Get all analyzer plugins."""
        return self._analyzer_plugins.copy()
    
    def get_formatter_plugins(self) -> List[FormatterPlugin]:
        """Get all formatter plugins."""
        return self._formatter_plugins.copy()
    
    def get_integration_plugins(self) -> List[IntegrationPlugin]:
        """Get all integration plugins."""
        return self._integration_plugins.copy()
    
    def get_plugin_rules(self, plugin_name: str) -> List[Type[AnalysisRule]]:
        """Get rules provided by a specific plugin."""
        return self._plugin_rules.get(plugin_name, [])
    
    def get_all_plugin_rules(self) -> List[Type[AnalysisRule]]:
        """Get all rules from all rule plugins."""
        all_rules = []
        for rules in self._plugin_rules.values():
            all_rules.extend(rules)
        return all_rules
    
    def get_formatter_by_name(self, format_name: str) -> Optional[FormatterPlugin]:
        """Get a formatter plugin by format name."""
        return self._format_registry.get(format_name)
    
    def get_available_formats(self) -> List[str]:
        """Get list of available output formats."""
        return list(self._format_registry.keys())
    
    def get_plugins_by_category(self, category: RuleCategory) -> List[RulePlugin]:
        """Get rule plugins that support a specific category."""
        matching_plugins = []
        
        for plugin in self._rule_plugins:
            try:
                if category in plugin.get_rule_categories():
                    matching_plugins.append(plugin)
            except Exception as e:
                logger.warning(f"Error checking categories for plugin {plugin.metadata.name}: {e}")
        
        return matching_plugins
    
    def get_analyzers_for_file_type(self, file_extension: str) -> List[AnalyzerPlugin]:
        """Get analyzer plugins that support a specific file type."""
        matching_analyzers = []
        
        for analyzer in self._analyzer_plugins:
            try:
                if file_extension in analyzer.get_supported_file_types():
                    matching_analyzers.append(analyzer)
            except Exception as e:
                logger.warning(f"Error checking file types for analyzer {analyzer.metadata.name}: {e}")
        
        return matching_analyzers
    
    def get_available_integrations(self) -> Dict[str, bool]:
        """Get available integrations and their availability status."""
        integrations = {}
        
        for integration in self._integration_plugins:
            try:
                tool_name = integration.get_tool_name()
                is_available = integration.is_tool_available()
                integrations[tool_name] = is_available
            except Exception as e:
                logger.warning(f"Error checking integration {integration.metadata.name}: {e}")
        
        return integrations
    
    def get_registry_statistics(self) -> Dict[str, Any]:
        """Get registry statistics."""
        return {
            "total_plugins": len(self._plugins),
            "rule_plugins": len(self._rule_plugins),
            "analyzer_plugins": len(self._analyzer_plugins),
            "formatter_plugins": len(self._formatter_plugins),
            "integration_plugins": len(self._integration_plugins),
            "total_plugin_rules": len(self.get_all_plugin_rules()),
            "available_formats": len(self._format_registry),
            "plugin_names": list(self._plugins.keys())
        }
    
    def clear(self) -> None:
        """Clear all registered plugins."""
        self._plugins.clear()
        self._plugins_by_type.clear()
        self._rule_plugins.clear()
        self._analyzer_plugins.clear()
        self._formatter_plugins.clear()
        self._integration_plugins.clear()
        self._plugin_rules.clear()
        self._format_registry.clear()
        
        logger.debug("Plugin registry cleared")
