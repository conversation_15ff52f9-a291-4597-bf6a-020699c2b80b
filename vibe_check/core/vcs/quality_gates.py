"""
File: vibe_check/core/vcs/quality_gates.py
Purpose: Quality gate system for VCS rule calibration and issue management
Related Files: vibe_check/core/vcs/engine.py, vibe_check/core/vcs/models.py
Dependencies: typing, dataclasses, enum
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, List, Set, Optional, Any
import logging

from .models import AnalysisIssue, IssueSeverity, RuleCategory

logger = logging.getLogger(__name__)


class QualityGateResult(Enum):
    """Quality gate evaluation results."""
    PASS = "pass"
    WARNING = "warning"
    FAIL = "fail"


@dataclass
class QualityGateConfig:
    """Configuration for quality gates."""
    
    # Issue count thresholds per file
    max_errors_per_file: int = 5
    max_warnings_per_file: int = 15
    max_info_per_file: int = 50
    
    # Category-specific thresholds
    max_security_issues: int = 0  # Zero tolerance for security
    max_complexity_issues_per_file: int = 5
    max_style_issues_per_file: int = 25
    
    # Auto-fixable issue handling
    exclude_auto_fixable_from_gates: bool = True
    
    # Rule-specific overrides
    rule_overrides: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    # Severity promotion rules
    promote_security_to_error: bool = True
    promote_complexity_to_warning: bool = True


@dataclass
class QualityGateReport:
    """Quality gate evaluation report."""
    
    result: QualityGateResult
    total_issues: int
    issues_by_severity: Dict[IssueSeverity, int]
    issues_by_category: Dict[RuleCategory, int]
    auto_fixable_issues: int
    critical_issues: List[AnalysisIssue]
    recommendations: List[str]
    gate_violations: List[str]


class QualityGateManager:
    """Manages quality gates for VCS analysis results."""
    
    def __init__(self, config: Optional[QualityGateConfig] = None):
        """Initialize quality gate manager."""
        self.config = config or QualityGateConfig()
        
    def evaluate_file_results(self, issues: List[AnalysisIssue], 
                             file_path: str) -> QualityGateReport:
        """
        Evaluate quality gates for a single file's issues.
        
        Args:
            issues: List of issues found in the file
            file_path: Path to the file being evaluated
            
        Returns:
            Quality gate evaluation report
        """
        # Filter out auto-fixable issues if configured
        filtered_issues = self._filter_issues(issues)
        
        # Categorize issues
        issues_by_severity = self._categorize_by_severity(filtered_issues)
        issues_by_category = self._categorize_by_category(filtered_issues)
        auto_fixable_count = len([i for i in issues if i.auto_fixable])
        
        # Evaluate gates
        violations = []
        result = QualityGateResult.PASS
        
        # Check severity-based gates
        if issues_by_severity.get(IssueSeverity.ERROR, 0) > self.config.max_errors_per_file:
            violations.append(f"Too many errors: {issues_by_severity[IssueSeverity.ERROR]} > {self.config.max_errors_per_file}")
            result = QualityGateResult.FAIL
            
        if issues_by_severity.get(IssueSeverity.WARNING, 0) > self.config.max_warnings_per_file:
            violations.append(f"Too many warnings: {issues_by_severity[IssueSeverity.WARNING]} > {self.config.max_warnings_per_file}")
            if result != QualityGateResult.FAIL:
                result = QualityGateResult.WARNING
                
        if issues_by_severity.get(IssueSeverity.INFO, 0) > self.config.max_info_per_file:
            violations.append(f"Too many info issues: {issues_by_severity[IssueSeverity.INFO]} > {self.config.max_info_per_file}")
            if result == QualityGateResult.PASS:
                result = QualityGateResult.WARNING
        
        # Check category-based gates
        security_issues = issues_by_category.get(RuleCategory.SECURITY, 0)
        if security_issues > self.config.max_security_issues:
            violations.append(f"Security issues found: {security_issues} > {self.config.max_security_issues}")
            result = QualityGateResult.FAIL
            
        complexity_issues = issues_by_category.get(RuleCategory.COMPLEXITY, 0)
        if complexity_issues > self.config.max_complexity_issues_per_file:
            violations.append(f"Too many complexity issues: {complexity_issues} > {self.config.max_complexity_issues_per_file}")
            if result != QualityGateResult.FAIL:
                result = QualityGateResult.WARNING
                
        style_issues = issues_by_category.get(RuleCategory.STYLE, 0)
        if style_issues > self.config.max_style_issues_per_file:
            violations.append(f"Too many style issues: {style_issues} > {self.config.max_style_issues_per_file}")
            if result == QualityGateResult.PASS:
                result = QualityGateResult.WARNING
        
        # Identify critical issues
        critical_issues = [
            issue for issue in filtered_issues 
            if issue.severity in [IssueSeverity.ERROR] or 
               issue.category == RuleCategory.SECURITY
        ]
        
        # Generate recommendations
        recommendations = self._generate_recommendations(
            issues_by_severity, issues_by_category, auto_fixable_count
        )
        
        return QualityGateReport(
            result=result,
            total_issues=len(filtered_issues),
            issues_by_severity=issues_by_severity,
            issues_by_category=issues_by_category,
            auto_fixable_issues=auto_fixable_count,
            critical_issues=critical_issues,
            recommendations=recommendations,
            gate_violations=violations
        )
    
    def _filter_issues(self, issues: List[AnalysisIssue]) -> List[AnalysisIssue]:
        """Filter issues based on configuration."""
        if not self.config.exclude_auto_fixable_from_gates:
            return issues
            
        return [issue for issue in issues if not issue.auto_fixable]
    
    def _categorize_by_severity(self, issues: List[AnalysisIssue]) -> Dict[IssueSeverity, int]:
        """Categorize issues by severity."""
        counts = {}
        for issue in issues:
            severity = issue.severity
            
            # Apply severity promotion rules
            if (issue.category == RuleCategory.SECURITY and 
                self.config.promote_security_to_error):
                severity = IssueSeverity.ERROR
            elif (issue.category == RuleCategory.COMPLEXITY and 
                  self.config.promote_complexity_to_warning):
                severity = IssueSeverity.WARNING
                
            counts[severity] = counts.get(severity, 0) + 1
            
        return counts
    
    def _categorize_by_category(self, issues: List[AnalysisIssue]) -> Dict[RuleCategory, int]:
        """Categorize issues by category."""
        counts = {}
        for issue in issues:
            counts[issue.category] = counts.get(issue.category, 0) + 1
        return counts
    
    def _generate_recommendations(self, 
                                 issues_by_severity: Dict[IssueSeverity, int],
                                 issues_by_category: Dict[RuleCategory, int],
                                 auto_fixable_count: int) -> List[str]:
        """Generate actionable recommendations."""
        recommendations = []
        
        if auto_fixable_count > 0:
            recommendations.append(f"Run with --fix-safe to automatically fix {auto_fixable_count} issues")
            
        if issues_by_category.get(RuleCategory.SECURITY, 0) > 0:
            recommendations.append("Address security issues immediately - they pose real risks")
            
        if issues_by_category.get(RuleCategory.COMPLEXITY, 0) > 5:
            recommendations.append("Consider refactoring complex functions to improve maintainability")
            
        if issues_by_category.get(RuleCategory.STYLE, 0) > 20:
            recommendations.append("Use automated formatting tools (black, isort) to fix style issues")
            
        return recommendations


def create_default_quality_gates() -> QualityGateConfig:
    """Create default quality gate configuration."""
    return QualityGateConfig(
        max_errors_per_file=5,
        max_warnings_per_file=15,
        max_info_per_file=50,
        max_security_issues=0,
        max_complexity_issues_per_file=5,
        max_style_issues_per_file=25,
        exclude_auto_fixable_from_gates=True,
        promote_security_to_error=True,
        promote_complexity_to_warning=True
    )
