"""
File: vibe_check/core/vcs/quality_gates.py
Purpose: Quality gate system for VCS rule calibration and issue management
Related Files: vibe_check/core/vcs/engine.py, vibe_check/core/vcs/models.py
Dependencies: typing, dataclasses, enum
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, List, Set, Optional, Any
import logging
import time
import json
from pathlib import Path

from .models import AnalysisIssue, IssueSeverity, RuleCategory
from .registry import AnalysisRule

logger = logging.getLogger(__name__)


class QualityGateResult(Enum):
    """Quality gate evaluation results."""
    PASS = "pass"
    WARNING = "warning"
    FAIL = "fail"


@dataclass
class QualityGateConfig:
    """Configuration for quality gates."""
    
    # Issue count thresholds per file
    max_errors_per_file: int = 5
    max_warnings_per_file: int = 15
    max_info_per_file: int = 50
    
    # Category-specific thresholds
    max_security_issues: int = 0  # Zero tolerance for security
    max_complexity_issues_per_file: int = 5
    max_style_issues_per_file: int = 25
    
    # Auto-fixable issue handling
    exclude_auto_fixable_from_gates: bool = True
    
    # Rule-specific overrides
    rule_overrides: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    # Severity promotion rules
    promote_security_to_error: bool = True
    promote_complexity_to_warning: bool = True


@dataclass
class QualityGateReport:
    """Quality gate evaluation report."""
    
    result: QualityGateResult
    total_issues: int
    issues_by_severity: Dict[IssueSeverity, int]
    issues_by_category: Dict[RuleCategory, int]
    auto_fixable_issues: int
    critical_issues: List[AnalysisIssue]
    recommendations: List[str]
    gate_violations: List[str]


class QualityGateManager:
    """Manages quality gates for VCS analysis results."""
    
    def __init__(self, config: Optional[QualityGateConfig] = None):
        """Initialize quality gate manager."""
        self.config = config or QualityGateConfig()
        
    def evaluate_file_results(self, issues: List[AnalysisIssue], 
                             file_path: str) -> QualityGateReport:
        """
        Evaluate quality gates for a single file's issues.
        
        Args:
            issues: List of issues found in the file
            file_path: Path to the file being evaluated
            
        Returns:
            Quality gate evaluation report
        """
        # Filter out auto-fixable issues if configured
        filtered_issues = self._filter_issues(issues)
        
        # Categorize issues
        issues_by_severity = self._categorize_by_severity(filtered_issues)
        issues_by_category = self._categorize_by_category(filtered_issues)
        auto_fixable_count = len([i for i in issues if i.auto_fixable])
        
        # Evaluate gates
        violations = []
        result = QualityGateResult.PASS
        
        # Check severity-based gates
        if issues_by_severity.get(IssueSeverity.ERROR, 0) > self.config.max_errors_per_file:
            violations.append(f"Too many errors: {issues_by_severity[IssueSeverity.ERROR]} > {self.config.max_errors_per_file}")
            result = QualityGateResult.FAIL
            
        if issues_by_severity.get(IssueSeverity.WARNING, 0) > self.config.max_warnings_per_file:
            violations.append(f"Too many warnings: {issues_by_severity[IssueSeverity.WARNING]} > {self.config.max_warnings_per_file}")
            if result != QualityGateResult.FAIL:
                result = QualityGateResult.WARNING
                
        if issues_by_severity.get(IssueSeverity.INFO, 0) > self.config.max_info_per_file:
            violations.append(f"Too many info issues: {issues_by_severity[IssueSeverity.INFO]} > {self.config.max_info_per_file}")
            if result == QualityGateResult.PASS:
                result = QualityGateResult.WARNING
        
        # Check category-based gates
        security_issues = issues_by_category.get(RuleCategory.SECURITY, 0)
        if security_issues > self.config.max_security_issues:
            violations.append(f"Security issues found: {security_issues} > {self.config.max_security_issues}")
            result = QualityGateResult.FAIL
            
        complexity_issues = issues_by_category.get(RuleCategory.COMPLEXITY, 0)
        if complexity_issues > self.config.max_complexity_issues_per_file:
            violations.append(f"Too many complexity issues: {complexity_issues} > {self.config.max_complexity_issues_per_file}")
            if result != QualityGateResult.FAIL:
                result = QualityGateResult.WARNING
                
        style_issues = issues_by_category.get(RuleCategory.STYLE, 0)
        if style_issues > self.config.max_style_issues_per_file:
            violations.append(f"Too many style issues: {style_issues} > {self.config.max_style_issues_per_file}")
            if result == QualityGateResult.PASS:
                result = QualityGateResult.WARNING
        
        # Identify critical issues
        critical_issues = [
            issue for issue in filtered_issues 
            if issue.severity in [IssueSeverity.ERROR] or 
               issue.category == RuleCategory.SECURITY
        ]
        
        # Generate recommendations
        recommendations = self._generate_recommendations(
            issues_by_severity, issues_by_category, auto_fixable_count
        )
        
        return QualityGateReport(
            result=result,
            total_issues=len(filtered_issues),
            issues_by_severity=issues_by_severity,
            issues_by_category=issues_by_category,
            auto_fixable_issues=auto_fixable_count,
            critical_issues=critical_issues,
            recommendations=recommendations,
            gate_violations=violations
        )
    
    def _filter_issues(self, issues: List[AnalysisIssue]) -> List[AnalysisIssue]:
        """Filter issues based on configuration."""
        if not self.config.exclude_auto_fixable_from_gates:
            return issues
            
        return [issue for issue in issues if not issue.auto_fixable]
    
    def _categorize_by_severity(self, issues: List[AnalysisIssue]) -> Dict[IssueSeverity, int]:
        """Categorize issues by severity."""
        counts = {}
        for issue in issues:
            severity = issue.severity
            
            # Apply severity promotion rules
            if (issue.category == RuleCategory.SECURITY and 
                self.config.promote_security_to_error):
                severity = IssueSeverity.ERROR
            elif (issue.category == RuleCategory.COMPLEXITY and 
                  self.config.promote_complexity_to_warning):
                severity = IssueSeverity.WARNING
                
            counts[severity] = counts.get(severity, 0) + 1
            
        return counts
    
    def _categorize_by_category(self, issues: List[AnalysisIssue]) -> Dict[RuleCategory, int]:
        """Categorize issues by category."""
        counts = {}
        for issue in issues:
            counts[issue.category] = counts.get(issue.category, 0) + 1
        return counts
    
    def _generate_recommendations(self, 
                                 issues_by_severity: Dict[IssueSeverity, int],
                                 issues_by_category: Dict[RuleCategory, int],
                                 auto_fixable_count: int) -> List[str]:
        """Generate actionable recommendations."""
        recommendations = []
        
        if auto_fixable_count > 0:
            recommendations.append(f"Run with --fix-safe to automatically fix {auto_fixable_count} issues")
            
        if issues_by_category.get(RuleCategory.SECURITY, 0) > 0:
            recommendations.append("Address security issues immediately - they pose real risks")
            
        if issues_by_category.get(RuleCategory.COMPLEXITY, 0) > 5:
            recommendations.append("Consider refactoring complex functions to improve maintainability")
            
        if issues_by_category.get(RuleCategory.STYLE, 0) > 20:
            recommendations.append("Use automated formatting tools (black, isort) to fix style issues")
            
        return recommendations


def create_default_quality_gates() -> QualityGateConfig:
    """Create default quality gate configuration."""
    return QualityGateConfig(
        max_errors_per_file=5,
        max_warnings_per_file=15,
        max_info_per_file=50,
        max_security_issues=0,
        max_complexity_issues_per_file=5,
        max_style_issues_per_file=25,
        exclude_auto_fixable_from_gates=True,
        promote_security_to_error=True,
        promote_complexity_to_warning=True
    )


@dataclass
class RuleValidationMetrics:
    """Metrics for rule validation and quality assessment."""

    rule_id: str
    false_positive_rate: float
    execution_time_ms: float
    memory_usage_mb: float
    test_cases_passed: int
    test_cases_failed: int
    last_validation: float
    validation_status: str  # 'PASSED', 'FAILED', 'PENDING', 'DISABLED'

    # Performance metrics
    avg_execution_time: float = 0.0
    max_execution_time: float = 0.0
    performance_score: float = 100.0

    # Quality metrics
    accuracy_score: float = 100.0
    reliability_score: float = 100.0
    overall_score: float = 100.0


@dataclass
class RuleQualityGateConfig:
    """Configuration for rule-level quality gates."""

    # Thresholds
    max_false_positive_rate: float = 0.10  # 10%
    max_execution_time_ms: float = 1000.0  # 1 second
    max_memory_usage_mb: float = 100.0     # 100MB
    min_test_coverage: float = 0.80        # 80%

    # Validation settings
    validation_interval_hours: int = 24
    require_validation_before_deployment: bool = True
    auto_disable_failing_rules: bool = True

    # Performance thresholds by category
    category_thresholds: Dict[str, float] = field(default_factory=lambda: {
        'style': 50.0,      # 50ms
        'security': 200.0,  # 200ms
        'complexity': 150.0, # 150ms
        'documentation': 100.0, # 100ms
        'imports': 75.0,    # 75ms
        'types': 300.0      # 300ms (more complex analysis)
    })


class RuleQualityGateSystem:
    """
    Quality gate system for individual VCS rules.

    Prevents deployment of rules that don't meet quality standards.
    """

    def __init__(self, config: Optional[RuleQualityGateConfig] = None):
        """Initialize rule quality gate system."""
        self.config = config or RuleQualityGateConfig()
        self.metrics: Dict[str, RuleValidationMetrics] = {}
        self.disabled_rules: Set[str] = set()

        # Load existing metrics
        self._load_metrics()

        logger.info("Rule quality gate system initialized")

    def validate_rule_for_deployment(self, rule: AnalysisRule) -> bool:
        """
        Validate a rule for deployment readiness.

        Args:
            rule: Rule to validate

        Returns:
            True if rule passes quality gates
        """
        # Check if rule is disabled
        if rule.rule_id in self.disabled_rules:
            logger.debug(f"Rule {rule.rule_id} is disabled")
            return False

        # Check if validation is required
        if self.config.require_validation_before_deployment:
            metrics = self.metrics.get(rule.rule_id)

            if not metrics:
                logger.warning(f"Rule {rule.rule_id} has no validation metrics")
                return False

            # Check if validation is recent enough
            hours_since_validation = (time.time() - metrics.last_validation) / 3600
            if hours_since_validation > self.config.validation_interval_hours:
                logger.warning(f"Rule {rule.rule_id} validation is stale ({hours_since_validation:.1f}h old)")
                return False

            # Check validation status
            if metrics.validation_status != 'PASSED':
                logger.debug(f"Rule {rule.rule_id} failed validation: {metrics.validation_status}")
                return False

        return True

    def record_rule_performance(self, rule_id: str, execution_time_ms: float, memory_usage_mb: float = 0.0) -> None:
        """Record performance metrics for a rule."""
        if rule_id not in self.metrics:
            # Create basic metrics if they don't exist
            self.metrics[rule_id] = RuleValidationMetrics(
                rule_id=rule_id,
                false_positive_rate=0.0,
                execution_time_ms=execution_time_ms,
                memory_usage_mb=memory_usage_mb,
                test_cases_passed=0,
                test_cases_failed=0,
                last_validation=time.time(),
                validation_status='PENDING'
            )
        else:
            # Update existing metrics
            metrics = self.metrics[rule_id]
            metrics.execution_time_ms = execution_time_ms
            metrics.memory_usage_mb = memory_usage_mb

            # Update averages
            if metrics.avg_execution_time == 0:
                metrics.avg_execution_time = execution_time_ms
            else:
                metrics.avg_execution_time = (metrics.avg_execution_time + execution_time_ms) / 2

            metrics.max_execution_time = max(metrics.max_execution_time, execution_time_ms)

        # Save updated metrics
        self._save_metrics()

    def get_quality_report(self) -> Dict[str, Any]:
        """Generate rule quality gate report."""
        total_rules = len(self.metrics)
        passed_rules = sum(1 for m in self.metrics.values() if m.validation_status == 'PASSED')
        failed_rules = sum(1 for m in self.metrics.values() if m.validation_status == 'FAILED')
        disabled_rules = len(self.disabled_rules)

        # Calculate average scores
        avg_accuracy = sum(m.accuracy_score for m in self.metrics.values()) / total_rules if total_rules > 0 else 0
        avg_performance = sum(m.performance_score for m in self.metrics.values()) / total_rules if total_rules > 0 else 0
        avg_reliability = sum(m.reliability_score for m in self.metrics.values()) / total_rules if total_rules > 0 else 0
        avg_overall = sum(m.overall_score for m in self.metrics.values()) / total_rules if total_rules > 0 else 0

        # Find problematic rules
        high_fp_rules = [m.rule_id for m in self.metrics.values()
                        if m.false_positive_rate > self.config.max_false_positive_rate]
        slow_rules = [m.rule_id for m in self.metrics.values()
                     if m.execution_time_ms > self.config.max_execution_time_ms]

        return {
            'summary': {
                'total_rules': total_rules,
                'passed_rules': passed_rules,
                'failed_rules': failed_rules,
                'disabled_rules': disabled_rules,
                'pass_rate': (passed_rules / total_rules * 100) if total_rules > 0 else 0
            },
            'scores': {
                'average_accuracy': avg_accuracy,
                'average_performance': avg_performance,
                'average_reliability': avg_reliability,
                'average_overall': avg_overall
            },
            'issues': {
                'high_false_positive_rules': high_fp_rules,
                'slow_rules': slow_rules,
                'disabled_rules': list(self.disabled_rules)
            },
            'thresholds': {
                'max_false_positive_rate': self.config.max_false_positive_rate,
                'max_execution_time_ms': self.config.max_execution_time_ms,
                'max_memory_usage_mb': self.config.max_memory_usage_mb
            }
        }

    def _load_metrics(self) -> None:
        """Load metrics from storage."""
        metrics_file = Path.home() / '.vibe_check' / 'rule_quality_gates.json'

        if metrics_file.exists():
            try:
                with open(metrics_file, 'r') as f:
                    data = json.load(f)

                # Load metrics
                for rule_id, metric_data in data.get('metrics', {}).items():
                    self.metrics[rule_id] = RuleValidationMetrics(**metric_data)

                # Load disabled rules
                self.disabled_rules = set(data.get('disabled_rules', []))

                logger.debug(f"Loaded {len(self.metrics)} rule validation metrics")

            except Exception as e:
                logger.warning(f"Failed to load rule quality gate metrics: {e}")

    def _save_metrics(self) -> None:
        """Save metrics to storage."""
        metrics_file = Path.home() / '.vibe_check' / 'rule_quality_gates.json'
        metrics_file.parent.mkdir(exist_ok=True)

        try:
            data = {
                'metrics': {
                    rule_id: {
                        'rule_id': m.rule_id,
                        'false_positive_rate': m.false_positive_rate,
                        'execution_time_ms': m.execution_time_ms,
                        'memory_usage_mb': m.memory_usage_mb,
                        'test_cases_passed': m.test_cases_passed,
                        'test_cases_failed': m.test_cases_failed,
                        'last_validation': m.last_validation,
                        'validation_status': m.validation_status,
                        'avg_execution_time': m.avg_execution_time,
                        'max_execution_time': m.max_execution_time,
                        'performance_score': m.performance_score,
                        'accuracy_score': m.accuracy_score,
                        'reliability_score': m.reliability_score,
                        'overall_score': m.overall_score
                    }
                    for rule_id, m in self.metrics.items()
                },
                'disabled_rules': list(self.disabled_rules)
            }

            with open(metrics_file, 'w') as f:
                json.dump(data, f, indent=2)

        except Exception as e:
            logger.warning(f"Failed to save rule quality gate metrics: {e}")


# Global rule quality gate system instance
_rule_quality_gate_system: Optional[RuleQualityGateSystem] = None


def get_rule_quality_gate_system() -> RuleQualityGateSystem:
    """Get the global rule quality gate system instance."""
    global _rule_quality_gate_system
    if _rule_quality_gate_system is None:
        _rule_quality_gate_system = RuleQualityGateSystem()
    return _rule_quality_gate_system


def initialize_rule_quality_gates(config: Optional[RuleQualityGateConfig] = None) -> None:
    """Initialize the global rule quality gate system."""
    global _rule_quality_gate_system
    _rule_quality_gate_system = RuleQualityGateSystem(config)
    logger.info("Rule quality gate system initialized globally")
