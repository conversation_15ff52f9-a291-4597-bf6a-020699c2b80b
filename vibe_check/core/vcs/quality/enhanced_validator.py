"""
File: vibe_check/core/vcs/quality/enhanced_validator.py
Purpose: Enhanced rule quality framework with calibration and systematic validation
Related Files: vibe_check/core/vcs/testing/rule_validator.py, vibe_check/core/vcs/quality_gates.py
Dependencies: typing, dataclasses, pathlib, asyncio, logging
"""

from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from pathlib import Path
import asyncio
import logging
import json
import time
from enum import Enum

from ..testing.rule_validator import RuleValidator, TestCase, RuleValidationReport, ValidationResult
from ..registry import AnalysisRule
from ..quality_gates import RuleQualityGateConfig, RuleValidationMetrics
from ...constants import AnalysisThresholds

logger = logging.getLogger(__name__)


class CalibrationStatus(Enum):
    """Status of rule calibration."""
    UNCALIBRATED = "uncalibrated"
    CALIBRATING = "calibrating"
    CALIBRATED = "calibrated"
    NEEDS_RECALIBRATION = "needs_recalibration"
    FAILED = "failed"


@dataclass
class RuleCalibrationResult:
    """Result of rule calibration process."""
    rule_id: str
    status: CalibrationStatus
    false_positive_rate: float
    false_negative_rate: float
    performance_score: float
    recommended_sensitivity: float
    calibration_timestamp: float
    test_cases_passed: int
    test_cases_total: int
    issues_found: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)


@dataclass
class SystemQualityReport:
    """Comprehensive system quality report."""
    total_rules: int
    calibrated_rules: int
    rules_meeting_threshold: int
    average_false_positive_rate: float
    rules_needing_attention: List[str]
    performance_summary: Dict[str, float]
    calibration_results: Dict[str, RuleCalibrationResult]
    quality_score: float
    recommendations: List[str] = field(default_factory=list)


class EnhancedRuleValidator:
    """Enhanced rule validator with calibration and systematic quality validation."""
    
    def __init__(self, config: Optional[RuleQualityGateConfig] = None):
        """Initialize enhanced validator with configuration."""
        self.config = config or RuleQualityGateConfig()
        self.base_validator = RuleValidator()
        self.calibration_history: Dict[str, List[RuleCalibrationResult]] = {}
        self.quality_thresholds = {
            'false_positive_rate': 0.10,  # 10% max
            'false_negative_rate': 0.05,  # 5% max
            'performance_threshold_ms': 100.0,  # 100ms max
            'min_test_coverage': 3  # minimum 3 test cases
        }
        
    async def calibrate_rule(self, rule: AnalysisRule, 
                           additional_test_cases: Optional[List[TestCase]] = None) -> RuleCalibrationResult:
        """Calibrate a single rule for optimal performance and accuracy."""
        start_time = time.time()
        
        logger.info(f"Starting calibration for rule {rule.rule_id}")
        
        # Add additional test cases if provided
        if additional_test_cases:
            existing_cases = self.base_validator.test_cases.get(rule.rule_id, [])
            self.base_validator.test_cases[rule.rule_id] = existing_cases + additional_test_cases
        
        # Run base validation
        validation_report = await self.base_validator.validate_rule(rule)
        
        # Performance testing
        performance_score = await self._measure_rule_performance(rule)
        
        # Determine calibration status
        status = self._determine_calibration_status(validation_report, performance_score)
        
        # Generate recommendations
        recommendations = self._generate_calibration_recommendations(validation_report, performance_score)
        
        # Calculate recommended sensitivity adjustment
        recommended_sensitivity = self._calculate_recommended_sensitivity(validation_report)
        
        result = RuleCalibrationResult(
            rule_id=rule.rule_id,
            status=status,
            false_positive_rate=validation_report.false_positive_rate,
            false_negative_rate=validation_report.false_negative_rate,
            performance_score=performance_score,
            recommended_sensitivity=recommended_sensitivity,
            calibration_timestamp=time.time(),
            test_cases_passed=validation_report.passed_tests,
            test_cases_total=validation_report.total_tests,
            recommendations=recommendations
        )
        
        # Store in calibration history
        if rule.rule_id not in self.calibration_history:
            self.calibration_history[rule.rule_id] = []
        self.calibration_history[rule.rule_id].append(result)
        
        calibration_time = time.time() - start_time
        logger.info(f"Calibration completed for {rule.rule_id} in {calibration_time:.2f}s")
        
        return result
    
    async def calibrate_all_rules(self, rules: List[AnalysisRule]) -> SystemQualityReport:
        """Calibrate all rules and generate comprehensive quality report."""
        logger.info(f"Starting system-wide calibration for {len(rules)} rules")
        
        calibration_results = {}
        total_rules = len(rules)
        calibrated_rules = 0
        rules_meeting_threshold = 0
        total_fp_rate = 0.0
        rules_needing_attention = []
        
        # Calibrate rules in parallel (with concurrency limit)
        semaphore = asyncio.Semaphore(5)  # Limit concurrent calibrations
        
        async def calibrate_with_semaphore(rule: AnalysisRule) -> Tuple[str, RuleCalibrationResult]:
            async with semaphore:
                result = await self.calibrate_rule(rule)
                return rule.rule_id, result
        
        # Run calibrations
        calibration_tasks = [calibrate_with_semaphore(rule) for rule in rules]
        results = await asyncio.gather(*calibration_tasks, return_exceptions=True)
        
        # Process results
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"Calibration failed: {result}")
                continue
                
            rule_id, calibration_result = result
            calibration_results[rule_id] = calibration_result
            
            if calibration_result.status in [CalibrationStatus.CALIBRATED, CalibrationStatus.CALIBRATING]:
                calibrated_rules += 1
            
            if calibration_result.false_positive_rate <= self.quality_thresholds['false_positive_rate']:
                rules_meeting_threshold += 1
            else:
                rules_needing_attention.append(rule_id)
            
            total_fp_rate += calibration_result.false_positive_rate
        
        # Calculate metrics
        average_fp_rate = total_fp_rate / len(calibration_results) if calibration_results else 0.0
        quality_score = self._calculate_system_quality_score(calibration_results)
        
        # Generate performance summary
        performance_summary = {
            'average_performance_ms': sum(r.performance_score for r in calibration_results.values()) / len(calibration_results) if calibration_results else 0.0,
            'slowest_rule_ms': max((r.performance_score for r in calibration_results.values()), default=0.0),
            'fastest_rule_ms': min((r.performance_score for r in calibration_results.values()), default=0.0)
        }
        
        # Generate system recommendations
        recommendations = self._generate_system_recommendations(calibration_results, rules_needing_attention)
        
        report = SystemQualityReport(
            total_rules=total_rules,
            calibrated_rules=calibrated_rules,
            rules_meeting_threshold=rules_meeting_threshold,
            average_false_positive_rate=average_fp_rate,
            rules_needing_attention=rules_needing_attention,
            performance_summary=performance_summary,
            calibration_results=calibration_results,
            quality_score=quality_score,
            recommendations=recommendations
        )
        
        logger.info(f"System calibration completed: {calibrated_rules}/{total_rules} rules calibrated, "
                   f"quality score: {quality_score:.1f}/100")
        
        return report
    
    async def _measure_rule_performance(self, rule: AnalysisRule, iterations: int = 10) -> float:
        """Measure rule performance in milliseconds."""
        test_cases = self.base_validator.test_cases.get(rule.rule_id, [])
        if not test_cases:
            return 0.0
        
        execution_times = []
        
        for _ in range(iterations):
            start_time = time.time()
            
            # Run rule on test cases
            for test_case in test_cases[:3]:  # Limit to first 3 for performance
                try:
                    from ..engine import AnalysisTarget, AnalysisContext, EngineMode
                    import ast
                    
                    target = AnalysisTarget.from_content(Path("test.py"), test_case.code)
                    context = AnalysisContext(mode=EngineMode.STANDALONE)
                    ast_tree = ast.parse(test_case.code)
                    
                    await rule.analyze(target, test_case.code, ast_tree, context)
                    
                except Exception as e:
                    logger.debug(f"Performance test error for {rule.rule_id}: {e}")
                    continue
            
            execution_times.append((time.time() - start_time) * 1000)  # Convert to ms
        
        return sum(execution_times) / len(execution_times) if execution_times else 0.0
    
    def _determine_calibration_status(self, validation_report: RuleValidationReport, 
                                    performance_score: float) -> CalibrationStatus:
        """Determine calibration status based on validation results."""
        if validation_report.overall_result == ValidationResult.ERROR:
            return CalibrationStatus.FAILED
        
        fp_threshold = self.quality_thresholds['false_positive_rate']
        fn_threshold = self.quality_thresholds['false_negative_rate']
        perf_threshold = self.quality_thresholds['performance_threshold_ms']
        
        if (validation_report.false_positive_rate <= fp_threshold and
            validation_report.false_negative_rate <= fn_threshold and
            performance_score <= perf_threshold):
            return CalibrationStatus.CALIBRATED
        elif validation_report.false_positive_rate > fp_threshold * 2:
            return CalibrationStatus.NEEDS_RECALIBRATION
        else:
            return CalibrationStatus.CALIBRATING
    
    def _calculate_recommended_sensitivity(self, validation_report: RuleValidationReport) -> float:
        """Calculate recommended sensitivity adjustment (0.0 to 1.0)."""
        if validation_report.false_positive_rate > self.quality_thresholds['false_positive_rate']:
            # Reduce sensitivity to decrease false positives
            reduction_factor = min(0.5, validation_report.false_positive_rate / 0.2)
            return max(0.1, 1.0 - reduction_factor)
        elif validation_report.false_negative_rate > self.quality_thresholds['false_negative_rate']:
            # Increase sensitivity to decrease false negatives
            increase_factor = min(0.3, validation_report.false_negative_rate / 0.1)
            return min(1.0, 1.0 + increase_factor)
        else:
            return 1.0  # No adjustment needed
    
    def _generate_calibration_recommendations(self, validation_report: RuleValidationReport,
                                            performance_score: float) -> List[str]:
        """Generate specific recommendations for rule improvement."""
        recommendations = []
        
        if validation_report.false_positive_rate > self.quality_thresholds['false_positive_rate']:
            recommendations.append(f"High false positive rate ({validation_report.false_positive_rate:.1%}). "
                                 f"Consider reducing rule sensitivity or adding more specific conditions.")
        
        if validation_report.false_negative_rate > self.quality_thresholds['false_negative_rate']:
            recommendations.append(f"High false negative rate ({validation_report.false_negative_rate:.1%}). "
                                 f"Consider increasing rule sensitivity or expanding detection patterns.")
        
        if performance_score > self.quality_thresholds['performance_threshold_ms']:
            recommendations.append(f"Slow execution ({performance_score:.1f}ms). "
                                 f"Consider optimizing rule logic or caching results.")
        
        if validation_report.total_tests < self.quality_thresholds['min_test_coverage']:
            recommendations.append(f"Insufficient test coverage ({validation_report.total_tests} tests). "
                                 f"Add more test cases to improve validation confidence.")
        
        return recommendations
    
    def _calculate_system_quality_score(self, calibration_results: Dict[str, RuleCalibrationResult]) -> float:
        """Calculate overall system quality score (0-100)."""
        if not calibration_results:
            return 0.0
        
        total_score = 0.0
        
        for result in calibration_results.values():
            # Accuracy score (0-50 points)
            accuracy_score = max(0, 50 - (result.false_positive_rate * 500))
            
            # Performance score (0-30 points)
            performance_score = max(0, 30 - (result.performance_score / 10))
            
            # Reliability score (0-20 points)
            reliability_score = (result.test_cases_passed / result.test_cases_total * 20) if result.test_cases_total > 0 else 0
            
            rule_score = accuracy_score + performance_score + reliability_score
            total_score += rule_score
        
        return total_score / len(calibration_results)
    
    def _generate_system_recommendations(self, calibration_results: Dict[str, RuleCalibrationResult],
                                       rules_needing_attention: List[str]) -> List[str]:
        """Generate system-wide recommendations."""
        recommendations = []
        
        if len(rules_needing_attention) > len(calibration_results) * 0.2:
            recommendations.append(f"High number of rules need attention ({len(rules_needing_attention)}). "
                                 f"Consider systematic review of rule configurations.")
        
        slow_rules = [r.rule_id for r in calibration_results.values() 
                     if r.performance_score > self.quality_thresholds['performance_threshold_ms']]
        if slow_rules:
            recommendations.append(f"Performance optimization needed for rules: {', '.join(slow_rules[:5])}")
        
        uncalibrated_rules = [r.rule_id for r in calibration_results.values() 
                            if r.status == CalibrationStatus.UNCALIBRATED]
        if uncalibrated_rules:
            recommendations.append(f"Complete calibration for rules: {', '.join(uncalibrated_rules[:5])}")
        
        return recommendations
    
    async def validate_regression_prevention(self, rules: List[AnalysisRule]) -> Dict[str, Any]:
        """Validate that recent changes haven't caused regression."""
        regression_report = {
            'rules_tested': 0,
            'regressions_detected': 0,
            'new_issues': [],
            'performance_regressions': [],
            'accuracy_regressions': []
        }
        
        for rule in rules:
            if rule.rule_id not in self.calibration_history:
                continue
            
            # Get latest calibration
            latest_calibration = self.calibration_history[rule.rule_id][-1]
            
            # Run current validation
            current_report = await self.base_validator.validate_rule(rule)
            regression_report['rules_tested'] += 1
            
            # Check for accuracy regression
            if current_report.false_positive_rate > latest_calibration.false_positive_rate + 0.05:
                regression_report['accuracy_regressions'].append({
                    'rule_id': rule.rule_id,
                    'previous_fp_rate': latest_calibration.false_positive_rate,
                    'current_fp_rate': current_report.false_positive_rate,
                    'regression_amount': current_report.false_positive_rate - latest_calibration.false_positive_rate
                })
                regression_report['regressions_detected'] += 1
        
        return regression_report
    
    def save_calibration_results(self, file_path: Path) -> None:
        """Save calibration results to file for persistence."""
        data = {}
        for rule_id, results in self.calibration_history.items():
            data[rule_id] = [
                {
                    'status': result.status.value,
                    'false_positive_rate': result.false_positive_rate,
                    'false_negative_rate': result.false_negative_rate,
                    'performance_score': result.performance_score,
                    'recommended_sensitivity': result.recommended_sensitivity,
                    'calibration_timestamp': result.calibration_timestamp,
                    'test_cases_passed': result.test_cases_passed,
                    'test_cases_total': result.test_cases_total,
                    'recommendations': result.recommendations
                }
                for result in results
            ]
        
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2)
        
        logger.info(f"Calibration results saved to {file_path}")
    
    def load_calibration_results(self, file_path: Path) -> None:
        """Load calibration results from file."""
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            for rule_id, results_data in data.items():
                self.calibration_history[rule_id] = [
                    RuleCalibrationResult(
                        rule_id=rule_id,
                        status=CalibrationStatus(result_data['status']),
                        false_positive_rate=result_data['false_positive_rate'],
                        false_negative_rate=result_data['false_negative_rate'],
                        performance_score=result_data['performance_score'],
                        recommended_sensitivity=result_data['recommended_sensitivity'],
                        calibration_timestamp=result_data['calibration_timestamp'],
                        test_cases_passed=result_data['test_cases_passed'],
                        test_cases_total=result_data['test_cases_total'],
                        recommendations=result_data.get('recommendations', [])
                    )
                    for result_data in results_data
                ]
            
            logger.info(f"Calibration results loaded from {file_path}")
            
        except Exception as e:
            logger.error(f"Failed to load calibration results from {file_path}: {e}")
