"""
File: vibe_check/core/vcs/quality/cicd_integration.py
Purpose: CI/CD integration for rule quality gates and validation
Related Files: enhanced_validator.py, quality_gates.py
Dependencies: typing, dataclasses, pathlib, asyncio, logging
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from pathlib import Path
import asyncio
import logging
import json
import sys

from .enhanced_validator import EnhancedRuleValidator, SystemQualityReport, CalibrationStatus
from ..registry import AnalysisRule
from ..quality_gates import RuleQualityGateConfig

logger = logging.getLogger(__name__)


@dataclass
class CICDQualityGateResult:
    """Result of CI/CD quality gate validation."""
    passed: bool
    quality_score: float
    rules_tested: int
    rules_passed: int
    rules_failed: int
    critical_issues: List[str]
    warnings: List[str]
    recommendations: List[str]
    execution_time_seconds: float
    detailed_report: Optional[SystemQualityReport] = None


class CICDQualityGateRunner:
    """CI/CD integration for running quality gates on rule changes."""
    
    def __init__(self, config: Optional[RuleQualityGateConfig] = None):
        """Initialize CI/CD quality gate runner."""
        self.config = config or RuleQualityGateConfig()
        self.validator = EnhancedRuleValidator(config)
        
    async def run_quality_gates(self, rules: List[AnalysisRule], 
                               baseline_file: Optional[Path] = None) -> CICDQualityGateResult:
        """Run quality gates for CI/CD pipeline."""
        import time
        start_time = time.time()
        
        logger.info(f"Running quality gates for {len(rules)} rules")
        
        # Load baseline if available
        if baseline_file and baseline_file.exists():
            self.validator.load_calibration_results(baseline_file)
        
        # Run comprehensive validation
        quality_report = await self.validator.calibrate_all_rules(rules)
        
        # Check for regressions
        regression_report = await self.validator.validate_regression_prevention(rules)
        
        # Evaluate quality gates
        gate_result = self._evaluate_quality_gates(quality_report, regression_report)
        gate_result.execution_time_seconds = time.time() - start_time
        gate_result.detailed_report = quality_report
        
        # Log results
        if gate_result.passed:
            logger.info(f"✅ Quality gates PASSED - Score: {gate_result.quality_score:.1f}/100")
        else:
            logger.error(f"❌ Quality gates FAILED - Score: {gate_result.quality_score:.1f}/100")
            for issue in gate_result.critical_issues:
                logger.error(f"  Critical: {issue}")
        
        return gate_result
    
    def _evaluate_quality_gates(self, quality_report: SystemQualityReport,
                               regression_report: Dict[str, Any]) -> CICDQualityGateResult:
        """Evaluate quality gates and determine pass/fail status."""
        critical_issues = []
        warnings = []
        recommendations = []
        
        # Check overall quality score
        min_quality_score = 70.0  # Configurable threshold
        if quality_report.quality_score < min_quality_score:
            critical_issues.append(f"Quality score {quality_report.quality_score:.1f} below threshold {min_quality_score}")
        
        # Check false positive rate threshold
        if quality_report.average_false_positive_rate > self.config.max_false_positive_rate:
            critical_issues.append(
                f"Average false positive rate {quality_report.average_false_positive_rate:.1%} "
                f"exceeds threshold {self.config.max_false_positive_rate:.1%}"
            )
        
        # Check for regressions
        if regression_report['regressions_detected'] > 0:
            critical_issues.append(f"Detected {regression_report['regressions_detected']} regressions")
            for regression in regression_report['accuracy_regressions']:
                critical_issues.append(
                    f"Rule {regression['rule_id']}: FP rate increased by "
                    f"{regression['regression_amount']:.1%}"
                )
        
        # Check rules needing attention
        attention_threshold = max(1, quality_report.total_rules * 0.2)  # 20% of rules
        if len(quality_report.rules_needing_attention) > attention_threshold:
            warnings.append(
                f"{len(quality_report.rules_needing_attention)} rules need attention "
                f"(threshold: {attention_threshold})"
            )
        
        # Check performance issues
        slow_rules = [
            rule_id for rule_id, result in quality_report.calibration_results.items()
            if result.performance_score > self.config.max_execution_time_ms
        ]
        if slow_rules:
            warnings.append(f"Slow rules detected: {', '.join(slow_rules[:5])}")
        
        # Add system recommendations
        recommendations.extend(quality_report.recommendations)
        
        # Determine pass/fail
        passed = len(critical_issues) == 0
        
        return CICDQualityGateResult(
            passed=passed,
            quality_score=quality_report.quality_score,
            rules_tested=quality_report.total_rules,
            rules_passed=quality_report.rules_meeting_threshold,
            rules_failed=len(quality_report.rules_needing_attention),
            critical_issues=critical_issues,
            warnings=warnings,
            recommendations=recommendations,
            execution_time_seconds=0.0  # Will be set by caller
        )
    
    def generate_cicd_report(self, result: CICDQualityGateResult, 
                           output_format: str = "json") -> str:
        """Generate CI/CD report in specified format."""
        if output_format.lower() == "json":
            return self._generate_json_report(result)
        elif output_format.lower() == "markdown":
            return self._generate_markdown_report(result)
        elif output_format.lower() == "junit":
            return self._generate_junit_report(result)
        else:
            raise ValueError(f"Unsupported output format: {output_format}")
    
    def _generate_json_report(self, result: CICDQualityGateResult) -> str:
        """Generate JSON report for CI/CD systems."""
        report_data = {
            "status": "PASSED" if result.passed else "FAILED",
            "quality_score": result.quality_score,
            "execution_time_seconds": result.execution_time_seconds,
            "summary": {
                "rules_tested": result.rules_tested,
                "rules_passed": result.rules_passed,
                "rules_failed": result.rules_failed
            },
            "critical_issues": result.critical_issues,
            "warnings": result.warnings,
            "recommendations": result.recommendations
        }
        
        if result.detailed_report:
            report_data["detailed_metrics"] = {
                "average_false_positive_rate": result.detailed_report.average_false_positive_rate,
                "calibrated_rules": result.detailed_report.calibrated_rules,
                "performance_summary": result.detailed_report.performance_summary
            }
        
        return json.dumps(report_data, indent=2)
    
    def _generate_markdown_report(self, result: CICDQualityGateResult) -> str:
        """Generate Markdown report for human consumption."""
        status_emoji = "✅" if result.passed else "❌"
        status_text = "PASSED" if result.passed else "FAILED"
        
        report = f"""# VCS Rule Quality Gate Report

## {status_emoji} Status: {status_text}

**Quality Score:** {result.quality_score:.1f}/100  
**Execution Time:** {result.execution_time_seconds:.2f}s

## Summary
- **Rules Tested:** {result.rules_tested}
- **Rules Passed:** {result.rules_passed}
- **Rules Failed:** {result.rules_failed}

"""
        
        if result.critical_issues:
            report += "## ❌ Critical Issues\n"
            for issue in result.critical_issues:
                report += f"- {issue}\n"
            report += "\n"
        
        if result.warnings:
            report += "## ⚠️ Warnings\n"
            for warning in result.warnings:
                report += f"- {warning}\n"
            report += "\n"
        
        if result.recommendations:
            report += "## 💡 Recommendations\n"
            for rec in result.recommendations:
                report += f"- {rec}\n"
            report += "\n"
        
        if result.detailed_report:
            report += "## 📊 Detailed Metrics\n"
            report += f"- **Average False Positive Rate:** {result.detailed_report.average_false_positive_rate:.1%}\n"
            report += f"- **Calibrated Rules:** {result.detailed_report.calibrated_rules}/{result.detailed_report.total_rules}\n"
            
            perf = result.detailed_report.performance_summary
            report += f"- **Average Performance:** {perf.get('average_performance_ms', 0):.1f}ms\n"
        
        return report
    
    def _generate_junit_report(self, result: CICDQualityGateResult) -> str:
        """Generate JUnit XML report for CI/CD systems."""
        status = "passed" if result.passed else "failed"
        
        xml = f"""<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="VCS Rule Quality Gates" 
           tests="{result.rules_tested}" 
           failures="{result.rules_failed}" 
           time="{result.execution_time_seconds:.3f}">
    <testcase name="Quality Score Check" classname="QualityGates">
"""
        
        if not result.passed:
            xml += f"""        <failure message="Quality gates failed">
Quality Score: {result.quality_score:.1f}/100
Critical Issues:
{chr(10).join(f"- {issue}" for issue in result.critical_issues)}
        </failure>
"""
        
        xml += """    </testcase>
</testsuite>"""
        
        return xml
    
    async def run_pre_commit_validation(self, rules: List[AnalysisRule]) -> bool:
        """Run lightweight validation for pre-commit hooks."""
        logger.info("Running pre-commit rule validation")
        
        # Quick validation - focus on critical rules only
        critical_rules = [rule for rule in rules if hasattr(rule, 'severity') and 
                         rule.severity.value in ['error', 'warning']]
        
        if not critical_rules:
            return True
        
        # Run fast validation
        validation_tasks = [
            self.validator.base_validator.validate_rule(rule) 
            for rule in critical_rules[:10]  # Limit for speed
        ]
        
        try:
            reports = await asyncio.wait_for(
                asyncio.gather(*validation_tasks, return_exceptions=True),
                timeout=30.0  # 30 second timeout for pre-commit
            )
            
            # Check for critical failures
            for report in reports:
                if isinstance(report, Exception):
                    logger.error(f"Pre-commit validation error: {report}")
                    return False
                
                if hasattr(report, 'false_positive_rate') and report.false_positive_rate > 0.2:
                    logger.error(f"Rule {report.rule_id} has high false positive rate: {report.false_positive_rate:.1%}")
                    return False
            
            logger.info("✅ Pre-commit validation passed")
            return True
            
        except asyncio.TimeoutError:
            logger.error("❌ Pre-commit validation timed out")
            return False
        except Exception as e:
            logger.error(f"❌ Pre-commit validation failed: {e}")
            return False


def main() -> int:
    """Main entry point for CI/CD quality gate runner."""
    import argparse
    import sys
    
    parser = argparse.ArgumentParser(description="VCS Rule Quality Gate Runner")
    parser.add_argument("--rules-path", type=Path, help="Path to rules directory")
    parser.add_argument("--baseline", type=Path, help="Baseline calibration file")
    parser.add_argument("--output", type=Path, help="Output report file")
    parser.add_argument("--format", choices=["json", "markdown", "junit"], 
                       default="json", help="Output format")
    parser.add_argument("--pre-commit", action="store_true", 
                       help="Run in pre-commit mode (fast validation)")
    parser.add_argument("--verbose", action="store_true", help="Verbose logging")
    
    args = parser.parse_args()
    
    # Configure logging
    level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(level=level, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # Initialize runner
    runner = CICDQualityGateRunner()
    
    async def run_validation():
        try:
            # Load rules (this would need to be implemented based on your rule loading system)
            # For now, return empty list as placeholder
            rules = []  # TODO: Implement rule loading from rules_path
            
            if args.pre_commit:
                success = await runner.run_pre_commit_validation(rules)
                return 0 if success else 1
            else:
                result = await runner.run_quality_gates(rules, args.baseline)
                
                # Generate report
                report = runner.generate_cicd_report(result, args.format)
                
                if args.output:
                    with open(args.output, 'w') as f:
                        f.write(report)
                    logger.info(f"Report saved to {args.output}")
                else:
                    print(report)
                
                return 0 if result.passed else 1
                
        except Exception as e:
            logger.error(f"Quality gate runner failed: {e}")
            return 1
    
    return asyncio.run(run_validation())


if __name__ == "__main__":
    sys.exit(main())
