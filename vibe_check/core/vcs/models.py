"""
VCS Core Models
===============

This module defines the core data models and structures used by the VCS engine.
"""

import ast
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Union
from datetime import datetime


class EngineMode(Enum):
    """VCS engine operation modes."""
    INTEGRATED = "integrated"  # Operates as part of Vibe Check analysis pipeline
    STANDALONE = "standalone"  # Operates independently with full feature set


class RuleCategory(Enum):
    """Analysis rule categories."""
    STYLE = "style"           # PEP 8 style guidelines
    COMPLEXITY = "complexity" # Code complexity metrics
    SECURITY = "security"     # Security vulnerabilities
    DOCS = "documentation"    # Documentation quality
    IMPORTS = "imports"       # Import organization
    TYPES = "types"          # Type-related issues


class IssueSeverity(Enum):
    """Issue severity levels."""
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"
    HINT = "hint"


@dataclass
class AnalysisTarget:
    """Represents a target for analysis (file, directory, or content)."""
    
    path: Path
    content: Optional[str] = None
    encoding: str = "utf-8"
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @classmethod
    def from_file(cls, file_path: Union[str, Path]) -> "AnalysisTarget":
        """Create target from file path."""
        path = Path(file_path)
        return cls(path=path)
    
    @classmethod
    def from_content(cls, content: str, file_path: Union[str, Path]) -> "AnalysisTarget":
        """Create target from content and file path."""
        path = Path(file_path)
        return cls(path=path, content=content)
    
    def get_content(self) -> str:
        """Get file content, reading from disk if not provided."""
        if self.content is not None:
            return self.content
        
        try:
            return self.path.read_text(encoding=self.encoding)
        except Exception as e:
            raise ValueError(f"Failed to read file {self.path}: {e}")
    
    def get_ast(self) -> ast.AST:
        """Parse content and return AST."""
        content = self.get_content()
        try:
            return ast.parse(content, filename=str(self.path))
        except SyntaxError as e:
            raise ValueError(f"Syntax error in {self.path}: {e}")


@dataclass
class AnalysisIssue:
    """Represents an analysis issue found in code."""
    
    line: int
    column: int
    message: str
    severity: IssueSeverity
    rule_id: str
    category: RuleCategory
    source: str = "vcs"
    fix_suggestion: Optional[str] = None
    auto_fixable: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "line": self.line,
            "column": self.column,
            "message": self.message,
            "severity": self.severity.value,
            "rule_id": self.rule_id,
            "category": self.category.value,
            "source": self.source,
            "fix_suggestion": self.fix_suggestion,
            "auto_fixable": self.auto_fixable,
            "metadata": self.metadata
        }


@dataclass
class AnalysisMetrics:
    """Code metrics calculated during analysis."""
    
    lines_of_code: int = 0
    cyclomatic_complexity: int = 0
    cognitive_complexity: int = 0
    maintainability_index: float = 0.0
    test_coverage: Optional[float] = None
    documentation_coverage: float = 0.0
    type_coverage: float = 0.0
    duplication_ratio: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "lines_of_code": self.lines_of_code,
            "cyclomatic_complexity": self.cyclomatic_complexity,
            "cognitive_complexity": self.cognitive_complexity,
            "maintainability_index": self.maintainability_index,
            "test_coverage": self.test_coverage,
            "documentation_coverage": self.documentation_coverage,
            "type_coverage": self.type_coverage,
            "duplication_ratio": self.duplication_ratio
        }


@dataclass
class AnalysisResult:
    """Results of VCS analysis."""
    
    target: AnalysisTarget
    issues: List[AnalysisIssue] = field(default_factory=list)
    metrics: Optional[AnalysisMetrics] = None
    execution_time: float = 0.0
    rules_executed: List[str] = field(default_factory=list)
    success: bool = True
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)

    @classmethod
    def combine_results(cls, results: List["AnalysisResult"]) -> "AnalysisResult":
        """Combine multiple analysis results into one."""
        if not results:
            # Return empty result
            return cls(
                target=AnalysisTarget.from_file(Path(".")),
                issues=[],
                execution_time=0.0,
                success=True
            )

        # Combine all issues
        all_issues = []
        total_time = 0.0
        all_rules = set()
        combined_success = True
        error_messages = []

        for result in results:
            all_issues.extend(result.issues)
            total_time += result.execution_time
            all_rules.update(result.rules_executed)
            if not result.success:
                combined_success = False
                if result.error_message:
                    error_messages.append(result.error_message)

        # Use first result's target as base, but modify for combined
        base_target = results[0].target
        combined_target = AnalysisTarget(
            path=base_target.path.parent if base_target.path.is_file() else base_target.path,
            content=None  # Combined results don't have single content
        )

        return cls(
            target=combined_target,
            issues=all_issues,
            execution_time=total_time,
            rules_executed=list(all_rules),
            success=combined_success,
            error_message="; ".join(error_messages) if error_messages else None
        )
    
    def add_issue(self, issue: AnalysisIssue) -> None:
        """Add an issue to the results."""
        self.issues.append(issue)
    
    def get_issues_by_category(self, category: RuleCategory) -> List[AnalysisIssue]:
        """Get issues filtered by category."""
        return [issue for issue in self.issues if issue.category == category]
    
    def get_issues_by_severity(self, severity: IssueSeverity) -> List[AnalysisIssue]:
        """Get issues filtered by severity."""
        return [issue for issue in self.issues if issue.severity == severity]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "target": str(self.target.path),
            "issues": [issue.to_dict() for issue in self.issues],
            "metrics": self.metrics.to_dict() if self.metrics else None,
            "execution_time": self.execution_time,
            "rules_executed": self.rules_executed,
            "success": self.success,
            "error_message": self.error_message,
            "metadata": self.metadata,
            "timestamp": self.timestamp.isoformat()
        }


@dataclass
class AnalysisContext:
    """Context information for analysis execution."""
    
    mode: EngineMode
    config: Dict[str, Any] = field(default_factory=dict)
    enabled_categories: Set[RuleCategory] = field(default_factory=set)
    enabled_rules: Set[str] = field(default_factory=set)
    disabled_rules: Set[str] = field(default_factory=set)
    performance_mode: bool = False
    cache_enabled: bool = True
    auto_fix_enabled: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @classmethod
    def create_default(cls, mode: EngineMode) -> "AnalysisContext":
        """Create default context for given mode."""
        return cls(
            mode=mode,
            enabled_categories=set(RuleCategory),
            performance_mode=False,
            cache_enabled=True,
            auto_fix_enabled=False
        )
