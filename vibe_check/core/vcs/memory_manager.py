"""
File: vibe_check/core/vcs/memory_manager.py
Purpose: Memory management for large project analysis
Related Files: vibe_check/core/vcs/engine.py, vibe_check/cli/parallel_processing.py
Dependencies: psutil, gc, asyncio
"""

import asyncio
import gc
import sys
from pathlib import Path
from typing import Dict, List, Optional, Any, AsyncGenerator, <PERSON><PERSON>
from dataclasses import dataclass
from enum import Enum

from vibe_check.core.vcs.models import AnalysisTarget, AnalysisResult, AnalysisContext
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)

# Try to import psutil for system monitoring
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    logger.warning("psutil not available - memory monitoring limited")


class MemoryPressure(Enum):
    """Memory pressure levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class MemoryStats:
    """Memory usage statistics."""
    total_memory_mb: float
    available_memory_mb: float
    used_memory_mb: float
    memory_percent: float
    process_memory_mb: float
    gc_collections: int
    pressure_level: MemoryPressure
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "total_memory_mb": self.total_memory_mb,
            "available_memory_mb": self.available_memory_mb,
            "used_memory_mb": self.used_memory_mb,
            "memory_percent": self.memory_percent,
            "process_memory_mb": self.process_memory_mb,
            "gc_collections": self.gc_collections,
            "pressure_level": self.pressure_level.value
        }


class MemoryMonitor:
    """Monitors system and process memory usage."""
    
    def __init__(self):
        self.baseline_memory = 0.0
        self.peak_memory = 0.0
        self.gc_threshold_adjustments = 0
    
    def get_memory_stats(self) -> MemoryStats:
        """Get current memory statistics."""
        if PSUTIL_AVAILABLE:
            # System memory
            memory = psutil.virtual_memory()
            total_mb = memory.total / (1024 * 1024)
            available_mb = memory.available / (1024 * 1024)
            used_mb = memory.used / (1024 * 1024)
            memory_percent = memory.percent
            
            # Process memory
            process = psutil.Process()
            process_memory_mb = process.memory_info().rss / (1024 * 1024)
        else:
            # Fallback to basic Python memory info
            total_mb = 8192.0  # Assume 8GB
            process_memory_mb = sys.getsizeof(gc.get_objects()) / (1024 * 1024)
            available_mb = total_mb - process_memory_mb
            used_mb = process_memory_mb
            memory_percent = (used_mb / total_mb) * 100
        
        # Update peak memory
        if process_memory_mb > self.peak_memory:
            self.peak_memory = process_memory_mb
        
        # Determine pressure level
        pressure_level = self._calculate_pressure_level(memory_percent, available_mb)
        
        # GC statistics
        gc_stats = gc.get_stats()
        total_collections = sum(stat['collections'] for stat in gc_stats)
        
        return MemoryStats(
            total_memory_mb=total_mb,
            available_memory_mb=available_mb,
            used_memory_mb=used_mb,
            memory_percent=memory_percent,
            process_memory_mb=process_memory_mb,
            gc_collections=total_collections,
            pressure_level=pressure_level
        )
    
    def _calculate_pressure_level(self, memory_percent: float, available_mb: float) -> MemoryPressure:
        """Calculate memory pressure level."""
        if memory_percent > 90 or available_mb < 512:
            return MemoryPressure.CRITICAL
        elif memory_percent > 80 or available_mb < 1024:
            return MemoryPressure.HIGH
        elif memory_percent > 70 or available_mb < 2048:
            return MemoryPressure.MEDIUM
        else:
            return MemoryPressure.LOW
    
    def set_baseline(self) -> None:
        """Set baseline memory usage."""
        stats = self.get_memory_stats()
        self.baseline_memory = stats.process_memory_mb
        logger.info(f"Memory baseline set: {self.baseline_memory:.1f} MB")
    
    def get_memory_growth(self) -> float:
        """Get memory growth since baseline."""
        current_stats = self.get_memory_stats()
        return current_stats.process_memory_mb - self.baseline_memory


class MemoryManager:
    """Manages memory usage during analysis operations."""
    
    def __init__(self, max_memory_mb: Optional[float] = None):
        self.monitor = MemoryMonitor()
        self.max_memory_mb = max_memory_mb or 4096  # 4GB default
        self.chunk_size = 50  # Files per chunk
        self.adaptive_chunk_size = True
        self.gc_frequency = 10  # Force GC every N operations
        self.operation_count = 0
    
    async def initialize(self) -> None:
        """Initialize memory manager."""
        self.monitor.set_baseline()
        
        # Adjust GC thresholds for better memory management
        gc.set_threshold(700, 10, 10)  # More aggressive collection
        
        logger.info(f"Memory manager initialized (max: {self.max_memory_mb} MB)")
    
    async def process_files_with_memory_management(
        self, 
        files: List[Path], 
        processor_func,
        context: AnalysisContext
    ) -> AsyncGenerator[Tuple[List[AnalysisResult], MemoryStats], None]:
        """
        Process files in chunks with memory management.
        
        Yields:
            Tuple of (chunk_results, memory_stats)
        """
        total_files = len(files)
        processed = 0
        
        while processed < total_files:
            # Check memory pressure and adjust chunk size
            stats = self.monitor.get_memory_stats()
            chunk_size = self._calculate_optimal_chunk_size(stats)
            
            # Get next chunk
            chunk_end = min(processed + chunk_size, total_files)
            chunk = files[processed:chunk_end]
            
            logger.debug(f"Processing chunk {processed}-{chunk_end} ({len(chunk)} files)")
            
            # Process chunk
            chunk_results = []
            for file_path in chunk:
                try:
                    result = await processor_func(file_path, context)
                    chunk_results.append(result)
                    
                    self.operation_count += 1
                    
                    # Periodic memory management
                    if self.operation_count % self.gc_frequency == 0:
                        await self._perform_memory_cleanup()
                    
                    # Check for memory pressure
                    if self.operation_count % 5 == 0:  # Check every 5 operations
                        current_stats = self.monitor.get_memory_stats()
                        if current_stats.pressure_level in [MemoryPressure.HIGH, MemoryPressure.CRITICAL]:
                            logger.warning(f"High memory pressure detected: {current_stats.memory_percent:.1f}%")
                            await self._handle_memory_pressure(current_stats)
                
                except Exception as e:
                    logger.error(f"Error processing {file_path}: {e}")
            
            # Update stats and yield results
            final_stats = self.monitor.get_memory_stats()
            yield chunk_results, final_stats
            
            processed = chunk_end
            
            # Memory cleanup between chunks
            await self._perform_memory_cleanup()
    
    def _calculate_optimal_chunk_size(self, stats: MemoryStats) -> int:
        """Calculate optimal chunk size based on memory pressure."""
        if not self.adaptive_chunk_size:
            return self.chunk_size
        
        base_size = self.chunk_size
        
        if stats.pressure_level == MemoryPressure.CRITICAL:
            return max(1, base_size // 4)
        elif stats.pressure_level == MemoryPressure.HIGH:
            return max(5, base_size // 2)
        elif stats.pressure_level == MemoryPressure.MEDIUM:
            return max(10, int(base_size * 0.75))
        else:
            # Low pressure - can increase chunk size
            return min(100, int(base_size * 1.5))
    
    async def _perform_memory_cleanup(self) -> None:
        """Perform memory cleanup operations."""
        # Force garbage collection
        collected = gc.collect()
        
        if collected > 0:
            logger.debug(f"Garbage collection freed {collected} objects")
        
        # Small delay to allow cleanup
        await asyncio.sleep(0.001)
    
    async def _handle_memory_pressure(self, stats: MemoryStats) -> None:
        """Handle high memory pressure situations."""
        logger.warning(f"Handling memory pressure: {stats.pressure_level.value}")
        
        if stats.pressure_level == MemoryPressure.CRITICAL:
            # Emergency measures
            logger.error("Critical memory pressure - forcing aggressive cleanup")
            
            # Force multiple GC cycles
            for _ in range(3):
                gc.collect()
                await asyncio.sleep(0.01)
            
            # Reduce chunk size dramatically
            self.chunk_size = max(1, self.chunk_size // 4)
            
            # Increase GC frequency
            self.gc_frequency = max(1, self.gc_frequency // 2)
            
        elif stats.pressure_level == MemoryPressure.HIGH:
            # Moderate measures
            gc.collect()
            self.chunk_size = max(5, self.chunk_size // 2)
            self.gc_frequency = max(2, self.gc_frequency // 2)
        
        # Log memory state after cleanup
        new_stats = self.monitor.get_memory_stats()
        logger.info(f"Memory after cleanup: {new_stats.process_memory_mb:.1f} MB "
                   f"({new_stats.memory_percent:.1f}%)")
    
    def get_memory_report(self) -> Dict[str, Any]:
        """Get comprehensive memory usage report."""
        current_stats = self.monitor.get_memory_stats()
        
        return {
            "current": current_stats.to_dict(),
            "baseline_mb": self.monitor.baseline_memory,
            "peak_mb": self.monitor.peak_memory,
            "growth_mb": self.monitor.get_memory_growth(),
            "operations_processed": self.operation_count,
            "current_chunk_size": self.chunk_size,
            "gc_frequency": self.gc_frequency,
            "gc_threshold_adjustments": self.monitor.gc_threshold_adjustments
        }
    
    async def optimize_for_large_project(self, estimated_files: int) -> None:
        """Optimize settings for large project analysis."""
        logger.info(f"Optimizing for large project ({estimated_files} files)")
        
        # Adjust chunk size based on project size
        if estimated_files > 10000:
            self.chunk_size = 20
            self.gc_frequency = 5
        elif estimated_files > 5000:
            self.chunk_size = 30
            self.gc_frequency = 8
        elif estimated_files > 1000:
            self.chunk_size = 40
            self.gc_frequency = 10
        
        # Enable adaptive chunk sizing for large projects
        self.adaptive_chunk_size = True
        
        # More aggressive GC for large projects
        gc.set_threshold(500, 8, 8)
        
        logger.info(f"Optimized settings: chunk_size={self.chunk_size}, "
                   f"gc_frequency={self.gc_frequency}")


class StreamingAnalyzer:
    """Streaming analyzer for processing large projects without loading everything into memory."""
    
    def __init__(self, memory_manager: MemoryManager):
        self.memory_manager = memory_manager
    
    async def analyze_project_streaming(
        self, 
        project_root: Path, 
        engine,
        context: AnalysisContext
    ) -> AsyncGenerator[Tuple[List[AnalysisResult], MemoryStats], None]:
        """
        Analyze project using streaming approach.
        
        Yields:
            Tuple of (chunk_results, memory_stats)
        """
        # Discover Python files
        python_files = list(project_root.rglob("*.py"))
        
        # Filter out ignored files
        filtered_files = []
        ignore_patterns = {'__pycache__', '.git', '.venv', 'venv', 'node_modules'}
        
        for file_path in python_files:
            if not any(pattern in str(file_path) for pattern in ignore_patterns):
                filtered_files.append(file_path)
        
        logger.info(f"Found {len(filtered_files)} Python files to analyze")
        
        # Optimize for project size
        await self.memory_manager.optimize_for_large_project(len(filtered_files))
        
        # Define processor function
        async def process_file(file_path: Path, ctx: AnalysisContext) -> AnalysisResult:
            target = AnalysisTarget.from_file(file_path)
            return await engine.analyze(target, ctx)
        
        # Process files in chunks with memory management
        async for chunk_results, memory_stats in self.memory_manager.process_files_with_memory_management(
            filtered_files, process_file, context
        ):
            yield chunk_results, memory_stats
