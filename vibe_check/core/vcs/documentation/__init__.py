"""
VCS Documentation Module
========================

Automatic documentation generation for VCS rules and components.

Components:
- RuleDocumentationGenerator: Main documentation generator
- RuleDocumentationExtractor: Extracts rule metadata and schemas
- ExampleCodeGenerator: Generates practical code examples
- MarkdownDocumentationRenderer: Creates professional Markdown docs
"""

from .rule_doc_generator import (
    RuleDocumentationGenerator,
    RuleDocumentationExtractor,
    ExampleCodeGenerator,
    MarkdownDocumentationRenderer,
    RuleDocumentation
)

__all__ = [
    "RuleDocumentationGenerator",
    "RuleDocumentationExtractor", 
    "ExampleCodeGenerator",
    "MarkdownDocumentationRenderer",
    "RuleDocumentation"
]
