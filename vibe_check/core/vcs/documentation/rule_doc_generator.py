"""
Rule Documentation Generator
============================

Automatic documentation generation for VCS rules using metadata and typed configuration.
Generates comprehensive Markdown documentation with examples and configuration options.

Components:
- RuleDocumentationExtractor: Extracts rule metadata and configuration schemas
- ExampleCodeGenerator: Generates practical code examples for rules
- MarkdownDocumentationRenderer: Creates professional Markdown documentation
- RuleDocumentationGenerator: Main orchestrator for documentation generation
"""

import ast
import re
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import logging

from ..models import RuleCategory, IssueSeverity
from ..rules.decorators import get_all_rule_metadata, get_discovered_rules
from ..config.rule_config import get_config_registry, create_rule_config
from ..registry import AnalysisRule

logger = logging.getLogger(__name__)


@dataclass
class RuleDocumentation:
    """Complete documentation for a single rule."""
    rule_id: str
    name: str
    category: str
    severity: str
    description: str
    tags: List[str]
    auto_fixable: bool
    performance_impact: str
    dependencies: List[str]
    version: str
    class_name: str
    module: str
    configuration_schema: Dict[str, Any]
    examples: List[Dict[str, Any]]
    usage_notes: List[str]
    related_rules: List[str]


class RuleDocumentationExtractor:
    """Extracts comprehensive documentation data from rules."""
    
    def __init__(self):
        self.config_registry = get_config_registry()
    
    def extract_rule_documentation(self, rule_id: str) -> Optional[RuleDocumentation]:
        """Extract complete documentation for a rule."""
        # Get rule metadata from decorator
        metadata = get_all_rule_metadata().get(rule_id)
        if not metadata:
            logger.warning(f"No metadata found for rule {rule_id}")
            return None
        
        # Get rule class
        rule_classes = get_discovered_rules()
        rule_class = rule_classes.get(rule_id)
        if not rule_class:
            logger.warning(f"No rule class found for rule {rule_id}")
            return None
        
        # Extract configuration schema
        config_schema = self._extract_configuration_schema(rule_id)
        
        # Generate examples
        examples = self._generate_rule_examples(rule_id, rule_class)
        
        # Extract usage notes from docstring
        usage_notes = self._extract_usage_notes(rule_class)
        
        # Find related rules
        related_rules = self._find_related_rules(rule_id, metadata)
        
        return RuleDocumentation(
            rule_id=rule_id,
            name=metadata['name'],
            category=metadata['category'].value,
            severity=metadata['severity'].value,
            description=metadata['description'],
            tags=metadata['tags'],
            auto_fixable=metadata['auto_fixable'],
            performance_impact=metadata['performance_impact'],
            dependencies=metadata['dependencies'],
            version=metadata['version'],
            class_name=metadata['class_name'],
            module=metadata['module'],
            configuration_schema=config_schema,
            examples=examples,
            usage_notes=usage_notes,
            related_rules=related_rules
        )
    
    def _extract_configuration_schema(self, rule_id: str) -> Dict[str, Any]:
        """Extract configuration schema for a rule."""
        try:
            config = create_rule_config(rule_id, {})
            return config.get_config_schema()
        except Exception as e:
            logger.warning(f"Could not extract config schema for {rule_id}: {e}")
            return {}
    
    def _generate_rule_examples(self, rule_id: str, rule_class: type) -> List[Dict[str, Any]]:
        """Generate practical examples for a rule."""
        examples = []
        
        # Generate examples based on rule category and ID
        if rule_id == "S001":  # Line Length
            examples.extend([
                {
                    "title": "Long Line Violation",
                    "code": "# This line is intentionally very long to demonstrate the line length rule violation that occurs when code exceeds the configured maximum",
                    "violation": True,
                    "explanation": "Line exceeds maximum length (100 characters by default)"
                },
                {
                    "title": "Proper Line Length",
                    "code": "# This line is properly formatted within the length limit",
                    "violation": False,
                    "explanation": "Line is within the configured maximum length"
                }
            ])
        elif rule_id == "S002":  # Trailing Whitespace
            examples.extend([
                {
                    "title": "Trailing Whitespace Violation",
                    "code": "def example_function():   \n    return True",
                    "violation": True,
                    "explanation": "Line has trailing whitespace after the colon"
                },
                {
                    "title": "Clean Code",
                    "code": "def example_function():\n    return True",
                    "violation": False,
                    "explanation": "No trailing whitespace"
                }
            ])
        elif rule_id.startswith("C"):  # Complexity rules
            examples.extend([
                {
                    "title": "High Complexity Example",
                    "code": self._generate_complex_function_example(),
                    "violation": True,
                    "explanation": "Function has high cyclomatic complexity due to multiple branches"
                },
                {
                    "title": "Simplified Version",
                    "code": self._generate_simple_function_example(),
                    "violation": False,
                    "explanation": "Refactored to reduce complexity using early returns"
                }
            ])
        elif rule_id.startswith("SEC"):  # Security rules
            examples.extend([
                {
                    "title": "Security Vulnerability",
                    "code": "password = 'hardcoded_password_123'",
                    "violation": True,
                    "explanation": "Hardcoded password in source code"
                },
                {
                    "title": "Secure Approach",
                    "code": "password = os.environ.get('PASSWORD')",
                    "violation": False,
                    "explanation": "Password loaded from environment variable"
                }
            ])
        
        return examples
    
    def _generate_complex_function_example(self) -> str:
        """Generate example of complex function."""
        return """def process_data(data, mode, options):
    if mode == 'strict':
        if options.get('validate'):
            if data is None:
                return None
            elif len(data) == 0:
                return []
            elif isinstance(data, str):
                return data.strip()
            else:
                return str(data)
        else:
            return data
    elif mode == 'lenient':
        return data or ''
    else:
        raise ValueError('Invalid mode')"""
    
    def _generate_simple_function_example(self) -> str:
        """Generate example of simplified function."""
        return """def process_data(data, mode, options):
    if mode not in ['strict', 'lenient']:
        raise ValueError('Invalid mode')
    
    if mode == 'lenient':
        return data or ''
    
    if not options.get('validate'):
        return data
    
    return _validate_and_process(data)

def _validate_and_process(data):
    if data is None:
        return None
    if len(data) == 0:
        return []
    if isinstance(data, str):
        return data.strip()
    return str(data)"""
    
    def _extract_usage_notes(self, rule_class: type) -> List[str]:
        """Extract usage notes from rule docstring."""
        notes = []
        
        if rule_class.__doc__:
            # Extract notes from docstring
            docstring = rule_class.__doc__.strip()
            
            # Look for common patterns
            if "Note:" in docstring:
                note_section = docstring.split("Note:")[-1].strip()
                notes.append(note_section)
            
            # Add general usage note
            notes.append(f"This rule is implemented in {rule_class.__name__}")
        
        return notes
    
    def _find_related_rules(self, rule_id: str, metadata: Dict[str, Any]) -> List[str]:
        """Find rules related to the current rule."""
        related = []
        current_category = metadata['category']
        current_tags = set(metadata['tags'])
        
        # Find rules in same category
        all_metadata = get_all_rule_metadata()
        for other_rule_id, other_metadata in all_metadata.items():
            if other_rule_id == rule_id:
                continue
            
            # Same category
            if other_metadata['category'] == current_category:
                related.append(other_rule_id)
            # Shared tags
            elif current_tags & set(other_metadata['tags']):
                related.append(other_rule_id)
        
        return sorted(related)


class ExampleCodeGenerator:
    """Generates practical code examples for rules."""
    
    def generate_configuration_examples(self, rule_id: str, config_schema: Dict[str, Any]) -> List[Dict[str, str]]:
        """Generate configuration examples for a rule."""
        examples = []
        
        if not config_schema:
            return examples
        
        # Default configuration
        default_config = {
            field: info['default']
            for field, info in config_schema.items()
        }
        
        examples.append({
            "title": "Default Configuration",
            "config": self._format_config_dict(default_config),
            "description": "Default settings for the rule"
        })
        
        # Custom configuration examples
        if rule_id == "S001":  # Line Length
            examples.append({
                "title": "Strict Configuration",
                "config": self._format_config_dict({
                    "max_line_length": 80,
                    "ignore_urls": False,
                    "ignore_comments": False
                }),
                "description": "Strict line length checking with no exceptions"
            })
            
            examples.append({
                "title": "Lenient Configuration",
                "config": self._format_config_dict({
                    "max_line_length": 120,
                    "ignore_urls": True,
                    "ignore_comments": True
                }),
                "description": "More lenient settings for legacy codebases"
            })
        
        return examples
    
    def _format_config_dict(self, config: Dict[str, Any]) -> str:
        """Format configuration dictionary as readable text."""
        lines = []
        for key, value in config.items():
            if isinstance(value, str):
                lines.append(f'"{key}": "{value}"')
            else:
                lines.append(f'"{key}": {value}')
        
        return "{\n  " + ",\n  ".join(lines) + "\n}"


class MarkdownDocumentationRenderer:
    """Renders rule documentation as Markdown."""
    
    def __init__(self):
        self.example_generator = ExampleCodeGenerator()
    
    def render_rule_documentation(self, doc: RuleDocumentation) -> str:
        """Render complete rule documentation as Markdown."""
        sections = []
        
        # Header
        sections.append(self._render_header(doc))
        
        # Overview
        sections.append(self._render_overview(doc))
        
        # Configuration
        if doc.configuration_schema:
            sections.append(self._render_configuration(doc))
        
        # Examples
        if doc.examples:
            sections.append(self._render_examples(doc))
        
        # Usage Notes
        if doc.usage_notes:
            sections.append(self._render_usage_notes(doc))
        
        # Related Rules
        if doc.related_rules:
            sections.append(self._render_related_rules(doc))
        
        # Technical Details
        sections.append(self._render_technical_details(doc))
        
        return "\n\n".join(sections)
    
    def _render_header(self, doc: RuleDocumentation) -> str:
        """Render rule header."""
        severity_emoji = {
            "error": "🚨",
            "warning": "⚠️",
            "info": "ℹ️",
            "hint": "💡"
        }
        
        category_emoji = {
            "style": "🎨",
            "complexity": "🔄",
            "security": "🔒",
            "documentation": "📝",
            "imports": "📦",
            "types": "🏷️"
        }
        
        auto_fix_badge = " 🔧 **Auto-fixable**" if doc.auto_fixable else ""
        
        return f"""# {doc.rule_id}: {doc.name}

{severity_emoji.get(doc.severity, '📋')} **{doc.severity.upper()}** | {category_emoji.get(doc.category, '📋')} **{doc.category.upper()}**{auto_fix_badge}

> {doc.description}"""
    
    def _render_overview(self, doc: RuleDocumentation) -> str:
        """Render rule overview."""
        sections = ["## Overview"]
        
        # Basic info table
        sections.append("| Property | Value |")
        sections.append("|----------|-------|")
        sections.append(f"| **Rule ID** | `{doc.rule_id}` |")
        sections.append(f"| **Category** | {doc.category.title()} |")
        sections.append(f"| **Severity** | {doc.severity.title()} |")
        sections.append(f"| **Auto-fixable** | {'Yes' if doc.auto_fixable else 'No'} |")
        sections.append(f"| **Performance Impact** | {doc.performance_impact.title()} |")
        sections.append(f"| **Version** | {doc.version} |")
        
        # Tags
        if doc.tags:
            sections.append(f"\n**Tags:** {', '.join(f'`{tag}`' for tag in doc.tags)}")
        
        # Dependencies
        if doc.dependencies:
            sections.append(f"\n**Dependencies:** {', '.join(f'`{dep}`' for dep in doc.dependencies)}")
        
        return "\n".join(sections)
    
    def _render_configuration(self, doc: RuleDocumentation) -> str:
        """Render configuration section."""
        sections = ["## Configuration"]
        
        # Configuration schema table
        sections.append("| Option | Type | Default | Description |")
        sections.append("|--------|------|---------|-------------|")
        
        for field_name, field_info in doc.configuration_schema.items():
            field_type = field_info['type']
            default = field_info['default']
            description = field_info['description']
            
            # Format default value
            if isinstance(default, str):
                default_str = f'`"{default}"`'
            else:
                default_str = f'`{default}`'
            
            sections.append(f"| `{field_name}` | {field_type} | {default_str} | {description} |")
        
        # Configuration examples
        config_examples = self.example_generator.generate_configuration_examples(
            doc.rule_id, doc.configuration_schema
        )
        
        if config_examples:
            sections.append("\n### Configuration Examples")
            
            for example in config_examples:
                sections.append(f"\n#### {example['title']}")
                sections.append(f"\n{example['description']}")
                sections.append(f"\n```json\n{example['config']}\n```")
        
        return "\n".join(sections)
    
    def _render_examples(self, doc: RuleDocumentation) -> str:
        """Render examples section."""
        sections = ["## Examples"]
        
        for i, example in enumerate(doc.examples, 1):
            sections.append(f"\n### Example {i}: {example['title']}")
            
            violation_status = "❌ **Violation**" if example['violation'] else "✅ **Clean Code**"
            sections.append(f"\n{violation_status}")
            
            sections.append(f"\n```python\n{example['code']}\n```")
            sections.append(f"\n{example['explanation']}")
        
        return "\n".join(sections)
    
    def _render_usage_notes(self, doc: RuleDocumentation) -> str:
        """Render usage notes section."""
        sections = ["## Usage Notes"]
        
        for note in doc.usage_notes:
            sections.append(f"- {note}")
        
        return "\n".join(sections)
    
    def _render_related_rules(self, doc: RuleDocumentation) -> str:
        """Render related rules section."""
        sections = ["## Related Rules"]
        
        for rule_id in doc.related_rules:
            sections.append(f"- [`{rule_id}`](#{rule_id.lower()})")
        
        return "\n".join(sections)
    
    def _render_technical_details(self, doc: RuleDocumentation) -> str:
        """Render technical details section."""
        return f"""## Technical Details

- **Implementation:** `{doc.class_name}` in `{doc.module}`
- **Rule ID:** `{doc.rule_id}`
- **Version:** `{doc.version}`

---
*Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} by Vibe Check Rule Documentation Generator*"""


class RuleDocumentationGenerator:
    """Main orchestrator for rule documentation generation."""
    
    def __init__(self):
        self.extractor = RuleDocumentationExtractor()
        self.renderer = MarkdownDocumentationRenderer()
    
    def generate_rule_documentation(self, rule_id: str) -> Optional[str]:
        """Generate documentation for a single rule."""
        doc = self.extractor.extract_rule_documentation(rule_id)
        if not doc:
            return None
        
        return self.renderer.render_rule_documentation(doc)
    
    def generate_all_rules_documentation(self) -> Dict[str, str]:
        """Generate documentation for all discovered rules."""
        all_docs = {}
        
        rule_metadata = get_all_rule_metadata()
        for rule_id in rule_metadata.keys():
            doc_content = self.generate_rule_documentation(rule_id)
            if doc_content:
                all_docs[rule_id] = doc_content
                logger.info(f"Generated documentation for rule {rule_id}")
            else:
                logger.warning(f"Failed to generate documentation for rule {rule_id}")
        
        return all_docs
    
    def generate_rules_index(self) -> str:
        """Generate an index of all rules."""
        sections = []
        
        # Header
        sections.append("# VCS Rules Documentation")
        sections.append("\n**Generated:** " + datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        sections.append("\nComprehensive documentation for all Vibe Check VCS analysis rules.")
        
        # Group rules by category
        rule_metadata = get_all_rule_metadata()
        rules_by_category = {}
        
        for rule_id, metadata in rule_metadata.items():
            category = metadata['category'].value
            if category not in rules_by_category:
                rules_by_category[category] = []
            rules_by_category[category].append((rule_id, metadata))
        
        # Render each category
        for category, rules in sorted(rules_by_category.items()):
            sections.append(f"\n## {category.title()} Rules")
            
            for rule_id, metadata in sorted(rules):
                severity_emoji = {
                    IssueSeverity.ERROR: "🚨",
                    IssueSeverity.WARNING: "⚠️",
                    IssueSeverity.INFO: "ℹ️",
                    IssueSeverity.HINT: "💡"
                }.get(metadata['severity'], '📋')
                
                auto_fix = " 🔧" if metadata['auto_fixable'] else ""
                
                sections.append(f"- **[{rule_id}](#{rule_id.lower()})**: {metadata['name']} {severity_emoji}{auto_fix}")
                sections.append(f"  - {metadata['description']}")
        
        # Statistics
        total_rules = len(rule_metadata)
        auto_fixable = sum(1 for m in rule_metadata.values() if m['auto_fixable'])
        
        sections.append(f"\n## Statistics")
        sections.append(f"- **Total Rules:** {total_rules}")
        sections.append(f"- **Auto-fixable Rules:** {auto_fixable}")
        sections.append(f"- **Categories:** {len(rules_by_category)}")
        
        return "\n".join(sections)
    
    def save_documentation(self, output_dir: Path, include_index: bool = True) -> Dict[str, Path]:
        """Save all rule documentation to files."""
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        saved_files = {}
        
        # Generate and save individual rule docs
        all_docs = self.generate_all_rules_documentation()
        
        for rule_id, content in all_docs.items():
            filename = f"{rule_id.lower()}.md"
            file_path = output_dir / filename
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            saved_files[rule_id] = file_path
            logger.info(f"Saved documentation for {rule_id} to {file_path}")
        
        # Generate and save index
        if include_index:
            index_content = self.generate_rules_index()
            index_path = output_dir / "index.md"
            
            with open(index_path, 'w', encoding='utf-8') as f:
                f.write(index_content)
            
            saved_files['index'] = index_path
            logger.info(f"Saved rules index to {index_path}")
        
        return saved_files
