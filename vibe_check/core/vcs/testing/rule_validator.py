"""
File: vibe_check/core/vcs/testing/rule_validator.py
Purpose: Rule validation framework to ensure VCS rules produce accurate results
Related Files: vibe_check/core/vcs/rules/*.py
Dependencies: ast, typing, dataclasses
"""

import ast
import asyncio
import json
import time
from dataclasses import dataclass, field
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, Set
from enum import Enum

from ..models import AnalysisTarget, AnalysisContext, AnalysisIssue, EngineMode
from ..registry import AnalysisRule


class ValidationResult(Enum):
    """Result of rule validation."""
    PASS = "pass"
    FAIL = "fail"
    ERROR = "error"


@dataclass
class TestCase:
    """Test case for rule validation."""
    name: str
    code: str
    expected_issues: int
    expected_rule_ids: List[str]
    description: str
    should_pass: bool = True


@dataclass
class RuleValidationReport:
    """Report of rule validation results."""
    rule_id: str
    rule_name: str
    total_tests: int
    passed_tests: int
    failed_tests: int
    error_tests: int
    false_positive_rate: float
    false_negative_rate: float
    test_results: List[Dict[str, Any]]
    overall_result: ValidationResult


class RuleValidator:
    """Validates VCS rules against known test cases."""
    
    def __init__(self) -> None:
        self.test_cases: Dict[str, List[TestCase]] = {}
        self._load_built_in_test_cases()
    
    def _load_built_in_test_cases(self) -> None:
        """Load built-in test cases for common rules."""
        
        # Line length test cases (updated for 111-character limit and consolidated reporting)
        self.test_cases["S001"] = [
            TestCase(
                name="short_line",
                code="x = 1",
                expected_issues=0,
                expected_rule_ids=[],
                description="Short line should not trigger line length rule"
            ),
            TestCase(
                name="exactly_111_chars",
                code="x = 'this line is exactly one hundred eleven characters long and should not trigger the rule at all'",
                expected_issues=0,
                expected_rule_ids=[],
                description="Line exactly at 111-character limit should not trigger"
            ),
            TestCase(
                name="single_long_line",
                code="x = 'this is a very long line that exceeds the maximum line length limit of 111 characters and should trigger the line length rule'",
                expected_issues=1,
                expected_rule_ids=["S001"],
                description="Single long line should trigger one consolidated issue"
            ),
            TestCase(
                name="multiple_long_lines",
                code="# This line exceeds 111 characters and should be detected by the line length rule for consolidated reporting\ny = 'another very long line that also exceeds the maximum line length limit and should be included in the same issue'\nz = 'short line'",
                expected_issues=1,
                expected_rule_ids=["S001"],
                description="Multiple long lines should trigger one consolidated issue per file"
            ),
            TestCase(
                name="mixed_lines",
                code="short = 'ok'\n# This comment line is longer than 111 characters and should be detected by the line length validation rule\nmedium_line = 'this is fine'\nvery_long_variable_name_that_exceeds_the_limit = 'this assignment statement is definitely longer than 111 characters'",
                expected_issues=1,
                expected_rule_ids=["S001"],
                description="Mix of short and long lines should trigger one consolidated issue"
            ),
            TestCase(
                name="empty_and_whitespace_lines",
                code="\n   \n# This comment line exceeds the 111-character limit and should be detected by the line length rule for proper validation\n\n",
                expected_issues=1,
                expected_rule_ids=["S001"],
                description="Empty lines should be ignored, only long lines counted"
            )
        ]
        
        # Naming convention test cases
        self.test_cases["S002"] = [
            TestCase(
                name="good_snake_case",
                code="def good_function_name():\n    pass",
                expected_issues=0,
                expected_rule_ids=[],
                description="Snake case function name should pass"
            ),
            TestCase(
                name="bad_camel_case",
                code="def badCamelCase():\n    pass",
                expected_issues=1,
                expected_rule_ids=["S002"],
                description="CamelCase function name should fail"
            ),
            TestCase(
                name="good_variable",
                code="good_variable = 42",
                expected_issues=0,
                expected_rule_ids=[],
                description="Snake case variable should pass"
            )
        ]
        
        # ADV003 (AsyncAwait) test cases
        self.test_cases["ADV003"] = [
            TestCase(
                name="good_async_function",
                code="async def good_async():\n    await some_async_call()",
                expected_issues=0,
                expected_rule_ids=[],
                description="Async function with await should pass"
            ),
            TestCase(
                name="async_without_await",
                code="async def bad_async():\n    return 42",
                expected_issues=1,
                expected_rule_ids=["ADV003"],
                description="Async function without await should fail"
            ),
            TestCase(
                name="await_non_awaitable",
                code="async def bad_await():\n    await print('hello')",
                expected_issues=1,
                expected_rule_ids=["ADV003"],
                description="Awaiting non-awaitable should fail"
            ),
            TestCase(
                name="complex_attribute_access",
                code="async def complex_call():\n    await obj.attr.method()",
                expected_issues=0,
                expected_rule_ids=[],
                description="Complex attribute access should not crash"
            )
        ]
    
    async def validate_rule(self, rule: AnalysisRule) -> RuleValidationReport:
        """Validate a single rule against its test cases."""
        
        test_cases = self.test_cases.get(rule.rule_id, [])
        if not test_cases:
            # Create minimal test case if none exist
            test_cases = [TestCase(
                name="minimal_test",
                code="# minimal test\npass",
                expected_issues=0,
                expected_rule_ids=[],
                description="Minimal test case"
            )]
        
        total_tests = len(test_cases)
        passed_tests = 0
        failed_tests = 0
        error_tests = 0
        test_results = []
        
        false_positives = 0
        false_negatives = 0
        
        for test_case in test_cases:
            try:
                # Create temporary file for testing
                target = AnalysisTarget.from_content(
                    content=test_case.code,
                    file_path=Path(f"test_{test_case.name}.py")
                )
                context = AnalysisContext.create_default(EngineMode.STANDALONE)
                
                # Parse AST
                try:
                    ast_tree = ast.parse(test_case.code)
                except SyntaxError as e:
                    test_results.append({
                        "test_name": test_case.name,
                        "result": ValidationResult.ERROR.value,
                        "error": f"Syntax error: {e}",
                        "expected_issues": test_case.expected_issues,
                        "actual_issues": 0
                    })
                    error_tests += 1
                    continue
                
                # Run rule analysis
                issues = await rule.analyze(target, test_case.code, ast_tree, context)
                
                # Filter issues to only those from this rule
                rule_issues = [issue for issue in issues if issue.rule_id == rule.rule_id]
                actual_issues = len(rule_issues)
                
                # Determine test result
                if actual_issues == test_case.expected_issues:
                    result = ValidationResult.PASS
                    passed_tests += 1
                else:
                    result = ValidationResult.FAIL
                    failed_tests += 1
                    
                    # Track false positives/negatives
                    if actual_issues > test_case.expected_issues:
                        false_positives += actual_issues - test_case.expected_issues
                    else:
                        false_negatives += test_case.expected_issues - actual_issues
                
                test_results.append({
                    "test_name": test_case.name,
                    "result": result.value,
                    "expected_issues": test_case.expected_issues,
                    "actual_issues": actual_issues,
                    "description": test_case.description,
                    "issues": [
                        {
                            "line": issue.line,
                            "message": issue.message,
                            "rule_id": issue.rule_id
                        }
                        for issue in rule_issues
                    ]
                })
                
            except Exception as e:
                test_results.append({
                    "test_name": test_case.name,
                    "result": ValidationResult.ERROR.value,
                    "error": str(e),
                    "expected_issues": test_case.expected_issues,
                    "actual_issues": 0
                })
                error_tests += 1
        
        # Calculate rates
        total_expected = sum(tc.expected_issues for tc in test_cases)
        false_positive_rate = false_positives / max(total_expected, 1) if total_expected > 0 else 0.0
        false_negative_rate = false_negatives / max(total_expected, 1) if total_expected > 0 else 0.0
        
        # Determine overall result
        if error_tests > 0:
            overall_result = ValidationResult.ERROR
        elif failed_tests == 0:
            overall_result = ValidationResult.PASS
        else:
            overall_result = ValidationResult.FAIL
        
        return RuleValidationReport(
            rule_id=rule.rule_id,
            rule_name=rule.name,
            total_tests=total_tests,
            passed_tests=passed_tests,
            failed_tests=failed_tests,
            error_tests=error_tests,
            false_positive_rate=false_positive_rate,
            false_negative_rate=false_negative_rate,
            test_results=test_results,
            overall_result=overall_result
        )
    
    async def validate_all_rules(self, rules: List[AnalysisRule]) -> List[RuleValidationReport]:
        """Validate all rules and return reports."""
        reports = []
        
        for rule in rules:
            try:
                report = await self.validate_rule(rule)
                reports.append(report)
            except Exception as e:
                # Create error report for failed validation
                reports.append(RuleValidationReport(
                    rule_id=rule.rule_id,
                    rule_name=rule.name,
                    total_tests=0,
                    passed_tests=0,
                    failed_tests=0,
                    error_tests=1,
                    false_positive_rate=1.0,
                    false_negative_rate=0.0,
                    test_results=[{
                        "test_name": "validation_error",
                        "result": ValidationResult.ERROR.value,
                        "error": str(e)
                    }],
                    overall_result=ValidationResult.ERROR
                ))
        
        return reports
    
    def add_test_case(self, rule_id: str, test_case: TestCase) -> None:
        """Add a custom test case for a rule."""
        if rule_id not in self.test_cases:
            self.test_cases[rule_id] = []
        self.test_cases[rule_id].append(test_case)

    def load_test_cases_from_file(self, file_path: Path) -> None:
        """Load test cases from a JSON file."""
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)

            for rule_id, test_data in data.items():
                for test_case_data in test_data:
                    test_case = TestCase(
                        name=test_case_data['name'],
                        code=test_case_data['code'],
                        expected_issues=test_case_data['expected_issues'],
                        expected_rule_ids=test_case_data.get('expected_rule_ids', [rule_id]),
                        description=test_case_data['description'],
                        should_pass=test_case_data.get('should_pass', True)
                    )
                    self.add_test_case(rule_id, test_case)

        except Exception as e:
            print(f"Failed to load test cases from {file_path}: {e}")

    def save_test_cases_to_file(self, file_path: Path) -> None:
        """Save current test cases to a JSON file."""
        data = {}
        for rule_id, test_cases in self.test_cases.items():
            data[rule_id] = []
            for test_case in test_cases:
                data[rule_id].append({
                    'name': test_case.name,
                    'code': test_case.code,
                    'expected_issues': test_case.expected_issues,
                    'expected_rule_ids': test_case.expected_rule_ids,
                    'description': test_case.description,
                    'should_pass': test_case.should_pass
                })

        file_path.parent.mkdir(parents=True, exist_ok=True)
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2)

    def get_test_coverage_report(self) -> Dict[str, Any]:
        """Generate test coverage report."""
        total_rules = len(self.test_cases)
        total_test_cases = sum(len(cases) for cases in self.test_cases.values())

        coverage_by_category = {}
        rules_without_tests = []

        # Analyze coverage
        for rule_id, test_cases in self.test_cases.items():
            if not test_cases:
                rules_without_tests.append(rule_id)

        return {
            'total_rules_with_tests': total_rules,
            'total_test_cases': total_test_cases,
            'average_tests_per_rule': total_test_cases / total_rules if total_rules > 0 else 0,
            'rules_without_tests': rules_without_tests,
            'coverage_percentage': (total_rules / (total_rules + len(rules_without_tests)) * 100) if (total_rules + len(rules_without_tests)) > 0 else 0
        }


@dataclass
class ComprehensiveTestSuite:
    """Comprehensive test suite for VCS rules with regression testing."""

    validator: RuleValidator = field(default_factory=RuleValidator)
    test_history: Dict[str, List[Dict[str, Any]]] = field(default_factory=dict)
    performance_benchmarks: Dict[str, float] = field(default_factory=dict)

    async def run_regression_tests(self, rules: List[AnalysisRule]) -> Dict[str, Any]:
        """Run regression tests to ensure rule changes don't break existing functionality."""
        regression_results = {
            'total_rules_tested': 0,
            'rules_passed': 0,
            'rules_failed': 0,
            'performance_regressions': [],
            'accuracy_regressions': [],
            'new_false_positives': [],
            'detailed_results': {}
        }

        for rule in rules:
            if rule.rule_id not in self.validator.test_cases:
                continue

            # Run current validation
            current_report = await self.validator.validate_rule(rule)
            regression_results['total_rules_tested'] += 1

            # Check for regressions
            if rule.rule_id in self.test_history:
                last_result = self.test_history[rule.rule_id][-1]

                # Performance regression check
                if (current_report.false_positive_rate > last_result.get('false_positive_rate', 0) + 0.05):
                    regression_results['accuracy_regressions'].append({
                        'rule_id': rule.rule_id,
                        'previous_fp_rate': last_result.get('false_positive_rate', 0),
                        'current_fp_rate': current_report.false_positive_rate,
                        'regression_amount': current_report.false_positive_rate - last_result.get('false_positive_rate', 0)
                    })

            # Store current results
            if rule.rule_id not in self.test_history:
                self.test_history[rule.rule_id] = []

            self.test_history[rule.rule_id].append({
                'timestamp': time.time(),
                'false_positive_rate': current_report.false_positive_rate,
                'false_negative_rate': current_report.false_negative_rate,
                'total_tests': current_report.total_tests,
                'passed_tests': current_report.passed_tests
            })

            # Track results
            if current_report.overall_result == ValidationResult.PASS:
                regression_results['rules_passed'] += 1
            else:
                regression_results['rules_failed'] += 1

            regression_results['detailed_results'][rule.rule_id] = {
                'status': current_report.overall_result.value,
                'false_positive_rate': current_report.false_positive_rate,
                'test_coverage': current_report.total_tests
            }

        return regression_results

    async def benchmark_rule_performance(self, rule: AnalysisRule, iterations: int = 10) -> Dict[str, float]:
        """Benchmark rule performance with multiple iterations."""
        if rule.rule_id not in self.validator.test_cases:
            return {'error': 'No test cases available'}

        execution_times = []
        test_cases = self.validator.test_cases[rule.rule_id]

        for _ in range(iterations):
            start_time = time.time()

            # Run all test cases
            for test_case in test_cases:
                try:
                    target = AnalysisTarget.from_content(Path("test.py"), test_case.code)
                    context = AnalysisContext(mode=EngineMode.STANDALONE)

                    # Simulate rule execution
                    await rule.analyze(target, test_case.code, ast.parse(test_case.code), context)

                except Exception:
                    pass  # Ignore errors for performance testing

            execution_times.append((time.time() - start_time) * 1000)  # Convert to ms

        avg_time = sum(execution_times) / len(execution_times)
        min_time = min(execution_times)
        max_time = max(execution_times)

        # Store benchmark
        self.performance_benchmarks[rule.rule_id] = avg_time

        return {
            'average_ms': avg_time,
            'min_ms': min_time,
            'max_ms': max_time,
            'iterations': iterations,
            'test_cases_count': len(test_cases)
        }

    async def generate_quality_report(self, rules: List[AnalysisRule]) -> Dict[str, Any]:
        """Generate comprehensive quality report for all rules."""
        report = {
            'summary': {
                'total_rules': len(rules),
                'rules_with_tests': 0,
                'average_test_coverage': 0,
                'overall_quality_score': 0
            },
            'rule_details': {},
            'recommendations': []
        }

        total_quality_score = 0.0
        rules_with_tests = 0

        for rule in rules:
            if rule.rule_id in self.validator.test_cases:
                rules_with_tests += 1
                validation_report = await self.validator.validate_rule(rule)

                # Calculate quality score (0-100)
                accuracy_score = max(0, 100 - (validation_report.false_positive_rate * 100))
                coverage_score = min(100, (validation_report.total_tests / 5) * 100)  # 5 tests = 100%
                reliability_score = (validation_report.passed_tests / validation_report.total_tests * 100) if validation_report.total_tests > 0 else 0

                quality_score = (accuracy_score + coverage_score + reliability_score) / 3
                total_quality_score += quality_score

                report['rule_details'][rule.rule_id] = {
                    'quality_score': quality_score,
                    'accuracy_score': accuracy_score,
                    'coverage_score': coverage_score,
                    'reliability_score': reliability_score,
                    'false_positive_rate': validation_report.false_positive_rate,
                    'test_count': validation_report.total_tests,
                    'status': validation_report.overall_result.value
                }

                # Generate recommendations
                if validation_report.false_positive_rate > 0.1:
                    report['recommendations'].append(f"Rule {rule.rule_id}: High false positive rate ({validation_report.false_positive_rate:.1%})")

                if validation_report.total_tests < 3:
                    report['recommendations'].append(f"Rule {rule.rule_id}: Insufficient test coverage ({validation_report.total_tests} tests)")

        # Update summary
        report['summary']['rules_with_tests'] = rules_with_tests
        report['summary']['average_test_coverage'] = (sum(len(cases) for cases in self.validator.test_cases.values()) / rules_with_tests) if rules_with_tests > 0 else 0
        report['summary']['overall_quality_score'] = total_quality_score / rules_with_tests if rules_with_tests > 0 else 0

        return report


# Global test suite instance
_test_suite: Optional[ComprehensiveTestSuite] = None


def get_test_suite() -> ComprehensiveTestSuite:
    """Get the global test suite instance."""
    global _test_suite
    if _test_suite is None:
        _test_suite = ComprehensiveTestSuite()
    return _test_suite


def initialize_test_suite() -> None:
    """Initialize the global test suite."""
    global _test_suite
    _test_suite = ComprehensiveTestSuite()

    # Load test cases from standard location
    test_cases_file = Path(__file__).parent / 'test_cases.json'
    if test_cases_file.exists():
        _test_suite.validator.load_test_cases_from_file(test_cases_file)
