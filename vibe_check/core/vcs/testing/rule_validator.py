"""
File: vibe_check/core/vcs/testing/rule_validator.py
Purpose: Rule validation framework to ensure VCS rules produce accurate results
Related Files: vibe_check/core/vcs/rules/*.py
Dependencies: ast, typing, dataclasses
"""

import ast
import asyncio
from dataclasses import dataclass
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from enum import Enum

from ..models import AnalysisTarget, AnalysisContext, AnalysisIssue, EngineMode
from ..registry import AnalysisRule


class ValidationResult(Enum):
    """Result of rule validation."""
    PASS = "pass"
    FAIL = "fail"
    ERROR = "error"


@dataclass
class TestCase:
    """Test case for rule validation."""
    name: str
    code: str
    expected_issues: int
    expected_rule_ids: List[str]
    description: str
    should_pass: bool = True


@dataclass
class RuleValidationReport:
    """Report of rule validation results."""
    rule_id: str
    rule_name: str
    total_tests: int
    passed_tests: int
    failed_tests: int
    error_tests: int
    false_positive_rate: float
    false_negative_rate: float
    test_results: List[Dict[str, Any]]
    overall_result: ValidationResult


class RuleValidator:
    """Validates VCS rules against known test cases."""
    
    def __init__(self):
        self.test_cases: Dict[str, List[TestCase]] = {}
        self._load_built_in_test_cases()
    
    def _load_built_in_test_cases(self) -> None:
        """Load built-in test cases for common rules."""
        
        # Line length test cases
        self.test_cases["S001"] = [
            TestCase(
                name="short_line",
                code="x = 1",
                expected_issues=0,
                expected_rule_ids=[],
                description="Short line should not trigger line length rule"
            ),
            TestCase(
                name="long_line",
                code="x = 'this is a very long line that exceeds the maximum line length limit and should trigger the line length rule'",
                expected_issues=1,
                expected_rule_ids=["S001"],
                description="Long line should trigger line length rule"
            ),
            TestCase(
                name="exactly_88_chars",
                code="x = 'this line is exactly eighty-eight characters long and should not trigger'",
                expected_issues=0,
                expected_rule_ids=[],
                description="Line exactly at limit should not trigger"
            )
        ]
        
        # Naming convention test cases
        self.test_cases["S002"] = [
            TestCase(
                name="good_snake_case",
                code="def good_function_name():\n    pass",
                expected_issues=0,
                expected_rule_ids=[],
                description="Snake case function name should pass"
            ),
            TestCase(
                name="bad_camel_case",
                code="def badCamelCase():\n    pass",
                expected_issues=1,
                expected_rule_ids=["S002"],
                description="CamelCase function name should fail"
            ),
            TestCase(
                name="good_variable",
                code="good_variable = 42",
                expected_issues=0,
                expected_rule_ids=[],
                description="Snake case variable should pass"
            )
        ]
        
        # ADV003 (AsyncAwait) test cases
        self.test_cases["ADV003"] = [
            TestCase(
                name="good_async_function",
                code="async def good_async():\n    await some_async_call()",
                expected_issues=0,
                expected_rule_ids=[],
                description="Async function with await should pass"
            ),
            TestCase(
                name="async_without_await",
                code="async def bad_async():\n    return 42",
                expected_issues=1,
                expected_rule_ids=["ADV003"],
                description="Async function without await should fail"
            ),
            TestCase(
                name="await_non_awaitable",
                code="async def bad_await():\n    await print('hello')",
                expected_issues=1,
                expected_rule_ids=["ADV003"],
                description="Awaiting non-awaitable should fail"
            ),
            TestCase(
                name="complex_attribute_access",
                code="async def complex_call():\n    await obj.attr.method()",
                expected_issues=0,
                expected_rule_ids=[],
                description="Complex attribute access should not crash"
            )
        ]
    
    async def validate_rule(self, rule: AnalysisRule) -> RuleValidationReport:
        """Validate a single rule against its test cases."""
        
        test_cases = self.test_cases.get(rule.rule_id, [])
        if not test_cases:
            # Create minimal test case if none exist
            test_cases = [TestCase(
                name="minimal_test",
                code="# minimal test\npass",
                expected_issues=0,
                expected_rule_ids=[],
                description="Minimal test case"
            )]
        
        total_tests = len(test_cases)
        passed_tests = 0
        failed_tests = 0
        error_tests = 0
        test_results = []
        
        false_positives = 0
        false_negatives = 0
        
        for test_case in test_cases:
            try:
                # Create temporary file for testing
                target = AnalysisTarget.from_content(
                    content=test_case.code,
                    file_path=Path(f"test_{test_case.name}.py")
                )
                context = AnalysisContext.create_default(EngineMode.STANDALONE)
                
                # Parse AST
                try:
                    ast_tree = ast.parse(test_case.code)
                except SyntaxError as e:
                    test_results.append({
                        "test_name": test_case.name,
                        "result": ValidationResult.ERROR.value,
                        "error": f"Syntax error: {e}",
                        "expected_issues": test_case.expected_issues,
                        "actual_issues": 0
                    })
                    error_tests += 1
                    continue
                
                # Run rule analysis
                issues = await rule.analyze(target, test_case.code, ast_tree, context)
                
                # Filter issues to only those from this rule
                rule_issues = [issue for issue in issues if issue.rule_id == rule.rule_id]
                actual_issues = len(rule_issues)
                
                # Determine test result
                if actual_issues == test_case.expected_issues:
                    result = ValidationResult.PASS
                    passed_tests += 1
                else:
                    result = ValidationResult.FAIL
                    failed_tests += 1
                    
                    # Track false positives/negatives
                    if actual_issues > test_case.expected_issues:
                        false_positives += actual_issues - test_case.expected_issues
                    else:
                        false_negatives += test_case.expected_issues - actual_issues
                
                test_results.append({
                    "test_name": test_case.name,
                    "result": result.value,
                    "expected_issues": test_case.expected_issues,
                    "actual_issues": actual_issues,
                    "description": test_case.description,
                    "issues": [
                        {
                            "line": issue.line,
                            "message": issue.message,
                            "rule_id": issue.rule_id
                        }
                        for issue in rule_issues
                    ]
                })
                
            except Exception as e:
                test_results.append({
                    "test_name": test_case.name,
                    "result": ValidationResult.ERROR.value,
                    "error": str(e),
                    "expected_issues": test_case.expected_issues,
                    "actual_issues": 0
                })
                error_tests += 1
        
        # Calculate rates
        total_expected = sum(tc.expected_issues for tc in test_cases)
        false_positive_rate = false_positives / max(total_expected, 1) if total_expected > 0 else 0.0
        false_negative_rate = false_negatives / max(total_expected, 1) if total_expected > 0 else 0.0
        
        # Determine overall result
        if error_tests > 0:
            overall_result = ValidationResult.ERROR
        elif failed_tests == 0:
            overall_result = ValidationResult.PASS
        else:
            overall_result = ValidationResult.FAIL
        
        return RuleValidationReport(
            rule_id=rule.rule_id,
            rule_name=rule.name,
            total_tests=total_tests,
            passed_tests=passed_tests,
            failed_tests=failed_tests,
            error_tests=error_tests,
            false_positive_rate=false_positive_rate,
            false_negative_rate=false_negative_rate,
            test_results=test_results,
            overall_result=overall_result
        )
    
    async def validate_all_rules(self, rules: List[AnalysisRule]) -> List[RuleValidationReport]:
        """Validate all rules and return reports."""
        reports = []
        
        for rule in rules:
            try:
                report = await self.validate_rule(rule)
                reports.append(report)
            except Exception as e:
                # Create error report for failed validation
                reports.append(RuleValidationReport(
                    rule_id=rule.rule_id,
                    rule_name=rule.name,
                    total_tests=0,
                    passed_tests=0,
                    failed_tests=0,
                    error_tests=1,
                    false_positive_rate=1.0,
                    false_negative_rate=0.0,
                    test_results=[{
                        "test_name": "validation_error",
                        "result": ValidationResult.ERROR.value,
                        "error": str(e)
                    }],
                    overall_result=ValidationResult.ERROR
                ))
        
        return reports
    
    def add_test_case(self, rule_id: str, test_case: TestCase) -> None:
        """Add a custom test case for a rule."""
        if rule_id not in self.test_cases:
            self.test_cases[rule_id] = []
        self.test_cases[rule_id].append(test_case)
