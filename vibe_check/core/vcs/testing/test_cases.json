{"S001": [{"name": "line_too_long", "code": "# This is a very long line that exceeds the maximum line length limit and should be flagged by the line length rule", "expected_issues": 1, "expected_rule_ids": ["S001"], "description": "Line exceeding 88 characters should be flagged", "should_pass": false}, {"name": "line_acceptable_length", "code": "# This is a normal length line", "expected_issues": 0, "expected_rule_ids": [], "description": "Normal length line should pass", "should_pass": true}, {"name": "multiple_long_lines", "code": "# This is a very long line that exceeds the maximum line length limit and should be flagged\n# Another very long line that also exceeds the maximum line length limit and should be flagged", "expected_issues": 1, "expected_rule_ids": ["S001"], "description": "Multiple long lines should be counted as one issue per file", "should_pass": false}], "S002": [{"name": "trailing_whitespace", "code": "print('hello')   \n", "expected_issues": 1, "expected_rule_ids": ["S002"], "description": "Line with trailing whitespace should be flagged", "should_pass": false}, {"name": "no_trailing_whitespace", "code": "print('hello')\n", "expected_issues": 0, "expected_rule_ids": [], "description": "Line without trailing whitespace should pass", "should_pass": true}], "S003": [{"name": "mixed_indentation", "code": "def func():\n    if True:\n\treturn 'mixed'", "expected_issues": 1, "expected_rule_ids": ["S003"], "description": "Mixed tabs and spaces should be flagged", "should_pass": false}, {"name": "consistent_spaces", "code": "def func():\n    if True:\n        return 'consistent'", "expected_issues": 0, "expected_rule_ids": [], "description": "Consistent space indentation should pass", "should_pass": true}], "SEC001": [{"name": "hardcoded_password", "code": "password = 'secret123'\napi_key = 'sk-1234567890abcdef'", "expected_issues": 2, "expected_rule_ids": ["SEC001"], "description": "Hardcoded secrets should be flagged", "should_pass": false}, {"name": "no_hardcoded_secrets", "code": "password = os.getenv('PASSWORD')\napi_key = config.get('api_key')", "expected_issues": 0, "expected_rule_ids": [], "description": "Environment variables should pass", "should_pass": true}], "SEC002": [{"name": "sql_injection_risk", "code": "query = f\"SELECT * FROM users WHERE id = {user_id}\"", "expected_issues": 1, "expected_rule_ids": ["SEC002"], "description": "SQL injection risk should be flagged", "should_pass": false}, {"name": "parameterized_query", "code": "query = \"SELECT * FROM users WHERE id = %s\"", "expected_issues": 0, "expected_rule_ids": [], "description": "Parameterized query should pass", "should_pass": true}], "C001": [{"name": "high_complexity_function", "code": "def complex_func(x):\n    if x > 10:\n        if x > 20:\n            if x > 30:\n                if x > 40:\n                    if x > 50:\n                        return 'very high'\n                    return 'high'\n                return 'medium'\n            return 'low'\n        return 'very low'\n    return 'minimal'", "expected_issues": 1, "expected_rule_ids": ["C001"], "description": "High complexity function should be flagged", "should_pass": false}, {"name": "simple_function", "code": "def simple_func(x):\n    return x * 2", "expected_issues": 0, "expected_rule_ids": [], "description": "Simple function should pass", "should_pass": true}], "C002": [{"name": "too_many_arguments", "code": "def func_with_many_args(a, b, c, d, e, f, g, h, i, j):\n    return a + b + c + d + e + f + g + h + i + j", "expected_issues": 1, "expected_rule_ids": ["C002"], "description": "Function with too many arguments should be flagged", "should_pass": false}, {"name": "reasonable_arguments", "code": "def func_with_few_args(a, b, c):\n    return a + b + c", "expected_issues": 0, "expected_rule_ids": [], "description": "Function with reasonable arguments should pass", "should_pass": true}], "D001": [{"name": "missing_docstring", "code": "def undocumented_function():\n    return 'no docs'", "expected_issues": 1, "expected_rule_ids": ["D001"], "description": "Function without docstring should be flagged", "should_pass": false}, {"name": "has_docstring", "code": "def documented_function():\n    \"\"\"This function is documented.\"\"\"\n    return 'has docs'", "expected_issues": 0, "expected_rule_ids": [], "description": "Function with docstring should pass", "should_pass": true}], "I001": [{"name": "unused_import", "code": "import os\nimport sys\nprint('hello')", "expected_issues": 2, "expected_rule_ids": ["I001"], "description": "Unused imports should be flagged", "should_pass": false}, {"name": "used_import", "code": "import os\nprint(os.getcwd())", "expected_issues": 0, "expected_rule_ids": [], "description": "Used import should pass", "should_pass": true}], "I002": [{"name": "wrong_import_order", "code": "import requests\nimport os\nimport sys", "expected_issues": 1, "expected_rule_ids": ["I002"], "description": "Wrong import order should be flagged", "should_pass": false}, {"name": "correct_import_order", "code": "import os\nimport sys\nimport requests", "expected_issues": 0, "expected_rule_ids": [], "description": "Correct import order should pass", "should_pass": true}], "T001": [{"name": "missing_type_hints", "code": "def add_numbers(a, b):\n    return a + b", "expected_issues": 1, "expected_rule_ids": ["T001"], "description": "Function without type hints should be flagged", "should_pass": false}, {"name": "has_type_hints", "code": "def add_numbers(a: int, b: int) -> int:\n    return a + b", "expected_issues": 0, "expected_rule_ids": [], "description": "Function with type hints should pass", "should_pass": true}], "T002": [{"name": "inconsistent_return_types", "code": "def inconsistent_func(x):\n    if x > 0:\n        return 'positive'\n    return 42", "expected_issues": 1, "expected_rule_ids": ["T002"], "description": "Inconsistent return types should be flagged", "should_pass": false}, {"name": "consistent_return_types", "code": "def consistent_func(x: int) -> str:\n    if x > 0:\n        return 'positive'\n    return 'non-positive'", "expected_issues": 0, "expected_rule_ids": [], "description": "Consistent return types should pass", "should_pass": true}]}