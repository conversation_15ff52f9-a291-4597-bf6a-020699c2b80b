"""
Rule Performance Monitoring
===========================

This module provides performance monitoring specifically for VCS rules,
including timing decorators, performance alerts, and rule benchmarking.
"""

import time
import functools
import logging
from collections import defaultdict, deque
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Callable, Deque
from pathlib import Path

logger = logging.getLogger(__name__)


@dataclass
class RulePerformanceMetric:
    """Performance metric for a single rule execution."""
    
    rule_id: str
    rule_name: str
    execution_time: float
    file_path: str
    file_size: int
    issues_found: int
    timestamp: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RulePerformanceStats:
    """Aggregated performance statistics for a rule."""
    
    rule_id: str
    rule_name: str
    total_executions: int
    total_time: float
    average_time: float
    min_time: float
    max_time: float
    p95_time: float
    total_issues_found: int
    average_issues_per_execution: float
    files_analyzed: int
    slow_executions: int  # Count of executions > threshold
    threshold_ms: float = 100.0  # Default threshold in milliseconds


class RulePerformanceMonitor:
    """
    Performance monitoring system specifically for VCS rules.
    
    Provides rule-level timing, alerts for slow rules, and performance analytics.
    """
    
    def __init__(self, history_size: int = 10000, alert_threshold_ms: float = 100.0):
        """
        Initialize rule performance monitor.
        
        Args:
            history_size: Maximum number of rule execution records to keep
            alert_threshold_ms: Threshold in milliseconds for slow rule alerts
        """
        self.history_size = history_size
        self.alert_threshold_ms = alert_threshold_ms
        
        # Performance data storage
        self.execution_history: Deque[RulePerformanceMetric] = deque(maxlen=history_size)
        self.rule_stats: Dict[str, RulePerformanceStats] = {}
        
        # Alert tracking
        self.slow_rule_alerts: Dict[str, int] = defaultdict(int)
        self.alert_callbacks: List[Callable[[str, float], None]] = []
        
        # Performance thresholds by rule category
        self.category_thresholds = {
            'style': 50.0,      # Style rules should be fast
            'security': 200.0,  # Security rules can be slower
            'complexity': 150.0, # Complexity analysis takes time
            'documentation': 75.0, # Doc rules should be fast
            'imports': 100.0,   # Import analysis moderate
            'types': 300.0      # Type checking can be slow
        }
    
    def track_rule_execution(self, rule_id: str, rule_name: str, execution_time: float,
                           file_path: str, file_size: int, issues_found: int,
                           metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        Track performance of a single rule execution.
        
        Args:
            rule_id: Unique rule identifier
            rule_name: Human-readable rule name
            execution_time: Time taken in seconds
            file_path: Path to analyzed file
            file_size: Size of analyzed file in bytes
            issues_found: Number of issues found by this rule
            metadata: Optional metadata (rule category, etc.)
        """
        # Create performance metric
        metric = RulePerformanceMetric(
            rule_id=rule_id,
            rule_name=rule_name,
            execution_time=execution_time,
            file_path=file_path,
            file_size=file_size,
            issues_found=issues_found,
            metadata=metadata or {}
        )
        
        # Store in history
        self.execution_history.append(metric)
        
        # Update rule statistics
        self._update_rule_stats(metric)
        
        # Check for performance alerts
        self._check_performance_alert(metric)
        
        logger.debug(f"Tracked rule {rule_id}: {execution_time*1000:.1f}ms on {Path(file_path).name}")
    
    def _update_rule_stats(self, metric: RulePerformanceMetric) -> None:
        """Update aggregated statistics for a rule."""
        rule_id = metric.rule_id
        
        if rule_id not in self.rule_stats:
            # Initialize stats for new rule
            self.rule_stats[rule_id] = RulePerformanceStats(
                rule_id=rule_id,
                rule_name=metric.rule_name,
                total_executions=0,
                total_time=0.0,
                average_time=0.0,
                min_time=float('inf'),
                max_time=0.0,
                p95_time=0.0,
                total_issues_found=0,
                average_issues_per_execution=0.0,
                files_analyzed=0,
                slow_executions=0,
                threshold_ms=self._get_threshold_for_rule(metric)
            )
        
        stats = self.rule_stats[rule_id]
        
        # Update basic stats
        stats.total_executions += 1
        stats.total_time += metric.execution_time
        stats.average_time = stats.total_time / stats.total_executions
        stats.min_time = min(stats.min_time, metric.execution_time)
        stats.max_time = max(stats.max_time, metric.execution_time)
        stats.total_issues_found += metric.issues_found
        stats.average_issues_per_execution = stats.total_issues_found / stats.total_executions
        
        # Count unique files
        if metric.file_path not in [m.file_path for m in self.execution_history if m.rule_id == rule_id]:
            stats.files_analyzed += 1
        
        # Count slow executions
        if metric.execution_time * 1000 > stats.threshold_ms:
            stats.slow_executions += 1
        
        # Calculate P95 time
        rule_times = [m.execution_time for m in self.execution_history if m.rule_id == rule_id]
        if rule_times:
            rule_times.sort()
            p95_index = int(0.95 * len(rule_times))
            stats.p95_time = rule_times[min(p95_index, len(rule_times) - 1)]
    
    def _get_threshold_for_rule(self, metric: RulePerformanceMetric) -> float:
        """Get performance threshold for a rule based on its category."""
        category = metric.metadata.get('category', 'style')
        return self.category_thresholds.get(category, self.alert_threshold_ms)
    
    def _check_performance_alert(self, metric: RulePerformanceMetric) -> None:
        """Check if rule execution warrants a performance alert."""
        execution_time_ms = metric.execution_time * 1000
        threshold = self._get_threshold_for_rule(metric)
        
        if execution_time_ms > threshold:
            self.slow_rule_alerts[metric.rule_id] += 1
            
            # Trigger alert callbacks
            for callback in self.alert_callbacks:
                try:
                    callback(metric.rule_id, execution_time_ms)
                except Exception as e:
                    logger.warning(f"Alert callback failed: {e}")
            
            logger.warning(
                f"Slow rule execution: {metric.rule_id} took {execution_time_ms:.1f}ms "
                f"(threshold: {threshold:.1f}ms) on {Path(metric.file_path).name}"
            )
    
    def add_alert_callback(self, callback: Callable[[str, float], None]) -> None:
        """Add a callback function for performance alerts."""
        self.alert_callbacks.append(callback)
    
    def get_rule_performance_report(self) -> Dict[str, Any]:
        """
        Generate comprehensive rule performance report.
        
        Returns:
            Detailed performance report for all rules
        """
        if not self.rule_stats:
            return {"status": "no_data", "rules_tracked": 0}
        
        # Sort rules by average execution time (slowest first)
        sorted_rules = sorted(
            self.rule_stats.values(),
            key=lambda x: x.average_time,
            reverse=True
        )
        
        # Identify problematic rules
        slow_rules = [r for r in sorted_rules if r.average_time * 1000 > r.threshold_ms]
        very_slow_rules = [r for r in sorted_rules if r.average_time * 1000 > r.threshold_ms * 2]
        
        # Calculate overall statistics
        total_executions = sum(r.total_executions for r in self.rule_stats.values())
        total_time = sum(r.total_time for r in self.rule_stats.values())
        avg_time_per_rule = total_time / len(self.rule_stats) if self.rule_stats else 0
        
        return {
            "summary": {
                "rules_tracked": len(self.rule_stats),
                "total_executions": total_executions,
                "total_time": total_time,
                "average_time_per_rule": avg_time_per_rule,
                "slow_rules_count": len(slow_rules),
                "very_slow_rules_count": len(very_slow_rules)
            },
            "slowest_rules": [
                {
                    "rule_id": r.rule_id,
                    "rule_name": r.rule_name,
                    "average_time_ms": r.average_time * 1000,
                    "max_time_ms": r.max_time * 1000,
                    "p95_time_ms": r.p95_time * 1000,
                    "total_executions": r.total_executions,
                    "slow_executions": r.slow_executions,
                    "threshold_ms": r.threshold_ms,
                    "issues_per_execution": r.average_issues_per_execution
                }
                for r in sorted_rules[:10]  # Top 10 slowest
            ],
            "problematic_rules": [
                {
                    "rule_id": r.rule_id,
                    "rule_name": r.rule_name,
                    "average_time_ms": r.average_time * 1000,
                    "threshold_ms": r.threshold_ms,
                    "slow_execution_rate": r.slow_executions / r.total_executions * 100,
                    "recommendation": self._get_optimization_recommendation(r)
                }
                for r in slow_rules
            ],
            "alert_summary": {
                "total_alerts": sum(self.slow_rule_alerts.values()),
                "rules_with_alerts": len(self.slow_rule_alerts),
                "most_problematic": max(self.slow_rule_alerts.items(), key=lambda x: x[1]) if self.slow_rule_alerts else None
            }
        }
    
    def _get_optimization_recommendation(self, stats: RulePerformanceStats) -> str:
        """Get optimization recommendation for a slow rule."""
        if stats.average_time * 1000 > stats.threshold_ms * 3:
            return "Critical: Consider algorithm optimization or caching"
        elif stats.average_time * 1000 > stats.threshold_ms * 2:
            return "High: Review implementation for performance bottlenecks"
        else:
            return "Medium: Monitor and consider minor optimizations"


def rule_performance_timer(rule_id: str, rule_name: str, category: str = "style"):
    """
    Decorator to automatically track rule performance.
    
    Args:
        rule_id: Unique rule identifier
        rule_name: Human-readable rule name
        category: Rule category for threshold determination
    
    Usage:
        @rule_performance_timer("S001", "Line Length", "style")
        def analyze_line_length(self, file_content, file_path):
            # Rule implementation
            pass
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Get performance monitor from engine (if available)
            monitor = None
            if hasattr(args[0], 'performance_monitor') and hasattr(args[0].performance_monitor, 'rule_monitor'):
                monitor = args[0].performance_monitor.rule_monitor
            
            start_time = time.time()
            
            try:
                # Execute the rule
                result = func(*args, **kwargs)
                
                # Track performance if monitor is available
                if monitor:
                    execution_time = time.time() - start_time
                    
                    # Extract file information from arguments
                    file_path = "unknown"
                    file_size = 0
                    issues_found = 0
                    
                    # Try to extract file path from common argument patterns
                    if len(args) > 1:
                        if hasattr(args[1], 'name'):  # File-like object
                            file_path = str(args[1].name)
                        elif isinstance(args[1], (str, Path)):  # String or Path
                            file_path = str(args[1])
                    
                    # Try to get file size
                    try:
                        if file_path != "unknown":
                            file_size = Path(file_path).stat().st_size
                    except:
                        pass
                    
                    # Count issues in result
                    if isinstance(result, list):
                        issues_found = len(result)
                    elif isinstance(result, dict) and 'issues' in result:
                        issues_found = len(result['issues'])
                    
                    monitor.track_rule_execution(
                        rule_id=rule_id,
                        rule_name=rule_name,
                        execution_time=execution_time,
                        file_path=file_path,
                        file_size=file_size,
                        issues_found=issues_found,
                        metadata={'category': category}
                    )
                
                return result
                
            except Exception as e:
                # Track failed execution
                if monitor:
                    execution_time = time.time() - start_time
                    monitor.track_rule_execution(
                        rule_id=rule_id,
                        rule_name=rule_name,
                        execution_time=execution_time,
                        file_path="error",
                        file_size=0,
                        issues_found=0,
                        metadata={'category': category, 'error': str(e)}
                    )
                raise
        
        return wrapper
    return decorator
