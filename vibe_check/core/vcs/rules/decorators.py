"""
Rule Decorators
===============

Decorators for automatic rule discovery and registration in the VCS engine.
"""

import logging
from typing import Dict, Type, Set, Optional, Any, List
from functools import wraps

from ..registry import AnalysisRule
from ..models import RuleCategory, IssueSeverity

logger = logging.getLogger(__name__)


# Global registry for auto-discovered rules
_DISCOVERED_RULES: Dict[str, Type[AnalysisRule]] = {}
_RULE_METADATA: Dict[str, Dict[str, Any]] = {}


def analysis_rule(
    rule_id: str,
    name: str,
    category: RuleCategory,
    severity: IssueSeverity = IssueSeverity.WARNING,
    enabled: bool = True,
    description: Optional[str] = None,
    tags: Optional[List[str]] = None,
    auto_fixable: bool = False,
    performance_impact: str = "low",  # low, medium, high
    dependencies: Optional[List[str]] = None,
    version: str = "1.0.0"
):
    """
    Decorator for automatic rule discovery and registration.
    
    This decorator marks a class as an analysis rule and automatically
    registers it for discovery by the rule loading system.
    
    Args:
        rule_id: Unique identifier for the rule (e.g., "S001", "C001")
        name: Human-readable name for the rule
        category: Rule category (style, security, complexity, etc.)
        severity: Default severity level for issues found by this rule
        enabled: Whether the rule is enabled by default
        description: Optional description of what the rule checks
        tags: Optional list of tags for categorization
        auto_fixable: Whether the rule can automatically fix issues
        performance_impact: Expected performance impact (low/medium/high)
        dependencies: Optional list of dependencies (modules, tools)
        version: Rule version for compatibility tracking
        
    Returns:
        Decorated class with auto-discovery metadata
        
    Example:
        @analysis_rule(
            rule_id="S001",
            name="Line Length Check",
            category=RuleCategory.STYLE,
            severity=IssueSeverity.WARNING,
            description="Check for lines exceeding maximum length",
            tags=["pep8", "formatting"],
            auto_fixable=True
        )
        class LineLengthRule(AnalysisRule):
            # Rule implementation
            pass
    """
    def decorator(cls: Type[AnalysisRule]) -> Type[AnalysisRule]:
        # Validate that the class is an AnalysisRule
        if not issubclass(cls, AnalysisRule):
            raise TypeError(f"@analysis_rule can only be applied to AnalysisRule subclasses, got {cls}")
        
        # Check for duplicate rule IDs
        if rule_id in _DISCOVERED_RULES:
            existing_class = _DISCOVERED_RULES[rule_id]
            raise ValueError(
                f"Duplicate rule ID '{rule_id}': already registered by {existing_class.__name__}, "
                f"cannot register {cls.__name__}"
            )
        
        # Store rule metadata
        metadata = {
            'rule_id': rule_id,
            'name': name,
            'category': category,
            'severity': severity,
            'enabled': enabled,
            'description': description or f"Analysis rule: {name}",
            'tags': tags or [],
            'auto_fixable': auto_fixable,
            'performance_impact': performance_impact,
            'dependencies': dependencies or [],
            'version': version,
            'class_name': cls.__name__,
            'module': cls.__module__
        }
        
        # Register the rule for discovery
        _DISCOVERED_RULES[rule_id] = cls
        _RULE_METADATA[rule_id] = metadata
        
        # Add metadata as class attributes for runtime access
        cls._rule_metadata = metadata
        cls._auto_discovered = True
        
        logger.debug(f"Auto-discovered rule: {rule_id} ({cls.__name__}) in {cls.__module__}")
        
        return cls
    
    return decorator


def get_discovered_rules() -> Dict[str, Type[AnalysisRule]]:
    """
    Get all auto-discovered rules.
    
    Returns:
        Dictionary mapping rule IDs to rule classes
    """
    return _DISCOVERED_RULES.copy()


def get_rule_metadata(rule_id: str) -> Optional[Dict[str, Any]]:
    """
    Get metadata for a specific rule.
    
    Args:
        rule_id: Rule identifier
        
    Returns:
        Rule metadata dictionary or None if not found
    """
    return _RULE_METADATA.get(rule_id)


def get_all_rule_metadata() -> Dict[str, Dict[str, Any]]:
    """
    Get metadata for all discovered rules.
    
    Returns:
        Dictionary mapping rule IDs to metadata dictionaries
    """
    return _RULE_METADATA.copy()


def filter_rules_by_category(category: RuleCategory) -> Dict[str, Type[AnalysisRule]]:
    """
    Filter discovered rules by category.
    
    Args:
        category: Rule category to filter by
        
    Returns:
        Dictionary of rules in the specified category
    """
    return {
        rule_id: rule_class
        for rule_id, rule_class in _DISCOVERED_RULES.items()
        if _RULE_METADATA[rule_id]['category'] == category
    }


def filter_rules_by_tags(tags: List[str], match_all: bool = False) -> Dict[str, Type[AnalysisRule]]:
    """
    Filter discovered rules by tags.
    
    Args:
        tags: List of tags to filter by
        match_all: If True, rule must have all tags; if False, any tag matches
        
    Returns:
        Dictionary of rules matching the tag criteria
    """
    result = {}
    
    for rule_id, rule_class in _DISCOVERED_RULES.items():
        rule_tags = set(_RULE_METADATA[rule_id]['tags'])
        search_tags = set(tags)
        
        if match_all:
            # Rule must have all specified tags
            if search_tags.issubset(rule_tags):
                result[rule_id] = rule_class
        else:
            # Rule must have at least one specified tag
            if search_tags.intersection(rule_tags):
                result[rule_id] = rule_class
    
    return result


def filter_rules_by_performance(max_impact: str) -> Dict[str, Type[AnalysisRule]]:
    """
    Filter discovered rules by performance impact.
    
    Args:
        max_impact: Maximum performance impact ("low", "medium", "high")
        
    Returns:
        Dictionary of rules with performance impact <= max_impact
    """
    impact_levels = {"low": 1, "medium": 2, "high": 3}
    max_level = impact_levels.get(max_impact, 3)
    
    return {
        rule_id: rule_class
        for rule_id, rule_class in _DISCOVERED_RULES.items()
        if impact_levels.get(_RULE_METADATA[rule_id]['performance_impact'], 1) <= max_level
    }


def clear_discovered_rules() -> None:
    """
    Clear all discovered rules (mainly for testing).
    """
    global _DISCOVERED_RULES, _RULE_METADATA
    _DISCOVERED_RULES.clear()
    _RULE_METADATA.clear()
    logger.debug("Cleared all discovered rules")


def validate_rule_dependencies() -> List[str]:
    """
    Validate that all rule dependencies are available.
    
    Returns:
        List of missing dependencies
    """
    missing_deps = []
    
    for rule_id, metadata in _RULE_METADATA.items():
        for dep in metadata['dependencies']:
            try:
                __import__(dep)
            except ImportError:
                missing_deps.append(f"{rule_id}: {dep}")
    
    return missing_deps


class RuleDiscoveryStats:
    """Statistics about rule discovery."""
    
    @staticmethod
    def get_stats() -> Dict[str, Any]:
        """Get comprehensive discovery statistics."""
        total_rules = len(_DISCOVERED_RULES)
        
        # Count by category
        category_counts = {}
        for metadata in _RULE_METADATA.values():
            category = metadata['category'].value
            category_counts[category] = category_counts.get(category, 0) + 1
        
        # Count by performance impact
        performance_counts = {}
        for metadata in _RULE_METADATA.values():
            impact = metadata['performance_impact']
            performance_counts[impact] = performance_counts.get(impact, 0) + 1
        
        # Count auto-fixable rules
        auto_fixable_count = sum(
            1 for metadata in _RULE_METADATA.values()
            if metadata['auto_fixable']
        )
        
        # Count enabled rules
        enabled_count = sum(
            1 for metadata in _RULE_METADATA.values()
            if metadata['enabled']
        )
        
        return {
            'total_rules': total_rules,
            'enabled_rules': enabled_count,
            'auto_fixable_rules': auto_fixable_count,
            'category_distribution': category_counts,
            'performance_distribution': performance_counts,
            'missing_dependencies': validate_rule_dependencies()
        }
