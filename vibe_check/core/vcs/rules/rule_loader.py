"""
Rule Loader
===========

Loads and registers all built-in analysis rules for the VCS engine.
Supports both auto-discovery (preferred) and manual registration (fallback).
"""

import logging
from typing import List, Dict, Type, Optional

from ..registry import RuleRegistry, AnalysisRule
from ...constants import AnalysisThresholds
from .discovery import discover_all_rules, get_discovery_engine
from .decorators import get_discovered_rules, RuleDiscoveryStats

# Import all rule classes
from .style_rules import (
    LineLengthRule, TrailingWhitespaceRule, IndentationRule,
    NamingConventionRule, BlankLineRule, MultipleStatementsRule
)
from .security_rules import (
    HardcodedPasswordRule, SqlInjectionRule, UnsafeEvalRule,
    WeakCryptographyRule, InsecureRandomRule
)
from .complexity_rules import (
    CyclomaticComplexityRule, FunctionLengthRule, NestedComplexityRule,
    ParameterCountRule, ClassComplexityRule
)
from .documentation_rules import (
    MissingDocstringRule, DocstringFormatRule, CommentQualityRule,
    TypeHintDocumentationRule
)
from .import_rules import (
    UnusedImportRule, ImportOrderRule, WildcardImportRule,
    RelativeImportRule, CircularImportRule, ImportGroupingRule
)
from .type_rules import (
    MissingTypeHintsRule, InconsistentTypeHintsRule, ComplexTypeHintsRule,
    TypeAliasRule, GenericTypeRule, OptionalTypeRule,
    EnhancedTypeCheckingRule, MypyIntegrationRule
)
from .framework_rules import DjangoRuleSet, FlaskRuleSet, FastAPIRuleSet, FrameworkDetector
from .advanced_python_rules import (
    DecoratorUsageRule, ContextManagerRule, AsyncAwaitRule,
    MetaclassRule, GeneratorRule
)
from .performance_rules import (
    LoopOptimizationRule, DataStructureRule, StringOptimizationRule,
    ImportOptimizationRule, MemoryOptimizationRule
)

logger = logging.getLogger(__name__)


def load_built_in_rules(registry: RuleRegistry, use_auto_discovery: bool = True) -> int:
    """
    Load all built-in analysis rules into the registry.

    Args:
        registry: Rule registry to load rules into
        use_auto_discovery: Whether to use auto-discovery (True) or manual loading (False)

    Returns:
        Number of rules successfully loaded
    """
    if use_auto_discovery:
        return _load_rules_with_auto_discovery(registry)
    else:
        return _load_rules_manually(registry)


def _load_rules_with_auto_discovery(registry: RuleRegistry) -> int:
    """
    Load rules using automatic discovery system.

    Args:
        registry: RuleRegistry instance to load rules into

    Returns:
        Number of rules successfully loaded
    """
    logger.info("Loading rules using auto-discovery system")

    try:
        # Discover all rules in the rules package
        discovered_rules = discover_all_rules()

        if not discovered_rules:
            logger.warning("No rules discovered via auto-discovery, falling back to manual loading")
            return _load_rules_manually(registry)

        # Load discovered rules
        loaded_count = 0
        failed_rules = []

        for rule_id, rule_class in discovered_rules.items():
            try:
                # Instantiate the rule
                rule = rule_class()

                # Register with the registry
                registry.register_rule(rule)
                loaded_count += 1

                logger.debug(f"Auto-discovered and loaded rule: {rule_id} ({rule.name})")

            except Exception as e:
                logger.error(f"Failed to load auto-discovered rule {rule_id} ({rule_class.__name__}): {e}")
                failed_rules.append(rule_id)

        # Get discovery report
        discovery_engine = get_discovery_engine()
        report = discovery_engine.get_discovery_report()

        logger.info(f"Auto-discovery completed: {loaded_count} rules loaded from {report['discovery_stats']['modules_scanned']} modules")

        if failed_rules:
            logger.warning(f"Failed to load {len(failed_rules)} auto-discovered rules: {', '.join(failed_rules)}")

        # Log discovery statistics
        stats = RuleDiscoveryStats.get_stats()
        logger.info(f"Rule distribution: {stats['category_distribution']}")

        return loaded_count

    except Exception as e:
        logger.error(f"Auto-discovery failed: {e}")
        logger.info("Falling back to manual rule loading")
        return _load_rules_manually(registry)


def _load_rules_manually(registry: RuleRegistry) -> int:
    """
    Load rules using manual registration (fallback method).

    Args:
        registry: Rule registry to load rules into

    Returns:
        Number of rules successfully loaded
    """
    logger.info("Loading rules using manual registration")

    # Define all built-in rules (manual list)
    rule_classes = [
        # Style Rules (6 rules)
        LineLengthRule,
        TrailingWhitespaceRule,
        IndentationRule,
        NamingConventionRule,
        BlankLineRule,
        MultipleStatementsRule,
        
        # Security Rules (5 rules)
        HardcodedPasswordRule,
        SqlInjectionRule,
        UnsafeEvalRule,
        WeakCryptographyRule,
        InsecureRandomRule,
        
        # Complexity Rules (5 rules)
        CyclomaticComplexityRule,
        FunctionLengthRule,
        NestedComplexityRule,
        ParameterCountRule,
        ClassComplexityRule,
        
        # Documentation Rules (4 rules)
        MissingDocstringRule,
        DocstringFormatRule,
        CommentQualityRule,
        TypeHintDocumentationRule,
        
        # Import Rules (6 rules)
        UnusedImportRule,
        ImportOrderRule,
        WildcardImportRule,
        RelativeImportRule,
        CircularImportRule,
        ImportGroupingRule,
        
        # Type Rules (8 rules)
        MissingTypeHintsRule,
        InconsistentTypeHintsRule,
        ComplexTypeHintsRule,
        TypeAliasRule,
        GenericTypeRule,
        OptionalTypeRule,
        EnhancedTypeCheckingRule,
        MypyIntegrationRule,

        # Advanced Python Rules (5 rules)
        DecoratorUsageRule,
        ContextManagerRule,
        AsyncAwaitRule,
        MetaclassRule,
        GeneratorRule,

        # Performance Rules (5 rules)
        LoopOptimizationRule,
        DataStructureRule,
        StringOptimizationRule,
        ImportOptimizationRule,
        MemoryOptimizationRule,
    ]
    
    loaded_count = 0
    failed_rules = []
    
    for rule_class in rule_classes:
        try:
            # Instantiate the rule
            rule = rule_class()
            
            # Register with the registry
            registry.register_rule(rule)
            loaded_count += 1
            
            logger.debug(f"Loaded rule: {rule.rule_id} ({rule.name})")
            
        except Exception as e:
            logger.error(f"Failed to load rule {rule_class.__name__}: {e}")
            failed_rules.append(rule_class.__name__)
    
    logger.info(f"Loaded {loaded_count} built-in rules")
    
    if failed_rules:
        logger.warning(f"Failed to load {len(failed_rules)} rules: {', '.join(failed_rules)}")
    
    return loaded_count


def get_rule_summary() -> dict:
    """
    Get a summary of all available built-in rules.
    
    Returns:
        Dictionary with rule categories and counts
    """
    from ..models import RuleCategory
    
    rule_counts = {
        RuleCategory.STYLE: 7,  # +1 for DecoratorUsageRule
        RuleCategory.SECURITY: 5,
        RuleCategory.COMPLEXITY: 13,  # +4 for ContextManagerRule, AsyncAwaitRule, MetaclassRule, GeneratorRule, +4 for performance rules
        RuleCategory.DOCS: 4,
        RuleCategory.IMPORTS: 7,  # +1 for ImportOptimizationRule
        RuleCategory.TYPES: 8,
    }
    
    total_rules = sum(rule_counts.values())
    
    return {
        "total_rules": total_rules,
        "categories": {
            category.value: count 
            for category, count in rule_counts.items()
        },
        "rule_details": {
            "style": [
                "S001: Line Length",
                "S002: Trailing Whitespace",
                "S003: Indentation",
                "S004: Naming Convention",
                "S005: Blank Lines",
                "S006: Multiple Statements",
                "ADV001: Decorator Usage Analysis"
            ],
            "security": [
                "SEC001: Hardcoded Passwords",
                "SEC002: SQL Injection",
                "SEC003: Unsafe Eval",
                "SEC004: Weak Cryptography",
                "SEC005: Insecure Random"
            ],
            "complexity": [
                "C001: Cyclomatic Complexity",
                "C002: Function Length",
                "C003: Nested Complexity",
                "C004: Parameter Count",
                "C005: Class Complexity",
                "ADV002: Context Manager Analysis",
                "ADV003: Async/Await Analysis",
                "ADV004: Metaclass Analysis",
                "ADV005: Generator Analysis",
                "PERF001: Loop Optimization",
                "PERF002: Data Structure Optimization",
                "PERF003: String Optimization",
                "PERF005: Memory Optimization"
            ],
            "documentation": [
                "D001: Missing Docstring",
                "D002: Docstring Format",
                "D003: Comment Quality",
                "D004: Type Hint Documentation"
            ],
            "imports": [
                "I001: Unused Import",
                "I002: Import Order",
                "I003: Wildcard Import",
                "I004: Relative Import",
                "I005: Circular Import",
                "I006: Import Grouping",
                "PERF004: Import Optimization"
            ],
            "types": [
                "T001: Missing Type Hints",
                "T002: Inconsistent Type Hints",
                "T003: Complex Type Hints",
                "T004: Type Alias",
                "T005: Generic Type Usage",
                "T006: Optional Type Usage",
                "T007: Enhanced Type Checking",
                "T008: Mypy Integration"
            ]
        }
    }


def configure_default_rules(registry: RuleRegistry, config: dict = None) -> None:
    """
    Configure default settings for built-in rules.
    
    Args:
        registry: RuleRegistry instance
        config: Optional configuration dictionary
    """
    if config is None:
        config = {}
    
    # Default rule configurations
    default_configs = {
        # Style rules
        "S001": {"max_line_length": config.get("max_line_length", AnalysisThresholds.MAX_LINE_LENGTH)},
        "S004": {"enforce_naming": config.get("enforce_naming", True)},

        # Complexity rules
        "C001": {"complexity_threshold": config.get("complexity_threshold", AnalysisThresholds.CYCLOMATIC_COMPLEXITY)},
        "C002": {"max_function_lines": config.get("max_function_lines", AnalysisThresholds.MAX_FUNCTION_LENGTH)},
        "C003": {"max_nesting_depth": config.get("max_nesting_depth", AnalysisThresholds.MAX_NESTING_DEPTH)},
        "C004": {"max_parameters": config.get("max_parameters", AnalysisThresholds.MAX_PARAMETERS)},
        "C005": {"max_methods": config.get("max_methods", AnalysisThresholds.MAX_METHODS_PER_CLASS)},
        
        # Documentation rules
        "D001": {"require_module_docstring": config.get("require_module_docstring", True)},
        "D002": {"enforce_docstring_format": config.get("enforce_docstring_format", True)},
        
        # Import rules
        "I001": {"ignore_star_imports": config.get("ignore_star_imports", False)},
        "I002": {"enforce_import_order": config.get("enforce_import_order", True)},
        
        # Type rules
        "T001": {"require_return_type": config.get("require_return_type", True)},
        "T002": {"enforce_type_consistency": config.get("enforce_type_consistency", True)},
    }
    
    # Apply configurations
    for rule_id, rule_config in default_configs.items():
        try:
            registry.configure_rule(rule_id, rule_config)
            logger.debug(f"Configured rule {rule_id}: {rule_config}")
        except Exception as e:
            logger.warning(f"Failed to configure rule {rule_id}: {e}")


def get_rules_by_category(registry: RuleRegistry, category_name: str) -> List[AnalysisRule]:
    """
    Get all rules for a specific category.
    
    Args:
        registry: RuleRegistry instance
        category_name: Category name (style, security, complexity, docs, imports, types)
        
    Returns:
        List of rules in the category
    """
    from ..models import RuleCategory
    
    try:
        category = RuleCategory(category_name.lower())
        return registry.get_rules_for_category(category)
    except ValueError:
        logger.error(f"Invalid category: {category_name}")
        return []


def validate_rule_dependencies(registry: RuleRegistry) -> List[str]:
    """
    Validate that all rule dependencies are satisfied.
    
    Args:
        registry: RuleRegistry instance
        
    Returns:
        List of validation errors (empty if all dependencies are satisfied)
    """
    errors = []
    
    for rule_id, rule in registry.rules.items():
        for dep_id in rule.dependencies:
            if dep_id not in registry.rules:
                errors.append(f"Rule {rule_id} depends on missing rule {dep_id}")
    
    return errors


def get_rule_statistics(registry: RuleRegistry) -> dict:
    """
    Get statistics about loaded rules.
    
    Args:
        registry: RuleRegistry instance
        
    Returns:
        Dictionary with rule statistics
    """
    from ..models import RuleCategory
    
    stats = registry.get_registry_stats()
    
    # Add severity breakdown
    severity_counts = {}
    for rule in registry.rules.values():
        severity = rule.severity.value
        severity_counts[severity] = severity_counts.get(severity, 0) + 1
    
    stats["severity_breakdown"] = severity_counts
    
    # Add auto-fixable count
    auto_fixable_count = sum(1 for rule in registry.rules.values() 
                           if hasattr(rule, 'auto_fixable') and rule.auto_fixable)
    stats["auto_fixable_rules"] = auto_fixable_count
    
    return stats


def load_framework_rules(registry: RuleRegistry, project_root=None) -> int:
    """
    Load framework-specific rules based on detected frameworks.

    Args:
        registry: RuleRegistry instance to load rules into
        project_root: Optional project root for framework detection

    Returns:
        Number of framework rules successfully loaded
    """
    loaded_count = 0

    if project_root:
        # Detect frameworks in the project
        detector = FrameworkDetector()
        framework_scores = detector.detect_project_framework(project_root)

        # Load rules for detected frameworks (confidence > 0.3)
        for framework, confidence in framework_scores.items():
            if confidence > 0.3:
                try:
                    if framework.value == "django":
                        rules = DjangoRuleSet.get_rules()
                    elif framework.value == "flask":
                        rules = FlaskRuleSet.get_rules()
                    elif framework.value == "fastapi":
                        rules = FastAPIRuleSet.get_rules()
                    else:
                        continue

                    for rule in rules:
                        registry.register_rule(rule)
                        loaded_count += 1
                        logger.debug(f"Loaded {framework.value} rule: {rule.rule_id}")

                    logger.info(f"Loaded {len(rules)} {framework.value} rules (confidence: {confidence:.2f})")

                except Exception as e:
                    logger.error(f"Failed to load {framework.value} rules: {e}")
    else:
        # Load all framework rules if no project root provided
        try:
            all_framework_rules = []
            all_framework_rules.extend(DjangoRuleSet.get_rules())
            all_framework_rules.extend(FlaskRuleSet.get_rules())
            all_framework_rules.extend(FastAPIRuleSet.get_rules())

            for rule in all_framework_rules:
                registry.register_rule(rule)
                loaded_count += 1
                logger.debug(f"Loaded framework rule: {rule.rule_id}")

            logger.info(f"Loaded {loaded_count} framework rules (all frameworks)")

        except Exception as e:
            logger.error(f"Failed to load framework rules: {e}")

    return loaded_count


def get_framework_summary(project_root=None) -> dict:
    """
    Get summary of framework detection and available rules.

    Args:
        project_root: Optional project root for framework detection

    Returns:
        Dictionary with framework detection results and rule counts
    """
    summary = {
        "framework_detection": {},
        "available_frameworks": {
            "django": len(DjangoRuleSet.get_rules()),
            "flask": len(FlaskRuleSet.get_rules()),
            "fastapi": len(FastAPIRuleSet.get_rules())
        },
        "total_framework_rules": (
            len(DjangoRuleSet.get_rules()) +
            len(FlaskRuleSet.get_rules()) +
            len(FastAPIRuleSet.get_rules())
        )
    }

    if project_root:
        detector = FrameworkDetector()
        framework_summary = detector.get_framework_summary(project_root)
        summary["framework_detection"] = framework_summary

    return summary
