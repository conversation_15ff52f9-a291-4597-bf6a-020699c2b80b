"""
Style Analysis Rules
====================

PEP 8 style compliance and formatting rules for Python code.
"""

import ast
import re
from typing import List, Dict, Any

from ..models import RuleCategory, IssueSeverity, AnalysisTarget, AnalysisIssue, AnalysisContext
from ..registry import AnalysisRule
from ..config.rule_config import LineLengthConfig, create_rule_config
from .decorators import analysis_rule


@analysis_rule(
    rule_id="S001",
    name="Line Length Check",
    category=RuleCategory.STYLE,
    severity=IssueSeverity.WARNING,
    description="Check for lines exceeding maximum length (default: 100 characters)",
    tags=["pep8", "formatting", "style"],
    auto_fixable=False,
    performance_impact="low"
)
class LineLengthRule(AnalysisRule):
    """Check for lines that exceed maximum length."""

    def __init__(self) -> None:
        super().__init__(
            rule_id="S001",
            category=RuleCategory.STYLE,
            name="Line Length",
            description="Lines should not exceed maximum length (default: 100 characters)",
            severity=IssueSeverity.WARNING
        )
        # Initialize typed configuration
        typed_config = create_rule_config("S001", self.config)
        assert isinstance(typed_config, LineLengthConfig), f"Expected LineLengthConfig, got {type(typed_config)}"
        self._typed_config: LineLengthConfig = typed_config

    def get_typed_config(self) -> LineLengthConfig:
        """Get typed configuration for this rule."""
        return self._typed_config

    def update_config(self, new_config: Dict[str, Any]) -> None:
        """Update rule configuration with validation."""
        # Update the base config dictionary
        self.config.update(new_config)
        # Recreate typed configuration with validation
        typed_config = create_rule_config("S001", self.config)
        assert isinstance(typed_config, LineLengthConfig), f"Expected LineLengthConfig, got {type(typed_config)}"
        self._typed_config = typed_config

    async def analyze(self, target: AnalysisTarget, content: str,
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        # Use typed configuration
        config = self.get_typed_config()
        max_length = config.max_line_length
        ignore_urls = config.ignore_urls
        ignore_imports = config.ignore_imports
        ignore_comments = config.ignore_comments

        long_lines: List[Dict[str, Any]] = []

        lines = content.split('\n')
        for line_num, line in enumerate(lines, 1):
            # Skip empty lines
            if not line.strip():
                continue

            # Apply ignore rules
            if ignore_comments and line.strip().startswith('#'):
                continue

            if ignore_imports and (line.strip().startswith('import ') or line.strip().startswith('from ')):
                continue

            if ignore_urls and ('http://' in line or 'https://' in line):
                continue

            if len(line) > max_length:
                long_lines.append({
                    "line": line_num,
                    "length": len(line),
                    "content": line[:50] + "..." if len(line) > 50 else line
                })

        # Return single consolidated issue if any long lines found
        if long_lines:
            total_violations = len(long_lines)
            line_list = ", ".join([f"line {ll['line']} ({ll['length']} chars)" for ll in long_lines[:5]])
            if total_violations > 5:
                line_list += f" and {total_violations - 5} more"

            first_line_num: int = long_lines[0]["line"]  # Type annotation for clarity
            return [self.create_issue(
                line=first_line_num,  # Point to first violation
                column=max_length,
                message=f"{total_violations} lines exceed {max_length} characters: {line_list}",
                fix_suggestion="Break long lines into multiple lines, use shorter variable names, or configure max_line_length",
                metadata={
                    "max_length": max_length,
                    "total_violations": total_violations,
                    "long_lines": long_lines
                }
            )]

        return []


class TrailingWhitespaceRule(AnalysisRule):
    """Check for trailing whitespace."""
    
    def __init__(self):
        super().__init__(
            rule_id="S002",
            category=RuleCategory.STYLE,
            name="Trailing Whitespace",
            description="Lines should not have trailing whitespace",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        
        lines = content.split('\n')
        for line_num, line in enumerate(lines, 1):
            if line.rstrip() != line:
                # Count trailing whitespace more accurately
                stripped = line.rstrip()
                trailing_part = line[len(stripped):]
                trailing_count = len(trailing_part)
                issues.append(self.create_issue(
                    line=line_num,
                    column=len(stripped),
                    message=f"Trailing whitespace ({trailing_count} characters)",
                    fix_suggestion="Remove trailing whitespace",
                    auto_fixable=True,
                    metadata={"trailing_count": trailing_count}
                ))
        
        return issues


class IndentationRule(AnalysisRule):
    """Check for consistent indentation (4 spaces)."""
    
    def __init__(self):
        super().__init__(
            rule_id="S003",
            category=RuleCategory.STYLE,
            name="Indentation",
            description="Use 4 spaces for indentation, not tabs",
            severity=IssueSeverity.ERROR
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        
        lines = content.split('\n')
        for line_num, line in enumerate(lines, 1):
            if not line.strip():  # Skip empty lines
                continue
            
            # Check for tabs
            if '\t' in line:
                tab_pos = line.find('\t')
                issues.append(self.create_issue(
                    line=line_num,
                    column=tab_pos,
                    message="Use spaces instead of tabs for indentation",
                    fix_suggestion="Replace tabs with 4 spaces",
                    auto_fixable=True,
                    severity=IssueSeverity.ERROR
                ))
            
            # Check for inconsistent indentation
            leading_spaces = len(line) - len(line.lstrip(' '))
            if leading_spaces > 0 and leading_spaces % 4 != 0:
                issues.append(self.create_issue(
                    line=line_num,
                    column=0,
                    message=f"Indentation is not a multiple of 4 (found {leading_spaces} spaces)",
                    fix_suggestion="Use 4 spaces per indentation level",
                    auto_fixable=True,
                    metadata={"spaces": leading_spaces}
                ))
        
        return issues


class NamingConventionRule(AnalysisRule):
    """Check Python naming conventions."""
    
    def __init__(self):
        super().__init__(
            rule_id="S004",
            category=RuleCategory.STYLE,
            name="Naming Convention",
            description="Follow Python naming conventions (PEP 8)",
            severity=IssueSeverity.WARNING
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        
        class NamingVisitor(ast.NodeVisitor):
            def visit_FunctionDef(self, node):
                # Function names should be snake_case
                if not self._is_snake_case(node.name) and not node.name.startswith('_'):
                    issues.append(self.create_issue(
                        line=node.lineno,
                        column=node.col_offset,
                        message=f"Function name '{node.name}' should be snake_case",
                        fix_suggestion=f"Rename to '{self._to_snake_case(node.name)}'",
                        metadata={"name": node.name, "suggested": self._to_snake_case(node.name)}
                    ))
                self.generic_visit(node)
            
            def visit_ClassDef(self, node):
                # Class names should be PascalCase
                if not self._is_pascal_case(node.name):
                    issues.append(self.create_issue(
                        line=node.lineno,
                        column=node.col_offset,
                        message=f"Class name '{node.name}' should be PascalCase",
                        fix_suggestion=f"Rename to '{self._to_pascal_case(node.name)}'",
                        metadata={"name": node.name, "suggested": self._to_pascal_case(node.name)}
                    ))
                self.generic_visit(node)
            
            def visit_Name(self, node):
                # Variable names should be snake_case (limited to prevent spam)
                if isinstance(node.ctx, ast.Store) and len(node.id) > 1:
                    # Limit to max 5 naming issues per file to prevent spam
                    current_naming_issues = len([i for i in issues if 'should be snake_case' in i.message])
                    if current_naming_issues < 5:
                        if not self._is_snake_case(node.id) and not node.id.isupper():
                            issues.append(self.create_issue(
                                line=node.lineno,
                                column=node.col_offset,
                                message=f"Variable name '{node.id}' should be snake_case",
                                fix_suggestion=f"Rename to '{self._to_snake_case(node.id)}'",
                                severity=IssueSeverity.INFO,
                                metadata={"name": node.id, "suggested": self._to_snake_case(node.id)}
                            ))
                self.generic_visit(node)
            
            def _is_snake_case(self, name):
                return re.match(r'^[a-z_][a-z0-9_]*$', name) is not None
            
            def _is_pascal_case(self, name):
                return re.match(r'^[A-Z][a-zA-Z0-9]*$', name) is not None
            
            def _to_snake_case(self, name):
                # Simple conversion to snake_case
                s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
                return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()
            
            def _to_pascal_case(self, name):
                # Simple conversion to PascalCase
                return ''.join(word.capitalize() for word in name.split('_'))
        
        visitor = NamingVisitor()
        visitor.create_issue = self.create_issue
        visitor.visit(ast_tree)
        
        return issues


class BlankLineRule(AnalysisRule):
    """Check for proper blank line usage."""
    
    def __init__(self):
        super().__init__(
            rule_id="S005",
            category=RuleCategory.STYLE,
            name="Blank Lines",
            description="Use blank lines to separate logical sections",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        lines = content.split('\n')
        
        # Check for multiple consecutive blank lines
        blank_count = 0
        for line_num, line in enumerate(lines, 1):
            if not line.strip():
                blank_count += 1
            else:
                if blank_count > 2:
                    issues.append(self.create_issue(
                        line=line_num - blank_count,
                        column=0,
                        message=f"Too many blank lines ({blank_count}), maximum 2 allowed",
                        fix_suggestion="Remove extra blank lines",
                        auto_fixable=True,
                        metadata={"blank_count": blank_count}
                    ))
                blank_count = 0
        
        return issues


class MultipleStatementsRule(AnalysisRule):
    """Check for multiple statements on one line."""
    
    def __init__(self):
        super().__init__(
            rule_id="S006",
            category=RuleCategory.STYLE,
            name="Multiple Statements",
            description="Avoid multiple statements on one line",
            severity=IssueSeverity.WARNING
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        
        lines = content.split('\n')
        for line_num, line in enumerate(lines, 1):
            stripped = line.strip()
            if not stripped or stripped.startswith('#'):
                continue

            # Remove comments from the line before checking for semicolons
            # Find the first # that's not in a string
            code_part = line
            in_string = False
            string_char = None
            for i, char in enumerate(line):
                if char in ['"', "'"] and (i == 0 or line[i-1] != '\\'):
                    if not in_string:
                        in_string = True
                        string_char = char
                    elif char == string_char:
                        in_string = False
                        string_char = None
                elif char == '#' and not in_string:
                    code_part = line[:i]
                    break

            # Count semicolons in code part only
            semicolon_count = code_part.count(';')
            if semicolon_count > 0:
                semicolon_pos = code_part.find(';')
                issues.append(self.create_issue(
                    line=line_num,
                    column=semicolon_pos,
                    message="Multiple statements on one line",
                    fix_suggestion="Put each statement on a separate line",
                    auto_fixable=True,
                    metadata={"semicolon_count": semicolon_count}
                ))
        
        return issues
