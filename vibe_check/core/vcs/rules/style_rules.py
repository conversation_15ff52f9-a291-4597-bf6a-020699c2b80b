"""
Style Analysis Rules
====================

PEP 8 style compliance and formatting rules for Python code.
"""

import ast
import re
from typing import List

from ..models import RuleCategory, IssueSeverity, AnalysisTarget, AnalysisIssue, AnalysisContext
from ..registry import AnalysisRule


class LineLengthRule(AnalysisRule):
    """Check for lines that exceed maximum length."""
    
    def __init__(self):
        super().__init__(
            rule_id="S001",
            category=RuleCategory.STYLE,
            name="Line Length",
            description="Lines should not exceed maximum length (default: 88 characters)",
            severity=IssueSeverity.WARNING
        )
    
    async def analyze(self, target: AnalysisTarget, content: str,
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        max_length = self.config.get("max_line_length", 88)

        lines = content.split('\n')
        for line_num, line in enumerate(lines, 1):
            # Skip empty lines
            if not line.strip():
                continue

            if len(line) > max_length:
                issues.append(self.create_issue(
                    line=line_num,
                    column=max_length,
                    message=f"Line too long ({len(line)} > {max_length} characters)",
                    fix_suggestion="Break line into multiple lines or use shorter variable names",
                    metadata={"actual_length": len(line), "max_length": max_length}
                ))

        return issues


class TrailingWhitespaceRule(AnalysisRule):
    """Check for trailing whitespace."""
    
    def __init__(self):
        super().__init__(
            rule_id="S002",
            category=RuleCategory.STYLE,
            name="Trailing Whitespace",
            description="Lines should not have trailing whitespace",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        
        lines = content.split('\n')
        for line_num, line in enumerate(lines, 1):
            if line.rstrip() != line:
                # Count trailing whitespace more accurately
                stripped = line.rstrip()
                trailing_part = line[len(stripped):]
                trailing_count = len(trailing_part)
                issues.append(self.create_issue(
                    line=line_num,
                    column=len(stripped),
                    message=f"Trailing whitespace ({trailing_count} characters)",
                    fix_suggestion="Remove trailing whitespace",
                    auto_fixable=True,
                    metadata={"trailing_count": trailing_count}
                ))
        
        return issues


class IndentationRule(AnalysisRule):
    """Check for consistent indentation (4 spaces)."""
    
    def __init__(self):
        super().__init__(
            rule_id="S003",
            category=RuleCategory.STYLE,
            name="Indentation",
            description="Use 4 spaces for indentation, not tabs",
            severity=IssueSeverity.ERROR
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        
        lines = content.split('\n')
        for line_num, line in enumerate(lines, 1):
            if not line.strip():  # Skip empty lines
                continue
            
            # Check for tabs
            if '\t' in line:
                tab_pos = line.find('\t')
                issues.append(self.create_issue(
                    line=line_num,
                    column=tab_pos,
                    message="Use spaces instead of tabs for indentation",
                    fix_suggestion="Replace tabs with 4 spaces",
                    auto_fixable=True,
                    severity=IssueSeverity.ERROR
                ))
            
            # Check for inconsistent indentation
            leading_spaces = len(line) - len(line.lstrip(' '))
            if leading_spaces > 0 and leading_spaces % 4 != 0:
                issues.append(self.create_issue(
                    line=line_num,
                    column=0,
                    message=f"Indentation is not a multiple of 4 (found {leading_spaces} spaces)",
                    fix_suggestion="Use 4 spaces per indentation level",
                    auto_fixable=True,
                    metadata={"spaces": leading_spaces}
                ))
        
        return issues


class NamingConventionRule(AnalysisRule):
    """Check Python naming conventions."""
    
    def __init__(self):
        super().__init__(
            rule_id="S004",
            category=RuleCategory.STYLE,
            name="Naming Convention",
            description="Follow Python naming conventions (PEP 8)",
            severity=IssueSeverity.WARNING
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        
        class NamingVisitor(ast.NodeVisitor):
            def visit_FunctionDef(self, node):
                # Function names should be snake_case
                if not self._is_snake_case(node.name) and not node.name.startswith('_'):
                    issues.append(self.create_issue(
                        line=node.lineno,
                        column=node.col_offset,
                        message=f"Function name '{node.name}' should be snake_case",
                        fix_suggestion=f"Rename to '{self._to_snake_case(node.name)}'",
                        metadata={"name": node.name, "suggested": self._to_snake_case(node.name)}
                    ))
                self.generic_visit(node)
            
            def visit_ClassDef(self, node):
                # Class names should be PascalCase
                if not self._is_pascal_case(node.name):
                    issues.append(self.create_issue(
                        line=node.lineno,
                        column=node.col_offset,
                        message=f"Class name '{node.name}' should be PascalCase",
                        fix_suggestion=f"Rename to '{self._to_pascal_case(node.name)}'",
                        metadata={"name": node.name, "suggested": self._to_pascal_case(node.name)}
                    ))
                self.generic_visit(node)
            
            def visit_Name(self, node):
                # Variable names should be snake_case (basic check)
                if isinstance(node.ctx, ast.Store) and len(node.id) > 1:
                    if not self._is_snake_case(node.id) and not node.id.isupper():
                        issues.append(self.create_issue(
                            line=node.lineno,
                            column=node.col_offset,
                            message=f"Variable name '{node.id}' should be snake_case",
                            fix_suggestion=f"Rename to '{self._to_snake_case(node.id)}'",
                            severity=IssueSeverity.INFO,
                            metadata={"name": node.id, "suggested": self._to_snake_case(node.id)}
                        ))
                self.generic_visit(node)
            
            def _is_snake_case(self, name):
                return re.match(r'^[a-z_][a-z0-9_]*$', name) is not None
            
            def _is_pascal_case(self, name):
                return re.match(r'^[A-Z][a-zA-Z0-9]*$', name) is not None
            
            def _to_snake_case(self, name):
                # Simple conversion to snake_case
                s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
                return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()
            
            def _to_pascal_case(self, name):
                # Simple conversion to PascalCase
                return ''.join(word.capitalize() for word in name.split('_'))
        
        visitor = NamingVisitor()
        visitor.create_issue = self.create_issue
        visitor.visit(ast_tree)
        
        return issues


class BlankLineRule(AnalysisRule):
    """Check for proper blank line usage."""
    
    def __init__(self):
        super().__init__(
            rule_id="S005",
            category=RuleCategory.STYLE,
            name="Blank Lines",
            description="Use blank lines to separate logical sections",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        lines = content.split('\n')
        
        # Check for multiple consecutive blank lines
        blank_count = 0
        for line_num, line in enumerate(lines, 1):
            if not line.strip():
                blank_count += 1
            else:
                if blank_count > 2:
                    issues.append(self.create_issue(
                        line=line_num - blank_count,
                        column=0,
                        message=f"Too many blank lines ({blank_count}), maximum 2 allowed",
                        fix_suggestion="Remove extra blank lines",
                        auto_fixable=True,
                        metadata={"blank_count": blank_count}
                    ))
                blank_count = 0
        
        return issues


class MultipleStatementsRule(AnalysisRule):
    """Check for multiple statements on one line."""
    
    def __init__(self):
        super().__init__(
            rule_id="S006",
            category=RuleCategory.STYLE,
            name="Multiple Statements",
            description="Avoid multiple statements on one line",
            severity=IssueSeverity.WARNING
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        
        lines = content.split('\n')
        for line_num, line in enumerate(lines, 1):
            stripped = line.strip()
            if not stripped or stripped.startswith('#'):
                continue

            # Remove comments from the line before checking for semicolons
            # Find the first # that's not in a string
            code_part = line
            in_string = False
            string_char = None
            for i, char in enumerate(line):
                if char in ['"', "'"] and (i == 0 or line[i-1] != '\\'):
                    if not in_string:
                        in_string = True
                        string_char = char
                    elif char == string_char:
                        in_string = False
                        string_char = None
                elif char == '#' and not in_string:
                    code_part = line[:i]
                    break

            # Count semicolons in code part only
            semicolon_count = code_part.count(';')
            if semicolon_count > 0:
                semicolon_pos = code_part.find(';')
                issues.append(self.create_issue(
                    line=line_num,
                    column=semicolon_pos,
                    message="Multiple statements on one line",
                    fix_suggestion="Put each statement on a separate line",
                    auto_fixable=True,
                    metadata={"semicolon_count": semicolon_count}
                ))
        
        return issues
