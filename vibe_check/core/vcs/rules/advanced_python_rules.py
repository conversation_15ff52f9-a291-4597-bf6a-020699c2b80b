"""
File: vibe_check/core/vcs/rules/advanced_python_rules.py
Purpose: Advanced Python feature analysis rules
Related Files: vibe_check/core/vcs/rules/
Dependencies: ast, typing
"""

import ast

from vibe_check.core.vcs.models import (
    AnalysisTarget, AnalysisContext, AnalysisIssue, 
    RuleCategory, IssueSeverity
)
from vibe_check.core.vcs.registry import AnalysisRule
from vibe_check.core.logging import get_logger
from typing import List

logger = get_logger(__name__)


class DecoratorUsageRule(AnalysisRule):
    """Analyzes decorator usage patterns and best practices."""
    
    def __init__(self):
        super().__init__(
            rule_id="ADV001",
            category=RuleCategory.STYLE,
            name="Decorator Usage Analysis",
            description="Analyzes decorator usage patterns and best practices",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Analyze decorator usage."""
        issues = []
        
        for node in ast.walk(ast_tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef, ast.ClassDef)):
                issues.extend(self._check_decorator_usage(node))
        
        return issues
    
    def _check_decorator_usage(self, node: ast.AST) -> List[AnalysisIssue]:
        """Check decorator usage on functions and classes."""
        issues = []
        
        if not hasattr(node, 'decorator_list'):
            return issues
        
        # Check for excessive decorators
        if len(node.decorator_list) > 5:
            issues.append(self.create_issue(
                line=node.lineno,
                column=node.col_offset,
                message=f"{type(node).__name__} {node.name} has too many decorators ({len(node.decorator_list)})",
                fix_suggestion="Consider refactoring to reduce decorator complexity"
            ))
        
        # Check for deprecated decorators
        deprecated_decorators = ['deprecated', 'obsolete']
        for decorator in node.decorator_list:
            decorator_name = self._get_decorator_name(decorator)
            if decorator_name in deprecated_decorators:
                issues.append(self.create_issue(
                    line=decorator.lineno if hasattr(decorator, 'lineno') else node.lineno,
                    column=decorator.col_offset if hasattr(decorator, 'col_offset') else node.col_offset,
                    message=f"Using deprecated decorator: {decorator_name}",
                    fix_suggestion="Replace with modern alternative"
                ))
        
        # Check for property decorator misuse
        if isinstance(node, ast.FunctionDef):
            property_decorators = ['property', 'setter', 'getter', 'deleter']
            has_property = any(
                self._get_decorator_name(d) in property_decorators 
                for d in node.decorator_list
            )
            
            if has_property and len(node.args.args) != 1:
                issues.append(self.create_issue(
                    line=node.lineno,
                    column=node.col_offset,
                    message="Property method should only have 'self' parameter",
                    fix_suggestion="Remove extra parameters from property method"
                ))
        
        return issues
    
    def _get_decorator_name(self, decorator: ast.AST) -> str:
        """Extract decorator name from AST node."""
        if isinstance(decorator, ast.Name):
            return decorator.id
        elif isinstance(decorator, ast.Attribute):
            return decorator.attr
        elif isinstance(decorator, ast.Call):
            if isinstance(decorator.func, ast.Name):
                return decorator.func.id
            elif isinstance(decorator.func, ast.Attribute):
                return decorator.func.attr
        return "unknown"


class ContextManagerRule(AnalysisRule):
    """Analyzes context manager usage and implementation."""
    
    def __init__(self):
        super().__init__(
            rule_id="ADV002",
            category=RuleCategory.COMPLEXITY,
            name="Context Manager Analysis",
            description="Analyzes context manager usage and implementation",
            severity=IssueSeverity.WARNING
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Analyze context manager usage."""
        issues = []
        
        for node in ast.walk(ast_tree):
            if isinstance(node, ast.With):
                issues.extend(self._check_with_statement(node))
            elif isinstance(node, ast.ClassDef):
                issues.extend(self._check_context_manager_class(node))
        
        return issues
    
    def _check_with_statement(self, node: ast.With) -> List[AnalysisIssue]:
        """Check with statement usage."""
        issues = []
        
        # Check for nested with statements that could be combined
        if len(node.items) == 1 and isinstance(node.body[0], ast.With):
            issues.append(self.create_issue(
                line=node.lineno,
                column=node.col_offset,
                message="Nested with statements can be combined",
                fix_suggestion="Use 'with item1, item2:' syntax"
            ))
        
        # Check for file operations without context managers
        for item in node.items:
            if isinstance(item.context_expr, ast.Call):
                if (isinstance(item.context_expr.func, ast.Name) and 
                    item.context_expr.func.id == 'open'):
                    # This is good - file opened with context manager
                    pass
        
        return issues
    
    def _check_context_manager_class(self, node: ast.ClassDef) -> List[AnalysisIssue]:
        """Check context manager class implementation."""
        issues = []
        
        has_enter = False
        has_exit = False
        
        for method in node.body:
            if isinstance(method, ast.FunctionDef):
                if method.name == '__enter__':
                    has_enter = True
                elif method.name == '__exit__':
                    has_exit = True
                    # Check __exit__ signature
                    if len(method.args.args) != 4:  # self, exc_type, exc_val, exc_tb
                        issues.append(self.create_issue(
                            line=method.lineno,
                            column=method.col_offset,
                            message="__exit__ method should have 4 parameters",
                            fix_suggestion="Use signature: __exit__(self, exc_type, exc_val, exc_tb)"
                        ))
        
        # Check if class implements context manager protocol
        if has_enter and not has_exit:
            issues.append(self.create_issue(
                line=node.lineno,
                column=node.col_offset,
                message="Class has __enter__ but missing __exit__ method",
                fix_suggestion="Implement __exit__ method for complete context manager"
            ))
        elif has_exit and not has_enter:
            issues.append(self.create_issue(
                line=node.lineno,
                column=node.col_offset,
                message="Class has __exit__ but missing __enter__ method",
                fix_suggestion="Implement __enter__ method for complete context manager"
            ))
        
        return issues


class AsyncAwaitRule(AnalysisRule):
    """Analyzes async/await usage patterns."""
    
    def __init__(self):
        super().__init__(
            rule_id="ADV003",
            category=RuleCategory.COMPLEXITY,
            name="Async/Await Analysis",
            description="Analyzes async/await usage patterns and best practices",
            severity=IssueSeverity.WARNING
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Analyze async/await usage."""
        issues = []
        
        for node in ast.walk(ast_tree):
            if isinstance(node, ast.AsyncFunctionDef):
                issues.extend(self._check_async_function(node))
            elif isinstance(node, ast.Await):
                issues.extend(self._check_await_usage(node))
        
        return issues
    
    def _check_async_function(self, node: ast.AsyncFunctionDef) -> List[AnalysisIssue]:
        """Check async function implementation."""
        issues = []
        
        # Check if async function actually uses await
        has_await = any(
            isinstance(child, ast.Await)
            for child in ast.walk(node)
        )
        
        if not has_await:
            issues.append(self.create_issue(
                line=node.lineno,
                column=node.col_offset,
                message=f"Async function {node.name} doesn't use await",
                fix_suggestion="Remove async if not needed, or add await for async operations"
            ))
        
        # Check for blocking operations in async functions
        for child in ast.walk(node):
            if isinstance(child, ast.Call):
                if self._is_blocking_operation(child):
                    issues.append(self.create_issue(
                        line=child.lineno,
                        column=child.col_offset,
                        message="Blocking operation in async function",
                        fix_suggestion="Use async version or run in executor"
                    ))
        
        return issues
    
    def _check_await_usage(self, node: ast.Await) -> List[AnalysisIssue]:
        """Check await usage."""
        issues = []
        
        # Check if awaiting non-awaitable
        if isinstance(node.value, ast.Call):
            if isinstance(node.value.func, ast.Name):
                # Common non-awaitable functions
                non_awaitable = ['print', 'len', 'str', 'int', 'float']
                if node.value.func.id in non_awaitable:
                    issues.append(self.create_issue(
                        line=node.lineno,
                        column=node.col_offset,
                        message=f"Awaiting non-awaitable function: {node.value.func.id}",
                        fix_suggestion="Remove await from non-async function call"
                    ))
        
        return issues
    
    def _is_blocking_operation(self, call_node: ast.Call) -> bool:
        """Check if call is a blocking operation."""
        blocking_functions = [
            'time.sleep', 'input', 'open'  # Basic blocking operations
        ]
        
        if isinstance(call_node.func, ast.Attribute):
            func_name = f"{call_node.func.value.id}.{call_node.func.attr}"
            return func_name in blocking_functions
        elif isinstance(call_node.func, ast.Name):
            return call_node.func.id in ['input', 'open']
        
        return False


class MetaclassRule(AnalysisRule):
    """Analyzes metaclass usage and implementation."""
    
    def __init__(self):
        super().__init__(
            rule_id="ADV004",
            category=RuleCategory.COMPLEXITY,
            name="Metaclass Analysis",
            description="Analyzes metaclass usage and implementation",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Analyze metaclass usage."""
        issues = []
        
        for node in ast.walk(ast_tree):
            if isinstance(node, ast.ClassDef):
                issues.extend(self._check_metaclass_usage(node))
        
        return issues
    
    def _check_metaclass_usage(self, node: ast.ClassDef) -> List[AnalysisIssue]:
        """Check metaclass usage in class definition."""
        issues = []
        
        # Check for metaclass keyword
        for keyword in node.keywords:
            if keyword.arg == 'metaclass':
                issues.append(self.create_issue(
                    line=node.lineno,
                    column=node.col_offset,
                    message=f"Class {node.name} uses metaclass - ensure it's necessary",
                    fix_suggestion="Consider if metaclass is really needed or if class decorators would suffice"
                ))
                
                # Check if metaclass is type (redundant)
                if (isinstance(keyword.value, ast.Name) and 
                    keyword.value.id == 'type'):
                    issues.append(self.create_issue(
                        line=node.lineno,
                        column=node.col_offset,
                        message="Redundant metaclass=type specification",
                        fix_suggestion="Remove metaclass=type as it's the default"
                    ))
        
        return issues


class GeneratorRule(AnalysisRule):
    """Analyzes generator usage and implementation."""
    
    def __init__(self):
        super().__init__(
            rule_id="ADV005",
            category=RuleCategory.COMPLEXITY,
            name="Generator Analysis",
            description="Analyzes generator usage and implementation",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Analyze generator usage."""
        issues = []
        
        for node in ast.walk(ast_tree):
            if isinstance(node, ast.FunctionDef):
                issues.extend(self._check_generator_function(node))
            elif isinstance(node, ast.GeneratorExp):
                issues.extend(self._check_generator_expression(node))
        
        return issues
    
    def _check_generator_function(self, node: ast.FunctionDef) -> List[AnalysisIssue]:
        """Check generator function implementation."""
        issues = []
        
        has_yield = any(
            isinstance(child, (ast.Yield, ast.YieldFrom))
            for child in ast.walk(node)
        )
        
        if has_yield:
            # Check for return with value in generator
            for child in ast.walk(node):
                if isinstance(child, ast.Return) and child.value is not None:
                    issues.append(self.create_issue(
                        line=child.lineno,
                        column=child.col_offset,
                        message="Generator function should not return a value",
                        fix_suggestion="Use 'return' without value or 'yield' instead"
                    ))
        
        return issues
    
    def _check_generator_expression(self, node: ast.GeneratorExp) -> List[AnalysisIssue]:
        """Check generator expression usage."""
        issues = []
        
        # Check for complex generator expressions that should be functions
        complexity = self._calculate_expression_complexity(node)
        if complexity > 3:
            issues.append(self.create_issue(
                line=node.lineno,
                column=node.col_offset,
                message="Complex generator expression should be a function",
                fix_suggestion="Convert to generator function for better readability"
            ))
        
        return issues
    
    def _calculate_expression_complexity(self, node: ast.GeneratorExp) -> int:
        """Calculate complexity of generator expression."""
        complexity = 0
        
        # Count comprehensions
        complexity += len(node.generators)
        
        # Count conditions
        for generator in node.generators:
            complexity += len(generator.ifs)
        
        # Count nested calls in element
        for child in ast.walk(node.elt):
            if isinstance(child, ast.Call):
                complexity += 1
        
        return complexity
