"""
Documentation Analysis Rules
============================

Documentation quality and coverage analysis rules.
"""

import ast
import re
from typing import List

from ..models import RuleCategory, IssueSeverity
from ..registry import AnalysisRule, AnalysisTarget, AnalysisIssue, AnalysisContext


class MissingDocstringRule(AnalysisRule):
    """Check for missing docstrings in modules, classes, and functions."""
    
    def __init__(self):
        super().__init__(
            rule_id="D001",
            category=RuleCategory.DOCS,
            name="Missing Docstring",
            description="Public modules, classes, and functions should have docstrings",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        
        # Check module docstring
        if not ast.get_docstring(ast_tree):
            issues.append(self.create_issue(
                line=1,
                column=0,
                message="Module missing docstring",
                fix_suggestion="Add a module-level docstring describing the module's purpose",
                severity=IssueSeverity.INFO,
                metadata={"type": "module"}
            ))
        
        class DocstringVisitor(ast.NodeVisitor):
            def visit_ClassDef(self, node):
                if not ast.get_docstring(node):
                    issues.append(self.create_issue(
                        line=node.lineno,
                        column=node.col_offset,
                        message=f"Class '{node.name}' missing docstring",
                        fix_suggestion="Add a docstring describing the class purpose and usage",
                        severity=IssueSeverity.INFO,
                        metadata={"type": "class", "name": node.name}
                    ))
                self.generic_visit(node)
            
            def visit_FunctionDef(self, node):
                # Skip private methods (starting with _) for less noise
                if not node.name.startswith('_') and not ast.get_docstring(node):
                    issues.append(self.create_issue(
                        line=node.lineno,
                        column=node.col_offset,
                        message=f"Function '{node.name}' missing docstring",
                        fix_suggestion="Add a docstring describing the function's purpose, parameters, and return value",
                        severity=IssueSeverity.INFO,
                        metadata={"type": "function", "name": node.name}
                    ))
                self.generic_visit(node)
            
            def visit_AsyncFunctionDef(self, node):
                self.visit_FunctionDef(node)
        
        visitor = DocstringVisitor()
        visitor.create_issue = self.create_issue
        visitor.visit(ast_tree)
        
        return issues


class DocstringFormatRule(AnalysisRule):
    """Check docstring format and content quality."""
    
    def __init__(self):
        super().__init__(
            rule_id="D002",
            category=RuleCategory.DOCS,
            name="Docstring Format",
            description="Docstrings should follow proper format conventions",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        
        class DocstringFormatVisitor(ast.NodeVisitor):
            def visit_FunctionDef(self, node):
                docstring = ast.get_docstring(node)
                if docstring:
                    self._check_docstring_format(node, docstring, "function")
                self.generic_visit(node)
            
            def visit_AsyncFunctionDef(self, node):
                self.visit_FunctionDef(node)
            
            def visit_ClassDef(self, node):
                docstring = ast.get_docstring(node)
                if docstring:
                    self._check_docstring_format(node, docstring, "class")
                self.generic_visit(node)
            
            def _check_docstring_format(self, node, docstring, node_type):
                lines = docstring.split('\n')
                
                # Check for empty first line after opening quotes
                if len(lines) > 1 and lines[0].strip() == "":
                    issues.append(self.create_issue(
                        line=node.lineno,
                        column=node.col_offset,
                        message=f"{node_type.capitalize()} '{node.name}' docstring should not start with empty line",
                        fix_suggestion="Remove empty line after opening quotes",
                        severity=IssueSeverity.INFO,
                        metadata={"type": node_type, "name": node.name, "issue": "empty_first_line"}
                    ))
                
                # Check for proper sentence structure (starts with capital, ends with period)
                first_line = lines[0].strip()
                if first_line:
                    if not first_line[0].isupper():
                        issues.append(self.create_issue(
                            line=node.lineno,
                            column=node.col_offset,
                            message=f"{node_type.capitalize()} '{node.name}' docstring should start with capital letter",
                            fix_suggestion="Start docstring with a capital letter",
                            severity=IssueSeverity.INFO,
                            metadata={"type": node_type, "name": node.name, "issue": "no_capital"}
                        ))
                    
                    if not first_line.endswith('.'):
                        issues.append(self.create_issue(
                            line=node.lineno,
                            column=node.col_offset,
                            message=f"{node_type.capitalize()} '{node.name}' docstring should end with period",
                            fix_suggestion="End docstring summary with a period",
                            severity=IssueSeverity.INFO,
                            metadata={"type": node_type, "name": node.name, "issue": "no_period"}
                        ))
                
                # Check for function parameters documentation
                if node_type == "function" and hasattr(node, 'args') and node.args.args:
                    param_names = [arg.arg for arg in node.args.args if arg.arg not in ['self', 'cls']]
                    if param_names and not self._has_parameter_docs(docstring):
                        issues.append(self.create_issue(
                            line=node.lineno,
                            column=node.col_offset,
                            message=f"Function '{node.name}' with parameters should document them",
                            fix_suggestion="Add Args: section documenting function parameters",
                            severity=IssueSeverity.INFO,
                            metadata={"type": node_type, "name": node.name, "issue": "missing_param_docs"}
                        ))
            
            def _has_parameter_docs(self, docstring):
                """Check if docstring contains parameter documentation."""
                # Look for common parameter documentation patterns
                param_patterns = [
                    r'Args?:',
                    r'Parameters?:',
                    r'Arguments?:',
                    r'\w+\s*\([^)]+\):',  # param (type): description
                    r':\w+\s+\w+:',       # :param name: description
                ]
                
                for pattern in param_patterns:
                    if re.search(pattern, docstring, re.IGNORECASE):
                        return True
                return False
        
        visitor = DocstringFormatVisitor()
        visitor.create_issue = self.create_issue
        visitor.visit(ast_tree)
        
        return issues


class CommentQualityRule(AnalysisRule):
    """Check for comment quality and usefulness."""
    
    def __init__(self):
        super().__init__(
            rule_id="D003",
            category=RuleCategory.DOCS,
            name="Comment Quality",
            description="Comments should be meaningful and well-formatted",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        
        lines = content.split('\n')
        for line_num, line in enumerate(lines, 1):
            stripped = line.strip()
            
            # Skip non-comment lines
            if not stripped.startswith('#'):
                continue
            
            comment = stripped[1:].strip()
            
            # Skip empty comments
            if not comment:
                continue
            
            # Check for TODO/FIXME/HACK comments
            if re.match(r'^(TODO|FIXME|HACK|XXX)', comment, re.IGNORECASE):
                issues.append(self.create_issue(
                    line=line_num,
                    column=line.find('#'),
                    message=f"TODO/FIXME comment found: {comment[:50]}...",
                    fix_suggestion="Address the TODO/FIXME or create a proper issue tracker entry",
                    severity=IssueSeverity.INFO,
                    metadata={"comment_type": "todo", "comment": comment}
                ))
            
            # Check for very short, potentially useless comments
            elif len(comment) < 10 and not re.match(r'^(end|start|begin)', comment, re.IGNORECASE):
                issues.append(self.create_issue(
                    line=line_num,
                    column=line.find('#'),
                    message=f"Comment may be too brief to be useful: '{comment}'",
                    fix_suggestion="Expand comment to be more descriptive or remove if not needed",
                    severity=IssueSeverity.INFO,
                    metadata={"comment_type": "brief", "comment": comment}
                ))
            
            # Check for commented-out code (heuristic)
            elif self._looks_like_code(comment):
                issues.append(self.create_issue(
                    line=line_num,
                    column=line.find('#'),
                    message="Commented-out code detected",
                    fix_suggestion="Remove commented-out code or use version control instead",
                    severity=IssueSeverity.INFO,
                    metadata={"comment_type": "code", "comment": comment}
                ))
        
        return issues
    
    def _looks_like_code(self, comment):
        """Heuristic to detect commented-out code."""
        # Look for common code patterns
        code_patterns = [
            r'^\s*(def|class|if|for|while|try|except|import|from)\s+',
            r'^\s*\w+\s*=\s*',  # assignments
            r'^\s*\w+\([^)]*\)',  # function calls
            r'^\s*return\s+',
            r'^\s*print\s*\(',
        ]
        
        for pattern in code_patterns:
            if re.search(pattern, comment):
                return True
        return False


class TypeHintDocumentationRule(AnalysisRule):
    """Check for proper type hint documentation."""
    
    def __init__(self):
        super().__init__(
            rule_id="D004",
            category=RuleCategory.DOCS,
            name="Type Hint Documentation",
            description="Functions with type hints should have consistent documentation",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        
        class TypeHintVisitor(ast.NodeVisitor):
            def visit_FunctionDef(self, node):
                # Check if function has type hints
                has_type_hints = (
                    node.returns is not None or
                    any(arg.annotation is not None for arg in node.args.args)
                )
                
                if has_type_hints:
                    docstring = ast.get_docstring(node)
                    if docstring:
                        # If function has type hints, docstring doesn't need to repeat type info
                        # but should focus on behavior description
                        if self._docstring_has_redundant_type_info(docstring, node):
                            issues.append(self.create_issue(
                                line=node.lineno,
                                column=node.col_offset,
                                message=f"Function '{node.name}' has type hints and redundant type info in docstring",
                                fix_suggestion="Remove redundant type information from docstring since type hints are present",
                                severity=IssueSeverity.INFO,
                                metadata={"type": "function", "name": node.name, "issue": "redundant_types"}
                            ))
                    else:
                        # Function with type hints but no docstring
                        if not node.name.startswith('_'):  # Skip private functions
                            issues.append(self.create_issue(
                                line=node.lineno,
                                column=node.col_offset,
                                message=f"Function '{node.name}' has type hints but no docstring",
                                fix_suggestion="Add docstring focusing on behavior since types are already documented",
                                severity=IssueSeverity.INFO,
                                metadata={"type": "function", "name": node.name, "issue": "missing_behavior_docs"}
                            ))
                
                self.generic_visit(node)
            
            def visit_AsyncFunctionDef(self, node):
                self.visit_FunctionDef(node)
            
            def _docstring_has_redundant_type_info(self, docstring, node):
                """Check if docstring repeats type information that's in type hints."""
                # Simple heuristic: look for type names in docstring that match type hints
                type_keywords = ['str', 'int', 'float', 'bool', 'list', 'dict', 'tuple', 'set']
                
                # Count type mentions in docstring
                type_mentions = sum(1 for keyword in type_keywords if keyword in docstring.lower())
                
                # If there are many type mentions and the function has type hints,
                # it might be redundant
                return type_mentions > 2
        
        visitor = TypeHintVisitor()
        visitor.create_issue = self.create_issue
        visitor.visit(ast_tree)
        
        return issues
