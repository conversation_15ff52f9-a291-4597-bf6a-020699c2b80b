"""
Rule Discovery System
====================

Automatic discovery and loading of analysis rules through module scanning.
"""

import importlib
import logging
import pkgutil
from pathlib import Path
from typing import Dict, Type, List, Set, Optional, Any
import inspect

from ..registry import AnalysisRule
from .decorators import get_discovered_rules, get_all_rule_metadata, RuleDiscoveryStats

logger = logging.getLogger(__name__)


class RuleDiscoveryEngine:
    """
    Engine for discovering and loading analysis rules automatically.
    
    This engine scans specified modules and packages to find classes
    decorated with @analysis_rule and automatically registers them.
    """
    
    def __init__(self):
        """Initialize the discovery engine."""
        self.discovered_rules: Dict[str, Type[AnalysisRule]] = {}
        self.discovery_stats = {
            'modules_scanned': 0,
            'rules_discovered': 0,
            'discovery_errors': 0,
            'load_errors': 0
        }
        self.failed_modules: List[str] = []
        self.failed_rules: List[str] = []
    
    def discover_rules_in_package(self, package_name: str, 
                                recursive: bool = True) -> Dict[str, Type[AnalysisRule]]:
        """
        Discover all rules in a package.
        
        Args:
            package_name: Name of the package to scan (e.g., 'vibe_check.core.vcs.rules')
            recursive: Whether to scan subpackages recursively
            
        Returns:
            Dictionary mapping rule IDs to rule classes
        """
        logger.info(f"Discovering rules in package: {package_name}")
        
        try:
            # Import the package
            package = importlib.import_module(package_name)
            package_path = package.__path__
            
            # Scan all modules in the package
            for importer, modname, ispkg in pkgutil.iter_modules(package_path):
                if ispkg and recursive:
                    # Recursively scan subpackages
                    subpackage_name = f"{package_name}.{modname}"
                    self.discover_rules_in_package(subpackage_name, recursive=True)
                elif not ispkg:
                    # Scan module for rules
                    module_name = f"{package_name}.{modname}"
                    self._scan_module(module_name)
            
        except Exception as e:
            logger.error(f"Failed to discover rules in package {package_name}: {e}")
            self.discovery_stats['discovery_errors'] += 1
            self.failed_modules.append(package_name)
        
        # Update discovered rules from the global registry
        self.discovered_rules.update(get_discovered_rules())
        self.discovery_stats['rules_discovered'] = len(self.discovered_rules)
        
        logger.info(f"Discovery complete: {len(self.discovered_rules)} rules found")
        return self.discovered_rules.copy()
    
    def discover_rules_in_directory(self, directory_path: Path, 
                                  package_prefix: str = "") -> Dict[str, Type[AnalysisRule]]:
        """
        Discover rules in a directory by scanning Python files.
        
        Args:
            directory_path: Path to directory containing rule modules
            package_prefix: Package prefix for module names
            
        Returns:
            Dictionary mapping rule IDs to rule classes
        """
        logger.info(f"Discovering rules in directory: {directory_path}")
        
        if not directory_path.exists() or not directory_path.is_dir():
            logger.warning(f"Directory does not exist: {directory_path}")
            return {}
        
        # Scan all Python files in the directory
        for py_file in directory_path.glob("*.py"):
            if py_file.name.startswith("__"):
                continue  # Skip __init__.py, __pycache__, etc.
            
            module_name = py_file.stem
            if package_prefix:
                full_module_name = f"{package_prefix}.{module_name}"
            else:
                full_module_name = module_name
            
            self._scan_module(full_module_name)
        
        # Update discovered rules
        self.discovered_rules.update(get_discovered_rules())
        self.discovery_stats['rules_discovered'] = len(self.discovered_rules)
        
        return self.discovered_rules.copy()
    
    def _scan_module(self, module_name: str) -> None:
        """
        Scan a specific module for analysis rules.
        
        Args:
            module_name: Full module name to scan
        """
        try:
            logger.debug(f"Scanning module: {module_name}")
            
            # Import the module (this triggers @analysis_rule decorators)
            importlib.import_module(module_name)
            
            self.discovery_stats['modules_scanned'] += 1
            
        except Exception as e:
            logger.warning(f"Failed to scan module {module_name}: {e}")
            self.discovery_stats['discovery_errors'] += 1
            self.failed_modules.append(module_name)
    
    def get_discovery_report(self) -> Dict[str, Any]:
        """
        Get a comprehensive discovery report.
        
        Returns:
            Dictionary containing discovery statistics and metadata
        """
        rule_metadata = get_all_rule_metadata()
        discovery_stats = RuleDiscoveryStats.get_stats()
        
        return {
            'discovery_stats': self.discovery_stats,
            'rule_stats': discovery_stats,
            'discovered_rules': {
                rule_id: {
                    'class_name': rule_class.__name__,
                    'module': rule_class.__module__,
                    'metadata': rule_metadata.get(rule_id, {})
                }
                for rule_id, rule_class in self.discovered_rules.items()
            },
            'failed_modules': self.failed_modules,
            'failed_rules': self.failed_rules
        }
    
    def validate_discovered_rules(self) -> List[str]:
        """
        Validate all discovered rules for common issues.
        
        Returns:
            List of validation errors
        """
        errors = []
        
        for rule_id, rule_class in self.discovered_rules.items():
            try:
                # Check if class can be instantiated
                rule_instance = rule_class()
                
                # Validate required attributes
                required_attrs = ['rule_id', 'name', 'category', 'severity']
                for attr in required_attrs:
                    if not hasattr(rule_instance, attr):
                        errors.append(f"{rule_id}: Missing required attribute '{attr}'")
                
                # Validate analyze method exists
                if not hasattr(rule_instance, 'analyze'):
                    errors.append(f"{rule_id}: Missing 'analyze' method")
                elif not callable(getattr(rule_instance, 'analyze')):
                    errors.append(f"{rule_id}: 'analyze' is not callable")
                
                # Validate analyze method signature
                analyze_method = getattr(rule_instance, 'analyze')
                sig = inspect.signature(analyze_method)
                expected_params = ['target', 'content', 'ast_tree', 'context']
                actual_params = list(sig.parameters.keys())[1:]  # Skip 'self'
                
                if actual_params != expected_params:
                    errors.append(
                        f"{rule_id}: Invalid analyze method signature. "
                        f"Expected {expected_params}, got {actual_params}"
                    )
                
            except Exception as e:
                errors.append(f"{rule_id}: Validation failed - {e}")
                self.failed_rules.append(rule_id)
        
        return errors


class PluginRuleLoader:
    """
    Loader for external plugin rules.
    
    This class provides functionality to load rules from external packages
    or directories, enabling a plugin system for custom rules.
    """
    
    def __init__(self, discovery_engine: RuleDiscoveryEngine):
        """
        Initialize the plugin loader.
        
        Args:
            discovery_engine: Rule discovery engine to use
        """
        self.discovery_engine = discovery_engine
        self.loaded_plugins: Set[str] = set()
    
    def load_plugin_package(self, package_name: str) -> Dict[str, Type[AnalysisRule]]:
        """
        Load rules from an external plugin package.
        
        Args:
            package_name: Name of the plugin package
            
        Returns:
            Dictionary of discovered rules
        """
        if package_name in self.loaded_plugins:
            logger.info(f"Plugin package {package_name} already loaded")
            return {}
        
        logger.info(f"Loading plugin package: {package_name}")
        
        try:
            rules = self.discovery_engine.discover_rules_in_package(package_name)
            self.loaded_plugins.add(package_name)
            
            logger.info(f"Loaded {len(rules)} rules from plugin package {package_name}")
            return rules
            
        except Exception as e:
            logger.error(f"Failed to load plugin package {package_name}: {e}")
            return {}
    
    def load_plugin_directory(self, directory_path: Path, 
                            package_prefix: str = "") -> Dict[str, Type[AnalysisRule]]:
        """
        Load rules from an external plugin directory.
        
        Args:
            directory_path: Path to plugin directory
            package_prefix: Package prefix for module names
            
        Returns:
            Dictionary of discovered rules
        """
        plugin_id = str(directory_path)
        if plugin_id in self.loaded_plugins:
            logger.info(f"Plugin directory {directory_path} already loaded")
            return {}
        
        logger.info(f"Loading plugin directory: {directory_path}")
        
        try:
            rules = self.discovery_engine.discover_rules_in_directory(
                directory_path, package_prefix
            )
            self.loaded_plugins.add(plugin_id)
            
            logger.info(f"Loaded {len(rules)} rules from plugin directory {directory_path}")
            return rules
            
        except Exception as e:
            logger.error(f"Failed to load plugin directory {directory_path}: {e}")
            return {}
    
    def get_loaded_plugins(self) -> Set[str]:
        """Get set of loaded plugin identifiers."""
        return self.loaded_plugins.copy()


# Global discovery engine instance
_discovery_engine = RuleDiscoveryEngine()


def discover_all_rules(package_name: str = "vibe_check.core.vcs.rules") -> Dict[str, Type[AnalysisRule]]:
    """
    Convenience function to discover all rules in the default package.
    
    Args:
        package_name: Package to scan for rules
        
    Returns:
        Dictionary mapping rule IDs to rule classes
    """
    return _discovery_engine.discover_rules_in_package(package_name)


def get_discovery_engine() -> RuleDiscoveryEngine:
    """Get the global discovery engine instance."""
    return _discovery_engine
