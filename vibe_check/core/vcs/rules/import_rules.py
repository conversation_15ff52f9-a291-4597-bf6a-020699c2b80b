"""
Import Analysis Rules
=====================

Import organization, unused imports, and import-related analysis rules.
"""

import ast

from ..models import RuleCategory, IssueSeverity
from ..registry import AnalysisRule, AnalysisTarget, AnalysisIssue, AnalysisContext
from typing import List


class UnusedImportRule(AnalysisRule):
    """Detect unused imports."""
    
    def __init__(self):
        super().__init__(
            rule_id="I001",
            category=RuleCategory.IMPORTS,
            name="Unused Import",
            description="Remove unused imports",
            severity=IssueSeverity.WARNING
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        
        # Collect all imports
        imports = {}  # name -> (line, col, import_node)
        
        # Collect all name usages
        used_names = set()
        
        class ImportCollector(ast.NodeVisitor):
            def visit_Import(self, node):
                for alias in node.names:
                    name = alias.asname if alias.asname else alias.name
                    imports[name] = (node.lineno, node.col_offset, node, alias.name)
            
            def visit_ImportFrom(self, node):
                for alias in node.names:
                    if alias.name == '*':
                        # Skip star imports for now
                        continue
                    name = alias.asname if alias.asname else alias.name
                    imports[name] = (node.lineno, node.col_offset, node, alias.name)
        
        class NameCollector(ast.NodeVisitor):
            def visit_Name(self, node):
                used_names.add(node.id)
            
            def visit_Attribute(self, node):
                # For module.attribute usage
                if isinstance(node.value, ast.Name):
                    used_names.add(node.value.id)
                self.generic_visit(node)
        
        # Collect imports and usages
        import_collector = ImportCollector()
        import_collector.visit(ast_tree)
        
        name_collector = NameCollector()
        name_collector.visit(ast_tree)
        
        # Check for unused imports
        for imported_name, (line, col, import_node, original_name) in imports.items():
            if imported_name not in used_names:
                # Special cases to skip
                if self._is_special_import(imported_name, original_name):
                    continue
                
                issues.append(self.create_issue(
                    line=line,
                    column=col,
                    message=f"Unused import: '{imported_name}'",
                    fix_suggestion=f"Remove unused import '{imported_name}'",
                    auto_fixable=True,
                    severity=IssueSeverity.WARNING,
                    metadata={"imported_name": imported_name, "original_name": original_name}
                ))
        
        return issues
    
    def _is_special_import(self, imported_name, original_name):
        """Check if import should be ignored (e.g., __future__, typing)."""
        special_imports = {
            '__future__', 'typing', 'annotations',
            # Common imports that might be used implicitly
            'pytest', 'unittest', 'mock'
        }
        
        return (imported_name in special_imports or 
                original_name in special_imports or
                imported_name.startswith('_'))


class ImportOrderRule(AnalysisRule):
    """Check import order according to PEP 8."""
    
    def __init__(self):
        super().__init__(
            rule_id="I002",
            category=RuleCategory.IMPORTS,
            name="Import Order",
            description="Imports should be ordered according to PEP 8",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        
        # Collect imports with their categories
        imports = []
        
        class ImportOrderVisitor(ast.NodeVisitor):
            def visit_Import(self, node):
                for alias in node.names:
                    category = self._categorize_import(alias.name)
                    imports.append((node.lineno, category, alias.name, 'import'))
            
            def visit_ImportFrom(self, node):
                if node.module:
                    category = self._categorize_import(node.module)
                    imports.append((node.lineno, category, node.module, 'from'))
            
            def _categorize_import(self, module_name):
                """Categorize import according to PEP 8."""
                if not module_name:
                    return 3  # Unknown
                
                # Standard library
                stdlib_modules = {
                    'os', 'sys', 'json', 're', 'time', 'datetime', 'collections',
                    'itertools', 'functools', 'pathlib', 'typing', 'asyncio',
                    'logging', 'unittest', 'argparse', 'configparser', 'urllib',
                    'http', 'email', 'xml', 'html', 'csv', 'sqlite3', 'threading',
                    'multiprocessing', 'subprocess', 'shutil', 'tempfile', 'glob'
                }
                
                first_part = module_name.split('.')[0]
                
                if first_part in stdlib_modules:
                    return 1  # Standard library
                elif self._is_third_party(first_part):
                    return 2  # Third party
                else:
                    return 3  # Local/relative
            
            def _is_third_party(self, module_name):
                """Heuristic to detect third-party modules."""
                # Common third-party packages
                third_party = {
                    'numpy', 'pandas', 'matplotlib', 'requests', 'flask', 'django',
                    'pytest', 'click', 'pydantic', 'fastapi', 'sqlalchemy',
                    'aiohttp', 'redis', 'celery', 'boto3', 'psycopg2', 'pymongo'
                }
                return module_name in third_party
        
        visitor = ImportOrderVisitor()
        visitor.visit(ast_tree)
        
        # Check if imports are in correct order
        if len(imports) > 1:
            current_category = 0
            for line, category, module, import_type in imports:
                if category < current_category:
                    issues.append(self.create_issue(
                        line=line,
                        column=0,
                        message=f"Import '{module}' is not in correct order (should come before previous imports)",
                        fix_suggestion="Reorder imports: standard library, third-party, local imports",
                        auto_fixable=True,
                        severity=IssueSeverity.INFO,
                        metadata={"module": module, "category": category, "expected_order": "stdlib, third-party, local"}
                    ))
                current_category = max(current_category, category)
        
        return issues


class WildcardImportRule(AnalysisRule):
    """Detect wildcard imports (from module import *)."""
    
    def __init__(self):
        super().__init__(
            rule_id="I003",
            category=RuleCategory.IMPORTS,
            name="Wildcard Import",
            description="Avoid wildcard imports (from module import *)",
            severity=IssueSeverity.WARNING
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        
        class WildcardVisitor(ast.NodeVisitor):
            def __init__(self, rule_instance):
                self.rule = rule_instance

            def visit_ImportFrom(self, node):
                for alias in node.names:
                    if alias.name == '*':
                        module_name = node.module or "unknown"
                        issues.append(self.rule.create_issue(
                            line=node.lineno,
                            column=node.col_offset,
                            message=f"Wildcard import from '{module_name}' should be avoided",
                            fix_suggestion=f"Import specific names instead of using 'from {module_name} import *'",
                            severity=IssueSeverity.WARNING,
                            metadata={"module": module_name}
                        ))

        visitor = WildcardVisitor(self)
        visitor.visit(ast_tree)
        
        return issues


class RelativeImportRule(AnalysisRule):
    """Check for proper use of relative imports."""
    
    def __init__(self):
        super().__init__(
            rule_id="I004",
            category=RuleCategory.IMPORTS,
            name="Relative Import",
            description="Use explicit relative imports in packages",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        
        class RelativeImportVisitor(ast.NodeVisitor):
            def visit_ImportFrom(self, node):
                if node.level > 0:  # Relative import
                    if node.level > 2:  # Very deep relative import
                        issues.append(self.create_issue(
                            line=node.lineno,
                            column=node.col_offset,
                            message=f"Relative import with level {node.level} may be too deep",
                            fix_suggestion="Consider using absolute imports for better clarity",
                            severity=IssueSeverity.INFO,
                            metadata={"level": node.level, "module": node.module}
                        ))
                elif node.module and '.' in node.module:
                    # Check for implicit relative imports (should be explicit)
                    # This is a heuristic - in practice, this is complex to detect accurately
                    pass
        
        visitor = RelativeImportVisitor()
        visitor.visit(ast_tree)
        
        return issues


class CircularImportRule(AnalysisRule):
    """Detect potential circular import issues."""
    
    def __init__(self):
        super().__init__(
            rule_id="I005",
            category=RuleCategory.IMPORTS,
            name="Circular Import",
            description="Detect potential circular import patterns",
            severity=IssueSeverity.WARNING
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        
        # This is a simplified check - full circular import detection
        # requires analyzing multiple files
        
        class CircularImportVisitor(ast.NodeVisitor):
            def __init__(self, rule_instance):
                self.rule = rule_instance

            def visit_ImportFrom(self, node):
                if node.module:
                    # Check for imports that might cause circular dependencies
                    current_module = str(target.path.stem)

                    # Simple heuristic: if importing from a module with similar name
                    if (node.module.endswith(current_module) or
                        current_module in node.module):
                        issues.append(self.rule.create_issue(
                            line=node.lineno,
                            column=node.col_offset,
                            message=f"Potential circular import: importing from '{node.module}' in '{current_module}'",
                            fix_suggestion="Consider restructuring modules to avoid circular dependencies",
                            severity=IssueSeverity.WARNING,
                            metadata={"imported_module": node.module, "current_module": current_module}
                        ))

            def visit_Import(self, node):
                current_module = str(target.path.stem)
                for alias in node.names:
                    if (alias.name.endswith(current_module) or
                        current_module in alias.name):
                        issues.append(self.rule.create_issue(
                            line=node.lineno,
                            column=node.col_offset,
                            message=f"Potential circular import: importing '{alias.name}' in '{current_module}'",
                            fix_suggestion="Consider restructuring modules to avoid circular dependencies",
                            severity=IssueSeverity.WARNING,
                            metadata={"imported_module": alias.name, "current_module": current_module}
                        ))

        visitor = CircularImportVisitor(self)
        visitor.visit(ast_tree)
        
        return issues


class ImportGroupingRule(AnalysisRule):
    """Check for proper import grouping with blank lines."""
    
    def __init__(self):
        super().__init__(
            rule_id="I006",
            category=RuleCategory.IMPORTS,
            name="Import Grouping",
            description="Import groups should be separated by blank lines",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        
        lines = content.split('\n')
        import_lines = []
        
        # Find all import lines
        class ImportLineVisitor(ast.NodeVisitor):
            def visit_Import(self, node):
                import_lines.append(node.lineno)
            
            def visit_ImportFrom(self, node):
                import_lines.append(node.lineno)
        
        visitor = ImportLineVisitor()
        visitor.visit(ast_tree)
        
        if len(import_lines) < 2:
            return issues
        
        import_lines.sort()
        
        # Check for missing blank lines between import groups
        # This is a simplified check - proper grouping detection would need
        # to categorize imports first
        for i in range(1, len(import_lines)):
            current_line = import_lines[i]
            prev_line = import_lines[i-1]
            
            # If imports are not consecutive and there's no blank line
            if current_line - prev_line > 1:
                # Check if there's a blank line between them
                between_lines = lines[prev_line:current_line-1]
                if between_lines and all(line.strip() for line in between_lines):
                    # There's non-blank content between imports
                    issues.append(self.create_issue(
                        line=current_line,
                        column=0,
                        message="Import groups should be separated by blank lines",
                        fix_suggestion="Add blank line between different import groups",
                        auto_fixable=True,
                        severity=IssueSeverity.INFO,
                        metadata={"prev_import_line": prev_line, "current_import_line": current_line}
                    ))
        
        return issues
