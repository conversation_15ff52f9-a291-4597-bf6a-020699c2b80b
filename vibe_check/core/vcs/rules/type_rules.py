"""
Type Analysis Rules
===================

Type annotation coverage, consistency, and quality analysis rules.
"""

import ast

from ..models import RuleCategory, IssueSeverity, AnalysisTarget, AnalysisIssue, AnalysisContext
from ..registry import AnalysisRule
from ..type_checking import <PERSON>In<PERSON><PERSON>ng<PERSON>, MypyIntegration
from typing import Dict, List, Optional, Set, Tuple, Union


class MissingTypeHintsRule(AnalysisRule):
    """Check for missing type hints in function signatures."""
    
    def __init__(self):
        super().__init__(
            rule_id="T001",
            category=RuleCategory.TYPES,
            name="Missing Type Hints",
            description="Public functions should have type hints",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        
        class TypeHintVisitor(ast.NodeVisitor):
            def visit_FunctionDef(self, node):
                # Skip private functions and special methods
                if node.name.startswith('_'):
                    self.generic_visit(node)
                    return
                
                missing_hints = []
                
                # Check return type annotation
                if node.returns is None:
                    missing_hints.append("return type")
                
                # Check parameter type annotations
                for arg in node.args.args:
                    if arg.arg in ['self', 'cls']:
                        continue  # Skip self and cls
                    if arg.annotation is None:
                        missing_hints.append(f"parameter '{arg.arg}'")
                
                # Check keyword-only arguments
                for arg in node.args.kwonlyargs:
                    if arg.annotation is None:
                        missing_hints.append(f"keyword parameter '{arg.arg}'")
                
                if missing_hints:
                    issues.append(self.create_issue(
                        line=node.lineno,
                        column=node.col_offset,
                        message=f"Function '{node.name}' missing type hints for: {', '.join(missing_hints)}",
                        fix_suggestion="Add type hints to improve code clarity and enable static type checking",
                        severity=IssueSeverity.INFO,
                        metadata={"function_name": node.name, "missing_hints": missing_hints}
                    ))
                
                self.generic_visit(node)
            
            def visit_AsyncFunctionDef(self, node):
                self.visit_FunctionDef(node)
        
        visitor = TypeHintVisitor()
        visitor.create_issue = self.create_issue
        visitor.visit(ast_tree)
        
        return issues


class InconsistentTypeHintsRule(AnalysisRule):
    """Check for inconsistent type hint usage."""
    
    def __init__(self):
        super().__init__(
            rule_id="T002",
            category=RuleCategory.TYPES,
            name="Inconsistent Type Hints",
            description="Type hints should be used consistently",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        
        # Collect type hint usage statistics
        functions_with_hints = 0
        functions_without_hints = 0
        total_functions = 0
        
        class TypeConsistencyVisitor(ast.NodeVisitor):
            def visit_FunctionDef(self, node):
                nonlocal functions_with_hints, functions_without_hints, total_functions
                
                # Skip private functions
                if node.name.startswith('_'):
                    self.generic_visit(node)
                    return
                
                total_functions += 1
                
                # Check if function has any type hints
                has_hints = (
                    node.returns is not None or
                    any(arg.annotation is not None for arg in node.args.args if arg.arg not in ['self', 'cls']) or
                    any(arg.annotation is not None for arg in node.args.kwonlyargs)
                )
                
                if has_hints:
                    functions_with_hints += 1
                    
                    # Check for partial type hints (some params have hints, others don't)
                    params_with_hints = sum(1 for arg in node.args.args 
                                          if arg.arg not in ['self', 'cls'] and arg.annotation is not None)
                    total_params = len([arg for arg in node.args.args if arg.arg not in ['self', 'cls']])
                    
                    if total_params > 0 and 0 < params_with_hints < total_params:
                        issues.append(self.create_issue(
                            line=node.lineno,
                            column=node.col_offset,
                            message=f"Function '{node.name}' has partial type hints ({params_with_hints}/{total_params} parameters)",
                            fix_suggestion="Add type hints to all parameters for consistency",
                            severity=IssueSeverity.INFO,
                            metadata={"function_name": node.name, "partial_hints": True}
                        ))
                else:
                    functions_without_hints += 1
                
                self.generic_visit(node)
            
            def visit_AsyncFunctionDef(self, node):
                self.visit_FunctionDef(node)
        
        visitor = TypeConsistencyVisitor()
        visitor.create_issue = self.create_issue
        visitor.visit(ast_tree)
        
        # Check overall consistency
        if total_functions > 1:
            hint_ratio = functions_with_hints / total_functions
            
            # If some functions have hints but not all, suggest consistency
            if 0.2 < hint_ratio < 0.8:
                issues.append(self.create_issue(
                    line=1,
                    column=0,
                    message=f"Inconsistent type hint usage: {functions_with_hints}/{total_functions} functions have type hints",
                    fix_suggestion="Consider adding type hints to all public functions for consistency",
                    severity=IssueSeverity.INFO,
                    metadata={"hint_ratio": hint_ratio, "total_functions": total_functions}
                ))
        
        return issues


class ComplexTypeHintsRule(AnalysisRule):
    """Check for overly complex type hints that could be simplified."""
    
    def __init__(self):
        super().__init__(
            rule_id="T003",
            category=RuleCategory.TYPES,
            name="Complex Type Hints",
            description="Simplify overly complex type hints",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        
        class ComplexTypeVisitor(ast.NodeVisitor):
            def visit_FunctionDef(self, node):
                # Check return type complexity
                if node.returns:
                    complexity = self._calculate_type_complexity(node.returns)
                    if complexity > 3:
                        issues.append(self.create_issue(
                            line=node.lineno,
                            column=node.col_offset,
                            message=f"Function '{node.name}' has complex return type hint",
                            fix_suggestion="Consider using type aliases for complex type hints",
                            severity=IssueSeverity.INFO,
                            metadata={"function_name": node.name, "complexity": complexity, "hint_type": "return"}
                        ))
                
                # Check parameter type complexity
                for arg in node.args.args:
                    if arg.annotation:
                        complexity = self._calculate_type_complexity(arg.annotation)
                        if complexity > 3:
                            issues.append(self.create_issue(
                                line=node.lineno,
                                column=node.col_offset,
                                message=f"Parameter '{arg.arg}' in function '{node.name}' has complex type hint",
                                fix_suggestion="Consider using type aliases for complex type hints",
                                severity=IssueSeverity.INFO,
                                metadata={"function_name": node.name, "parameter": arg.arg, "complexity": complexity}
                            ))
                
                self.generic_visit(node)
            
            def visit_AsyncFunctionDef(self, node):
                self.visit_FunctionDef(node)
            
            def _calculate_type_complexity(self, type_node):
                """Calculate complexity of a type hint."""
                if isinstance(type_node, ast.Name):
                    return 1
                elif isinstance(type_node, ast.Constant):
                    return 1
                elif isinstance(type_node, ast.Subscript):
                    # Generic types like List[int], Dict[str, int]
                    base_complexity = 1
                    if isinstance(type_node.slice, ast.Tuple):
                        # Multiple type parameters
                        return base_complexity + sum(self._calculate_type_complexity(elt) 
                                                   for elt in type_node.slice.elts)
                    else:
                        return base_complexity + self._calculate_type_complexity(type_node.slice)
                elif isinstance(type_node, ast.BinOp) and isinstance(type_node.op, ast.BitOr):
                    # Union types (X | Y)
                    return self._calculate_type_complexity(type_node.left) + self._calculate_type_complexity(type_node.right)
                elif isinstance(type_node, ast.Tuple):
                    # Tuple types
                    return sum(self._calculate_type_complexity(elt) for elt in type_node.elts)
                else:
                    return 2  # Default for unknown complex types
        
        visitor = ComplexTypeVisitor()
        visitor.create_issue = self.create_issue
        visitor.visit(ast_tree)
        
        return issues


class TypeAliasRule(AnalysisRule):
    """Suggest type aliases for repeated complex types."""
    
    def __init__(self):
        super().__init__(
            rule_id="T004",
            category=RuleCategory.TYPES,
            name="Type Alias",
            description="Use type aliases for repeated complex types",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        
        # Collect all type hints and count occurrences
        type_hints = []
        
        class TypeCollector(ast.NodeVisitor):
            def visit_FunctionDef(self, node):
                if node.returns:
                    type_hints.append((node.lineno, ast.unparse(node.returns)))
                
                for arg in node.args.args:
                    if arg.annotation:
                        type_hints.append((node.lineno, ast.unparse(arg.annotation)))
                
                self.generic_visit(node)
            
            def visit_AsyncFunctionDef(self, node):
                self.visit_FunctionDef(node)
        
        visitor = TypeCollector()
        visitor.visit(ast_tree)
        
        # Count type hint occurrences
        type_counts = {}
        for line, type_str in type_hints:
            if len(type_str) > 20:  # Only consider complex types
                if type_str in type_counts:
                    type_counts[type_str].append(line)
                else:
                    type_counts[type_str] = [line]
        
        # Suggest type aliases for repeated complex types
        for type_str, lines in type_counts.items():
            if len(lines) >= 3:  # Used 3 or more times
                issues.append(self.create_issue(
                    line=lines[0],
                    column=0,
                    message=f"Complex type '{type_str[:50]}...' is used {len(lines)} times",
                    fix_suggestion=f"Consider creating a type alias: TypeAlias = {type_str}",
                    severity=IssueSeverity.INFO,
                    metadata={"type_hint": type_str, "occurrences": len(lines), "lines": lines}
                ))
        
        return issues


class GenericTypeRule(AnalysisRule):
    """Check for proper use of generic types."""
    
    def __init__(self):
        super().__init__(
            rule_id="T005",
            category=RuleCategory.TYPES,
            name="Generic Type Usage",
            description="Use specific generic types instead of bare containers",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        
        class GenericTypeVisitor(ast.NodeVisitor):
            def visit_FunctionDef(self, node):
                # Check return type
                if node.returns:
                    self._check_generic_usage(node.returns, node.lineno, node.col_offset, 
                                            f"return type of function '{node.name}'")
                
                # Check parameter types
                for arg in node.args.args:
                    if arg.annotation:
                        self._check_generic_usage(arg.annotation, node.lineno, node.col_offset,
                                                f"parameter '{arg.arg}' in function '{node.name}'")
                
                self.generic_visit(node)
            
            def visit_AsyncFunctionDef(self, node):
                self.visit_FunctionDef(node)
            
            def _check_generic_usage(self, type_node, line, col, context_desc):
                """Check if generic types are used properly."""
                if isinstance(type_node, ast.Name):
                    # Check for bare container types
                    bare_containers = {
                        'list': 'List[T]',
                        'dict': 'Dict[K, V]',
                        'set': 'Set[T]',
                        'tuple': 'Tuple[T, ...]',
                        'frozenset': 'FrozenSet[T]'
                    }
                    
                    if type_node.id in bare_containers:
                        issues.append(self.create_issue(
                            line=line,
                            column=col,
                            message=f"Use specific generic type instead of bare '{type_node.id}' for {context_desc}",
                            fix_suggestion=f"Use {bare_containers[type_node.id]} instead of {type_node.id}",
                            severity=IssueSeverity.INFO,
                            metadata={"bare_type": type_node.id, "suggested": bare_containers[type_node.id]}
                        ))
        
        visitor = GenericTypeVisitor()
        visitor.create_issue = self.create_issue
        visitor.visit(ast_tree)
        
        return issues


class OptionalTypeRule(AnalysisRule):
    """Check for proper use of Optional types."""
    
    def __init__(self):
        super().__init__(
            rule_id="T006",
            category=RuleCategory.TYPES,
            name="Optional Type Usage",
            description="Use Optional[T] or T | None for nullable types",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        
        class OptionalTypeVisitor(ast.NodeVisitor):
            def visit_FunctionDef(self, node):
                # Check for default None parameters without Optional type
                for arg in node.args.args:
                    if arg.annotation and self._has_none_default(node, arg.arg):
                        if not self._is_optional_type(arg.annotation):
                            issues.append(self.create_issue(
                                line=node.lineno,
                                column=node.col_offset,
                                message=f"Parameter '{arg.arg}' has default None but type is not Optional",
                                fix_suggestion=f"Use Optional[{ast.unparse(arg.annotation)}] or {ast.unparse(arg.annotation)} | None",
                                severity=IssueSeverity.INFO,
                                metadata={"parameter": arg.arg, "function": node.name}
                            ))
                
                self.generic_visit(node)
            
            def visit_AsyncFunctionDef(self, node):
                self.visit_FunctionDef(node)
            
            def _has_none_default(self, func_node, param_name):
                """Check if parameter has None as default value."""
                # Get parameter index
                param_names = [arg.arg for arg in func_node.args.args]
                if param_name not in param_names:
                    return False
                
                param_index = param_names.index(param_name)
                defaults_start = len(param_names) - len(func_node.args.defaults)
                
                if param_index >= defaults_start:
                    default_index = param_index - defaults_start
                    default_value = func_node.args.defaults[default_index]
                    return isinstance(default_value, ast.Constant) and default_value.value is None
                
                return False
            
            def _is_optional_type(self, type_node):
                """Check if type annotation represents Optional."""
                # Check for Optional[T]
                if (isinstance(type_node, ast.Subscript) and
                    isinstance(type_node.value, ast.Name) and
                    type_node.value.id == 'Optional'):
                    return True
                
                # Check for Union[T, None] or T | None
                if isinstance(type_node, ast.BinOp) and isinstance(type_node.op, ast.BitOr):
                    # Check if one side is None
                    return (self._is_none_type(type_node.left) or 
                            self._is_none_type(type_node.right))
                
                return False
            
            def _is_none_type(self, type_node):
                """Check if type node represents None."""
                return (isinstance(type_node, ast.Constant) and type_node.value is None) or \
                       (isinstance(type_node, ast.Name) and type_node.id == 'None')
        
        visitor = OptionalTypeVisitor()
        visitor.create_issue = self.create_issue
        visitor.visit(ast_tree)
        
        return issues


class EnhancedTypeCheckingRule(AnalysisRule):
    """Enhanced type checking using the type inference engine."""

    def __init__(self):
        super().__init__(
            rule_id="T007",
            category=RuleCategory.TYPES,
            name="Enhanced Type Checking",
            description="Comprehensive type checking with inference and validation",
            severity=IssueSeverity.WARNING
        )
        self.type_engine = TypeInferenceEngine()

    async def analyze(self, target: AnalysisTarget, content: str,
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Run enhanced type checking."""
        return self.type_engine.analyze_file(target, content, ast_tree)


class MypyIntegrationRule(AnalysisRule):
    """Integration with mypy for external type checking."""

    def __init__(self):
        super().__init__(
            rule_id="T008",
            category=RuleCategory.TYPES,
            name="Mypy Integration",
            description="External type checking using mypy",
            severity=IssueSeverity.WARNING
        )
        self.mypy_integration = MypyIntegration()

    async def analyze(self, target: AnalysisTarget, content: str,
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Run mypy type checking."""
        if target.path and target.path.exists():
            return self.mypy_integration.run_mypy_check(target.path)
        return []
