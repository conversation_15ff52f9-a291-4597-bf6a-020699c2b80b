"""
Security Analysis Rules
=======================

Security vulnerability detection and unsafe pattern analysis rules.
"""

import ast
import re
from typing import List

from ..models import RuleCategory, IssueSeverity
from ..registry import AnalysisRule, AnalysisTarget, AnalysisIssue, AnalysisContext


class HardcodedPasswordRule(AnalysisRule):
    """Detect hardcoded passwords and secrets."""
    
    def __init__(self):
        super().__init__(
            rule_id="SEC001",
            category=RuleCategory.SECURITY,
            name="Hardcoded Passwords",
            description="Avoid hardcoded passwords and secrets in code",
            severity=IssueSeverity.ERROR
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        
        # Patterns that suggest hardcoded secrets
        secret_patterns = [
            (r'password\s*=\s*["\'][^"\']{3,}["\']', "password"),
            (r'passwd\s*=\s*["\'][^"\']{3,}["\']', "password"),
            (r'secret\s*=\s*["\'][^"\']{8,}["\']', "secret"),
            (r'api_key\s*=\s*["\'][^"\']{8,}["\']', "API key"),
            (r'token\s*=\s*["\'][^"\']{8,}["\']', "token"),
            (r'private_key\s*=\s*["\'][^"\']{20,}["\']', "private key"),
        ]
        
        lines = content.split('\n')
        for line_num, line in enumerate(lines, 1):
            line_lower = line.lower()
            for pattern, secret_type in secret_patterns:
                if re.search(pattern, line_lower, re.IGNORECASE):
                    match = re.search(pattern, line_lower, re.IGNORECASE)
                    if match:
                        issues.append(self.create_issue(
                            line=line_num,
                            column=match.start(),
                            message=f"Possible hardcoded {secret_type} detected",
                            fix_suggestion=f"Use environment variables or secure configuration for {secret_type}",
                            severity=IssueSeverity.ERROR,
                            metadata={"secret_type": secret_type}
                        ))
        
        return issues


class SqlInjectionRule(AnalysisRule):
    """Detect potential SQL injection vulnerabilities."""
    
    def __init__(self):
        super().__init__(
            rule_id="SEC002",
            category=RuleCategory.SECURITY,
            name="SQL Injection",
            description="Detect potential SQL injection vulnerabilities",
            severity=IssueSeverity.ERROR
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        
        class SqlInjectionVisitor(ast.NodeVisitor):
            def visit_BinOp(self, node):
                # Check for string concatenation with SQL keywords
                if isinstance(node.op, ast.Add):
                    if self._contains_sql_and_format(node):
                        issues.append(self.create_issue(
                            line=node.lineno,
                            column=node.col_offset,
                            message="Possible SQL injection vulnerability from string concatenation",
                            fix_suggestion="Use parameterized queries instead of string concatenation",
                            severity=IssueSeverity.ERROR,
                            metadata={"vulnerability_type": "sql_injection"}
                        ))
                self.generic_visit(node)
            
            def visit_Call(self, node):
                # Check for format() or % formatting with SQL
                if isinstance(node.func, ast.Attribute) and node.func.attr == 'format':
                    if self._is_sql_string(node.func.value):
                        issues.append(self.create_issue(
                            line=node.lineno,
                            column=node.col_offset,
                            message="Possible SQL injection vulnerability from string formatting",
                            fix_suggestion="Use parameterized queries instead of string formatting",
                            severity=IssueSeverity.ERROR,
                            metadata={"vulnerability_type": "sql_injection"}
                        ))
                self.generic_visit(node)
            
            def _contains_sql_and_format(self, node):
                # Simple heuristic to detect SQL-like strings being concatenated
                sql_keywords = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'FROM', 'WHERE']
                
                def extract_string_value(n):
                    if isinstance(n, ast.Constant) and isinstance(n.value, str):
                        return n.value.upper()
                    return ""
                
                left_str = extract_string_value(node.left)
                right_str = extract_string_value(node.right)
                
                return any(keyword in left_str or keyword in right_str for keyword in sql_keywords)
            
            def _is_sql_string(self, node):
                if isinstance(node, ast.Constant) and isinstance(node.value, str):
                    sql_keywords = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'FROM', 'WHERE']
                    return any(keyword in node.value.upper() for keyword in sql_keywords)
                return False
        
        visitor = SqlInjectionVisitor()
        visitor.create_issue = self.create_issue
        visitor.visit(ast_tree)
        
        return issues


class UnsafeEvalRule(AnalysisRule):
    """Detect use of unsafe eval() and exec() functions."""
    
    def __init__(self):
        super().__init__(
            rule_id="SEC003",
            category=RuleCategory.SECURITY,
            name="Unsafe Eval",
            description="Avoid using eval() and exec() with untrusted input",
            severity=IssueSeverity.ERROR
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        
        class UnsafeEvalVisitor(ast.NodeVisitor):
            def visit_Call(self, node):
                if isinstance(node.func, ast.Name):
                    if node.func.id in ['eval', 'exec']:
                        issues.append(self.create_issue(
                            line=node.lineno,
                            column=node.col_offset,
                            message=f"Use of {node.func.id}() is potentially unsafe",
                            fix_suggestion=f"Avoid {node.func.id}() or ensure input is trusted and validated",
                            severity=IssueSeverity.ERROR,
                            metadata={"function": node.func.id}
                        ))
                    elif node.func.id == 'compile' and len(node.args) >= 3:
                        # Check if compile() is used with 'eval' or 'exec' mode
                        if (isinstance(node.args[2], ast.Constant) and 
                            node.args[2].value in ['eval', 'exec']):
                            issues.append(self.create_issue(
                                line=node.lineno,
                                column=node.col_offset,
                                message=f"Use of compile() with '{node.args[2].value}' mode is potentially unsafe",
                                fix_suggestion="Ensure compiled code is from trusted sources",
                                severity=IssueSeverity.WARNING,
                                metadata={"function": "compile", "mode": node.args[2].value}
                            ))
                self.generic_visit(node)
        
        visitor = UnsafeEvalVisitor()
        visitor.create_issue = self.create_issue
        visitor.visit(ast_tree)
        
        return issues


class WeakCryptographyRule(AnalysisRule):
    """Detect use of weak cryptographic functions."""
    
    def __init__(self):
        super().__init__(
            rule_id="SEC004",
            category=RuleCategory.SECURITY,
            name="Weak Cryptography",
            description="Detect use of weak cryptographic algorithms",
            severity=IssueSeverity.WARNING
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        
        # Weak algorithms to detect
        weak_algorithms = {
            'md5': 'MD5 is cryptographically broken',
            'sha1': 'SHA1 is cryptographically weak',
            'des': 'DES has insufficient key length',
            'rc4': 'RC4 has known vulnerabilities'
        }
        
        class WeakCryptoVisitor(ast.NodeVisitor):
            def visit_Call(self, node):
                # Check for hashlib weak algorithms
                if (isinstance(node.func, ast.Attribute) and 
                    isinstance(node.func.value, ast.Name) and 
                    node.func.value.id == 'hashlib'):
                    
                    if node.func.attr.lower() in weak_algorithms:
                        issues.append(self.create_issue(
                            line=node.lineno,
                            column=node.col_offset,
                            message=f"Use of weak cryptographic algorithm: {node.func.attr}",
                            fix_suggestion=f"Use stronger algorithms like SHA-256 or SHA-3. {weak_algorithms[node.func.attr.lower()]}",
                            severity=IssueSeverity.WARNING,
                            metadata={"algorithm": node.func.attr, "reason": weak_algorithms[node.func.attr.lower()]}
                        ))
                
                # Check for direct hashlib.new() calls with weak algorithms
                elif (isinstance(node.func, ast.Attribute) and 
                      isinstance(node.func.value, ast.Name) and 
                      node.func.value.id == 'hashlib' and 
                      node.func.attr == 'new' and 
                      node.args):
                    
                    if (isinstance(node.args[0], ast.Constant) and 
                        isinstance(node.args[0].value, str) and 
                        node.args[0].value.lower() in weak_algorithms):
                        
                        algo = node.args[0].value.lower()
                        issues.append(self.create_issue(
                            line=node.lineno,
                            column=node.col_offset,
                            message=f"Use of weak cryptographic algorithm: {algo}",
                            fix_suggestion=f"Use stronger algorithms like SHA-256 or SHA-3. {weak_algorithms[algo]}",
                            severity=IssueSeverity.WARNING,
                            metadata={"algorithm": algo, "reason": weak_algorithms[algo]}
                        ))
                
                self.generic_visit(node)
        
        visitor = WeakCryptoVisitor()
        visitor.create_issue = self.create_issue
        visitor.visit(ast_tree)
        
        return issues


class InsecureRandomRule(AnalysisRule):
    """Detect use of insecure random number generation."""
    
    def __init__(self):
        super().__init__(
            rule_id="SEC005",
            category=RuleCategory.SECURITY,
            name="Insecure Random",
            description="Use cryptographically secure random number generation for security purposes",
            severity=IssueSeverity.WARNING
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        
        class InsecureRandomVisitor(ast.NodeVisitor):
            def visit_Call(self, node):
                # Check for random module usage
                if (isinstance(node.func, ast.Attribute) and 
                    isinstance(node.func.value, ast.Name) and 
                    node.func.value.id == 'random'):
                    
                    # Common random functions that shouldn't be used for security
                    insecure_functions = [
                        'random', 'randint', 'choice', 'choices', 
                        'shuffle', 'sample', 'uniform', 'randrange'
                    ]
                    
                    if node.func.attr in insecure_functions:
                        issues.append(self.create_issue(
                            line=node.lineno,
                            column=node.col_offset,
                            message=f"Use of insecure random function: random.{node.func.attr}()",
                            fix_suggestion="Use secrets module for cryptographically secure random generation",
                            severity=IssueSeverity.WARNING,
                            metadata={"function": f"random.{node.func.attr}"}
                        ))
                
                self.generic_visit(node)
        
        visitor = InsecureRandomVisitor()
        visitor.create_issue = self.create_issue
        visitor.visit(ast_tree)
        
        return issues
