"""
File: vibe_check/core/vcs/rules/performance_rules.py
Purpose: Performance optimization analysis rules
Related Files: vibe_check/core/vcs/rules/
Dependencies: ast, typing
"""

import ast

from vibe_check.core.vcs.models import (
    AnalysisTarget, AnalysisContext, AnalysisIssue, 
    RuleCategory, IssueSeverity
)
from vibe_check.core.vcs.registry import AnalysisRule
from vibe_check.core.logging import get_logger
from typing import List

logger = get_logger(__name__)


class LoopOptimizationRule(AnalysisRule):
    """Detects loop optimization opportunities."""
    
    def __init__(self):
        super().__init__(
            rule_id="PERF001",
            category=RuleCategory.COMPLEXITY,
            name="Loop Optimization",
            description="Detects loop optimization opportunities",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str,
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Analyze loop optimization opportunities (limited to prevent spam)."""
        issues = []

        # Limit to top-level nodes only to prevent spam
        for node in ast_tree.body if hasattr(ast_tree, 'body') else []:
            if isinstance(node, ast.For):
                issues.extend(self._check_for_loop(node))
            elif isinstance(node, ast.While):
                issues.extend(self._check_while_loop(node))
            elif isinstance(node, ast.ListComp):
                issues.extend(self._check_list_comprehension(node))

            # Limit to max 3 loop issues per file
            if len(issues) >= 3:
                break

        return issues
    
    def _check_for_loop(self, node: ast.For) -> List[AnalysisIssue]:
        """Check for loop optimization opportunities."""
        issues = []
        
        # Check for range(len(list)) pattern
        if isinstance(node.iter, ast.Call):
            if (isinstance(node.iter.func, ast.Name) and 
                node.iter.func.id == 'range' and
                len(node.iter.args) == 1):
                
                arg = node.iter.args[0]
                if (isinstance(arg, ast.Call) and
                    isinstance(arg.func, ast.Name) and
                    arg.func.id == 'len'):
                    
                    issues.append(self.create_issue(
                        line=node.lineno,
                        column=node.col_offset,
                        message="Use enumerate() instead of range(len())",
                        fix_suggestion="Replace 'for i in range(len(items)):' with 'for i, item in enumerate(items):'"
                    ))
        
        # Check for nested loops that could be optimized
        nested_loops = [n for n in ast.walk(node) if isinstance(n, (ast.For, ast.While)) and n != node]
        if len(nested_loops) >= 2:
            issues.append(self.create_issue(
                line=node.lineno,
                column=node.col_offset,
                message="Deeply nested loops may impact performance",
                fix_suggestion="Consider refactoring or using more efficient algorithms"
            ))
        
        return issues
    
    def _check_while_loop(self, node: ast.While) -> List[AnalysisIssue]:
        """Check while loop optimization opportunities."""
        issues = []
        
        # Check for infinite loop patterns
        if isinstance(node.test, ast.Constant) and node.test.value is True:
            # Check if there's a break statement
            has_break = any(
                isinstance(n, ast.Break)
                for n in ast.walk(node)
            )
            
            if not has_break:
                issues.append(self.create_issue(
                    line=node.lineno,
                    column=node.col_offset,
                    message="Potential infinite loop detected",
                    fix_suggestion="Ensure loop has proper exit condition"
                ))
        
        return issues
    
    def _check_list_comprehension(self, node: ast.ListComp) -> List[AnalysisIssue]:
        """Check list comprehension optimization opportunities."""
        issues = []
        
        # Check for complex comprehensions that should be functions
        complexity = len(node.generators) + sum(len(gen.ifs) for gen in node.generators)
        
        if complexity > 3:
            issues.append(self.create_issue(
                line=node.lineno,
                column=node.col_offset,
                message="Complex list comprehension should be a function",
                fix_suggestion="Convert to function for better readability and performance"
            ))
        
        # Check for nested comprehensions
        nested_comps = [n for n in ast.walk(node.elt) if isinstance(n, (ast.ListComp, ast.DictComp, ast.SetComp))]
        if nested_comps:
            issues.append(self.create_issue(
                line=node.lineno,
                column=node.col_offset,
                message="Nested comprehensions can be hard to read and slow",
                fix_suggestion="Consider using nested functions or itertools"
            ))
        
        return issues


class DataStructureRule(AnalysisRule):
    """Analyzes data structure usage for performance."""
    
    def __init__(self):
        super().__init__(
            rule_id="PERF002",
            category=RuleCategory.COMPLEXITY,
            name="Data Structure Optimization",
            description="Analyzes data structure usage for performance",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Analyze data structure usage."""
        issues = []
        
        for node in ast.walk(ast_tree):
            if isinstance(node, ast.Call):
                issues.extend(self._check_function_calls(node))
            elif isinstance(node, ast.For):
                issues.extend(self._check_membership_tests(node))
        
        return issues
    
    def _check_function_calls(self, node: ast.Call) -> List[AnalysisIssue]:
        """Check function calls for data structure optimization."""
        issues = []
        
        if isinstance(node.func, ast.Attribute):
            # Check for list.append() in loops
            if node.func.attr == 'append':
                # Check if we're in a loop
                parent = node
                in_loop = False
                while hasattr(parent, 'parent'):
                    parent = parent.parent
                    if isinstance(parent, (ast.For, ast.While)):
                        in_loop = True
                        break
                
                if in_loop:
                    issues.append(self.create_issue(
                        line=node.lineno,
                        column=node.col_offset,
                        message="Consider using list comprehension instead of append in loop",
                        fix_suggestion="Use list comprehension for better performance"
                    ))
        
        elif isinstance(node.func, ast.Name):
            # Check for inefficient operations
            if node.func.id == 'list' and len(node.args) == 1:
                arg = node.args[0]
                if isinstance(arg, ast.Call) and isinstance(arg.func, ast.Name):
                    if arg.func.id in ['range', 'enumerate']:
                        issues.append(self.create_issue(
                            line=node.lineno,
                            column=node.col_offset,
                            message="Unnecessary list() conversion",
                            fix_suggestion="Use generator directly if possible"
                        ))
        
        return issues
    
    def _check_membership_tests(self, node: ast.For) -> List[AnalysisIssue]:
        """Check for inefficient membership tests."""
        issues = []
        
        # Look for 'in' operations on lists that should use sets
        for child in ast.walk(node):
            if isinstance(child, ast.Compare):
                for op in child.ops:
                    if isinstance(op, ast.In):
                        # Check if we're testing membership in a list literal
                        for comparator in child.comparators:
                            if isinstance(comparator, ast.List) and len(comparator.elts) > 5:
                                issues.append(self.create_issue(
                                    line=child.lineno,
                                    column=child.col_offset,
                                    message="Use set for membership testing instead of list",
                                    fix_suggestion="Convert list to set for O(1) membership testing"
                                ))
        
        return issues


class StringOptimizationRule(AnalysisRule):
    """Analyzes string operations for performance."""
    
    def __init__(self):
        super().__init__(
            rule_id="PERF003",
            category=RuleCategory.COMPLEXITY,
            name="String Optimization",
            description="Analyzes string operations for performance",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str,
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Analyze string operations (limited to prevent spam)."""
        issues = []

        # Limit to top-level nodes only and max 3 issues
        for node in ast_tree.body if hasattr(ast_tree, 'body') else []:
            if isinstance(node, ast.BinOp):
                issues.extend(self._check_string_concatenation(node))
            elif isinstance(node, ast.Call):
                issues.extend(self._check_string_methods(node))

            # Limit to max 3 string issues per file
            if len(issues) >= 3:
                break

        return issues
    
    def _check_string_concatenation(self, node: ast.BinOp) -> List[AnalysisIssue]:
        """Check string concatenation patterns."""
        issues = []
        
        if isinstance(node.op, ast.Add):
            # Check if both operands are strings
            if self._is_string_operation(node.left) and self._is_string_operation(node.right):
                # Check if we're in a loop
                parent = node
                in_loop = False
                while hasattr(parent, 'parent'):
                    parent = parent.parent
                    if isinstance(parent, (ast.For, ast.While)):
                        in_loop = True
                        break
                
                if in_loop:
                    issues.append(self.create_issue(
                        line=node.lineno,
                        column=node.col_offset,
                        message="String concatenation in loop is inefficient",
                        fix_suggestion="Use join() or f-strings for better performance"
                    ))
        
        return issues
    
    def _check_string_methods(self, node: ast.Call) -> List[AnalysisIssue]:
        """Check string method usage."""
        issues = []
        
        if isinstance(node.func, ast.Attribute):
            # Check for inefficient string operations
            if node.func.attr == 'replace' and len(node.args) >= 2:
                # Multiple replace calls could use str.translate
                issues.append(self.create_issue(
                    line=node.lineno,
                    column=node.col_offset,
                    message="Multiple replace() calls could use str.translate()",
                    fix_suggestion="Consider using str.translate() for multiple character replacements"
                ))
        
        return issues
    
    def _is_string_operation(self, node: ast.AST) -> bool:
        """Check if node represents a string operation."""
        if isinstance(node, ast.Constant) and isinstance(node.value, str):
            return True
        elif isinstance(node, ast.Call):
            if isinstance(node.func, ast.Name) and node.func.id == 'str':
                return True
        elif isinstance(node, ast.JoinedStr):  # f-string
            return True
        return False


class ImportOptimizationRule(AnalysisRule):
    """Analyzes import statements for performance."""
    
    def __init__(self):
        super().__init__(
            rule_id="PERF004",
            category=RuleCategory.IMPORTS,
            name="Import Optimization",
            description="Analyzes import statements for performance",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Analyze import optimization."""
        issues = []
        
        for node in ast.walk(ast_tree):
            if isinstance(node, ast.Import):
                issues.extend(self._check_import_statement(node))
            elif isinstance(node, ast.ImportFrom):
                issues.extend(self._check_from_import(node))
        
        return issues
    
    def _check_import_statement(self, node: ast.Import) -> List[AnalysisIssue]:
        """Check import statement optimization."""
        issues = []
        
        # Check for imports inside functions
        if self._is_inside_function(node):
            issues.append(self.create_issue(
                line=node.lineno,
                column=node.col_offset,
                message="Import inside function may impact performance",
                fix_suggestion="Move import to module level unless lazy loading is needed"
            ))
        
        return issues
    
    def _check_from_import(self, node: ast.ImportFrom) -> List[AnalysisIssue]:
        """Check from import optimization."""
        issues = []
        
        # Check for star imports
        for alias in node.names:
            if alias.name == '*':
                issues.append(self.create_issue(
                    line=node.lineno,
                    column=node.col_offset,
                    message="Star imports can impact performance and namespace",
                    fix_suggestion="Import specific names instead of using '*'"
                ))
        
        return issues
    
    def _is_inside_function(self, node: ast.AST) -> bool:
        """Check if node is inside a function."""
        # This is a simplified check - in a real implementation,
        # we'd need to track the AST hierarchy
        return False  # Placeholder


class MemoryOptimizationRule(AnalysisRule):
    """Analyzes memory usage patterns."""
    
    def __init__(self):
        super().__init__(
            rule_id="PERF005",
            category=RuleCategory.COMPLEXITY,
            name="Memory Optimization",
            description="Analyzes memory usage patterns",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Analyze memory optimization."""
        issues = []
        
        for node in ast.walk(ast_tree):
            if isinstance(node, ast.Call):
                issues.extend(self._check_memory_usage(node))
        
        return issues
    
    def _check_memory_usage(self, node: ast.Call) -> List[AnalysisIssue]:
        """Check memory usage patterns."""
        issues = []
        
        if isinstance(node.func, ast.Name):
            # Check for list() when generator would suffice
            if node.func.id == 'list' and len(node.args) == 1:
                arg = node.args[0]
                if isinstance(arg, (ast.GeneratorExp, ast.Call)):
                    issues.append(self.create_issue(
                        line=node.lineno,
                        column=node.col_offset,
                        message="Consider using generator instead of list for memory efficiency",
                        fix_suggestion="Use generator expression if full list is not needed"
                    ))
        
        return issues
