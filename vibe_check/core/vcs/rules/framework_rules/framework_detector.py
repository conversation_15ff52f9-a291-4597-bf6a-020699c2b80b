"""
File: vibe_check/core/vcs/rules/framework_rules/framework_detector.py
Purpose: Framework detection for Python projects
Related Files: vibe_check/core/vcs/rules/framework_rules/
Dependencies: ast, pathlib, typing
"""

import ast
from pathlib import Path
from dataclasses import dataclass
from enum import Enum

from vibe_check.core.vcs.models import AnalysisTarget
from vibe_check.core.logging import get_logger
from typing import Any, Dict, List, Optional

logger = get_logger(__name__)


class FrameworkType(Enum):
    """Supported framework types."""
    DJANGO = "django"
    FLASK = "flask"
    FASTAPI = "fastapi"
    PYRAMID = "pyramid"
    TORNADO = "tornado"
    BOTTLE = "bottle"
    UNKNOWN = "unknown"


@dataclass
class FrameworkDetectionResult:
    """Result of framework detection."""
    framework: FrameworkType
    confidence: float
    version: Optional[str] = None
    indicators: List[str] = None
    
    def __post_init__(self):
        if self.indicators is None:
            self.indicators = []


class FrameworkDetector:
    """Detects Python web frameworks in use within a project."""
    
    def __init__(self):
        self.framework_indicators = {
            FrameworkType.DJANGO: {
                'imports': [
                    'django', 'django.conf', 'django.urls', 'django.views',
                    'django.models', 'django.forms', 'django.contrib'
                ],
                'files': ['manage.py', 'settings.py', 'urls.py', 'wsgi.py'],
                'patterns': ['from django', 'import django', 'DJANGO_SETTINGS_MODULE']
            },
            FrameworkType.FLASK: {
                'imports': ['flask', 'Flask', 'flask.views', 'flask_'],
                'files': ['app.py', 'application.py'],
                'patterns': ['from flask', 'Flask(__name__)', '@app.route']
            },
            FrameworkType.FASTAPI: {
                'imports': ['fastapi', 'FastAPI', 'fastapi.'],
                'files': ['main.py'],
                'patterns': ['from fastapi', 'FastAPI()', '@app.get', '@app.post']
            },
            FrameworkType.PYRAMID: {
                'imports': ['pyramid', 'pyramid.config', 'pyramid.view'],
                'files': [],
                'patterns': ['from pyramid', 'pyramid.config']
            },
            FrameworkType.TORNADO: {
                'imports': ['tornado', 'tornado.web', 'tornado.ioloop'],
                'files': [],
                'patterns': ['tornado.web.RequestHandler', 'tornado.ioloop']
            },
            FrameworkType.BOTTLE: {
                'imports': ['bottle', 'from bottle'],
                'files': [],
                'patterns': ['@route(', 'bottle.route']
            }
        }
    
    def detect_framework(self, target: AnalysisTarget) -> FrameworkDetectionResult:
        """
        Detect framework from a single file.
        
        Args:
            target: Analysis target to examine
            
        Returns:
            Framework detection result
        """
        if not target.path or not target.path.exists():
            return FrameworkDetectionResult(FrameworkType.UNKNOWN, 0.0)
        
        content = target.get_content()
        
        # Try AST-based detection first
        try:
            ast_tree = target.get_ast()
            return self._detect_from_ast(ast_tree, content, target.path)
        except Exception as e:
            logger.debug(f"AST detection failed for {target.path}: {e}")
            # Fallback to text-based detection
            return self._detect_from_content(content, target.path)
    
    def detect_project_framework(self, project_root: Path) -> Dict[FrameworkType, float]:
        """
        Detect frameworks across an entire project with fast detection.

        Args:
            project_root: Root directory of the project

        Returns:
            Dictionary mapping frameworks to confidence scores
        """
        framework_scores = {fw: 0.0 for fw in FrameworkType}

        # Fast detection: Check framework-specific files first (most reliable)
        for framework, indicators in self.framework_indicators.items():
            for filename in indicators['files']:
                if (project_root / filename).exists():
                    framework_scores[framework] += 0.8  # High confidence for file presence

        # If we found strong indicators, return early
        max_score = max(framework_scores.values())
        if max_score >= 0.5:
            return framework_scores

        # Fallback: Sample-based analysis (analyze only first 10 Python files)
        total_files = 0
        max_files_to_analyze = 10

        for py_file in project_root.rglob("*.py"):
            if total_files >= max_files_to_analyze:
                break

            if self._should_analyze_file(py_file):
                try:
                    target = AnalysisTarget.from_file(py_file)
                    result = self.detect_framework(target)

                    if result.framework != FrameworkType.UNKNOWN:
                        framework_scores[result.framework] += result.confidence
                        total_files += 1

                except Exception as e:
                    logger.debug(f"Failed to analyze {py_file}: {e}")

        # Normalize scores based on sampled files
        if total_files > 0:
            for framework in framework_scores:
                framework_scores[framework] /= max(total_files, 1)

        return framework_scores
    
    def _detect_from_ast(self, ast_tree: ast.AST, content: str, file_path: Path) -> FrameworkDetectionResult:
        """Detect framework using AST analysis."""
        imports = self._extract_imports(ast_tree)
        decorators = self._extract_decorators(ast_tree)
        function_calls = self._extract_function_calls(ast_tree)
        
        best_framework = FrameworkType.UNKNOWN
        best_confidence = 0.0
        indicators = []
        
        for framework, framework_indicators in self.framework_indicators.items():
            confidence = 0.0
            framework_indicators_found = []
            
            # Check imports
            for import_pattern in framework_indicators['imports']:
                for imp in imports:
                    if import_pattern in imp:
                        confidence += 0.3
                        framework_indicators_found.append(f"import: {imp}")
            
            # Check patterns in content
            for pattern in framework_indicators['patterns']:
                if pattern in content:
                    confidence += 0.2
                    framework_indicators_found.append(f"pattern: {pattern}")
            
            # Check decorators
            for decorator in decorators:
                if any(pattern in decorator for pattern in framework_indicators['patterns']):
                    confidence += 0.3
                    framework_indicators_found.append(f"decorator: {decorator}")
            
            # Check function calls
            for call in function_calls:
                if any(pattern in call for pattern in framework_indicators['patterns']):
                    confidence += 0.2
                    framework_indicators_found.append(f"call: {call}")
            
            # Bonus for framework-specific files
            if file_path.name in framework_indicators['files']:
                confidence += 0.5
                framework_indicators_found.append(f"filename: {file_path.name}")
            
            if confidence > best_confidence:
                best_confidence = confidence
                best_framework = framework
                indicators = framework_indicators_found
        
        # Cap confidence at 1.0
        best_confidence = min(best_confidence, 1.0)
        
        return FrameworkDetectionResult(
            framework=best_framework,
            confidence=best_confidence,
            indicators=indicators
        )
    
    def _detect_from_content(self, content: str, file_path: Path) -> FrameworkDetectionResult:
        """Fallback text-based detection."""
        best_framework = FrameworkType.UNKNOWN
        best_confidence = 0.0
        indicators = []
        
        for framework, framework_indicators in self.framework_indicators.items():
            confidence = 0.0
            framework_indicators_found = []
            
            # Check patterns
            for pattern in framework_indicators['patterns']:
                if pattern in content:
                    confidence += 0.2
                    framework_indicators_found.append(f"pattern: {pattern}")
            
            # Check imports (simple text search)
            for import_pattern in framework_indicators['imports']:
                if import_pattern in content:
                    confidence += 0.3
                    framework_indicators_found.append(f"import: {import_pattern}")
            
            # Check filename
            if file_path.name in framework_indicators['files']:
                confidence += 0.5
                framework_indicators_found.append(f"filename: {file_path.name}")
            
            if confidence > best_confidence:
                best_confidence = confidence
                best_framework = framework
                indicators = framework_indicators_found
        
        return FrameworkDetectionResult(
            framework=best_framework,
            confidence=min(best_confidence, 1.0),
            indicators=indicators
        )
    
    def _extract_imports(self, ast_tree: ast.AST) -> List[str]:
        """Extract import statements from AST."""
        imports = []
        
        for node in ast.walk(ast_tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    imports.append(node.module)
                    for alias in node.names:
                        imports.append(f"{node.module}.{alias.name}")
        
        return imports
    
    def _extract_decorators(self, ast_tree: ast.AST) -> List[str]:
        """Extract decorator names from AST."""
        decorators = []
        
        for node in ast.walk(ast_tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef, ast.ClassDef)):
                for decorator in node.decorator_list:
                    if isinstance(decorator, ast.Name):
                        decorators.append(decorator.id)
                    elif isinstance(decorator, ast.Attribute):
                        decorators.append(ast.unparse(decorator))
                    elif isinstance(decorator, ast.Call):
                        if isinstance(decorator.func, ast.Name):
                            decorators.append(decorator.func.id)
                        elif isinstance(decorator.func, ast.Attribute):
                            decorators.append(ast.unparse(decorator.func))
        
        return decorators
    
    def _extract_function_calls(self, ast_tree: ast.AST) -> List[str]:
        """Extract function call names from AST."""
        calls = []
        
        for node in ast.walk(ast_tree):
            if isinstance(node, ast.Call):
                if isinstance(node.func, ast.Name):
                    calls.append(node.func.id)
                elif isinstance(node.func, ast.Attribute):
                    calls.append(ast.unparse(node.func))
        
        return calls
    
    def _should_analyze_file(self, file_path: Path) -> bool:
        """Check if file should be analyzed."""
        # Skip common non-framework files
        skip_patterns = [
            '__pycache__', '.git', '.venv', 'venv', 'node_modules',
            'migrations', '__init__.py'
        ]
        
        path_str = str(file_path)
        return not any(pattern in path_str for pattern in skip_patterns)
    
    def get_framework_summary(self, project_root: Path) -> Dict[str, Any]:
        """Get comprehensive framework detection summary."""
        framework_scores = self.detect_project_framework(project_root)
        
        # Find primary framework
        primary_framework = max(framework_scores.items(), key=lambda x: x[1])
        
        # Filter out frameworks with very low confidence
        significant_frameworks = {
            fw.value: score for fw, score in framework_scores.items()
            if score > 0.1
        }
        
        return {
            "primary_framework": primary_framework[0].value,
            "primary_confidence": primary_framework[1],
            "all_frameworks": {fw.value: score for fw, score in framework_scores.items()},
            "significant_frameworks": significant_frameworks,
            "is_multi_framework": len(significant_frameworks) > 1
        }
