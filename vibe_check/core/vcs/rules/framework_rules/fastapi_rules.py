"""
File: vibe_check/core/vcs/rules/framework_rules/fastapi_rules.py
Purpose: FastAPI-specific analysis rules
Related Files: vibe_check/core/vcs/rules/framework_rules/
Dependencies: ast, typing
"""

import ast

from vibe_check.core.vcs.models import (
    AnalysisTarget, AnalysisContext, AnalysisIssue, 
    RuleCategory, IssueSeverity
)
from vibe_check.core.vcs.registry import AnalysisRule
from vibe_check.core.logging import get_logger
from typing import List, Set

logger = get_logger(__name__)


class FastAPIAsyncRule(AnalysisRule):
    """Detects improper async usage in FastAPI applications."""
    
    def __init__(self):
        super().__init__(
            rule_id="FASTAPI001",
            category=RuleCategory.COMPLEXITY,
            name="FastAPI Async Usage",
            description="Detects improper async usage in FastAPI endpoints",
            severity=IssueSeverity.WARNING
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Analyze FastAPI async usage."""
        issues = []
        
        for node in ast.walk(ast_tree):
            if isinstance(node, ast.FunctionDef):
                if self._is_fastapi_endpoint(node):
                    issues.extend(self._check_async_usage(node))
        
        return issues
    
    def _is_fastapi_endpoint(self, func_node: ast.FunctionDef) -> bool:
        """Check if function is a FastAPI endpoint."""
        for decorator in func_node.decorator_list:
            if isinstance(decorator, ast.Attribute):
                if decorator.attr in ['get', 'post', 'put', 'delete', 'patch']:
                    return True
            elif isinstance(decorator, ast.Call):
                if isinstance(decorator.func, ast.Attribute):
                    if decorator.func.attr in ['get', 'post', 'put', 'delete', 'patch']:
                        return True
        return False
    
    def _check_async_usage(self, func_node: ast.FunctionDef) -> List[AnalysisIssue]:
        """Check async usage in endpoint."""
        issues = []
        
        # Check if function is async but doesn't use await
        if isinstance(func_node, ast.AsyncFunctionDef):
            has_await = any(
                isinstance(node, ast.Await)
                for node in ast.walk(func_node)
            )
            
            if not has_await:
                issues.append(self.create_issue(
                    line=func_node.lineno,
                    column=func_node.col_offset,
                    message=f"Async endpoint {func_node.name} doesn't use await",
                    fix_suggestion="Remove async if not needed, or add await for async operations"
                ))
        
        # Check for blocking operations in async functions
        elif isinstance(func_node, ast.AsyncFunctionDef):
            for node in ast.walk(func_node):
                if isinstance(node, ast.Call):
                    if self._is_blocking_operation(node):
                        issues.append(self.create_issue(
                            line=node.lineno,
                            column=node.col_offset,
                            message="Blocking operation in async endpoint",
                            fix_suggestion="Use async version of this operation or run in thread pool"
                        ))
        
        return issues
    
    def _is_blocking_operation(self, call_node: ast.Call) -> bool:
        """Check if call is a blocking operation."""
        blocking_functions = [
            'time.sleep', 'requests.get', 'requests.post',
            'open', 'input', 'print'  # Basic blocking operations
        ]
        
        if isinstance(call_node.func, ast.Attribute):
            func_name = f"{call_node.func.value.id}.{call_node.func.attr}"
            return func_name in blocking_functions
        
        return False


class FastAPIValidationRule(AnalysisRule):
    """Detects missing input validation in FastAPI endpoints."""
    
    def __init__(self):
        super().__init__(
            rule_id="FASTAPI002",
            category=RuleCategory.SECURITY,
            name="FastAPI Input Validation",
            description="Detects missing input validation in FastAPI endpoints",
            severity=IssueSeverity.WARNING
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Analyze FastAPI input validation."""
        issues = []
        
        for node in ast.walk(ast_tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                if self._is_fastapi_endpoint(node):
                    issues.extend(self._check_input_validation(node))
        
        return issues
    
    def _is_fastapi_endpoint(self, func_node: ast.FunctionDef) -> bool:
        """Check if function is a FastAPI endpoint."""
        for decorator in func_node.decorator_list:
            if isinstance(decorator, ast.Attribute):
                if decorator.attr in ['get', 'post', 'put', 'delete', 'patch']:
                    return True
            elif isinstance(decorator, ast.Call):
                if isinstance(decorator.func, ast.Attribute):
                    if decorator.func.attr in ['get', 'post', 'put', 'delete', 'patch']:
                        return True
        return False
    
    def _check_input_validation(self, func_node: ast.FunctionDef) -> List[AnalysisIssue]:
        """Check input validation in endpoint."""
        issues = []
        
        # Check for parameters without type hints
        for arg in func_node.args.args:
            if not arg.annotation:
                issues.append(self.create_issue(
                    line=func_node.lineno,
                    column=func_node.col_offset,
                    message=f"Parameter {arg.arg} missing type annotation",
                    fix_suggestion="Add type annotation for automatic validation"
                ))
        
        # Check for string parameters without validation
        for arg in func_node.args.args:
            if arg.annotation and isinstance(arg.annotation, ast.Name):
                if arg.annotation.id == 'str':
                    # Check if there's any validation in the function body
                    has_validation = self._has_string_validation(func_node, arg.arg)
                    if not has_validation:
                        issues.append(self.create_issue(
                            line=func_node.lineno,
                            column=func_node.col_offset,
                            message=f"String parameter {arg.arg} may need validation",
                            fix_suggestion="Consider using Pydantic models or Query/Path validators"
                        ))
        
        return issues
    
    def _has_string_validation(self, func_node: ast.FunctionDef, param_name: str) -> bool:
        """Check if string parameter has validation."""
        # Simple check for validation patterns
        for node in ast.walk(func_node):
            if isinstance(node, ast.Call):
                # Check for len() calls on the parameter
                if (isinstance(node.func, ast.Name) and 
                    node.func.id == 'len' and
                    node.args and
                    isinstance(node.args[0], ast.Name) and
                    node.args[0].id == param_name):
                    return True
        return False


class FastAPISecurityRule(AnalysisRule):
    """Detects security issues in FastAPI applications."""
    
    def __init__(self):
        super().__init__(
            rule_id="FASTAPI003",
            category=RuleCategory.SECURITY,
            name="FastAPI Security",
            description="Detects security issues in FastAPI applications",
            severity=IssueSeverity.ERROR
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Analyze FastAPI security."""
        issues = []
        
        # Check for missing authentication
        for node in ast.walk(ast_tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                if self._is_fastapi_endpoint(node):
                    if not self._has_authentication(node):
                        issues.append(self.create_issue(
                            line=node.lineno,
                            column=node.col_offset,
                            message=f"Endpoint {node.name} may need authentication",
                            fix_suggestion="Consider adding Depends(get_current_user) or similar authentication"
                        ))
        
        # Check for CORS configuration
        if 'CORSMiddleware' in content:
            if 'allow_origins=["*"]' in content:
                issues.append(self.create_issue(
                    line=1,
                    column=1,
                    message="CORS configured to allow all origins",
                    fix_suggestion="Specify exact origins instead of using '*'"
                ))
        
        return issues
    
    def _is_fastapi_endpoint(self, func_node: ast.FunctionDef) -> bool:
        """Check if function is a FastAPI endpoint."""
        for decorator in func_node.decorator_list:
            if isinstance(decorator, ast.Attribute):
                if decorator.attr in ['get', 'post', 'put', 'delete', 'patch']:
                    return True
            elif isinstance(decorator, ast.Call):
                if isinstance(decorator.func, ast.Attribute):
                    if decorator.func.attr in ['get', 'post', 'put', 'delete', 'patch']:
                        return True
        return False
    
    def _has_authentication(self, func_node: ast.FunctionDef) -> bool:
        """Check if endpoint has authentication."""
        # Check for Depends() in function parameters
        for arg in func_node.args.args:
            if arg.annotation:
                # Look for Depends annotation
                if isinstance(arg.annotation, ast.Call):
                    if (isinstance(arg.annotation.func, ast.Name) and
                        arg.annotation.func.id == 'Depends'):
                        return True
        
        # Check for authentication-related parameter names
        auth_params = ['current_user', 'user', 'token', 'auth']
        for arg in func_node.args.args:
            if arg.arg in auth_params:
                return True
        
        return False


class FastAPIResponseModelRule(AnalysisRule):
    """Detects missing response models in FastAPI endpoints."""
    
    def __init__(self):
        super().__init__(
            rule_id="FASTAPI004",
            category=RuleCategory.STYLE,
            name="FastAPI Response Models",
            description="Detects missing response models in FastAPI endpoints",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Analyze FastAPI response models."""
        issues = []
        
        for node in ast.walk(ast_tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                if self._is_fastapi_endpoint(node):
                    if not self._has_response_model(node):
                        issues.append(self.create_issue(
                            line=node.lineno,
                            column=node.col_offset,
                            message=f"Endpoint {node.name} missing response model",
                            fix_suggestion="Add response_model parameter to endpoint decorator"
                        ))
        
        return issues
    
    def _is_fastapi_endpoint(self, func_node: ast.FunctionDef) -> bool:
        """Check if function is a FastAPI endpoint."""
        for decorator in func_node.decorator_list:
            if isinstance(decorator, ast.Call):
                if isinstance(decorator.func, ast.Attribute):
                    if decorator.func.attr in ['get', 'post', 'put', 'delete', 'patch']:
                        return True
        return False
    
    def _has_response_model(self, func_node: ast.FunctionDef) -> bool:
        """Check if endpoint has response model."""
        for decorator in func_node.decorator_list:
            if isinstance(decorator, ast.Call):
                for keyword in decorator.keywords:
                    if keyword.arg == 'response_model':
                        return True
        return False


class FastAPIErrorHandlingRule(AnalysisRule):
    """Detects missing error handling in FastAPI endpoints."""
    
    def __init__(self):
        super().__init__(
            rule_id="FASTAPI005",
            category=RuleCategory.COMPLEXITY,
            name="FastAPI Error Handling",
            description="Detects missing error handling in FastAPI endpoints",
            severity=IssueSeverity.WARNING
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Analyze FastAPI error handling."""
        issues = []
        
        for node in ast.walk(ast_tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                if self._is_fastapi_endpoint(node):
                    if not self._has_error_handling(node):
                        issues.append(self.create_issue(
                            line=node.lineno,
                            column=node.col_offset,
                            message=f"Endpoint {node.name} missing error handling",
                            fix_suggestion="Add try-except blocks and raise HTTPException for errors"
                        ))
        
        return issues
    
    def _is_fastapi_endpoint(self, func_node: ast.FunctionDef) -> bool:
        """Check if function is a FastAPI endpoint."""
        for decorator in func_node.decorator_list:
            if isinstance(decorator, ast.Attribute):
                if decorator.attr in ['get', 'post', 'put', 'delete', 'patch']:
                    return True
            elif isinstance(decorator, ast.Call):
                if isinstance(decorator.func, ast.Attribute):
                    if decorator.func.attr in ['get', 'post', 'put', 'delete', 'patch']:
                        return True
        return False
    
    def _has_error_handling(self, func_node: ast.FunctionDef) -> bool:
        """Check if endpoint has error handling."""
        for node in ast.walk(func_node):
            if isinstance(node, ast.Try):
                return True
            if isinstance(node, ast.Raise):
                # Check if it's raising HTTPException
                if isinstance(node.exc, ast.Call):
                    if (isinstance(node.exc.func, ast.Name) and
                        node.exc.func.id == 'HTTPException'):
                        return True
        return False


class FastAPIRuleSet:
    """Collection of FastAPI-specific analysis rules."""
    
    @staticmethod
    def get_rules() -> List[AnalysisRule]:
        """Get all FastAPI analysis rules."""
        return [
            FastAPIAsyncRule(),
            FastAPIValidationRule(),
            FastAPISecurityRule(),
            FastAPIResponseModelRule(),
            FastAPIErrorHandlingRule()
        ]
    
    @staticmethod
    def get_rule_categories() -> Set[RuleCategory]:
        """Get categories covered by FastAPI rules."""
        return {
            RuleCategory.SECURITY,
            RuleCategory.COMPLEXITY,
            RuleCategory.STYLE
        }
    
    @staticmethod
    def get_framework_name() -> str:
        """Get framework name."""
        return "FastAPI"
