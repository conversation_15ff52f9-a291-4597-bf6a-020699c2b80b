"""
File: vibe_check/core/vcs/type_checking.py
Purpose: Type checking engine for VCS analysis
Related Files: vibe_check/core/vcs/engine.py, vibe_check/core/vcs/rules/type_rules.py
Dependencies: ast, typing, subprocess
"""

import ast
import subprocess
import sys
from pathlib import Path

from vibe_check.core.logging import get_logger
from .models import AnalysisTarget, AnalysisIssue, IssueSeverity, RuleCategory
from typing import Any, Dict, List, Optional

logger = get_logger(__name__)


class TypeInferenceEngine:
    """Basic type inference engine for Python code."""
    
    def __init__(self):
        self.builtin_types = {
            'int', 'float', 'str', 'bool', 'list', 'dict', 'tuple', 'set',
            'None', 'bytes', 'bytearray', 'complex', 'frozenset'
        }
        self.typing_imports = set()
        self.variable_types = {}
        self.function_signatures = {}
    
    def analyze_file(self, target: AnalysisTarget, content: str, ast_tree: ast.AST) -> List[AnalysisIssue]:
        """Analyze file for type-related issues."""
        issues = []
        
        # Reset state for new file
        self.typing_imports.clear()
        self.variable_types.clear()
        self.function_signatures.clear()
        
        # Collect typing imports
        self._collect_typing_imports(ast_tree)
        
        # Analyze type annotations
        issues.extend(self._analyze_type_annotations(ast_tree, target))
        
        # Check for missing type hints
        issues.extend(self._check_missing_type_hints(ast_tree, target))
        
        # Validate type annotations
        issues.extend(self._validate_type_annotations(ast_tree, target))
        
        return issues
    
    def _collect_typing_imports(self, tree: ast.AST) -> None:
        """Collect typing module imports."""
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    if alias.name == 'typing':
                        self.typing_imports.add('typing')
                    elif alias.name.startswith('typing.'):
                        self.typing_imports.add(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module == 'typing':
                    for alias in node.names:
                        self.typing_imports.add(alias.name)
    
    def _analyze_type_annotations(self, tree: ast.AST, target: AnalysisTarget) -> List[AnalysisIssue]:
        """Analyze type annotations in the code."""
        issues = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                # Check function annotations
                issues.extend(self._check_function_annotations(node, target))
            elif isinstance(node, ast.AnnAssign):
                # Check variable annotations
                issues.extend(self._check_variable_annotations(node, target))
        
        return issues
    
    def _check_function_annotations(self, node: ast.FunctionDef, target: AnalysisTarget) -> List[AnalysisIssue]:
        """Check function type annotations."""
        issues = []
        
        # Check if function has return annotation
        if not node.returns and not node.name.startswith('_'):
            # Skip private functions and special methods
            if not (node.name.startswith('__') and node.name.endswith('__')):
                issues.append(AnalysisIssue(
                    line=node.lineno,
                    column=node.col_offset,
                    message=f"Function '{node.name}' missing return type annotation",
                    severity=IssueSeverity.WARNING,
                    rule_id="T001",
                    category=RuleCategory.TYPES,
                    source="vcs_type_checker",
                    fix_suggestion=f"Add return type annotation: def {node.name}(...) -> ReturnType:",
                    auto_fixable=False,
                    metadata={"function_name": node.name, "file_path": str(target.path)}
                ))
        
        # Check parameter annotations
        for arg in node.args.args:
            if not arg.annotation and arg.arg != 'self' and arg.arg != 'cls':
                issues.append(AnalysisIssue(
                    line=node.lineno,
                    column=node.col_offset,
                    message=f"Parameter '{arg.arg}' in function '{node.name}' missing type annotation",
                    severity=IssueSeverity.INFO,
                    rule_id="T002",
                    category=RuleCategory.TYPES,
                    source="vcs_type_checker",
                    fix_suggestion=f"Add type annotation: {arg.arg}: ParameterType",
                    auto_fixable=False,
                    metadata={"parameter_name": arg.arg, "function_name": node.name, "file_path": str(target.path)}
                ))
        
        return issues
    
    def _check_variable_annotations(self, node: ast.AnnAssign, target: AnalysisTarget) -> List[AnalysisIssue]:
        """Check variable type annotations."""
        issues = []
        
        # Validate annotation syntax
        if node.annotation:
            try:
                # Try to compile the annotation to check for syntax errors
                compile(ast.Expression(body=node.annotation), '<annotation>', 'eval')
            except SyntaxError as e:
                issues.append(AnalysisIssue(
                    line=node.lineno,
                    column=node.col_offset,
                    message=f"Invalid type annotation syntax: {e}",
                    severity=IssueSeverity.ERROR,
                    rule_id="T003",
                    category=RuleCategory.TYPES,
                    source="vcs_type_checker",
                    fix_suggestion="Fix the type annotation syntax",
                    auto_fixable=False,
                    metadata={"file_path": str(target.path)}
                ))
        
        return issues
    
    def _check_missing_type_hints(self, tree: ast.AST, target: AnalysisTarget) -> List[AnalysisIssue]:
        """Check for missing type hints in critical locations (limited to avoid spam)."""
        issues = []

        # Limit the number of issues to prevent spam
        max_issues_per_file = 10
        issue_count = 0

        # Check only top-level class attributes (not nested)
        for node in tree.body if hasattr(tree, 'body') else []:
            if isinstance(node, ast.ClassDef) and issue_count < max_issues_per_file:
                # Only check first few attributes per class
                attribute_count = 0
                max_attributes_per_class = 3

                for item in node.body:
                    if isinstance(item, ast.Assign) and attribute_count < max_attributes_per_class:
                        # Class attribute without annotation
                        for target_node in item.targets:
                            if isinstance(target_node, ast.Name) and not target_node.id.startswith('_'):
                                issues.append(AnalysisIssue(
                                    line=item.lineno,
                                    column=item.col_offset,
                                    message=f"Class attribute '{target_node.id}' missing type annotation",
                                    severity=IssueSeverity.INFO,
                                    rule_id="T004",
                                    category=RuleCategory.TYPES,
                                    source="vcs_type_checker",
                                    fix_suggestion=f"Add type annotation: {target_node.id}: AttributeType = ...",
                                    auto_fixable=False,
                                    metadata={"attribute_name": target_node.id, "class_name": node.name, "file_path": str(target.path)}
                                ))
                                attribute_count += 1
                                issue_count += 1

                                if issue_count >= max_issues_per_file:
                                    break

        return issues
    
    def _validate_type_annotations(self, tree: ast.AST, target: AnalysisTarget) -> List[AnalysisIssue]:
        """Validate type annotations for common issues (limited to avoid spam)."""
        issues = []

        # Limit issues to prevent spam
        max_issues = 5
        issue_count = 0

        # Only check top-level functions
        for node in tree.body if hasattr(tree, 'body') else []:
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)) and issue_count < max_issues:
                if node.returns and isinstance(node.returns, ast.Name):
                    # Check for bare 'list' or 'dict' without type parameters
                    annotation_name = node.returns.id if hasattr(node.returns, 'id') else str(node.returns)

                    if annotation_name in ['list', 'dict', 'set', 'tuple']:
                        issues.append(AnalysisIssue(
                            line=node.lineno,
                            column=node.col_offset,
                            message=f"Use generic type annotation: {annotation_name}[T] instead of bare {annotation_name}",
                            severity=IssueSeverity.WARNING,
                            rule_id="T005",
                            category=RuleCategory.TYPES,
                            source="vcs_type_checker",
                            fix_suggestion=f"Use {annotation_name}[ElementType] for better type safety",
                            auto_fixable=False,
                            metadata={"function_name": node.name, "annotation": annotation_name, "file_path": str(target.path)}
                        ))
                        issue_count += 1

        return issues
    
    def calculate_type_coverage(self, tree: ast.AST) -> Dict[str, Any]:
        """Calculate type annotation coverage statistics."""
        stats = {
            "total_functions": 0,
            "annotated_functions": 0,
            "total_parameters": 0,
            "annotated_parameters": 0,
            "total_variables": 0,
            "annotated_variables": 0,
            "coverage_percentage": 0.0
        }
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                stats["total_functions"] += 1
                if node.returns:
                    stats["annotated_functions"] += 1
                
                # Count parameters
                for arg in node.args.args:
                    if arg.arg not in ['self', 'cls']:
                        stats["total_parameters"] += 1
                        if arg.annotation:
                            stats["annotated_parameters"] += 1
            
            elif isinstance(node, ast.AnnAssign):
                stats["total_variables"] += 1
                if node.annotation:
                    stats["annotated_variables"] += 1
        
        # Calculate overall coverage
        total_items = stats["total_functions"] + stats["total_parameters"] + stats["total_variables"]
        annotated_items = stats["annotated_functions"] + stats["annotated_parameters"] + stats["annotated_variables"]
        
        if total_items > 0:
            stats["coverage_percentage"] = (annotated_items / total_items) * 100
        
        return stats


class MypyIntegration:
    """Integration with mypy for external type checking."""
    
    def __init__(self):
        self.mypy_available = self._check_mypy_availability()
    
    def _check_mypy_availability(self) -> bool:
        """Check if mypy is available."""
        try:
            subprocess.run([sys.executable, '-m', 'mypy', '--version'], 
                         capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.warning("mypy not available - external type checking disabled")
            return False
    
    def run_mypy_check(self, file_path: Path) -> List[AnalysisIssue]:
        """Run mypy on a file and convert results to AnalysisIssue objects."""
        if not self.mypy_available:
            return []
        
        issues = []
        
        try:
            # Run mypy with JSON output
            result = subprocess.run([
                sys.executable, '-m', 'mypy',
                '--show-error-codes',
                '--no-error-summary',
                str(file_path)
            ], capture_output=True, text=True)
            
            # Parse mypy output
            for line in result.stdout.strip().split('\n'):
                if line and ':' in line:
                    issue = self._parse_mypy_line(line, file_path)
                    if issue:
                        issues.append(issue)
        
        except Exception as e:
            logger.error(f"Error running mypy: {e}")
        
        return issues
    
    def _parse_mypy_line(self, line: str, file_path: Path) -> Optional[AnalysisIssue]:
        """Parse a mypy output line into an AnalysisIssue."""
        try:
            # Format: file:line:column: severity: message [error-code]
            parts = line.split(':', 3)
            if len(parts) < 4:
                return None
            
            line_num = int(parts[1])
            col_num = int(parts[2]) if parts[2].isdigit() else 0
            rest = parts[3].strip()
            
            # Extract severity and message
            if rest.startswith('error:'):
                severity = IssueSeverity.ERROR
                message = rest[6:].strip()
            elif rest.startswith('warning:'):
                severity = IssueSeverity.WARNING
                message = rest[8:].strip()
            elif rest.startswith('note:'):
                severity = IssueSeverity.INFO
                message = rest[5:].strip()
            else:
                severity = IssueSeverity.WARNING
                message = rest
            
            # Extract error code if present
            error_code = "MYPY"
            if '[' in message and ']' in message:
                code_start = message.rfind('[')
                code_end = message.rfind(']')
                if code_start < code_end:
                    error_code = message[code_start+1:code_end]
                    message = message[:code_start].strip()
            
            return AnalysisIssue(
                line=line_num,
                column=col_num,
                message=message,
                severity=severity,
                rule_id=f"MYPY_{error_code}",
                category=RuleCategory.TYPES,
                source="mypy",
                fix_suggestion=None,
                auto_fixable=False,
                metadata={"error_code": error_code, "file_path": str(file_path)}
            )
        
        except (ValueError, IndexError) as e:
            logger.warning(f"Failed to parse mypy line: {line} - {e}")
            return None
