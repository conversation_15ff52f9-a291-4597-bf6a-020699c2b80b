"""
File Content Cache System
=========================

Optimized file I/O operations with content caching and memory management.
"""

import ast
import logging
import time
import weakref
from pathlib import Path
from typing import Dict, Optional, Any, Tuple, Union, List
from dataclasses import dataclass
from collections import OrderedDict
import hashlib

logger = logging.getLogger(__name__)


@dataclass
class CachedFileContent:
    """Cached file content with metadata."""
    
    content: str
    ast_tree: Optional[ast.AST]
    encoding: str
    file_size: int
    mtime: float
    cache_time: float
    access_count: int = 0
    last_access: float = 0.0
    
    def mark_accessed(self) -> None:
        """Mark content as accessed."""
        self.access_count += 1
        self.last_access = time.time()


class FileContentCache:
    """
    High-performance file content cache with memory management.
    
    This cache provides:
    - Single file read per analysis
    - Shared content across rules
    - AST tree caching
    - Memory-efficient large file handling
    - LRU eviction policy
    """
    
    def __init__(self, max_cache_size: int = 100, max_memory_mb: float = 512.0):
        """
        Initialize the file content cache.
        
        Args:
            max_cache_size: Maximum number of files to cache
            max_memory_mb: Maximum memory usage in MB
        """
        self.max_cache_size = max_cache_size
        self.max_memory_bytes = int(max_memory_mb * 1024 * 1024)
        
        # LRU cache using OrderedDict
        self.cache: OrderedDict[str, CachedFileContent] = OrderedDict()
        
        # Memory tracking
        self.current_memory_bytes = 0
        
        # Statistics
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'memory_pressure_evictions': 0,
            'ast_cache_hits': 0,
            'ast_cache_misses': 0
        }
        
        # Large file threshold (files larger than this use streaming)
        self.large_file_threshold = 10 * 1024 * 1024  # 10MB
        
        logger.debug(f"FileContentCache initialized: max_size={max_cache_size}, max_memory={max_memory_mb}MB")
    
    def get_content(self, file_path: Path, encoding: str = 'utf-8') -> str:
        """
        Get file content with caching.
        
        Args:
            file_path: Path to the file
            encoding: File encoding
            
        Returns:
            File content as string
        """
        cache_key = self._get_cache_key(file_path)
        
        # Check cache first
        if cache_key in self.cache:
            cached = self.cache[cache_key]
            
            # Validate cache (check if file was modified)
            if self._is_cache_valid(file_path, cached):
                # Move to end (LRU)
                self.cache.move_to_end(cache_key)
                cached.mark_accessed()
                self.stats['hits'] += 1
                
                logger.debug(f"Cache hit for {file_path.name}")
                return cached.content
            else:
                # Cache invalid, remove it
                self._remove_from_cache(cache_key)
        
        # Cache miss - read from disk
        self.stats['misses'] += 1
        logger.debug(f"Cache miss for {file_path.name}")
        
        return self._read_and_cache_file(file_path, encoding)
    
    def get_ast(self, file_path: Path, content: Optional[str] = None) -> ast.AST:
        """
        Get parsed AST with caching.
        
        Args:
            file_path: Path to the file
            content: Optional pre-loaded content
            
        Returns:
            Parsed AST tree
        """
        cache_key = self._get_cache_key(file_path)
        
        # Get content if not provided
        if content is None:
            content = self.get_content(file_path)
        
        # Check if we have cached AST
        if cache_key in self.cache:
            cached = self.cache[cache_key]
            if cached.ast_tree is not None:
                cached.mark_accessed()
                self.stats['ast_cache_hits'] += 1
                logger.debug(f"AST cache hit for {file_path.name}")
                return cached.ast_tree
        
        # Parse AST and cache it
        self.stats['ast_cache_misses'] += 1
        logger.debug(f"AST cache miss for {file_path.name}")
        
        try:
            ast_tree = ast.parse(content, filename=str(file_path))
            
            # Update cache with AST
            if cache_key in self.cache:
                self.cache[cache_key].ast_tree = ast_tree
            
            return ast_tree
            
        except SyntaxError as e:
            raise ValueError(f"Syntax error in {file_path}: {e}")
    
    def _read_and_cache_file(self, file_path: Path, encoding: str) -> str:
        """Read file from disk and cache it."""
        try:
            # Check file size
            file_size = file_path.stat().st_size
            
            if file_size > self.large_file_threshold:
                # Large file - use streaming approach
                return self._read_large_file(file_path, encoding)
            
            # Normal file - read and cache
            content = file_path.read_text(encoding=encoding)
            mtime = file_path.stat().st_mtime
            
            # Create cache entry
            cached_content = CachedFileContent(
                content=content,
                ast_tree=None,  # Will be parsed on demand
                encoding=encoding,
                file_size=file_size,
                mtime=mtime,
                cache_time=time.time()
            )
            
            # Add to cache
            cache_key = self._get_cache_key(file_path)
            self._add_to_cache(cache_key, cached_content)
            
            return content
            
        except Exception as e:
            raise ValueError(f"Failed to read file {file_path}: {e}")
    
    def _read_large_file(self, file_path: Path, encoding: str) -> str:
        """
        Read large file with memory-efficient approach.
        
        For very large files, we don't cache the content to avoid memory issues.
        """
        logger.info(f"Reading large file ({file_path.stat().st_size / 1024 / 1024:.1f}MB): {file_path.name}")
        
        try:
            return file_path.read_text(encoding=encoding)
        except Exception as e:
            raise ValueError(f"Failed to read large file {file_path}: {e}")
    
    def _add_to_cache(self, cache_key: str, cached_content: CachedFileContent) -> None:
        """Add content to cache with memory management."""
        # Calculate memory usage
        content_size = len(cached_content.content.encode('utf-8'))
        
        # Check if we need to evict entries
        while (len(self.cache) >= self.max_cache_size or 
               self.current_memory_bytes + content_size > self.max_memory_bytes):
            
            if not self.cache:
                break  # No more entries to evict
            
            # Evict least recently used entry
            oldest_key, oldest_content = self.cache.popitem(last=False)
            evicted_size = len(oldest_content.content.encode('utf-8'))
            self.current_memory_bytes -= evicted_size
            
            self.stats['evictions'] += 1
            if self.current_memory_bytes + content_size > self.max_memory_bytes:
                self.stats['memory_pressure_evictions'] += 1
            
            logger.debug(f"Evicted cache entry: {oldest_key}")
        
        # Add new entry
        self.cache[cache_key] = cached_content
        self.current_memory_bytes += content_size
        
        logger.debug(f"Cached file: {cache_key} ({content_size} bytes)")
    
    def _remove_from_cache(self, cache_key: str) -> None:
        """Remove entry from cache."""
        if cache_key in self.cache:
            cached_content = self.cache.pop(cache_key)
            content_size = len(cached_content.content.encode('utf-8'))
            self.current_memory_bytes -= content_size
            logger.debug(f"Removed from cache: {cache_key}")
    
    def _get_cache_key(self, file_path: Path) -> str:
        """Generate cache key for file path."""
        # Use absolute path for consistent keys
        abs_path = str(file_path.resolve())
        return hashlib.md5(abs_path.encode('utf-8')).hexdigest()
    
    def _is_cache_valid(self, file_path: Path, cached: CachedFileContent) -> bool:
        """Check if cached content is still valid."""
        try:
            current_mtime = file_path.stat().st_mtime
            return current_mtime == cached.mtime
        except Exception:
            return False
    
    def clear(self) -> None:
        """Clear all cached content."""
        self.cache.clear()
        self.current_memory_bytes = 0
        logger.debug("File content cache cleared")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        total_requests = self.stats['hits'] + self.stats['misses']
        hit_rate = (self.stats['hits'] / total_requests * 100) if total_requests > 0 else 0
        
        ast_total = self.stats['ast_cache_hits'] + self.stats['ast_cache_misses']
        ast_hit_rate = (self.stats['ast_cache_hits'] / ast_total * 100) if ast_total > 0 else 0
        
        return {
            'cache_size': len(self.cache),
            'max_cache_size': self.max_cache_size,
            'memory_usage_mb': self.current_memory_bytes / 1024 / 1024,
            'max_memory_mb': self.max_memory_bytes / 1024 / 1024,
            'hit_rate_percent': hit_rate,
            'ast_hit_rate_percent': ast_hit_rate,
            'total_requests': total_requests,
            'evictions': self.stats['evictions'],
            'memory_pressure_evictions': self.stats['memory_pressure_evictions'],
            **self.stats
        }
    
    def get_cache_info(self) -> List[Dict[str, Any]]:
        """Get detailed cache information."""
        info = []
        for cache_key, cached in self.cache.items():
            content_size = len(cached.content.encode('utf-8'))
            info.append({
                'cache_key': cache_key,
                'file_size': cached.file_size,
                'memory_size': content_size,
                'access_count': cached.access_count,
                'last_access': cached.last_access,
                'cache_time': cached.cache_time,
                'has_ast': cached.ast_tree is not None
            })
        return info


# Global file content cache instance
_file_cache: Optional[FileContentCache] = None


def get_file_cache() -> FileContentCache:
    """Get the global file content cache instance."""
    global _file_cache
    if _file_cache is None:
        _file_cache = FileContentCache()
    return _file_cache


def initialize_file_cache(max_cache_size: int = 100, max_memory_mb: float = 512.0) -> None:
    """Initialize the global file content cache."""
    global _file_cache
    _file_cache = FileContentCache(max_cache_size, max_memory_mb)
    logger.info(f"File content cache initialized: {max_cache_size} files, {max_memory_mb}MB")


def clear_file_cache() -> None:
    """Clear the global file content cache."""
    global _file_cache
    if _file_cache:
        _file_cache.clear()
