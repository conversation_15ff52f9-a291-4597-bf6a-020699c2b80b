"""
File: vibe_check/core/vcs/integration/integration_manager.py
Purpose: Integration manager for coordinating all VCS integration features
Related Files: vibe_check/core/vcs/engine.py
Dependencies: asyncio, pathlib, typing
"""

import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple

from .unified_reporter import UnifiedReporter, UnifiedReport
from vibe_check.core.vcs.models import AnalysisResult, AnalysisContext
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class IntegrationManager:
    """Manages all VCS integration features including meta-analysis and unified reporting."""
    
    def __init__(self, output_dir: Optional[Path] = None):
        self.meta_analyzer = MetaAnalyzer()
        self.unified_reporter = UnifiedReporter(output_dir)
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize the integration manager."""
        if self._initialized:
            return
        
        self._initialized = True
        logger.info("Integration manager initialized")
    
    async def perform_comprehensive_analysis(
        self,
        engine,
        project_root: Path,
        context: Optional[AnalysisContext] = None,
        include_plugins: bool = True,
        export_formats: Optional[List[str]] = None
    ) -> <PERSON>ple[UnifiedReport, Dict[str, Any]]:
        """
        Perform comprehensive analysis with meta-analysis and unified reporting.
        
        Args:
            engine: VCS engine instance
            project_root: Project root directory
            context: Analysis context
            include_plugins: Whether to include plugin analysis
            export_formats: List of formats to export (default: ["json", "html"])
            
        Returns:
            Tuple of (unified_report, performance_stats)
        """
        if not self._initialized:
            await self.initialize()
        
        logger.info(f"Starting comprehensive analysis of {project_root}")
        start_time = asyncio.get_event_loop().time()
        
        # Perform main analysis
        if hasattr(engine, 'analyze_with_memory_management'):
            # Use memory-managed analysis for large projects
            main_results, perf_stats = await engine.analyze_with_memory_management(
                project_root, context, use_streaming=True
            )
        else:
            # Fallback to regular analysis
            python_files = list(project_root.rglob("*.py"))
            filtered_files = [f for f in python_files if not any(
                part in str(f) for part in ['__pycache__', '.git', '.venv', 'venv']
            )]
            
            main_results = []
            for file_path in filtered_files:
                from vibe_check.core.vcs.models import AnalysisTarget
                target = AnalysisTarget.from_file(file_path)
                result = await engine.analyze(target, context)
                main_results.append(result)
            
            perf_stats = {"files_analyzed": len(main_results)}
        
        # Perform plugin analysis if requested
        plugin_results = []
        if include_plugins and hasattr(engine, 'plugin_manager'):
            logger.info("Running plugin analysis")
            for result in main_results:
                plugin_analysis = await engine.plugin_manager.run_analyzer_plugins(
                    result.target, context or AnalysisContext.create_default(engine.mode)
                )
                plugin_results.extend(plugin_analysis)
        
        # Perform meta-analysis
        logger.info("Performing meta-analysis")
        meta_analysis = await self.meta_analyzer.analyze_project_meta(
            main_results, project_root
        )
        
        # Create unified report
        logger.info("Creating unified report")
        unified_report = await self.unified_reporter.create_unified_report(
            analysis_results=main_results,
            meta_analysis=meta_analysis,
            plugin_results=plugin_results,
            performance_metrics=perf_stats,
            project_root=project_root
        )
        
        # Export reports
        export_formats = export_formats or ["json", "html"]
        exported_files = []
        
        for format_type in export_formats:
            try:
                exported_file = await self.unified_reporter.export_report(
                    unified_report, format_type
                )
                exported_files.append(str(exported_file))
                logger.info(f"Exported {format_type.upper()} report to {exported_file}")
            except Exception as e:
                logger.error(f"Failed to export {format_type} report: {e}")
        
        # Calculate final performance stats
        total_time = asyncio.get_event_loop().time() - start_time
        final_stats = {
            **perf_stats,
            "total_analysis_time": total_time,
            "main_results_count": len(main_results),
            "plugin_results_count": len(plugin_results),
            "meta_analysis_quality_score": meta_analysis.quality_score,
            "exported_files": exported_files
        }
        
        logger.info(f"Comprehensive analysis complete in {total_time:.2f}s")
        return unified_report, final_stats
    
    async def analyze_project_trends(self, project_root: Path) -> Dict[str, Any]:
        """Analyze project trends from historical data."""
        historical_summary = self.meta_analyzer.get_historical_summary()
        
        # Get report statistics
        report_stats = self.unified_reporter.get_report_statistics()
        
        return {
            "historical_analysis": historical_summary,
            "report_statistics": report_stats,
            "project_root": str(project_root)
        }
    
    async def generate_comparison_report(
        self,
        current_results: List[AnalysisResult],
        previous_results: Optional[List[AnalysisResult]] = None,
        project_root: Optional[Path] = None
    ) -> Dict[str, Any]:
        """Generate a comparison report between current and previous analysis."""
        if not previous_results:
            return {"status": "no_previous_results_for_comparison"}
        
        # Current analysis summary
        current_issues = sum(len(r.issues) for r in current_results)
        current_files = len(current_results)
        
        # Previous analysis summary
        previous_issues = sum(len(r.issues) for r in previous_results)
        previous_files = len(previous_results)
        
        # Calculate changes
        issue_change = current_issues - previous_issues
        file_change = current_files - previous_files
        
        # Issue category comparison
        from collections import Counter
        from vibe_check.core.vcs.models import RuleCategory
        
        current_categories = Counter()
        previous_categories = Counter()
        
        for result in current_results:
            for issue in result.issues:
                current_categories[issue.category] += 1
        
        for result in previous_results:
            for issue in result.issues:
                previous_categories[issue.category] += 1
        
        category_changes = {}
        for category in RuleCategory:
            current_count = current_categories.get(category, 0)
            previous_count = previous_categories.get(category, 0)
            category_changes[category.value] = {
                "current": current_count,
                "previous": previous_count,
                "change": current_count - previous_count
            }
        
        # Generate trend assessment
        if issue_change < 0:
            trend = "improving"
        elif issue_change > 0:
            trend = "declining"
        else:
            trend = "stable"
        
        comparison = {
            "summary": {
                "current_files": current_files,
                "previous_files": previous_files,
                "file_change": file_change,
                "current_issues": current_issues,
                "previous_issues": previous_issues,
                "issue_change": issue_change,
                "trend": trend
            },
            "category_comparison": category_changes,
            "recommendations": self._generate_comparison_recommendations(
                issue_change, category_changes, trend
            )
        }
        
        return comparison
    
    def _generate_comparison_recommendations(
        self,
        issue_change: int,
        category_changes: Dict[str, Dict[str, int]],
        trend: str
    ) -> List[str]:
        """Generate recommendations based on comparison analysis."""
        recommendations = []
        
        if trend == "improving":
            recommendations.append("✅ Great progress! Issue count has decreased.")
        elif trend == "declining":
            recommendations.append("⚠️ Code quality declining - increased issues detected.")
        else:
            recommendations.append("📊 Stable code quality - issue count unchanged.")
        
        # Category-specific recommendations
        for category, changes in category_changes.items():
            change = changes["change"]
            if change > 5:
                recommendations.append(f"📈 {category} issues increased by {change}")
            elif change < -5:
                recommendations.append(f"📉 {category} issues decreased by {abs(change)}")
        
        return recommendations
    
    async def export_integration_summary(self, format_type: str = "json") -> Path:
        """Export integration system summary."""
        summary = {
            "meta_analyzer": {
                "historical_analyses": len(self.meta_analyzer.historical_results),
                "latest_quality_scores": [
                    r.quality_score for r in self.meta_analyzer.historical_results[-5:]
                ]
            },
            "unified_reporter": self.unified_reporter.get_report_statistics(),
            "integration_features": [
                "meta_analysis",
                "unified_reporting", 
                "trend_analysis",
                "comparison_reports",
                "multi_format_export"
            ]
        }
        
        timestamp = asyncio.get_event_loop().time()
        filename = f"integration_summary_{int(timestamp)}.{format_type}"
        filepath = self.unified_reporter.output_dir / filename
        
        if format_type == "json":
            import json
            with open(filepath, 'w') as f:
                json.dump(summary, f, indent=2)
        else:
            # Fallback to JSON for unsupported formats
            import json
            with open(filepath, 'w') as f:
                json.dump(summary, f, indent=2)
        
        logger.info(f"Exported integration summary to {filepath}")
        return filepath
    
    def get_integration_statistics(self) -> Dict[str, Any]:
        """Get comprehensive integration system statistics."""
        return {
            "meta_analyzer": {
                "historical_analyses": len(self.meta_analyzer.historical_results),
                "average_quality_score": (
                    sum(r.quality_score for r in self.meta_analyzer.historical_results) / 
                    len(self.meta_analyzer.historical_results)
                ) if self.meta_analyzer.historical_results else 0
            },
            "unified_reporter": self.unified_reporter.get_report_statistics(),
            "initialized": self._initialized,
            "features_available": [
                "comprehensive_analysis",
                "trend_analysis", 
                "comparison_reports",
                "multi_format_export",
                "meta_analysis"
            ]
        }
    
    async def cleanup(self) -> None:
        """Cleanup integration manager resources."""
        # No specific cleanup needed for current implementation
        logger.info("Integration manager cleaned up")
