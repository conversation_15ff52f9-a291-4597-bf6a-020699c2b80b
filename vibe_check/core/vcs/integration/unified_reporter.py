"""
File: vibe_check/core/vcs/integration/unified_reporter.py
Purpose: Unified reporting system for all analysis types
Related Files: vibe_check/core/vcs/integration/meta_analyzer.py
Dependencies: typing, pathlib, datetime
"""

import json
from datetime import datetime
from pathlib import Path
from dataclasses import dataclass
from typing import Dict, List, Any, Optional

from vibe_check.core.vcs.models import AnalysisResult, RuleCategory, IssueSeverity
from vibe_check.core.vcs.integration.meta_analyzer import MetaAnalysisResult
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


@dataclass
class UnifiedReport:
    """Unified report containing all analysis information."""
    timestamp: datetime
    project_root: str
    analysis_type: str
    summary: Dict[str, Any]
    detailed_results: List[Dict[str, Any]]
    meta_analysis: Optional[Dict[str, Any]]
    plugin_results: List[Dict[str, Any]]
    performance_metrics: Dict[str, Any]
    recommendations: List[str]
    export_formats: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "timestamp": self.timestamp.isoformat(),
            "project_root": self.project_root,
            "analysis_type": self.analysis_type,
            "summary": self.summary,
            "detailed_results": self.detailed_results,
            "meta_analysis": self.meta_analysis,
            "plugin_results": self.plugin_results,
            "performance_metrics": self.performance_metrics,
            "recommendations": self.recommendations,
            "export_formats": self.export_formats
        }


class UnifiedReporter:
    """Creates unified reports across all analysis types and formats."""
    
    def __init__(self, output_dir: Optional[Path] = None):
        self.output_dir = output_dir or Path.cwd() / "vibe_check_reports"
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    async def create_unified_report(
        self,
        analysis_results: List[AnalysisResult],
        meta_analysis: Optional[MetaAnalysisResult] = None,
        plugin_results: Optional[List[AnalysisResult]] = None,
        performance_metrics: Optional[Dict[str, Any]] = None,
        project_root: Optional[Path] = None
    ) -> UnifiedReport:
        """
        Create a unified report from all analysis data.
        
        Args:
            analysis_results: Main analysis results
            meta_analysis: Meta-analysis results
            plugin_results: Results from plugin analyzers
            performance_metrics: Performance and timing data
            project_root: Project root directory
            
        Returns:
            Unified report object
        """
        timestamp = datetime.now()
        project_root_str = str(project_root) if project_root else "unknown"
        
        # Generate summary
        summary = self._generate_summary(analysis_results, meta_analysis)
        
        # Convert detailed results
        detailed_results = [self._convert_analysis_result(result) for result in analysis_results]
        
        # Convert meta-analysis
        meta_dict = meta_analysis.to_dict() if meta_analysis else None
        
        # Convert plugin results
        plugin_dict = []
        if plugin_results:
            plugin_dict = [self._convert_analysis_result(result) for result in plugin_results]
        
        # Performance metrics
        perf_metrics = performance_metrics or {}
        
        # Generate recommendations
        recommendations = self._generate_unified_recommendations(
            analysis_results, meta_analysis, plugin_results
        )
        
        # Available export formats
        export_formats = ["json", "html", "markdown", "csv", "xml"]
        
        report = UnifiedReport(
            timestamp=timestamp,
            project_root=project_root_str,
            analysis_type="unified",
            summary=summary,
            detailed_results=detailed_results,
            meta_analysis=meta_dict,
            plugin_results=plugin_dict,
            performance_metrics=perf_metrics,
            recommendations=recommendations,
            export_formats=export_formats
        )
        
        logger.info(f"Created unified report with {len(analysis_results)} files analyzed")
        return report
    
    def _generate_summary(self, results: List[AnalysisResult], 
                         meta_analysis: Optional[MetaAnalysisResult]) -> Dict[str, Any]:
        """Generate summary statistics."""
        total_files = len(results)
        successful_analyses = sum(1 for r in results if r.success)
        failed_analyses = total_files - successful_analyses
        
        all_issues = []
        for result in results:
            all_issues.extend(result.issues)
        
        total_issues = len(all_issues)
        
        # Issue breakdown by category
        category_breakdown = {}
        for category in RuleCategory:
            category_breakdown[category.value] = sum(
                1 for issue in all_issues if issue.category == category
            )
        
        # Severity breakdown
        severity_breakdown = {}
        for severity in IssueSeverity:
            severity_breakdown[severity.value] = sum(
                1 for issue in all_issues if issue.severity == severity
            )
        
        summary = {
            "total_files": total_files,
            "successful_analyses": successful_analyses,
            "failed_analyses": failed_analyses,
            "total_issues": total_issues,
            "issues_per_file": total_issues / total_files if total_files > 0 else 0,
            "category_breakdown": category_breakdown,
            "severity_breakdown": severity_breakdown,
            "quality_score": meta_analysis.quality_score if meta_analysis else None
        }
        
        return summary
    
    def _convert_analysis_result(self, result: AnalysisResult) -> Dict[str, Any]:
        """Convert AnalysisResult to dictionary."""
        return {
            "file_path": str(result.target.path) if result.target.path else "unknown",
            "success": result.success,
            "execution_time": result.execution_time,
            "rules_executed": result.rules_executed,
            "issues": [
                {
                    "line": issue.line,
                    "column": issue.column,
                    "message": issue.message,
                    "severity": issue.severity.value,
                    "category": issue.category.value,
                    "rule_id": issue.rule_id,
                    "source": issue.source,
                    "fix_suggestion": issue.fix_suggestion
                }
                for issue in result.issues
            ],
            "error_message": result.error_message
        }
    
    def _generate_unified_recommendations(
        self,
        analysis_results: List[AnalysisResult],
        meta_analysis: Optional[MetaAnalysisResult],
        plugin_results: Optional[List[AnalysisResult]]
    ) -> List[str]:
        """Generate unified recommendations from all analysis sources."""
        recommendations = []
        
        # Add meta-analysis recommendations
        if meta_analysis and meta_analysis.recommendations:
            recommendations.extend(meta_analysis.recommendations)
        
        # Add general recommendations based on results
        total_issues = sum(len(r.issues) for r in analysis_results)
        
        if total_issues == 0:
            recommendations.append("🎉 Excellent! No issues found in the analyzed files.")
        elif total_issues < 10:
            recommendations.append("✅ Good code quality with minimal issues to address.")
        elif total_issues < 50:
            recommendations.append("⚠️ Moderate number of issues - consider addressing high-priority ones first.")
        else:
            recommendations.append("🚨 High number of issues detected - systematic review recommended.")
        
        # Plugin-specific recommendations
        if plugin_results:
            plugin_issues = sum(len(r.issues) for r in plugin_results)
            if plugin_issues > 0:
                recommendations.append(f"🔌 Plugin analysis found {plugin_issues} additional issues.")
        
        return recommendations
    
    async def export_report(self, report: UnifiedReport, format_type: str = "json") -> Path:
        """
        Export unified report to specified format.
        
        Args:
            report: Unified report to export
            format_type: Export format (json, html, markdown, csv, xml)
            
        Returns:
            Path to exported file
        """
        timestamp_str = report.timestamp.strftime("%Y%m%d_%H%M%S")
        
        if format_type == "json":
            return await self._export_json(report, timestamp_str)
        elif format_type == "html":
            return await self._export_html(report, timestamp_str)
        elif format_type == "markdown":
            return await self._export_markdown(report, timestamp_str)
        elif format_type == "csv":
            return await self._export_csv(report, timestamp_str)
        elif format_type == "xml":
            return await self._export_xml(report, timestamp_str)
        else:
            raise ValueError(f"Unsupported export format: {format_type}")
    
    async def _export_json(self, report: UnifiedReport, timestamp: str) -> Path:
        """Export report as JSON."""
        filename = f"vibe_check_report_{timestamp}.json"
        filepath = self.output_dir / filename
        
        with open(filepath, 'w') as f:
            json.dump(report.to_dict(), f, indent=2, default=str)
        
        logger.info(f"Exported JSON report to {filepath}")
        return filepath
    
    async def _export_html(self, report: UnifiedReport, timestamp: str) -> Path:
        """Export report as HTML."""
        filename = f"vibe_check_report_{timestamp}.html"
        filepath = self.output_dir / filename
        
        html_content = self._generate_html_report(report)
        
        with open(filepath, 'w') as f:
            f.write(html_content)
        
        logger.info(f"Exported HTML report to {filepath}")
        return filepath
    
    async def _export_markdown(self, report: UnifiedReport, timestamp: str) -> Path:
        """Export report as Markdown."""
        filename = f"vibe_check_report_{timestamp}.md"
        filepath = self.output_dir / filename
        
        md_content = self._generate_markdown_report(report)
        
        with open(filepath, 'w') as f:
            f.write(md_content)
        
        logger.info(f"Exported Markdown report to {filepath}")
        return filepath
    
    async def _export_csv(self, report: UnifiedReport, timestamp: str) -> Path:
        """Export report as CSV."""
        filename = f"vibe_check_report_{timestamp}.csv"
        filepath = self.output_dir / filename
        
        csv_content = self._generate_csv_report(report)
        
        with open(filepath, 'w') as f:
            f.write(csv_content)
        
        logger.info(f"Exported CSV report to {filepath}")
        return filepath
    
    async def _export_xml(self, report: UnifiedReport, timestamp: str) -> Path:
        """Export report as XML."""
        filename = f"vibe_check_report_{timestamp}.xml"
        filepath = self.output_dir / filename
        
        xml_content = self._generate_xml_report(report)
        
        with open(filepath, 'w') as f:
            f.write(xml_content)
        
        logger.info(f"Exported XML report to {filepath}")
        return filepath
    
    def _generate_html_report(self, report: UnifiedReport) -> str:
        """Generate HTML report content."""
        html = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Vibe Check Analysis Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .summary {{ margin: 20px 0; }}
        .recommendations {{ background-color: #fff3cd; padding: 15px; border-radius: 5px; }}
        .issue {{ margin: 10px 0; padding: 10px; border-left: 3px solid #007bff; }}
        .error {{ border-left-color: #dc3545; }}
        .warning {{ border-left-color: #ffc107; }}
        .info {{ border-left-color: #17a2b8; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Vibe Check Analysis Report</h1>
        <p><strong>Generated:</strong> {report.timestamp}</p>
        <p><strong>Project:</strong> {report.project_root}</p>
    </div>
    
    <div class="summary">
        <h2>Summary</h2>
        <p><strong>Files Analyzed:</strong> {report.summary['total_files']}</p>
        <p><strong>Total Issues:</strong> {report.summary['total_issues']}</p>
        <p><strong>Quality Score:</strong> {report.summary.get('quality_score', 'N/A')}</p>
    </div>
    
    <div class="recommendations">
        <h2>Recommendations</h2>
        <ul>
        {''.join(f'<li>{rec}</li>' for rec in report.recommendations)}
        </ul>
    </div>
</body>
</html>
        """
        return html
    
    def _generate_markdown_report(self, report: UnifiedReport) -> str:
        """Generate Markdown report content."""
        md = f"""# Vibe Check Analysis Report

**Generated:** {report.timestamp}  
**Project:** {report.project_root}

## Summary

- **Files Analyzed:** {report.summary['total_files']}
- **Total Issues:** {report.summary['total_issues']}
- **Quality Score:** {report.summary.get('quality_score', 'N/A')}

## Recommendations

{chr(10).join(f'- {rec}' for rec in report.recommendations)}

## Issue Breakdown

### By Category
{chr(10).join(f'- **{cat}:** {count}' for cat, count in report.summary['category_breakdown'].items())}

### By Severity
{chr(10).join(f'- **{sev}:** {count}' for sev, count in report.summary['severity_breakdown'].items())}
"""
        return md
    
    def _generate_csv_report(self, report: UnifiedReport) -> str:
        """Generate CSV report content."""
        lines = ["File,Line,Column,Severity,Category,Rule,Message"]
        
        for result in report.detailed_results:
            file_path = result['file_path']
            for issue in result['issues']:
                line = f'"{file_path}",{issue["line"]},{issue["column"]},"{issue["severity"]}","{issue["category"]}","{issue["rule_id"]}","{issue["message"]}"'
                lines.append(line)
        
        return '\n'.join(lines)
    
    def _generate_xml_report(self, report: UnifiedReport) -> str:
        """Generate XML report content."""
        xml = f"""<?xml version="1.0" encoding="UTF-8"?>
<vibe_check_report>
    <metadata>
        <timestamp>{report.timestamp}</timestamp>
        <project_root>{report.project_root}</project_root>
        <total_files>{report.summary['total_files']}</total_files>
        <total_issues>{report.summary['total_issues']}</total_issues>
    </metadata>
    <recommendations>
        {''.join(f'<recommendation>{rec}</recommendation>' for rec in report.recommendations)}
    </recommendations>
</vibe_check_report>"""
        return xml

    def get_report_statistics(self) -> Dict[str, Any]:
        """Get statistics about generated reports."""
        if not self.output_dir.exists():
            return {"reports_generated": 0}

        report_files = list(self.output_dir.glob("vibe_check_report_*"))

        return {
            "reports_generated": len(report_files),
            "output_directory": str(self.output_dir),
            "latest_reports": [f.name for f in sorted(report_files, key=lambda x: x.stat().st_mtime, reverse=True)[:5]]
        }
