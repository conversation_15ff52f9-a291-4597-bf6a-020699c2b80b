"""
File: vibe_check/core/vcs/integration/meta_analyzer.py
Purpose: Meta-analysis capabilities for cross-cutting analysis
Related Files: vibe_check/core/vcs/engine.py
Dependencies: typing, pathlib, asyncio
"""

from pathlib import Path
from dataclasses import dataclass
from typing import Dict, <PERSON>, Tuple, Any
from collections import Counter

from vibe_check.core.vcs.models import (
    AnalysisResult, AnalysisIssue, RuleCategory, IssueSeverity
)
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


@dataclass
class MetaAnalysisResult:
    """Result of meta-analysis across multiple files/results."""
    project_root: Path
    total_files: int
    total_issues: int
    issue_distribution: Dict[RuleCategory, int]
    severity_distribution: Dict[IssueSeverity, int]
    hotspot_files: List[Tuple[str, int]]  # (file_path, issue_count)
    rule_effectiveness: Dict[str, int]  # rule_id -> issue_count
    complexity_metrics: Dict[str, Any]
    quality_score: float
    recommendations: List[str]
    trends: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "project_root": str(self.project_root),
            "total_files": self.total_files,
            "total_issues": self.total_issues,
            "issue_distribution": {cat.value: count for cat, count in self.issue_distribution.items()},
            "severity_distribution": {sev.value: count for sev, count in self.severity_distribution.items()},
            "hotspot_files": self.hotspot_files,
            "rule_effectiveness": self.rule_effectiveness,
            "complexity_metrics": self.complexity_metrics,
            "quality_score": self.quality_score,
            "recommendations": self.recommendations,
            "trends": self.trends
        }


class MetaAnalyzer:
    """Performs meta-analysis across analysis results to identify patterns and trends."""
    
    def __init__(self) -> None:
        self.historical_results: List[MetaAnalysisResult] = []
    
    async def analyze_project_meta(self, results: List[AnalysisResult], 
                                 project_root: Path) -> MetaAnalysisResult:
        """
        Perform meta-analysis on project analysis results.
        
        Args:
            results: List of analysis results from all files
            project_root: Root directory of the project
            
        Returns:
            Meta-analysis result with insights and recommendations
        """
        logger.info(f"Performing meta-analysis on {len(results)} files")
        
        # Basic statistics
        total_files = len(results)
        all_issues = []
        for result in results:
            all_issues.extend(result.issues)
        
        total_issues = len(all_issues)
        
        # Issue distribution analysis
        issue_distribution = self._analyze_issue_distribution(all_issues)
        severity_distribution = self._analyze_severity_distribution(all_issues)
        
        # Hotspot analysis
        hotspot_files = self._identify_hotspot_files(results)
        
        # Rule effectiveness analysis
        rule_effectiveness = self._analyze_rule_effectiveness(all_issues)
        
        # Complexity metrics
        complexity_metrics = await self._calculate_complexity_metrics(results)
        
        # Quality score calculation
        quality_score = self._calculate_quality_score(
            total_files, total_issues, severity_distribution, complexity_metrics
        )
        
        # Generate recommendations
        recommendations = self._generate_recommendations(
            issue_distribution, hotspot_files, rule_effectiveness, quality_score
        )
        
        # Trend analysis (if historical data available)
        trends = self._analyze_trends(total_files, total_issues, quality_score)
        
        meta_result = MetaAnalysisResult(
            project_root=project_root,
            total_files=total_files,
            total_issues=total_issues,
            issue_distribution=issue_distribution,
            severity_distribution=severity_distribution,
            hotspot_files=hotspot_files,
            rule_effectiveness=rule_effectiveness,
            complexity_metrics=complexity_metrics,
            quality_score=quality_score,
            recommendations=recommendations,
            trends=trends
        )
        
        # Store for trend analysis
        self.historical_results.append(meta_result)
        
        logger.info(f"Meta-analysis complete: Quality Score {quality_score:.2f}")
        return meta_result
    
    def _analyze_issue_distribution(self, issues: List[AnalysisIssue]) -> Dict[RuleCategory, int]:
        """Analyze distribution of issues by category."""
        distribution = Counter()
        
        for issue in issues:
            distribution[issue.category] += 1
        
        return dict(distribution)
    
    def _analyze_severity_distribution(self, issues: List[AnalysisIssue]) -> Dict[IssueSeverity, int]:
        """Analyze distribution of issues by severity."""
        distribution = Counter()
        
        for issue in issues:
            distribution[issue.severity] += 1
        
        return dict(distribution)
    
    def _identify_hotspot_files(self, results: List[AnalysisResult]) -> List[Tuple[str, int]]:
        """Identify files with the most issues (hotspots)."""
        file_issues = []
        
        for result in results:
            file_path = str(result.target.path) if result.target.path else "unknown"
            issue_count = len(result.issues)
            file_issues.append((file_path, issue_count))
        
        # Sort by issue count (descending) and return top 10
        file_issues.sort(key=lambda x: x[1], reverse=True)
        return file_issues[:10]
    
    def _analyze_rule_effectiveness(self, issues: List[AnalysisIssue]) -> Dict[str, int]:
        """Analyze which rules are finding the most issues."""
        rule_counts = Counter()
        
        for issue in issues:
            rule_counts[issue.rule_id] += 1
        
        return dict(rule_counts)
    
    async def _calculate_complexity_metrics(self, results: List[AnalysisResult]) -> Dict[str, Any]:
        """Calculate project complexity metrics."""
        total_lines = 0
        total_functions = 0
        total_classes = 0
        cyclomatic_complexity = 0
        
        # These would be calculated from AST analysis in a real implementation
        # For now, we'll estimate based on issue patterns
        
        complexity_issues = 0
        for result in results:
            for issue in result.issues:
                if issue.category == RuleCategory.COMPLEXITY:
                    complexity_issues += 1
        
        # Estimate metrics (in a real implementation, these would come from AST analysis)
        estimated_lines_per_file = 100  # Average estimate
        total_lines = len(results) * estimated_lines_per_file
        
        return {
            "estimated_total_lines": total_lines,
            "complexity_issues": complexity_issues,
            "complexity_density": complexity_issues / len(results) if results else 0,
            "average_issues_per_file": sum(len(r.issues) for r in results) / len(results) if results else 0
        }
    
    def _calculate_quality_score(self, total_files: int, total_issues: int,
                               severity_distribution: Dict[IssueSeverity, int],
                               complexity_metrics: Dict[str, Any]) -> float:
        """Calculate overall project quality score (0-100)."""
        if total_files == 0:
            return 100.0
        
        # Base score
        base_score = 100.0
        
        # Penalty for issues per file
        issues_per_file = total_issues / total_files
        issue_penalty = min(issues_per_file * 2, 30)  # Max 30 point penalty
        
        # Severity-based penalties
        error_penalty = severity_distribution.get(IssueSeverity.ERROR, 0) * 5
        warning_penalty = severity_distribution.get(IssueSeverity.WARNING, 0) * 2
        info_penalty = severity_distribution.get(IssueSeverity.INFO, 0) * 0.5
        
        # Complexity penalty
        complexity_penalty = complexity_metrics.get("complexity_density", 0) * 10
        
        # Calculate final score
        total_penalty = issue_penalty + error_penalty + warning_penalty + info_penalty + complexity_penalty
        final_score = max(0, base_score - total_penalty)
        
        return round(final_score, 2)
    
    def _generate_recommendations(self, issue_distribution: Dict[RuleCategory, int],
                                hotspot_files: List[Tuple[str, int]],
                                rule_effectiveness: Dict[str, int],
                                quality_score: float) -> List[str]:
        """Generate actionable recommendations based on analysis."""
        recommendations = []
        
        # Quality-based recommendations
        if quality_score < 50:
            recommendations.append("🚨 Critical: Project quality is very low. Immediate attention required.")
        elif quality_score < 70:
            recommendations.append("⚠️ Warning: Project quality needs improvement.")
        elif quality_score > 90:
            recommendations.append("✅ Excellent: Project maintains high quality standards.")
        
        # Category-specific recommendations
        if issue_distribution.get(RuleCategory.SECURITY, 0) > 0:
            recommendations.append("🔒 Security: Address security issues immediately.")
        
        if issue_distribution.get(RuleCategory.COMPLEXITY, 0) > 10:
            recommendations.append("🔄 Complexity: Consider refactoring complex code sections.")
        
        if issue_distribution.get(RuleCategory.STYLE, 0) > 20:
            recommendations.append("🎨 Style: Implement consistent code formatting.")
        
        # Hotspot recommendations
        if hotspot_files and hotspot_files[0][1] > 10:
            recommendations.append(f"🔥 Hotspot: Focus on {hotspot_files[0][0]} ({hotspot_files[0][1]} issues)")
        
        # Rule effectiveness recommendations
        if rule_effectiveness:
            top_rule = max(rule_effectiveness.items(), key=lambda x: x[1])
            if top_rule[1] > 5:
                recommendations.append(f"📊 Pattern: Rule {top_rule[0]} found {top_rule[1]} issues - review pattern")
        
        return recommendations
    
    def _analyze_trends(self, total_files: int, total_issues: int, quality_score: float) -> Dict[str, Any]:
        """Analyze trends compared to historical data."""
        trends = {
            "files_trend": "stable",
            "issues_trend": "stable", 
            "quality_trend": "stable",
            "historical_data_points": len(self.historical_results)
        }
        
        if len(self.historical_results) > 1:
            previous = self.historical_results[-2]
            
            # File count trend
            if total_files > previous.total_files * 1.1:
                trends["files_trend"] = "increasing"
            elif total_files < previous.total_files * 0.9:
                trends["files_trend"] = "decreasing"
            
            # Issues trend
            if total_issues > previous.total_issues * 1.1:
                trends["issues_trend"] = "increasing"
            elif total_issues < previous.total_issues * 0.9:
                trends["issues_trend"] = "decreasing"
            
            # Quality trend
            if quality_score > previous.quality_score + 5:
                trends["quality_trend"] = "improving"
            elif quality_score < previous.quality_score - 5:
                trends["quality_trend"] = "declining"
        
        return trends
    
    def get_historical_summary(self) -> Dict[str, Any]:
        """Get summary of historical analysis data."""
        if not self.historical_results:
            return {"status": "no_historical_data"}
        
        quality_scores = [result.quality_score for result in self.historical_results]
        issue_counts = [result.total_issues for result in self.historical_results]
        
        return {
            "analysis_count": len(self.historical_results),
            "average_quality_score": sum(quality_scores) / len(quality_scores),
            "quality_trend": "improving" if quality_scores[-1] > quality_scores[0] else "declining",
            "average_issues": sum(issue_counts) / len(issue_counts),
            "latest_quality_score": quality_scores[-1],
            "best_quality_score": max(quality_scores),
            "worst_quality_score": min(quality_scores)
        }
