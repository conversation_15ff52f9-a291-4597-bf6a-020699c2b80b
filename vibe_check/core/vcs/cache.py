"""
VCS Cache Management
====================

This module provides multi-level caching for VCS analysis results
to optimize performance for repeated analysis operations.
"""

import hashlib
import json
import pickle
import time
from pathlib import Path
from typing import Any, Dict, Optional
import logging

from .models import AnalysisTarget, AnalysisResult
from .dependency_tracker import DependencyTracker

logger = logging.getLogger(__name__)


class LRUCache:
    """Simple LRU cache implementation for in-memory caching."""
    
    def __init__(self, maxsize: int = 1000):
        """Initialize LRU cache with maximum size."""
        self.maxsize = maxsize
        self.cache: Dict[str, Any] = {}
        self.access_order: Dict[str, float] = {}
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        if key in self.cache:
            self.access_order[key] = time.time()
            return self.cache[key]
        return None
    
    def put(self, key: str, value: Any) -> None:
        """Put value in cache."""
        # Remove oldest items if cache is full
        if len(self.cache) >= self.maxsize:
            self._evict_oldest()
        
        self.cache[key] = value
        self.access_order[key] = time.time()
    
    def _evict_oldest(self) -> None:
        """Evict the least recently used item."""
        if not self.access_order:
            return
        
        oldest_key = min(self.access_order.keys(), key=lambda k: self.access_order[k])
        del self.cache[oldest_key]
        del self.access_order[oldest_key]
    
    def clear(self) -> None:
        """Clear all cached items."""
        self.cache.clear()
        self.access_order.clear()
    
    def size(self) -> int:
        """Get current cache size."""
        return len(self.cache)


class DiskCache:
    """Disk-based cache for persistent storage of analysis results."""
    
    def __init__(self, cache_dir: Path):
        """Initialize disk cache with cache directory."""
        self.cache_dir = cache_dir
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Create subdirectories
        self.results_dir = self.cache_dir / "results"
        self.metadata_dir = self.cache_dir / "metadata"
        self.results_dir.mkdir(exist_ok=True)
        self.metadata_dir.mkdir(exist_ok=True)
    
    def get(self, key: str) -> Optional[AnalysisResult]:
        """Get analysis result from disk cache."""
        try:
            result_path = self.results_dir / f"{key}.pkl"
            metadata_path = self.metadata_dir / f"{key}.json"
            
            if not result_path.exists() or not metadata_path.exists():
                return None
            
            # Check if cache is still valid
            with open(metadata_path, 'r') as f:
                metadata = json.load(f)
            
            if not self._is_cache_valid(metadata):
                self._remove_cache_entry(key)
                return None
            
            # Load result
            with open(result_path, 'rb') as f:
                result = pickle.load(f)
            
            logger.debug(f"Cache hit for key: {key}")
            return result
            
        except Exception as e:
            logger.warning(f"Failed to read from disk cache: {e}")
            return None
    
    def put(self, key: str, result: AnalysisResult, file_mtime: float) -> None:
        """Put analysis result in disk cache."""
        try:
            result_path = self.results_dir / f"{key}.pkl"
            metadata_path = self.metadata_dir / f"{key}.json"
            
            # Save result
            with open(result_path, 'wb') as f:
                pickle.dump(result, f)
            
            # Save metadata
            metadata = {
                "timestamp": time.time(),
                "file_mtime": file_mtime,
                "file_path": str(result.target.path),
                "success": result.success,
                "rules_executed": result.rules_executed
            }
            
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2)
            
            logger.debug(f"Cached result for key: {key}")
            
        except Exception as e:
            logger.warning(f"Failed to write to disk cache: {e}")
    
    def _is_cache_valid(self, metadata: Dict[str, Any]) -> bool:
        """Check if cached entry is still valid."""
        try:
            file_path = Path(metadata["file_path"])
            
            # Check if file still exists
            if not file_path.exists():
                return False
            
            # Check if file has been modified
            current_mtime = file_path.stat().st_mtime
            cached_mtime = metadata["file_mtime"]
            
            if current_mtime > cached_mtime:
                return False
            
            # Check cache age (expire after 24 hours)
            cache_age = time.time() - metadata["timestamp"]
            if cache_age > 24 * 3600:  # 24 hours
                return False
            
            return True
            
        except Exception:
            return False
    
    def _remove_cache_entry(self, key: str) -> None:
        """Remove cache entry from disk."""
        try:
            result_path = self.results_dir / f"{key}.pkl"
            metadata_path = self.metadata_dir / f"{key}.json"
            
            if result_path.exists():
                result_path.unlink()
            if metadata_path.exists():
                metadata_path.unlink()
                
        except Exception as e:
            logger.warning(f"Failed to remove cache entry: {e}")
    
    def clear(self) -> None:
        """Clear all cached entries."""
        try:
            for file_path in self.results_dir.glob("*.pkl"):
                file_path.unlink()
            for file_path in self.metadata_dir.glob("*.json"):
                file_path.unlink()
                
        except Exception as e:
            logger.warning(f"Failed to clear disk cache: {e}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        try:
            result_files = list(self.results_dir.glob("*.pkl"))
            metadata_files = list(self.metadata_dir.glob("*.json"))
            
            total_size = sum(f.stat().st_size for f in result_files)
            total_size += sum(f.stat().st_size for f in metadata_files)
            
            return {
                "entries": len(result_files),
                "total_size_bytes": total_size,
                "total_size_mb": total_size / (1024 * 1024)
            }
            
        except Exception:
            return {"entries": 0, "total_size_bytes": 0, "total_size_mb": 0}


class MetadataCache:
    """Cache for file metadata to optimize cache key generation."""
    
    def __init__(self):
        """Initialize metadata cache."""
        self.cache: Dict[str, Dict[str, Any]] = {}
    
    def get_file_metadata(self, file_path: Path) -> Dict[str, Any]:
        """Get file metadata with caching."""
        path_str = str(file_path)
        
        try:
            stat = file_path.stat()
            current_mtime = stat.st_mtime
            
            # Check if we have cached metadata
            if path_str in self.cache:
                cached_metadata = self.cache[path_str]
                if cached_metadata["mtime"] == current_mtime:
                    return cached_metadata
            
            # Generate new metadata
            metadata = {
                "mtime": current_mtime,
                "size": stat.st_size,
                "path": path_str
            }
            
            self.cache[path_str] = metadata
            return metadata
            
        except Exception as e:
            logger.warning(f"Failed to get file metadata for {file_path}: {e}")
            return {"mtime": 0, "size": 0, "path": path_str}


class CacheManager:
    """Multi-level cache manager for VCS analysis results."""
    
    def __init__(self, cache_dir: Path):
        """Initialize cache manager."""
        self.cache_dir = cache_dir
        self.memory_cache = LRUCache(maxsize=1000)
        self.disk_cache = DiskCache(cache_dir)
        self.metadata_cache = MetadataCache()
        self.dependency_tracker = DependencyTracker(cache_dir)
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize cache manager."""
        if self._initialized:
            return
        
        # Ensure cache directory exists
        self.cache_dir.mkdir(parents=True, exist_ok=True)

        # Initialize dependency tracker
        await self.dependency_tracker.initialize()

        self._initialized = True
        logger.info(f"Cache manager initialized at {self.cache_dir}")
    
    async def get_cached_result(self, target: AnalysisTarget) -> Optional[AnalysisResult]:
        """Get cached analysis result if available and valid."""
        if not self._initialized:
            return None
        
        try:
            # Generate cache key
            cache_key = self._generate_cache_key(target)
            
            # Try memory cache first
            result = self.memory_cache.get(cache_key)
            if result:
                logger.debug(f"Memory cache hit for {target.path}")
                return result
            
            # Try disk cache
            result = self.disk_cache.get(cache_key)
            if result:
                # Store in memory cache for faster access
                self.memory_cache.put(cache_key, result)
                logger.debug(f"Disk cache hit for {target.path}")
                return result
            
            return None
            
        except Exception as e:
            logger.warning(f"Failed to get cached result: {e}")
            return None
    
    async def cache_result(self, target: AnalysisTarget, result: AnalysisResult) -> None:
        """Cache analysis result."""
        if not self._initialized:
            return
        
        try:
            # Generate cache key
            cache_key = self._generate_cache_key(target)
            
            # Get file metadata
            metadata = self.metadata_cache.get_file_metadata(target.path)
            
            # Store in memory cache
            self.memory_cache.put(cache_key, result)
            
            # Store in disk cache
            self.disk_cache.put(cache_key, result, metadata["mtime"])

            # Update dependencies
            await self.dependency_tracker.update_dependencies(target)

            logger.debug(f"Cached result for {target.path}")
            
        except Exception as e:
            logger.warning(f"Failed to cache result: {e}")
    
    def _generate_cache_key(self, target: AnalysisTarget) -> str:
        """Generate cache key for analysis target."""
        # Get file metadata
        metadata = self.metadata_cache.get_file_metadata(target.path)
        
        # Create key from path, mtime, and size
        key_data = f"{target.path}:{metadata['mtime']}:{metadata['size']}"
        
        # Hash the key for consistent length
        return hashlib.sha256(key_data.encode()).hexdigest()
    
    async def cleanup(self) -> None:
        """Cleanup cache resources."""
        if not self._initialized:
            return
        
        # Clear memory cache
        self.memory_cache.clear()

        # Cleanup dependency tracker
        await self.dependency_tracker.cleanup()

        logger.info("Cache manager cleaned up")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        memory_stats = {
            "size": self.memory_cache.size(),
            "maxsize": self.memory_cache.maxsize
        }
        
        disk_stats = self.disk_cache.get_cache_stats()
        
        return {
            "memory": memory_stats,
            "disk": disk_stats,
            "metadata_entries": len(self.metadata_cache.cache)
        }

    def invalidate_cache(self, target: AnalysisTarget) -> None:
        """Invalidate cache for a specific target."""
        try:
            cache_key = self._generate_cache_key(target)

            # Remove from memory cache
            if cache_key in self.memory_cache.cache:
                del self.memory_cache.cache[cache_key]
                del self.memory_cache.access_order[cache_key]

            # Remove from disk cache
            self.disk_cache._remove_cache_entry(cache_key)

            logger.debug(f"Invalidated cache for {target.path}")

        except Exception as e:
            logger.warning(f"Failed to invalidate cache: {e}")

    def clear_all_caches(self) -> None:
        """Clear all caches."""
        self.memory_cache.clear()
        self.disk_cache.clear()
        self.metadata_cache.cache.clear()
        logger.info("All caches cleared")

    async def invalidate_dependent_caches(self, changed_file: Path) -> None:
        """Invalidate caches for files that depend on the changed file."""
        if not self._initialized:
            return

        try:
            # Get files that depend on the changed file
            dependent_files = self.dependency_tracker.get_files_to_invalidate(changed_file)

            # Invalidate cache for each dependent file
            for file_path in dependent_files:
                target = AnalysisTarget.from_file(file_path)
                self.invalidate_cache(target)

            # Also invalidate the changed file itself
            target = AnalysisTarget.from_file(changed_file)
            self.invalidate_cache(target)

            logger.info(f"Invalidated caches for {len(dependent_files) + 1} files due to change in {changed_file}")

        except Exception as e:
            logger.warning(f"Failed to invalidate dependent caches: {e}")

    async def get_cache_statistics(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics."""
        stats = self.get_cache_stats()

        # Add dependency tracking stats
        if self.dependency_tracker.graph:
            dependency_stats = {
                "total_dependencies": sum(len(deps) for deps in self.dependency_tracker.graph.dependencies.values()),
                "files_with_dependencies": len(self.dependency_tracker.graph.dependencies),
                "last_updated": self.dependency_tracker.graph.last_updated
            }
            stats["dependencies"] = dependency_stats

        return stats
