"""
VCS Rule Registry
=================

This module provides the rule registry system for managing
analysis rules with categorization and dependency resolution.
"""

import ast
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Set
import logging

from .models import (
    RuleCategory, AnalysisTarget, AnalysisIssue, AnalysisContext,
    IssueSeverity
)

logger = logging.getLogger(__name__)


class AnalysisRule(ABC):
    """
    Abstract base class for VCS analysis rules.
    
    All analysis rules must inherit from this class and implement
    the analyze method.
    """
    
    def __init__(self, rule_id: str, category: RuleCategory, 
                 name: str, description: str, severity: IssueSeverity = IssueSeverity.WARNING):
        """
        Initialize analysis rule.
        
        Args:
            rule_id: Unique rule identifier
            category: Rule category
            name: Human-readable rule name
            description: Rule description
            severity: Default severity level
        """
        self.rule_id = rule_id
        self.category = category
        self.name = name
        self.description = description
        self.severity = severity
        self.enabled = True
        self.dependencies: Set[str] = set()
        self.config: Dict[str, Any] = {}
    
    @abstractmethod
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """
        Analyze the target and return issues.
        
        Args:
            target: Analysis target
            content: File content
            ast_tree: Parsed AST
            context: Analysis context
            
        Returns:
            List of analysis issues
        """
        pass
    
    def is_applicable(self, target: AnalysisTarget, context: AnalysisContext) -> bool:
        """
        Check if rule is applicable to the target.
        
        Args:
            target: Analysis target
            context: Analysis context
            
        Returns:
            True if rule should be executed
        """
        # Check if rule is enabled
        if not self.enabled:
            return False
        
        # Check if rule category is enabled
        if self.category not in context.enabled_categories:
            return False
        
        # Check if rule is explicitly disabled
        if self.rule_id in context.disabled_rules:
            return False
        
        # Check if rule is explicitly enabled (if enabled_rules is specified)
        if context.enabled_rules and self.rule_id not in context.enabled_rules:
            return False
        
        return True
    
    def configure(self, config: Dict[str, Any]) -> None:
        """Configure rule with custom settings."""
        self.config.update(config)
    
    def create_issue(self, line: int, column: int, message: str, 
                    fix_suggestion: Optional[str] = None, 
                    auto_fixable: bool = False,
                    severity: Optional[IssueSeverity] = None,
                    metadata: Optional[Dict[str, Any]] = None) -> AnalysisIssue:
        """
        Create an analysis issue.
        
        Args:
            line: Line number (1-based)
            column: Column number (0-based)
            message: Issue message
            fix_suggestion: Optional fix suggestion
            auto_fixable: Whether issue can be auto-fixed
            severity: Issue severity (defaults to rule severity)
            metadata: Optional metadata
            
        Returns:
            Analysis issue
        """
        return AnalysisIssue(
            line=line,
            column=column,
            message=message,
            severity=severity or self.severity,
            rule_id=self.rule_id,
            category=self.category,
            source="vcs",
            fix_suggestion=fix_suggestion,
            auto_fixable=auto_fixable,
            metadata=metadata or {}
        )


class RuleRegistry:
    """
    Registry for managing analysis rules with categorization and dependencies.
    
    The registry provides:
    - Rule registration and discovery
    - Category-based rule organization
    - Dependency resolution
    - Rule configuration management
    """
    
    def __init__(self):
        """Initialize the rule registry."""
        self.rules: Dict[str, AnalysisRule] = {}
        self.categories: Dict[RuleCategory, Set[str]] = {
            category: set() for category in RuleCategory
        }
        self.dependencies: Dict[str, Set[str]] = {}
        self._rule_configs: Dict[str, Dict[str, Any]] = {}
    
    def register_rule(self, rule: AnalysisRule) -> None:
        """
        Register a new analysis rule.
        
        Args:
            rule: Analysis rule to register
            
        Raises:
            ValueError: If rule ID already exists
        """
        if rule.rule_id in self.rules:
            raise ValueError(f"Rule {rule.rule_id} already registered")
        
        # Register rule
        self.rules[rule.rule_id] = rule
        self.categories[rule.category].add(rule.rule_id)
        
        # Register dependencies
        if rule.dependencies:
            self.dependencies[rule.rule_id] = rule.dependencies.copy()
        
        # Apply any existing configuration
        if rule.rule_id in self._rule_configs:
            rule.configure(self._rule_configs[rule.rule_id])
        
        logger.debug(f"Registered rule: {rule.rule_id} ({rule.category.value})")
    
    def unregister_rule(self, rule_id: str) -> None:
        """
        Unregister an analysis rule.
        
        Args:
            rule_id: Rule ID to unregister
        """
        if rule_id not in self.rules:
            return
        
        rule = self.rules[rule_id]
        
        # Remove from registry
        del self.rules[rule_id]
        self.categories[rule.category].discard(rule_id)
        
        # Remove dependencies
        if rule_id in self.dependencies:
            del self.dependencies[rule_id]
        
        logger.debug(f"Unregistered rule: {rule_id}")
    
    def get_rule(self, rule_id: str) -> Optional[AnalysisRule]:
        """
        Get rule by ID.
        
        Args:
            rule_id: Rule ID
            
        Returns:
            Analysis rule or None if not found
        """
        return self.rules.get(rule_id)

    def get_all_rules(self) -> List[AnalysisRule]:
        """
        Get all registered rules.

        Returns:
            List of all analysis rules
        """
        return list(self.rules.values())

    def get_rules_for_category(self, category: RuleCategory) -> List[AnalysisRule]:
        """
        Get all rules for a specific category.
        
        Args:
            category: Rule category
            
        Returns:
            List of analysis rules
        """
        rule_ids = self.categories.get(category, set())
        return [self.rules[rule_id] for rule_id in rule_ids if rule_id in self.rules]
    
    def get_applicable_rules(self, context: AnalysisContext) -> List[AnalysisRule]:
        """
        Get all rules applicable for the given context.
        
        Args:
            context: Analysis context
            
        Returns:
            List of applicable rules in dependency order
        """
        # Get all potentially applicable rules
        applicable_rules = []
        
        for rule in self.rules.values():
            # Create a dummy target for applicability check
            # In real usage, this would be called with actual target
            dummy_target = AnalysisTarget.from_content("dummy.py", "")
            
            # Check basic applicability (category, enabled/disabled)
            if (rule.category in context.enabled_categories and
                rule.rule_id not in context.disabled_rules and
                (not context.enabled_rules or rule.rule_id in context.enabled_rules)):
                applicable_rules.append(rule)
        
        # Resolve dependencies and return in execution order
        return self._resolve_dependencies([rule.rule_id for rule in applicable_rules])
    
    def resolve_dependencies(self, rule_ids: Set[str]) -> List[str]:
        """
        Resolve rule dependencies and return execution order.
        
        Args:
            rule_ids: Set of rule IDs to resolve
            
        Returns:
            List of rule IDs in dependency order
            
        Raises:
            ValueError: If circular dependencies are detected
        """
        resolved = []
        visited = set()
        visiting = set()
        
        def visit(rule_id: str) -> None:
            if rule_id in visiting:
                raise ValueError(f"Circular dependency detected involving rule: {rule_id}")
            
            if rule_id in visited:
                return
            
            visiting.add(rule_id)
            
            # Visit dependencies first
            for dep_id in self.dependencies.get(rule_id, set()):
                if dep_id in rule_ids:  # Only consider dependencies that are in our set
                    visit(dep_id)
            
            visiting.remove(rule_id)
            visited.add(rule_id)
            resolved.append(rule_id)
        
        # Visit all rules
        for rule_id in rule_ids:
            if rule_id not in visited:
                visit(rule_id)
        
        return resolved
    
    def _resolve_dependencies(self, rule_ids: List[str]) -> List[AnalysisRule]:
        """Resolve dependencies and return rule objects."""
        try:
            ordered_ids = self.resolve_dependencies(set(rule_ids))
            return [self.rules[rule_id] for rule_id in ordered_ids if rule_id in self.rules]
        except ValueError as e:
            logger.warning(f"Dependency resolution failed: {e}")
            # Return rules without dependency ordering
            return [self.rules[rule_id] for rule_id in rule_ids if rule_id in self.rules]
    
    def configure_rule(self, rule_id: str, config: Dict[str, Any]) -> None:
        """
        Configure a rule with custom settings.
        
        Args:
            rule_id: Rule ID to configure
            config: Configuration dictionary
        """
        # Store configuration
        self._rule_configs[rule_id] = config
        
        # Apply to rule if it's registered
        if rule_id in self.rules:
            self.rules[rule_id].configure(config)
    
    def enable_rule(self, rule_id: str) -> None:
        """Enable a rule."""
        if rule_id in self.rules:
            self.rules[rule_id].enabled = True
    
    def disable_rule(self, rule_id: str) -> None:
        """Disable a rule."""
        if rule_id in self.rules:
            self.rules[rule_id].enabled = False
    
    def get_registry_stats(self) -> Dict[str, Any]:
        """Get registry statistics."""
        enabled_count = sum(1 for rule in self.rules.values() if rule.enabled)
        
        category_counts = {}
        for category in RuleCategory:
            category_rules = self.categories[category]
            enabled_in_category = sum(1 for rule_id in category_rules 
                                    if rule_id in self.rules and self.rules[rule_id].enabled)
            category_counts[category.value] = {
                "total": len(category_rules),
                "enabled": enabled_in_category
            }
        
        return {
            "total_rules": len(self.rules),
            "enabled_rules": enabled_count,
            "disabled_rules": len(self.rules) - enabled_count,
            "categories": category_counts,
            "dependencies": len(self.dependencies)
        }
