"""
AST Dispatcher
==============

Shared AST traversal system for VCS rules to optimize performance
from O(n*rules) to O(n) complexity through single-pass traversal.
"""

import ast
import time
import logging
from collections import defaultdict
from typing import Dict, List, Set, Type, Callable, Any, Optional
from dataclasses import dataclass

from .models import AnalysisTarget, AnalysisContext, AnalysisIssue
from .registry import AnalysisRule

logger = logging.getLogger(__name__)


@dataclass
class NodeDispatchInfo:
    """Information about a rule's interest in specific AST node types."""
    
    rule: AnalysisRule
    node_types: Set[Type[ast.AST]]
    handler: Callable[[ast.AST, AnalysisTarget, str, AnalysisContext], List[AnalysisIssue]]
    priority: int = 0  # Higher priority rules run first


class ASTDispatcher:
    """
    Shared AST traversal system that dispatches nodes to interested rules.
    
    This system replaces individual rule AST traversals with a single-pass
    traversal that dispatches nodes to rules based on their declared interests.
    
    Performance improvement: O(n*rules) -> O(n) where n is AST nodes.
    """
    
    def __init__(self):
        """Initialize the AST dispatcher."""
        # Map node types to interested rules
        self.node_handlers: Dict[Type[ast.AST], List[NodeDispatchInfo]] = defaultdict(list)
        
        # Performance tracking
        self.traversal_stats = {
            'total_traversals': 0,
            'total_nodes_visited': 0,
            'total_rule_dispatches': 0,
            'average_traversal_time': 0.0
        }
        
        # Rule registration tracking
        self.registered_rules: Set[str] = set()
    
    def register_rule(self, rule: AnalysisRule, node_interests: Dict[Type[ast.AST], Callable]) -> None:
        """
        Register a rule with its AST node interests.
        
        Args:
            rule: Analysis rule to register
            node_interests: Dict mapping AST node types to handler functions
                          e.g., {ast.FunctionDef: self._handle_function_def}
        """
        if rule.rule_id in self.registered_rules:
            logger.warning(f"Rule {rule.rule_id} already registered with AST dispatcher")
            return
        
        for node_type, handler in node_interests.items():
            dispatch_info = NodeDispatchInfo(
                rule=rule,
                node_types={node_type},
                handler=handler,
                priority=self._get_rule_priority(rule)
            )
            
            self.node_handlers[node_type].append(dispatch_info)
            logger.debug(f"Registered {rule.rule_id} for {node_type.__name__} nodes")
        
        # Sort handlers by priority (higher priority first)
        for node_type in node_interests.keys():
            self.node_handlers[node_type].sort(key=lambda x: x.priority, reverse=True)
        
        self.registered_rules.add(rule.rule_id)
        logger.info(f"Registered rule {rule.rule_id} with {len(node_interests)} node type interests")
    
    def unregister_rule(self, rule_id: str) -> None:
        """
        Unregister a rule from the dispatcher.
        
        Args:
            rule_id: Rule ID to unregister
        """
        if rule_id not in self.registered_rules:
            return
        
        # Remove from all node handlers
        for node_type, handlers in self.node_handlers.items():
            self.node_handlers[node_type] = [
                h for h in handlers if h.rule.rule_id != rule_id
            ]
        
        self.registered_rules.discard(rule_id)
        logger.debug(f"Unregistered rule {rule_id} from AST dispatcher")
    
    async def dispatch_analysis(self, target: AnalysisTarget, content: str, 
                              ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """
        Perform shared AST traversal and dispatch nodes to interested rules.
        
        Args:
            target: Analysis target
            content: File content
            ast_tree: Parsed AST tree
            context: Analysis context
            
        Returns:
            Combined list of issues from all rules
        """
        start_time = time.time()
        
        # Create visitor for this analysis
        visitor = SharedASTVisitor(self, target, content, context)
        
        # Perform single traversal
        visitor.visit(ast_tree)
        
        # Update performance stats
        traversal_time = time.time() - start_time
        self._update_stats(visitor.nodes_visited, visitor.rule_dispatches, traversal_time)
        
        logger.debug(
            f"AST dispatch completed: {visitor.nodes_visited} nodes, "
            f"{visitor.rule_dispatches} rule dispatches, "
            f"{len(visitor.issues)} issues, {traversal_time*1000:.1f}ms"
        )
        
        return visitor.issues
    
    def _get_rule_priority(self, rule: AnalysisRule) -> int:
        """
        Get priority for a rule based on its category.
        
        Higher priority rules run first to potentially short-circuit analysis.
        """
        priority_map = {
            'security': 100,    # Security rules highest priority
            'types': 90,        # Type checking high priority
            'complexity': 80,   # Complexity analysis medium-high
            'imports': 70,      # Import analysis medium
            'style': 60,        # Style rules medium-low
            'documentation': 50 # Documentation lowest priority
        }
        
        return priority_map.get(rule.category.value, 50)
    
    def _update_stats(self, nodes_visited: int, rule_dispatches: int, traversal_time: float) -> None:
        """Update performance statistics."""
        self.traversal_stats['total_traversals'] += 1
        self.traversal_stats['total_nodes_visited'] += nodes_visited
        self.traversal_stats['total_rule_dispatches'] += rule_dispatches
        
        # Update average traversal time
        total_traversals = self.traversal_stats['total_traversals']
        current_avg = self.traversal_stats['average_traversal_time']
        self.traversal_stats['average_traversal_time'] = (
            (current_avg * (total_traversals - 1) + traversal_time) / total_traversals
        )
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics for the dispatcher."""
        stats = self.traversal_stats.copy()
        
        if stats['total_traversals'] > 0:
            stats['average_nodes_per_traversal'] = (
                stats['total_nodes_visited'] / stats['total_traversals']
            )
            stats['average_dispatches_per_traversal'] = (
                stats['total_rule_dispatches'] / stats['total_traversals']
            )
        else:
            stats['average_nodes_per_traversal'] = 0
            stats['average_dispatches_per_traversal'] = 0
        
        stats['registered_rules_count'] = len(self.registered_rules)
        stats['node_type_handlers'] = {
            node_type.__name__: len(handlers) 
            for node_type, handlers in self.node_handlers.items()
        }
        
        return stats


class SharedASTVisitor(ast.NodeVisitor):
    """
    Shared AST visitor that dispatches nodes to interested rules.
    
    This visitor performs a single traversal of the AST and dispatches
    each node to rules that have declared interest in that node type.
    """
    
    def __init__(self, dispatcher: ASTDispatcher, target: AnalysisTarget, 
                 content: str, context: AnalysisContext):
        """
        Initialize the shared visitor.
        
        Args:
            dispatcher: AST dispatcher instance
            target: Analysis target
            content: File content
            context: Analysis context
        """
        self.dispatcher = dispatcher
        self.target = target
        self.content = content
        self.context = context
        
        # Tracking
        self.issues: List[AnalysisIssue] = []
        self.nodes_visited = 0
        self.rule_dispatches = 0
        
        # Error tracking
        self.rule_errors: Dict[str, int] = defaultdict(int)
    
    def visit(self, node: ast.AST) -> None:
        """
        Visit a node and dispatch to interested rules.
        
        Args:
            node: AST node to visit
        """
        self.nodes_visited += 1
        
        # Get handlers for this node type
        node_type = type(node)
        handlers = self.dispatcher.node_handlers.get(node_type, [])
        
        # Dispatch to interested rules
        for dispatch_info in handlers:
            if not dispatch_info.rule.enabled:
                continue
                
            try:
                self.rule_dispatches += 1
                rule_issues = dispatch_info.handler(node, self.target, self.content, self.context)
                if rule_issues:
                    self.issues.extend(rule_issues)
                    
            except Exception as e:
                rule_id = dispatch_info.rule.rule_id
                self.rule_errors[rule_id] += 1
                
                # Log error only once per rule to avoid spam
                if self.rule_errors[rule_id] == 1:
                    logger.warning(
                        f"Rule {rule_id} failed on {node_type.__name__} node "
                        f"in {self.target.path.name}: {e}"
                    )
        
        # Continue traversal
        self.generic_visit(node)


class ASTRuleAdapter:
    """
    Adapter to help existing rules integrate with the shared AST dispatcher.
    
    This class provides utilities to convert existing rule visitor patterns
    to the new dispatcher-based system.
    """
    
    @staticmethod
    def create_node_handler(rule: AnalysisRule, visitor_method: str) -> Callable:
        """
        Create a node handler function from an existing visitor method.
        
        Args:
            rule: Analysis rule instance
            visitor_method: Name of the visitor method (e.g., 'visit_FunctionDef')
            
        Returns:
            Handler function compatible with ASTDispatcher
        """
        def handler(node: ast.AST, target: AnalysisTarget, content: str, 
                   context: AnalysisContext) -> List[AnalysisIssue]:
            """Generated handler function."""
            # Create a temporary visitor instance
            visitor = _TemporaryRuleVisitor(rule, target, content, context)
            
            # Call the specific visitor method
            method = getattr(visitor, visitor_method, None)
            if method:
                method(node)
            
            return visitor.issues
        
        return handler
    
    @staticmethod
    def extract_node_interests(rule: AnalysisRule) -> Dict[Type[ast.AST], Callable]:
        """
        Extract AST node interests from an existing rule.
        
        This method analyzes a rule's visitor methods to determine
        which AST node types it's interested in.
        
        Args:
            rule: Analysis rule to analyze
            
        Returns:
            Dict mapping AST node types to handler functions
        """
        interests = {}
        
        # Look for visitor methods in the rule
        for attr_name in dir(rule):
            if attr_name.startswith('visit_') and attr_name != 'visit':
                node_type_name = attr_name[6:]  # Remove 'visit_' prefix
                
                # Get the corresponding AST node type
                node_type = getattr(ast, node_type_name, None)
                if node_type and issubclass(node_type, ast.AST):
                    handler = ASTRuleAdapter.create_node_handler(rule, attr_name)
                    interests[node_type] = handler
        
        return interests


class _TemporaryRuleVisitor:
    """Temporary visitor for rule adaptation."""

    def __init__(self, rule: AnalysisRule, target: AnalysisTarget,
                 content: str, context: AnalysisContext):
        self.rule = rule
        self.target = target
        self.content = content
        self.context = context
        self.issues: List[AnalysisIssue] = []

    def create_issue(self, line: int, column: int, message: str,
                    severity: Optional[str] = None, auto_fixable: bool = False,
                    fix_suggestion: Optional[str] = None) -> AnalysisIssue:
        """Create an analysis issue (helper for rule adaptation)."""
        from .models import IssueSeverity

        # Convert string severity to enum if needed
        final_severity = self.rule.severity  # Default to rule severity
        if isinstance(severity, str):
            severity_map = {
                'error': IssueSeverity.ERROR,
                'warning': IssueSeverity.WARNING,
                'info': IssueSeverity.INFO,
                'hint': IssueSeverity.HINT
            }
            final_severity = severity_map.get(severity.lower(), self.rule.severity)

        issue = AnalysisIssue(
            rule_id=self.rule.rule_id,
            category=self.rule.category,
            severity=final_severity,
            line=line,
            column=column,
            message=message,
            auto_fixable=auto_fixable,
            fix_suggestion=fix_suggestion
        )

        self.issues.append(issue)
        return issue
