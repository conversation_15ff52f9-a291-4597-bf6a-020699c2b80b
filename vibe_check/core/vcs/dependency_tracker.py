"""
File: vibe_check/core/vcs/dependency_tracker.py
Purpose: Dependency tracking for intelligent cache invalidation
Related Files: vibe_check/core/vcs/cache.py, vibe_check/core/vcs/engine.py
Dependencies: ast, pathlib, typing
"""

import ast
import json
import time
from pathlib import Path
from typing import Dict, List, Set, Optional, Any
from dataclasses import dataclass, asdict

from vibe_check.core.vcs.models import AnalysisTarget
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


@dataclass
class FileDependency:
    """Represents a dependency relationship between files."""
    source_file: str
    target_file: str
    dependency_type: str  # 'import', 'include', 'config', etc.
    line_number: int
    confidence: float = 1.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FileDependency':
        """Create from dictionary."""
        return cls(**data)


@dataclass
class DependencyGraph:
    """Represents the dependency graph for a project."""
    dependencies: Dict[str, List[FileDependency]]
    reverse_dependencies: Dict[str, List[FileDependency]]
    last_updated: float
    
    def add_dependency(self, dependency: FileDependency) -> None:
        """Add a dependency to the graph."""
        source = dependency.source_file
        target = dependency.target_file
        
        # Add to forward dependencies
        if source not in self.dependencies:
            self.dependencies[source] = []
        self.dependencies[source].append(dependency)
        
        # Add to reverse dependencies
        if target not in self.reverse_dependencies:
            self.reverse_dependencies[target] = []
        self.reverse_dependencies[target].append(dependency)
    
    def get_dependents(self, file_path: str) -> List[str]:
        """Get all files that depend on the given file."""
        dependents = []
        if file_path in self.reverse_dependencies:
            for dep in self.reverse_dependencies[file_path]:
                dependents.append(dep.source_file)
        return dependents
    
    def get_dependencies(self, file_path: str) -> List[str]:
        """Get all files that the given file depends on."""
        dependencies = []
        if file_path in self.dependencies:
            for dep in self.dependencies[file_path]:
                dependencies.append(dep.target_file)
        return dependencies
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "dependencies": {
                k: [dep.to_dict() for dep in v] 
                for k, v in self.dependencies.items()
            },
            "reverse_dependencies": {
                k: [dep.to_dict() for dep in v] 
                for k, v in self.reverse_dependencies.items()
            },
            "last_updated": self.last_updated
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DependencyGraph':
        """Create from dictionary."""
        dependencies = {}
        for k, v in data.get("dependencies", {}).items():
            dependencies[k] = [FileDependency.from_dict(dep) for dep in v]
        
        reverse_dependencies = {}
        for k, v in data.get("reverse_dependencies", {}).items():
            reverse_dependencies[k] = [FileDependency.from_dict(dep) for dep in v]
        
        return cls(
            dependencies=dependencies,
            reverse_dependencies=reverse_dependencies,
            last_updated=data.get("last_updated", time.time())
        )


class DependencyAnalyzer:
    """Analyzes Python files to extract dependencies."""
    
    def __init__(self):
        self.stdlib_modules = {
            'os', 'sys', 'json', 'datetime', 'pathlib', 're', 'typing',
            'collections', 'itertools', 'functools', 'operator', 'math',
            'random', 'string', 'time', 'urllib', 'http', 'email',
            'html', 'xml', 'csv', 'sqlite3', 'logging', 'unittest',
            'asyncio', 'concurrent', 'multiprocessing', 'threading'
        }
    
    def analyze_file(self, file_path: Path) -> List[FileDependency]:
        """Analyze a Python file to extract its dependencies."""
        dependencies = []
        
        try:
            content = file_path.read_text(encoding='utf-8')
            tree = ast.parse(content)
            
            # Extract import dependencies
            dependencies.extend(self._extract_import_dependencies(tree, file_path))
            
            # Extract configuration dependencies (e.g., config files)
            dependencies.extend(self._extract_config_dependencies(tree, file_path))
            
        except Exception as e:
            logger.warning(f"Failed to analyze dependencies for {file_path}: {e}")
        
        return dependencies
    
    def _extract_import_dependencies(self, tree: ast.AST, source_file: Path) -> List[FileDependency]:
        """Extract import-based dependencies."""
        dependencies = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    dep = self._create_import_dependency(
                        source_file, alias.name, node.lineno, "import"
                    )
                    if dep:
                        dependencies.append(dep)
            
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    dep = self._create_import_dependency(
                        source_file, node.module, node.lineno, "from_import"
                    )
                    if dep:
                        dependencies.append(dep)
        
        return dependencies
    
    def _create_import_dependency(self, source_file: Path, module_name: str, 
                                line_number: int, dep_type: str) -> Optional[FileDependency]:
        """Create a dependency for an import statement."""
        # Skip standard library modules
        if module_name.split('.')[0] in self.stdlib_modules:
            return None
        
        # Try to resolve the module to a file path
        target_file = self._resolve_module_path(source_file, module_name)
        if not target_file:
            return None
        
        return FileDependency(
            source_file=str(source_file),
            target_file=str(target_file),
            dependency_type=dep_type,
            line_number=line_number,
            confidence=0.9
        )
    
    def _resolve_module_path(self, source_file: Path, module_name: str) -> Optional[Path]:
        """Resolve a module name to a file path."""
        # Simple resolution - look for .py files relative to source
        parts = module_name.split('.')
        
        # Try relative to source file directory
        current_dir = source_file.parent
        for part in parts:
            current_dir = current_dir / part
        
        # Check for .py file
        py_file = current_dir.with_suffix('.py')
        if py_file.exists():
            return py_file
        
        # Check for package (__init__.py)
        init_file = current_dir / '__init__.py'
        if init_file.exists():
            return init_file
        
        # Try from project root (simple heuristic)
        project_root = source_file.parent
        while project_root.parent != project_root:
            if (project_root / 'setup.py').exists() or (project_root / 'pyproject.toml').exists():
                break
            project_root = project_root.parent
        
        module_path = project_root
        for part in parts:
            module_path = module_path / part
        
        py_file = module_path.with_suffix('.py')
        if py_file.exists():
            return py_file
        
        init_file = module_path / '__init__.py'
        if init_file.exists():
            return init_file
        
        return None
    
    def _extract_config_dependencies(self, tree: ast.AST, source_file: Path) -> List[FileDependency]:
        """Extract configuration file dependencies."""
        dependencies = []
        
        # Look for common config file patterns
        config_patterns = [
            'config.json', 'settings.py', '.env', 'requirements.txt',
            'pyproject.toml', 'setup.py', 'setup.cfg'
        ]
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Str):  # String literals
                value = node.s
                for pattern in config_patterns:
                    if pattern in value:
                        config_path = source_file.parent / value
                        if config_path.exists():
                            dependencies.append(FileDependency(
                                source_file=str(source_file),
                                target_file=str(config_path),
                                dependency_type="config",
                                line_number=getattr(node, 'lineno', 0),
                                confidence=0.7
                            ))
        
        return dependencies


class DependencyTracker:
    """Tracks file dependencies for intelligent cache invalidation."""
    
    def __init__(self, cache_dir: Path):
        self.cache_dir = cache_dir
        self.dependency_file = cache_dir / "dependencies.json"
        self.analyzer = DependencyAnalyzer()
        self.graph: Optional[DependencyGraph] = None
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize the dependency tracker."""
        if self._initialized:
            return
        
        # Load existing dependency graph
        await self._load_dependency_graph()
        
        self._initialized = True
        logger.info("Dependency tracker initialized")
    
    async def _load_dependency_graph(self) -> None:
        """Load dependency graph from disk."""
        try:
            if self.dependency_file.exists():
                with open(self.dependency_file, 'r') as f:
                    data = json.load(f)
                self.graph = DependencyGraph.from_dict(data)
                logger.debug("Loaded dependency graph from disk")
            else:
                self.graph = DependencyGraph(
                    dependencies={},
                    reverse_dependencies={},
                    last_updated=time.time()
                )
                logger.debug("Created new dependency graph")
        except Exception as e:
            logger.warning(f"Failed to load dependency graph: {e}")
            self.graph = DependencyGraph(
                dependencies={},
                reverse_dependencies={},
                last_updated=time.time()
            )
    
    async def _save_dependency_graph(self) -> None:
        """Save dependency graph to disk."""
        try:
            self.cache_dir.mkdir(parents=True, exist_ok=True)
            with open(self.dependency_file, 'w') as f:
                json.dump(self.graph.to_dict(), f, indent=2)
            logger.debug("Saved dependency graph to disk")
        except Exception as e:
            logger.warning(f"Failed to save dependency graph: {e}")
    
    async def update_dependencies(self, target: AnalysisTarget) -> None:
        """Update dependencies for a file."""
        if not self._initialized or not target.path:
            return
        
        try:
            file_path = target.path
            dependencies = self.analyzer.analyze_file(file_path)
            
            # Remove old dependencies for this file
            file_str = str(file_path)
            if file_str in self.graph.dependencies:
                old_deps = self.graph.dependencies[file_str]
                for old_dep in old_deps:
                    # Remove from reverse dependencies
                    target_file = old_dep.target_file
                    if target_file in self.graph.reverse_dependencies:
                        self.graph.reverse_dependencies[target_file] = [
                            dep for dep in self.graph.reverse_dependencies[target_file]
                            if dep.source_file != file_str
                        ]
            
            # Clear old dependencies
            self.graph.dependencies[file_str] = []
            
            # Add new dependencies
            for dependency in dependencies:
                self.graph.add_dependency(dependency)
            
            self.graph.last_updated = time.time()
            await self._save_dependency_graph()
            
            logger.debug(f"Updated dependencies for {file_path}")
            
        except Exception as e:
            logger.warning(f"Failed to update dependencies: {e}")
    
    def get_files_to_invalidate(self, changed_file: Path) -> List[Path]:
        """Get list of files whose cache should be invalidated when a file changes."""
        if not self.graph:
            return []
        
        files_to_invalidate = []
        file_str = str(changed_file)
        
        # Get all files that depend on the changed file
        dependents = self.graph.get_dependents(file_str)
        
        for dependent in dependents:
            files_to_invalidate.append(Path(dependent))
            
            # Recursively get dependents of dependents (with cycle detection)
            visited = {file_str}
            self._get_recursive_dependents(dependent, files_to_invalidate, visited)
        
        return files_to_invalidate
    
    def _get_recursive_dependents(self, file_path: str, result: List[Path], visited: Set[str]) -> None:
        """Recursively get dependents with cycle detection."""
        if file_path in visited:
            return
        
        visited.add(file_path)
        dependents = self.graph.get_dependents(file_path)
        
        for dependent in dependents:
            if dependent not in visited:
                result.append(Path(dependent))
                self._get_recursive_dependents(dependent, result, visited)
    
    async def cleanup(self) -> None:
        """Cleanup dependency tracker."""
        if self.graph:
            await self._save_dependency_graph()
        logger.info("Dependency tracker cleaned up")
