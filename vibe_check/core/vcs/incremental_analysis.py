"""
File: vibe_check/core/vcs/incremental_analysis.py
Purpose: Incremental analysis system for large projects
Related Files: vibe_check/core/vcs/engine.py, vibe_check/core/vcs/cache.py
Dependencies: asyncio, pathlib, typing
"""

import hashlib
import time
from pathlib import Path
from typing import Dict, List, Set, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from vibe_check.core.vcs.models import AnalysisTarget, AnalysisResult, AnalysisContext
from vibe_check.core.vcs.cache import CacheManager
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class ChangeType(Enum):
    """Types of file changes."""
    ADDED = "added"
    MODIFIED = "modified"
    DELETED = "deleted"
    RENAMED = "renamed"


@dataclass
class FileChange:
    """Represents a change to a file."""
    path: Path
    change_type: ChangeType
    old_path: Optional[Path] = None
    timestamp: float = 0.0
    
    def __post_init__(self):
        if self.timestamp == 0.0:
            self.timestamp = time.time()


@dataclass
class AnalysisSnapshot:
    """Snapshot of analysis state for incremental processing."""
    timestamp: float
    file_checksums: Dict[str, str]
    analysis_results: Dict[str, AnalysisResult]
    total_files: int
    total_issues: int
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "timestamp": self.timestamp,
            "file_checksums": self.file_checksums,
            "total_files": self.total_files,
            "total_issues": self.total_issues
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AnalysisSnapshot':
        """Create from dictionary."""
        return cls(
            timestamp=data["timestamp"],
            file_checksums=data["file_checksums"],
            analysis_results={},  # Results are stored separately in cache
            total_files=data["total_files"],
            total_issues=data["total_issues"]
        )


class ChangeDetector:
    """Detects changes in project files for incremental analysis."""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.ignore_patterns = {
            '__pycache__', '.git', '.venv', 'venv', 'node_modules',
            '.pytest_cache', '.mypy_cache', '.coverage', '*.pyc'
        }
    
    def detect_changes(self, last_snapshot: Optional[AnalysisSnapshot]) -> List[FileChange]:
        """Detect changes since the last snapshot."""
        changes = []
        
        if not last_snapshot:
            # First run - all files are new
            for file_path in self._get_python_files():
                changes.append(FileChange(file_path, ChangeType.ADDED))
            return changes
        
        current_files = self._get_python_files()
        current_checksums = self._calculate_checksums(current_files)
        last_checksums = last_snapshot.file_checksums
        
        # Find added files
        for file_path in current_files:
            file_str = str(file_path)
            if file_str not in last_checksums:
                changes.append(FileChange(file_path, ChangeType.ADDED))
        
        # Find modified files
        for file_path in current_files:
            file_str = str(file_path)
            if (file_str in last_checksums and 
                current_checksums[file_str] != last_checksums[file_str]):
                changes.append(FileChange(file_path, ChangeType.MODIFIED))
        
        # Find deleted files
        for file_str in last_checksums:
            file_path = Path(file_str)
            if file_path not in current_files:
                changes.append(FileChange(file_path, ChangeType.DELETED))
        
        return changes
    
    def _get_python_files(self) -> Set[Path]:
        """Get all Python files in the project."""
        python_files = set()
        
        for file_path in self.project_root.rglob("*.py"):
            if not self._should_ignore(file_path):
                python_files.add(file_path)
        
        return python_files
    
    def _should_ignore(self, file_path: Path) -> bool:
        """Check if file should be ignored."""
        path_parts = file_path.parts
        
        for pattern in self.ignore_patterns:
            if pattern.startswith('*'):
                # Wildcard pattern
                if file_path.name.endswith(pattern[1:]):
                    return True
            else:
                # Directory pattern
                if pattern in path_parts:
                    return True
        
        return False
    
    def _calculate_checksums(self, files: Set[Path]) -> Dict[str, str]:
        """Calculate checksums for files."""
        checksums = {}
        
        for file_path in files:
            try:
                content = file_path.read_bytes()
                checksum = hashlib.sha256(content).hexdigest()
                checksums[str(file_path)] = checksum
            except Exception as e:
                logger.warning(f"Failed to calculate checksum for {file_path}: {e}")
                checksums[str(file_path)] = ""
        
        return checksums


class IncrementalAnalyzer:
    """Performs incremental analysis on large projects."""
    
    def __init__(self, project_root: Path, cache_manager: CacheManager):
        self.project_root = project_root
        self.cache_manager = cache_manager
        self.change_detector = ChangeDetector(project_root)
        self.snapshot_file = cache_manager.cache_dir / "analysis_snapshot.json"
        self.last_snapshot: Optional[AnalysisSnapshot] = None
    
    async def initialize(self) -> None:
        """Initialize the incremental analyzer."""
        await self._load_last_snapshot()
        logger.info("Incremental analyzer initialized")
    
    async def _load_last_snapshot(self) -> None:
        """Load the last analysis snapshot."""
        try:
            if self.snapshot_file.exists():
                import json
                with open(self.snapshot_file, 'r') as f:
                    data = json.load(f)
                self.last_snapshot = AnalysisSnapshot.from_dict(data)
                logger.debug("Loaded last analysis snapshot")
            else:
                logger.debug("No previous snapshot found")
        except Exception as e:
            logger.warning(f"Failed to load snapshot: {e}")
            self.last_snapshot = None
    
    async def _save_snapshot(self, snapshot: AnalysisSnapshot) -> None:
        """Save analysis snapshot."""
        try:
            import json
            self.cache_manager.cache_dir.mkdir(parents=True, exist_ok=True)
            with open(self.snapshot_file, 'w') as f:
                json.dump(snapshot.to_dict(), f, indent=2)
            logger.debug("Saved analysis snapshot")
        except Exception as e:
            logger.warning(f"Failed to save snapshot: {e}")
    
    async def analyze_incrementally(self, engine, context: AnalysisContext) -> Tuple[List[AnalysisResult], Dict[str, Any]]:
        """
        Perform incremental analysis.
        
        Returns:
            Tuple of (results, statistics)
        """
        start_time = time.time()
        
        # Detect changes
        changes = self.change_detector.detect_changes(self.last_snapshot)
        
        if not changes:
            logger.info("No changes detected - using cached results")
            return await self._get_cached_results(), {
                "changes_detected": 0,
                "files_analyzed": 0,
                "cache_hits": self.last_snapshot.total_files if self.last_snapshot else 0,
                "analysis_time": time.time() - start_time
            }
        
        logger.info(f"Detected {len(changes)} changes")
        
        # Analyze changed files
        results = []
        files_analyzed = 0
        
        for change in changes:
            if change.change_type == ChangeType.DELETED:
                # Remove from cache
                if change.path.exists():  # Safety check
                    target = AnalysisTarget.from_file(change.path)
                    self.cache_manager.invalidate_cache(target)
                continue
            
            # Analyze added/modified files
            try:
                target = AnalysisTarget.from_file(change.path)
                result = await engine.analyze(target, context)
                results.append(result)
                files_analyzed += 1
                
                # Invalidate dependent caches for modified files
                if change.change_type == ChangeType.MODIFIED:
                    await self.cache_manager.invalidate_dependent_caches(change.path)
                
            except Exception as e:
                logger.error(f"Failed to analyze {change.path}: {e}")
        
        # Get cached results for unchanged files
        cached_results = await self._get_cached_results_for_unchanged_files(changes)
        results.extend(cached_results)
        
        # Create new snapshot
        current_files = self.change_detector._get_python_files()
        current_checksums = self.change_detector._calculate_checksums(current_files)
        
        new_snapshot = AnalysisSnapshot(
            timestamp=time.time(),
            file_checksums=current_checksums,
            analysis_results={str(r.target.path): r for r in results},
            total_files=len(current_files),
            total_issues=sum(len(r.issues) for r in results)
        )
        
        await self._save_snapshot(new_snapshot)
        self.last_snapshot = new_snapshot
        
        statistics = {
            "changes_detected": len(changes),
            "files_analyzed": files_analyzed,
            "cache_hits": len(cached_results),
            "total_files": len(current_files),
            "total_issues": new_snapshot.total_issues,
            "analysis_time": time.time() - start_time
        }
        
        return results, statistics
    
    async def _get_cached_results(self) -> List[AnalysisResult]:
        """Get all cached results."""
        if not self.last_snapshot:
            return []
        
        results = []
        for file_str in self.last_snapshot.file_checksums:
            file_path = Path(file_str)
            if file_path.exists():
                try:
                    target = AnalysisTarget.from_file(file_path)
                    cached_result = await self.cache_manager.get_cached_result(target)
                    if cached_result:
                        results.append(cached_result)
                except Exception as e:
                    logger.warning(f"Failed to get cached result for {file_path}: {e}")
        
        return results
    
    async def _get_cached_results_for_unchanged_files(self, changes: List[FileChange]) -> List[AnalysisResult]:
        """Get cached results for files that haven't changed."""
        if not self.last_snapshot:
            return []
        
        changed_files = {str(change.path) for change in changes}
        results = []
        
        for file_str in self.last_snapshot.file_checksums:
            if file_str not in changed_files:
                file_path = Path(file_str)
                if file_path.exists():
                    try:
                        target = AnalysisTarget.from_file(file_path)
                        cached_result = await self.cache_manager.get_cached_result(target)
                        if cached_result:
                            results.append(cached_result)
                    except Exception as e:
                        logger.warning(f"Failed to get cached result for {file_path}: {e}")
        
        return results
    
    def get_analysis_statistics(self) -> Dict[str, Any]:
        """Get analysis statistics."""
        if not self.last_snapshot:
            return {"status": "no_previous_analysis"}
        
        return {
            "last_analysis": self.last_snapshot.timestamp,
            "total_files": self.last_snapshot.total_files,
            "total_issues": self.last_snapshot.total_issues,
            "snapshot_age_hours": (time.time() - self.last_snapshot.timestamp) / 3600
        }
