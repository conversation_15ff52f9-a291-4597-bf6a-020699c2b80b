"""
Git Integration for Fast Execution Mode
=======================================

Provides Git integration for detecting changed files and analyzing
only modified content in pre-commit scenarios.
"""

import subprocess
import logging
from pathlib import Path
from typing import List, Optional, Set, Dict, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class ChangeType(Enum):
    """Types of file changes detected by Git."""
    
    ADDED = "A"
    MODIFIED = "M"
    DELETED = "D"
    RENAMED = "R"
    COPIED = "C"
    UNMERGED = "U"


@dataclass
class FileChange:
    """Information about a changed file."""
    
    path: Path
    change_type: ChangeType
    old_path: Optional[Path] = None  # For renamed files
    lines_added: int = 0
    lines_removed: int = 0
    is_binary: bool = False


@dataclass
class FileDiff:
    """Detailed diff information for a file."""
    
    file_path: Path
    old_content: str
    new_content: str
    added_lines: List[int]
    removed_lines: List[int]
    modified_functions: List[str]


class GitChangeDetector:
    """
    Detects changed files using Git commands.
    
    Provides efficient detection of file changes for incremental analysis
    in pre-commit scenarios and fast execution mode.
    """
    
    def __init__(self, repository_path: Optional[Path] = None):
        """
        Initialize Git change detector.
        
        Args:
            repository_path: Path to Git repository (defaults to current directory)
        """
        self.repository_path = repository_path or Path.cwd()
        self._git_available = self._check_git_availability()
    
    def is_git_repository(self, path: Optional[Path] = None) -> bool:
        """
        Check if path is within a Git repository.
        
        Args:
            path: Path to check (defaults to repository_path)
            
        Returns:
            True if path is in a Git repository
        """
        check_path = path or self.repository_path
        
        try:
            result = subprocess.run(
                ["git", "rev-parse", "--git-dir"],
                cwd=check_path,
                capture_output=True,
                text=True,
                timeout=5
            )
            return result.returncode == 0
            
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False
    
    def get_staged_files(self, file_extensions: Optional[Set[str]] = None) -> List[FileChange]:
        """
        Get list of staged files for commit.
        
        Args:
            file_extensions: Optional set of file extensions to filter
            
        Returns:
            List of staged file changes
        """
        if not self._git_available:
            logger.warning("Git not available, cannot detect staged files")
            return []
        
        try:
            # Get staged files with status
            result = subprocess.run(
                ["git", "diff", "--cached", "--name-status"],
                cwd=self.repository_path,
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode != 0:
                logger.error(f"Git command failed: {result.stderr}")
                return []
            
            changes = []
            for line in result.stdout.strip().split('\n'):
                if not line:
                    continue
                
                parts = line.split('\t')
                if len(parts) < 2:
                    continue
                
                status = parts[0]
                file_path = Path(parts[1])
                
                # Filter by file extensions if specified
                if file_extensions and file_path.suffix not in file_extensions:
                    continue
                
                # Handle different change types
                change_type = ChangeType(status[0])
                old_path = None
                
                if change_type == ChangeType.RENAMED and len(parts) >= 3:
                    old_path = Path(parts[1])
                    file_path = Path(parts[2])
                
                change = FileChange(
                    path=file_path,
                    change_type=change_type,
                    old_path=old_path
                )
                
                # Get line count information
                self._add_line_count_info(change)
                
                changes.append(change)
            
            logger.info(f"Found {len(changes)} staged files")
            return changes
            
        except subprocess.TimeoutExpired:
            logger.error("Git command timed out")
            return []
        except Exception as e:
            logger.error(f"Error getting staged files: {e}")
            return []
    
    def get_modified_files(self, base_ref: str = "HEAD", file_extensions: Optional[Set[str]] = None) -> List[FileChange]:
        """
        Get list of modified files compared to base reference.
        
        Args:
            base_ref: Base reference to compare against
            file_extensions: Optional set of file extensions to filter
            
        Returns:
            List of modified file changes
        """
        if not self._git_available:
            logger.warning("Git not available, cannot detect modified files")
            return []
        
        try:
            # Get modified files with status
            result = subprocess.run(
                ["git", "diff", "--name-status", base_ref],
                cwd=self.repository_path,
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode != 0:
                logger.error(f"Git command failed: {result.stderr}")
                return []
            
            changes = []
            for line in result.stdout.strip().split('\n'):
                if not line:
                    continue
                
                parts = line.split('\t')
                if len(parts) < 2:
                    continue
                
                status = parts[0]
                file_path = Path(parts[1])
                
                # Filter by file extensions if specified
                if file_extensions and file_path.suffix not in file_extensions:
                    continue
                
                change_type = ChangeType(status[0])
                old_path = None
                
                if change_type == ChangeType.RENAMED and len(parts) >= 3:
                    old_path = Path(parts[1])
                    file_path = Path(parts[2])
                
                change = FileChange(
                    path=file_path,
                    change_type=change_type,
                    old_path=old_path
                )
                
                # Get line count information
                self._add_line_count_info(change, base_ref)
                
                changes.append(change)
            
            logger.info(f"Found {len(changes)} modified files compared to {base_ref}")
            return changes
            
        except subprocess.TimeoutExpired:
            logger.error("Git command timed out")
            return []
        except Exception as e:
            logger.error(f"Error getting modified files: {e}")
            return []
    
    def get_file_diff(self, file_path: Path, base_ref: str = "HEAD") -> Optional[FileDiff]:
        """
        Get detailed diff for specific file.
        
        Args:
            file_path: Path to file
            base_ref: Base reference to compare against
            
        Returns:
            Detailed diff information or None if failed
        """
        if not self._git_available:
            return None
        
        try:
            # Get old content
            old_content = self._get_file_content_at_ref(file_path, base_ref)
            
            # Get new content
            new_content = ""
            if file_path.exists():
                new_content = file_path.read_text(encoding='utf-8', errors='ignore')
            
            # Get diff with line numbers
            result = subprocess.run(
                ["git", "diff", "--unified=0", base_ref, "--", str(file_path)],
                cwd=self.repository_path,
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode != 0:
                logger.warning(f"Could not get diff for {file_path}")
                return None
            
            # Parse diff to get line numbers
            added_lines, removed_lines = self._parse_diff_line_numbers(result.stdout)
            
            # Detect modified functions (simplified)
            modified_functions = self._detect_modified_functions(old_content, new_content)
            
            return FileDiff(
                file_path=file_path,
                old_content=old_content,
                new_content=new_content,
                added_lines=added_lines,
                removed_lines=removed_lines,
                modified_functions=modified_functions
            )
            
        except Exception as e:
            logger.error(f"Error getting file diff for {file_path}: {e}")
            return None
    
    def get_current_branch(self) -> Optional[str]:
        """
        Get current Git branch name.
        
        Returns:
            Current branch name or None if failed
        """
        if not self._git_available:
            return None
        
        try:
            result = subprocess.run(
                ["git", "rev-parse", "--abbrev-ref", "HEAD"],
                cwd=self.repository_path,
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode == 0:
                return result.stdout.strip()
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting current branch: {e}")
            return None
    
    def _check_git_availability(self) -> bool:
        """Check if Git is available and repository is valid."""
        try:
            result = subprocess.run(
                ["git", "--version"],
                capture_output=True,
                timeout=5
            )
            
            if result.returncode != 0:
                return False
            
            return self.is_git_repository()
            
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False
    
    def _add_line_count_info(self, change: FileChange, base_ref: str = "HEAD") -> None:
        """Add line count information to file change."""
        try:
            if change.change_type == ChangeType.ADDED:
                # For new files, count all lines as added
                if change.path.exists():
                    content = change.path.read_text(encoding='utf-8', errors='ignore')
                    change.lines_added = len(content.splitlines())
                return
            
            if change.change_type == ChangeType.DELETED:
                # For deleted files, count all lines as removed
                old_content = self._get_file_content_at_ref(change.path, base_ref)
                change.lines_removed = len(old_content.splitlines())
                return
            
            # For modified files, get actual diff stats
            result = subprocess.run(
                ["git", "diff", "--numstat", base_ref, "--", str(change.path)],
                cwd=self.repository_path,
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode == 0 and result.stdout.strip():
                parts = result.stdout.strip().split('\t')
                if len(parts) >= 2:
                    try:
                        change.lines_added = int(parts[0]) if parts[0] != '-' else 0
                        change.lines_removed = int(parts[1]) if parts[1] != '-' else 0
                        change.is_binary = parts[0] == '-' and parts[1] == '-'
                    except ValueError:
                        pass
                        
        except Exception as e:
            logger.debug(f"Could not get line count info for {change.path}: {e}")
    
    def _get_file_content_at_ref(self, file_path: Path, ref: str) -> str:
        """Get file content at specific Git reference."""
        try:
            result = subprocess.run(
                ["git", "show", f"{ref}:{file_path}"],
                cwd=self.repository_path,
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                return result.stdout
            
            return ""
            
        except Exception:
            return ""
    
    def _parse_diff_line_numbers(self, diff_output: str) -> tuple[List[int], List[int]]:
        """Parse diff output to extract added and removed line numbers."""
        added_lines = []
        removed_lines = []
        
        for line in diff_output.split('\n'):
            if line.startswith('@@'):
                # Parse hunk header: @@ -old_start,old_count +new_start,new_count @@
                parts = line.split()
                if len(parts) >= 3:
                    try:
                        old_info = parts[1][1:]  # Remove '-'
                        new_info = parts[2][1:]  # Remove '+'
                        
                        if ',' in old_info:
                            old_start, old_count = map(int, old_info.split(','))
                        else:
                            old_start, old_count = int(old_info), 1
                        
                        if ',' in new_info:
                            new_start, new_count = map(int, new_info.split(','))
                        else:
                            new_start, new_count = int(new_info), 1
                        
                        # Add line numbers for removed lines
                        removed_lines.extend(range(old_start, old_start + old_count))
                        
                        # Add line numbers for added lines
                        added_lines.extend(range(new_start, new_start + new_count))
                        
                    except ValueError:
                        continue
        
        return added_lines, removed_lines
    
    def _detect_modified_functions(self, old_content: str, new_content: str) -> List[str]:
        """Detect modified functions (simplified implementation)."""
        # This is a simplified implementation
        # A more sophisticated version would use AST parsing
        
        import re
        
        function_pattern = r'^def\s+(\w+)\s*\('
        
        old_functions = set()
        new_functions = set()
        
        for line in old_content.split('\n'):
            match = re.match(function_pattern, line.strip())
            if match:
                old_functions.add(match.group(1))
        
        for line in new_content.split('\n'):
            match = re.match(function_pattern, line.strip())
            if match:
                new_functions.add(match.group(1))
        
        # Functions that exist in both versions might be modified
        # This is a simplification - real implementation would compare function bodies
        common_functions = old_functions.intersection(new_functions)
        
        return list(common_functions)


# Global Git change detector instance
_git_detector: Optional[GitChangeDetector] = None


def get_git_detector() -> GitChangeDetector:
    """Get the global Git change detector instance."""
    global _git_detector
    if _git_detector is None:
        _git_detector = GitChangeDetector()
    return _git_detector


def initialize_git_integration(repository_path: Optional[Path] = None) -> None:
    """Initialize the global Git integration."""
    global _git_detector
    _git_detector = GitChangeDetector(repository_path)
    logger.info(f"Git integration initialized for repository: {repository_path or Path.cwd()}")
