#!/usr/bin/env python3
"""
Pre-commit Phase 0 Check
========================

This script runs as a pre-commit hook to ensure Phase 0 completion
criteria are maintained during development.

Usage:
    python scripts/pre_commit_phase_0_check.py

Exit codes:
    0: All checks pass
    1: One or more checks fail (blocks commit)
"""

import sys
from pathlib import Path

# Import the main verifier
sys.path.insert(0, str(Path(__file__).parent))
from verify_phase_0_completion import Phase0Verifier


def main():
    """Run Phase 0 verification as pre-commit check."""
    print("🔒 Pre-commit Phase 0 verification...")
    
    project_root = Path(__file__).parent.parent
    verifier = Phase0Verifier(project_root)
    
    # Run only critical checks for pre-commit
    critical_checks = [
        ("Print Statements", verifier.check_print_statements),
        ("Actor System Removal", verifier.check_actor_system_removal),
    ]
    
    all_passed = True
    
    for check_name, check_func in critical_checks:
        try:
            passed, details = check_func()
            
            if passed:
                print(f"✅ {check_name}: PASS")
            else:
                print(f"❌ {check_name}: FAIL")
                for detail in details[:3]:  # Limit output
                    print(f"   {detail}")
                all_passed = False
                
        except Exception as e:
            print(f"❌ {check_name}: ERROR - {e}")
            all_passed = False
    
    if all_passed:
        print("✅ Pre-commit checks passed")
        return 0
    else:
        print("❌ Pre-commit checks failed - fix issues before committing")
        print("💡 Run 'python scripts/verify_phase_0_completion.py' for full verification")
        return 1


if __name__ == "__main__":
    sys.exit(main())
