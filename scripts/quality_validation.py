#!/usr/bin/env python3
"""
Quality Validation Script for Vibe Check
========================================

This script performs comprehensive quality validation across the entire codebase,
implementing the systematic methodology that has proven successful in our roadmap execution.

Features:
- Test infrastructure validation (>90% collection success)
- Constants usage validation
- Terminology consistency checking
- Import organization validation
- Code quality metrics
- Performance regression detection
- Security vulnerability scanning

Usage:
    python3 scripts/quality_validation.py [--fix] [--report]
"""

import argparse
import asyncio
import json
import subprocess
import sys
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# Add project root to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from vibe_check.core.constants import ToolNames, FileExtensions, AnalysisThresholds
from vibe_check.core.constants.terminology import DeprecatedTerms, ComponentTerms
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class QualityValidator:
    """Comprehensive quality validation system."""
    
    def __init__(self, fix_issues: bool = False, generate_report: bool = True):
        self.fix_issues = fix_issues
        self.generate_report = generate_report
        self.results: Dict[str, Dict] = {}
        self.start_time = time.time()
    
    async def run_validation(self) -> Dict[str, Dict]:
        """Run comprehensive quality validation."""
        print("🔍 COMPREHENSIVE QUALITY VALIDATION")
        print("=" * 50)
        
        # Test infrastructure validation
        await self._validate_test_infrastructure()
        
        # Constants usage validation
        await self._validate_constants_usage()
        
        # Terminology consistency validation
        await self._validate_terminology()
        
        # Import organization validation
        await self._validate_imports()
        
        # Code quality metrics
        await self._validate_code_quality()
        
        # Security validation
        await self._validate_security()
        
        # Generate comprehensive report
        if self.generate_report:
            self._generate_report()
        
        return self.results
    
    async def _validate_test_infrastructure(self) -> None:
        """Validate test infrastructure health."""
        print("\n1. 🧪 Test Infrastructure Validation...")
        
        try:
            # Run test collection
            result = subprocess.run([
                sys.executable, '-m', 'pytest', 'tests/', '--collect-only', '-q'
            ], capture_output=True, text=True, timeout=60)
            
            # Count errors and collected tests
            error_lines = [line for line in result.stdout.split('\n') 
                          if 'ERROR' in line and 'collecting' in line]
            collected_lines = [line for line in result.stdout.split('\n') 
                             if 'collected' in line and 'error' in line]
            
            error_count = len(error_lines)
            total_tests = 0
            
            if collected_lines:
                # Extract test count from "X tests collected, Y errors"
                import re
                match = re.search(r'(\d+) tests collected', collected_lines[0])
                if match:
                    total_tests = int(match.group(1))
            
            success_rate = (total_tests / (total_tests + error_count)) * 100 if total_tests > 0 else 0
            
            self.results['test_infrastructure'] = {
                'total_tests': total_tests,
                'collection_errors': error_count,
                'success_rate': success_rate,
                'status': 'PASS' if success_rate >= 90 else 'FAIL',
                'errors': error_lines[:5]  # First 5 errors for analysis
            }
            
            print(f"   📊 Tests collected: {total_tests}")
            print(f"   ❌ Collection errors: {error_count}")
            print(f"   📈 Success rate: {success_rate:.1f}%")
            print(f"   🎯 Status: {'✅ PASS' if success_rate >= 90 else '❌ FAIL'}")
            
        except Exception as e:
            self.results['test_infrastructure'] = {
                'status': 'ERROR',
                'error': str(e)
            }
            print(f"   ❌ Test infrastructure validation failed: {e}")
    
    async def _validate_constants_usage(self) -> None:
        """Validate proper constants usage across codebase."""
        print("\n2. 🔧 Constants Usage Validation...")
        
        violations = []
        hardcoded_patterns = [
            ('"ruff"', 'Use ToolNames.RUFF'),
            ('"mypy"', 'Use ToolNames.MYPY'),
            ('"bandit"', 'Use ToolNames.BANDIT'),
            ('".py"', 'Use FileExtensions.PYTHON'),
            ('88', 'Use AnalysisThresholds.MAX_LINE_LENGTH'),
            ('10', 'Use AnalysisThresholds.CYCLOMATIC_COMPLEXITY'),
        ]
        
        python_files = list(Path('vibe_check').rglob('*.py'))
        
        for file_path in python_files:
            if 'constants' in str(file_path).lower():
                continue  # Skip constants files themselves
                
            try:
                content = file_path.read_text()
                for pattern, message in hardcoded_patterns:
                    if pattern in content:
                        violations.append(f"{file_path}: {message}")
            except Exception:
                continue
        
        self.results['constants_usage'] = {
            'violations': violations[:10],  # First 10 violations
            'total_violations': len(violations),
            'status': 'PASS' if len(violations) < 5 else 'FAIL'
        }
        
        print(f"   📊 Files checked: {len(python_files)}")
        print(f"   ❌ Violations found: {len(violations)}")
        print(f"   🎯 Status: {'✅ PASS' if len(violations) < 5 else '❌ FAIL'}")
    
    async def _validate_terminology(self) -> None:
        """Validate terminology consistency."""
        print("\n3. 🏷️ Terminology Validation...")
        
        violations = []
        deprecated_terms = {
            'analyse': 'analyze',
            'analyser': 'analyzer',
            'configuration': 'config',
        }
        
        python_files = list(Path('vibe_check').rglob('*.py'))
        
        for file_path in python_files:
            try:
                content = file_path.read_text()
                for deprecated, replacement in deprecated_terms.items():
                    if deprecated in content.lower():
                        violations.append(f"{file_path}: Use '{replacement}' instead of '{deprecated}'")
            except Exception:
                continue
        
        self.results['terminology'] = {
            'violations': violations[:10],
            'total_violations': len(violations),
            'status': 'PASS' if len(violations) == 0 else 'WARN'
        }
        
        print(f"   📊 Files checked: {len(python_files)}")
        print(f"   ❌ Violations found: {len(violations)}")
        print(f"   🎯 Status: {'✅ PASS' if len(violations) == 0 else '⚠️ WARN'}")
    
    async def _validate_imports(self) -> None:
        """Validate import organization."""
        print("\n4. 📦 Import Organization Validation...")
        
        violations = []
        python_files = list(Path('vibe_check').rglob('*.py'))
        
        for file_path in python_files:
            try:
                content = file_path.read_text()
                
                # Check for missing typing imports
                if any(pattern in content for pattern in ['Optional[', 'List[', 'Dict[', 'Union[']):
                    if 'from typing import' not in content and 'import typing' not in content:
                        violations.append(f"{file_path}: Missing typing imports")
                        
            except Exception:
                continue
        
        self.results['imports'] = {
            'violations': violations[:10],
            'total_violations': len(violations),
            'status': 'PASS' if len(violations) < 3 else 'FAIL'
        }
        
        print(f"   📊 Files checked: {len(python_files)}")
        print(f"   ❌ Violations found: {len(violations)}")
        print(f"   🎯 Status: {'✅ PASS' if len(violations) < 3 else '❌ FAIL'}")
    
    async def _validate_code_quality(self) -> None:
        """Validate code quality metrics."""
        print("\n5. 📈 Code Quality Validation...")
        
        try:
            # Run basic quality checks
            result = subprocess.run([
                sys.executable, '-m', 'vibe_check', 'analyze', 'vibe_check', 
                '--profile', 'minimal'
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                # Extract quality metrics from output
                lines = result.stdout.split('\n')
                total_issues = 0
                
                for line in lines:
                    if 'Total issues found:' in line:
                        import re
                        match = re.search(r'(\d+)', line)
                        if match:
                            total_issues = int(match.group(1))
                
                self.results['code_quality'] = {
                    'total_issues': total_issues,
                    'status': 'PASS' if total_issues < 1000 else 'WARN'
                }
                
                print(f"   📊 Total issues: {total_issues}")
                print(f"   🎯 Status: {'✅ PASS' if total_issues < 1000 else '⚠️ WARN'}")
            else:
                self.results['code_quality'] = {
                    'status': 'ERROR',
                    'error': result.stderr[:200]
                }
                print(f"   ❌ Quality analysis failed")
                
        except Exception as e:
            self.results['code_quality'] = {
                'status': 'ERROR',
                'error': str(e)
            }
            print(f"   ❌ Code quality validation failed: {e}")
    
    async def _validate_security(self) -> None:
        """Validate security standards."""
        print("\n6. 🔒 Security Validation...")
        
        try:
            # Run bandit security scan
            result = subprocess.run([
                sys.executable, '-m', 'bandit', '-r', 'vibe_check', 
                '-f', 'json', '-o', 'bandit-report.json'
            ], capture_output=True, text=True, timeout=60)
            
            # Parse bandit results
            if Path('bandit-report.json').exists():
                with open('bandit-report.json') as f:
                    bandit_data = json.load(f)
                
                high_severity = len([r for r in bandit_data.get('results', []) 
                                   if r.get('issue_severity') == 'HIGH'])
                medium_severity = len([r for r in bandit_data.get('results', []) 
                                     if r.get('issue_severity') == 'MEDIUM'])
                
                self.results['security'] = {
                    'high_severity_issues': high_severity,
                    'medium_severity_issues': medium_severity,
                    'status': 'PASS' if high_severity == 0 else 'FAIL'
                }
                
                print(f"   📊 High severity issues: {high_severity}")
                print(f"   📊 Medium severity issues: {medium_severity}")
                print(f"   🎯 Status: {'✅ PASS' if high_severity == 0 else '❌ FAIL'}")
            else:
                self.results['security'] = {
                    'status': 'WARN',
                    'message': 'Bandit report not generated'
                }
                print(f"   ⚠️ Security scan incomplete")
                
        except Exception as e:
            self.results['security'] = {
                'status': 'ERROR',
                'error': str(e)
            }
            print(f"   ❌ Security validation failed: {e}")
    
    def _generate_report(self) -> None:
        """Generate comprehensive quality report."""
        print("\n📊 COMPREHENSIVE QUALITY REPORT")
        print("=" * 50)
        
        total_time = time.time() - self.start_time
        
        # Calculate overall status
        statuses = [result.get('status', 'UNKNOWN') for result in self.results.values()]
        overall_status = 'PASS'
        if 'FAIL' in statuses:
            overall_status = 'FAIL'
        elif 'ERROR' in statuses:
            overall_status = 'ERROR'
        elif 'WARN' in statuses:
            overall_status = 'WARN'
        
        print(f"\n🎯 OVERALL STATUS: {overall_status}")
        print(f"⏱️ Total validation time: {total_time:.2f}s")
        
        # Detailed results
        for category, result in self.results.items():
            status = result.get('status', 'UNKNOWN')
            emoji = {'PASS': '✅', 'FAIL': '❌', 'WARN': '⚠️', 'ERROR': '💥'}.get(status, '❓')
            print(f"{emoji} {category.replace('_', ' ').title()}: {status}")
        
        # Save detailed report
        report_data = {
            'timestamp': time.time(),
            'overall_status': overall_status,
            'validation_time': total_time,
            'results': self.results
        }
        
        with open('quality_validation_report.json', 'w') as f:
            json.dump(report_data, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: quality_validation_report.json")


async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='Comprehensive quality validation for Vibe Check')
    parser.add_argument('--fix', action='store_true', help='Attempt to fix issues automatically')
    parser.add_argument('--report', action='store_true', default=True, help='Generate detailed report')
    
    args = parser.parse_args()
    
    validator = QualityValidator(fix_issues=args.fix, generate_report=args.report)
    results = await validator.run_validation()
    
    # Exit with appropriate code
    statuses = [result.get('status', 'UNKNOWN') for result in results.values()]
    if 'FAIL' in statuses or 'ERROR' in statuses:
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == '__main__':
    asyncio.run(main())
