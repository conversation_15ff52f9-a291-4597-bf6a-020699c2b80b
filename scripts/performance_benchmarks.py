#!/usr/bin/env python3
"""
Performance Benchmarking Suite
==============================

Establishes performance baselines for Vibe Check:
- Startup time measurement
- Memory usage profiling
- Analysis speed benchmarking
- Resource utilization tracking
"""

import time
import psutil
import os
import sys
import asyncio
import subprocess
import json
import tracemalloc
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from contextlib import contextmanager
import argparse


@dataclass
class PerformanceMetrics:
    """Performance metrics data structure"""
    startup_time: float
    memory_usage_mb: float
    peak_memory_mb: float
    cpu_percent: float
    analysis_time: float
    files_per_second: float
    lines_per_second: float
    timestamp: float


class PerformanceBenchmark:
    """Main performance benchmarking class"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.results = []
        self.baseline_metrics = None
        
    @contextmanager
    def memory_profiler(self):
        """Context manager for memory profiling"""
        tracemalloc.start()
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        try:
            yield
        finally:
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc.stop()
            
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            self.peak_memory = peak / 1024 / 1024  # MB
            self.memory_usage = final_memory - initial_memory
    
    def measure_startup_time(self) -> float:
        """Measure application startup time"""
        print("📏 Measuring startup time...")
        
        startup_times = []
        
        # Test startup time multiple times for accuracy
        for i in range(5):
            start_time = time.time()
            
            # Simulate importing the main vibe_check module
            try:
                result = subprocess.run([
                    sys.executable, '-c', 
                    'import time; start=time.time(); import vibe_check; print(f"Import time: {time.time()-start:.3f}s")'
                ], capture_output=True, text=True, timeout=30)
                
                if result.returncode == 0:
                    # Extract import time from output
                    output_lines = result.stdout.strip().split('\n')
                    for line in output_lines:
                        if 'Import time:' in line:
                            import_time = float(line.split(':')[1].strip().rstrip('s'))
                            startup_times.append(import_time)
                            break
                else:
                    # Fallback: measure subprocess time
                    subprocess_time = time.time() - start_time
                    startup_times.append(subprocess_time)
                    
            except subprocess.TimeoutExpired:
                print(f"  ⚠️  Startup test {i+1} timed out")
                startup_times.append(30.0)  # Timeout value
            except Exception as e:
                print(f"  ⚠️  Startup test {i+1} failed: {e}")
                # Fallback measurement
                end_time = time.time()
                startup_times.append(end_time - start_time)
        
        avg_startup_time = sum(startup_times) / len(startup_times)
        print(f"  ✅ Average startup time: {avg_startup_time:.3f}s")
        print(f"  📊 Individual times: {[f'{t:.3f}s' for t in startup_times]}")
        
        return avg_startup_time
    
    def measure_memory_usage(self) -> Dict[str, float]:
        """Measure memory usage patterns"""
        print("💾 Measuring memory usage...")
        
        process = psutil.Process()
        
        # Initial memory
        initial_memory = process.memory_info()
        
        memory_metrics = {
            'initial_rss_mb': initial_memory.rss / 1024 / 1024,
            'initial_vms_mb': initial_memory.vms / 1024 / 1024,
        }
        
        # Simulate typical usage
        with self.memory_profiler():
            try:
                # Import and use vibe_check components
                import vibe_check
                
                # Simulate analysis (if possible)
                if hasattr(vibe_check, 'analyze_project'):
                    # Create a small test project
                    test_dir = self.project_root / 'test_memory_project'
                    test_dir.mkdir(exist_ok=True)
                    
                    # Create test files
                    for i in range(10):
                        test_file = test_dir / f'test_{i}.py'
                        test_file.write_text(f'''
def test_function_{i}():
    """Test function {i}"""
    result = []
    for j in range(100):
        result.append(j * {i})
    return result

class TestClass_{i}:
    def __init__(self):
        self.data = list(range(100))
    
    def process(self):
        return sum(self.data)
''')
                    
                    # Run analysis
                    try:
                        result = vibe_check.analyze_project(str(test_dir))
                        print(f"  ✅ Analyzed {len(list(test_dir.glob('*.py')))} test files")
                    except Exception as e:
                        print(f"  ⚠️  Analysis failed: {e}")
                    
                    # Cleanup
                    import shutil
                    shutil.rmtree(test_dir, ignore_errors=True)
                
            except ImportError as e:
                print(f"  ⚠️  Could not import vibe_check: {e}")
        
        # Final memory
        final_memory = process.memory_info()
        
        memory_metrics.update({
            'final_rss_mb': final_memory.rss / 1024 / 1024,
            'final_vms_mb': final_memory.vms / 1024 / 1024,
            'peak_memory_mb': getattr(self, 'peak_memory', 0),
            'memory_increase_mb': getattr(self, 'memory_usage', 0),
        })
        
        print(f"  ✅ Initial RSS: {memory_metrics['initial_rss_mb']:.1f} MB")
        print(f"  ✅ Final RSS: {memory_metrics['final_rss_mb']:.1f} MB")
        print(f"  ✅ Peak memory: {memory_metrics['peak_memory_mb']:.1f} MB")
        print(f"  ✅ Memory increase: {memory_metrics['memory_increase_mb']:.1f} MB")
        
        return memory_metrics
    
    def measure_analysis_speed(self, project_path: str) -> Dict[str, float]:
        """Measure code analysis performance"""
        print(f"⚡ Measuring analysis speed on: {project_path}")
        
        target_path = Path(project_path)
        if not target_path.exists():
            print(f"  ⚠️  Path does not exist: {project_path}")
            return {'analysis_time': 0, 'files_per_second': 0, 'lines_per_second': 0}
        
        # Count files and lines
        python_files = list(target_path.rglob("*.py"))
        total_lines = 0
        
        for file_path in python_files[:100]:  # Limit to first 100 files for speed
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    total_lines += len(f.readlines())
            except:
                pass
        
        print(f"  📊 Found {len(python_files)} Python files, {total_lines} lines")
        
        # Measure analysis time
        start_time = time.time()
        
        try:
            # Try to run actual analysis
            result = subprocess.run([
                sys.executable, '-c', f'''
import time
start = time.time()
try:
    import vibe_check
    result = vibe_check.analyze_project("{project_path}")
    print(f"Analysis completed in {{time.time() - start:.3f}}s")
except Exception as e:
    print(f"Analysis failed: {{e}}")
    print(f"Simulated analysis time: {{time.time() - start:.3f}}s")
'''
            ], capture_output=True, text=True, timeout=120)
            
            # Extract analysis time from output
            analysis_time = time.time() - start_time
            if result.stdout:
                for line in result.stdout.split('\n'):
                    if 'completed in' in line or 'analysis time:' in line:
                        try:
                            time_str = line.split()[-1].rstrip('s')
                            analysis_time = float(time_str)
                            break
                        except:
                            pass
            
        except subprocess.TimeoutExpired:
            analysis_time = 120.0  # Timeout
            print(f"  ⚠️  Analysis timed out after 120s")
        except Exception as e:
            analysis_time = time.time() - start_time
            print(f"  ⚠️  Analysis error: {e}")
        
        # Calculate performance metrics
        files_per_second = len(python_files) / analysis_time if analysis_time > 0 else 0
        lines_per_second = total_lines / analysis_time if analysis_time > 0 else 0
        
        metrics = {
            'analysis_time': analysis_time,
            'files_analyzed': len(python_files),
            'lines_analyzed': total_lines,
            'files_per_second': files_per_second,
            'lines_per_second': lines_per_second,
        }
        
        print(f"  ✅ Analysis time: {analysis_time:.3f}s")
        print(f"  ✅ Files per second: {files_per_second:.1f}")
        print(f"  ✅ Lines per second: {lines_per_second:.0f}")
        
        return metrics
    
    def measure_cpu_usage(self) -> float:
        """Measure CPU usage during operation"""
        print("🖥️  Measuring CPU usage...")
        
        process = psutil.Process()
        cpu_measurements = []
        
        # Measure CPU over 10 seconds
        for i in range(10):
            cpu_percent = process.cpu_percent(interval=1)
            cpu_measurements.append(cpu_percent)
        
        avg_cpu = sum(cpu_measurements) / len(cpu_measurements)
        print(f"  ✅ Average CPU usage: {avg_cpu:.1f}%")
        
        return avg_cpu
    
    def run_comprehensive_benchmark(self, analysis_path: str = None) -> PerformanceMetrics:
        """Run comprehensive performance benchmark"""
        print("🚀 Running Comprehensive Performance Benchmark")
        print("=" * 50)
        
        # Use current directory if no analysis path provided
        if analysis_path is None:
            analysis_path = str(self.project_root / "vibe_check")
        
        # Measure startup time
        startup_time = self.measure_startup_time()
        
        # Measure memory usage
        memory_metrics = self.measure_memory_usage()
        
        # Measure analysis speed
        analysis_metrics = self.measure_analysis_speed(analysis_path)
        
        # Measure CPU usage
        cpu_usage = self.measure_cpu_usage()
        
        # Create comprehensive metrics
        metrics = PerformanceMetrics(
            startup_time=startup_time,
            memory_usage_mb=memory_metrics.get('memory_increase_mb', 0),
            peak_memory_mb=memory_metrics.get('peak_memory_mb', 0),
            cpu_percent=cpu_usage,
            analysis_time=analysis_metrics.get('analysis_time', 0),
            files_per_second=analysis_metrics.get('files_per_second', 0),
            lines_per_second=analysis_metrics.get('lines_per_second', 0),
            timestamp=time.time()
        )
        
        self.results.append(metrics)
        
        return metrics
    
    def establish_baseline(self, analysis_path: str = None) -> Dict[str, Any]:
        """Establish performance baseline"""
        print("📊 Establishing Performance Baseline")
        print("=" * 40)
        
        # Run multiple benchmark iterations
        iterations = 3
        all_metrics = []
        
        for i in range(iterations):
            print(f"\n🔄 Iteration {i+1}/{iterations}")
            metrics = self.run_comprehensive_benchmark(analysis_path)
            all_metrics.append(metrics)
            
            # Brief pause between iterations
            time.sleep(2)
        
        # Calculate averages
        avg_metrics = {
            'startup_time': sum(m.startup_time for m in all_metrics) / len(all_metrics),
            'memory_usage_mb': sum(m.memory_usage_mb for m in all_metrics) / len(all_metrics),
            'peak_memory_mb': sum(m.peak_memory_mb for m in all_metrics) / len(all_metrics),
            'cpu_percent': sum(m.cpu_percent for m in all_metrics) / len(all_metrics),
            'analysis_time': sum(m.analysis_time for m in all_metrics) / len(all_metrics),
            'files_per_second': sum(m.files_per_second for m in all_metrics) / len(all_metrics),
            'lines_per_second': sum(m.lines_per_second for m in all_metrics) / len(all_metrics),
        }
        
        # System information
        system_info = {
            'cpu_count': psutil.cpu_count(),
            'memory_total_gb': psutil.virtual_memory().total / 1024 / 1024 / 1024,
            'python_version': sys.version,
            'platform': sys.platform,
        }
        
        baseline = {
            'baseline_metrics': avg_metrics,
            'individual_runs': [asdict(m) for m in all_metrics],
            'system_info': system_info,
            'timestamp': time.time(),
            'iterations': iterations
        }
        
        self.baseline_metrics = baseline
        
        return baseline
    
    def generate_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        if not self.baseline_metrics:
            return {'error': 'No baseline metrics available'}
        
        baseline = self.baseline_metrics['baseline_metrics']
        
        # Performance targets (goals for optimization)
        targets = {
            'startup_time': 2.0,  # seconds
            'memory_usage_mb': 512,  # MB
            'analysis_time_per_1000_lines': 1.0,  # seconds
            'files_per_second': 50,  # files/sec
        }
        
        # Calculate performance scores (0-10)
        scores = {}
        scores['startup_score'] = max(0, 10 - (baseline['startup_time'] / targets['startup_time']) * 5)
        scores['memory_score'] = max(0, 10 - (baseline['memory_usage_mb'] / targets['memory_usage_mb']) * 5)
        scores['speed_score'] = min(10, (baseline['files_per_second'] / targets['files_per_second']) * 10)
        scores['overall_score'] = sum(scores.values()) / len(scores)
        
        report = {
            'summary': {
                'overall_score': scores['overall_score'],
                'startup_time': baseline['startup_time'],
                'memory_usage_mb': baseline['memory_usage_mb'],
                'analysis_speed_files_per_sec': baseline['files_per_second'],
                'cpu_usage_percent': baseline['cpu_percent'],
            },
            'detailed_metrics': baseline,
            'performance_scores': scores,
            'targets': targets,
            'recommendations': self._generate_recommendations(baseline, targets),
            'system_info': self.baseline_metrics['system_info'],
            'timestamp': time.time()
        }
        
        return report
    
    def _generate_recommendations(self, baseline: Dict, targets: Dict) -> List[str]:
        """Generate performance optimization recommendations"""
        recommendations = []
        
        if baseline['startup_time'] > targets['startup_time']:
            recommendations.append(f"🚀 Optimize startup time: {baseline['startup_time']:.2f}s > {targets['startup_time']}s target")
        
        if baseline['memory_usage_mb'] > targets['memory_usage_mb']:
            recommendations.append(f"💾 Reduce memory usage: {baseline['memory_usage_mb']:.1f}MB > {targets['memory_usage_mb']}MB target")
        
        if baseline['files_per_second'] < targets['files_per_second']:
            recommendations.append(f"⚡ Improve analysis speed: {baseline['files_per_second']:.1f} < {targets['files_per_second']} files/sec target")
        
        if baseline['cpu_percent'] > 80:
            recommendations.append(f"🖥️  Optimize CPU usage: {baseline['cpu_percent']:.1f}% is high")
        
        if not recommendations:
            recommendations.append("✅ Performance is within acceptable targets")
        
        return recommendations


def main():
    """Main benchmarking function"""
    parser = argparse.ArgumentParser(description="Performance benchmarking for Vibe Check")
    parser.add_argument("--project-root", default=".", help="Project root directory")
    parser.add_argument("--analysis-path", help="Path to analyze for speed testing")
    parser.add_argument("--output", default="performance_baseline.json", help="Output report file")
    
    args = parser.parse_args()
    
    benchmark = PerformanceBenchmark(args.project_root)
    
    # Establish baseline
    baseline = benchmark.establish_baseline(args.analysis_path)
    
    # Generate report
    report = benchmark.generate_performance_report()
    
    # Display summary
    print("\n" + "=" * 50)
    print("📊 PERFORMANCE BASELINE SUMMARY")
    print("=" * 50)
    print(f"Overall Score: {report['summary']['overall_score']:.1f}/10")
    print(f"Startup Time: {report['summary']['startup_time']:.3f}s")
    print(f"Memory Usage: {report['summary']['memory_usage_mb']:.1f}MB")
    print(f"Analysis Speed: {report['summary']['analysis_speed_files_per_sec']:.1f} files/sec")
    print(f"CPU Usage: {report['summary']['cpu_usage_percent']:.1f}%")
    
    print(f"\n💡 RECOMMENDATIONS:")
    for rec in report['recommendations']:
        print(f"  {rec}")
    
    # Save report
    with open(args.output, 'w') as f:
        json.dump(report, f, indent=2)
    print(f"\n📄 Detailed report saved to: {args.output}")


if __name__ == "__main__":
    main()
