#!/usr/bin/env python3
"""
Comprehensive Dependency Analysis Tool
======================================

Analyzes dependency relationships, detects circular dependencies,
and provides optimization recommendations for the Vibe Check codebase.
"""

import ast
import os
import sys
import json
import networkx as nx
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
from collections import defaultdict, Counter
import argparse


class DependencyAnalyzer:
    """Comprehensive dependency analyzer"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.dependency_graph = nx.DiGraph()
        self.module_imports = defaultdict(set)
        self.import_counts = Counter()
        self.circular_dependencies = []
        self.heavy_modules = []
        self.unused_modules = []
        
    def analyze_file(self, file_path: Path) -> Dict[str, Set[str]]:
        """Analyze imports in a single Python file"""
        imports = {'direct': set(), 'from': set()}
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports['direct'].add(alias.name)
                        self.import_counts[alias.name] += 1
                
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        imports['from'].add(node.module)
                        self.import_counts[node.module] += 1
                        
                        # Also count individual imports
                        for alias in node.names:
                            full_name = f"{node.module}.{alias.name}"
                            self.import_counts[full_name] += 1
        
        except Exception as e:
            print(f"Error analyzing {file_path}: {e}")
        
        return imports
    
    def build_dependency_graph(self):
        """Build the complete dependency graph"""
        print("🔍 Building dependency graph...")
        
        python_files = list(self.project_root.rglob("*.py"))
        print(f"Found {len(python_files)} Python files")
        
        for file_path in python_files:
            # Skip __pycache__ and other generated files
            if "__pycache__" in str(file_path) or ".pyc" in str(file_path):
                continue
            
            # Get relative module path
            try:
                rel_path = file_path.relative_to(self.project_root)
                module_name = str(rel_path).replace('/', '.').replace('\\', '.').replace('.py', '')
                
                # Analyze imports
                imports = self.analyze_file(file_path)
                self.module_imports[module_name] = imports
                
                # Add to graph
                self.dependency_graph.add_node(module_name)
                
                # Add edges for dependencies
                for imp in imports['direct'] | imports['from']:
                    # Only add internal dependencies (within project)
                    if imp.startswith('vibe_check') or imp.startswith('.'):
                        self.dependency_graph.add_edge(module_name, imp)
                
            except Exception as e:
                print(f"Error processing {file_path}: {e}")
    
    def detect_circular_dependencies(self) -> List[List[str]]:
        """Detect circular dependencies in the graph"""
        print("🔄 Detecting circular dependencies...")
        
        try:
            cycles = list(nx.simple_cycles(self.dependency_graph))
            self.circular_dependencies = cycles
            
            if cycles:
                print(f"Found {len(cycles)} circular dependencies:")
                for i, cycle in enumerate(cycles[:5]):  # Show first 5
                    print(f"  {i+1}. {' -> '.join(cycle)} -> {cycle[0]}")
                if len(cycles) > 5:
                    print(f"  ... and {len(cycles) - 5} more")
            else:
                print("✅ No circular dependencies found!")
                
        except Exception as e:
            print(f"Error detecting cycles: {e}")
            
        return self.circular_dependencies
    
    def identify_heavy_modules(self, threshold: int = 10) -> List[Tuple[str, int]]:
        """Identify modules with many dependencies"""
        print(f"📦 Identifying heavy modules (>{threshold} dependencies)...")
        
        heavy = []
        for module, imports in self.module_imports.items():
            total_imports = len(imports['direct']) + len(imports['from'])
            if total_imports > threshold:
                heavy.append((module, total_imports))
        
        # Sort by dependency count
        heavy.sort(key=lambda x: x[1], reverse=True)
        self.heavy_modules = heavy
        
        if heavy:
            print(f"Found {len(heavy)} heavy modules:")
            for module, count in heavy[:10]:  # Show top 10
                print(f"  • {module}: {count} dependencies")
        else:
            print("✅ No heavy modules found!")
        
        return heavy
    
    def find_unused_modules(self) -> List[str]:
        """Find modules that are never imported"""
        print("🗑️  Finding unused modules...")
        
        all_modules = set(self.module_imports.keys())
        imported_modules = set()
        
        # Collect all imported modules
        for imports in self.module_imports.values():
            imported_modules.update(imports['direct'])
            imported_modules.update(imports['from'])
        
        # Find modules that exist but are never imported
        unused = []
        for module in all_modules:
            # Check if this module is imported by any other module
            is_imported = False
            for imp in imported_modules:
                if module in imp or imp.startswith(module):
                    is_imported = True
                    break
            
            if not is_imported and not module.endswith('__main__'):
                unused.append(module)
        
        self.unused_modules = unused
        
        if unused:
            print(f"Found {len(unused)} potentially unused modules:")
            for module in unused[:10]:  # Show first 10
                print(f"  • {module}")
        else:
            print("✅ No unused modules found!")
        
        return unused
    
    def analyze_import_patterns(self) -> Dict[str, int]:
        """Analyze import patterns and frequency"""
        print("📊 Analyzing import patterns...")
        
        # Get most common imports
        common_imports = self.import_counts.most_common(20)
        
        print("Most frequently imported modules:")
        for module, count in common_imports:
            print(f"  • {module}: {count} times")
        
        return dict(common_imports)
    
    def generate_optimization_recommendations(self) -> List[str]:
        """Generate optimization recommendations"""
        recommendations = []
        
        # Circular dependency recommendations
        if self.circular_dependencies:
            recommendations.append(
                f"🔄 Fix {len(self.circular_dependencies)} circular dependencies by refactoring imports"
            )
        
        # Heavy module recommendations
        if self.heavy_modules:
            heavy_count = len([m for m, c in self.heavy_modules if c > 20])
            if heavy_count > 0:
                recommendations.append(
                    f"📦 Refactor {heavy_count} heavy modules with >20 dependencies"
                )
        
        # Unused module recommendations
        if self.unused_modules:
            recommendations.append(
                f"🗑️  Consider removing {len(self.unused_modules)} unused modules"
            )
        
        # Import consolidation recommendations
        common_imports = self.import_counts.most_common(10)
        frequently_imported = [m for m, c in common_imports if c > 10]
        if frequently_imported:
            recommendations.append(
                f"🔗 Consider creating utility modules for {len(frequently_imported)} frequently imported modules"
            )
        
        return recommendations
    
    def calculate_dependency_health_score(self) -> float:
        """Calculate overall dependency health score (0-10)"""
        score = 10.0
        
        # Penalize circular dependencies
        if self.circular_dependencies:
            score -= min(len(self.circular_dependencies) * 0.5, 3.0)
        
        # Penalize heavy modules
        heavy_penalty = len([m for m, c in self.heavy_modules if c > 30]) * 0.3
        score -= min(heavy_penalty, 2.0)
        
        # Penalize unused modules
        unused_penalty = len(self.unused_modules) * 0.1
        score -= min(unused_penalty, 1.0)
        
        # Penalize excessive imports
        total_modules = len(self.module_imports)
        if total_modules > 0:
            avg_imports = sum(len(imp['direct']) + len(imp['from']) 
                            for imp in self.module_imports.values()) / total_modules
            if avg_imports > 15:
                score -= min((avg_imports - 15) * 0.1, 2.0)
        
        return max(0.0, score)
    
    def export_graph(self, output_path: str = "dependency_graph.json"):
        """Export dependency graph for visualization"""
        graph_data = {
            'nodes': [{'id': node, 'label': node} for node in self.dependency_graph.nodes()],
            'edges': [{'source': u, 'target': v} for u, v in self.dependency_graph.edges()],
            'stats': {
                'total_nodes': self.dependency_graph.number_of_nodes(),
                'total_edges': self.dependency_graph.number_of_edges(),
                'circular_dependencies': len(self.circular_dependencies),
                'heavy_modules': len(self.heavy_modules),
                'unused_modules': len(self.unused_modules)
            }
        }
        
        with open(output_path, 'w') as f:
            json.dump(graph_data, f, indent=2)
        
        print(f"📊 Dependency graph exported to: {output_path}")
    
    def generate_report(self) -> Dict:
        """Generate comprehensive dependency analysis report"""
        health_score = self.calculate_dependency_health_score()
        recommendations = self.generate_optimization_recommendations()
        
        report = {
            'summary': {
                'total_modules': len(self.module_imports),
                'total_dependencies': self.dependency_graph.number_of_edges(),
                'circular_dependencies': len(self.circular_dependencies),
                'heavy_modules': len(self.heavy_modules),
                'unused_modules': len(self.unused_modules),
                'health_score': health_score
            },
            'circular_dependencies': self.circular_dependencies,
            'heavy_modules': self.heavy_modules[:20],  # Top 20
            'unused_modules': self.unused_modules,
            'common_imports': self.import_counts.most_common(20),
            'recommendations': recommendations
        }
        
        return report


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Analyze project dependencies")
    parser.add_argument("--project-root", default=".", help="Project root directory")
    parser.add_argument("--output", default="dependency_analysis_report.json", help="Output report file")
    parser.add_argument("--export-graph", action="store_true", help="Export dependency graph")
    
    args = parser.parse_args()
    
    print("🔍 Vibe Check Dependency Analysis")
    print("=" * 40)
    
    analyzer = DependencyAnalyzer(args.project_root)
    
    # Run analysis
    analyzer.build_dependency_graph()
    analyzer.detect_circular_dependencies()
    analyzer.identify_heavy_modules()
    analyzer.find_unused_modules()
    analyzer.analyze_import_patterns()
    
    # Generate report
    report = analyzer.generate_report()
    
    # Display summary
    print("\n📊 ANALYSIS SUMMARY")
    print("=" * 40)
    print(f"Total modules: {report['summary']['total_modules']}")
    print(f"Total dependencies: {report['summary']['total_dependencies']}")
    print(f"Circular dependencies: {report['summary']['circular_dependencies']}")
    print(f"Heavy modules: {report['summary']['heavy_modules']}")
    print(f"Unused modules: {report['summary']['unused_modules']}")
    print(f"Health score: {report['summary']['health_score']:.1f}/10")
    
    # Show recommendations
    if report['recommendations']:
        print(f"\n💡 RECOMMENDATIONS")
        print("=" * 40)
        for rec in report['recommendations']:
            print(f"  {rec}")
    
    # Save report
    with open(args.output, 'w') as f:
        json.dump(report, f, indent=2)
    print(f"\n📄 Detailed report saved to: {args.output}")
    
    # Export graph if requested
    if args.export_graph:
        analyzer.export_graph()


if __name__ == "__main__":
    main()
