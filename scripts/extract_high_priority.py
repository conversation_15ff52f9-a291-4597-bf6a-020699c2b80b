#!/usr/bin/env python3
"""
High-Priority File Extractor for PAT

This script extracts the files with the most critical issues from the PAT output
and generates focused prompts for them. It ranks files based on a priority score
calculated from various metrics like complexity, security issues, etc.

Usage:
    python extract_high_priority.py <pat_output_dir> [--top N] [--min-score SCORE]

Arguments:
    pat_output_dir: Path to the PAT output directory
    --top N: Number of top files to extract (default: 10)
    --min-score: Minimum priority score to include a file (default: 5)
    --output-dir: Directory to save the high-priority prompts (default: high_priority_prompts)
"""

import argparse
import json
import os
import shutil
import sys
from pathlib import Path
from typing import Any, Dict, List, Tuple

# Priority weights for different issue types
PRIORITY_WEIGHTS = {
    "complexity": 1.0,        # Weight for cyclomatic complexity
    "lines": 0.01,            # Weight for lines of code
    "security": 5.0,          # Weight for security issues
    "type_errors": 2.0,       # Weight for type errors
    "lint_issues": 1.0,       # Weight for lint issues
    "circular_imports": 3.0,  # Weight for circular imports
    "many_imports": 0.5,      # Weight for excessive imports
    "low_coverage": 1.0,      # Weight for low test coverage
}

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Extract high-priority files from PAT output")
    parser.add_argument("pat_output_dir", help="Path to the PAT output directory")
    parser.add_argument("--top", type=int, default=10, help="Number of top files to extract (default: 10)")
    parser.add_argument("--min-score", type=float, default=5.0, help="Minimum priority score to include a file (default: 5)")
    parser.add_argument("--output-dir", default="high_priority_prompts", help="Directory to save the high-priority prompts")
    return parser.parse_args()


    def _helper_load_pat_json(self, *args, **kwargs):
        """Helper function to reduce complexity of load_pat_json"""
        # TODO: Extract complex logic here
        pass

def load_pat_json(pat_output_dir: Path) -> Dict[str, Any]:
    """Load the PAT JSON output file."""
    # Find the JSON report file
    json_files = list(pat_output_dir.glob("PAT_*_analysis.json"))
    if not json_files:
        raise FileNotFoundError(f"No PAT JSON report found in {pat_output_dir}")

    print(f"Loading PAT JSON from {json_files[0]}")

    # Load the JSON file
    with open(json_files[0], 'r') as f:
        data = json.load(f)

    # Check if the data is in the expected format
    if isinstance(data, dict) and "files" in data and isinstance(data["files"], list):
        print(f"Found {len(data['files'])} files in standard format")
        return data

    # If not, try to parse it differently
    print(f"Warning: PAT JSON format is not as expected. Attempting to parse differently.")

    # Create a structured format
    structured_data = {"files": []}

    # If data is a list, assume it's a list of file metrics
    if isinstance(data, list):
        print(f"Data is a list with {len(data)} items")
        structured_data["files"] = data
    # If data is a dict without a 'files' key, assume it's a single file's metrics
    elif isinstance(data, dict):
        print(f"Data is a dict with keys: {list(data.keys())[:10]}...")

        # Check if it has file metrics keys
        if any(key in data for key in ["path", "complexity", "lines", "imports"]):
            structured_data["files"] = [data]
        # Check if it has a 'metrics' key
        elif "metrics" in data and isinstance(data["metrics"], dict):
            print(f"Found 'metrics' key with {len(data['metrics'])} items")
            for path, metrics in data["metrics"].items():
                if isinstance(metrics, dict):
                    metrics["path"] = path
                    structured_data["files"].append(metrics)
        # Check if it has a 'file_metrics' key
        elif "file_metrics" in data and isinstance(data["file_metrics"], list):
            print(f"Found 'file_metrics' key with {len(data['file_metrics'])} items")
            structured_data["files"] = data["file_metrics"]
        # Check if it has a 'files' key with a different structure
        elif "files" in data and not isinstance(data["files"], list):
            print(f"Found 'files' key with non-list type: {type(data['files'])}")
            if isinstance(data["files"], dict):
                for path, file_data in data["files"].items():
                    if isinstance(file_data, dict):
                        file_data["path"] = path
                        structured_data["files"].append(file_data)
        # Try to extract file metrics from the PAT format
        elif all(key in data for key in ["project_name", "timestamp", "files"]):
            print(f"Found PAT format with {len(data['files'])} files")
            for file_info in data["files"]:
                if isinstance(file_info, dict) and "path" in file_info:
                    # Extract metrics from the file info
                    file_metrics = {
                        "path": file_info["path"],
                        "complexity": file_info.get("complexity", 0),
                        "lines": file_info.get("lines", 0),
                        "imports": file_info.get("imports", []),
                        "classes": file_info.get("classes", []),
                        "functions": file_info.get("functions", []),
                        "tool_results": {}
                    }

                    # Extract tool results if available
                    if "tool_results" in file_info and isinstance(file_info["tool_results"], dict):
                        file_metrics["tool_results"] = file_info["tool_results"]

                    structured_data["files"].append(file_metrics)
        # Otherwise, try to extract file metrics from the dict
        else:
            for key, value in data.items():
                if isinstance(value, dict) and any(k in value for k in ["path", "complexity", "lines", "imports"]):
                    file_metrics = value.copy()
                    file_metrics["path"] = key
                    structured_data["files"].append(file_metrics)

    print(f"Extracted {len(structured_data['files'])} file metrics")
    return structured_data

    def _helper_calculate_priority_score(self, *args, **kwargs):
        """Helper function to reduce complexity of calculate_priority_score"""
        # TODO: Extract complex logic here
        pass


def calculate_priority_score(file_metrics: Dict[str, Any]) -> float:
    """Calculate a priority score for a file based on its metrics."""
    score = 0.0

    # Add score for complexity
    complexity = file_metrics.get("complexity", 0)
    if isinstance(complexity, (int, float)) and complexity > 10:
        score += PRIORITY_WEIGHTS["complexity"] * (complexity - 10)

    # Add score for lines of code
    lines = file_metrics.get("lines", 0)
    if isinstance(lines, (int, float)) and lines > 600:
        score += PRIORITY_WEIGHTS["lines"] * (lines - 600)

    # Add score for security issues
    tool_results = file_metrics.get("tool_results", {})
    if isinstance(tool_results, dict):
        if "bandit" in tool_results and isinstance(tool_results["bandit"], dict) and tool_results["bandit"].get("issues"):
            security_issues = len(tool_results["bandit"]["issues"])
            score += PRIORITY_WEIGHTS["security"] * security_issues

        # Add score for type errors
        if "mypy" in tool_results and isinstance(tool_results["mypy"], dict) and tool_results["mypy"].get("errors"):
            type_errors = len(tool_results["mypy"]["errors"])
            score += PRIORITY_WEIGHTS["type_errors"] * type_errors

        if "pyright" in tool_results and isinstance(tool_results["pyright"], dict) and tool_results["pyright"].get("errors"):
            type_errors = len(tool_results["pyright"]["errors"])
            score += PRIORITY_WEIGHTS["type_errors"] * type_errors

        # Add score for lint issues
        if "ruff" in tool_results and isinstance(tool_results["ruff"], dict) and tool_results["ruff"].get("errors"):
            lint_issues = len(tool_results["ruff"]["errors"])
            score += PRIORITY_WEIGHTS["lint_issues"] * lint_issues

        # Add score for circular imports
        if "mypy" in tool_results and isinstance(tool_results["mypy"], dict) and tool_results["mypy"].get("errors"):
            for error in tool_results["mypy"].get("errors", []):
                if "circular" in str(error).lower():
                    score += PRIORITY_WEIGHTS["circular_imports"]
                    break

        # Add score for low test coverage
        if "coverage" in tool_results and isinstance(tool_results["coverage"], dict) and "coverage" in tool_results["coverage"]:
            coverage = tool_results["coverage"].get("coverage", 100)
            if isinstance(coverage, (int, float)) and coverage < 70:
                score += PRIORITY_WEIGHTS["low_coverage"] * ((70 - coverage) / 10)

    # Add score for many imports
    imports = file_metrics.get("imports", [])
    if isinstance(imports, list) and len(imports) > 15:
        score += PRIORITY_WEIGHTS["many_imports"] * (len(imports) - 15)
    elif isinstance(imports, str):
        # If imports is a string, count the number of commas + 1
        import_count = imports.count(",") + 1
        if import_count > 15:
            score += PRIORITY_WEIGHTS["many_imports"] * (import_count - 15)

    return score

def rank_files(pat_data: Dict[str, Any]) -> List[Tuple[str, float, Dict[str, Any]]]:
    """Rank files based on their priority score."""
    files = pat_data.get("files", [])
    ranked_files = []

    for file_metrics in files:
        # Handle different formats of file metrics
        if isinstance(file_metrics, str):
            # If it's just a string, assume it's a file path
            path = file_metrics
            file_metrics = {"path": path}
        elif isinstance(file_metrics, dict):
            path = file_metrics.get("path", "")
        else:
            # Skip if it's neither a string nor a dict
            continue

        if not path:
            continue

        score = calculate_priority_score(file_metrics)
        ranked_files.append((path, score, file_metrics))

    # Sort by score in descending order
    ranked_files.sort(key=lambda x: x[1], reverse=True)
    return ranked_files

def generate_prompt(file_path: str, file_metrics: Dict[str, Any], score: float) -> str:
    """Generate a focused prompt for a high-priority file."""
    # Extract file information
    complexity = file_metrics.get("complexity", 0)
    lines = file_metrics.get("lines", 0)
    imports = file_metrics.get("imports", [])
    classes = file_metrics.get("classes", [])
    functions = file_metrics.get("functions", [])
    # Handle docstrings safely
    docstrings = {}
    if "docstrings" in file_metrics and isinstance(file_metrics["docstrings"], dict):
        docstrings = file_metrics["docstrings"]
    tool_results = file_metrics.get("tool_results", {})

    # Identify issues
    issues = []
    if isinstance(complexity, (int, float)) and complexity > 10:
        issues.append(f"High Complexity: {complexity} (recommended: <10)")

    if isinstance(lines, (int, float)) and lines > 600:
        issues.append(f"Large File: {lines} lines (recommended: <600)")

    # Handle imports count
    import_count = 0
    if isinstance(imports, list):
        import_count = len(imports)
    elif isinstance(imports, str):
        import_count = imports.count(",") + 1

    if import_count > 15:
        issues.append(f"Many Imports: {import_count} imports (recommended: <15)")

    # Handle tool results safely
    if isinstance(tool_results, dict):
        if "bandit" in tool_results and isinstance(tool_results["bandit"], dict) and tool_results["bandit"].get("issues"):
            security_issues = len(tool_results["bandit"]["issues"])
            issues.append(f"Security Issues: {security_issues} issues detected")

        if "mypy" in tool_results and isinstance(tool_results["mypy"], dict) and tool_results["mypy"].get("errors"):
            type_errors = len(tool_results["mypy"]["errors"])
            issues.append(f"Type Errors: {type_errors} errors detected")

        if "pyright" in tool_results and isinstance(tool_results["pyright"], dict) and tool_results["pyright"].get("errors"):
            type_errors = len(tool_results["pyright"]["errors"])
            issues.append(f"Type Errors: {type_errors} errors detected")

        if "ruff" in tool_results and isinstance(tool_results["ruff"], dict) and tool_results["ruff"].get("errors"):
            lint_issues = len(tool_results["ruff"]["errors"])
            issues.append(f"Lint Issues: {lint_issues} issues detected")

        if "coverage" in tool_results and isinstance(tool_results["coverage"], dict) and "coverage" in tool_results["coverage"]:
            coverage = tool_results["coverage"].get("coverage", 100)
            if isinstance(coverage, (int, float)) and coverage < 70:
                issues.append(f"Low Test Coverage: {coverage:.1f}% (recommended: >70%)")

        # Check for circular imports
        has_circular_imports = False
        if "mypy" in tool_results and isinstance(tool_results["mypy"], dict) and tool_results["mypy"].get("errors"):
            for error in tool_results["mypy"].get("errors", []):
                if "circular" in str(error).lower():
                    has_circular_imports = True
                    issues.append("Circular Imports: Detected")
                    break

    # Generate the prompt
    prompt = f"# HIGH PRIORITY FILE: {file_path}\n\n"
    prompt += f"## Priority Score: {score:.2f}\n\n"

    prompt += "## Purpose of this Prompt\n"
    prompt += "This file requires immediate attention due to critical issues that affect code quality, maintainability, and security.\n\n"

    if issues:
        prompt += "## Critical Issues Identified\n"
        for issue in issues:
            prompt += f"- **{issue}**\n"
        prompt += "\n"

    # Add file information
    prompt += f"## File Information\n"
    prompt += f"- **Lines:** {lines}\n"
    prompt += f"- **Complexity:** {complexity}\n"

    # Handle imports count safely
    if isinstance(imports, list):
        prompt += f"- **Imports:** {len(imports)}\n"
    elif isinstance(imports, str):
        import_count = imports.count(",") + 1
        prompt += f"- **Imports:** {import_count}\n"
    else:
        prompt += f"- **Imports:** Unknown\n"

    # Handle classes count safely
    if isinstance(classes, list) and classes:
        prompt += f"- **Classes:** {len(classes)}\n"

    # Handle functions count safely
    if isinstance(functions, list) and functions:
        prompt += f"- **Functions:** {len(functions)}\n"

    prompt += "\n"

    # Add specific refactoring instructions
    prompt += "## Refactoring Instructions\n"
    prompt += "Please provide specific recommendations for refactoring this file to address the identified issues:\n\n"

    instruction_count = 0

    if isinstance(complexity, (int, float)) and complexity > 10:
        instruction_count += 1
        prompt += f"{instruction_count}. **Reduce Complexity**: Identify methods that can be broken down into smaller, more focused functions.\n"

    if isinstance(lines, (int, float)) and lines > 600:
        instruction_count += 1
        prompt += f"{instruction_count}. **Split File**: Suggest how this file could be split into multiple files with clear responsibilities.\n"

    # Handle type errors safely
    has_type_errors = False
    if isinstance(tool_results, dict):
        if "mypy" in tool_results and isinstance(tool_results["mypy"], dict) and tool_results["mypy"].get("errors"):
            has_type_errors = True
        elif "pyright" in tool_results and isinstance(tool_results["pyright"], dict) and tool_results["pyright"].get("errors"):
            has_type_errors = True

    if has_type_errors:
        instruction_count += 1
        prompt += f"{instruction_count}. **Fix Type Errors**: Provide solutions for the type errors, ensuring proper type annotations.\n"

    if has_circular_imports:
        instruction_count += 1
        prompt += f"{instruction_count}. **Resolve Circular Imports**: Suggest restructuring to eliminate circular dependencies.\n"

    # Handle security issues safely
    has_security_issues = False
    if isinstance(tool_results, dict) and "bandit" in tool_results and isinstance(tool_results["bandit"], dict) and tool_results["bandit"].get("issues"):
        has_security_issues = True

    if has_security_issues:
        instruction_count += 1
        prompt += f"{instruction_count}. **Address Security Issues**: Recommend fixes for security vulnerabilities.\n"

    prompt += "\nPlease provide code examples where appropriate and ensure all recommendations align with CAW principles.\n"

    return prompt

def extract_high_priority_files(pat_output_dir: Path, top_n: int, min_score: float, output_dir: Path):
    """Extract high-priority files from PAT output and generate prompts for them."""
    # Load PAT JSON data
    pat_data = load_pat_json(pat_output_dir)

    # Rank files by priority score
    ranked_files = rank_files(pat_data)

    # Create output directory
    output_dir.mkdir(parents=True, exist_ok=True)

    # Generate prompts for high-priority files
    high_priority_files = []
    for i, (path, score, metrics) in enumerate(ranked_files):
        if i >= top_n or score < min_score:
            break

        # Generate prompt
        prompt = generate_prompt(path, metrics, score)

        # Save prompt to file
        file_name = f"priority_{i+1:02d}_{Path(path).name}.md"
        prompt_path = output_dir / file_name
        with open(prompt_path, 'w') as f:
            f.write(prompt)

        high_priority_files.append((path, score, prompt_path))

    # Generate summary report
    summary = "# High-Priority Files Summary\n\n"
    summary += f"Found {len(high_priority_files)} high-priority files with score >= {min_score}.\n\n"

    if high_priority_files:
        summary += "## Files Ranked by Priority\n\n"
        summary += "| Rank | File | Priority Score | Prompt |\n"
        summary += "|------|------|---------------|--------|\n"

        for i, (path, score, prompt_path) in enumerate(high_priority_files):
            summary += f"| {i+1} | {path} | {score:.2f} | [{Path(prompt_path).name}]({prompt_path.name}) |\n"
    else:
        summary += "No files met the minimum priority score threshold.\n"

    # Save summary report
    with open(output_dir / "high_priority_summary.md", 'w') as f:
        f.write(summary)

    print(f"Generated prompts for {len(high_priority_files)} high-priority files in {output_dir}")
    print(f"Summary report saved to {output_dir / 'high_priority_summary.md'}")

def main():
    """Main function."""
    args = parse_args()
    pat_output_dir = Path(args.pat_output_dir)
    output_dir = Path(args.output_dir)

    if not pat_output_dir.exists():
        print(f"Error: PAT output directory {pat_output_dir} does not exist")
        return 1

    try:
        extract_high_priority_files(pat_output_dir, args.top, args.min_score, output_dir)
        return 0
    except Exception as e:
        print(f"Error extracting high-priority files: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
