#!/usr/bin/env python3
"""
Debug PromQL Results
====================

Debug what the PromQL engine actually returns to fix alerting integration.
"""

import asyncio
import time
import tempfile
import shutil
from pathlib import Path
import sys

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

async def main():
    """Debug PromQL engine results"""
    from vibe_check.monitoring.storage.time_series_engine import (
        TimeSeriesStorageEngine, TSDBConfig
    )
    from vibe_check.monitoring.query.promql_engine import PromQLEngine
    
    # Create temporary storage
    temp_dir = Path(tempfile.mkdtemp())
    tsdb_config = TSDBConfig(
        data_dir=temp_dir / "tsdb",
        flush_interval_seconds=0.1
    )
    
    # Initialize TSDB and PromQL engine
    tsdb = TimeSeriesStorageEngine(tsdb_config)
    promql_engine = PromQLEngine(tsdb)
    
    # Add test data
    base_time = time.time()
    await tsdb.ingest_sample(
        metric_name="cpu_usage_percent",
        value=85.0,  # High CPU that should trigger alert
        labels={"instance": "server1", "job": "node"},
        timestamp=base_time
    )
    
    await asyncio.sleep(1.0)  # Wait for data to be flushed
    
    # Test query
    print("Testing PromQL query: cpu_usage_percent")
    results = await promql_engine.execute_query("cpu_usage_percent")
    
    print(f"Results type: {type(results)}")
    print(f"Results length: {len(results)}")
    
    for i, result in enumerate(results):
        print(f"\nResult {i}:")
        print(f"  Type: {type(result)}")
        print(f"  Content: {result}")
        
        # Check attributes
        if hasattr(result, 'value'):
            print(f"  Has value attribute: {result.value}")
        if hasattr(result, 'samples'):
            print(f"  Has samples attribute: {result.samples}")
        if hasattr(result, 'labels'):
            print(f"  Has labels attribute: {result.labels}")
        if isinstance(result, dict):
            print(f"  Dict keys: {result.keys()}")
    
    # Cleanup
    shutil.rmtree(temp_dir, ignore_errors=True)

if __name__ == "__main__":
    asyncio.run(main())
