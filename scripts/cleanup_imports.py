#!/usr/bin/env python3
"""
Automated Import Cleanup Script for Vibe Check
Identifies and removes unused imports, consolidates dependencies
"""

import ast
import os
import sys
import subprocess
from pathlib import Path
from typing import List, Dict, Set, Tuple
import argparse
from collections import defaultdict
import json


class ImportAnalyzer(ast.NodeVisitor):
    """AST visitor to analyze imports and their usage"""
    
    def __init__(self):
        self.imports: Dict[str, Set[str]] = defaultdict(set)  # module -> {names}
        self.from_imports: Dict[str, Set[str]] = defaultdict(set)  # module -> {names}
        self.used_names: Set[str] = set()
        self.all_names: Set[str] = set()
        
    def visit_Import(self, node: ast.Import):
        """Visit import statements"""
        for alias in node.names:
            name = alias.asname if alias.asname else alias.name
            self.imports[alias.name].add(name)
            self.all_names.add(name)
        self.generic_visit(node)
    
    def visit_ImportFrom(self, node: ast.ImportFrom):
        """Visit from import statements"""
        if node.module:
            for alias in node.names:
                name = alias.asname if alias.asname else alias.name
                self.from_imports[node.module].add(name)
                self.all_names.add(name)
        self.generic_visit(node)
    
    def visit_Name(self, node: ast.Name):
        """Visit name usage"""
        self.used_names.add(node.id)
        self.generic_visit(node)
    
    def visit_Attribute(self, node: ast.Attribute):
        """Visit attribute access"""
        if isinstance(node.value, ast.Name):
            self.used_names.add(node.value.id)
        self.generic_visit(node)


class ImportCleaner:
    """Main import cleanup class"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.stats = {
            'files_processed': 0,
            'imports_removed': 0,
            'files_modified': 0,
            'errors': []
        }
    
    def scan_unused_imports(self, file_path: Path) -> Tuple[List[str], List[str]]:
        """Identify unused imports in a file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            analyzer = ImportAnalyzer()
            analyzer.visit(tree)
            
            unused_imports = []
            unused_from_imports = []
            
            # Check regular imports
            for module, names in analyzer.imports.items():
                for name in names:
                    if name not in analyzer.used_names:
                        unused_imports.append(f"import {module}")
            
            # Check from imports
            for module, names in analyzer.from_imports.items():
                unused_names = names - analyzer.used_names
                if unused_names:
                    unused_from_imports.append(f"from {module} import {', '.join(unused_names)}")
            
            return unused_imports, unused_from_imports
            
        except Exception as e:
            self.stats['errors'].append(f"Error analyzing {file_path}: {e}")
            return [], []
    
    def remove_unused_imports(self, file_path: Path) -> bool:
        """Remove unused imports from a file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            unused_imports, unused_from_imports = self.scan_unused_imports(file_path)
            
            if not unused_imports and not unused_from_imports:
                return False
            
            # Create a set of lines to remove
            lines_to_remove = set()
            
            for i, line in enumerate(lines):
                line_stripped = line.strip()
                
                # Check if this line contains an unused import
                for unused in unused_imports + unused_from_imports:
                    if line_stripped.startswith(unused.split()[0]) and unused.split()[1] in line:
                        lines_to_remove.add(i)
                        break
            
            if lines_to_remove:
                # Remove the lines
                new_lines = [line for i, line in enumerate(lines) if i not in lines_to_remove]
                
                # Write back to file
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.writelines(new_lines)
                
                self.stats['imports_removed'] += len(lines_to_remove)
                self.stats['files_modified'] += 1
                return True
            
            return False
            
        except Exception as e:
            self.stats['errors'].append(f"Error removing imports from {file_path}: {e}")
            return False
    
    def process_directory(self, directory: Path) -> None:
        """Process all Python files in a directory"""
        for file_path in directory.rglob("*.py"):
            # Skip __pycache__ and other generated files
            if "__pycache__" in str(file_path) or ".pyc" in str(file_path):
                continue
            
            print(f"Processing: {file_path}")
            self.stats['files_processed'] += 1
            self.remove_unused_imports(file_path)
    
    def analyze_dependencies(self) -> Dict[str, int]:
        """Analyze dependency usage across the project"""
        dependency_count = defaultdict(int)
        
        for file_path in self.project_root.rglob("*.py"):
            if "__pycache__" in str(file_path):
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                analyzer = ImportAnalyzer()
                analyzer.visit(tree)
                
                # Count module usage
                for module in analyzer.imports.keys():
                    dependency_count[module] += 1
                
                for module in analyzer.from_imports.keys():
                    dependency_count[module] += 1
                    
            except Exception as e:
                self.stats['errors'].append(f"Error analyzing dependencies in {file_path}: {e}")
        
        return dict(dependency_count)
    
    def generate_report(self) -> Dict:
        """Generate cleanup report"""
        dependency_usage = self.analyze_dependencies()
        
        return {
            'stats': self.stats,
            'dependency_usage': dependency_usage,
            'recommendations': self._generate_recommendations(dependency_usage)
        }
    
    def _generate_recommendations(self, dependency_usage: Dict[str, int]) -> List[str]:
        """Generate optimization recommendations"""
        recommendations = []
        
        # Find rarely used dependencies
        rarely_used = {k: v for k, v in dependency_usage.items() if v <= 2}
        if rarely_used:
            recommendations.append(f"Consider removing rarely used dependencies: {list(rarely_used.keys())}")
        
        # Find potential consolidation opportunities
        similar_deps = self._find_similar_dependencies(dependency_usage)
        if similar_deps:
            recommendations.append(f"Consider consolidating similar dependencies: {similar_deps}")
        
        return recommendations
    
    def _find_similar_dependencies(self, dependency_usage: Dict[str, int]) -> List[List[str]]:
        """Find dependencies that could be consolidated"""
        # Simple heuristic: group by common prefixes
        groups = defaultdict(list)
        
        for dep in dependency_usage.keys():
            if '.' in dep:
                base = dep.split('.')[0]
                groups[base].append(dep)
        
        # Return groups with more than one dependency
        return [group for group in groups.values() if len(group) > 1]


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Clean up unused imports in Vibe Check")
    parser.add_argument("--project-root", default=".", help="Project root directory")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be removed without making changes")
    parser.add_argument("--report", action="store_true", help="Generate detailed report")
    
    args = parser.parse_args()
    
    cleaner = ImportCleaner(args.project_root)
    
    if args.dry_run:
        print("DRY RUN MODE - No files will be modified")
        # TODO: Implement dry run logic
    
    print(f"Starting import cleanup in: {args.project_root}")
    cleaner.process_directory(Path(args.project_root) / "vibe_check")
    
    if args.report:
        report = cleaner.generate_report()
        
        print("\n" + "="*50)
        print("CLEANUP REPORT")
        print("="*50)
        print(f"Files processed: {report['stats']['files_processed']}")
        print(f"Files modified: {report['stats']['files_modified']}")
        print(f"Imports removed: {report['stats']['imports_removed']}")
        
        if report['stats']['errors']:
            print(f"\nErrors encountered: {len(report['stats']['errors'])}")
            for error in report['stats']['errors'][:5]:  # Show first 5 errors
                print(f"  - {error}")
        
        print(f"\nTop dependencies by usage:")
        sorted_deps = sorted(report['dependency_usage'].items(), key=lambda x: x[1], reverse=True)
        for dep, count in sorted_deps[:10]:
            print(f"  {dep}: {count} files")
        
        if report['recommendations']:
            print(f"\nRecommendations:")
            for rec in report['recommendations']:
                print(f"  - {rec}")
        
        # Save detailed report
        with open('import_cleanup_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        print(f"\nDetailed report saved to: import_cleanup_report.json")


if __name__ == "__main__":
    main()
