#!/usr/bin/env python3
"""
Dependency Optimization Script
==============================

Implements critical dependency optimizations based on the analysis results.
Focuses on Vibe Check modules only, ignoring virtual environments and legacy code.
"""

import os
import shutil
from pathlib import Path
from typing import List, Set
import json


class DependencyOptimizer:
    """Optimize dependencies based on analysis results"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.optimizations_applied = []
        
    def remove_legacy_directories(self) -> int:
        """Remove legacy directories that are no longer needed"""
        legacy_dirs = [
            "legacy",
            "old_pat", 
            "PAT_tool",
            "meta_system_analysis"
        ]
        
        removed_count = 0
        for legacy_dir in legacy_dirs:
            legacy_path = self.project_root / legacy_dir
            if legacy_path.exists():
                print(f"🗑️  Removing legacy directory: {legacy_dir}")
                try:
                    shutil.rmtree(legacy_path)
                    removed_count += 1
                    self.optimizations_applied.append(f"Removed legacy directory: {legacy_dir}")
                except Exception as e:
                    print(f"  ❌ Error removing {legacy_dir}: {e}")
        
        return removed_count
    
    def fix_syntax_errors(self) -> int:
        """Fix critical syntax errors in __init__.py files"""
        syntax_fixes = [
            # Fix missing imports in __init__.py files
            {
                'file': 'vibe_check/cli/__init__.py',
                'fixes': [
                    ('from .monitor import monitor', 'from .monitor import monitor\n')
                ]
            },
            {
                'file': 'vibe_check/core/monitoring/__init__.py', 
                'fixes': [
                    ('from .monitoring_engine import VibeCheckMonitoringEngine', 
                     'from .monitoring_engine import VibeCheckMonitoringEngine\n')
                ]
            }
        ]
        
        fixes_applied = 0
        for fix_info in syntax_fixes:
            file_path = self.project_root / fix_info['file']
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    modified = False
                    for old_text, new_text in fix_info['fixes']:
                        if old_text not in content and new_text.strip() not in content:
                            content += new_text
                            modified = True
                            fixes_applied += 1
                    
                    if modified:
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(content)
                        print(f"✅ Fixed syntax in: {fix_info['file']}")
                        self.optimizations_applied.append(f"Fixed syntax in: {fix_info['file']}")
                        
                except Exception as e:
                    print(f"❌ Error fixing {fix_info['file']}: {e}")
        
        return fixes_applied
    
    def consolidate_utility_imports(self) -> int:
        """Create consolidated utility modules for frequently used imports"""
        
        # Create a common utilities module
        utils_content = '''"""
Common Utilities Module
======================

Consolidated imports and utilities used throughout Vibe Check.
"""

# Standard library imports
import os
import sys
import time
import json
import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from collections import defaultdict

# Third-party imports
import click
import yaml
import psutil

# Common type aliases
ConfigDict = Dict[str, Any]
MetricDict = Dict[str, float]
LabelDict = Dict[str, str]

# Common utility functions
def get_timestamp() -> float:
    """Get current timestamp"""
    return time.time()

def format_bytes(bytes_value: int) -> str:
    """Format bytes in human readable format"""
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if bytes_value < 1024.0:
            return f"{bytes_value:.1f}{unit}"
        bytes_value /= 1024.0
    return f"{bytes_value:.1f}PB"

def safe_json_loads(json_str: str, default: Any = None) -> Any:
    """Safely load JSON with default fallback"""
    try:
        return json.loads(json_str)
    except (json.JSONDecodeError, TypeError):
        return default

def ensure_directory(path: Path) -> Path:
    """Ensure directory exists"""
    path.mkdir(parents=True, exist_ok=True)
    return path
'''
        
        utils_path = self.project_root / 'vibe_check' / 'core' / 'common_utils.py'
        try:
            with open(utils_path, 'w', encoding='utf-8') as f:
                f.write(utils_content)
            print(f"✅ Created consolidated utilities: {utils_path}")
            self.optimizations_applied.append(f"Created consolidated utilities module")
            return 1
        except Exception as e:
            print(f"❌ Error creating utilities module: {e}")
            return 0
    
    def optimize_import_structure(self) -> int:
        """Optimize import structure in key modules"""
        
        # Update main CLI to use consolidated imports
        cli_main_path = self.project_root / 'vibe_check' / 'cli' / 'main.py'
        if cli_main_path.exists():
            try:
                with open(cli_main_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Add import for monitoring commands if not present
                if 'from .monitor import monitor' not in content:
                    # Find the imports section and add the monitor import
                    lines = content.split('\n')
                    import_section_end = -1
                    
                    for i, line in enumerate(lines):
                        if line.startswith('from ..core.version import __version__'):
                            import_section_end = i
                            break
                    
                    if import_section_end > -1:
                        lines.insert(import_section_end + 1, 'from .monitor import monitor')
                        
                        # Also add the command registration if not present
                        if 'cli.add_command(monitor)' not in content:
                            # Find where to add the command
                            for i, line in enumerate(lines):
                                if 'if __name__ == "__main__":' in line:
                                    lines.insert(i, '# Add monitoring commands')
                                    lines.insert(i + 1, 'cli.add_command(monitor)')
                                    lines.insert(i + 2, '')
                                    break
                        
                        with open(cli_main_path, 'w', encoding='utf-8') as f:
                            f.write('\n'.join(lines))
                        
                        print(f"✅ Optimized imports in: {cli_main_path}")
                        self.optimizations_applied.append(f"Optimized imports in CLI main")
                        return 1
                        
            except Exception as e:
                print(f"❌ Error optimizing CLI imports: {e}")
        
        return 0
    
    def clean_empty_directories(self) -> int:
        """Remove empty directories"""
        removed_count = 0
        
        for root, dirs, files in os.walk(self.project_root, topdown=False):
            for dir_name in dirs:
                dir_path = Path(root) / dir_name
                
                # Skip virtual environments and git directories
                if any(skip in str(dir_path) for skip in ['.venv', 'venv', '.git', '__pycache__']):
                    continue
                
                try:
                    # Check if directory is empty
                    if dir_path.is_dir() and not any(dir_path.iterdir()):
                        dir_path.rmdir()
                        removed_count += 1
                        print(f"🗑️  Removed empty directory: {dir_path}")
                        self.optimizations_applied.append(f"Removed empty directory: {dir_path}")
                except Exception as e:
                    # Directory might not be empty or have permission issues
                    pass
        
        return removed_count
    
    def generate_optimization_report(self) -> dict:
        """Generate optimization report"""
        return {
            'optimizations_applied': self.optimizations_applied,
            'total_optimizations': len(self.optimizations_applied),
            'timestamp': time.time()
        }


def main():
    """Main optimization function"""
    print("🔧 Vibe Check Dependency Optimization")
    print("=" * 40)
    
    optimizer = DependencyOptimizer(".")
    
    # Apply optimizations
    print("1. Removing legacy directories...")
    legacy_removed = optimizer.remove_legacy_directories()
    
    print("\n2. Fixing syntax errors...")
    syntax_fixed = optimizer.fix_syntax_errors()
    
    print("\n3. Consolidating utility imports...")
    utils_created = optimizer.consolidate_utility_imports()
    
    print("\n4. Optimizing import structure...")
    imports_optimized = optimizer.optimize_import_structure()
    
    print("\n5. Cleaning empty directories...")
    empty_dirs_removed = optimizer.clean_empty_directories()
    
    # Generate report
    report = optimizer.generate_optimization_report()
    
    print("\n" + "=" * 40)
    print("📊 OPTIMIZATION SUMMARY")
    print("=" * 40)
    print(f"Legacy directories removed: {legacy_removed}")
    print(f"Syntax errors fixed: {syntax_fixed}")
    print(f"Utility modules created: {utils_created}")
    print(f"Import structures optimized: {imports_optimized}")
    print(f"Empty directories removed: {empty_dirs_removed}")
    print(f"Total optimizations: {report['total_optimizations']}")
    
    # Save report
    with open('dependency_optimization_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📄 Optimization report saved to: dependency_optimization_report.json")
    print("✅ Dependency optimization complete!")


if __name__ == "__main__":
    import time
    main()
