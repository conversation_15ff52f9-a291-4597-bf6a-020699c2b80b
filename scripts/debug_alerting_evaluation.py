#!/usr/bin/env python3
"""
Debug Alerting Evaluation
==========================

Debug the alerting rule evaluation process step by step.
"""

import asyncio
import time
import tempfile
import shutil
import logging
from pathlib import Path
import sys

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Enable debug logging
logging.basicConfig(level=logging.DEBUG)

async def main():
    """Debug alerting evaluation"""
    from vibe_check.monitoring.storage.time_series_engine import (
        TimeSeriesStorageEngine, TSDBConfig
    )
    from vibe_check.monitoring.query.promql_engine import PromQLEngine
    from vibe_check.monitoring.alerting import (
        AlertingEngine, AlertRule, AlertSeverity
    )
    
    # Create temporary storage
    temp_dir = Path(tempfile.mkdtemp())
    tsdb_config = TSDBConfig(
        data_dir=temp_dir / "tsdb",
        flush_interval_seconds=0.1
    )
    
    # Initialize TSDB and PromQL engine
    tsdb = TimeSeriesStorageEngine(tsdb_config)
    promql_engine = PromQLEngine(tsdb)
    
    # Add test data
    base_time = time.time()
    print(f"Adding test data at timestamp {base_time}")
    
    await tsdb.ingest_sample(
        metric_name="cpu_usage_percent",
        value=85.0,  # High CPU that should trigger alert
        labels={"instance": "server1", "job": "node"},
        timestamp=base_time
    )
    
    await asyncio.sleep(1.0)  # Wait for data to be flushed
    print("Data flushed to TSDB")
    
    # Test direct query first
    print("\n=== Testing Direct PromQL Query ===")
    results = await promql_engine.execute_query("cpu_usage_percent")
    print(f"Direct query results: {len(results)} results")
    for i, result in enumerate(results):
        print(f"  Result {i}: {result}")
        if hasattr(result, 'values') and result.values:
            print(f"    Latest value: {result.values[-1][1]}")
    
    # Create alerting engine
    print("\n=== Creating Alerting Engine ===")
    alerting_engine = AlertingEngine(promql_engine)
    
    # Create alert rule
    cpu_rule = AlertRule(
        name="test_high_cpu",
        query="cpu_usage_percent",
        condition="> 80",  # Should trigger with our test data (85)
        severity=AlertSeverity.CRITICAL,
        description="Test high CPU alert",
        evaluation_interval=1.0,  # Fast evaluation
        for_duration=1.0  # Short duration for testing
    )
    
    alerting_engine.add_alert_rule(cpu_rule)
    print(f"Added alert rule: {cpu_rule.name}")
    
    # Test single rule evaluation
    print("\n=== Testing Single Rule Evaluation ===")
    current_time = time.time()
    try:
        await alerting_engine._evaluate_rule(cpu_rule, current_time)
        print("Rule evaluation completed")
    except Exception as e:
        print(f"Rule evaluation failed: {e}")
        import traceback
        traceback.print_exc()
    
    # Check active alerts
    active_alerts = alerting_engine.get_active_alerts()
    print(f"\nActive alerts after evaluation: {len(active_alerts)}")
    for alert in active_alerts:
        print(f"  Alert: {alert.rule_name} - {alert.state.value} (value: {alert.value})")
    
    # Cleanup
    shutil.rmtree(temp_dir, ignore_errors=True)

if __name__ == "__main__":
    asyncio.run(main())
