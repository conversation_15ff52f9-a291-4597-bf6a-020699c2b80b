#!/usr/bin/env python3
"""
Pre-commit Quality Check Script
===============================

Enhanced pre-commit hook script that performs comprehensive quality validation
using the automated enforcement system.

Usage:
    python scripts/pre_commit_quality_check.py [file1] [file2] ...

Features:
- Constants usage validation
- Terminology compliance checking
- Configuration schema validation
- Detailed violation reporting
- Exit codes for CI/CD integration
"""

import sys
import argparse
from pathlib import Path
from typing import List

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from vibe_check.core.quality import (
        AutomatedEnforcementEngine,
        ViolationType
    )
except ImportError as e:
    print(f"❌ Failed to import quality enforcement system: {e}")
    print("Make sure vibe_check is properly installed")
    sys.exit(1)


def main():
    """Main entry point for pre-commit quality check."""
    parser = argparse.ArgumentParser(
        description="Pre-commit quality enforcement check"
    )
    parser.add_argument(
        "files",
        nargs="*",
        help="Files to check (if none provided, checks all Python files)"
    )
    parser.add_argument(
        "--severity",
        choices=["error", "warning", "info"],
        default="warning",
        help="Minimum severity level to report (default: warning)"
    )
    parser.add_argument(
        "--types",
        nargs="+",
        choices=[vt.value for vt in ViolationType],
        help="Violation types to check (default: all)"
    )
    parser.add_argument(
        "--quiet",
        action="store_true",
        help="Only show summary, not individual violations"
    )
    parser.add_argument(
        "--fix",
        action="store_true",
        help="Attempt to auto-fix violations where possible"
    )
    
    args = parser.parse_args()
    
    # Initialize enforcement engine
    engine = AutomatedEnforcementEngine()
    
    # Determine files to check
    if args.files:
        file_paths = [Path(f) for f in args.files if Path(f).exists()]
    else:
        # Check all Python files in project
        file_paths = list(project_root.rglob("*.py"))
        # Also check configuration files
        for pattern in ["*.yaml", "*.yml", "*.json"]:
            file_paths.extend(project_root.rglob(pattern))
    
    # Filter out excluded directories
    excluded_patterns = [
        "venv", ".venv", "env", ".env",
        "__pycache__", ".pytest_cache", ".mypy_cache",
        ".git", "node_modules", "build", "dist"
    ]
    
    filtered_paths = []
    for path in file_paths:
        if not any(pattern in str(path) for pattern in excluded_patterns):
            filtered_paths.append(path)
    
    if not filtered_paths:
        print("✅ No files to check")
        return 0
    
    print(f"🔍 Checking {len(filtered_paths)} files for quality violations...")
    
    # Run validation
    violations = engine.validate_files(filtered_paths)
    
    # Filter by severity
    severity_order = {"info": 0, "warning": 1, "error": 2}
    min_severity = severity_order[args.severity]
    filtered_violations = [
        v for v in violations 
        if severity_order.get(v.severity, 0) >= min_severity
    ]
    
    # Filter by types if specified
    if args.types:
        type_filter = set(ViolationType(t) for t in args.types)
        filtered_violations = [
            v for v in filtered_violations
            if v.violation_type in type_filter
        ]
    
    # Generate and display report
    if not args.quiet:
        report = engine.generate_report(filtered_violations)
        print(report)
    else:
        # Just show summary
        error_count = sum(1 for v in filtered_violations if v.severity == "error")
        warning_count = sum(1 for v in filtered_violations if v.severity == "warning")
        info_count = sum(1 for v in filtered_violations if v.severity == "info")
        
        if filtered_violations:
            print(f"❌ Found {len(filtered_violations)} violations: "
                  f"{error_count} errors, {warning_count} warnings, {info_count} info")
        else:
            print("✅ No quality violations found!")
    
    # Auto-fix if requested (placeholder for future implementation)
    if args.fix and filtered_violations:
        print("\n🔧 Auto-fix functionality coming soon...")
    
    # Exit with appropriate code
    error_count = sum(1 for v in filtered_violations if v.severity == "error")
    if error_count > 0:
        return 1  # Fail on errors
    elif args.severity == "warning" and filtered_violations:
        return 1  # Fail on warnings if checking warnings
    else:
        return 0  # Success


if __name__ == "__main__":
    sys.exit(main())
