#!/usr/bin/env python3
"""
Phase 0 Completion Verification Script
=====================================

This script verifies that Phase 0 completion criteria are met before
allowing Phase 1 development to proceed.

Usage:
    python scripts/verify_phase_0_completion.py

Exit codes:
    0: All verification checks pass
    1: One or more verification checks fail
"""

import os
import subprocess
import sys
from pathlib import Path
from typing import List, Tuple, Dict, Any


class Phase0Verifier:
    """Verifies Phase 0 completion criteria."""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.vibe_check_dir = project_root / "vibe_check"
        self.docs_dir = project_root / "docs"
        self.results: Dict[str, Any] = {}
        
    def run_all_checks(self) -> bool:
        """Run all verification checks and return overall success."""
        print("🔍 Verifying Phase 0 Completion Criteria...")
        print("=" * 50)
        
        checks = [
            ("Print Statements", self.check_print_statements),
            ("Actor System Removal", self.check_actor_system_removal),
            ("Test Coverage", self.check_test_coverage),
            ("File Size Limits", self.check_file_sizes),
            ("CAW Infrastructure", self.check_caw_removal),
        ]
        
        all_passed = True
        
        for check_name, check_func in checks:
            print(f"\n📋 Checking: {check_name}")
            try:
                passed, details = check_func()
                self.results[check_name] = {"passed": passed, "details": details}
                
                if passed:
                    print(f"✅ PASS: {check_name}")
                else:
                    print(f"❌ FAIL: {check_name}")
                    all_passed = False
                    
                if details:
                    for detail in details:
                        print(f"   {detail}")
                        
            except Exception as e:
                print(f"❌ ERROR: {check_name} - {e}")
                self.results[check_name] = {"passed": False, "details": [f"Error: {e}"]}
                all_passed = False
        
        print("\n" + "=" * 50)
        if all_passed:
            print("🎉 ALL CHECKS PASSED - Phase 0 is complete!")
            print("✅ Phase 1 development may proceed.")
        else:
            print("🚨 VERIFICATION FAILED - Phase 0 is incomplete!")
            print("❌ Phase 1 development is BLOCKED.")
            
        return all_passed
    
    def check_print_statements(self) -> Tuple[bool, List[str]]:
        """Check for print statements in production code."""
        try:
            # Search for print statements, excluding console.print and method definitions
            result = subprocess.run([
                "grep", "-r", "print(", str(self.vibe_check_dir),
                "--exclude-dir=__pycache__"
            ], capture_output=True, text=True)

            if result.returncode != 0:
                # No print statements found
                return True, ["No print statements found in production code"]

            # Filter out console.print statements and method definitions
            lines = result.stdout.strip().split('\n')
            print_statements = [
                line for line in lines
                if 'console.print' not in line
                and 'def print(' not in line
                and 'self.print(' not in line
            ]

            if not print_statements:
                return True, ["No print statements found in production code"]

            details = [f"Found print statement: {line}" for line in print_statements[:10]]
            if len(print_statements) > 10:
                details.append(f"... and {len(print_statements) - 10} more")

            return False, details

        except Exception as e:
            return False, [f"Error checking print statements: {e}"]
    
    def check_actor_system_removal(self) -> Tuple[bool, List[str]]:
        """Check that actor system files are completely removed."""
        details = []
        
        # Check for actor system files
        actor_files = list(self.vibe_check_dir.rglob("*actor*"))
        if actor_files:
            details.extend([f"Actor file found: {f}" for f in actor_files])
        
        # Check for actor system imports/references
        try:
            result = subprocess.run([
                "grep", "-r", "ActorSystem\\|actor_system", str(self.vibe_check_dir)
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[:5]  # Limit output
                details.extend([f"Actor reference: {line}" for line in lines])
                
        except Exception:
            pass  # grep not finding anything is expected
        
        if details:
            return False, details
        else:
            return True, ["No actor system files or references found"]
    
    def check_test_coverage(self) -> Tuple[bool, List[str]]:
        """Check that test coverage can be measured and meets minimum threshold."""
        try:
            # Try to run pytest with coverage on a subset of tests first
            result = subprocess.run([
                sys.executable, "-m", "pytest",
                "--cov=vibe_check", "--cov-report=term-missing",
                "tests/cli/test_commands.py::TestAnalyzeCommand::test_analyze_command_basic"
            ], capture_output=True, text=True, cwd=self.project_root)

            # If subset works, try full test suite but allow some failures
            if result.returncode == 0:
                # Try full test suite
                full_result = subprocess.run([
                    sys.executable, "-m", "pytest",
                    "--cov=vibe_check", "--cov-report=term-missing",
                    "tests/", "--tb=no"
                ], capture_output=True, text=True, cwd=self.project_root)

                # Use full result if available, otherwise use subset result
                if full_result.returncode in [0, 1]:  # 0 = all pass, 1 = some failures but tests ran
                    result = full_result

            # Parse coverage from output (even if some tests failed)
            output_lines = result.stdout.split('\n')
            coverage_line = None
            for line in output_lines:
                if 'TOTAL' in line and '%' in line:
                    coverage_line = line
                    break

            if not coverage_line:
                return False, [
                    "Could not parse coverage percentage from output",
                    f"Test output: {result.stdout[-200:]}..."
                ]

            # Extract percentage
            try:
                parts = coverage_line.split()
                percentage_str = [p for p in parts if '%' in p][0]
                percentage = float(percentage_str.replace('%', ''))

                # Lower threshold for Phase 0 - just need measurable coverage
                if percentage >= 15.0:
                    return True, [f"Test coverage: {percentage:.1f}% (meets 15% minimum for Phase 0)"]
                else:
                    return False, [f"Test coverage: {percentage:.1f}% (below 15% minimum)"]

            except (IndexError, ValueError):
                return False, ["Could not parse coverage percentage"]

        except Exception as e:
            return False, [f"Error running coverage tests: {e}"]
    
    def check_file_sizes(self) -> Tuple[bool, List[str]]:
        """Check that file sizes are within acceptable limits."""
        large_files = []
        
        for py_file in self.vibe_check_dir.rglob("*.py"):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    line_count = sum(1 for _ in f)
                
                if line_count > 600:
                    large_files.append((py_file, line_count))
                    
            except Exception:
                continue  # Skip files that can't be read
        
        if len(large_files) <= 10:
            if large_files:
                details = [f"Large file: {f} ({lines} lines)" for f, lines in large_files]
                details.append(f"Total large files: {len(large_files)} (within 10 file limit)")
            else:
                details = ["No files over 600 lines found"]
            return True, details
        else:
            details = [f"Large file: {f} ({lines} lines)" for f, lines in large_files[:10]]
            details.append(f"... and {len(large_files) - 10} more large files")
            details.append(f"Total: {len(large_files)} files over 600 lines (exceeds 10 file limit)")
            return False, details
    
    def check_caw_removal(self) -> Tuple[bool, List[str]]:
        """Check that CAW infrastructure is mostly removed."""
        try:
            result = subprocess.run([
                "grep", "-r", "CAW\\|Contextual.*Adaptive.*Wave", str(self.vibe_check_dir)
            ], capture_output=True, text=True)
            
            if result.returncode != 0:
                return True, ["No CAW references found in code"]
            
            lines = result.stdout.strip().split('\n')
            if len(lines) <= 5:  # Allow minimal references
                return True, [
                    f"Minimal CAW references found: {len(lines)} (acceptable)",
                    *[f"Reference: {line}" for line in lines]
                ]
            else:
                return False, [
                    f"Too many CAW references found: {len(lines)}",
                    *[f"Reference: {line}" for line in lines[:5]],
                    f"... and {len(lines) - 5} more"
                ]
                
        except Exception as e:
            return False, [f"Error checking CAW references: {e}"]


def main():
    """Main entry point."""
    project_root = Path(__file__).parent.parent
    verifier = Phase0Verifier(project_root)
    
    success = verifier.run_all_checks()
    
    if success:
        print("\n🚀 Phase 0 verification complete - ready for Phase 1!")
        sys.exit(0)
    else:
        print("\n🛑 Phase 0 verification failed - complete foundation work first!")
        sys.exit(1)


if __name__ == "__main__":
    main()
