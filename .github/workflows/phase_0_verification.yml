name: Phase 0 Verification

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  phase-0-verification:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov
    
    - name: Run Phase 0 Verification
      run: |
        python scripts/verify_phase_0_completion.py
      continue-on-error: true
      id: verification
    
    - name: Phase 0 Status Check
      run: |
        if [ ${{ steps.verification.outcome }} == 'success' ]; then
          echo "✅ Phase 0 verification passed - development may proceed"
          echo "phase_0_complete=true" >> $GITHUB_OUTPUT
        else
          echo "❌ Phase 0 verification failed - foundation work required"
          echo "phase_0_complete=false" >> $GITHUB_OUTPUT
          echo "::warning::Phase 0 is incomplete. Phase 1 development is blocked."
          exit 1
        fi
      id: status
    
    - name: Comment PR (if Phase 0 incomplete)
      if: github.event_name == 'pull_request' && steps.status.outputs.phase_0_complete == 'false'
      uses: actions/github-script@v6
      with:
        script: |
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: `## 🚨 Phase 0 Verification Failed
            
            This PR cannot be merged because Phase 0 foundation work is incomplete.
            
            **Required Actions:**
            1. Complete actor system removal
            2. Replace all print statements with proper logging
            3. Fix test coverage system
            4. Address oversized files
            
            **Verification Command:**
            \`\`\`bash
            python scripts/verify_phase_0_completion.py
            \`\`\`
            
            See [Phase 0 Completion Plan](docs/roadmap/PHASE_0_COMPLETION_PLAN.md) for details.`
          })

  block-phase-1-development:
    runs-on: ubuntu-latest
    needs: phase-0-verification
    if: failure()
    
    steps:
    - name: Block Phase 1 Development
      run: |
        echo "🛑 BLOCKING: Phase 1 development is not allowed until Phase 0 is complete"
        echo "📋 See docs/roadmap/PHASE_0_COMPLETION_PLAN.md for required tasks"
        echo "🔍 Run 'python scripts/verify_phase_0_completion.py' to check status"
        exit 1
