{"errors": [{"filename": "vibe_check/cli/knowledge_manager.py", "reason": "syntax error while parsing AST from file"}, {"filename": "vibe_check/core/docs/__init__.py", "reason": "syntax error while parsing AST from file"}, {"filename": "vibe_check/core/docs/utils.py", "reason": "syntax error while parsing AST from file"}, {"filename": "vibe_check/ui/tui/app.py", "reason": "syntax error while parsing AST from file"}], "generated_at": "2025-06-28T20:23:47Z", "metrics": {"_totals": {"CONFIDENCE.HIGH": 64, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 2, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 18, "SEVERITY.LOW": 41, "SEVERITY.MEDIUM": 7, "SEVERITY.UNDEFINED": 0, "loc": 76020, "nosec": 0, "skipped_tests": 0}, "vibe_check/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 31, "nosec": 0, "skipped_tests": 0}, "vibe_check/__main__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 8, "nosec": 0, "skipped_tests": 0}, "vibe_check/ai/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 20, "nosec": 0, "skipped_tests": 0}, "vibe_check/ai/explanation/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 10, "nosec": 0, "skipped_tests": 0}, "vibe_check/ai/explanation/comment_analyzer.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 2, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 388, "nosec": 0, "skipped_tests": 0}, "vibe_check/ai/explanation/documentation_generator.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 498, "nosec": 0, "skipped_tests": 0}, "vibe_check/ai/explanation/explanation_engine.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 382, "nosec": 0, "skipped_tests": 0}, "vibe_check/ai/infrastructure/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 18, "nosec": 0, "skipped_tests": 0}, "vibe_check/ai/infrastructure/model_manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 408, "nosec": 0, "skipped_tests": 0}, "vibe_check/ai/infrastructure/model_optimizer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 368, "nosec": 0, "skipped_tests": 0}, "vibe_check/ai/infrastructure/privacy_processor.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 317, "nosec": 0, "skipped_tests": 0}, "vibe_check/ai/refactoring/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 19, "nosec": 0, "skipped_tests": 0}, "vibe_check/ai/refactoring/code_smell_detector.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 461, "nosec": 0, "skipped_tests": 0}, "vibe_check/ai/refactoring/impact_analyzer.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 520, "nosec": 0, "skipped_tests": 0}, "vibe_check/ai/refactoring/pattern_recommender.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 501, "nosec": 0, "skipped_tests": 0}, "vibe_check/ai/refactoring/refactoring_engine.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 481, "nosec": 0, "skipped_tests": 0}, "vibe_check/ai/temporal/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 19, "nosec": 0, "skipped_tests": 0}, "vibe_check/ai/temporal/debt_predictor.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 439, "nosec": 0, "skipped_tests": 0}, "vibe_check/ai/temporal/productivity_analyzer.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 570, "nosec": 0, "skipped_tests": 0}, "vibe_check/ai/temporal/temporal_engine.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 2, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 447, "nosec": 0, "skipped_tests": 0}, "vibe_check/ai/temporal/trend_visualizer.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 2, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 512, "nosec": 0, "skipped_tests": 0}, "vibe_check/ai/visualization/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 19, "nosec": 0, "skipped_tests": 0}, "vibe_check/ai/visualization/dashboard_engine.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 678, "nosec": 0, "skipped_tests": 0}, "vibe_check/ai/visualization/data_aggregator.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 431, "nosec": 0, "skipped_tests": 0}, "vibe_check/ai/visualization/interactive_charts.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 535, "nosec": 0, "skipped_tests": 0}, "vibe_check/ai/visualization/report_generator.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 588, "nosec": 0, "skipped_tests": 0}, "vibe_check/cli/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 15, "nosec": 0, "skipped_tests": 0}, "vibe_check/cli/async_commands.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 307, "nosec": 0, "skipped_tests": 0}, "vibe_check/cli/commands.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 566, "nosec": 0, "skipped_tests": 0}, "vibe_check/cli/completion.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 169, "nosec": 0, "skipped_tests": 0}, "vibe_check/cli/error_handler.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 147, "nosec": 0, "skipped_tests": 0}, "vibe_check/cli/formatters.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 462, "nosec": 0, "skipped_tests": 0}, "vibe_check/cli/handlers.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 228, "nosec": 0, "skipped_tests": 0}, "vibe_check/cli/knowledge_manager.py": {"loc": 256, "nosec": 0, "skipped_tests": 0}, "vibe_check/cli/main.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 212, "nosec": 0, "skipped_tests": 0}, "vibe_check/cli/monitor.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 68, "nosec": 0, "skipped_tests": 0}, "vibe_check/cli/output_formats.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 237, "nosec": 0, "skipped_tests": 0}, "vibe_check/cli/parallel_processing.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 284, "nosec": 0, "skipped_tests": 0}, "vibe_check/cli/standalone.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 358, "nosec": 0, "skipped_tests": 0}, "vibe_check/cli/unified_formatters.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 88, "nosec": 0, "skipped_tests": 0}, "vibe_check/cli/watch_mode.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 197, "nosec": 0, "skipped_tests": 0}, "vibe_check/compat.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 189, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 33, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/analysis/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 19, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/analysis/dependency_analyzer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 491, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/analysis/file_analyzer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 126, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/analysis/framework_detector.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 327, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/analysis/framework_rules.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 362, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/analysis/import_analyzer.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 346, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/analysis/import_visualizer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 48, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/analysis/meta_analyzer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 408, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/analysis/metrics_aggregator.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 104, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/analysis/performance_optimizer.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 330, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/analysis/project_analyzer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 150, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/analysis/project_meritocracy_analyzer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 380, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/analysis/python_semantic_analyzer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 383, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/analysis/python_version_analyzer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 294, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/analysis/result_processor.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 120, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/analysis/semantic_output_formatter.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 355, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/analysis/semantic_rules.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 342, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/analysis/standalone_analyzer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 334, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/analysis/tool_executor.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 108, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/analysis/type_analyzer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 274, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/analysis/visualization/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 37, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/analysis/visualization/chart_generators.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 183, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/analysis/visualization/html_generators.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 187, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/analysis/visualization/interactive_dashboard.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 181, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/async_unified_analyzer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 305, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/caching/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 101, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/caching/cache_engine.py": {"CONFIDENCE.HIGH": 3, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 2, "SEVERITY.UNDEFINED": 0, "loc": 417, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/caching/cache_invalidation.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 268, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/caching/cached_analyzer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 222, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/common_utils.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 40, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/compatibility.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 61, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/config.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 197, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/constants/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 223, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/constants/config_schema.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 248, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/constants/terminology.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 189, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/dependency_manager.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 285, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/docs/__init__.py": {"loc": 39, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/docs/templates.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 65, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/docs/utils.py": {"loc": 273, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/error_handling.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 206, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/error_handling/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 43, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/error_handling/collection_decorators.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 221, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/error_handling/decorators.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 93, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/error_handling/error_aggregator.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 258, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/error_handling/error_manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 85, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/error_handling/exceptions.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 156, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/error_handling/handlers.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 125, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/fs_utils.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 204, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/knowledge/framework_knowledge_base.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 544, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/knowledge/rule_engine.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 307, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/logging.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 187, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/logging/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 36, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/logging/contextual_logger.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 102, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/logging/correlation.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 162, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/logging/setup.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 64, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/logging/structured_logger.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 174, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/models.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 118, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/models/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 28, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/models/analysis_config.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 29, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/models/directory_metrics.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 206, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/models/file_metrics.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 345, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/models/progress_tracker.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 443, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/models/project_metrics.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 346, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/monitoring/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 18, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/monitoring/metrics_collector.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 247, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/monitoring/monitoring_engine.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 206, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/monitoring/query_engine.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 235, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/monitoring/time_series_storage.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 235, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/progress.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 778, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/simple_analyzer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 106, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/trend_analysis/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 10, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/trend_analysis/trend_analyzer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 246, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/trend_analysis/trend_storage.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 139, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/trend_analysis/trend_visualizer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 374, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/unified_analyzer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 274, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/utils/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 67, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/utils/async_utils.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 208, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/utils/config_utils.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 93, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/utils/dict_utils.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 150, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/utils/error_handling.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 99, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/utils/error_utils.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 252, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/utils/file_utils.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 115, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/utils/fs_utils.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 276, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/utils/gitignore_utils.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 52, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/utils/preset_manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 74, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/utils/report_utils.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 191, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/utils/tool_utils.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 105, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 31, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/auto_fix.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 254, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/cache.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 288, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/config.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 210, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/dependency_tracker.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 289, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/engine.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 811, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/incremental_analysis.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 256, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/integration/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 15, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/integration/ecosystem_integrator.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 28, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/integration/integration_manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 246, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/integration/meta_analyzer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 217, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/integration/unified_reporter.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 323, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/memory_manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 251, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/models.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 203, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/performance.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 301, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/plugins/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 17, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/plugins/plugin_interface.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 235, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/plugins/plugin_loader.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 177, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/plugins/plugin_manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 214, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/plugins/plugin_registry.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 222, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/registry.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 269, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/rules/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 9, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/rules/advanced_python_rules.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 316, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/rules/complexity_rules.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 270, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/rules/documentation_rules.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 261, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/rules/framework_rules/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 12, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/rules/framework_rules/django_rules.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 256, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/rules/framework_rules/fastapi_rules.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 314, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/rules/framework_rules/flask_rules.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 275, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/rules/framework_rules/framework_detector.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 247, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/rules/import_rules.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 282, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/rules/performance_rules.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 306, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/rules/rule_loader.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 344, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/rules/security_rules.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 235, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/rules/style_rules.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 236, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/rules/type_rules.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 364, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/vcs/type_checking.py": {"CONFIDENCE.HIGH": 3, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 3, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 258, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/version.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 6, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/visualization/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 88, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/visualization/async_dashboard_engine.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 356, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/visualization/dashboard_engine.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 389, "nosec": 0, "skipped_tests": 0}, "vibe_check/core/visualization/unified_charts.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 313, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 17, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/api/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 23, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/api/graphql.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 435, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/api/models.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 250, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/api/rest.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 390, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/api/unified.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 301, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/api/websocket.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 286, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/cicd/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 23, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/cicd/azure_devops.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 458, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/cicd/github_actions.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 306, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/cicd/gitlab_ci.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 355, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/cicd/jenkins.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 330, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/cicd/manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 314, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/cicd/models.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 240, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/collaboration.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 708, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/collaboration/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 24, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/collaboration/analysis.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 382, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/collaboration/shared_config.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 274, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/collaboration/team_manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 302, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/dashboard/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 25, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/dashboard/components.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 436, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/dashboard/models.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 227, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/dashboard/static_assets.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 405, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/dashboard/templates.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 654, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/dashboard/web_server.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 439, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/integration.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 22, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/monitoring.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 46, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/monitoring/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 28, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/monitoring/alerting.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 405, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/monitoring/dashboards.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 426, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/monitoring/models.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 261, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/monitoring/monitoring.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 367, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/monitoring/quality_gates.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 400, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/performance/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 22, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/performance/cache_manager.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 307, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/performance/distributed_processor.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 343, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/performance/load_balancer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 42, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/performance/performance_optimizer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 273, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/performance/scalability_manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 56, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/reporting/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 23, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/reporting/customization.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 82, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/reporting/engine.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 346, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/reporting/executive.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 338, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/reporting/formats.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 370, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/reporting/templates.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 332, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/security/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 28, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/security/access_control.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 374, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/security/audit_trail.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 389, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/security/compliance_manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 335, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/security/encryption_manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 77, "nosec": 0, "skipped_tests": 0}, "vibe_check/enterprise/security/security_monitor.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 267, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 110, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/alerting/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 35, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/alerting/alerting_engine.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 386, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/anomaly/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 35, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/anomaly/anomaly_detector.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 331, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/api/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 14, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/api/prometheus_api.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 403, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/collectors/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 49, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/collectors/base_collector.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 247, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/collectors/code_quality_collector.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 310, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/collectors/memory_collector.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 332, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/collectors/metrics_manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 275, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/collectors/network_collector.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 322, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/collectors/process_collector.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 241, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/collectors/profiling_collector.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 287, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/collectors/system_collector.py": {"CONFIDENCE.HIGH": 12, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 12, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 346, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/dashboard/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 32, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/dashboard/dashboard_components.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 360, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/discovery/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 27, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/discovery/service_discovery.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 351, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/infrastructure/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 30, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/infrastructure/system_monitor.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 445, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/ingestion/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 23, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/ingestion/high_frequency_ingester.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 328, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/instrumentation/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 60, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/instrumentation/framework_integrations.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 343, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/instrumentation/process_monitor.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 353, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/logging/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 65, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/logging/log_aggregation.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 447, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/memory/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 30, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/memory/memory_tracker.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 439, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/optimization/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 44, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/optimization/performance_optimizer.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 352, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/profiling/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 28, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/profiling/execution_profiler.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 384, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/query/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 24, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/query/promql_engine.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 325, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/storage/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 24, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/storage/compression_manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 342, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/storage/time_series_engine.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 336, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/streaming/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 30, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/streaming/stream_processor.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 349, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/tracing/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 78, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/tracing/distributed_tracing.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 381, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/ux/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 32, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/ux/ux_optimizer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 436, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/visualization/charts/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 90, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/visualization/charts/code_quality_charts.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 475, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/visualization/charts/infrastructure_topology.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 463, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/visualization/charts/time_series_charts.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 445, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/visualization/ui/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 47, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/visualization/ui/dashboard_ui.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 404, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/web/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 14, "nosec": 0, "skipped_tests": 0}, "vibe_check/monitoring/web/web_interface.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 437, "nosec": 0, "skipped_tests": 0}, "vibe_check/plugins/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 20, "nosec": 0, "skipped_tests": 0}, "vibe_check/plugins/base_plugin.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 13, "nosec": 0, "skipped_tests": 0}, "vibe_check/plugins/manager.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 381, "nosec": 0, "skipped_tests": 0}, "vibe_check/plugins/plugin_base.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 186, "nosec": 0, "skipped_tests": 0}, "vibe_check/plugins/plugin_interface.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 151, "nosec": 0, "skipped_tests": 0}, "vibe_check/plugins/plugin_registry.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 183, "nosec": 0, "skipped_tests": 0}, "vibe_check/tools/custom_rules/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 8, "nosec": 0, "skipped_tests": 0}, "vibe_check/tools/custom_rules/python_rules.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 273, "nosec": 0, "skipped_tests": 0}, "vibe_check/tools/parsers/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 21, "nosec": 0, "skipped_tests": 0}, "vibe_check/tools/parsers/bandit_parser.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 129, "nosec": 0, "skipped_tests": 0}, "vibe_check/tools/parsers/base_parser.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 62, "nosec": 0, "skipped_tests": 0}, "vibe_check/tools/parsers/complexity_parser.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 148, "nosec": 0, "skipped_tests": 0}, "vibe_check/tools/parsers/custom_rules_parser.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 110, "nosec": 0, "skipped_tests": 0}, "vibe_check/tools/parsers/doc_analyzer_parser.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 205, "nosec": 0, "skipped_tests": 0}, "vibe_check/tools/parsers/mypy_parser.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 119, "nosec": 0, "skipped_tests": 0}, "vibe_check/tools/parsers/parser_registry.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 42, "nosec": 0, "skipped_tests": 0}, "vibe_check/tools/parsers/pyflakes_parser.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 112, "nosec": 0, "skipped_tests": 0}, "vibe_check/tools/parsers/pylint_parser.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 105, "nosec": 0, "skipped_tests": 0}, "vibe_check/tools/parsers/ruff_parser.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 129, "nosec": 0, "skipped_tests": 0}, "vibe_check/tools/runners/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 52, "nosec": 0, "skipped_tests": 0}, "vibe_check/tools/runners/bandit_runner.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 123, "nosec": 0, "skipped_tests": 0}, "vibe_check/tools/runners/base_runner.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 174, "nosec": 0, "skipped_tests": 0}, "vibe_check/tools/runners/complexity_runner.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 273, "nosec": 0, "skipped_tests": 0}, "vibe_check/tools/runners/custom_rules_runner.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 75, "nosec": 0, "skipped_tests": 0}, "vibe_check/tools/runners/doc_analyzer_runner.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 246, "nosec": 0, "skipped_tests": 0}, "vibe_check/tools/runners/mypy_runner.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 171, "nosec": 0, "skipped_tests": 0}, "vibe_check/tools/runners/pyflakes_runner.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 97, "nosec": 0, "skipped_tests": 0}, "vibe_check/tools/runners/pylint_runner.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 155, "nosec": 0, "skipped_tests": 0}, "vibe_check/tools/runners/ruff_runner.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 164, "nosec": 0, "skipped_tests": 0}, "vibe_check/tools/runners/tool_registry.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 79, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/cli/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 14, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/cli/formatter.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 186, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/gui/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 16, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/gui/app.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 42, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/gui/main_window.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 256, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/gui/simple_gui.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 333, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/gui/themes.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 255, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/reporting/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 26, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/reporting/custom_report_generator.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 138, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/reporting/formatters.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 118, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/reporting/generators.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 140, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/reporting/markdown.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 89, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/reporting/report_generator.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 374, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/reporting/templates.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 69, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/tui/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 15, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/tui/app.py": {"loc": 167, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/tui/components.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 48, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/tui/config_components.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 75, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/tui/header_footer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 53, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/tui/menu_components.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 72, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/tui/progress_components.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 69, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/tui/results_components.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 235, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/tui/state_manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 208, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/visualization/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 45, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/visualization/charts.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 209, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/visualization/exporters.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 98, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/visualization/generators.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 273, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/visualization/interactive_charts.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 852, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/visualization/visualization_generator.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 196, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/web/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 16, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/web/app.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 140, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/web/components.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 451, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/web/run_web_ui.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 53, "nosec": 0, "skipped_tests": 0}, "vibe_check/ui/web/state_manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 192, "nosec": 0, "skipped_tests": 0}}, "results": [{"code": "229         import hashlib\n230         comment_id = hashlib.md5(f\"{comment_text}{line_number}\".encode()).hexdigest()[:8]\n231         \n", "col_offset": 21, "end_col_offset": 73, "filename": "vibe_check/ai/explanation/comment_analyzer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 327, "link": "https://cwe.mitre.org/data/definitions/327.html"}, "issue_severity": "HIGH", "issue_text": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "line_number": 230, "line_range": [230], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html", "test_id": "B324", "test_name": "<PERSON><PERSON><PERSON>"}, {"code": "391         import hashlib\n392         cache_key = hashlib.md5(code.encode()).hexdigest()\n393         \n", "col_offset": 20, "end_col_offset": 46, "filename": "vibe_check/ai/explanation/comment_analyzer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 327, "link": "https://cwe.mitre.org/data/definitions/327.html"}, "issue_severity": "HIGH", "issue_text": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "line_number": 392, "line_range": [392], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html", "test_id": "B324", "test_name": "<PERSON><PERSON><PERSON>"}, {"code": "322         import hashlib\n323         cache_key = hashlib.md5(\n324             f\"{code}{doc_format.value}{doc_style.value}{include_examples}\".encode()\n325         ).hexdigest()\n326         \n", "col_offset": 20, "end_col_offset": 9, "filename": "vibe_check/ai/explanation/documentation_generator.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 327, "link": "https://cwe.mitre.org/data/definitions/327.html"}, "issue_severity": "HIGH", "issue_text": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "line_number": 323, "line_range": [323, 324, 325], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html", "test_id": "B324", "test_name": "<PERSON><PERSON><PERSON>"}, {"code": "228         import hashlib\n229         cache_key = hashlib.md5(\n230             f\"{code}{explanation_type.value}{explanation_level.value}\".encode()\n231         ).hexdigest()\n232         \n", "col_offset": 20, "end_col_offset": 9, "filename": "vibe_check/ai/explanation/explanation_engine.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 327, "link": "https://cwe.mitre.org/data/definitions/327.html"}, "issue_severity": "HIGH", "issue_text": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "line_number": 229, "line_range": [229, 230, 231], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html", "test_id": "B324", "test_name": "<PERSON><PERSON><PERSON>"}, {"code": "374         import hashlib\n375         cache_key = hashlib.md5(code.encode()).hexdigest()\n376         \n", "col_offset": 20, "end_col_offset": 46, "filename": "vibe_check/ai/refactoring/code_smell_detector.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 327, "link": "https://cwe.mitre.org/data/definitions/327.html"}, "issue_severity": "HIGH", "issue_text": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "line_number": 375, "line_range": [375], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html", "test_id": "B324", "test_name": "<PERSON><PERSON><PERSON>"}, {"code": "404         import hashlib\n405         cache_key = hashlib.md5(\n406             f\"{code}{refactoring_type}{target_component}\".encode()\n407         ).hexdigest()\n408         \n", "col_offset": 20, "end_col_offset": 9, "filename": "vibe_check/ai/refactoring/impact_analyzer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 327, "link": "https://cwe.mitre.org/data/definitions/327.html"}, "issue_severity": "HIGH", "issue_text": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "line_number": 405, "line_range": [405, 406, 407], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html", "test_id": "B324", "test_name": "<PERSON><PERSON><PERSON>"}, {"code": "440         import hashlib\n441         cache_key = hashlib.md5(code.encode()).hexdigest()\n442         \n", "col_offset": 20, "end_col_offset": 46, "filename": "vibe_check/ai/refactoring/pattern_recommender.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 327, "link": "https://cwe.mitre.org/data/definitions/327.html"}, "issue_severity": "HIGH", "issue_text": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "line_number": 441, "line_range": [441], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html", "test_id": "B324", "test_name": "<PERSON><PERSON><PERSON>"}, {"code": "279         import hashlib\n280         cache_key = hashlib.md5(code.encode()).hexdigest()\n281         \n", "col_offset": 20, "end_col_offset": 46, "filename": "vibe_check/ai/refactoring/refactoring_engine.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 327, "link": "https://cwe.mitre.org/data/definitions/327.html"}, "issue_severity": "HIGH", "issue_text": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "line_number": 280, "line_range": [280], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html", "test_id": "B324", "test_name": "<PERSON><PERSON><PERSON>"}, {"code": "424         import hashlib\n425         cache_key = hashlib.md5(\n426             f\"{str(code_metrics)}{prediction_timeframe.days}\".encode()\n427         ).hexdigest()\n428         \n", "col_offset": 20, "end_col_offset": 9, "filename": "vibe_check/ai/temporal/debt_predictor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 327, "link": "https://cwe.mitre.org/data/definitions/327.html"}, "issue_severity": "HIGH", "issue_text": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "line_number": 425, "line_range": [425, 426, 427], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html", "test_id": "B324", "test_name": "<PERSON><PERSON><PERSON>"}, {"code": "464         import hashlib\n465         cache_key = hashlib.md5(\n466             f\"{entity_id}{entity_type}{len(raw_data_history)}\".encode()\n467         ).hexdigest()\n468         \n", "col_offset": 20, "end_col_offset": 9, "filename": "vibe_check/ai/temporal/productivity_analyzer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 327, "link": "https://cwe.mitre.org/data/definitions/327.html"}, "issue_severity": "HIGH", "issue_text": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "line_number": 465, "line_range": [465, 466, 467], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html", "test_id": "B324", "test_name": "<PERSON><PERSON><PERSON>"}, {"code": "134         # Generate snapshot ID\n135         snapshot_id = hashlib.md5(f\"{code}{timestamp.isoformat()}\".encode()).hexdigest()[:16]\n136         \n", "col_offset": 22, "end_col_offset": 76, "filename": "vibe_check/ai/temporal/temporal_engine.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 327, "link": "https://cwe.mitre.org/data/definitions/327.html"}, "issue_severity": "HIGH", "issue_text": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "line_number": 135, "line_range": [135], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html", "test_id": "B324", "test_name": "<PERSON><PERSON><PERSON>"}, {"code": "394         # Generate cache key\n395         cache_key = hashlib.md5(\n396             f\"{len(code_history)}{timeframe.value}{','.join(m.value for m in metrics)}\".encode()\n397         ).hexdigest()\n398         \n", "col_offset": 20, "end_col_offset": 9, "filename": "vibe_check/ai/temporal/temporal_engine.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 327, "link": "https://cwe.mitre.org/data/definitions/327.html"}, "issue_severity": "HIGH", "issue_text": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "line_number": 395, "line_range": [395, 396, 397], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html", "test_id": "B324", "test_name": "<PERSON><PERSON><PERSON>"}, {"code": "295         import hashlib\n296         viz_id = hashlib.md5(f\"{title}{len(time_series_data)}\".encode()).hexdigest()[:16]\n297         \n", "col_offset": 17, "end_col_offset": 72, "filename": "vibe_check/ai/temporal/trend_visualizer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 327, "link": "https://cwe.mitre.org/data/definitions/327.html"}, "issue_severity": "HIGH", "issue_text": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "line_number": 296, "line_range": [296], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html", "test_id": "B324", "test_name": "<PERSON><PERSON><PERSON>"}, {"code": "364         import hashlib\n365         viz_id = hashlib.md5(f\"{title}{len(datasets)}\".encode()).hexdigest()[:16]\n366         \n", "col_offset": 17, "end_col_offset": 64, "filename": "vibe_check/ai/temporal/trend_visualizer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 327, "link": "https://cwe.mitre.org/data/definitions/327.html"}, "issue_severity": "HIGH", "issue_text": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "line_number": 365, "line_range": [365], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html", "test_id": "B324", "test_name": "<PERSON><PERSON><PERSON>"}, {"code": "214             import hashlib\n215             cache_key = hashlib.md5(\n216                 f\"{len(data)}{len(aggregation_rules)}{start_time.isoformat()}\".encode()\n217             ).hexdigest()[:16]\n218         \n", "col_offset": 24, "end_col_offset": 13, "filename": "vibe_check/ai/visualization/data_aggregator.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 327, "link": "https://cwe.mitre.org/data/definitions/327.html"}, "issue_severity": "HIGH", "issue_text": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "line_number": 215, "line_range": [215, 216, 217], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html", "test_id": "B324", "test_name": "<PERSON><PERSON><PERSON>"}, {"code": "468         import hashlib\n469         report_id = hashlib.md5(f\"{title}{template_name}{datetime.now().isoformat()}\".encode()).hexdigest()[:16]\n470         \n", "col_offset": 20, "end_col_offset": 95, "filename": "vibe_check/ai/visualization/report_generator.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 327, "link": "https://cwe.mitre.org/data/definitions/327.html"}, "issue_severity": "HIGH", "issue_text": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "line_number": 469, "line_range": [469], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html", "test_id": "B324", "test_name": "<PERSON><PERSON><PERSON>"}, {"code": "341                         print(f\"  Average per file: {avg_time:.3f}s\")\n342                     except:\n343                         pass\n344 \n", "col_offset": 20, "end_col_offset": 28, "filename": "vibe_check/cli/commands.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 342, "line_range": [342, 343], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "45                 return [str(m) for m in python_matches]\n46     except Exception:\n47         pass\n48     \n", "col_offset": 4, "end_col_offset": 12, "filename": "vibe_check/cli/completion.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 46, "line_range": [46, 47], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "132     \"\"\"\n133     return os.path.normpath(os.getenv(\"TMPDIR\") or os.getenv(\"TMP\") or os.getenv(\"TEMP\") or \"/tmp\")\n134 \n", "col_offset": 92, "end_col_offset": 98, "filename": "vibe_check/compat.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 377, "link": "https://cwe.mitre.org/data/definitions/377.html"}, "issue_severity": "MEDIUM", "issue_text": "Probable insecure usage of temp file/directory.", "line_number": 133, "line_range": [133], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b108_hardcoded_tmp_directory.html", "test_id": "B108", "test_name": "hardcoded_tmp_directory"}, {"code": "253         \n254         except Exception:\n255             # If graph analysis fails, continue without circular dependency detection\n256             pass\n257         \n", "col_offset": 8, "end_col_offset": 16, "filename": "vibe_check/core/analysis/import_analyzer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 254, "line_range": [254, 255, 256], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "10 import logging\n11 import pickle\n12 import time\n", "col_offset": 0, "end_col_offset": 13, "filename": "vibe_check/core/analysis/performance_optimizer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 502, "link": "https://cwe.mitre.org/data/definitions/502.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with pickle module.", "line_number": 11, "line_range": [11], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b403-import-pickle", "test_id": "B403", "test_name": "blacklist"}, {"code": "101                 with open(cache_file, 'rb') as f:\n102                     self.cache = pickle.load(f)\n103                 logger.debug(f\"Loaded {len(self.cache)} cached results\")\n", "col_offset": 33, "end_col_offset": 47, "filename": "vibe_check/core/analysis/performance_optimizer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 502, "link": "https://cwe.mitre.org/data/definitions/502.html"}, "issue_severity": "MEDIUM", "issue_text": "Pickle and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue.", "line_number": 102, "line_range": [102], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_calls.html#b301-pickle", "test_id": "B301", "test_name": "blacklist"}, {"code": "11 import json\n12 import pickle\n13 import time\n", "col_offset": 0, "end_col_offset": 13, "filename": "vibe_check/core/caching/cache_engine.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 502, "link": "https://cwe.mitre.org/data/definitions/502.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with pickle module.", "line_number": 12, "line_range": [12], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b403-import-pickle", "test_id": "B403", "test_name": "blacklist"}, {"code": "371                 import gzip\n372                 return pickle.loads(gzip.decompress(f.read()))\n373             else:\n", "col_offset": 23, "end_col_offset": 62, "filename": "vibe_check/core/caching/cache_engine.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 502, "link": "https://cwe.mitre.org/data/definitions/502.html"}, "issue_severity": "MEDIUM", "issue_text": "Pickle and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue.", "line_number": 372, "line_range": [372], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_calls.html#b301-pickle", "test_id": "B301", "test_name": "blacklist"}, {"code": "373             else:\n374                 return pickle.load(f)\n375     \n", "col_offset": 23, "end_col_offset": 37, "filename": "vibe_check/core/caching/cache_engine.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 502, "link": "https://cwe.mitre.org/data/definitions/502.html"}, "issue_severity": "MEDIUM", "issue_text": "Pickle and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue.", "line_number": 374, "line_range": [374], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_calls.html#b301-pickle", "test_id": "B301", "test_name": "blacklist"}, {"code": "8 \n9 import subprocess\n10 import sys\n", "col_offset": 0, "end_col_offset": 17, "filename": "vibe_check/core/dependency_manager.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 9, "line_range": [9], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "223             # Use the package manager installation command\n224             result = subprocess.run(\n225                 [sys.executable, \"-m\", \"pip\", \"install\"] + install_cmd.split()[2:],\n226                 capture_output=True,\n227                 text=True,\n228                 timeout=300  # 5 minute timeout\n229             )\n230             \n", "col_offset": 21, "end_col_offset": 13, "filename": "vibe_check/core/dependency_manager.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 224, "line_range": [224, 225, 226, 227, 228, 229], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "163                 self.progress.stop()\n164             except Exception:\n165                 pass  # Ignore cleanup errors\n166         RichProgressTracker._live_display_active = False\n", "col_offset": 12, "end_col_offset": 20, "filename": "vibe_check/core/models/progress_tracker.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 164, "line_range": [164, 165], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "10 import json\n11 import pickle\n12 import time\n", "col_offset": 0, "end_col_offset": 13, "filename": "vibe_check/core/vcs/cache.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 502, "link": "https://cwe.mitre.org/data/definitions/502.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with pickle module.", "line_number": 11, "line_range": [11], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b403-import-pickle", "test_id": "B403", "test_name": "blacklist"}, {"code": "99             with open(result_path, 'rb') as f:\n100                 result = pickle.load(f)\n101             \n", "col_offset": 25, "end_col_offset": 39, "filename": "vibe_check/core/vcs/cache.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 502, "link": "https://cwe.mitre.org/data/definitions/502.html"}, "issue_severity": "MEDIUM", "issue_text": "Pickle and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue.", "line_number": 100, "line_range": [100], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_calls.html#b301-pickle", "test_id": "B301", "test_name": "blacklist"}, {"code": "8 import ast\n9 import subprocess\n10 import sys\n", "col_offset": 0, "end_col_offset": 17, "filename": "vibe_check/core/vcs/type_checking.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 9, "line_range": [9], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "249         try:\n250             subprocess.run([sys.executable, '-m', 'mypy', '--version'], \n251                          capture_output=True, check=True)\n252             return True\n", "col_offset": 12, "end_col_offset": 57, "filename": "vibe_check/core/vcs/type_checking.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 250, "line_range": [250, 251], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "265             # Run mypy with JSON output\n266             result = subprocess.run([\n267                 sys.executable, '-m', 'mypy',\n268                 '--show-error-codes',\n269                 '--no-error-summary',\n270                 str(file_path)\n271             ], capture_output=True, text=True)\n272             \n", "col_offset": 21, "end_col_offset": 46, "filename": "vibe_check/core/vcs/type_checking.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 266, "line_range": [266, 267, 268, 269, 270, 271], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "368                     update_data['panels'][panel.id] = {\n369                         'value': random.randint(10, 100)\n370                     }\n", "col_offset": 33, "end_col_offset": 56, "filename": "vibe_check/core/visualization/async_dashboard_engine.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 369, "line_range": [369], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "7 \n8 import xml.etree.ElementTree as ET\n9 from pathlib import Path\n", "col_offset": 0, "end_col_offset": 34, "filename": "vibe_check/enterprise/cicd/jenkins.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 20, "link": "https://cwe.mitre.org/data/definitions/20.html"}, "issue_severity": "LOW", "issue_text": "Using xml.etree.ElementTree to parse untrusted XML data is known to be vulnerable to XML attacks. Replace xml.etree.ElementTree with the equivalent defusedxml package, or make sure defusedxml.defuse_stdlib() is called.", "line_number": 8, "line_range": [8], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b405-import-xml-etree", "test_id": "B405", "test_name": "blacklist"}, {"code": "222                 try:\n223                     result = eval(expression, {\"__builtins__\": {}}, {})\n224                     return {\"result\": result, \"failed\": not bool(result)}\n", "col_offset": 29, "end_col_offset": 71, "filename": "vibe_check/enterprise/monitoring/quality_gates.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "MEDIUM", "issue_text": "Use of possibly insecure function - consider using safer ast.literal_eval.", "line_number": 223, "line_range": [223], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_calls.html#b307-eval", "test_id": "B307", "test_name": "blacklist"}, {"code": "237         key_data = str(args) + str(sorted(kwargs.items()))\n238         return hashlib.md5(key_data.encode()).hexdigest()\n239     \n", "col_offset": 15, "end_col_offset": 45, "filename": "vibe_check/enterprise/performance/cache_manager.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 327, "link": "https://cwe.mitre.org/data/definitions/327.html"}, "issue_severity": "HIGH", "issue_text": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "line_number": 238, "line_range": [238], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html", "test_id": "B324", "test_name": "<PERSON><PERSON><PERSON>"}, {"code": "419         flags_data = {\n420             \"storage.tsdb.path\": \"/tmp/vibe_check_tsdb\",\n421             \"web.listen-address\": f\"{self.host}:{self.port}\",\n422             \"log.level\": \"info\"\n423         }\n424         \n425         success_response = PrometheusResponse(\n", "col_offset": 33, "end_col_offset": 55, "filename": "vibe_check/monitoring/api/prometheus_api.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 377, "link": "https://cwe.mitre.org/data/definitions/377.html"}, "issue_severity": "MEDIUM", "issue_text": "Probable insecure usage of temp file/directory.", "line_number": 420, "line_range": [419, 420, 421, 422, 423], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b108_hardcoded_tmp_directory.html", "test_id": "B108", "test_name": "hardcoded_tmp_directory"}, {"code": "19 from typing import Dict, List, Optional, Any, Union\n20 import subprocess\n21 import platform\n", "col_offset": 0, "end_col_offset": 17, "filename": "vibe_check/monitoring/collectors/network_collector.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 20, "line_range": [20], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "299                 try:\n300                     import subprocess\n301                     result = subprocess.run(\n", "col_offset": 20, "end_col_offset": 37, "filename": "vibe_check/monitoring/collectors/system_collector.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 300, "line_range": [300], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "300                     import subprocess\n301                     result = subprocess.run(\n302                         [\"netstat\", \"-an\"], \n303                         capture_output=True, \n304                         text=True, \n305                         timeout=2\n306                     )\n307                     connection_count = len(result.stdout.splitlines()) - 2\n", "col_offset": 29, "end_col_offset": 21, "filename": "vibe_check/monitoring/collectors/system_collector.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 301, "line_range": [301, 302, 303, 304, 305, 306], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "300                     import subprocess\n301                     result = subprocess.run(\n302                         [\"netstat\", \"-an\"], \n303                         capture_output=True, \n304                         text=True, \n305                         timeout=2\n306                     )\n307                     connection_count = len(result.stdout.splitlines()) - 2\n", "col_offset": 29, "end_col_offset": 21, "filename": "vibe_check/monitoring/collectors/system_collector.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 301, "line_range": [301, 302, 303, 304, 305, 306], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "338                 try:\n339                     import subprocess\n340                     result = subprocess.run(\n", "col_offset": 20, "end_col_offset": 37, "filename": "vibe_check/monitoring/collectors/system_collector.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 339, "line_range": [339], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "339                     import subprocess\n340                     result = subprocess.run(\n341                         [\"ps\", \"aux\"], \n342                         capture_output=True, \n343                         text=True, \n344                         timeout=2\n345                     )\n346                     proc_count = len(result.stdout.splitlines()) - 1\n", "col_offset": 29, "end_col_offset": 21, "filename": "vibe_check/monitoring/collectors/system_collector.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 340, "line_range": [340, 341, 342, 343, 344, 345], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "339                     import subprocess\n340                     result = subprocess.run(\n341                         [\"ps\", \"aux\"], \n342                         capture_output=True, \n343                         text=True, \n344                         timeout=2\n345                     )\n346                     proc_count = len(result.stdout.splitlines()) - 1\n", "col_offset": 29, "end_col_offset": 21, "filename": "vibe_check/monitoring/collectors/system_collector.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 340, "line_range": [340, 341, 342, 343, 344, 345], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "398         try:\n399             import subprocess\n400             result = subprocess.run(\n", "col_offset": 12, "end_col_offset": 29, "filename": "vibe_check/monitoring/collectors/system_collector.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 399, "line_range": [399], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "399             import subprocess\n400             result = subprocess.run(\n401                 [\"sysctl\", \"hw.memsize\"], \n402                 capture_output=True, \n403                 text=True, \n404                 timeout=2\n405             )\n406             total_bytes = int(result.stdout.split(\":\")[1].strip())\n", "col_offset": 21, "end_col_offset": 13, "filename": "vibe_check/monitoring/collectors/system_collector.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 400, "line_range": [400, 401, 402, 403, 404, 405], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "399             import subprocess\n400             result = subprocess.run(\n401                 [\"sysctl\", \"hw.memsize\"], \n402                 capture_output=True, \n403                 text=True, \n404                 timeout=2\n405             )\n406             total_bytes = int(result.stdout.split(\":\")[1].strip())\n", "col_offset": 21, "end_col_offset": 13, "filename": "vibe_check/monitoring/collectors/system_collector.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 400, "line_range": [400, 401, 402, 403, 404, 405], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "444             elif self.is_darwin:\n445                 import subprocess\n446                 result = subprocess.run(\n", "col_offset": 16, "end_col_offset": 33, "filename": "vibe_check/monitoring/collectors/system_collector.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 445, "line_range": [445], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "445                 import subprocess\n446                 result = subprocess.run(\n447                     [\"sysctl\", \"-n\", \"kern.boottime\"], \n448                     capture_output=True, \n449                     text=True, \n450                     timeout=2\n451                 )\n452                 # Parse boot time and calculate uptime\n", "col_offset": 25, "end_col_offset": 17, "filename": "vibe_check/monitoring/collectors/system_collector.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 446, "line_range": [446, 447, 448, 449, 450, 451], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "445                 import subprocess\n446                 result = subprocess.run(\n447                     [\"sysctl\", \"-n\", \"kern.boottime\"], \n448                     capture_output=True, \n449                     text=True, \n450                     timeout=2\n451                 )\n452                 # Parse boot time and calculate uptime\n", "col_offset": 25, "end_col_offset": 17, "filename": "vibe_check/monitoring/collectors/system_collector.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 446, "line_range": [446, 447, 448, 449, 450, 451], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "144             import random\n145             return random.random() < self.config.sample_rate\n146         \n", "col_offset": 19, "end_col_offset": 34, "filename": "vibe_check/monitoring/instrumentation/framework_integrations.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 145, "line_range": [145], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "384                 metrics.memory_peak = max(metrics.memory_peak, memory_used)\n385             except Exception:\n386                 pass\n387     \n", "col_offset": 12, "end_col_offset": 20, "filename": "vibe_check/monitoring/instrumentation/process_monitor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 385, "line_range": [385, 386], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "441                 tracemalloc.stop()\n442             except Exception:\n443                 pass\n444 \n", "col_offset": 12, "end_col_offset": 20, "filename": "vibe_check/monitoring/instrumentation/process_monitor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 442, "line_range": [442, 443], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "267                         continue\n268             except:\n269                 pass\n270         \n", "col_offset": 12, "end_col_offset": 20, "filename": "vibe_check/monitoring/logging/log_aggregation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 268, "line_range": [268, 269], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "324         # Use hash for consistent key length\n325         return hashlib.md5(combined.encode()).hexdigest()\n326     \n", "col_offset": 15, "end_col_offset": 45, "filename": "vibe_check/monitoring/optimization/performance_optimizer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 327, "link": "https://cwe.mitre.org/data/definitions/327.html"}, "issue_severity": "HIGH", "issue_text": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "line_number": 325, "line_range": [325], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html", "test_id": "B324", "test_name": "<PERSON><PERSON><PERSON>"}, {"code": "314                     memory_start = tracemalloc.get_traced_memory()[0]\n315             except Exception:\n316                 pass\n317         \n", "col_offset": 12, "end_col_offset": 20, "filename": "vibe_check/monitoring/profiling/execution_profiler.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 315, "line_range": [315, 316], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "337                     memory_end = tracemalloc.get_traced_memory()[0]\n338             except Exception:\n339                 pass\n340         \n", "col_offset": 12, "end_col_offset": 20, "filename": "vibe_check/monitoring/profiling/execution_profiler.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 338, "line_range": [338, 339], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "295             positions[node.id] = (\n296                 random.uniform(50, width - 50),\n297                 random.uniform(50, height - 50)\n", "col_offset": 16, "end_col_offset": 46, "filename": "vibe_check/monitoring/visualization/charts/infrastructure_topology.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 296, "line_range": [296], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "296                 random.uniform(50, width - 50),\n297                 random.uniform(50, height - 50)\n298             )\n", "col_offset": 16, "end_col_offset": 47, "filename": "vibe_check/monitoring/visualization/charts/infrastructure_topology.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 297, "line_range": [297], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "164                         logger.error(f\"Error instantiating plugin {name}: {e}\")\n165             except Exception:\n166                 # Ignore errors getting attributes\n167                 pass\n168 \n", "col_offset": 12, "end_col_offset": 20, "filename": "vibe_check/plugins/manager.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 165, "line_range": [165, 166, 167], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "177                     self.hooks[plugin_type].append(item)\n178             except Exception:\n179                 # Ignore errors getting attributes\n180                 pass\n181 \n", "col_offset": 12, "end_col_offset": 20, "filename": "vibe_check/plugins/manager.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 178, "line_range": [178, 179, 180], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "182                     process.kill()\n183                 except Exception:\n184                     pass\n185 \n", "col_offset": 16, "end_col_offset": 24, "filename": "vibe_check/tools/runners/base_runner.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 183, "line_range": [183, 184], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "188             })\n189         except Exception:\n190             # If AST parsing fails, provide minimal analysis\n191             pass\n192 \n", "col_offset": 8, "end_col_offset": 16, "filename": "vibe_check/tools/runners/ruff_runner.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 189, "line_range": [189, 190, 191], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "13 from pathlib import Path\n14 import subprocess\n15 import sys\n", "col_offset": 0, "end_col_offset": 17, "filename": "vibe_check/ui/gui/simple_gui.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 14, "line_range": [14], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "331             \n332             result = subprocess.run(\n333                 cmd,\n334                 capture_output=True,\n335                 text=True,\n336                 timeout=300  # 5 minute timeout\n337             )\n338             \n", "col_offset": 21, "end_col_offset": 13, "filename": "vibe_check/ui/gui/simple_gui.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 332, "line_range": [332, 333, 334, 335, 336, 337], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}]}