{"timestamp": "2025-06-29T16:32:26.428422", "databases": {"sqlite_analysis_results": {"status": "healthy", "path": "/Users/<USER>/.vibe_check/analysis_results.db", "exists": true, "size_bytes": 299008, "connectivity": true, "schema_valid": true, "crud_operations": true, "integrity_check": true, "performance_baseline": {"connectivity_time": 7.081031799316406e-05, "schema_check_time": 0.001775979995727539, "crud_time": 0.0013229846954345703, "integrity_check_time": 0.003159046173095703}, "errors": [], "warnings": []}, "tsdb_time_series": {"status": "degraded", "data_dir": ".vibe_check_tsdb_test", "exists": true, "connectivity": true, "ingestion_test": false, "query_test": false, "compression_test": false, "performance_baseline": {"initialization_time": 0.00028204917907714844, "stats": {"series_count": 0, "total_samples": 0, "ingestion_rate": 0, "write_buffer_size": 0, "memory_usage_mb": 0.0, "cache_stats": {"cache": {"memory": {"hits": 0, "misses": 0, "evictions": 0, "disk_reads": 0, "disk_writes": 0, "memory_usage_bytes": 0, "disk_usage_bytes": 0, "hit_ratio": 0.0}, "disk": {"hits": 0, "misses": 0, "evictions": 0, "disk_reads": 0, "disk_writes": 0, "memory_usage_bytes": 0, "disk_usage_bytes": 0, "hit_ratio": 0.0}, "combined": {"total_hits": 0, "total_misses": 0, "total_hit_ratio": 0.0, "memory_usage_mb": 0.0, "disk_usage_mb": 0.0}}, "invalidation": {"invalidations_count": 0, "cascade_invalidations_count": 0, "file_change_events": 0, "dependency_count": 0, "rule_count": 0, "watch_paths_count": 0}}}}, "errors": ["Metric ingestion failed: '>' not supported between instances of 'dict' and 'float'"], "warnings": []}, "historical_trend_storage": {"status": "degraded", "storage_dir": "/Users/<USER>/.vibe_check/history", "exists": true, "writable": false, "readable": false, "json_format_valid": false, "performance_baseline": {"initialization_time": 4.1961669921875e-05}, "errors": ["Write test failed: property 'total_file_count' of 'ProjectMetrics' object has no setter"], "warnings": []}, "legacy_metrics_db": {"status": "not_found", "searched_paths": ["vibe_check_metrics.db", "/Users/<USER>/vibe_check_metrics.db", "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check_metrics.db"], "found_path": null, "size_bytes": 0, "connectivity": false, "errors": [], "warnings": ["Legacy database not found - this may be expected if not used"]}}, "summary": {"total_databases": 4, "healthy_databases": 1, "failed_databases": 2, "warnings": [], "health_percentage": 25.0, "recommendations": ["Investigate tsdb_time_series: ", "Investigate historical_trend_storage: "], "overall_status": "critical"}}