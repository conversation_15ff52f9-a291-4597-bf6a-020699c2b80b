[{"config_id": "default_rest_api", "name": "Vibe Check REST API", "host": "localhost", "port": 8000, "ssl_enabled": false, "ssl_cert_path": null, "ssl_key_path": null, "cors_enabled": true, "cors_origins": ["*"], "rate_limiting_enabled": true, "rate_limit_requests": 100, "rate_limit_window": 60, "authentication_enabled": false, "jwt_secret": null, "api_key_header": "X-API-Key"}, {"config_id": "default_graphql_api", "name": "Vibe Check GraphQL API", "host": "localhost", "port": 8001, "ssl_enabled": false, "ssl_cert_path": null, "ssl_key_path": null, "cors_enabled": true, "cors_origins": ["*"], "rate_limiting_enabled": true, "rate_limit_requests": 100, "rate_limit_window": 60, "authentication_enabled": false, "jwt_secret": null, "api_key_header": "X-API-Key"}, {"config_id": "default_websocket_api", "name": "Vibe Check WebSocket API", "host": "localhost", "port": 8002, "ssl_enabled": false, "ssl_cert_path": null, "ssl_key_path": null, "cors_enabled": true, "cors_origins": ["*"], "rate_limiting_enabled": false, "rate_limit_requests": 100, "rate_limit_window": 60, "authentication_enabled": false, "jwt_secret": null, "api_key_header": "X-API-Key"}]