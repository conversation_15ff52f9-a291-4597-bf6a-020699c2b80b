repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict
      - id: check-toml
      - id: debug-statements
      - id: mixed-line-ending

  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        language_version: python3
        exclude: ^legacy/

  - repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: v0.0.270
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]
        exclude: ^legacy/

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.3.0
    hooks:
      - id: mypy
        additional_dependencies: [types-PyYAML, types-requests]
        exclude: ^(legacy/|tests/|scripts/)

  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: [-c, pyproject.toml]
        additional_dependencies: ["bandit[toml]"]
        exclude: ^(legacy/|tests/)

  # Enhanced isort for import sorting
  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        name: Sort Python imports with isort
        args: [--profile=black, --line-length=88]

  # Documentation style checking
  - repo: https://github.com/pycqa/pydocstyle
    rev: 6.3.0
    hooks:
      - id: pydocstyle
        name: Check docstring style
        args: [--convention=google]
        exclude: ^(legacy/|tests/)

  # Custom Vibe Check quality checks
  - repo: local
    hooks:
      - id: vibe-check-constants
        name: Validate constants usage
        entry: python3 -c "
import sys
from pathlib import Path

# Check for hardcoded values that should use constants
hardcoded_patterns = [
    ('\"ruff\"', 'Use ToolNames.RUFF'),
    ('\"mypy\"', 'Use ToolNames.MYPY'),
    ('\"bandit\"', 'Use ToolNames.BANDIT'),
    ('\".py\"', 'Use FileExtensions.PYTHON'),
]

violations = []
for file_path in sys.argv[1:]:
    if file_path.endswith('.py') and 'constants' not in file_path.lower():
        try:
            with open(file_path, 'r') as f:
                content = f.read()
                for pattern, message in hardcoded_patterns:
                    if pattern in content:
                        violations.append(f'{file_path}: {message}')
        except Exception:
            pass

if violations:
    print('Constants validation violations:')
    for violation in violations[:5]:  # Show first 5
        print(f'  {violation}')
    print('Use centralized constants from vibe_check.core.constants')
    sys.exit(1)
"
        language: system
        files: \.py$
        pass_filenames: true

      - id: vibe-check-terminology
        name: Validate terminology consistency
        entry: python3 -c "
import sys
import re

# Check for deprecated terminology
deprecated_terms = {
    'analyse': 'analyze',
    'analyser': 'analyzer',
    'configuration': 'config',
}

violations = []
for file_path in sys.argv[1:]:
    if file_path.endswith('.py'):
        try:
            with open(file_path, 'r') as f:
                content = f.read()
                for deprecated, replacement in deprecated_terms.items():
                    if re.search(r'\b' + deprecated + r'\b', content, re.IGNORECASE):
                        violations.append(f'{file_path}: Use \"{replacement}\" instead of \"{deprecated}\"')
        except Exception:
            pass

if violations:
    print('Terminology validation violations:')
    for violation in violations[:5]:  # Show first 5
        print(f'  {violation}')
    sys.exit(1)
"
        language: system
        files: \.py$
        pass_filenames: true

      - id: pytest-check
        name: pytest-check
        entry: pytest
        language: system
        pass_filenames: false
        always_run: true
        stages: [push]
