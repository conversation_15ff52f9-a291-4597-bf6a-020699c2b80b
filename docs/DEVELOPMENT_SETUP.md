# Development Setup Guide

**Documentation Cycle: Strawberry | Updated: 28-06-2025**

This guide will help you set up a development environment for Vibe Check.

## Prerequisites

- Python 3.8 or higher
- Git
- Virtual environment tool (venv, conda, etc.)

## Quick Setup

1. **Clone the repository**:
   ```bash
   git clone https://github.com/ptzajac/vibe_check.git
   cd vibe_check
   ```

2. **Create and activate a virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install development dependencies**:
   ```bash
   pip install -e ".[dev]"
   ```

4. **Install pre-commit hooks** (recommended):
   ```bash
   pre-commit install
   ```

5. **Verify installation**:
   ```bash
   vibe-check --version
   pytest --version
   ```

## Project Structure

The project follows modern Python packaging standards:

- `vibe_check/` - Main package code
- `tests/` - Test suite
- `docs/` - Documentation
- `scripts/` - Development scripts
- `examples/` - Usage examples
- `test_projects/` - Test projects for analysis
- `legacy/` - Legacy and experimental code (not part of main codebase)

## Development Tools

The project uses several development tools configured in `pyproject.toml`:

### Code Formatting
- **Black**: Code formatter
- **isort**: Import sorter (via ruff)

### Code Quality
- **Ruff**: Fast Python linter
- **Mypy**: Static type checker
- **Bandit**: Security linter

### Testing
- **Pytest**: Test framework
- **Coverage**: Test coverage reporting

## Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=vibe_check

# Run specific test file
pytest tests/test_specific.py

# Run tests with verbose output
pytest -v
```

## Code Quality Checks

```bash
# Format code
black .

# Lint code
ruff check .

# Type checking
mypy .

# Security scanning
bandit -r vibe_check/

# Run all pre-commit hooks
pre-commit run --all-files
```

## Building and Installing

```bash
# Build the package
python -m build

# Install in development mode
pip install -e .

# Install with specific extras
pip install -e ".[web,tui]"
```

## Contributing

1. Create a feature branch
2. Make your changes
3. Add tests for new functionality
4. Ensure all tests pass
5. Run code quality checks
6. Update documentation if needed
7. Submit a pull request

## Troubleshooting

### Common Issues

1. **Import errors**: Make sure you've installed the package in development mode (`pip install -e .`)
2. **Test failures**: Check that all dependencies are installed (`pip install -e ".[dev]"`)
3. **Type checking errors**: Review mypy configuration in `pyproject.toml`

### Getting Help

- Check the documentation in `docs/`
- Look at examples in `examples/`
- Review test cases in `tests/`
- Open an issue on GitHub
