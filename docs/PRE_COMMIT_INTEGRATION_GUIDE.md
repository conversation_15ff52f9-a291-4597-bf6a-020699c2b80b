# Vibe Check Pre-commit Integration Guide

**Documentation Cycle: Strawberry**  
**Date: 29-06-2025**  
**Status: Complete Implementation**

## Overview

Vibe Check provides seamless integration with pre-commit hooks, enabling automated code quality enforcement during development workflows. This integration allows teams to catch issues early and maintain consistent code quality standards.

## Quick Start

### 1. Install Pre-commit Hooks

```bash
# Install hooks with default configuration
vibe-check install-hooks

# Install with specific validation level
vibe-check install-hooks --level strict

# Preview changes without installing
vibe-check install-hooks --dry-run
```

### 2. Validation Levels

**Minimal** (fastest, essential checks only):
- Basic syntax validation
- Critical security issues
- Import errors

**Standard** (balanced performance and coverage):
- Style violations (line length, whitespace)
- Code complexity issues
- Documentation requirements
- Import organization

**Strict** (comprehensive analysis):
- All VCS rules enabled
- Type checking integration
- Advanced security analysis
- Performance optimization hints

### 3. Configuration Options

```bash
# Merge with existing .pre-commit-config.yaml
vibe-check install-hooks --mode merge

# Create fresh configuration (overwrites existing)
vibe-check install-hooks --mode fresh

# Backup existing configuration
vibe-check install-hooks --backup
```

## Advanced Configuration

### Custom Hook Configuration

Create `.vibe-check-precommit.yaml` in your project root:

```yaml
# Vibe Check Pre-commit Configuration
validation_level: standard
timeout: 30
fail_fast: true

# Rule configuration
rules:
  enabled_categories:
    - style
    - security
    - complexity
  
  disabled_rules:
    - S001  # Line length (if using black)
    - D001  # Docstring requirements

# Performance settings
performance:
  parallel_processing: true
  max_workers: 4
  cache_enabled: true

# Integration settings
integration:
  external_tools:
    - ruff
    - mypy
  
  exclude_patterns:
    - "tests/"
    - "migrations/"
    - "__pycache__/"
```

### Generated .pre-commit-config.yaml

Example of generated configuration:

```yaml
# Generated by Vibe Check v0.3.0
repos:
  - repo: https://github.com/ptzajac/vibe_check
    rev: v0.3.0
    hooks:
      - id: vibe-check
        name: Vibe Check Analysis
        entry: vibe-check analyze
        language: python
        args: [
          "--vcs-mode",
          "--level", "standard",
          "--fast",
          "--no-save",
          "--quiet"
        ]
        files: \.py$
        exclude: ^(tests/|migrations/|__pycache__/)
        pass_filenames: true
        require_serial: false
```

## Performance Optimization

### Fast Execution Mode

Pre-commit integration automatically enables fast execution mode:

- **Changed Files Only**: Analyzes only modified files
- **Incremental Analysis**: Leverages git diff for file detection
- **Optimized Rules**: Runs essential rules first
- **Parallel Processing**: Concurrent file analysis
- **Memory Efficient**: Minimal memory footprint

### Performance Targets

| Validation Level | Target Time | File Limit |
|-----------------|-------------|------------|
| Minimal         | <10 seconds | 50+ files  |
| Standard        | <30 seconds | 30+ files  |
| Strict          | <60 seconds | 20+ files  |

### Performance Monitoring

```bash
# Check hook performance
vibe-check performance --hook-mode

# Analyze bottlenecks
vibe-check performance --detailed --hook-mode
```

## Integration Examples

### Basic Python Project

```bash
# 1. Install Vibe Check
pip install vibe-check

# 2. Install pre-commit
pip install pre-commit

# 3. Install Vibe Check hooks
vibe-check install-hooks

# 4. Install pre-commit hooks
pre-commit install

# 5. Test the setup
pre-commit run --all-files
```

### Django Project

```yaml
# .vibe-check-precommit.yaml
validation_level: standard
rules:
  enabled_categories:
    - style
    - security
    - complexity
  
  django_specific:
    - check_migrations: true
    - validate_models: true
    - security_middleware: true

exclude_patterns:
  - "migrations/"
  - "static/"
  - "media/"
```

### FastAPI Project

```yaml
# .vibe-check-precommit.yaml
validation_level: strict
integration:
  external_tools:
    - ruff
    - mypy
    - bandit
  
  fastapi_specific:
    - validate_routes: true
    - check_dependencies: true
    - security_headers: true
```

## Troubleshooting

### Common Issues

**1. Hook Execution Timeout**
```bash
# Increase timeout in .pre-commit-config.yaml
- id: vibe-check
  args: ["--timeout", "60"]
```

**2. Memory Issues with Large Files**
```bash
# Enable streaming mode
- id: vibe-check
  args: ["--stream-large-files"]
```

**3. Conflicting Rules with Other Tools**
```bash
# Disable conflicting rules
vibe-check install-hooks --disable-rules S001,S002
```

### Debug Mode

```bash
# Run with verbose output
vibe-check analyze --vcs-mode --verbose --hook-mode

# Check hook configuration
vibe-check install-hooks --dry-run --verbose
```

### Performance Issues

```bash
# Profile hook execution
vibe-check performance --profile --hook-mode

# Optimize for your project
vibe-check install-hooks --optimize
```

## Best Practices

### 1. Gradual Adoption

Start with minimal validation level and gradually increase:

```bash
# Week 1: Minimal
vibe-check install-hooks --level minimal

# Week 2: Standard
vibe-check install-hooks --level standard --mode merge

# Week 3: Strict
vibe-check install-hooks --level strict --mode merge
```

### 2. Team Configuration

Use shared configuration file:

```bash
# Commit .vibe-check-precommit.yaml to repository
git add .vibe-check-precommit.yaml
git commit -m "Add Vibe Check pre-commit configuration"
```

### 3. CI/CD Integration

```yaml
# .github/workflows/quality.yml
name: Code Quality
on: [push, pull_request]

jobs:
  quality:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      
      - name: Install dependencies
        run: |
          pip install vibe-check pre-commit
      
      - name: Run pre-commit
        run: pre-commit run --all-files
```

### 4. Performance Monitoring

```bash
# Regular performance checks
vibe-check performance --hook-mode --report weekly

# Optimize configuration based on metrics
vibe-check install-hooks --optimize --based-on-metrics
```

## Migration Guide

### From Manual Analysis

```bash
# Before: Manual analysis
vibe-check analyze . --vcs-mode

# After: Automated pre-commit
vibe-check install-hooks
git commit -m "Enable automated quality checks"
```

### From Other Tools

```bash
# Migrate from flake8/pylint
vibe-check install-hooks --migrate-from flake8

# Integrate with existing tools
vibe-check install-hooks --integrate-with ruff,mypy
```

## Support and Resources

### Documentation
- [Main Documentation](../README.md)
- [VCS Engine Guide](./VCS_ENGINE_GUIDE.md)
- [Configuration Reference](./CONFIGURATION_REFERENCE.md)

### Community
- [GitHub Issues](https://github.com/ptzajac/vibe_check/issues)
- [Discussions](https://github.com/ptzajac/vibe_check/discussions)

### Examples
- [Example Projects](../examples/)
- [Configuration Templates](../templates/)

---

**Next Steps**: After setting up pre-commit integration, consider exploring [Advanced VCS Features](./VCS_ADVANCED_FEATURES.md) for enhanced code analysis capabilities.
