# Vibe Check API Documentation

**Documentation Cycle: Strawberry | Updated: 28-06-2025**

This document provides detailed documentation for the Vibe Check Python API.

## Core API

### analyze_project

```python
def analyze_project(
    project_path: str,
    output_dir: Optional[str] = None,
    config_path: Optional[str] = None,
    config_override: Optional[Dict[str, Any]] = None,
    show_progress: bool = True
) -> ProjectMetrics:
    """
    Analyze a Python project and generate reports.
    
    Args:
        project_path: Path to the project to analyze
        output_dir: Directory to store output files (default: {project_path}/vibe_check_output)
        config_path: Path to a YAML config file
        config_override: Dictionary of config options to override
        show_progress: Whether to show progress information
        
    Returns:
        ProjectMetrics object containing analysis results
        
    Raises:
        FileNotFoundError: If the project path does not exist
        PermissionError: If the project path cannot be read
        ConfigError: If the config is invalid
    """
```

### ProjectMetrics

```python
class ProjectMetrics:
    """
    Container for project-level metrics.
    
    Attributes:
        project_path: Path to the analyzed project
        file_metrics: Dictionary mapping file paths to FileMetrics objects
        directory_metrics: Dictionary mapping directory paths to DirectoryMetrics objects
        tool_results: Dictionary mapping tool names to tool-specific results
        total_file_count: Total number of files analyzed
        total_line_count: Total number of lines in all files
        avg_complexity: Average complexity across all files
        max_complexity: Maximum complexity of any file
        issue_count: Total number of issues found
        issue_by_severity: Dictionary mapping severity levels to issue counts
    """
```

### FileMetrics

```python
class FileMetrics:
    """
    Container for file-level metrics.
    
    Attributes:
        path: Path to the file
        line_count: Number of lines in the file
        complexity: Cyclomatic complexity of the file
        issues: List of issues found in the file
        imports: List of imports in the file
        dependencies: List of dependencies of the file
        doc_coverage: Documentation coverage percentage
        type_coverage: Type annotation coverage percentage
    """
```

### DirectoryMetrics

```python
class DirectoryMetrics:
    """
    Container for directory-level metrics.
    
    Attributes:
        path: Path to the directory
        file_count: Number of files in the directory
        line_count: Total number of lines in all files in the directory
        avg_complexity: Average complexity of files in the directory
        max_complexity: Maximum complexity of any file in the directory
        issue_count: Total number of issues in the directory
    """
```

## ⚠️ DEPRECATED: Actor System API

**DEPRECATION NOTICE**: The Actor System API has been removed as part of Phase 0 stabilization. All analysis now uses the Simple Analyzer directly.

**Migration Path**: Use the Simple Analyzer API documented below instead of Actor System components.

---

## Simple Analyzer API

### simple_analyze_project

```python
def simple_analyze_project(
    project_path: Path,
    output_dir: Path,
    config: Optional[Dict[str, Any]] = None
) -> ProjectMetrics:
    """
    Analyze a project using the Simple Analyzer engine.

    This is the primary analysis function that provides comprehensive
    project analysis with a simple, straightforward interface.

    Args:
        project_path: Path to the project to analyze
        output_dir: Directory to write analysis results
        config: Optional config dictionary

    Returns:
        ProjectMetrics: Complete analysis results

    Example:
        ```python
        from vibe_check.core.simple_analyzer import simple_analyze_project
        from pathlib import Path

        metrics = simple_analyze_project(
            project_path=Path("./my_project"),
            output_dir=Path("./results"),
            config={"tools": {"ruff": {"enabled": True}}}
        )
        ```
    """
```

## Config API

### AnalysisConfig

```python
class AnalysisConfig:
    """
    Config for project analysis.
    
    Attributes:
        file_extensions: List of file extensions to analyze
        exclude_patterns: List of patterns to exclude
        analyze_docs: Whether to analyze documentation files
        max_workers: Maximum number of parallel workers
        tools: Dictionary mapping tool names to tool configurations
    """
```

## Tool API

### ToolRunner

```python
class ToolRunner:
    """
    Base class for tool runners.
    
    Tool runners execute analysis tools and process their output.
    
    Attributes:
        name: Name of the tool
        config: Tool-specific configuration
    """
    
    def is_available(self) -> bool:
        """
        Check if the tool is available on the system.
        
        Returns:
            True if the tool is available, False otherwise
        """
    
    async def run(self, file_path: str, context: ContextWave) -> Dict[str, Any]:
        """
        Run the tool on a file.
        
        Args:
            file_path: Path to the file to analyze
            context: Context wave for the analysis
            
        Returns:
            Dictionary containing the tool's results
        """
```

### ToolParser

```python
class ToolParser:
    """
    Base class for tool output parsers.
    
    Tool parsers process the raw output from tools and convert it into
    standardized data structures.
    
    Attributes:
        name: Name of the tool
    """
    
    def parse(self, output: str, file_path: str) -> Dict[str, Any]:
        """
        Parse the output of a tool.
        
        Args:
            output: Raw output from the tool
            file_path: Path to the file that was analyzed
            
        Returns:
            Dictionary containing the parsed results
        """
```

## UI API

### ReportGenerator

```python
class ReportGenerator:
    """
    Generator for analysis reports.
    
    Report generators create reports from analysis results.
    
    Attributes:
        output_dir: Directory to store output files
        format: Format of the reports
    """
    
    def generate_summary_report(self, metrics: ProjectMetrics) -> str:
        """
        Generate a summary report.
        
        Args:
            metrics: Project metrics
            
        Returns:
            Path to the generated report
        """
    
    def generate_issue_report(self, metrics: ProjectMetrics) -> str:
        """
        Generate an issue report.
        
        Args:
            metrics: Project metrics
            
        Returns:
            Path to the generated report
        """
```

### VisualizationGenerator

```python
class VisualizationGenerator:
    """
    Generator for visualizations.
    
    Visualization generators create visualizations from analysis results.
    
    Attributes:
        output_dir: Directory to store output files
        format: Format of the visualizations
    """
    
    def generate_dependency_graph(self, metrics: ProjectMetrics) -> str:
        """
        Generate a dependency graph.
        
        Args:
            metrics: Project metrics
            
        Returns:
            Path to the generated visualization
        """
    
    def generate_complexity_heatmap(self, metrics: ProjectMetrics) -> str:
        """
        Generate a complexity heatmap.
        
        Args:
            metrics: Project metrics
            
        Returns:
            Path to the generated visualization
        """
```

## Plugin API

### Plugin

```python
class Plugin:
    """
    Base class for plugins.
    
    Plugins extend the functionality of Vibe Check.
    
    Attributes:
        name: Name of the plugin
        version: Version of the plugin
        description: Description of the plugin
    """
    
    def initialize(self, actor_system: ActorSystem) -> None:
        """
        Initialize the plugin.
        
        Args:
            actor_system: Actor system to register with
        """
    
    def register_tools(self) -> List[Type[ToolRunner]]:
        """
        Register tool runners provided by the plugin.
        
        Returns:
            List of tool runner classes
        """
```

### PluginManager

```python
class PluginManager:
    """
    Manager for plugins.
    
    Plugin managers discover, load, and manage plugins.
    
    Attributes:
        plugins: List of loaded plugins
    """
    
    def list_plugins(self) -> List[Plugin]:
        """
        List all loaded plugins.
        
        Returns:
            List of loaded plugins
        """
    
    def load_plugin(self, name: str) -> Optional[Plugin]:
        """
        Load a plugin by name.
        
        Args:
            name: Name of the plugin
            
        Returns:
            Loaded plugin or None if the plugin could not be loaded
        """
```
