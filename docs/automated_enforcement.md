# Automated Enforcement System

**Documentation Cycle: Strawberry**  
**Date: 29-06-2025**  
**Status: Production Ready**

## Overview

The Automated Enforcement System provides comprehensive quality validation and enforcement through pre-commit hooks and CLI commands. It prevents regression of systematic improvements and ensures consistency across the development team.

## Features

### 🔧 **Core Validation Types**

1. **Constants Usage Validation** (`constants_usage`)
   - Detects hardcoded tool names that should use `ToolNames.*`
   - Identifies hardcoded file extensions that should use `FileExtensions.*`
   - Finds hardcoded thresholds that should use `AnalysisThresholds.*`
   - Excludes constants files and test files from validation

2. **Terminology Compliance** (`terminology`)
   - Enforces American spelling (analyze vs analyse)
   - Standardizes terminology (config vs configuration/settings)
   - Validates component naming conventions
   - Respects context exceptions (error handling contexts)

3. **Configuration Schema Validation** (`config_schema`)
   - Validates YAML/JSON configuration files
   - Checks required fields in known configuration files
   - Detects syntax errors in configuration files
   - Supports nested field validation

### 🚀 **Performance Metrics**

- **Analysis Time**: <1 second for typical file sets
- **Memory Usage**: Minimal overhead
- **Scalability**: Handles large projects efficiently
- **Integration**: Seamless pre-commit hook integration

## Usage

### CLI Commands

```bash
# Run comprehensive quality check
vibe-check quality check [path] [options]

# Generate detailed quality report
vibe-check quality report [path] [options]

# Auto-fix violations (coming soon)
vibe-check quality fix [path] [options]
```

#### Options

- `--severity {error,warning,info}`: Minimum severity level
- `--types {constants_usage,terminology,config_schema}`: Violation types to check
- `--output {text,json}`: Output format
- `--quiet`: Show summary only
- `--fail-on-violations`: Exit with error if violations found

### Pre-commit Hooks

The system provides multiple pre-commit hooks for different validation levels:

```yaml
# Comprehensive quality enforcement
- id: vibe-check-quality-enforcement
  args: [--severity=warning]

# Strict constants validation
- id: vibe-check-constants-strict
  args: [--types=constants_usage, --severity=error]

# Terminology consistency check
- id: vibe-check-terminology-check
  args: [--types=terminology, --severity=info]

# Configuration validation
- id: vibe-check-config-validation
  args: [--types=config_schema, --severity=error]
```

### Direct Script Usage

```bash
# Check specific files
python scripts/pre_commit_quality_check.py file1.py file2.py

# Check with specific severity
python scripts/pre_commit_quality_check.py --severity=error

# Check specific violation types
python scripts/pre_commit_quality_check.py --types=constants_usage,terminology
```

## Examples

### Constants Usage Violations

**❌ Violation:**
```python
def run_analysis():
    tool = "ruff"  # Should use ToolNames.RUFF
    return tool
```

**✅ Correct:**
```python
from vibe_check.core.constants import ToolNames

def run_analysis():
    tool = ToolNames.RUFF
    return tool
```

### Terminology Violations

**❌ Violation:**
```python
def analyse_project():  # Should be "analyze"
    configuration = load_settings()  # Should be "config"
    return analyser.run()  # Should be "analyzer"
```

**✅ Correct:**
```python
def analyze_project():
    config = load_config()
    return analyzer.run()
```

### Configuration Schema Violations

**❌ Violation:**
```yaml
# Missing required fields
version: "1.0"
```

**✅ Correct:**
```yaml
version: "1.0"
analysis:
  enabled: true
output:
  format: "text"
```

## Integration

### Pre-commit Setup

1. **Install pre-commit** (if not already installed):
   ```bash
   pip install pre-commit
   ```

2. **Install hooks**:
   ```bash
   pre-commit install
   ```

3. **Run manually**:
   ```bash
   pre-commit run vibe-check-quality-enforcement --all-files
   ```

### CI/CD Integration

```yaml
# GitHub Actions example
- name: Quality Enforcement
  run: |
    python scripts/pre_commit_quality_check.py \
      --severity=error \
      --fail-on-violations
```

## Configuration

### Severity Levels

- **ERROR**: Critical violations that must be fixed
- **WARNING**: Important violations that should be addressed
- **INFO**: Minor suggestions for improvement

### Violation Types

- **constants_usage**: Hardcoded values that should use constants
- **terminology**: Inconsistent terminology usage
- **config_schema**: Configuration file validation

### Exclusions

The system automatically excludes:
- Constants files (`*constants*`)
- Test files (`test_*`, `*_test.py`)
- Configuration files (`conftest.py`)
- Init files (`__init__.py`)
- Virtual environments (`venv`, `.venv`, etc.)
- Cache directories (`__pycache__`, `.pytest_cache`, etc.)

## Architecture

### Core Components

1. **AutomatedEnforcementEngine**: Main orchestration engine
2. **ConstantsValidator**: Validates constants usage
3. **TerminologyValidator**: Enforces terminology consistency
4. **ConfigSchemaValidator**: Validates configuration schemas
5. **QualityViolation**: Represents individual violations

### Extensibility

The system is designed for easy extension:

```python
from vibe_check.core.quality import AutomatedEnforcementEngine

# Add custom validator
engine = AutomatedEnforcementEngine()
engine.validators[ViolationType.CUSTOM] = CustomValidator()
```

## Performance Optimization

- **File Filtering**: Excludes irrelevant files automatically
- **Lazy Loading**: Loads validators only when needed
- **Efficient Parsing**: Uses optimized regex patterns
- **Caching**: Reuses parsed AST trees where possible

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure vibe_check is properly installed
2. **Permission Errors**: Check file permissions for script execution
3. **Performance Issues**: Use `--types` to limit validation scope

### Debug Mode

```bash
# Enable verbose output
python scripts/pre_commit_quality_check.py --verbose

# Check specific violation type
python scripts/pre_commit_quality_check.py --types=constants_usage
```

## Future Enhancements

- **Auto-fix Functionality**: Automatic violation correction
- **Custom Rules**: User-defined validation rules
- **IDE Integration**: Real-time validation in editors
- **Metrics Dashboard**: Quality trends visualization

---

## Strategic Benefits

✅ **Prevents Regression**: Maintains systematic improvements  
✅ **Ensures Consistency**: Standardizes code quality across team  
✅ **Immediate Feedback**: Catches issues before commit  
✅ **Automated Enforcement**: Reduces manual review overhead  
✅ **Configurable Validation**: Adapts to project needs  
✅ **Performance Optimized**: Fast execution for large codebases
