# Vibe Check Examples

This document provides examples of how to use Vibe Check in various scenarios.

## Basic Usage Examples

### Command Line Interface

#### Basic Analysis

Analyze a project with default config:

```bash
vibe-check analyze /path/to/your/project
```

#### Custom Output Directory

Specify a custom output directory:

```bash
vibe-check analyze /path/to/your/project --output ./results
```

#### Custom Config File

Use a custom config file:

```bash
vibe-check analyze /path/to/your/project --config ./my_config.yaml
```

#### Security-Focused Analysis

Run a security-focused analysis:

```bash
vibe-check analyze /path/to/your/project --security-focused
```

#### Quality-Focused Analysis

Run a quality-focused analysis:

```bash
vibe-check analyze /path/to/your/project --quality-focused
```

#### Verbose Output

Enable verbose output:

```bash
vibe-check analyze /path/to/your/project --verbose
```

#### JSON Output

Generate JSON output:

```bash
vibe-check analyze /path/to/your/project --format json
```

#### Text-Based UI

Launch the text-based UI:

```bash
vibe-check tui
```

#### Web Dashboard

Launch the web dashboard:

```bash
vibe-check web
```

### Python API

#### Basic Analysis

```python
from vibe_check import analyze_project

# Analyze a project with default config
metrics = analyze_project("/path/to/your/project")

# Print summary information
print(f"Total files analyzed: {metrics.total_file_count}")
print(f"Total lines of code: {metrics.total_line_count}")
print(f"Average complexity: {metrics.avg_complexity:.2f}")
print(f"Total issues: {metrics.issue_count}")
```

#### Custom Config

```python
from vibe_check import analyze_project

# Analyze a project with custom config
metrics = analyze_project(
    project_path="/path/to/your/project",
    output_dir="./results",
    config_path="./my_config.yaml",
    config_override={
        "analyze_docs": True,
        "max_workers": 4,
        "tools": {
            "ruff": {
                "enabled": True,
                "args": ["--select=E,F,W,I,C"]
            }
        }
    },
    show_progress=True
)
```

#### Working with Results

```python
from vibe_check import analyze_project

metrics = analyze_project("/path/to/your/project")

# Access file metrics
for file_path, file_metrics in metrics.file_metrics.items():
    print(f"File: {file_path}")
    print(f"  Lines: {file_metrics.line_count}")
    print(f"  Complexity: {file_metrics.complexity}")
    print(f"  Issues: {len(file_metrics.issues)}")
    
    # Access issues
    for issue in file_metrics.issues:
        print(f"  - {issue.code}: {issue.message} (line {issue.line})")

# Access directory metrics
for dir_path, dir_metrics in metrics.directory_metrics.items():
    print(f"Directory: {dir_path}")
    print(f"  Files: {dir_metrics.file_count}")
    print(f"  Average Complexity: {dir_metrics.avg_complexity:.2f}")

# Access tool results
for tool_name, tool_results in metrics.tool_results.items():
    print(f"Tool: {tool_name}")
    print(f"  Issues: {len(tool_results.get('issues', []))}")
```

## Advanced Usage Examples

### Custom Analysis Config

```python
from vibe_check import analyze_project
from vibe_check.core.models import AnalysisConfig

# Create a custom config
config = AnalysisConfig(
    file_extensions=[".py", ".pyx", ".pyi"],
    exclude_patterns=["**/venv/**", "**/tests/**", "**/.git/**"],
    tools={
        "ruff": {
            "enabled": True,
            "args": ["--select=E,F,W,I,C", "--ignore=E501"]
        },
        "mypy": {
            "enabled": True,
            "args": ["--ignore-missing-imports", "--disallow-untyped-defs"]
        },
        "bandit": {
            "enabled": True,
            "args": ["--recursive", "--severity-level=medium"]
        }
    },
    analyze_docs=True,
    max_workers=4
)

# Run analysis with custom config
metrics = analyze_project("/path/to/your/project", config=config)
```

### Using the Simple Analyzer Directly

⚠️ **Note**: The Actor System has been removed as part of Phase 0 stabilization. All analysis now uses the Simple Analyzer.

```python
from vibe_check.core.simple_analyzer import simple_analyze_project
from pathlib import Path

# Configure the analysis
config = {
    "tools": {
        "ruff": {"enabled": True},
        "mypy": {"enabled": True},
        "bandit": {"enabled": True}
    },
    "exclude_patterns": ["**/venv/**", "**/tests/**"],
    "file_extensions": [".py", ".pyx", ".pyi"]
}

# Run the analysis using Simple Analyzer
metrics = simple_analyze_project(
    project_path=Path("/path/to/your/project"),
    output_dir=Path("./results"),
    config=config
)

print(f"Analysis complete: {metrics.total_file_count} files analyzed")
print(f"Total issues found: {sum(len(fm.issues) for fm in metrics.file_metrics.values())}")
```

### Custom Tool Integration

```python
from vibe_check.tools.runners import ToolRunner
from vibe_check.tools.parsers import ToolParser
from vibe_check.tools.runners.tool_registry import register_tool
from vibe_check.tools.parsers.parser_registry import register_parser

# Create a custom tool runner
class CustomToolRunner(ToolRunner):
    def __init__(self, name, config=None):
        super().__init__(name, config)
    
    def is_available(self):
        # Check if the tool is available
        return True
    
    async def run(self, file_path, context):
        # Run the tool on the file
        # ...
        return {"issues": []}

# Create a custom tool parser
class CustomToolParser(ToolParser):
    def __init__(self):
        super().__init__("custom-tool")
    
    def parse(self, output, file_path):
        # Parse the tool output
        # ...
        return {"issues": []}

# Register the custom tool
register_tool("custom-tool", CustomToolRunner)
register_parser("custom-tool", CustomToolParser)

# Use the custom tool in analysis
from vibe_check import analyze_project

metrics = analyze_project(
    "/path/to/your/project",
    config_override={
        "tools": {
            "custom-tool": {
                "enabled": True,
                "args": []
            }
        }
    }
)
```

### Custom Report Generation

```python
from vibe_check import analyze_project
from vibe_check.ui.reporting import ReportGenerator

# Analyze a project
metrics = analyze_project("/path/to/your/project")

# Create a custom report generator
class CustomReportGenerator(ReportGenerator):
    def __init__(self, output_dir):
        super().__init__(output_dir, "custom")
    
    def generate_summary_report(self, metrics):
        # Generate a custom summary report
        # ...
        return f"{self.output_dir}/custom_summary.md"

# Generate custom reports
report_generator = CustomReportGenerator("./results")
summary_report_path = report_generator.generate_summary_report(metrics)
print(f"Custom summary report generated at: {summary_report_path}")
```

### Integration with CI/CD

#### GitHub Actions

```yaml
# .github/workflows/vibe-check.yml
name: Vibe Check Analysis

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  analyze:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.10'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install vibe-check[full]
    
    - name: Run Vibe Check
      run: |
        vibe-check analyze . --output ./vibe_check_results --format json
    
    - name: Upload results
      uses: actions/upload-artifact@v2
      with:
        name: vibe-check-results
        path: ./vibe_check_results
```

#### GitLab CI

```yaml
# .gitlab-ci.yml
vibe-check:
  image: python:3.10
  script:
    - pip install vibe-check[full]
    - vibe-check analyze . --output ./vibe_check_results --format json
  artifacts:
    paths:
      - ./vibe_check_results
```

#### Jenkins Pipeline

```groovy
// Jenkinsfile
pipeline {
    agent {
        docker {
            image 'python:3.10'
        }
    }
    
    stages {
        stage('Install') {
            steps {
                sh 'pip install vibe-check[full]'
            }
        }
        
        stage('Analyze') {
            steps {
                sh 'vibe-check analyze . --output ./vibe_check_results --format json'
            }
        }
        
        stage('Archive') {
            steps {
                archiveArtifacts artifacts: 'vibe_check_results/**/*', fingerprint: true
            }
        }
    }
}
```

### Programmatic Config

```python
import os
from vibe_check import analyze_project

# Get config from environment variables
output_dir = os.environ.get("VIBE_CHECK_OUTPUT_DIR", "./results")
max_workers = int(os.environ.get("VIBE_CHECK_MAX_WORKERS", "4"))
analyze_docs = os.environ.get("VIBE_CHECK_ANALYZE_DOCS", "true").lower() == "true"

# Configure tools based on environment
tools = {
    "ruff": {
        "enabled": os.environ.get("VIBE_CHECK_TOOLS_RUFF_ENABLED", "true").lower() == "true",
        "args": os.environ.get("VIBE_CHECK_TOOLS_RUFF_ARGS", "--select=E,F,W,I").split()
    },
    "mypy": {
        "enabled": os.environ.get("VIBE_CHECK_TOOLS_MYPY_ENABLED", "true").lower() == "true",
        "args": os.environ.get("VIBE_CHECK_TOOLS_MYPY_ARGS", "--ignore-missing-imports").split()
    }
}

# Run analysis with environment-based config
metrics = analyze_project(
    "/path/to/your/project",
    output_dir=output_dir,
    config_override={
        "max_workers": max_workers,
        "analyze_docs": analyze_docs,
        "tools": tools
    }
)
```

## Real-World Examples

### Analyzing a Django Project

```python
from vibe_check import analyze_project

# Configure for Django project analysis
metrics = analyze_project(
    "/path/to/django/project",
    config_override={
        "file_extensions": [".py", ".html", ".js", ".css"],
        "exclude_patterns": [
            "**/venv/**",
            "**/migrations/**",
            "**/static/vendor/**",
            "**/node_modules/**"
        ],
        "tools": {
            "ruff": {
                "enabled": True,
                "args": ["--select=E,F,W,I,DJ"]  # Include Django-specific rules
            },
            "bandit": {
                "enabled": True,
                "args": ["--recursive", "--severity-level=medium"]
            }
        }
    }
)

# Print Django-specific metrics
print(f"Django models: {len([f for f in metrics.file_metrics if 'models.py' in f])}")
print(f"Django views: {len([f for f in metrics.file_metrics if 'views.py' in f])}")
print(f"Django templates: {len([f for f in metrics.file_metrics if f.endswith('.html')])}")
```

### Analyzing a Flask Project

```python
from vibe_check import analyze_project

# Configure for Flask project analysis
metrics = analyze_project(
    "/path/to/flask/project",
    config_override={
        "file_extensions": [".py", ".html", ".js", ".css"],
        "exclude_patterns": [
            "**/venv/**",
            "**/static/vendor/**",
            "**/node_modules/**"
        ],
        "tools": {
            "ruff": {
                "enabled": True,
                "args": ["--select=E,F,W,I,S"]  # Include security rules
            },
            "bandit": {
                "enabled": True,
                "args": ["--recursive", "--severity-level=medium"]
            }
        }
    }
)

# Print Flask-specific metrics
print(f"Flask routes: {sum(len([l for l in file_metrics.issues if '@app.route' in l.message]) for file_metrics in metrics.file_metrics.values())}")
```

### Analyzing a Data Science Project

```python
from vibe_check import analyze_project

# Configure for data science project analysis
metrics = analyze_project(
    "/path/to/data/science/project",
    config_override={
        "file_extensions": [".py", ".ipynb"],
        "exclude_patterns": [
            "**/venv/**",
            "**/data/**",
            "**/.ipynb_checkpoints/**"
        ],
        "tools": {
            "ruff": {
                "enabled": True,
                "args": ["--select=E,F,W,I,NPY,PD"]  # Include numpy and pandas rules
            },
            "mypy": {
                "enabled": True,
                "args": ["--ignore-missing-imports", "--disallow-untyped-defs"]
            }
        }
    }
)

# Print data science-specific metrics
notebook_count = len([f for f in metrics.file_metrics if f.endswith('.ipynb')])
print(f"Jupyter notebooks: {notebook_count}")
```
