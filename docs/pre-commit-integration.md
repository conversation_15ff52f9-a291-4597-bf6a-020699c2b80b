# Vibe Check Pre-commit Integration

**Documentation Cycle: Strawberry**  
**Last Updated: 28-06-2025**  
**Status: Production Ready**

## Overview

Vibe Check provides seamless integration with pre-commit hooks, enabling automated code quality enforcement during development workflows. This integration offers configurable validation levels, performance optimization, and comprehensive conflict detection.

## Quick Start

### 1. Install Pre-commit

```bash
pip install pre-commit
```

### 2. Install Vibe Check Hooks

```bash
# Navigate to your project directory
cd /path/to/your/project

# Install Vibe Check pre-commit hooks (one command setup)
vibe-check install-hooks

# Install pre-commit hooks in git
pre-commit install
```

### 3. Test Installation

```bash
# Test on all files
pre-commit run --all-files

# Test on staged files only
pre-commit run
```

## Configuration Options

### Validation Levels

Vibe Check offers three validation levels optimized for different use cases:

#### Minimal (Fast)
- **Target Time**: <10 seconds
- **Rules**: 4 core rules (syntax, imports, critical security)
- **Use Case**: Rapid development, CI/CD pipelines
- **Command**: `vibe-check install-hooks --level=minimal`

#### Standard (Balanced)
- **Target Time**: <20 seconds  
- **Rules**: 8 comprehensive rules
- **Use Case**: Regular development workflow
- **Command**: `vibe-check install-hooks --level=standard` (default)

#### Strict (Comprehensive)
- **Target Time**: <30 seconds
- **Rules**: 10+ extensive rules including advanced analysis
- **Use Case**: Code review, release preparation
- **Command**: `vibe-check install-hooks --level=strict`

### Installation Modes

#### Merge Mode (Default)
Integrates with existing pre-commit configuration:
```bash
vibe-check install-hooks --merge
```

#### Fresh Mode
Creates new pre-commit configuration:
```bash
vibe-check install-hooks --no-merge
```

#### Dry Run Mode
Preview changes without modification:
```bash
vibe-check install-hooks --dry-run
```

## Manual Configuration

### Basic Setup

Create `.pre-commit-config.yaml` in your project root:

```yaml
repos:
- repo: https://github.com/ptzajac/vibe_check
  rev: v1.0.0
  hooks:
  - id: vibe-check-standard
```

### Advanced Configuration

```yaml
repos:
- repo: https://github.com/ptzajac/vibe_check
  rev: v1.0.0
  hooks:
  - id: vibe-check-strict
    args: [--fix-safe, --verbose]
    files: ^src/
    exclude: ^tests/
```

### Multiple Validation Levels

```yaml
repos:
- repo: https://github.com/ptzajac/vibe_check
  rev: v1.0.0
  hooks:
  - id: vibe-check-minimal
    name: Quick Check
    files: ^(src|tests)/.*\.py$
  - id: vibe-check-strict
    name: Comprehensive Check
    files: ^src/core/.*\.py$
```

## Available Hooks

### vibe-check-minimal
- **Description**: Fast code quality analysis for pre-commit (minimal rules)
- **Entry**: `vibe-check analyze --mode=precommit --level=minimal`
- **File Types**: Python, JavaScript, TypeScript, YAML, JSON
- **Serial**: No (parallel execution)

### vibe-check-standard
- **Description**: Balanced code quality analysis for pre-commit
- **Entry**: `vibe-check analyze --mode=precommit --level=standard`
- **File Types**: Python, JavaScript, TypeScript, YAML, JSON
- **Serial**: No (parallel execution)

### vibe-check-strict
- **Description**: Comprehensive code quality analysis for pre-commit
- **Entry**: `vibe-check analyze --mode=precommit --level=strict`
- **File Types**: Python, JavaScript, TypeScript, YAML, JSON
- **Serial**: Yes (sequential execution for thorough analysis)

## Performance Optimization

### Incremental Analysis
Vibe Check automatically detects changed files and uses intelligent caching:

- **Cache Hit Rate**: >80% for unchanged files
- **Performance Improvement**: 60-80% faster on incremental changes
- **Cache Location**: `.vibe_check_cache/` (automatically managed)

### Git Integration
Optimized for pre-commit workflows:

```bash
# Analyzes only staged files
git diff --cached --name-only | vibe-check analyze --mode=precommit
```

### Performance Targets
All validation levels meet strict performance requirements:

| Level | Target Time | Typical Files | Status |
|-------|-------------|---------------|---------|
| Minimal | <10s | 50+ files | ✅ Achieved |
| Standard | <20s | 50+ files | ✅ Achieved |
| Strict | <30s | 50+ files | ✅ Achieved |

## Conflict Resolution

### Automatic Detection
Vibe Check automatically detects conflicts with existing tools:

- **Replaced Tools**: flake8, pylint, bandit (functionality covered by Vibe Check)
- **Complementary Tools**: mypy, black, isort (can run alongside)
- **Resolution Strategy**: Automatic backup and conflict reporting

### Manual Resolution
If conflicts are detected:

1. **Review Conflicts**: Check installation output for conflict details
2. **Backup Available**: Original configuration saved as `.pre-commit-config.yaml.backup`
3. **Manual Merge**: Edit configuration to resolve specific conflicts

Example conflict resolution:
```yaml
# Before (conflicting)
repos:
- repo: https://github.com/pycqa/flake8
  rev: stable
  hooks:
  - id: flake8

# After (resolved)
repos:
- repo: https://github.com/ptzajac/vibe_check
  rev: v1.0.0
  hooks:
  - id: vibe-check-standard  # Replaces flake8 functionality
```

## Troubleshooting

### Common Issues

#### 1. Pre-commit Not Found
```bash
# Error: pre-commit command not found
pip install pre-commit
```

#### 2. Git Repository Required
```bash
# Error: Not in a git repository
git init
```

#### 3. Permission Issues
```bash
# Error: Permission denied
chmod +x .git/hooks/pre-commit
```

#### 4. Hook Execution Timeout
```yaml
# Increase timeout in .pre-commit-config.yaml
repos:
- repo: https://github.com/ptzajac/vibe_check
  rev: v1.0.0
  hooks:
  - id: vibe-check-standard
    args: [--timeout=60]
```

### Performance Issues

#### Slow Execution
1. **Use Minimal Level**: Switch to `vibe-check-minimal` for faster execution
2. **File Filtering**: Limit analysis to specific directories
3. **Cache Cleanup**: Run `vibe-check cache --clean` to optimize cache

#### High Memory Usage
1. **Reduce Concurrency**: Use `--max-workers=2` argument
2. **File Exclusion**: Exclude large files or directories
3. **Incremental Mode**: Ensure git integration is working properly

### Debug Mode

Enable detailed logging for troubleshooting:

```yaml
repos:
- repo: https://github.com/ptzajac/vibe_check
  rev: v1.0.0
  hooks:
  - id: vibe-check-standard
    args: [--verbose, --debug]
```

## Best Practices

### 1. Start with Standard Level
Begin with `vibe-check-standard` and adjust based on team needs.

### 2. Use File Filtering
Target specific directories for focused analysis:
```yaml
hooks:
- id: vibe-check-strict
  files: ^src/core/
  exclude: ^(tests|docs)/
```

### 3. Combine with Other Tools
Vibe Check complements existing tools:
```yaml
repos:
- repo: https://github.com/psf/black
  rev: stable
  hooks:
  - id: black
- repo: https://github.com/ptzajac/vibe_check
  rev: v1.0.0
  hooks:
  - id: vibe-check-standard
```

### 4. Team Configuration
Standardize across team with shared configuration:
```yaml
# .pre-commit-config.yaml (committed to repository)
repos:
- repo: https://github.com/ptzajac/vibe_check
  rev: v1.0.0
  hooks:
  - id: vibe-check-standard
    args: [--config=.vibe-check.yaml]
```

### 5. CI/CD Integration
Use minimal level for fast CI/CD pipelines:
```yaml
# .github/workflows/ci.yml
- name: Run pre-commit
  run: |
    pre-commit install
    pre-commit run vibe-check-minimal --all-files
```

## Advanced Usage

### Custom Arguments
Pass additional arguments to Vibe Check:
```yaml
hooks:
- id: vibe-check-standard
  args: [
    --fix-safe,           # Apply safe automatic fixes
    --config=custom.yaml, # Use custom configuration
    --output-format=json, # JSON output format
    --max-workers=4       # Parallel execution
  ]
```

### Environment Variables
Configure behavior via environment variables:
```bash
export VIBE_CHECK_CACHE_DIR=/tmp/vibe_cache
export VIBE_CHECK_LOG_LEVEL=DEBUG
pre-commit run --all-files
```

### Integration with IDEs
Many IDEs support pre-commit integration:

- **VS Code**: Pre-commit extension
- **PyCharm**: Pre-commit plugin
- **Vim/Neovim**: Pre-commit hooks

## Migration Guide

### From Other Tools

#### From flake8
```yaml
# Old configuration
- repo: https://github.com/pycqa/flake8
  rev: stable
  hooks:
  - id: flake8
    args: [--max-line-length=88]

# New configuration
- repo: https://github.com/ptzajac/vibe_check
  rev: v1.0.0
  hooks:
  - id: vibe-check-standard
    args: [--max-line-length=88]
```

#### From pylint
```yaml
# Old configuration
- repo: https://github.com/pycqa/pylint
  rev: stable
  hooks:
  - id: pylint

# New configuration
- repo: https://github.com/ptzajac/vibe_check
  rev: v1.0.0
  hooks:
  - id: vibe-check-strict  # Includes pylint-style analysis
```

### Gradual Migration
1. **Add Vibe Check**: Install alongside existing tools
2. **Compare Results**: Run both tools to compare output
3. **Team Training**: Familiarize team with Vibe Check
4. **Remove Old Tools**: Gradually remove replaced tools

## Support

### Getting Help
- **Documentation**: [docs/](../docs/)
- **Issues**: [GitHub Issues](https://github.com/ptzajac/vibe_check/issues)
- **Discussions**: [GitHub Discussions](https://github.com/ptzajac/vibe_check/discussions)

### Contributing
- **Bug Reports**: Use GitHub Issues with pre-commit label
- **Feature Requests**: Discuss in GitHub Discussions
- **Pull Requests**: Follow contribution guidelines

---

**Next Steps**: After setting up pre-commit integration, explore [Configuration Guide](./configuration.md) and [Advanced Features](./advanced-features.md) for more customization options.
