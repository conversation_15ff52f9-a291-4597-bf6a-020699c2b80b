# Historical Data Storage Recommendation

**Documentation Cycle: Strawberry**  
**Date: 28-06-2025**  
**Status: Recommendation**

## Executive Summary

Based on research and analysis of Vibe Check's requirements, **SQLite with JSON columns** is recommended as the optimal solution for implementing historical analysis data storage. This approach provides the best balance of simplicity, performance, and functionality for the project's needs.

## Requirements Analysis

### Functional Requirements
- Store analysis results over time for trend analysis
- Support fast queries for historical comparisons
- Handle varying data structures (different tool outputs)
- Enable cross-project comparisons
- Support data migration from current caching system

### Non-Functional Requirements
- **Free**: No licensing costs
- **Fast**: Sub-second query performance for typical operations
- **Simple**: Minimal setup and maintenance
- **Lightweight**: Single-file deployment
- **Python-friendly**: Good integration with existing codebase

## Database Options Comparison

### 1. SQLite (Recommended)

**Pros:**
- ✅ Zero configuration, single file
- ✅ Excellent Python integration (`sqlite3` built-in)
- ✅ JSON support since version 3.38 (2022)
- ✅ ACID compliance
- ✅ Fast for read-heavy workloads
- ✅ Cross-platform compatibility
- ✅ Mature and stable

**Cons:**
- ❌ Limited concurrent write performance
- ❌ No built-in replication

**Performance:** Excellent for Vibe Check's use case (single-user, read-heavy)

### 2. TinyDB (JSON-based)

**Pros:**
- ✅ Pure Python, no dependencies
- ✅ Document-oriented (natural for analysis results)
- ✅ Simple API

**Cons:**
- ❌ Slower than SQLite for large datasets
- ❌ No SQL querying capabilities
- ❌ Limited indexing options

### 3. DuckDB

**Pros:**
- ✅ Excellent analytical performance
- ✅ JSON support
- ✅ SQL interface

**Cons:**
- ❌ Additional dependency
- ❌ Overkill for current requirements

## Recommended Solution: SQLite with JSON

### Architecture Overview

```
Current System                    Enhanced System
┌─────────────────┐              ┌─────────────────┐
│ ProjectMetrics  │              │ ProjectMetrics  │
│ .to_dict()      │              │ .to_dict()      │
└─────────────────┘              └─────────────────┘
         │                                │
         ▼                                ▼
┌─────────────────┐              ┌─────────────────┐
│ Cache Files     │              │ SQLite Database │
│ (.vibe_check_   │              │ + Cache Files   │
│  cache/)        │              │                 │
└─────────────────┘              └─────────────────┘
```

### Database Schema Design

```sql
-- Analysis runs table
CREATE TABLE analysis_runs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_path TEXT NOT NULL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    profile TEXT NOT NULL,
    version TEXT,
    duration_seconds REAL,
    total_files INTEGER,
    total_lines INTEGER,
    total_issues INTEGER,
    metrics_json TEXT, -- Full ProjectMetrics.to_dict() output
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Issues table (normalized for efficient querying)
CREATE TABLE issues (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    run_id INTEGER REFERENCES analysis_runs(id),
    file_path TEXT NOT NULL,
    tool TEXT NOT NULL,
    severity TEXT NOT NULL,
    code TEXT,
    message TEXT,
    line_number INTEGER,
    column_number INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX idx_analysis_runs_project_timestamp ON analysis_runs(project_path, timestamp);
CREATE INDEX idx_issues_run_severity ON issues(run_id, severity);
CREATE INDEX idx_issues_file_tool ON issues(file_path, tool);
```

### Integration Points

#### 1. Storage Integration
```python
# vibe_check/core/storage/historical_storage.py
class HistoricalStorage:
    def store_analysis_result(self, project_path: str, metrics: ProjectMetrics, profile: str) -> int:
        """Store analysis result and return run_id"""
        
    def get_historical_trends(self, project_path: str, days: int = 30) -> List[Dict]:
        """Get trend data for specified period"""
        
    def compare_runs(self, run_id1: int, run_id2: int) -> Dict:
        """Compare two analysis runs"""
```

#### 2. Migration Strategy
```python
# vibe_check/core/storage/migration.py
class CacheMigration:
    def migrate_cache_to_db(self, cache_dir: str) -> None:
        """Migrate existing cache files to database"""
        
    def validate_migration(self) -> bool:
        """Validate migration completed successfully"""
```

### Performance Considerations

#### Expected Performance
- **Storage**: ~1-2 seconds per analysis result
- **Queries**: Sub-second for typical trend analysis
- **Database Size**: ~1MB per 1000 analysis runs

#### Optimization Strategies
1. **Batch Inserts**: Use transactions for bulk operations
2. **Indexing**: Strategic indexes on commonly queried fields
3. **JSON Queries**: Use SQLite JSON functions for complex queries
4. **Partitioning**: Consider date-based partitioning for large datasets

### Implementation Plan

#### Phase 1: Core Infrastructure (1-2 days)
1. Create database schema and migration scripts
2. Implement `HistoricalStorage` class
3. Add storage integration to analysis pipeline
4. Create migration utility for existing cache

#### Phase 2: Query Interface (1 day)
1. Implement trend analysis queries
2. Add comparison functionality
3. Create data export utilities

#### Phase 3: Integration (1 day)
1. Update CLI to show historical data
2. Add trend analysis to TUI
3. Implement data retention policies

### Configuration Integration

```yaml
# vibe_check/config/storage.yaml
storage:
  historical:
    enabled: true
    database_path: ".vibe_check/history.db"
    retention_days: 365
    auto_cleanup: true
    backup_enabled: true
    backup_interval_days: 7
```

### Migration from Current System

#### Current Cache Structure
```
.vibe_check_cache/
├── project_hash_1/
│   ├── analysis_result.json
│   └── metadata.json
└── project_hash_2/
    ├── analysis_result.json
    └── metadata.json
```

#### Migration Process
1. **Scan** existing cache directories
2. **Parse** cached analysis results
3. **Transform** to new schema format
4. **Insert** into SQLite database
5. **Validate** migration success
6. **Cleanup** old cache files (optional)

### Alternative Approaches Considered

#### 1. Pure JSON Files
- **Pros**: Simple, human-readable
- **Cons**: Poor query performance, no indexing

#### 2. PostgreSQL/MySQL
- **Pros**: Full SQL features, excellent performance
- **Cons**: Additional setup complexity, overkill for single-user tool

#### 3. NoSQL (MongoDB, etc.)
- **Pros**: Document-oriented, flexible schema
- **Cons**: Additional dependencies, complexity

## Conclusion

SQLite with JSON columns provides the optimal balance of simplicity, performance, and functionality for Vibe Check's historical data storage needs. The solution:

- ✅ Meets all functional and non-functional requirements
- ✅ Integrates seamlessly with existing `ProjectMetrics.to_dict()` serialization
- ✅ Provides excellent query performance for trend analysis
- ✅ Requires minimal additional dependencies
- ✅ Supports future enhancements (cross-project comparison, advanced analytics)

The recommended implementation can be completed in 3-4 days and will provide a solid foundation for advanced analytics capabilities.
