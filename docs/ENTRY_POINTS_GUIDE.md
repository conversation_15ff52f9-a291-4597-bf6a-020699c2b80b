# Vibe Check - Entry Points and Interfaces Guide

This document defines the **singular, canonical entry points** and interfaces for using Vibe Check. All legacy structures have been removed to ensure a clean, consistent user experience.

## 🎯 **Canonical Entry Points**

### 1. **Command Line Interface (Primary)**

**Single CLI Command:**
```bash
vibe-check analyze /path/to/project
```

**Available Commands:**
```bash
# Main analysis command
vibe-check analyze /path/to/project [CONFIG]

# Text User Interface
vibe-check tui /path/to/project

# Web Interface
vibe-check web /path/to/project

# Plugin Management
vibe-check plugin list
vibe-check plugin install <plugin-name>
vibe-check plugin uninstall <plugin-name>

# Debug Actor System
vibe-check debug /path/to/project [CONFIG]

# Version and Help
vibe-check --version
vibe-check --help
```

### 2. **Python Module Interface**

**Direct Module Execution:**
```bash
python -m vibe_check analyze /path/to/project
```

### 3. **Python API Interface**

**Primary API:**
```python
from vibe_check import analyze_project

# Simple usage
results = analyze_project('/path/to/project')

# Advanced usage
results = analyze_project(
    project_path='/path/to/project',
    config_path='./config.yaml',
    output_dir='./results',
    config_override={'analyze_docs': True},
    show_progress=True
)
```

**Available Imports:**
```python
from vibe_check import (
    analyze_project,      # Main analysis function
    FileMetrics,         # File-level metrics model
    ProjectMetrics,      # Project-level metrics model
    DirectoryMetrics,    # Directory-level metrics model
    __version__          # Version information
)
```

## 🚫 **Removed Legacy Interfaces**

The following legacy interfaces have been **completely removed**:

- ❌ `pat` command (legacy PAT alias)
- ❌ PAT-specific scripts and references
- ❌ Deprecated initialization package
- ❌ Legacy import paths
- ❌ Compatibility layers for old package names

## 📋 **Interface Standards**

### **CLI Standards**
- **Single command**: `vibe-check` (no aliases)
- **Consistent options**: All commands use standard option patterns
- **Clear help**: Every command has comprehensive help text
- **Error handling**: Consistent error messages and exit codes

### **Python API Standards**
- **Single entry point**: `analyze_project()` function
- **Type hints**: All public APIs have complete type annotations
- **Documentation**: Comprehensive docstrings for all public functions
- **Consistent returns**: Standardized return types across all functions

### **Config Standards**
- **YAML-based**: Primary config format
- **Preset support**: Built-in config presets
- **Environment variables**: Support for environment-based config
- **Override capability**: Runtime config overrides

## 🔧 **Usage Examples**

### **Basic Analysis**
```bash
# Analyze with default config
vibe-check analyze ./my-project

# Analyze with specific preset
vibe-check analyze ./my-project --preset comprehensive

# Analyze with custom output directory
vibe-check analyze ./my-project --output ./analysis-results
```

### **Advanced Analysis**
```bash
# Security-focused analysis
vibe-check analyze ./my-project --security-focused

# Analysis with custom config
vibe-check analyze ./my-project --config ./my-config.yaml

# Debug mode with detailed logging
vibe-check analyze ./my-project --debug --log-file ./debug.log
```

### **Python API Usage**
```python
import vibe_check

# Basic analysis
metrics = vibe_check.analyze_project('./my-project')
print(f"Total files: {metrics.total_file_count}")
print(f"Average complexity: {metrics.avg_complexity:.2f}")

# Access file-level metrics
for file_path, file_metrics in metrics.file_metrics.items():
    print(f"{file_path}: {len(file_metrics.issues)} issues")
```

## 📦 **Installation**

**Standard Installation:**
```bash
pip install vibe-check
```

**Development Installation:**
```bash
pip install -e ".[dev]"
```

**Full Installation (all features):**
```bash
pip install -e ".[full]"
```

## 🎯 **Key Benefits**

✅ **Single Source of Truth**: One canonical way to use each interface
✅ **No Confusion**: No legacy aliases or deprecated paths
✅ **Consistent Experience**: Same patterns across CLI and API
✅ **Clean Architecture**: Clear separation between interfaces
✅ **Future-Proof**: Modern Python packaging standards

## 📚 **Documentation**

- **CLI Help**: `vibe-check --help` and `vibe-check <command> --help`
- **API Documentation**: See `docs/API_DOCUMENTATION.md`
- **Configuration Guide**: See `docs/CLI_USAGE_GUIDE.md`
- **Development Setup**: See `docs/DEVELOPMENT_SETUP.md`

---

**Last Updated**: 2024-12-21  
**Status**: ✅ All legacy structures removed  
**Entry Points**: 🎯 Singular and canonical
