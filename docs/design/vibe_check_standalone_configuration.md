# Vibe Check Standalone - Configuration System

**Documentation Cycle: Strawberry | Updated: 28-06-2025**

## Overview

The Vibe Check Standalone configuration system provides flexible, hierarchical configuration management supporting CLI arguments, configuration files, and inheritance patterns. It integrates seamlessly with existing Vibe Check configuration while providing standalone operation capabilities.

## Configuration Hierarchy

### Priority Order (Highest to Lowest)
1. **CLI Arguments** - Direct command-line flags and options
2. **Environment Variables** - `VCS_*` prefixed variables
3. **Project Configuration** - `./vibe-check.yaml` or `./pyproject.toml`
4. **User Configuration** - `~/.vibe-check/config.yaml`
5. **Global Defaults** - Built-in default values

### Configuration Sources

#### 1. CLI Arguments
```bash
# Analysis configuration
vibe-lint check --rules style,security --severity warning --fix

# Output configuration  
vibe-lint check --format json --output results.json --verbose

# Performance configuration
vibe-lint check --parallel 4 --cache-dir .vibe-cache --incremental
```

#### 2. Environment Variables
```bash
# Engine configuration
export VCS_RULES="style,security,complexity"
export VCS_SEVERITY="warning"
export VCS_AUTO_FIX="true"

# Performance configuration
export VCS_PARALLEL_WORKERS="4"
export VCS_CACHE_ENABLED="true"
export VCS_INCREMENTAL="true"

# Integration configuration
export VCS_MODE="standalone"  # standalone | integrated | both
export VCS_EXTERNAL_TOOLS="ruff,mypy"
```

#### 3. Configuration Files

##### YAML Configuration (`vibe-check.yaml`)
```yaml
# Vibe Check Standalone Configuration
vibe_check_standalone:
  # Engine configuration
  engine:
    mode: "integrated"  # standalone | integrated | both
    parallel_workers: 4
    cache_enabled: true
    incremental: true
    
  # Rule configuration
  rules:
    # Enable/disable rule categories
    style:
      enabled: true
      severity: "warning"
      auto_fix: true
      
    security:
      enabled: true
      severity: "error"
      auto_fix: false
      
    complexity:
      enabled: true
      severity: "warning"
      max_complexity: 10
      max_function_length: 50
      
    documentation:
      enabled: true
      severity: "info"
      require_docstrings: true
      
    imports:
      enabled: true
      severity: "warning"
      organize_imports: true
      
    types:
      enabled: true
      severity: "warning"
      strict_mode: false
      
  # Specific rule configuration
  rule_overrides:
    "line-too-long":
      enabled: true
      max_length: 88
      
    "missing-docstring":
      enabled: true
      exclude_private: true
      
    "cyclomatic-complexity":
      enabled: true
      max_complexity: 10
      
  # Formatting configuration
  formatting:
    enabled: true
    line_length: 88
    indent_size: 4
    quote_style: "double"
    trailing_commas: true
    
  # Output configuration
  output:
    format: "text"  # text | json | yaml | sarif
    show_source: true
    show_fixes: true
    group_by: "file"  # file | rule | severity
    
  # Performance configuration
  performance:
    cache_dir: ".vibe-cache"
    max_memory: "512MB"
    timeout: "300s"
    
  # Integration configuration
  integration:
    external_tools:
      - "ruff"
      - "mypy"
      - "bandit"
    meta_analysis: true
    unified_reporting: true

# File and directory configuration
files:
  include:
    - "**/*.py"
    - "**/*.pyi"
  exclude:
    - "**/test_*.py"
    - "**/tests/**"
    - "**/__pycache__/**"
    - "**/venv/**"
    - "**/node_modules/**"
  
# Project-specific settings
project:
  name: "my-project"
  version: "1.0.0"
  python_version: "3.9"
  dependencies:
    - "requests"
    - "click"
```

##### TOML Configuration (`pyproject.toml`)
```toml
[tool.vibe-check-standalone]
# Engine configuration
[tool.vibe-check-standalone.engine]
mode = "integrated"
parallel_workers = 4
cache_enabled = true
incremental = true

# Rule configuration
[tool.vibe-check-standalone.rules.style]
enabled = true
severity = "warning"
auto_fix = true

[tool.vibe-check-standalone.rules.security]
enabled = true
severity = "error"
auto_fix = false

[tool.vibe-check-standalone.rules.complexity]
enabled = true
severity = "warning"
max_complexity = 10
max_function_length = 50

# Formatting configuration
[tool.vibe-check-standalone.formatting]
enabled = true
line_length = 88
indent_size = 4
quote_style = "double"
trailing_commas = true

# File patterns
[tool.vibe-check-standalone.files]
include = ["**/*.py", "**/*.pyi"]
exclude = ["**/test_*.py", "**/tests/**", "**/__pycache__/**"]
```

## Configuration Schema

### Core Configuration Types

```python
from typing import Dict, List, Optional, Union
from enum import Enum
from pydantic import BaseModel, Field

class EngineMode(str, Enum):
    STANDALONE = "standalone"
    INTEGRATED = "integrated"
    BOTH = "both"

class Severity(str, Enum):
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"
    HINT = "hint"

class OutputFormat(str, Enum):
    TEXT = "text"
    JSON = "json"
    YAML = "yaml"
    SARIF = "sarif"

class EngineConfig(BaseModel):
    """Core engine configuration."""
    mode: EngineMode = EngineMode.INTEGRATED
    parallel_workers: int = Field(default=4, ge=1, le=16)
    cache_enabled: bool = True
    incremental: bool = True
    timeout: str = "300s"

class RuleCategoryConfig(BaseModel):
    """Configuration for a rule category."""
    enabled: bool = True
    severity: Severity = Severity.WARNING
    auto_fix: bool = False

class RuleConfig(BaseModel):
    """Individual rule configuration."""
    enabled: bool = True
    severity: Severity = Severity.WARNING
    options: Dict[str, Union[str, int, bool, float]] = Field(default_factory=dict)

class FormattingConfig(BaseModel):
    """Code formatting configuration."""
    enabled: bool = True
    line_length: int = Field(default=88, ge=60, le=120)
    indent_size: int = Field(default=4, ge=2, le=8)
    quote_style: str = Field(default="double", regex="^(single|double)$")
    trailing_commas: bool = True

class OutputConfig(BaseModel):
    """Output formatting configuration."""
    format: OutputFormat = OutputFormat.TEXT
    show_source: bool = True
    show_fixes: bool = True
    group_by: str = Field(default="file", regex="^(file|rule|severity)$")

class FileConfig(BaseModel):
    """File inclusion/exclusion configuration."""
    include: List[str] = Field(default_factory=lambda: ["**/*.py", "**/*.pyi"])
    exclude: List[str] = Field(default_factory=lambda: [
        "**/test_*.py", "**/tests/**", "**/__pycache__/**"
    ])

class VibeCheckStandaloneConfig(BaseModel):
    """Complete VCS configuration."""
    engine: EngineConfig = Field(default_factory=EngineConfig)
    rules: Dict[str, RuleCategoryConfig] = Field(default_factory=dict)
    rule_overrides: Dict[str, RuleConfig] = Field(default_factory=dict)
    formatting: FormattingConfig = Field(default_factory=FormattingConfig)
    output: OutputConfig = Field(default_factory=OutputConfig)
    files: FileConfig = Field(default_factory=FileConfig)
```

## Configuration Management

### Configuration Manager Implementation

```python
class ConfigurationManager:
    """Manages hierarchical configuration loading and merging."""
    
    def __init__(self):
        self.config_cache = {}
        self.file_watchers = {}
        
    def load_config(self, 
                   project_path: Optional[Path] = None,
                   config_file: Optional[Path] = None,
                   cli_args: Optional[Dict[str, Any]] = None) -> VibeCheckStandaloneConfig:
        """Load and merge configuration from all sources."""
        
        # 1. Load global defaults
        config = self._load_default_config()
        
        # 2. Load user configuration
        user_config = self._load_user_config()
        config = self._merge_configs(config, user_config)
        
        # 3. Load project configuration
        if project_path:
            project_config = self._load_project_config(project_path)
            config = self._merge_configs(config, project_config)
            
        # 4. Load specific config file
        if config_file:
            file_config = self._load_config_file(config_file)
            config = self._merge_configs(config, file_config)
            
        # 5. Apply environment variables
        env_config = self._load_env_config()
        config = self._merge_configs(config, env_config)
        
        # 6. Apply CLI arguments
        if cli_args:
            cli_config = self._convert_cli_args(cli_args)
            config = self._merge_configs(config, cli_config)
            
        return config
        
    def _load_project_config(self, project_path: Path) -> Dict[str, Any]:
        """Load project-specific configuration."""
        config_files = [
            project_path / "vibe-check.yaml",
            project_path / "vibe-check.yml", 
            project_path / ".vibe-check.yaml",
            project_path / "pyproject.toml"
        ]
        
        for config_file in config_files:
            if config_file.exists():
                return self._load_config_file(config_file)
                
        return {}
        
    def _load_config_file(self, config_file: Path) -> Dict[str, Any]:
        """Load configuration from a specific file."""
        if config_file.suffix in ['.yaml', '.yml']:
            return self._load_yaml_config(config_file)
        elif config_file.suffix == '.toml':
            return self._load_toml_config(config_file)
        else:
            raise ValueError(f"Unsupported config file format: {config_file}")
            
    def _merge_configs(self, base: Dict[str, Any], 
                      override: Dict[str, Any]) -> Dict[str, Any]:
        """Deep merge configuration dictionaries."""
        result = base.copy()
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
                
        return result
```

### Configuration Profiles

#### Built-in Profiles
```yaml
# profiles/strict.yaml
vibe_check_standalone:
  rules:
    style:
      enabled: true
      severity: "error"
    security:
      enabled: true
      severity: "error"
    complexity:
      enabled: true
      severity: "error"
      max_complexity: 5
    documentation:
      enabled: true
      severity: "error"

# profiles/relaxed.yaml  
vibe_check_standalone:
  rules:
    style:
      enabled: true
      severity: "warning"
    security:
      enabled: true
      severity: "warning"
    complexity:
      enabled: false
    documentation:
      enabled: false

# profiles/security-focused.yaml
vibe_check_standalone:
  rules:
    security:
      enabled: true
      severity: "error"
    style:
      enabled: false
    complexity:
      enabled: false
```

#### Profile Usage
```bash
# Use built-in profile
vibe-lint check --profile strict

# Use custom profile
vibe-lint check --profile ./custom-profile.yaml

# Combine profile with overrides
vibe-lint check --profile strict --rules style --severity warning
```

## CLI Configuration Interface

### Configuration Commands
```bash
# Show current configuration
vibe-lint config show

# Show configuration for specific section
vibe-lint config show rules

# Set configuration value
vibe-lint config set rules.style.severity warning

# Create configuration file
vibe-lint config init --profile strict

# Validate configuration
vibe-lint config validate

# Show configuration sources
vibe-lint config sources
```

### Configuration Validation
```python
class ConfigValidator:
    """Validates configuration against schema."""
    
    def validate_config(self, config: Dict[str, Any]) -> ValidationResult:
        """Validate configuration and return errors/warnings."""
        
    def validate_rules(self, rules: Dict[str, Any]) -> List[ValidationError]:
        """Validate rule configuration."""
        
    def suggest_fixes(self, errors: List[ValidationError]) -> List[ConfigFix]:
        """Suggest fixes for configuration errors."""
```

## Integration Examples

### With Existing Vibe Check
```yaml
# Existing vibe-check.yaml
tools:
  ruff:
    enabled: true
  mypy:
    enabled: true

# Add VCS configuration
vibe_check_standalone:
  engine:
    mode: "integrated"
  rules:
    style:
      enabled: true
      # Complement ruff, don't duplicate
      focus_areas: ["naming", "documentation"]
```

### Standalone Operation
```yaml
# Standalone configuration
vibe_check_standalone:
  engine:
    mode: "standalone"
  rules:
    style:
      enabled: true
      severity: "warning"
    security:
      enabled: true
      severity: "error"
  output:
    format: "json"
    file: "analysis-results.json"
```

### Editor Integration
```yaml
# Editor-optimized configuration
vibe_check_standalone:
  engine:
    mode: "standalone"
    incremental: true
    cache_enabled: true
  rules:
    style:
      enabled: true
      auto_fix: true
  output:
    format: "lsp"
    real_time: true
```

## Migration and Compatibility

### Backward Compatibility
- Existing Vibe Check configurations continue to work unchanged
- VCS configuration is additive, not replacing
- Default mode is "integrated" to maintain current behavior
- CLI maintains existing command structure

### Migration Helpers
```bash
# Migrate from external tool configs
vibe-lint config migrate --from ruff --to vcs

# Import existing configuration
vibe-lint config import --from .ruff.toml --format ruff

# Generate equivalent VCS config
vibe-lint config equivalent --tool mypy
```

This configuration system provides the flexibility needed for both standalone operation and seamless integration with existing Vibe Check workflows, while maintaining the simplicity users expect from modern development tools.
