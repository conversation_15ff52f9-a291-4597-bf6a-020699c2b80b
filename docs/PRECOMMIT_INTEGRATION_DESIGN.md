# Pre-commit Integration Design for Vibe Check
# Documentation Cycle: Strawberry
# Date: 28-06-2025

## Strategic Overview

This document outlines the architectural design for integrating Vibe Check as a seamless pre-commit hook, enabling automated code quality enforcement during development workflows.

## Research Findings

### Successful Tool Patterns
- **Ruff**: Fast execution (<1s), configurable validation levels, proper exit codes
- **MyPy**: Incremental analysis, changed files focus, caching strategies
- **Black**: Simple configuration, reliable formatting, minimal user friction

### Key Success Factors
1. **Performance**: <30 seconds execution time for typical commits
2. **Simplicity**: One-command installation (`vibe-check install-hooks`)
3. **Configurability**: Multiple validation levels (minimal/standard/strict)
4. **Reliability**: Proper exit codes (0 for pass, non-zero for violations)
5. **Integration**: Seamless compatibility with existing pre-commit ecosystem

## Architecture Design

### 1. Fast Execution Mode

#### Core Strategy
```python
# Fast execution mode focuses on:
# 1. Changed files only (git diff detection)
# 2. Incremental analysis with caching
# 3. Optimized rule sets for pre-commit usage
# 4. Parallel processing where possible

class PreCommitMode:
    def __init__(self, validation_level: str = "standard"):
        self.validation_level = validation_level
        self.changed_files = self._get_changed_files()
        self.cache_manager = IncrementalCacheManager()
    
    def _get_changed_files(self) -> List[Path]:
        """Get files changed in current commit using git."""
        # Use git diff --cached --name-only for staged files
        # Use git diff --name-only for working directory changes
        pass
    
    async def analyze_fast(self) -> AnalysisResult:
        """Optimized analysis for pre-commit usage."""
        # 1. Filter to changed files only
        # 2. Use cached results for unchanged dependencies
        # 3. Apply validation level-specific rules
        # 4. Return with proper exit codes
        pass
```

#### Performance Optimizations
- **Git Integration**: Detect changed files using `git diff --cached --name-only`
- **Incremental Caching**: Cache analysis results by file hash
- **Rule Filtering**: Apply only relevant rules based on validation level
- **Parallel Processing**: Analyze multiple files concurrently
- **Early Exit**: Stop on first critical violation (configurable)

### 2. Validation Levels

#### Minimal Level (Target: <10 seconds)
```yaml
# Focus on critical issues only
rules:
  - syntax_errors
  - import_errors
  - security_critical
  - type_safety_basic
```

#### Standard Level (Target: <20 seconds)
```yaml
# Balanced quality checks
rules:
  - minimal_rules
  - code_complexity
  - style_violations
  - documentation_basic
  - performance_basic
```

#### Strict Level (Target: <30 seconds)
```yaml
# Comprehensive analysis
rules:
  - standard_rules
  - advanced_patterns
  - architecture_violations
  - comprehensive_documentation
  - performance_advanced
```

### 3. Hook Generation System

#### .pre-commit-hooks.yaml Structure
```yaml
- id: vibe-check-minimal
  name: Vibe Check (Minimal)
  description: "Fast code quality analysis for pre-commit (minimal rules)"
  entry: vibe-check analyze --mode=precommit --level=minimal
  language: python
  types_or: [python, javascript, typescript, yaml, json]
  args: []
  require_serial: false
  minimum_pre_commit_version: "2.9.2"

- id: vibe-check-standard
  name: Vibe Check (Standard)
  description: "Balanced code quality analysis for pre-commit"
  entry: vibe-check analyze --mode=precommit --level=standard
  language: python
  types_or: [python, javascript, typescript, yaml, json]
  args: []
  require_serial: false
  minimum_pre_commit_version: "2.9.2"

- id: vibe-check-strict
  name: Vibe Check (Strict)
  description: "Comprehensive code quality analysis for pre-commit"
  entry: vibe-check analyze --mode=precommit --level=strict
  language: python
  types_or: [python, javascript, typescript, yaml, json]
  args: []
  require_serial: true
  minimum_pre_commit_version: "2.9.2"
```

#### Auto-Generated Configuration
```python
def generate_precommit_config(
    project_path: Path,
    validation_level: str = "standard",
    custom_rules: Optional[List[str]] = None
) -> str:
    """Generate .pre-commit-config.yaml with Vibe Check integration."""
    
    config = {
        "repos": [
            {
                "repo": "https://github.com/ptzajac/vibe_check",
                "rev": f"v{get_current_version()}",
                "hooks": [
                    {
                        "id": f"vibe-check-{validation_level}",
                        "args": custom_rules or []
                    }
                ]
            }
        ]
    }
    
    return yaml.dump(config, default_flow_style=False)
```

### 4. Install-Hooks Command

#### Command Interface
```bash
# Basic installation
vibe-check install-hooks

# With validation level
vibe-check install-hooks --level=strict

# With custom configuration
vibe-check install-hooks --config=custom-precommit.yaml

# Integration with existing config
vibe-check install-hooks --merge
```

#### Implementation Strategy
```python
class PreCommitInstaller:
    def __init__(self, project_path: Path):
        self.project_path = project_path
        self.config_path = project_path / ".pre-commit-config.yaml"
    
    def install(
        self,
        validation_level: str = "standard",
        merge_existing: bool = True,
        custom_config: Optional[Path] = None
    ) -> InstallResult:
        """Install Vibe Check as pre-commit hook."""
        
        # 1. Check for existing pre-commit configuration
        # 2. Validate compatibility and detect conflicts
        # 3. Generate or merge Vibe Check configuration
        # 4. Install pre-commit hooks (pre-commit install)
        # 5. Validate installation with test run
        
        return InstallResult(
            success=True,
            config_path=self.config_path,
            validation_level=validation_level,
            conflicts_resolved=[]
        )
```

### 5. Exit Code Compliance

#### Exit Code Strategy
```python
class PreCommitExitCodes:
    SUCCESS = 0          # No violations found
    VIOLATIONS_FOUND = 1 # Quality violations detected
    ANALYSIS_ERROR = 2   # Analysis failed (tool error)
    CONFIG_ERROR = 3     # Configuration issues
    
    @staticmethod
    def from_analysis_result(result: AnalysisResult) -> int:
        """Convert analysis result to appropriate exit code."""
        if result.has_errors():
            return PreCommitExitCodes.ANALYSIS_ERROR
        elif result.has_violations():
            return PreCommitExitCodes.VIOLATIONS_FOUND
        else:
            return PreCommitExitCodes.SUCCESS
```

#### Integration with Pre-commit Framework
```python
async def precommit_main(args: List[str]) -> int:
    """Main entry point for pre-commit integration."""
    try:
        # Parse pre-commit specific arguments
        config = PreCommitConfig.from_args(args)
        
        # Run fast analysis mode
        analyzer = PreCommitAnalyzer(config)
        result = await analyzer.analyze_changed_files()
        
        # Output results in pre-commit compatible format
        formatter = PreCommitFormatter()
        formatter.output_results(result)
        
        # Return appropriate exit code
        return PreCommitExitCodes.from_analysis_result(result)
        
    except Exception as e:
        logger.error(f"Pre-commit analysis failed: {e}")
        return PreCommitExitCodes.ANALYSIS_ERROR
```

## Implementation Roadmap

### Phase 1: Core Infrastructure (Day 1)
- [ ] Fast execution mode implementation
- [ ] Git integration for changed file detection
- [ ] Incremental caching system
- [ ] Basic validation levels

### Phase 2: Hook Generation (Day 2)
- [ ] .pre-commit-hooks.yaml creation
- [ ] Configuration generation system
- [ ] Exit code compliance implementation
- [ ] Basic testing framework

### Phase 3: Installation System (Day 3)
- [ ] install-hooks command implementation
- [ ] Existing configuration merging
- [ ] Conflict detection and resolution
- [ ] Comprehensive testing and validation

## Success Metrics

### Performance Targets
- **Minimal Level**: <10 seconds execution time
- **Standard Level**: <20 seconds execution time
- **Strict Level**: <30 seconds execution time
- **Cache Hit Rate**: >80% for incremental analysis

### Integration Targets
- **Installation Success**: >95% success rate across different projects
- **Compatibility**: Works with existing pre-commit configurations
- **User Adoption**: Seamless developer experience

### Quality Targets
- **False Positive Rate**: <5% for standard level
- **Coverage**: Detects >90% of critical quality issues
- **Reliability**: <1% failure rate in pre-commit execution

## Competitive Advantages

1. **Comprehensive Analysis**: Unlike single-purpose tools, provides holistic code quality assessment
2. **Multi-Language Support**: Supports Python, JavaScript, TypeScript, YAML, JSON
3. **Configurable Depth**: Three validation levels for different project needs
4. **Smart Caching**: Incremental analysis reduces repeated work
5. **Ecosystem Integration**: Seamless compatibility with existing pre-commit workflows

## Risk Mitigation

### Performance Risks
- **Mitigation**: Extensive benchmarking and optimization
- **Fallback**: Graceful degradation to basic analysis if timeout

### Compatibility Risks
- **Mitigation**: Comprehensive testing across different project types
- **Fallback**: Non-destructive installation with rollback capability

### Adoption Risks
- **Mitigation**: Excellent documentation and examples
- **Fallback**: Optional integration that doesn't disrupt existing workflows

## Conclusion

This design provides a comprehensive foundation for integrating Vibe Check as a pre-commit hook while maintaining the performance, reliability, and ease-of-use that developers expect from modern development tools.

The phased implementation approach ensures systematic delivery while the configurable validation levels provide flexibility for different project needs and team preferences.
