# Meta-System Separation Analysis for person_suit

This tool analyzes the separation between meta-systems in the person_suit project. Meta-systems should not import from each other directly, but can use common underlying components from person_suit.

## What is Meta-System Separation?

In the person_suit architecture, meta-systems are high-level components that should be independent of each other. They include:

- **PC (Persona Core)**: The core persona system
- **AN (Analyst)**: The analysis system
- **PR (Predictor)**: The prediction system
- **SIO (Shared I/O)**: The shared input/output system
- **UI (User Interface)**: The user interface system

These meta-systems should not import from each other directly. Instead, they should only use common underlying components from the core.

## Running the Analysis

There are several ways to run the meta-system separation analysis:

### 1. Using the Command-Line Script

```bash
./run_meta_system_check.sh /path/to/person_suit
```

This script:
- Activates the virtual environment
- Runs the meta-system separation analysis
- Generates visualizations
- Opens the interactive report in your browser

### 2. Using the Web UI

```bash
./run_meta_system_webui.sh
```

This script:
- Activates the virtual environment
- Starts the PAT Web UI
- Allows you to run the meta-system separation analysis through the web interface
- Provides interactive visualizations and detailed reports

### 3. Using the Python API

```python
from PAT_tool.meta_system_analyzer import MetaSystemAnalyzer
from PAT_tool.meta_system_visualizer import MetaSystemVisualizer
from PAT_tool.models import ProjectMetrics
from PAT_tool.utils import ProgressTracker

# Set up the analyzer
project_path = "/path/to/person_suit"
metrics = ProjectMetrics()
progress = ProgressTracker()
analyzer = MetaSystemAnalyzer(project_path, metrics, progress)

# Run the analysis
analyzer.analyze()

# Get results
results = metrics.tool_results.get("meta_system_separation", {})
violations = results.get("violations", [])

# Generate visualizations
output_dir = "analysis"
project_name = "person_suit"
visualizer = MetaSystemVisualizer(project_name, metrics, output_dir)
visualizer.generate()
```

## Understanding the Results

The analysis generates several outputs:

1. **Console Output**: Lists any violations found
2. **Static Graph**: A PNG image showing the meta-system dependency graph
3. **Interactive Report**: An HTML file with detailed information about violations and recommendations

### Interpreting Violations

A violation occurs when one meta-system imports from another. For example:

```
PC imports from AN in person_suit/meta_systems/persona_core/some_file.py
```

This means that the Persona Core meta-system is importing from the Analyst meta-system, which violates the separation principle.

### Fixing Violations

To fix violations:

1. **Refactor the code** to remove direct dependencies between meta-systems
2. **Move shared functionality** to the core or create proper interfaces
3. **Use dependency injection** to decouple meta-systems
4. **Create adapters** to translate between meta-systems if necessary
5. **Run the meta-system separation analysis** regularly to catch new violations

## Visualizations

The analysis generates two types of visualizations:

1. **Static Graph**: A PNG image showing the meta-system dependency graph
2. **Interactive Report**: An HTML file with detailed information about violations and recommendations

The static graph shows meta-systems as nodes and dependencies as edges. Violations are highlighted in red.

The interactive report provides a more detailed view, including:
- A list of all violations
- Violations grouped by source meta-system
- Violations grouped by target meta-system
- Recommendations for fixing violations

## Integration with CI/CD

You can integrate the meta-system separation analysis into your CI/CD pipeline by adding the following step:

```bash
python PAT_project_analysis/check_meta_system_separation.py /path/to/person_suit --output-dir analysis
```

The script will exit with a non-zero status code if violations are found, which can be used to fail the build.

## Conclusion

Maintaining proper separation between meta-systems is essential for the long-term maintainability and evolvability of the person_suit project. This tool helps you identify and fix violations of this principle.
