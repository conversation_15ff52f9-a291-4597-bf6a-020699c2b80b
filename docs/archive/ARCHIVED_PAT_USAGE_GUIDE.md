# Vibe Check Tool Usage Guide

## Overview

Vibe Check (formerly Project Analysis Tool or PAT) is a comprehensive static analysis tool for Python codebases. It analyzes code quality, complexity, and adherence to best practices, generating reports and LLM-ready prompts for further analysis and refactoring.

This guide covers the improved version of the Vibe Check tool, which focuses on generating purposeful prompts for files that need attention.

## Installation

The Vibe Check tool can be installed via pip:

```bash
pip install vibe-check
```

For development, you can install from source:

```bash
git clone https://github.com/your-username/vibe-check.git
cd vibe-check
pip install -e .
```

### Dependencies

For full functionality, the following dependencies are recommended:

- **Python packages** (installed in the virtual environment):
  - graphviz
  - scipy
  - types-PyYAML
  - pyvis
  - networkx
  - matplotlib
  - plotly

- **System packages**:
  - graphviz (for visualization)

## Running the Vibe Check Tool

We provide several ways to run the Vibe Check tool:

### 1. Basic Analysis

```bash
vibe-check analyze <project_path>
```

This runs the Vibe Check tool with the default configuration.

### 2. Improved Analysis with Purposeful Prompts

```bash
vibe-check analyze <project_path> --output-dir ./results
```

This runs the Vibe Check tool with improved prompt generation that:
- Only includes files that need attention (high complexity, errors, etc.)
- Adds purpose statements to each chunk
- Provides specific refactoring instructions
- Focuses on actionable insights

### 3. Analysis with High-Priority Extraction

```bash
vibe-check analyze <project_path> --priority-extraction [--top N] [--min-score SCORE]
```

This runs the Vibe Check tool and then extracts high-priority files from the output:
- `--top N`: Number of top files to extract (default: 10)
- `--min-score SCORE`: Minimum priority score to include a file (default: 5)

### 4. Analysis with Status Updates

```bash
vibe-check analyze <project_path> --verbose
```

This runs the Vibe Check tool with detailed status updates:
- Shows progress during analysis and extraction
- Provides time elapsed for each operation
- Explains what the parameters mean
- Provides a detailed summary of results
- Suggests next steps based on the results

### 5. Fixing Syntax Errors

If your codebase has syntax errors that prevent analysis, you can use the syntax fixing options:

```bash
# Fix common syntax errors
vibe-check fix-syntax <project_path>

# Fix with specific options
vibe-check fix-syntax <project_path> --fix-escapes --fix-imports --fix-indentation
```

## Output Files

The Vibe Check tool generates output in a timestamped directory under `vibe_check_output/`. The output includes:

### Reports

- **vibe_check_analysis_summary.md**: High-level overview of the analysis
- **vibe_check_report.md**: Detailed metrics and findings
- **vibe_check_structure.md**: Directory structure insights

### Prompts

- **prompts/chunk_*.md**: LLM-ready prompts for analyzing specific files
- **prompts/verification_*.md**: Prompts for verifying refactoring
- **prompts/project_diagnostics.md**: Project-level diagnostics

## Using the Prompts

The generated prompts are designed to be fed to an LLM (like Claude or GPT-4) for further analysis and refactoring recommendations. Each prompt includes:

1. **Purpose Statement**: Explains why the file needs attention
2. **File Information**: Basic metadata about the file
3. **Issues Identified**: Specific issues that need to be addressed
4. **Diagnostics**: Detailed analysis from various tools
5. **Refactoring Instructions**: Specific guidance for improving the file

## Customization

### Configuration

The Vibe Check tool uses a configuration file. You can provide your own configuration file with the `--config` option:

```bash
vibe-check analyze <project_path> --config my_config.yaml
```

You can customize:

- Enable/disable specific analysis tools
- Adjust thresholds for complexity, file size, etc.
- Configure visualization options

### Prompt Generation

The prompt generation is controlled by the prompt generator module. Key aspects that can be customized:

- Criteria for including files in prompts
- Format of the purpose statement
- Refactoring instructions

You can customize these by creating a plugin or by modifying the configuration file.

## Best Practices

1. **Run regularly**: Use the Vibe Check tool regularly to track progress in improving code quality
2. **Focus on high-priority issues**: Address high complexity and security issues first
3. **Implement recommendations incrementally**: Make small, focused changes based on the recommendations
4. **Verify changes**: Use the verification prompts to ensure refactoring doesn't break functionality
5. **Integrate with CI/CD**: Consider adding Vibe Check analysis to your CI/CD pipeline

## Troubleshooting

### Common Issues

1. **Import errors**: Ensure the tool is properly installed
2. **Missing dependencies**: Install required dependencies as listed above
3. **Visualization failures**: Install graphviz system package
4. **Memory errors**: For large codebases, increase available memory or analyze subsets

### Getting Help

If you encounter issues not covered in this guide, check the Vibe Check tool source code or consult the documentation in the `docs` directory.

## Conclusion

The Vibe Check tool provides valuable insights into your codebase and generates purposeful prompts for LLM-assisted refactoring. By focusing on files that need attention and providing clear refactoring instructions, it helps you improve code quality efficiently.

Use this tool regularly as part of your development workflow to maintain high code quality and adherence to best practices.
