# Actor System Enhancements: Implementation Plan

## Overview

This document outlines the implementation plan for enhancing the Vibe Check actor system with two major features:

1. **Adaptive Execution Mode**: A system that can dynamically switch between parallel and sequential execution modes based on system stability.
2. **Meta-Learning for Actor Optimization**: A learning system that optimizes actor configurations based on project characteristics.

The implementation will be phased, with Adaptive Execution Mode as the first priority, followed by the groundwork for Meta-Learning capabilities.

## Phase 1: Adaptive Execution Mode

### 1.1 Core Components

#### ExecutionMode Enum

```python
from enum import Enum, auto

class ExecutionMode(Enum):
    """Execution modes for the actor system."""
    PARALLEL = auto()    # Default mode: actors run concurrently
    SEQUENTIAL = auto()  # Fallback mode: actors run in sequence for reliability
```

#### StabilityMetrics Class

```python
class StabilityMetrics:
    """Tracks metrics related to actor system stability."""
    
    def __init__(self):
        self.actor_failures = 0
        self.message_timeouts = 0
        self.recovery_attempts = 0
        self.last_mode_switch_time = 0
        self.consecutive_failures = 0
        self.error_categories = {}
        
    def record_failure(self, actor_id, error_type):
        """Record a failure event."""
        self.actor_failures += 1
        self.consecutive_failures += 1
        self.error_categories[error_type] = self.error_categories.get(error_type, 0) + 1
        
    def record_success(self):
        """Record a successful operation."""
        self.consecutive_failures = 0
        
    def calculate_instability_score(self):
        """Calculate a numeric score representing system instability."""
        base_score = (self.actor_failures * 2) + (self.message_timeouts * 1.5)
        
        # Apply multipliers for consecutive failures
        if self.consecutive_failures > 3:
            base_score *= 1.5
            
        # Apply time decay (reduce score for older issues)
        time_since_switch = time.time() - self.last_mode_switch_time
        if time_since_switch > 300:  # 5 minutes
            base_score *= 0.8
            
        return base_score
```

### 1.2 Orchestrator Enhancements

#### Execution Mode Management

```python
class Orchestrator:
    def __init__(self, config=None):
        # Existing initialization
        self._execution_mode = ExecutionMode.PARALLEL
        self._stability_metrics = StabilityMetrics()
        self._instability_threshold = 10  # Score that triggers mode switch to sequential
        self._stability_threshold = 3     # Score that allows switch back to parallel
        
    async def _switch_execution_mode(self, new_mode):
        """Switch the execution mode of the actor system."""
        if self._execution_mode == new_mode:
            return
            
        logger.info(f"Switching execution mode from {self._execution_mode.name} to {new_mode.name}")
        
        # Record the switch time
        self._stability_metrics.last_mode_switch_time = time.time()
        
        # Notify all actors about the mode change
        await self._notify_actors_of_mode_change(new_mode)
        
        # Update the mode
        self._execution_mode = new_mode
        
    async def _notify_actors_of_mode_change(self, new_mode):
        """Notify all actors about the execution mode change."""
        # Get all actors
        registry = get_registry()
        all_actors = registry.get_all_actors()
        
        # Prepare the notification message
        mode_change_payload = {
            "new_execution_mode": new_mode.name,
            "timestamp": time.time(),
            "reason": "system_stability"
        }
        
        # Send to all actors
        for actor in all_actors:
            try:
                await self.send_message(
                    actor.actor_id, 
                    MessageType.SYSTEM_CONFIG, 
                    mode_change_payload
                )
            except Exception as e:
                logger.error(f"Failed to notify actor {actor.actor_id} of mode change: {e}")
```

#### Stability Monitoring

```python
class Orchestrator:
    # ... existing code ...
    
    async def _monitor_system_stability(self):
        """Periodically monitor system stability and adjust execution mode."""
        while self.is_running:
            try:
                # Get supervisor actor
                supervisor = self._get_supervisor_actor()
                if not supervisor:
                    await asyncio.sleep(5)
                    continue
                    
                # Get actor statuses
                statuses = await supervisor.get_all_actor_statuses_async()
                
                # Update stability metrics
                self._update_stability_metrics(statuses)
                
                # Calculate instability score
                instability_score = self._stability_metrics.calculate_instability_score()
                logger.debug(f"Current system instability score: {instability_score}")
                
                # Decide whether to switch modes
                if self._execution_mode == ExecutionMode.PARALLEL and instability_score > self._instability_threshold:
                    await self._switch_execution_mode(ExecutionMode.SEQUENTIAL)
                elif self._execution_mode == ExecutionMode.SEQUENTIAL and instability_score < self._stability_threshold:
                    await self._switch_execution_mode(ExecutionMode.PARALLEL)
                    
            except Exception as e:
                logger.error(f"Error monitoring system stability: {e}")
                
            # Check every 10 seconds
            await asyncio.sleep(10)
            
    def _update_stability_metrics(self, actor_statuses):
        """Update stability metrics based on actor statuses."""
        for actor_id, status in actor_statuses.items():
            # Check for failures
            if status["status"] in ["failed", "unresponsive"]:
                self._stability_metrics.record_failure(actor_id, status.get("last_error", "unknown"))
                
            # Check for restarts
            if "total_restarts" in status and status["total_restarts"] > 0:
                self._stability_metrics.actor_failures += 1
                
            # Check for message timeouts
            if "metrics" in status and "message_timeouts" in status["metrics"]:
                self._stability_metrics.message_timeouts += status["metrics"]["message_timeouts"]
```

### 1.3 Sequential Execution Implementation

```python
class Orchestrator:
    # ... existing code ...
    
    async def analyze_project(self, project_path, config):
        """Analyze a project with the current execution mode."""
        # Record start time for metrics
        start_time = time.time()
        
        try:
            # Initialize analysis
            await self._initialize_analysis(project_path, config)
            
            # Execute analysis based on current mode
            if self._execution_mode == ExecutionMode.PARALLEL:
                result = await self._execute_analysis_parallel(project_path, config)
            else:
                result = await self._execute_analysis_sequential(project_path, config)
                
            # Record success
            self._stability_metrics.record_success()
            
            return result
            
        except Exception as e:
            logger.error(f"Error analyzing project: {e}")
            # Record failure
            self._stability_metrics.record_failure("orchestrator", "analysis_error")
            raise
            
    async def _execute_analysis_parallel(self, project_path, config):
        """Execute analysis in parallel mode (existing implementation)."""
        # This is the current implementation
        pass
        
    async def _execute_analysis_sequential(self, project_path, config):
        """Execute analysis in sequential mode for increased reliability."""
        logger.info("Executing analysis in SEQUENTIAL mode for increased reliability")
        
        try:
            # Get project actor
            project_actor = self._get_project_actor()
            if not project_actor:
                raise ValueError("Project actor not found")
                
            # Initialize project analysis
            await self._send_and_wait(
                project_actor.actor_id,
                MessageType.INIT_ANALYSIS,
                {"project_path": project_path, "config": config}
            )
            
            # Get file actors (should be created by project actor)
            file_actors = self._get_file_actors()
            if not file_actors:
                raise ValueError("No file actors found after project initialization")
                
            # Process files one by one
            results = []
            for file_actor in file_actors:
                try:
                    # Process each file completely before moving to the next
                    file_result = await self._process_file_sequential(file_actor.actor_id)
                    results.append(file_result)
                except Exception as e:
                    logger.error(f"Error processing file with actor {file_actor.actor_id}: {e}")
                    # Continue with next file instead of failing completely
            
            # Generate report only after all files are processed
            report_result = await self._generate_report_sequential(results)
            
            return {
                "file_results": results,
                "report_result": report_result
            }
            
        except Exception as e:
            logger.error(f"Error in sequential analysis: {e}")
            import traceback
            logger.error(traceback.format_exc())
            raise
            
    async def _send_and_wait(self, actor_id, message_type, payload, timeout=30.0):
        """Send a message to an actor and wait for a response."""
        # Create a future to wait for the response
        response_future = asyncio.Future()
        
        # Generate a correlation ID for this request
        correlation_id = str(uuid.uuid4())
        
        # Register the future in a response registry
        self._response_futures[correlation_id] = response_future
        
        # Create context with correlation ID
        context = ContextWave()
        context.metadata["correlation_id"] = correlation_id
        context.metadata["sender_id"] = self.actor_id
        context.metadata["requires_response"] = True
        
        # Send the message
        await self.send_message(actor_id, message_type, payload, context)
        
        try:
            # Wait for the response with timeout
            return await asyncio.wait_for(response_future, timeout=timeout)
        except asyncio.TimeoutError:
            logger.error(f"Timeout waiting for response from {actor_id} for {message_type.name}")
            self._stability_metrics.message_timeouts += 1
            raise
        finally:
            # Clean up the future
            self._response_futures.pop(correlation_id, None)
```

### 1.4 Actor Enhancements

```python
class Actor:
    # ... existing code ...
    
    async def handle_system_config(self, payload, context):
        """Handle system configuration messages."""
        if "new_execution_mode" in payload:
            new_mode = payload["new_execution_mode"]
            logger.info(f"Actor {self.actor_id} received execution mode change to {new_mode}")
            
            # Update actor's internal mode tracking
            self._execution_mode = new_mode
            
            # Adjust internal processing based on mode
            if new_mode == "SEQUENTIAL":
                # Increase timeouts and retries for reliability
                self._message_processor.set_processing_options(
                    max_retries=5,
                    retry_delay=2.0,
                    message_timeout=60.0
                )
            else:
                # Reset to default values for performance
                self._message_processor.set_processing_options(
                    max_retries=2,
                    retry_delay=0.5,
                    message_timeout=30.0
                )
```

## Phase 2: Meta-Learning Foundation

### 2.1 Telemetry Collection

#### ExecutionMetricsCollector Class

```python
class ExecutionMetricsCollector:
    """Collects execution metrics for meta-learning optimization."""
    
    def __init__(self, db_path):
        self.db_path = db_path
        self._ensure_db_exists()
        
    def _ensure_db_exists(self):
        """Ensure the metrics database exists and has the correct schema."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create tables if they don't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS execution_records (
            id INTEGER PRIMARY KEY,
            timestamp REAL,
            project_hash TEXT,
            execution_time REAL,
            peak_memory_usage REAL,
            execution_mode TEXT,
            actor_count INTEGER,
            config_json TEXT
        )
        ''')
        
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS project_features (
            project_hash TEXT PRIMARY KEY,
            file_count INTEGER,
            avg_file_size REAL,
            max_file_size REAL,
            directory_depth INTEGER,
            language_distribution TEXT,
            complexity_metrics TEXT
        )
        ''')
        
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS actor_metrics (
            record_id INTEGER,
            actor_id TEXT,
            actor_type TEXT,
            messages_processed INTEGER,
            avg_processing_time REAL,
            errors INTEGER,
            FOREIGN KEY (record_id) REFERENCES execution_records(id)
        )
        ''')
        
        conn.commit()
        conn.close()
        
    async def record_execution_metrics(self, project_info, config, performance_metrics):
        """Record execution metrics for a completed analysis."""
        # Generate a hash for the project
        project_hash = self._generate_project_hash(project_info)
        
        # Store project features if not already stored
        await self._store_project_features(project_hash, project_info)
        
        # Store execution record
        record_id = await self._store_execution_record(
            project_hash, 
            config, 
            performance_metrics
        )
        
        # Store actor metrics
        if "actor_metrics" in performance_metrics:
            await self._store_actor_metrics(record_id, performance_metrics["actor_metrics"])
            
    def _generate_project_hash(self, project_info):
        """Generate a unique hash for a project based on its characteristics."""
        # Create a string representation of key project characteristics
        project_str = (
            f"{project_info.get('project_path', '')}"
            f"{project_info.get('file_count', 0)}"
            f"{project_info.get('language_distribution', {})}"
        )
        
        # Generate a hash
        return hashlib.md5(project_str.encode()).hexdigest()
```

### 2.2 Integration with Orchestrator

```python
class Orchestrator:
    def __init__(self, config=None):
        # Existing initialization
        self._execution_mode = ExecutionMode.PARALLEL
        self._stability_metrics = StabilityMetrics()
        
        # Add metrics collection
        metrics_db_path = os.path.join(
            os.path.dirname(os.path.abspath(__file__)),
            "..",
            "data",
            "execution_metrics.db"
        )
        self._metrics_collector = ExecutionMetricsCollector(metrics_db_path)
        
    async def analyze_project(self, project_path, config):
        """Analyze a project and collect metrics."""
        # Record start time and memory usage
        start_time = time.time()
        start_memory = self._get_memory_usage()
        
        try:
            # Get project info for metrics
            project_info = await self._analyze_project_structure(project_path)
            
            # Execute analysis based on current mode
            if self._execution_mode == ExecutionMode.PARALLEL:
                result = await self._execute_analysis_parallel(project_path, config)
            else:
                result = await self._execute_analysis_sequential(project_path, config)
                
            # Record metrics
            execution_time = time.time() - start_time
            peak_memory = self._get_peak_memory_usage() - start_memory
            
            # Collect actor metrics
            actor_metrics = await self._collect_actor_metrics()
            
            # Record execution metrics
            await self._metrics_collector.record_execution_metrics(
                project_info,
                config,
                {
                    "execution_time": execution_time,
                    "peak_memory_usage": peak_memory,
                    "execution_mode": self._execution_mode.name,
                    "actor_metrics": actor_metrics
                }
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error analyzing project: {e}")
            raise
```

## Implementation Timeline

### Phase 1: Adaptive Execution Mode (2-3 weeks)

1. **Week 1**:
   - Implement ExecutionMode enum and StabilityMetrics class
   - Add execution mode tracking to Orchestrator
   - Implement mode switching logic

2. **Week 2**:
   - Implement sequential execution pathway
   - Add actor enhancements for mode awareness
   - Implement stability monitoring

3. **Week 3**:
   - Integration testing
   - Performance benchmarking
   - Documentation and refinement

### Phase 2: Meta-Learning Foundation (1-2 weeks)

1. **Week 1**:
   - Implement ExecutionMetricsCollector
   - Add metrics collection to Orchestrator
   - Create database schema

2. **Week 2**:
   - Implement project feature extraction
   - Add actor metrics collection
   - Create basic reporting tools for collected metrics

## Future Work: Full Meta-Learning Implementation

After collecting sufficient execution data, implement:

1. Feature engineering for project characteristics
2. Model selection and training pipeline
3. Configuration optimization based on predictions
4. Continuous learning and model updating
5. User interface for viewing optimization insights

## Risks and Mitigations

| Risk | Mitigation |
|------|------------|
| Mode switching causes disruption | Implement graceful transitions with proper cleanup |
| Sequential mode is too slow | Optimize critical paths, consider hybrid approaches |
| Stability detection is inaccurate | Tune thresholds based on real-world testing |
| Database grows too large | Implement data retention policies and aggregation |
| Learning models are ineffective | Start with simple heuristics, gradually increase complexity |

## Success Metrics

- **Reliability**: Reduction in failed analyses by at least 50%
- **Recovery**: Successful completion of analyses that would have failed in parallel mode
- **Performance**: For stable projects, maintain parallel performance within 10% of baseline
- **Data Collection**: Gather execution metrics for at least 100 diverse projects
