# Vibe Check

A comprehensive Python project analysis tool that provides insights into code structure, dependencies, complexity, and quality metrics. This tool has been designed to be modular, maintainable, and resilient against errors that might occur when analyzing large or complex codebases.

## Features

- **Structure Analysis**: Analyze directory organization, file size distribution, and module descriptions
- **Dependency Analysis**: Discover module dependencies and detect circular references
- **Complexity Analysis**: Calculate cyclomatic complexity and maintainability metrics
- **Visualization**: Generate dependency graphs and complexity heatmaps
- **Reporting**: Create detailed reports in JSON and Markdown formats

## Directory Structure

- **vibe_check/**: Root directory for the tool
  - **core/**: Core modules for the analysis tool
  - **ui/**: User interface modules (CLI, Web, TUI)
  - **tools/**: Tool integration modules
  - **config/**: Configuration files
  - **models/**: Data models
  - **utils/**: Utility functions
  - **vibe-check.py**: Main entry point script
  - **setup.py**: Installation script
  - **requirements.txt**: Required Python packages
  - **README.md**: Main documentation file
  - **CHANGES.md**: Documentation of recent changes and improvements

## Key Improvements

This version of the analysis tool offers several improvements:

- **Error Resilience**: Gracefully handles recursion errors, syntax errors, and other exceptions
- **Modular Architecture**: Each analysis phase is implemented in a separate module for better maintainability
- **Fallback Mechanisms**: Provides alternative approaches when primary analysis methods fail
- **Incremental Analysis**: Processes files and directories incrementally to prevent memory issues
- **Improved Reporting**: Generates more comprehensive and useful reports
- **Self-contained Environment**: Includes virtual environment setup for dependencies
- **Clean Architecture**: Follows Clean Architecture principles with clear separation of concerns

## Setup

### Option 1: Install from PyPI (Recommended)

```bash
pip install vibe-check
```

### Option 2: Install from Source

```bash
# Clone the repository
git clone https://github.com/your-username/vibe-check.git
cd vibe-check

# Install in development mode
pip install -e .
```

## Usage

### Simple Usage

Run the analysis using the command-line interface:

```bash
# Analyze a project
vibe-check analyze <project_path>
```

Example:
```bash
vibe-check analyze .
```

### Advanced Usage

For more control over the analysis process, use additional options:

```bash
vibe-check analyze <project_path> [options]
```

Options:
- `--output-dir <dir>`: Directory to save analysis results
- `--config <file>`: Path to configuration file
- `--verbose`: Enable verbose output
- `--security-focused`: Prioritize security analysis
- `--quality-focused`: Prioritize code quality analysis
- `--format [text|json|html]`: Output format

## Output

The tool generates the following reports in the output directory:

- `vibe_check_analysis.json`: Complete analysis data in JSON format
- `vibe_check_report.md`: Comprehensive project analysis report
- `vibe_check_structure.md`: Detailed project structure analysis
- `vibe_check_analysis_summary.md`: Key findings and recommendations
- `vibe_check_dependency_graph.png`: Visual representation of module dependencies
- `vibe_check_complexity_heatmap.png`: Visual representation of module complexity

## Architecture

The tool is designed with a modular architecture following the CAW (Contextual Adaptive Wave) paradigm:

- `models.py`: Data classes for project metrics
- `utils.py`: Utility functions and error handling
- `structure_analyzer.py`: Analyzes project structure
- `dependency_analyzer.py`: Analyzes module dependencies
- `complexity_analyzer.py`: Analyzes code complexity
- `visualization.py`: Generates visual representations
- `reporter.py`: Generates reports in various formats
- `main.py`: Main entry point for the tool

## Troubleshooting

### Enhanced Error Handling

The tool now includes comprehensive error handling and reporting:

- **Detailed Error Summaries**: Critical errors are summarized at the end of the analysis
- **Error Classification**: Errors are categorized as critical errors, warnings, or tool-specific errors
- **JSON Error Report**: A detailed error report is saved in JSON format for further analysis
- **Helpful Hints**: Common errors include hints for resolution

### Common Issues

- **RecursionError**: The tool implements fallback mechanisms to handle recursion errors that might occur when parsing complex files.
- **Dependency graph generation fails**: Ensure that graphviz is installed, or use the `--skip-visualizations` option.
- **Missing dependencies**: Run the installation script to ensure all dependencies are installed.
- **Import errors**: The tool now handles import issues more gracefully with improved Python path management.

### Logging

The tool generates several log files:

- `vibe_check_output/latest/vibe_check_analysis.log`: Main log file with detailed information
- `vibe_check_output/latest/error_summary.json`: Structured error report in JSON format
- `vibe_check_output/latest/dependency_audit.log`: Log of dependency-related issues
- `vibe_check_output/latest/syntax_errors.log`: Log of syntax errors found in the codebase

Check these files for detailed error information if something goes wrong.

## License

This tool is part of the project and follows the same licensing terms.
