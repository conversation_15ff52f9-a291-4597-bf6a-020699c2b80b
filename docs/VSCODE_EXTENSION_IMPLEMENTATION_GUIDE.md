# VibeCheck VS Code Extension Implementation Guide

**Documentation Cycle: Strawberry | Updated: 28-06-2025**

## Quick Start Implementation

This guide provides a practical roadmap for implementing the VibeCheck VS Code extension based on our current architecture readiness.

---

## Phase 1: Foundation Setup (Week 1)

### 1.1 Create Extension Scaffold

```bash
# Create extension directory
mkdir vscode-vibecheck
cd vscode-vibecheck

# Initialize VS Code extension
npm install -g yo generator-code
yo code

# Choose:
# - New Extension (TypeScript)
# - Name: VibeCheck
# - Identifier: vibecheck
# - Description: Advanced Python code analysis with framework expertise
```

### 1.2 Basic Extension Structure

```
vscode-vibecheck/
├── package.json              # Extension manifest
├── src/
│   ├── extension.ts          # Main extension entry point
│   ├── providers/
│   │   ├── diagnosticProvider.ts
│   │   ├── codeActionProvider.ts
│   │   └── symbolProvider.ts
│   ├── services/
│   │   ├── vibeCheckService.ts
│   │   └── configService.ts
│   └── utils/
│       ├── pythonUtils.ts
│       └── diagnosticUtils.ts
├── python/
│   ├── lsp_server.py         # Python LSP server
│   ├── vscode_adapter.py     # VS Code specific adapter
│   └── requirements.txt      # Python dependencies
└── resources/
    ├── icons/
    └── schemas/
```

### 1.3 Package.json Configuration

```json
{
  "name": "vibecheck",
  "displayName": "VibeCheck",
  "description": "Advanced Python code analysis with framework expertise",
  "version": "0.1.0",
  "engines": {
    "vscode": "^1.74.0"
  },
  "categories": ["Linters", "Other"],
  "activationEvents": [
    "onLanguage:python",
    "workspaceContains:**/*.py"
  ],
  "main": "./out/extension.js",
  "contributes": {
    "commands": [
      {
        "command": "vibecheck.analyzeProject",
        "title": "Analyze Project",
        "category": "VibeCheck"
      },
      {
        "command": "vibecheck.analyzeFile", 
        "title": "Analyze Current File",
        "category": "VibeCheck"
      },
      {
        "command": "vibecheck.showFrameworks",
        "title": "Show Detected Frameworks",
        "category": "VibeCheck"
      }
    ],
    "configuration": {
      "title": "VibeCheck",
      "properties": {
        "vibecheck.analysis.enableSemanticAnalysis": {
          "type": "boolean",
          "default": true,
          "description": "Enable advanced semantic analysis"
        },
        "vibecheck.analysis.enableFrameworkDetection": {
          "type": "boolean",
          "default": true,
          "description": "Enable framework-specific analysis"
        },
        "vibecheck.performance.maxWorkers": {
          "type": "number",
          "default": 4,
          "minimum": 1,
          "maximum": 16,
          "description": "Maximum number of analysis workers"
        }
      }
    }
  },
  "scripts": {
    "vscode:prepublish": "npm run compile",
    "compile": "tsc -p ./",
    "watch": "tsc -watch -p ./"
  },
  "dependencies": {
    "node-fetch": "^3.3.0",
    "vscode-languageclient": "^8.1.0"
  },
  "devDependencies": {
    "@types/vscode": "^1.74.0",
    "@types/node": "16.x",
    "typescript": "^4.9.4"
  }
}
```

---

## Phase 2: Core Integration (Week 2)

### 2.1 Main Extension Entry Point

```typescript
// src/extension.ts
import * as vscode from 'vscode';
import { VibeCheckDiagnosticProvider } from './providers/diagnosticProvider';
import { VibeCheckCodeActionProvider } from './providers/codeActionProvider';
import { VibeCheckService } from './services/vibeCheckService';

export function activate(context: vscode.ExtensionContext) {
    console.log('VibeCheck extension is now active!');

    // Initialize services
    const vibeCheckService = new VibeCheckService(context);
    const diagnosticProvider = new VibeCheckDiagnosticProvider(vibeCheckService);
    const codeActionProvider = new VibeCheckCodeActionProvider(vibeCheckService);

    // Register diagnostic provider
    const diagnosticCollection = vscode.languages.createDiagnosticCollection('vibecheck');
    context.subscriptions.push(diagnosticCollection);

    // Register providers
    context.subscriptions.push(
        vscode.languages.registerCodeActionsProvider('python', codeActionProvider),
        vscode.workspace.onDidSaveTextDocument(document => {
            if (document.languageId === 'python') {
                diagnosticProvider.updateDiagnostics(document, diagnosticCollection);
            }
        }),
        vscode.workspace.onDidChangeTextDocument(event => {
            if (event.document.languageId === 'python') {
                // Debounced real-time analysis
                diagnosticProvider.scheduleAnalysis(event.document, diagnosticCollection);
            }
        })
    );

    // Register commands
    context.subscriptions.push(
        vscode.commands.registerCommand('vibecheck.analyzeProject', () => {
            vibeCheckService.analyzeProject();
        }),
        vscode.commands.registerCommand('vibecheck.analyzeFile', () => {
            const activeEditor = vscode.window.activeTextEditor;
            if (activeEditor && activeEditor.document.languageId === 'python') {
                vibeCheckService.analyzeFile(activeEditor.document);
            }
        }),
        vscode.commands.registerCommand('vibecheck.showFrameworks', () => {
            vibeCheckService.showDetectedFrameworks();
        })
    );
}

export function deactivate() {
    console.log('VibeCheck extension is now deactivated');
}
```

### 2.2 VibeCheck Service

```typescript
// src/services/vibeCheckService.ts
import * as vscode from 'vscode';
import * as cp from 'child_process';
import * as path from 'path';

export interface VibeCheckResult {
    issues: VibeCheckIssue[];
    frameworks: string[];
    metrics: {
        complexity: number;
        maintainability: number;
        typeCoverage: number;
    };
}

export interface VibeCheckIssue {
    ruleId: string;
    severity: 'error' | 'warning' | 'info';
    message: string;
    line: number;
    column: number;
    suggestion?: string;
    framework?: string;
}

export class VibeCheckService {
    private pythonPath: string;
    private vibeCheckPath: string;

    constructor(private context: vscode.ExtensionContext) {
        this.pythonPath = this.findPythonPath();
        this.vibeCheckPath = this.findVibeCheckPath();
    }

    async analyzeFile(document: vscode.TextDocument): Promise<VibeCheckResult> {
        const workspaceFolder = vscode.workspace.getWorkspaceFolder(document.uri);
        if (!workspaceFolder) {
            throw new Error('No workspace folder found');
        }

        const config = vscode.workspace.getConfiguration('vibecheck');
        const enableSemantic = config.get<boolean>('analysis.enableSemanticAnalysis', true);
        const enableFrameworks = config.get<boolean>('analysis.enableFrameworkDetection', true);

        const args = [
            '-m', 'vibe_check.core.vscode_adapter',
            '--file', document.fileName,
            '--workspace', workspaceFolder.uri.fsPath,
            '--format', 'json'
        ];

        if (enableSemantic) {
            args.push('--semantic');
        }

        if (enableFrameworks) {
            args.push('--frameworks');
        }

        return new Promise((resolve, reject) => {
            const process = cp.spawn(this.pythonPath, args, {
                cwd: workspaceFolder.uri.fsPath
            });

            let stdout = '';
            let stderr = '';

            process.stdout.on('data', (data) => {
                stdout += data.toString();
            });

            process.stderr.on('data', (data) => {
                stderr += data.toString();
            });

            process.on('close', (code) => {
                if (code === 0) {
                    try {
                        const result = JSON.parse(stdout);
                        resolve(this.convertToVibeCheckResult(result));
                    } catch (error) {
                        reject(new Error(`Failed to parse VibeCheck output: ${error}`));
                    }
                } else {
                    reject(new Error(`VibeCheck failed with code ${code}: ${stderr}`));
                }
            });

            process.on('error', (error) => {
                reject(new Error(`Failed to start VibeCheck: ${error.message}`));
            });
        });
    }

    async analyzeProject(): Promise<void> {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders) {
            vscode.window.showErrorMessage('No workspace folder open');
            return;
        }

        const workspaceFolder = workspaceFolders[0];
        
        vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: "Analyzing project with VibeCheck...",
            cancellable: true
        }, async (progress, token) => {
            try {
                const args = [
                    '-m', 'vibe_check.cli.main',
                    'analyze',
                    workspaceFolder.uri.fsPath,
                    '--semantic',
                    '--output', path.join(workspaceFolder.uri.fsPath, '.vscode', 'vibecheck-results.json')
                ];

                await this.runVibeCheck(args, workspaceFolder.uri.fsPath, progress, token);
                
                vscode.window.showInformationMessage('VibeCheck analysis completed!');
                
                // Show results in a new panel
                this.showProjectResults();
                
            } catch (error) {
                vscode.window.showErrorMessage(`VibeCheck analysis failed: ${error}`);
            }
        });
    }

    async showDetectedFrameworks(): Promise<void> {
        const activeEditor = vscode.window.activeTextEditor;
        if (!activeEditor || activeEditor.document.languageId !== 'python') {
            vscode.window.showWarningMessage('Please open a Python file');
            return;
        }

        try {
            const result = await this.analyzeFile(activeEditor.document);
            
            if (result.frameworks.length === 0) {
                vscode.window.showInformationMessage('No frameworks detected in this file');
                return;
            }

            const frameworkList = result.frameworks.join(', ');
            vscode.window.showInformationMessage(`Detected frameworks: ${frameworkList}`);
            
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to detect frameworks: ${error}`);
        }
    }

    private findPythonPath(): string {
        // Try to find Python executable
        const pythonExtension = vscode.extensions.getExtension('ms-python.python');
        if (pythonExtension) {
            // Use Python extension's interpreter if available
            const pythonPath = vscode.workspace.getConfiguration('python').get<string>('pythonPath');
            if (pythonPath) {
                return pythonPath;
            }
        }
        
        // Fallback to system Python
        return process.platform === 'win32' ? 'python.exe' : 'python3';
    }

    private findVibeCheckPath(): string {
        // For now, assume VibeCheck is installed in the Python environment
        // Later, we can bundle it with the extension
        return 'vibe_check';
    }

    private convertToVibeCheckResult(rawResult: any): VibeCheckResult {
        // Convert raw VibeCheck output to our interface
        return {
            issues: rawResult.semantic_issues?.map((issue: any) => ({
                ruleId: issue.rule_id,
                severity: issue.severity,
                message: issue.message,
                line: issue.line_number - 1, // VS Code uses 0-based line numbers
                column: issue.column_number,
                suggestion: issue.suggestion,
                framework: issue.framework
            })) || [],
            frameworks: rawResult.detected_frameworks || [],
            metrics: {
                complexity: rawResult.metrics?.complexity || 0,
                maintainability: rawResult.metrics?.maintainability || 0,
                typeCoverage: rawResult.metrics?.type_coverage || 0
            }
        };
    }

    private async runVibeCheck(args: string[], cwd: string, progress: any, token: any): Promise<void> {
        return new Promise((resolve, reject) => {
            const process = cp.spawn(this.pythonPath, args, { cwd });
            
            let stderr = '';
            
            process.stderr.on('data', (data) => {
                stderr += data.toString();
            });
            
            process.on('close', (code) => {
                if (code === 0) {
                    resolve();
                } else {
                    reject(new Error(stderr));
                }
            });
            
            process.on('error', (error) => {
                reject(error);
            });
            
            token.onCancellationRequested(() => {
                process.kill();
                reject(new Error('Analysis cancelled'));
            });
        });
    }

    private showProjectResults(): void {
        // Create and show a webview panel with project results
        const panel = vscode.window.createWebviewPanel(
            'vibeCheckResults',
            'VibeCheck Results',
            vscode.ViewColumn.Two,
            {
                enableScripts: true
            }
        );

        panel.webview.html = this.getResultsWebviewContent();
    }

    private getResultsWebviewContent(): string {
        return `
        <!DOCTYPE html>
        <html>
        <head>
            <title>VibeCheck Results</title>
            <style>
                body { font-family: var(--vscode-font-family); }
                .metric { margin: 10px 0; }
                .framework { background: var(--vscode-badge-background); padding: 2px 6px; border-radius: 3px; margin: 2px; }
            </style>
        </head>
        <body>
            <h1>VibeCheck Analysis Results</h1>
            <div id="results">Loading...</div>
            <script>
                // Load and display results
                // This would be populated with actual data
            </script>
        </body>
        </html>
        `;
    }
}
```

---

## Phase 3: Python Integration (Week 3)

### 3.1 VS Code Adapter for VibeCheck

```python
# python/vscode_adapter.py
"""
VS Code adapter for VibeCheck analysis.
Provides a streamlined interface for VS Code extension integration.
"""

import argparse
import asyncio
import json
import sys
from pathlib import Path
from typing import Dict, Any, List

# Import VibeCheck components
from vibe_check.core.analysis.project_analyzer import ProjectAnalyzer
from vibe_check.core.analysis.framework_rules import FrameworkSpecificAnalyzer
from vibe_check.core.knowledge.framework_knowledge_base import FrameworkKnowledgeBase


class VSCodeAdapter:
    """Adapter for VS Code integration."""
    
    def __init__(self):
        self.analyzer = ProjectAnalyzer(enable_semantic_analysis=True)
        self.framework_analyzer = FrameworkSpecificAnalyzer()
        self.knowledge_base = FrameworkKnowledgeBase()
    
    async def analyze_file(self, file_path: str, workspace_path: str, 
                          enable_semantic: bool = True, 
                          enable_frameworks: bool = True) -> Dict[str, Any]:
        """
        Analyze a single file for VS Code.
        
        Args:
            file_path: Path to the file to analyze
            workspace_path: Path to the workspace root
            enable_semantic: Whether to enable semantic analysis
            enable_frameworks: Whether to enable framework analysis
            
        Returns:
            Analysis results in VS Code format
        """
        file_path = Path(file_path)
        workspace_path = Path(workspace_path)
        
        # Read file content
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            return {'error': f'Failed to read file: {e}'}
        
        result = {
            'file_path': str(file_path),
            'semantic_issues': [],
            'framework_issues': [],
            'detected_frameworks': [],
            'metrics': {}
        }
        
        try:
            # Semantic analysis
            if enable_semantic:
                semantic_result = await self._analyze_semantic(file_path, content)
                result['semantic_issues'] = self._convert_semantic_issues(semantic_result)
                result['metrics'] = self._extract_metrics(semantic_result)
            
            # Framework analysis
            if enable_frameworks:
                framework_result = await self._analyze_frameworks({str(file_path): content})
                result['framework_issues'] = self._convert_framework_issues(framework_result)
                result['detected_frameworks'] = [
                    d.framework.name for d in framework_result['framework_analysis'].detected_frameworks
                ]
            
        except Exception as e:
            result['error'] = f'Analysis failed: {e}'
        
        return result
    
    async def _analyze_semantic(self, file_path: Path, content: str) -> Any:
        """Perform semantic analysis on a file."""
        from vibe_check.core.analysis.python_semantic_analyzer import PythonSemanticAnalyzer
        from vibe_check.core.analysis.semantic_rules import create_enhanced_rule_registry
        
        registry = create_enhanced_rule_registry()
        analyzer = PythonSemanticAnalyzer(registry)
        
        return analyzer.analyze_source(content, file_path)
    
    async def _analyze_frameworks(self, project_files: Dict[str, str]) -> Dict[str, Any]:
        """Perform framework analysis."""
        return self.framework_analyzer.analyze_project_with_frameworks(project_files)
    
    def _convert_semantic_issues(self, semantic_result: Any) -> List[Dict[str, Any]]:
        """Convert semantic issues to VS Code format."""
        issues = []
        
        for issue in semantic_result.issues:
            issues.append({
                'rule_id': issue.rule_id,
                'severity': issue.severity,
                'message': issue.message,
                'line_number': issue.line_number,
                'column_number': issue.column_number,
                'suggestion': issue.suggestion
            })
        
        return issues
    
    def _convert_framework_issues(self, framework_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Convert framework issues to VS Code format."""
        issues = []
        
        framework_issues = framework_result.get('framework_specific_issues', {})
        for file_path, file_issues in framework_issues.items():
            for issue in file_issues:
                issues.append({
                    'rule_id': issue.rule_id,
                    'severity': issue.severity,
                    'message': issue.message,
                    'line_number': issue.line_number,
                    'column_number': issue.column_number,
                    'suggestion': issue.suggestion,
                    'framework': self._extract_framework_from_rule(issue.rule_id)
                })
        
        return issues
    
    def _extract_metrics(self, semantic_result: Any) -> Dict[str, Any]:
        """Extract metrics from semantic analysis."""
        return {
            'complexity': semantic_result.metrics.get('complexity', 0),
            'maintainability': semantic_result.metrics.get('maintainability', 0),
            'type_coverage': semantic_result.metrics.get('type_coverage', 0)
        }
    
    def _extract_framework_from_rule(self, rule_id: str) -> str:
        """Extract framework name from rule ID."""
        for framework_name in ['django', 'flask', 'fastapi', 'pytest', 'pandas']:
            if framework_name in rule_id:
                return framework_name
        return 'unknown'


async def main():
    """Main entry point for VS Code adapter."""
    parser = argparse.ArgumentParser(description='VibeCheck VS Code Adapter')
    parser.add_argument('--file', required=True, help='File to analyze')
    parser.add_argument('--workspace', required=True, help='Workspace root path')
    parser.add_argument('--semantic', action='store_true', help='Enable semantic analysis')
    parser.add_argument('--frameworks', action='store_true', help='Enable framework analysis')
    parser.add_argument('--format', default='json', choices=['json'], help='Output format')
    
    args = parser.parse_args()
    
    adapter = VSCodeAdapter()
    
    try:
        result = await adapter.analyze_file(
            args.file,
            args.workspace,
            enable_semantic=args.semantic,
            enable_frameworks=args.frameworks
        )
        
        if args.format == 'json':
            print(json.dumps(result, indent=2))
        
    except Exception as e:
        error_result = {'error': str(e)}
        print(json.dumps(error_result, indent=2))
        sys.exit(1)


if __name__ == '__main__':
    asyncio.run(main())
```

---

## Next Steps

1. **Week 1**: Set up extension scaffold and basic structure
2. **Week 2**: Implement core TypeScript integration
3. **Week 3**: Create Python adapter and test integration
4. **Week 4**: Add diagnostic provider and real-time analysis
5. **Week 5**: Implement code actions and advanced features
6. **Week 6**: Testing, documentation, and marketplace preparation

This implementation guide provides a practical foundation for creating the VibeCheck VS Code extension, leveraging our existing architecture and analysis capabilities.
