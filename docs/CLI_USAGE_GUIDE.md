# Vibe Check CLI Usage Guide

**Documentation Cycle: Strawberry | Updated: 28-06-2025**

This guide provides instructions for using Vibe Check from the command line interface (CLI), which is the primary and most mature interface for the tool.

## Prerequisites

Before using Vibe Check, ensure you have:

1. **Activated the virtual environment**:
   ```bash
   source .venv/bin/activate
   ```

2. **Installed Vibe Check in development mode**:
   ```bash
   pip install -e .
   ```

3. **Installed required dependencies**:
   ```bash
   pip install matplotlib networkx pyyaml
   ```

## Basic Commands

### Getting Help

To see all available commands and options:

```bash
vibe-check --help
```

For help with a specific command:

```bash
vibe-check <command> --help
```

### Analyzing a Project

The primary function of Vibe Check is to analyze projects:

```bash
vibe-check analyze <project_path> -o <output_directory>
```

Example:
```bash
vibe-check analyze ./my_project -o ./analysis_results
```

**Note**: It's recommended to create the output directory before running the analysis:
```bash
mkdir -p ./analysis_results
```

### Checking Available Profiles

To see all analysis profiles available in Vibe Check:

```bash
vibe-check profiles
```

### Checking Dependencies

To check the status of optional dependencies:

```bash
vibe-check deps
```

### Launching the TUI

To start the Text-based User Interface:

```bash
vibe-check tui <project_path>
```

The TUI provides an interactive terminal interface for project analysis with real-time updates and navigation.

## Configuration Options

Vibe Check can be configured using configuration presets, command-line options, or a configuration file.

### Configuration Presets

Vibe Check comes with several built-in configuration presets that you can use to quickly set up common analysis configurations:

```bash
vibe-check analyze ./my_project -o ./analysis_results --preset standard
```

To see all available presets:

```bash
vibe-check list-presets
```

Available presets include:

- **standard**: Balanced analysis for general use
- **security**: Focus on security vulnerabilities
- **quality**: Focus on code quality and style
- **quick**: Fast analysis with minimal checks
- **comprehensive**: Thorough analysis with all checks enabled

### Using .gitignore Patterns

You can use patterns from your project's `.gitignore` file to exclude files from analysis:

```bash
vibe-check analyze ./my_project -o ./analysis_results --use-gitignore
```

This is also enabled by default in all presets.

### Command-line Configuration

You can override configuration settings directly from the command line:

```bash
vibe-check analyze ./my_project -o ./analysis_results --security-focused
```

### Configuration File

Create a `vibe_check_config.yaml` file in your project directory:

```yaml
# Vibe Check configuration file
file_extensions:
  - .py
exclude_patterns:
  - "**/__pycache__/**"
  - "**/venv/**"
  - "**/env/**"
  - "**/.git/**"
tools:
  ruff:
    enabled: true
    args:
      - "--select=E,F,W,I"
      - "--ignore=E501"
  mypy:
    enabled: true
    args:
      - "--ignore-missing-imports"
  bandit:
    enabled: true
    args:
      - "--recursive"
priorities:
  security: 0.33
  performance: 0.33
  maintainability: 0.34
```

Then run:

```bash
vibe-check analyze ./my_project -o ./analysis_results --config ./vibe_check_config.yaml
```

## Typical Workflow

1. **Activate the virtual environment**:

   ```bash
   source .venv/bin/activate
   ```

2. **List available presets** to choose the right one for your needs:

   ```bash
   vibe-check list-presets
   ```

3. **Analyze your project** using a preset:

   ```bash
   vibe-check analyze /path/to/your/project -o ./analysis_results --preset comprehensive
   ```

4. **Review the analysis summary** that appears in the terminal.

5. **Check the output directory** for detailed reports.

## Advanced Options

### Verbose Output

For more detailed logging during analysis:

```bash
vibe-check analyze ./my_project -o ./analysis_results -v
```

### Customizing Analysis Priorities

You can adjust the importance of different aspects of code quality:

```bash
vibe-check analyze ./my_project -o ./analysis_results --config-override '{"priorities": {"security": 0.5, "performance": 0.3, "maintainability": 0.2}}'
```

### Excluding Files or Directories

To exclude specific files or directories from analysis:

```bash
vibe-check analyze ./my_project -o ./analysis_results --config-override '{"exclude_patterns": ["**/tests/**", "**/docs/**"]}'
```

## Troubleshooting

### Missing Dependencies

If you encounter errors about missing dependencies, install them:

```bash
pip install matplotlib networkx pyyaml
```

### Simple Analyzer Issues

If you encounter analysis errors, these are typically related to file access or tool configuration:

1. **Permission Errors**: Ensure you have read access to all project files
2. **Tool Configuration**: Verify external tools (ruff, mypy, bandit) are properly installed if enabled
3. **Large Projects**: For very large projects, consider using exclude patterns to focus analysis

### Output Directory Issues

If you encounter issues with the output directory:

1. Make sure it exists before running the analysis:

   ```bash
   mkdir -p ./analysis_results
   ```

2. Ensure you have write permissions to the directory.

### Command Not Found

If the `vibe-check` command is not found:

1. Make sure you've activated the virtual environment.
2. Reinstall the package in development mode:

   ```bash
   pip install -e .
   ```

## Current Limitations

- The CLI is currently the most mature interface, but it still has some issues with the actor system and error handling.
- The analysis works and provides a summary, but the output file generation might not be working correctly in all cases.
- Future versions will improve the stability and output generation.

## Next Steps

As Vibe Check development progresses through the implementation phases outlined in the documentation, the CLI will become more robust and feature-complete. Future updates will include:

- Improved error handling
- More detailed reports
- Better visualization options
- Enhanced plugin management

For more information on the development roadmap, see the `INTERFACE_PRIORITIES.md` and `IMPLEMENTATION_PATH.md` documents in the `docs` directory.
