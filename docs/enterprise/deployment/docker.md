# Docker Deployment Guide

**Documentation Cycle: Strawberry | Updated: 28-06-2025**

## 🐳 **Docker Deployment for Vibe Check Enterprise**

This guide covers deploying Vibe Check Enterprise using Docker containers for production environments.

## 📋 **Prerequisites**

- Docker 20.10+ installed
- Docker Compose 2.0+ installed
- 4GB+ RAM available
- 10GB+ disk space

## 🚀 **Quick Start**

### **Single Container Deployment**
```bash
# Pull the latest enterprise image
docker pull vibecheck/enterprise:latest

# Run with default configuration
docker run -d \
  --name vibe-check-enterprise \
  -p 8000:8000 \
  -p 8001:8001 \
  -p 8002:8002 \
  -e VIBE_CHECK_MODE=enterprise \
  vibecheck/enterprise:latest
```

### **Docker Compose Deployment**
```yaml
# docker-compose.yml
version: '3.8'

services:
  vibe-check:
    image: vibecheck/enterprise:latest
    container_name: vibe-check-enterprise
    ports:
      - "8000:8000"  # REST API
      - "8001:8001"  # GraphQL API
      - "8002:8002"  # WebSocket API
    environment:
      - VIBE_CHECK_MODE=enterprise
      - VIBE_CHECK_API_HOST=0.0.0.0
      - VIBE_CHECK_DATABASE_URL=********************************************/vibecheck
      - VIBE_CHECK_REDIS_URL=redis://redis:6379/0
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  postgres:
    image: postgres:15
    container_name: vibe-check-postgres
    environment:
      - POSTGRES_DB=vibecheck
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: vibe-check-redis
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    container_name: vibe-check-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - vibe-check
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

## ⚙️ **Configuration**

### **Environment Variables**
```bash
# Core Configuration
VIBE_CHECK_MODE=enterprise
VIBE_CHECK_API_HOST=0.0.0.0
VIBE_CHECK_API_PORT=8000
VIBE_CHECK_GRAPHQL_PORT=8001
VIBE_CHECK_WEBSOCKET_PORT=8002

# Database Configuration
VIBE_CHECK_DATABASE_URL=********************************/vibecheck
VIBE_CHECK_REDIS_URL=redis://host:6379/0

# Security Configuration
VIBE_CHECK_JWT_SECRET=your-jwt-secret-key
VIBE_CHECK_API_KEY=your-api-key
VIBE_CHECK_ENCRYPTION_KEY=your-encryption-key

# Feature Flags
VIBE_CHECK_ENTERPRISE_REPORTING=true
VIBE_CHECK_TEAM_COLLABORATION=true
VIBE_CHECK_CICD_INTEGRATION=true
VIBE_CHECK_MONITORING=true
VIBE_CHECK_QUALITY_GATES=true

# Monitoring Configuration
VIBE_CHECK_METRICS_ENABLED=true
VIBE_CHECK_PROMETHEUS_PORT=9090
VIBE_CHECK_GRAFANA_PORT=3000

# Logging Configuration
VIBE_CHECK_LOG_LEVEL=INFO
VIBE_CHECK_LOG_FORMAT=json
VIBE_CHECK_LOG_FILE=/app/logs/vibe-check.log
```

### **Volume Mounts**
```bash
# Data persistence
-v ./data:/app/data

# Log files
-v ./logs:/app/logs

# Configuration files
-v ./config:/app/config

# SSL certificates
-v ./ssl:/app/ssl

# Custom plugins
-v ./plugins:/app/plugins
```

## 🔧 **Advanced Configuration**

### **Multi-Service Architecture**
```yaml
# docker-compose.production.yml
version: '3.8'

services:
  # Load Balancer
  traefik:
    image: traefik:v2.10
    command:
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock

  # API Gateway
  api-gateway:
    image: vibecheck/api-gateway:latest
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.api.rule=Host(`api.vibecheck.com`)"
    environment:
      - UPSTREAM_REST=http://vibe-check-rest:8000
      - UPSTREAM_GRAPHQL=http://vibe-check-graphql:8001
      - UPSTREAM_WEBSOCKET=http://vibe-check-websocket:8002

  # REST API Service
  vibe-check-rest:
    image: vibecheck/enterprise:latest
    command: ["python", "-m", "vibe_check.enterprise.api.rest"]
    environment:
      - VIBE_CHECK_SERVICE=rest
      - VIBE_CHECK_PORT=8000
    deploy:
      replicas: 3

  # GraphQL Service
  vibe-check-graphql:
    image: vibecheck/enterprise:latest
    command: ["python", "-m", "vibe_check.enterprise.api.graphql"]
    environment:
      - VIBE_CHECK_SERVICE=graphql
      - VIBE_CHECK_PORT=8001
    deploy:
      replicas: 2

  # WebSocket Service
  vibe-check-websocket:
    image: vibecheck/enterprise:latest
    command: ["python", "-m", "vibe_check.enterprise.api.websocket"]
    environment:
      - VIBE_CHECK_SERVICE=websocket
      - VIBE_CHECK_PORT=8002
    deploy:
      replicas: 2

  # Analysis Workers
  analysis-worker:
    image: vibecheck/enterprise:latest
    command: ["python", "-m", "vibe_check.core.workers.analysis"]
    environment:
      - VIBE_CHECK_SERVICE=worker
      - WORKER_TYPE=analysis
    deploy:
      replicas: 4

  # Monitoring Stack
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana

volumes:
  grafana_data:
```

### **Health Checks**
```yaml
services:
  vibe-check:
    # ... other configuration
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/system/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

## 📊 **Monitoring & Observability**

### **Prometheus Configuration**
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'vibe-check'
    static_configs:
      - targets: ['vibe-check:9090']
    metrics_path: '/metrics'
    scrape_interval: 5s

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
```

### **Grafana Dashboards**
```json
{
  "dashboard": {
    "title": "Vibe Check Enterprise",
    "panels": [
      {
        "title": "API Requests",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(vibe_check_api_requests_total[5m])",
            "legendFormat": "{{method}} {{endpoint}}"
          }
        ]
      },
      {
        "title": "Analysis Performance",
        "type": "graph",
        "targets": [
          {
            "expr": "vibe_check_analysis_duration_seconds",
            "legendFormat": "Analysis Duration"
          }
        ]
      }
    ]
  }
}
```

## 🔒 **Security**

### **SSL/TLS Configuration**
```nginx
# nginx.conf
server {
    listen 443 ssl http2;
    server_name api.vibecheck.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    location /api/v1/ {
        proxy_pass http://vibe-check:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /graphql {
        proxy_pass http://vibe-check:8001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location /ws {
        proxy_pass http://vibe-check:8002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }
}
```

### **Secrets Management**
```bash
# Using Docker Secrets
echo "your-jwt-secret" | docker secret create jwt_secret -
echo "your-api-key" | docker secret create api_key -

# In docker-compose.yml
services:
  vibe-check:
    secrets:
      - jwt_secret
      - api_key
    environment:
      - VIBE_CHECK_JWT_SECRET_FILE=/run/secrets/jwt_secret
      - VIBE_CHECK_API_KEY_FILE=/run/secrets/api_key

secrets:
  jwt_secret:
    external: true
  api_key:
    external: true
```

## 🚀 **Deployment Scripts**

### **Production Deployment**
```bash
#!/bin/bash
# deploy.sh

set -e

echo "🚀 Deploying Vibe Check Enterprise..."

# Pull latest images
docker-compose pull

# Stop existing containers
docker-compose down

# Start new containers
docker-compose up -d

# Wait for health checks
echo "⏳ Waiting for services to be healthy..."
sleep 30

# Verify deployment
curl -f http://localhost:8000/api/v1/system/health || exit 1

echo "✅ Deployment successful!"
```

### **Backup Script**
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backups/$(date +%Y%m%d_%H%M%S)"
mkdir -p $BACKUP_DIR

# Backup database
docker exec vibe-check-postgres pg_dump -U postgres vibecheck > $BACKUP_DIR/database.sql

# Backup data volumes
docker run --rm -v vibe-check_data:/data -v $BACKUP_DIR:/backup alpine tar czf /backup/data.tar.gz -C /data .

# Backup configuration
cp -r ./config $BACKUP_DIR/

echo "✅ Backup completed: $BACKUP_DIR"
```

## 🔧 **Troubleshooting**

### **Common Issues**
```bash
# Check container logs
docker logs vibe-check-enterprise

# Check service health
docker exec vibe-check-enterprise curl http://localhost:8000/api/v1/system/health

# Check database connection
docker exec vibe-check-enterprise python -c "
from vibe_check.core.database import test_connection
print('Database:', test_connection())
"

# Check Redis connection
docker exec vibe-check-enterprise redis-cli -h redis ping

# Monitor resource usage
docker stats vibe-check-enterprise
```

### **Performance Tuning**
```yaml
# Resource limits
services:
  vibe-check:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
```

## 📚 **Next Steps**

- [Kubernetes Deployment](./kubernetes.md) - Scale with Kubernetes
- [Cloud Deployment](./cloud.md) - Deploy to AWS/Azure/GCP
- [Monitoring Setup](./monitoring.md) - Advanced monitoring configuration
- [Backup & Recovery](./backup.md) - Data protection strategies
