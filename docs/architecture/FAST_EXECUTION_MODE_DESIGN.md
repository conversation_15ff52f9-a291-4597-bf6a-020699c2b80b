# Fast Execution Mode Architecture Design

**Documentation Cycle: Strawberry**  
**Date: 29-06-2025**  
**Status: Architecture Design Complete**

## Overview

The Fast Execution Mode is designed specifically for pre-commit hook integration, providing rapid analysis of only changed files with a target execution time of <30 seconds. This mode prioritizes developer workflow efficiency while maintaining code quality standards.

## Design Principles

### 1. Incremental Analysis
- **Changed Files Only**: Analyze only files modified in the current commit
- **Differential Analysis**: Compare against previous analysis results
- **Smart Caching**: Leverage existing analysis cache for unchanged files
- **Partial Rule Execution**: Run only rules relevant to changed content

### 2. Performance Optimization
- **Parallel Processing**: Concurrent analysis of multiple files
- **Rule Prioritization**: Execute critical rules first
- **Early Termination**: Stop on first critical issue (optional)
- **Memory Efficiency**: Minimal memory footprint for large repositories

### 3. Git Integration
- **Native Git Commands**: Use git diff for change detection
- **Staging Area Analysis**: Analyze staged changes only
- **Branch Comparison**: Compare against target branch
- **Conflict Detection**: Handle merge conflicts gracefully

## Architecture Components

### 1. Git Integration Layer

```python
# vibe_check/core/vcs/git_integration.py
class GitChangeDetector:
    """Detects changed files using Git commands."""
    
    def get_staged_files(self) -> List[Path]:
        """Get list of staged files for commit."""
        
    def get_modified_files(self, base_ref: str = "HEAD") -> List[Path]:
        """Get list of modified files compared to base reference."""
        
    def get_file_diff(self, file_path: Path, base_ref: str = "HEAD") -> FileDiff:
        """Get detailed diff for specific file."""
        
    def is_git_repository(self, path: Path) -> bool:
        """Check if path is within a Git repository."""
```

### 2. Fast Analysis Engine

```python
# vibe_check/core/vcs/fast_engine.py
class FastAnalysisEngine:
    """Optimized analysis engine for pre-commit usage."""
    
    def __init__(self, config: FastModeConfig):
        self.config = config
        self.git_detector = GitChangeDetector()
        self.cache_manager = FastCacheManager()
        self.rule_prioritizer = RulePrioritizer()
    
    async def analyze_changes(self, base_ref: str = "HEAD") -> FastAnalysisResult:
        """Analyze only changed files with optimizations."""
        
    async def analyze_staged_files(self) -> FastAnalysisResult:
        """Analyze staged files for pre-commit hook."""
        
    def estimate_execution_time(self, files: List[Path]) -> float:
        """Estimate analysis execution time."""
```

### 3. Rule Prioritization System

```python
# vibe_check/core/vcs/rule_prioritization.py
class RulePrioritizer:
    """Prioritizes rules for fast execution mode."""
    
    def get_critical_rules(self) -> List[AnalysisRule]:
        """Get rules that must run in fast mode."""
        
    def get_fast_rules(self) -> List[AnalysisRule]:
        """Get rules optimized for fast execution."""
        
    def prioritize_for_file_type(self, file_path: Path) -> List[AnalysisRule]:
        """Get prioritized rules for specific file type."""
        
    def estimate_rule_execution_time(self, rule: AnalysisRule, file_size: int) -> float:
        """Estimate execution time for rule on file."""
```

### 4. Fast Cache Manager

```python
# vibe_check/core/vcs/fast_cache.py
class FastCacheManager:
    """Optimized caching for fast execution mode."""
    
    def get_cached_result(self, file_path: Path, file_hash: str) -> Optional[AnalysisResult]:
        """Get cached analysis result for file."""
        
    def cache_result(self, file_path: Path, file_hash: str, result: AnalysisResult) -> None:
        """Cache analysis result for future use."""
        
    def invalidate_cache_for_file(self, file_path: Path) -> None:
        """Invalidate cache entries for modified file."""
        
    def get_cache_hit_rate(self) -> float:
        """Get cache hit rate for performance monitoring."""
```

## Fast Mode Configuration

### Configuration Schema

```python
@dataclass
class FastModeConfig:
    """Configuration for fast execution mode."""
    
    # Performance settings
    max_execution_time_seconds: float = 30.0
    max_files_to_analyze: int = 50
    parallel_workers: int = 4
    
    # Rule selection
    critical_rules_only: bool = False
    max_rules_per_file: int = 10
    rule_timeout_seconds: float = 5.0
    
    # Git integration
    base_reference: str = "HEAD"
    analyze_staged_only: bool = True
    include_untracked_files: bool = False
    
    # Caching
    cache_enabled: bool = True
    cache_ttl_hours: int = 24
    max_cache_size_mb: int = 100
    
    # Early termination
    fail_fast: bool = False
    max_issues_per_file: int = 5
    stop_on_critical_issue: bool = True
```

### Rule Categories for Fast Mode

```python
class FastModeRuleCategory(Enum):
    """Rule categories optimized for fast execution."""
    
    CRITICAL = "critical"      # Security, syntax errors (always run)
    FAST = "fast"             # Quick style checks (<100ms)
    STANDARD = "standard"     # Normal rules (100-500ms)
    SLOW = "slow"            # Complex analysis (>500ms, skip in fast mode)
```

## Implementation Strategy

### Phase 1: Git Integration (1 day)

#### 1.1 Git Change Detection
```python
class GitChangeDetector:
    def get_staged_files(self) -> List[Path]:
        """Implementation using 'git diff --cached --name-only'."""
        
    def get_modified_files(self, base_ref: str = "HEAD") -> List[Path]:
        """Implementation using 'git diff --name-only'."""
        
    def get_file_content_at_ref(self, file_path: Path, ref: str) -> str:
        """Get file content at specific Git reference."""
```

#### 1.2 Change Analysis
```python
class ChangeAnalyzer:
    def analyze_file_changes(self, file_path: Path) -> FileChangeInfo:
        """Analyze what changed in a file."""
        
    def get_changed_functions(self, file_path: Path) -> List[FunctionChange]:
        """Get list of modified functions."""
        
    def should_run_rule_for_changes(self, rule: AnalysisRule, changes: FileChangeInfo) -> bool:
        """Determine if rule should run based on changes."""
```

### Phase 2: Fast Analysis Engine (1-2 days)

#### 2.1 Core Engine
```python
class FastAnalysisEngine:
    async def analyze_changes(self, base_ref: str = "HEAD") -> FastAnalysisResult:
        # 1. Detect changed files
        changed_files = self.git_detector.get_modified_files(base_ref)
        
        # 2. Filter files by type and size
        analyzable_files = self._filter_analyzable_files(changed_files)
        
        # 3. Check cache for existing results
        cache_results = await self._check_cache(analyzable_files)
        
        # 4. Analyze uncached files
        new_results = await self._analyze_files_fast(cache_results.uncached_files)
        
        # 5. Combine and return results
        return self._combine_results(cache_results.cached_results, new_results)
```

#### 2.2 Performance Monitoring
```python
class FastModePerformanceMonitor:
    def track_execution_time(self, operation: str, duration: float) -> None:
        """Track execution time for performance analysis."""
        
    def get_performance_report(self) -> FastModePerformanceReport:
        """Generate performance report for optimization."""
        
    def suggest_optimizations(self) -> List[PerformanceOptimization]:
        """Suggest optimizations based on performance data."""
```

### Phase 3: Optimization and Caching (1 day)

#### 3.1 Smart Caching
```python
class SmartCache:
    def should_cache_result(self, file_path: Path, analysis_time: float) -> bool:
        """Determine if result should be cached based on analysis cost."""
        
    def get_cache_key(self, file_path: Path, rule_ids: List[str]) -> str:
        """Generate cache key including file hash and rule versions."""
        
    def cleanup_stale_cache(self) -> None:
        """Remove stale cache entries to maintain performance."""
```

#### 3.2 Rule Optimization
```python
class RuleOptimizer:
    def optimize_rule_for_fast_mode(self, rule: AnalysisRule) -> OptimizedRule:
        """Create optimized version of rule for fast execution."""
        
    def create_rule_subset(self, rules: List[AnalysisRule], time_budget: float) -> List[AnalysisRule]:
        """Create subset of rules that fit within time budget."""
```

## Performance Targets

### Execution Time Breakdown
- **Git Operations**: <2 seconds (file detection, diff analysis)
- **Cache Operations**: <1 second (cache lookup, validation)
- **Rule Execution**: <25 seconds (actual analysis)
- **Result Processing**: <2 seconds (aggregation, formatting)
- **Total Target**: <30 seconds

### Scalability Targets
- **Small Changes** (1-5 files): <10 seconds
- **Medium Changes** (6-20 files): <20 seconds
- **Large Changes** (21-50 files): <30 seconds
- **Very Large Changes** (>50 files): Graceful degradation with warnings

## Integration Points

### Pre-commit Hook Integration
```bash
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/ptzajac/vibe_check
    rev: v0.3.0
    hooks:
      - id: vibe-check-fast
        name: Vibe Check Fast Analysis
        entry: vibe-check analyze --fast-mode --staged-only
        language: python
        pass_filenames: false
        always_run: false
```

### CLI Integration
```bash
# Fast mode commands
vibe-check analyze --fast-mode                    # Analyze changed files
vibe-check analyze --fast-mode --staged-only      # Analyze staged files only
vibe-check analyze --fast-mode --since=main       # Compare against main branch
vibe-check analyze --fast-mode --time-budget=15   # Custom time budget
```

## Success Metrics

### Performance Metrics
- **Execution Time**: <30 seconds for 95% of commits
- **Cache Hit Rate**: >70% for repeated analysis
- **Memory Usage**: <200MB peak memory usage
- **CPU Efficiency**: <80% CPU utilization

### Quality Metrics
- **Issue Detection Rate**: >90% of critical issues detected
- **False Positive Rate**: <5% false positives
- **Rule Coverage**: >80% of important rules executed
- **Developer Satisfaction**: >4.5/5 rating for speed

## Risk Mitigation

### Performance Risks
- **Large File Changes**: Implement file size limits and warnings
- **Complex Rules**: Create fast alternatives for slow rules
- **Git Repository Size**: Optimize Git operations for large repositories

### Quality Risks
- **Missed Issues**: Provide full analysis option for comprehensive checking
- **Cache Invalidation**: Implement robust cache invalidation strategies
- **Rule Selection**: Allow customization of rule priorities

This architecture provides a comprehensive foundation for implementing fast execution mode that meets the <30 second target while maintaining code quality standards.
