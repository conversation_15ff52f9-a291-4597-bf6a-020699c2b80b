# Unified CLI Command Reference for VibeCheck Development Intelligence Platform

**Documentation Cycle: Strawberry | Updated: 30-06-2025**

**Date:** 2025-06-30
**Version:** 1.0
**Status:** Comprehensive CLI Command Specification

## Executive Summary

This document provides the complete CLI command reference for VibeCheck's Development Intelligence Platform, establishing the CLI-first architecture that serves as the foundation for all other interfaces. Every capability of VibeCheck is accessible through these CLI commands, ensuring universal access and scriptability.

## 🎯 **CLI-FIRST ARCHITECTURE PRINCIPLE**

### **Core Philosophy**
**"CLI is the primary interface. All features must be fully accessible via CLI before implementation in other interfaces."**

### **Design Principles**
1. **Completeness**: Every feature accessible via CLI
2. **Consistency**: Uniform command patterns and options
3. **Composability**: Commands can be chained and scripted
4. **Discoverability**: Built-in help and command completion
5. **Flexibility**: Support for all user levels (beginner to expert)

## 📋 **COMMAND HIERARCHY**

### **Top-Level Commands**
```bash
vibe-check <command> [options] [arguments]

Commands:
  analyze     # Core analysis capabilities
  docs        # Documentation intelligence
  team        # Team collaboration
  monitor     # Monitoring and observability
  llm         # LLM integration (optional)
  mcp         # MCP server and host capabilities
  config      # Configuration management
  plugin      # Plugin management
  export      # Export and reporting
  server      # Server mode operations
```

## 🔍 **CORE ANALYSIS COMMANDS**

### **Primary Analysis Command**
```bash
vibe-check analyze [PATH] [OPTIONS]

# Core analysis with all features
vibe-check analyze ./project --comprehensive

# Specific analysis types
vibe-check analyze ./project --oop --flow --security
vibe-check analyze ./project --quality --performance --docs

# Output control
vibe-check analyze ./project --output json --file results.json
vibe-check analyze ./project --format html --export dashboard.html

# Performance control
vibe-check analyze ./project --parallel 8 --incremental
vibe-check analyze ./project --fast --cache-enabled

# LLM integration (optional)
vibe-check analyze ./project --llm-enhance --model local
vibe-check analyze ./project --no-llm --fallback-only
```

### **Specialized Analysis Commands**
```bash
# Quality analysis
vibe-check quality [PATH] [OPTIONS]
vibe-check quality ./src --threshold high --fix-auto
vibe-check quality ./src --rules style,security,complexity
vibe-check quality ./src --compare-baseline --trend-analysis

# Security analysis
vibe-check security [PATH] [OPTIONS]
vibe-check security ./src --severity critical --report sarif
vibe-check security ./src --scan-dependencies --check-vulnerabilities
vibe-check security ./src --privacy-check --data-flow-analysis

# Performance analysis
vibe-check performance [PATH] [OPTIONS]
vibe-check performance ./src --profile memory --benchmark
vibe-check performance ./src --bottleneck-detection --optimization-hints
vibe-check performance ./src --compare-versions --regression-check

# Object-oriented analysis
vibe-check oop [PATH] [OPTIONS]
vibe-check oop ./src --inheritance-map --mro-analysis
vibe-check oop ./src --diamond-detection --polymorphism-analysis
vibe-check oop ./src --design-patterns --architecture-violations

# Program flow analysis
vibe-check flow [PATH] [OPTIONS]
vibe-check flow ./src --control-flow --data-flow
vibe-check flow ./src --unreachable-code --path-analysis
vibe-check flow ./src --complexity-metrics --execution-paths
```

## 📚 **DOCUMENTATION INTELLIGENCE COMMANDS**

### **Documentation Analysis**
```bash
vibe-check docs analyze [PATH] [OPTIONS]

# Comprehensive documentation analysis
vibe-check docs analyze ./docs --semantic --redundancy --quality

# Specific analysis types
vibe-check docs analyze ./docs --redundancy-threshold 0.8
vibe-check docs analyze ./docs --quality-metrics --readability
vibe-check docs analyze ./docs --cross-references --broken-links

# Code-documentation mapping
vibe-check docs map --code-path ./src --docs-path ./docs
vibe-check docs map --coverage-analysis --gap-detection
vibe-check docs map --bidirectional-links --correlation-strength

# Interactive documentation graph
vibe-check docs graph --interactive --3d --export html
vibe-check docs graph --obsidian-style --temporal-view
vibe-check docs graph --semantic-clusters --relationship-strength

# Documentation generation and improvement
vibe-check docs generate --from-code ./src --template api
vibe-check docs improve --suggestions --auto-fix
vibe-check docs validate --consistency --completeness
```

### **Knowledge Management**
```bash
vibe-check knowledge [SUBCOMMAND] [OPTIONS]

# Knowledge graph operations
vibe-check knowledge graph --build --interactive
vibe-check knowledge graph --explore --starting-point README.md
vibe-check knowledge graph --export --format graphml

# Zettelkasten operations
vibe-check knowledge atomic --create --from-large-docs
vibe-check knowledge atomic --link --auto-discover
vibe-check knowledge atomic --cluster --semantic-similarity

# Daily notes and journaling
vibe-check knowledge journal --daily --auto-context
vibe-check knowledge journal --weekly-summary --team-insights
vibe-check knowledge journal --link-commits --correlation-analysis
```

## 👥 **TEAM COLLABORATION COMMANDS**

### **Team Platform**
```bash
vibe-check team [SUBCOMMAND] [OPTIONS]

# Team initialization
vibe-check team init --github-repo owner/repo
vibe-check team init --gitlab-project 12345
vibe-check team init --local-git --team-config

# Team dashboard
vibe-check team dashboard --web --port 3000
vibe-check team dashboard --terminal --live-updates
vibe-check team dashboard --export --format pdf

# Team synchronization
vibe-check team sync --issues --pull-requests --commits
vibe-check team sync --auto-schedule --interval 1h
vibe-check team sync --conflict-resolution --merge-strategy

# Team analytics
vibe-check team performance --period week --metrics productivity
vibe-check team performance --individual --team-comparison
vibe-check team performance --trends --predictive-analysis

# Project mapping
vibe-check team map --project-structure --health-indicators
vibe-check team map --dependencies --risk-assessment
vibe-check team map --interactive --collaboration-view
```

### **Issue Management**
```bash
vibe-check issues [SUBCOMMAND] [OPTIONS]

# Issue analysis
vibe-check issues analyze --correlation-with-code
vibe-check issues analyze --pattern-detection --root-cause
vibe-check issues analyze --resolution-time --efficiency

# Issue tracking
vibe-check issues track --simple --lightweight
vibe-check issues track --integration github --bidirectional
vibe-check issues track --auto-create --from-analysis

# Issue resolution
vibe-check issues resolve --suggestions --code-correlation
vibe-check issues resolve --documentation-links --knowledge-base
vibe-check issues resolve --team-assignment --skill-matching
```

## 📊 **MONITORING AND OBSERVABILITY COMMANDS**

### **Monitoring Platform**
```bash
vibe-check monitor [SUBCOMMAND] [OPTIONS]

# Monitoring setup
vibe-check monitor start --background --auto-detect
vibe-check monitor start --config monitoring.yaml --enterprise
vibe-check monitor start --zero-config --beginner-friendly

# Dashboard operations
vibe-check monitor dashboard --web --port 8080
vibe-check monitor dashboard --terminal --live-metrics
vibe-check monitor dashboard --custom --template performance

# Alerting system
vibe-check monitor alerts setup --intelligent --auto-thresholds
vibe-check monitor alerts configure --channels slack,email
vibe-check monitor alerts test --all-channels --dry-run

# Metrics collection
vibe-check monitor metrics --collect --real-time
vibe-check monitor metrics --export prometheus --endpoint /metrics
vibe-check monitor metrics --historical --time-range 7d
```

### **Performance Monitoring**
```bash
vibe-check perf [SUBCOMMAND] [OPTIONS]

# Performance profiling
vibe-check perf profile --target application --duration 60s
vibe-check perf profile --memory --cpu --io --comprehensive
vibe-check perf profile --bottleneck-detection --optimization-hints

# Performance analysis
vibe-check perf analyze --baseline-comparison --regression-detection
vibe-check perf analyze --trends --predictive-modeling
vibe-check perf analyze --team-impact --productivity-correlation

# Performance optimization
vibe-check perf optimize --suggestions --auto-apply-safe
vibe-check perf optimize --code-recommendations --architecture-advice
vibe-check perf optimize --monitoring-setup --continuous-improvement
```

## 🤖 **LLM INTEGRATION COMMANDS (OPTIONAL)**

### **LLM Management**
```bash
vibe-check llm [SUBCOMMAND] [OPTIONS]

# Model management
vibe-check llm list --local --cloud --available
vibe-check llm download codellama-7b --source huggingface
vibe-check llm install --model starcoder-15b --optimize-hardware

# Model selection
vibe-check llm select --model auto --task code-analysis
vibe-check llm select --prefer local --fallback cloud
vibe-check llm select --cost-optimize --quality-threshold high

# LLM analysis (enhancement only)
vibe-check llm analyze ./src --model local --explain-detailed
vibe-check llm analyze ./src --enhance-insights --cost-limit 5.0
vibe-check llm analyze ./src --privacy-mode --local-only

# Cost and usage management
vibe-check llm usage --monthly-report --cost-breakdown
vibe-check llm budget --set-limit 50.0 --alerts-enabled
vibe-check llm optimize --cost-efficiency --quality-balance
```

## 🔌 **MCP INTEGRATION COMMANDS**

### **MCP Server Operations**
```bash
vibe-check mcp server [SUBCOMMAND] [OPTIONS]

# Server management
vibe-check mcp server start --port 3001 --capabilities all
vibe-check mcp server stop --graceful --save-state
vibe-check mcp server status --health-check --performance-metrics

# Capability exposure
vibe-check mcp server expose --analysis --documentation --team
vibe-check mcp server configure --security --authentication
vibe-check mcp server logs --real-time --level debug
```

### **MCP Host Operations**
```bash
vibe-check mcp host [SUBCOMMAND] [OPTIONS]

# MCP tool management
vibe-check mcp install github-mcp --source github --auto-configure
vibe-check mcp install code-review-mcp --source npm --version latest
vibe-check mcp list --installed --available --running

# MCP orchestration
vibe-check mcp orchestrate --task complex-analysis --tools auto
vibe-check mcp orchestrate --workflow code-review --parallel
vibe-check mcp orchestrate --custom --config workflow.yaml
```

## ⚙️ **CONFIGURATION MANAGEMENT COMMANDS**

### **Configuration Operations**
```bash
vibe-check config [SUBCOMMAND] [OPTIONS]

# Configuration initialization
vibe-check config init --template basic --level beginner
vibe-check config init --template enterprise --team-settings
vibe-check config init --interactive --guided-setup

# Configuration management
vibe-check config validate --fix-issues --backup-original
vibe-check config export --format yaml --include-secrets false
vibe-check config import config.yaml --merge --validate

# Preset management
vibe-check config preset list --built-in --user --shared
vibe-check config preset apply django-strict --project-scope
vibe-check config preset create my-team-standard --share-team
```

### **User Level Configuration**
```bash
# Beginner mode
vibe-check config level beginner --simple-output --guided-help

# Intermediate mode  
vibe-check config level intermediate --detailed-output --advanced-options

# Expert mode
vibe-check config level expert --full-control --raw-output --scripting-mode
```

## 🔧 **PLUGIN MANAGEMENT COMMANDS**

### **Plugin Operations**
```bash
vibe-check plugin [SUBCOMMAND] [OPTIONS]

# Plugin management
vibe-check plugin list --installed --available --updates
vibe-check plugin install mypy-integration --auto-configure
vibe-check plugin update --all --check-compatibility

# Plugin configuration
vibe-check plugin configure ruff-plugin --interactive
vibe-check plugin enable --all --selective security-plugins
vibe-check plugin disable legacy-plugins --keep-config

# Meta-analysis
vibe-check plugin meta-analyze --correlate-results --unified-report
vibe-check plugin meta-analyze --conflict-resolution --priority-rules
vibe-check plugin meta-analyze --performance-comparison --optimization
```

## 📤 **EXPORT AND REPORTING COMMANDS**

### **Export Operations**
```bash
vibe-check export [SUBCOMMAND] [OPTIONS]

# Report generation
vibe-check export report --format html --template executive
vibe-check export report --format pdf --include-charts --branding
vibe-check export report --format json --api-compatible --structured

# Data export
vibe-check export data --format csv --metrics-only
vibe-check export data --format sqlite --complete-database
vibe-check export data --format prometheus --metrics-endpoint

# Visualization export
vibe-check export viz --interactive-dashboard --standalone
vibe-check export viz --static-charts --high-resolution
vibe-check export viz --3d-models --architectural-views
```

## 🌐 **SERVER MODE COMMANDS**

### **Server Operations**
```bash
vibe-check server [SUBCOMMAND] [OPTIONS]

# Server management
vibe-check server start --mode production --port 8000
vibe-check server start --mode development --auto-reload
vibe-check server start --mode team --collaboration-features

# API operations
vibe-check server api --enable-rest --enable-graphql --enable-websocket
vibe-check server api --authentication oauth2 --authorization rbac
vibe-check server api --rate-limiting --security-headers

# Health and monitoring
vibe-check server health --check-all --detailed-status
vibe-check server metrics --prometheus --grafana-compatible
vibe-check server logs --real-time --structured --level info
```

## 🎯 **COMMAND COMPOSITION AND SCRIPTING**

### **Pipeline Operations**
```bash
# Analysis pipeline
vibe-check analyze ./src --output json | \
vibe-check export report --format html --template detailed

# Team workflow
vibe-check team sync --issues | \
vibe-check issues analyze --correlation-with-code | \
vibe-check team dashboard --update-metrics

# Monitoring pipeline
vibe-check monitor metrics --collect | \
vibe-check perf analyze --trends | \
vibe-check export viz --performance-dashboard
```

### **Automation Scripts**
```bash
#!/bin/bash
# Daily team analysis script

# Sync team data
vibe-check team sync --auto

# Run comprehensive analysis
vibe-check analyze ./project --comprehensive --llm-enhance

# Generate team report
vibe-check export report --format pdf --template team-daily

# Update monitoring dashboard
vibe-check monitor dashboard --update --team-metrics
```

## 🏁 **CONCLUSION**

This unified CLI command reference establishes VibeCheck's CLI-first architecture, ensuring that every capability of the Development Intelligence Platform is accessible through a consistent, powerful command-line interface. This foundation enables:

1. **Universal Access**: All features available regardless of interface preferences
2. **Automation**: Complete scriptability for CI/CD and workflow automation
3. **Consistency**: Uniform command patterns across all capabilities
4. **Extensibility**: Clear patterns for adding new features
5. **User Progression**: Support for all skill levels from beginner to expert

**Strategic Impact**: The CLI-first approach ensures VibeCheck can serve as the foundation for all other interfaces while providing immediate value to command-line users and enabling powerful automation capabilities.
