# User Configuration & Preset System Specification

**Documentation Cycle: Strawberry | Updated: 29-06-2025**

## Executive Summary

This document provides detailed technical specifications for the User Configuration & Preset System epic, enabling users to create and manage custom analysis configurations without manually excluding/modifying rules each time.

## System Architecture

### PresetDataModel Specification

```python
from typing import TypedDict, Optional, List, Dict, Any
from enum import Enum

class AnalysisProfile(Enum):
    MINIMAL = "minimal"
    STANDARD = "standard"
    STRICT = "strict"
    CUSTOM = "custom"

class PresetMetadata(TypedDict):
    name: str
    description: str
    author: str
    version: str
    created_at: str
    updated_at: str
    tags: List[str]

class RuleConfiguration(TypedDict):
    rule_ids: Optional[List[str]]
    exclude_rules: Optional[List[str]]
    categories: Optional[List[str]]
    exclude_categories: Optional[List[str]]
    severity_filter: Optional[str]

class ExternalToolConfiguration(TypedDict):
    tools: Optional[List[str]]
    exclude_tools: Optional[List[str]]
    tool_configs: Optional[Dict[str, Any]]

class AnalysisConfiguration(TypedDict):
    profile: AnalysisProfile
    vcs_mode: bool
    detailed: bool
    quiet: bool
    no_save: bool
    fast_mode: bool

class PresetDataModel(TypedDict):
    metadata: PresetMetadata
    rule_config: RuleConfiguration
    tool_config: ExternalToolConfiguration
    analysis_config: AnalysisConfiguration
    inheritance: Optional[str]  # Base preset name
```

### PresetManager Class Specification

```python
class PresetManager:
    """Manages preset CRUD operations with validation and storage."""
    
    def __init__(self, preset_dir: Path = Path.home() / '.vibe_check' / 'presets'):
        self.preset_dir = preset_dir
        self.validator = PresetValidator()
    
    async def create_preset(self, preset: PresetDataModel) -> bool:
        """Create new preset with validation."""
        
    async def save_preset(self, name: str, preset: PresetDataModel) -> bool:
        """Save preset to storage."""
        
    async def load_preset(self, name: str) -> PresetDataModel:
        """Load preset from storage with inheritance resolution."""
        
    async def list_presets(self) -> List[PresetMetadata]:
        """List all available presets."""
        
    async def delete_preset(self, name: str) -> bool:
        """Delete preset from storage."""
        
    async def validate_preset(self, preset: PresetDataModel) -> ValidationResult:
        """Validate preset configuration."""
        
    async def export_preset(self, name: str, output_path: Path) -> bool:
        """Export preset to file."""
        
    async def import_preset(self, input_path: Path) -> bool:
        """Import preset from file."""
```

## CLI Integration Specification

### Command Structure

```bash
# Preset Management Commands
vibe-check preset create <name> [options]
vibe-check preset save <name> [options]
vibe-check preset load <name>
vibe-check preset list [--detailed]
vibe-check preset delete <name>
vibe-check preset validate <name>
vibe-check preset export <name> <output-file>
vibe-check preset import <input-file>

# Analysis with Presets
vibe-check analyze <path> --preset <name>
vibe-check analyze <path> --preset-file <file>
```

### Preset Creation Options

```bash
vibe-check preset create security-focused \
    --profile strict \
    --categories security,complexity \
    --tools ruff,bandit \
    --vcs-mode \
    --description "Security-focused analysis preset"
```

## Preset Templates

### Built-in Templates

1. **minimal**: Basic analysis with essential rules only
2. **standard**: Balanced analysis for most projects
3. **strict**: Comprehensive analysis with all rules
4. **django**: Django-specific rules and configurations
5. **flask**: Flask-specific rules and configurations
6. **fastapi**: FastAPI-specific rules and configurations
7. **security-focused**: Security and vulnerability analysis
8. **performance-focused**: Performance and optimization analysis

### Template Specifications

```json
{
  "minimal": {
    "metadata": {
      "name": "minimal",
      "description": "Essential analysis for quick checks",
      "author": "vibe-check",
      "version": "1.0.0",
      "tags": ["built-in", "quick", "essential"]
    },
    "rule_config": {
      "categories": ["style", "security"],
      "severity_filter": "warning"
    },
    "analysis_config": {
      "profile": "minimal",
      "vcs_mode": true,
      "quiet": true
    }
  }
}
```

## Storage Format

### File Structure

```
~/.vibe_check/presets/
├── built-in/
│   ├── minimal.json
│   ├── standard.json
│   ├── strict.json
│   ├── django.json
│   ├── flask.json
│   ├── fastapi.json
│   ├── security-focused.json
│   └── performance-focused.json
├── user/
│   ├── my-project.json
│   ├── team-standard.json
│   └── custom-rules.json
└── shared/
    ├── company-standard.json
    └── imported-presets.json
```

### JSON Schema Validation

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "type": "object",
  "required": ["metadata", "rule_config", "analysis_config"],
  "properties": {
    "metadata": {
      "type": "object",
      "required": ["name", "description", "author", "version"],
      "properties": {
        "name": {"type": "string", "pattern": "^[a-zA-Z0-9_-]+$"},
        "description": {"type": "string"},
        "author": {"type": "string"},
        "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$"},
        "created_at": {"type": "string", "format": "date-time"},
        "updated_at": {"type": "string", "format": "date-time"},
        "tags": {"type": "array", "items": {"type": "string"}}
      }
    }
  }
}
```

## Validation Framework

### PresetValidator Class

```python
class PresetValidator:
    """Validates preset configurations against current rule set."""
    
    def validate_rule_ids(self, rule_ids: List[str]) -> ValidationResult:
        """Validate rule IDs exist in current rule set."""
        
    def validate_categories(self, categories: List[str]) -> ValidationResult:
        """Validate categories exist in current system."""
        
    def validate_tools(self, tools: List[str]) -> ValidationResult:
        """Validate external tools are available."""
        
    def validate_conflicts(self, preset: PresetDataModel) -> ValidationResult:
        """Check for configuration conflicts."""
        
    def validate_inheritance(self, preset: PresetDataModel) -> ValidationResult:
        """Validate preset inheritance chain."""
```

## Integration Points

### VCS Engine Integration

```python
# In VCS engine
async def apply_preset(self, preset_name: str) -> None:
    """Apply preset configuration to analysis."""
    preset = await self.preset_manager.load_preset(preset_name)
    self.configure_from_preset(preset)
```

### CLI Integration

```python
# In CLI commands
@click.option('--preset', help='Apply analysis preset')
@click.option('--preset-file', help='Apply preset from file')
async def analyze(path: str, preset: str = None, preset_file: str = None):
    """Analysis command with preset support."""
    if preset:
        preset_config = await preset_manager.load_preset(preset)
        apply_preset_to_analysis(preset_config)
```

## Performance Requirements

- **Preset Loading**: <100ms for typical presets
- **Validation**: <500ms for comprehensive validation
- **Storage Operations**: <200ms for save/load operations
- **CLI Response**: <1s for all preset commands

## Error Handling

### Validation Errors

```python
class PresetValidationError(Exception):
    """Raised when preset validation fails."""
    
    def __init__(self, errors: List[str]):
        self.errors = errors
        super().__init__(f"Preset validation failed: {', '.join(errors)}")
```

### User-Friendly Error Messages

```
❌ Preset validation failed:
   • Rule 'INVALID001' does not exist
   • Category 'invalid-category' is not supported
   • Tool 'unknown-tool' is not available

💡 Tip: Use 'vibe-check rules list' to see available rules
💡 Tip: Use 'vibe-check categories list' to see available categories
```

## Testing Strategy

### Unit Tests

- PresetDataModel validation
- PresetManager CRUD operations
- PresetValidator functionality
- CLI command integration

### Integration Tests

- End-to-end preset workflow
- VCS engine integration
- File system operations
- Error handling scenarios

### Performance Tests

- Large preset loading
- Bulk operations
- Concurrent access
- Memory usage validation

## Implementation Timeline

### Phase 1: Foundation (3-4 days)
- PresetDataModel implementation
- Basic PresetManager with file operations
- JSON schema validation
- Unit tests for core functionality

### Phase 2: CLI Integration (2-3 days)
- CLI commands implementation
- Validation framework
- Error handling and user feedback
- Integration tests

### Phase 3: Advanced Features (3-4 days)
- Preset templates
- Inheritance system
- Export/import functionality
- Workflow integration

### Phase 4: Documentation & Testing (2-3 days)
- User guide and tutorials
- API documentation
- Comprehensive test suite
- Performance optimization

## Success Metrics

- **User Adoption**: >50% of users create custom presets within 30 days
- **Performance**: All operations meet performance requirements
- **Quality**: >90% test coverage, <5% error rate
- **Usability**: <2 minutes to create first custom preset

This specification provides the foundation for implementing a comprehensive, user-friendly preset system that significantly improves the Vibe Check user experience.
