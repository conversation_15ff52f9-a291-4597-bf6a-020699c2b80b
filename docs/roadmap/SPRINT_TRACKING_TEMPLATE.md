# Sprint Tracking Template

**Documentation Cycle: Strawberry | Updated: 28-06-2025**

## Sprint Information

**Sprint**: [Sprint Number and Name]  
**Phase**: [Phase Number and Name]  
**Duration**: [Start Date] - [End Date]  
**Sprint Lead**: [Name]  
**Team Members**: [List of team members]  

## Sprint Goals

### Primary Objectives
- [ ] [Primary objective 1]
- [ ] [Primary objective 2]
- [ ] [Primary objective 3]

### Success Criteria
- [ ] [Success criterion 1]
- [ ] [Success criterion 2]
- [ ] [Success criterion 3]

## Sprint Backlog

### Epic 1: [Epic Name] ([Total Points] points)

#### Story 1.1: [Story Name]
- **Owner**: [Developer Name]
- **Points**: [Story Points]
- **Status**: [Not Started/In Progress/In Review/Done]
- **Progress**: [0-100%]

**Tasks**:
- [ ] [Task 1] - [Owner] - [Hours] - [Status]
- [ ] [Task 2] - [Owner] - [Hours] - [Status]
- [ ] [Task 3] - [Owner] - [Hours] - [Status]

**Acceptance Criteria**:
- [ ] [Criterion 1]
- [ ] [Criterion 2]
- [ ] [Criterion 3]

**Notes**: [Any relevant notes or blockers]

#### Story 1.2: [Story Name]
- **Owner**: [Developer Name]
- **Points**: [Story Points]
- **Status**: [Not Started/In Progress/In Review/Done]
- **Progress**: [0-100%]

**Tasks**:
- [ ] [Task 1] - [Owner] - [Hours] - [Status]
- [ ] [Task 2] - [Owner] - [Hours] - [Status]

**Acceptance Criteria**:
- [ ] [Criterion 1]
- [ ] [Criterion 2]

### Epic 2: [Epic Name] ([Total Points] points)

[Repeat structure for additional epics and stories]

## Sprint Metrics

### Velocity Tracking
- **Planned Points**: [Total planned story points]
- **Completed Points**: [Completed story points]
- **Velocity**: [Completed/Planned ratio]
- **Burndown**: [Points remaining by day]

### Time Tracking
- **Planned Hours**: [Total planned hours]
- **Actual Hours**: [Actual hours spent]
- **Efficiency**: [Planned/Actual ratio]

### Quality Metrics
- **Test Coverage**: [Percentage]
- **Code Review**: [Number of reviews completed]
- **Bugs Found**: [Number of bugs found in sprint]
- **Technical Debt**: [Hours of technical debt added/removed]

## Daily Standup Notes

### Day 1 ([Date])
**Yesterday**: Sprint planning completed  
**Today**: [Team member updates]  
**Blockers**: [Any blockers identified]  

### Day 2 ([Date])
**Yesterday**: [What was accomplished]  
**Today**: [What will be worked on]  
**Blockers**: [Any blockers identified]  

[Continue for each day of sprint]

## Sprint Review

### Completed Work
- [ ] [Completed item 1]
- [ ] [Completed item 2]
- [ ] [Completed item 3]

### Incomplete Work
- [ ] [Incomplete item 1] - [Reason]
- [ ] [Incomplete item 2] - [Reason]

### Demonstrations
- [Demo 1]: [Description and feedback]
- [Demo 2]: [Description and feedback]

### Stakeholder Feedback
- [Feedback item 1]
- [Feedback item 2]
- [Action items from feedback]

## Sprint Retrospective

### What Went Well
- [Positive item 1]
- [Positive item 2]
- [Positive item 3]

### What Could Be Improved
- [Improvement item 1]
- [Improvement item 2]
- [Improvement item 3]

### Action Items for Next Sprint
- [ ] [Action item 1] - [Owner] - [Due date]
- [ ] [Action item 2] - [Owner] - [Due date]
- [ ] [Action item 3] - [Owner] - [Due date]

### Process Improvements
- [Process improvement 1]
- [Process improvement 2]

## Risk Assessment

### Current Risks
- **Risk 1**: [Description] - [Impact: High/Medium/Low] - [Probability: High/Medium/Low]
  - **Mitigation**: [Mitigation strategy]
- **Risk 2**: [Description] - [Impact: High/Medium/Low] - [Probability: High/Medium/Low]
  - **Mitigation**: [Mitigation strategy]

### Risk Mitigation Actions
- [ ] [Mitigation action 1] - [Owner] - [Due date]
- [ ] [Mitigation action 2] - [Owner] - [Due date]

## Dependencies

### External Dependencies
- [Dependency 1]: [Status] - [Impact if delayed]
- [Dependency 2]: [Status] - [Impact if delayed]

### Internal Dependencies
- [Dependency 1]: [Status] - [Blocking/Blocked by]
- [Dependency 2]: [Status] - [Blocking/Blocked by]

## Technical Debt

### Debt Added This Sprint
- [Technical debt item 1] - [Estimated hours to resolve]
- [Technical debt item 2] - [Estimated hours to resolve]

### Debt Resolved This Sprint
- [Resolved debt item 1] - [Hours spent resolving]
- [Resolved debt item 2] - [Hours spent resolving]

### Outstanding Technical Debt
- [Outstanding item 1] - [Priority: High/Medium/Low]
- [Outstanding item 2] - [Priority: High/Medium/Low]

## Performance Metrics

### Code Quality
- **Complexity**: [Average/Max complexity scores]
- **Maintainability**: [Maintainability index]
- **Test Coverage**: [Percentage coverage]
- **Code Review Coverage**: [Percentage of code reviewed]

### Performance Benchmarks
- **Build Time**: [Time to build]
- **Test Execution Time**: [Time to run all tests]
- **Analysis Performance**: [Time to analyze typical project]
- **Memory Usage**: [Peak memory usage during analysis]

## Documentation Updates

### Documentation Created
- [Document 1]: [Description]
- [Document 2]: [Description]

### Documentation Updated
- [Document 1]: [Changes made]
- [Document 2]: [Changes made]

### Documentation Needed
- [ ] [Needed documentation 1] - [Owner] - [Due date]
- [ ] [Needed documentation 2] - [Owner] - [Due date]

## Next Sprint Planning

### Carry-over Items
- [Item 1]: [Reason for carry-over]
- [Item 2]: [Reason for carry-over]

### New Priorities
- [Priority 1]: [Justification]
- [Priority 2]: [Justification]

### Capacity Planning
- **Available Capacity**: [Hours available next sprint]
- **Planned Velocity**: [Story points planned]
- **Risk Buffer**: [Percentage of capacity reserved for risks]

### Dependencies for Next Sprint
- [Dependency 1]: [Required for next sprint]
- [Dependency 2]: [Required for next sprint]

## Lessons Learned

### Technical Lessons
- [Lesson 1]: [What was learned and how to apply]
- [Lesson 2]: [What was learned and how to apply]

### Process Lessons
- [Lesson 1]: [What was learned and how to apply]
- [Lesson 2]: [What was learned and how to apply]

### Team Lessons
- [Lesson 1]: [What was learned and how to apply]
- [Lesson 2]: [What was learned and how to apply]

## Appendix

### Useful Links
- [Sprint Planning Board]: [URL]
- [Code Repository]: [URL]
- [Documentation]: [URL]
- [Test Results]: [URL]

### Meeting Notes
- [Meeting 1]: [Date] - [Key decisions and action items]
- [Meeting 2]: [Date] - [Key decisions and action items]

### Additional Notes
[Any additional notes or observations from the sprint]
